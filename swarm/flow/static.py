import os


class FlowEnvVar:
    SWARM_FLOW_ID = "SWARM_FLOW_ID"
    SWARM_CLIENT_SECRETS = "SWARM_CLIENT_SECRETS"
    SWARM_FLOW_ARGS = "SWARM_FLOW_ARGS"
    SWARM_AUDIT_ID = "SWARM_AUDIT_ID"
    SWARM_FILE_URL = "SWARM_FILE_URL"
    SWARM_EMAIL_NOTIFICATION = "SWARM_EMAIL_NOTIFICATION"
    SWARM_LOCAL_FLOW_PATH = "SWARM_LOCAL_FLOW_PATH"
    SWARM_LOCAL_PORTS = "SWARM_LOCAL_PORTS"
    SWARM_DASK_EXECUTOR = "SWARM_DASK_EXECUTOR"
    SWARM_LOCAL_FLOW_ID = "SWARM_LOCAL_FLOW_ID"
    SWARM_ORIGIN = "SWARM_ORIGIN"


class RegistryCluster:
    HOST = os.environ.get(
        "REGISTRY_HOST",
        "vpc-platform-registry-rvm2fffwtukstkdkuj22tu7tw4.eu-west-1.es.amazonaws.com",
    )
    SCHEME = os.environ.get("REGISTRY_SCHEME", "https")
    PORT = os.environ.get("REGISTRY_PORT", 443)

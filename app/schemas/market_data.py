from enum import auto
from typing import Set

import pydantic
from api_sdk.utils.utils import StringEnum
from pydantic import Field


class InstrumentType(StringEnum):
    ALL = auto()
    ANNA_DSB = auto()
    FCA_FIRDS = auto()
    FIRDS = auto()
    LEI = auto()
    STEEL_EYE = auto()
    VENUE = auto()
    VENUE_DIRECT = auto()


class InstrumentsSearchBody(pydantic.BaseModel):
    # NOTE: this is 1000 because of  maxClauseCount,
    # so with must and must not if we set to es_client.MAX_TERMS_SIZE
    # it could overcome that value
    instrument_ids: Set[str] = Field(..., min_items=1, max_items=1000)


class PriceType(StringEnum):
    QUOTE = auto()
    TRADE = auto()

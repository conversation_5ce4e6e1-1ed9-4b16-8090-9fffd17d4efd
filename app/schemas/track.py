import datetime as dt
from typing import List, Optional, Type

from api_sdk.schemas.base import APIModel, RecordModel
from api_sdk.utils.utils import StringEnum

from app.utils.attributes import get_attr


class UserError(RecordModel):
    class Config:
        model_name = "UserError"
        index_suffix = "user_error"

    realm: str
    user: str
    userName: Optional[str]
    remoteAddr: Optional[str]
    timestamp: dt.datetime
    uri: Optional[str]
    category: Optional[str]
    error: str


class CategoryTitle(StringEnum):
    AUTHENTICATION = "Authentication"
    BACKTEST = "Backtest"
    CHART_INTERACTION = "Chart Interaction"
    COMMENT = "Comment"
    DOWNLOAD = "Download"
    NAVIGATION = "Navigation"
    RECORD_CREATION = "Record Creation"
    RECORD_DELETION = "Record Deletion"
    RECORD_MODIFICATION = "Record Modification"
    RECORD_VIEW = "Record View"
    REPORT_SUBMISSION_REQUEST = "Report Submission Request"
    REFINE = "Refine"
    SEARCH = "Search"
    WORKFLOW = "Workflow"
    SCHEDULE = "Schedule"  # TODO Does this need to be added to the schema?


# Don't use this as an enum in app. Use api_sdk.schemas.static.Module.
# Only use this if this is visible to user, example in UserAudits.
# We also have Module in se_elastic_schema. Don't know why.
class ModuleTitle(StringEnum):
    ADMIN = "Admin"
    MARKET = "Market"
    COMMUNICATIONS = "Communications"
    ORDERS = "Orders"
    TRADES = "Trades"
    CASE_MANAGER = "Case Manager"
    TRANSACTION_REPORTING = "Transaction Reporting"
    TRANSACTION_REPORTING_ADMIN = "Transaction Reporting Admin"
    BEST_EXECUTION = "Best Execution"
    MARKET_ABUSE = "Market Abuse"
    DATA_PROVENANCE = "Data Provenance"
    FILE_ZONE = "File Zone"
    COMMS_SURVEILLANCE = "Comms Surveillance"
    TRADE_SURVEILLANCE = "Trade Surveillance"
    INSIGHTS = "Insights"

    # Non-standard
    SURVEILLANCE = "Surveillance"
    NA = "Not Available"
    GLOBAL = "Global"

    # Submodule
    RESTRICTED_LIST = "Restricted List"


class UserErrorIn(APIModel):
    remoteAddr: Optional[str]
    uri: Optional[str]
    category: Optional[str]
    error: Optional[str]


class EventDetails(APIModel):
    module: Optional[str]
    category: str
    event: Optional[str]
    description: Optional[str]


class RecordDetails(APIModel):
    """
    1. On a new record created, recordDetails.recordKey is the &key of the record
    2. On a record being updated, recordDetails.recordKey is the &key of response
       and recordDetails.originalRecordKey is the &key of the original record
    3. On a record being deleted, recordDetails.originalRecordKey is the &key of the deleted record
    """

    model: str
    originalRecordKey: Optional[str]
    recordKey: Optional[str]

    @classmethod
    def for_record_cls(cls, record_cls: Type[RecordModel], **kwargs) -> "RecordDetails":
        kwargs.setdefault("model", record_cls.__config__.model_name)
        return cls(**kwargs)

    @classmethod
    def for_record(cls, record: RecordModel, **kwargs) -> "RecordDetails":
        kwargs.setdefault(
            "model", get_attr(record, "&model", "model_", "model__", "__class__.__name__", default="UNKNOWN")
        )
        kwargs.setdefault("originalRecordKey", get_attr(record, "&key", "key_", "key__", default="UNKNOWN"))
        kwargs.setdefault("recordKey", get_attr(record, "&key", "key_", "key__", default="UNKNOWN"))
        return cls(**kwargs)


class SearchDetails(APIModel):
    class SearchQueryItem(APIModel):
        key: str
        value: str

    searchText: Optional[str]
    refineQuery: Optional[str]
    searchQuery: Optional[List[SearchQueryItem]]


class UserAuditBase(APIModel):
    timestamp: Optional[dt.datetime]
    uri: Optional[str]
    body: Optional[bytes]
    statusCode: Optional[int]
    statusPhrase: Optional[str]
    searchText: Optional[str]  # Deprecated, use searchDetails.searchText instead.
    searchDetails: Optional[SearchDetails]
    action: Optional[str]
    recordId: Optional[str]
    eventDetails: Optional[EventDetails]
    recordDetails: Optional[RecordDetails]


class UserAudit(UserAuditBase, RecordModel):
    class Config:
        model_name = "UserAudit"
        index_suffix = "user_audit"

        # TODO once we work out a way to link these with schema, look up from there instead
        trait_fqn = "provenance/user_audit"

        safe_to_exclude_none = True

    realm: str
    user: str
    userName: Optional[str]
    remoteAddr: Optional[str]


class UserAuditIn(UserAuditBase):
    pass

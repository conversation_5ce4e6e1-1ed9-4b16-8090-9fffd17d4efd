import logging
from datetime import time
from typing import Dict, List, Optional

import pydantic
from api_sdk.schemas.base import Field, RecordModel
from dictbelt import dic_del, dic_get
from se_elastic_schema.components.communication.labels import Labels
from se_elastic_schema.components.reference.file_info import FileInfo
from se_elastic_schema.models.tenant.communication.attachment import Attachment
from se_elastic_schema.models.tenant.communication.transcript import Transcript as FullTranscript

from app.schemas.comms.common import Body

MISSING_TRANSCRIPT_TXT = "TRANSCRIPT MISSING"

log = logging.getLogger(__name__)


class TranscriptModel(pydantic.BaseModel):
    id: Optional[str]
    language: Optional[str]
    vendor: str


class Transcript(pydantic.BaseModel):
    id: str
    model: TranscriptModel

    class Config:
        extra = pydantic.Extra.allow


class TranscriptRecordModel(Transcript, RecordModel):
    class Config:
        model_name = "Transcript"
        index_suffix = "transcript"

        extra = pydantic.Extra.allow


class CommunicationTranscriptsModel(pydantic.BaseModel):
    hits: Dict[str, List[FullTranscript]]

    class Config:
        extra = pydantic.Extra.allow


def call_body_text(values):
    text = dic_get(values, ["body", "text"], raise_on_missing=False)
    if not text:
        return dic_get(values, ["body", "displayText"], raise_on_missing=False)

    return text


def _transcript_values_migrator(values: Dict) -> Dict:
    body_text = call_body_text(values)
    first_transcript = dic_get(values, ["transcripts", 0], default=None, raise_on_missing=False)

    if not first_transcript and body_text:
        # If there's a transcript that needs to be Shim
        # please ask for data remediation
        # See RL-1154
        log.error(f"Transcript not found for call with id: {values.get('&id')}")
        values["body"] = {"text": body_text}
    elif body_text:
        values["body"] = {"text": body_text}
    else:
        values["body"] = {"text": MISSING_TRANSCRIPT_TXT}

    dic_del(values, ["voiceTranscript"])
    return values


class Call(RecordModel):
    transcripts: Optional[List[Transcript]] = None
    body: Optional[Body]
    voiceFile: Optional[Attachment]
    attachments: Optional[List[Attachment]]
    callDuration: Optional[time]
    waveform: Optional[FileInfo]
    labels: Optional[Labels] = Field(None)

    class Config:
        model_name = "Call"
        index_suffix = "call"
        transcript_class = Transcript

        extra = pydantic.Extra.allow

    @pydantic.root_validator(pre=True)
    def migrate_transcripts(cls, values):
        return _transcript_values_migrator(values)

    def has_transcript(self, id: str) -> bool:
        if not self.transcripts:
            return False
        return len(list(filter(lambda x: x.id == id, self.transcripts))) > 0

    def shim_transcript(self) -> Optional[FullTranscript]:
        """
        Shims a Transcript from the information available within the Call.
        Use only for calls that don't have a real Transcript attached to them.
        """
        if not self.transcripts:
            return None

        last_transcript = self.transcripts[-1]
        if not last_transcript or not self.body or self.body.text == MISSING_TRANSCRIPT_TXT:
            return None

        return FullTranscript(
            id=last_transcript.id,
            isTranscribedBySteelEye=False,
            model=last_transcript.model.dict(by_alias=True),
            isAnnotated=False,
            transcriptSourceKey="Legacy",
            recordingSourceKey="Legacy",
            text=self.body.text,
        )

    def has_voicefile_attachment_key(self, attachment_key):
        if (
            self.voiceFile
            and self.voiceFile.fileInfo.location
            and self.voiceFile.fileInfo.location.key
            and self.voiceFile.fileInfo.location.bucket
            and self.voiceFile.fileInfo.location.key == attachment_key
        ):
            return True

        return False

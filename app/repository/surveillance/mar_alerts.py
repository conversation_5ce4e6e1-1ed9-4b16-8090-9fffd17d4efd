import datetime as dt
import logging
from typing import Any, Dict, List, Optional, Union

from api_sdk.es_dsl.base import ModelFilter, Not, NotExpiredFilter, Or, SearchModel, TermFilter
from api_sdk.es_dsl.features import RangeFilter
from api_sdk.es_dsl.params import SearchModelParams
from api_sdk.repository.syncronous.request_bound import RequestBoundRepository

from app.repository.surveillance.alerts import UnresolvedAlertsAssigneeStatusFilter
from app.schemas.surveillance.market_abuse import MarketAbuseAlert, MarketAbuseScenarioTag

log = logging.getLogger(__name__)


class MarketAbuseScenarioTagsSearch(SearchModel):
    class Params(SearchModelParams):
        alert_status: Optional[Union[str, List[str]]]
        unresolved_alerts_assignee_status: Optional[List[str]] = None
        start: Optional[Union[dt.datetime, dt.date]]
        end: Optional[Union[dt.datetime, dt.date]]
        f: Any = None
        hit_keys: Optional[List[str]] = None
        scenario_ids: Optional[Union[str, List[str]]]
        watch_id: Optional[Union[str, List[str]]]
        orders: Optional[Union[str, List[str]]] = None

    params: Params

    features = [
        ModelFilter(model=MarketAbuseScenarioTag),
        Not(TermFilter(name="detail.watchType", value="ON_DEMAND")),
        NotExpiredFilter,
        TermFilter(name="workflow.status", param="alert_status"),
        RangeFilter(field="detected"),
        # TODO: Do we want to introduce a range filter here for hit.timestamps.*?
        Or(
            TermFilter(name="scenarioId", param="scenario_ids"),
            TermFilter(name="slug", param="scenario_ids"),
        ),
        TermFilter(name="detail.watchId", param="watch_id"),
        UnresolvedAlertsAssigneeStatusFilter,
    ]


class MarketAbuseAlertsSearch(SearchModel):
    class Params(SearchModelParams):
        label: Optional[Union[str, List[str]]]
        hit_keys: Optional[List[str]] = None
        watch_id: Optional[Union[str, List[str]]]

    params: Params

    features = [
        ModelFilter(model=MarketAbuseAlert),
        TermFilter(name="detail.watchId", param="watch_id"),
        TermFilter(name="hit.&id", param="hit_id"),
        TermFilter(name="label"),
    ]


class MarketAbuseAlertRepository(RequestBoundRepository):
    def _get_scenario_tags(self, **params):
        yield from self.execute_scan(
            index=self.index_for_record_model(MarketAbuseScenarioTag),
            search_model=MarketAbuseScenarioTagsSearch(**params),
        )

    def _get_scenario_alerts(self, **params):
        yield from self.execute_scan(
            index=self.index_for_record_model(MarketAbuseAlert),
            search_model=MarketAbuseAlertsSearch(**params),
        )

    def _enrich_scenario_with_record_orders(self, watch_id: str, scenarios: List[Dict], keys_to_retrieve: List[str]):
        if not any(keys_to_retrieve):
            return

        order_keys = list(set(keys_to_retrieve))
        order_alerts = self._get_scenario_alerts(
            watch_id=watch_id,
            hit_keys=order_keys,
        )

        orders_by_keys = {}

        for alert_hit in order_alerts:
            hit = alert_hit.get("_source", {}).get("hit")
            if hit:
                orders_by_keys[hit.get("&key")] = hit

        if any(orders_by_keys):
            for scenario in scenarios:
                if isinstance(scenario.get("records").get("order"), dict):
                    continue

                record_order = orders_by_keys.get(scenario.get("records").get("order"))

                if record_order:
                    scenario["order"] = record_order.copy()

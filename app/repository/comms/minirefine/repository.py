from dataclasses import dataclass
from typing import Optional

import elasticsearch_dsl as dsl
from api_sdk.es_dsl.base import (
    ModelFilter,
    NotExpiredFilter,
    SearchBase,
    SearchFeature,
    SearchFeatureConfig,
    SearchModel,
)
from api_sdk.es_dsl.flang import <PERSON><PERSON><PERSON>ilter
from api_sdk.es_dsl.params import SearchModelParams
from api_sdk.es_dsl.utils import sanitize_es_reserved_characters
from api_sdk.repository.syncronous.request_bound import RepoHelpersMixin
from api_sdk.schemas.base import APIModel
from se_elastic_schema.models.tenant.communication.meeting import Meeting

from app.repository.comms.minirefine.autocomplete_fields import <PERSON><PERSON><PERSON><PERSON>ple<PERSON><PERSON><PERSON>, MrAutocompleteFieldName
from app.repository.comms.minirefine.search_fields import Mr<PERSON>earch<PERSON>ield
from app.schemas.comms.call import Call
from app.schemas.comms.chat import Message, Text
from app.schemas.comms.email import Email

COMMS_MODELS = [Call, Email, Meeting, Message, Text]

AUTOCOMPLETE_DEFAULT_SIZE = 25

ALLOWED_FILTERS = [
    "eq",
    "ne",
    "in",
    "is",
    "qs",
    "inrange",
    "gt",
    "ge",
    "lt",
    "le",
    "startswith",
    "endswith",
    "contains",
    "like",
    "matches",
]


class MiniRefineSearch(SearchModel):
    """
    TODO: nested paths auto-detected
    """

    class Params(SearchModelParams):
        f: Optional[str] = None

    features = [
        ModelFilter(model=COMMS_MODELS),
        NotExpiredFilter,
        FlangFilter.simple(
            allowed_filters=ALLOWED_FILTERS,
            allowed_operators=["and", "not", "or"],
            allowed_fields={ft.name: ft().generate_filter for ft in MrSearchField.get_all_types()},
            nested_paths=["participants"],
        ),
    ]


class AutocompleteAgg(SearchFeature):
    @dataclass
    class Config(SearchFeatureConfig):
        field_param: str = "agg_field"
        search_param: str = "agg_search"
        size: int = AUTOCOMPLETE_DEFAULT_SIZE
        agg_name: str = "AUTOCOMPLETE"

    config: Config

    def __call__(self, model: SearchBase, search: dsl.Search) -> dsl.Search:
        field_name = getattr(model.params, self.config.field_param, None)
        if not field_name:
            return search

        filters = []

        ac_field = MrAutocompleteField.get_by_name(field_name)

        if ac_field.filters:
            filters.extend(ac_field.filters)

        search_str = getattr(model.params, self.config.search_param, None)
        if search_str:
            search_expr = f"*{sanitize_es_reserved_characters(search_str).lower()}*"

            if ac_field.terms_agg_filter_fields:
                filter_fields = ac_field.terms_agg_filter_fields
                field_filter = dsl.Q("wildcard", **{f"{filter_fields[0]}.text": search_expr})
                for ff in filter_fields[1:]:
                    field_filter = field_filter | dsl.Q("wildcard", **{f"{ff}.text": search_expr})
                filters.append(field_filter)

        unreliable_agg_size = self.config.size * 5  # arbitrary, no science behind this
        if ac_field.terms_agg_filter_unreliable:
            agg_size = unreliable_agg_size
        else:
            agg_size = self.config.size

        if ac_field.terms_agg_field:
            terms_agg = {
                "field": ac_field.terms_agg_field,
                "size": agg_size,
                "min_doc_count": 1,
            }
        elif ac_field.terms_agg_script:
            # We assume here that the script does not do filtering, so filtering is "unreliable"
            terms_agg = {
                "script": ac_field.terms_agg_script,
                "size": unreliable_agg_size,
                "min_doc_count": 1,
            }
        else:
            raise NotImplementedError(f"Neither terms_agg_field, nor terms_agg_script set for {field_name}")

        nested_path = ac_field.nested_path
        if nested_path:
            model.agg(
                self.config.agg_name,
                {
                    "nested": {"path": nested_path},
                    "aggs": {
                        "NESTED": {
                            "filter": {"bool": {"must": [f.to_dict() for f in filters]}},
                            "aggs": {"VALUES": {"terms": terms_agg, "aggs": {"WITH_VALUE": {"reverse_nested": {}}}}},
                        }
                    },
                },
            )
        else:
            model.agg(
                self.config.agg_name,
                {
                    "filter": {"bool": {"must": [f.to_dict() for f in filters]}},
                    "aggs": {"VALUES": {"terms": terms_agg}},
                },
            )

        return search


class MiniRefineAutocompleteSearch(SearchModel):
    class Params(SearchModelParams):
        agg_search: Optional[str] = None
        agg_field: Optional[MrAutocompleteFieldName] = None

    features = [
        ModelFilter(model=COMMS_MODELS),
        NotExpiredFilter,
        AutocompleteAgg(size=AUTOCOMPLETE_DEFAULT_SIZE),
    ]


class CommsMiniRefineRepository(RepoHelpersMixin):
    def get_autocomplete_suggestions(self, field: MrAutocompleteFieldName, search: Optional[str] = None):
        result = self.get_aggs(
            search_model_cls=MiniRefineAutocompleteSearch,
            agg_field=field,
            agg_search=search,
        )

        spec = MrAutocompleteField.get_by_name(field.value)

        is_nested = spec.nested_path
        agg_path = "AUTOCOMPLETE.NESTED.VALUES" if is_nested else "AUTOCOMPLETE.VALUES"

        is_unreliable_filter = search and spec.terms_agg_filter_unreliable

        response = []
        for item in result.iter_raw_bucket_agg(agg_path):
            ac_item = AutocompleteItem(
                value=item["key"],
                count=item["WITH_VALUE"]["doc_count"] if is_nested else item["doc_count"],
            )
            if not is_unreliable_filter or (ac_item.value and search in ac_item.value):
                response.append(ac_item)

        return response[:AUTOCOMPLETE_DEFAULT_SIZE]


class AutocompleteItem(APIModel):
    value: str
    count: int

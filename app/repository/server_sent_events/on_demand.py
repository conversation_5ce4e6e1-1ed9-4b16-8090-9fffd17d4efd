import logging
from datetime import timezone
from typing import Dict, List, Optional

from api_sdk.models.elasticsearch import RawResult
from api_sdk.models.search import CustomPagination, Pagination, SearchResult, Sort
from api_sdk.utils.utils import nested_get
from se_elastic_schema.static.surveillance import WatchExecutionStatus

from app.repository.surveillance.audit import AuditRepository
from app.repository.surveillance.watches import WatchesRepository

log = logging.getLogger(__name__)


async def get_on_demand_watch(
    audit_repo: AuditRepository,
):
    """
    Stream the Execution by getting the Watches Execution Status using the Tenants WatchExecution records sorted by executedOn.
    """
    watch_executions = audit_repo.get_watches_execution_status(
        watch_type="ON_DEMAND", pagination=Pagination(take=250), execution_status=["IN_PROGRESS", "QUEUED"]
    )

    _raw_watch_executions = RawResult(
        **{"took": 0, "timed_out": False, "hits": {"hits": watch_executions, "total": len(watch_executions)}}
    )

    return SearchResult.from_raw_result(result=_raw_watch_executions).to_dict()


async def get_on_demand_watch_with_id(
    audit_repo: AuditRepository,
    watch_ids: Optional[List[str]] = None,
) -> Dict:
    watch_executions = audit_repo.get_watches_execution_status(
        watch_type="ON_DEMAND",
        watch_ids=watch_ids,
        pagination=Pagination(take=1),
    )

    _raw_watch_executions = RawResult(
        **{"took": 0, "timed_out": False, "hits": {"hits": watch_executions, "total": len(watch_executions)}}
    )

    return SearchResult.from_raw_result(result=_raw_watch_executions).to_dict()


async def get_on_demand_watches_summary_with_alerts(
    audit_repo: AuditRepository,
    watches_repo: WatchesRepository,
    status: Optional[List[WatchExecutionStatus]],
    query_id: Optional[str] = None,
    exclude_lexica: Optional[bool] = None,
    **params,
) -> Dict:
    """
    Steps:
    1. Call get_watches_with_alerts_summary to get watch with alert details.
    2. Picks the watchIds from Executions.
    3. Get the Watches Execution Status using WatchExecution records sorted by executedOn.
    4. Map and return the list of watch + alert count + execution status data.
    """

    watches_with_alerts_summary = watches_repo.get_watches_with_alerts_summary(
        status=status,
        queryId=query_id,
        exclude_lexica=exclude_lexica,
        watch_execution_type="ON_DEMAND",
        **params,
    )

    watch_alerts = [watch.to_dict() for watch in watches_with_alerts_summary.hits.hits]

    watch_ids = [watch_alert.get("&id") for watch_alert in watch_alerts]

    watch_executions = audit_repo.get_watches_execution_status(
        watch_type="ON_DEMAND",
        watch_ids=watch_ids,
        pagination=CustomPagination(
            take=1000,
            sorts=[
                Sort(field="executedOn", order=Sort.Order.asc),
            ],
        ),
    )

    watch_executions_dict = {
        nested_get(watch_execution, "watch.&id"): watch_execution for watch_execution in watch_executions
    }

    watch_alerts = [
        {
            **watch_alert,
            "onDemandExecution": watch_executions_dict.get(watch_alert.get("&id"))
            if watch_executions_dict.get(watch_alert.get("&id"))
            else {"status": "WAITING_FOR_STATUS"},
        }
        for watch_alert in watch_alerts
    ]

    for watch_alert in watch_alerts:
        watch_alert["createdOn"] = (
            watch_alert["createdOn"].replace(tzinfo=timezone.utc) if watch_alert.get("createdOn") else None
        )

    _raw_watch_alerts = RawResult(
        **{
            "took": 0,
            "timed_out": False,
            "hits": {"hits": watch_alerts, "total": watches_with_alerts_summary.hits.total},
        }
    )

    return SearchResult.from_raw_result(result=_raw_watch_alerts).to_dict()

# TODO: Remove this when api-svc is moved to se-mono
import abc
from typing import Any, Generic, Union

import addict
import httpx
from se_api_client.types import ResponseType


class ResponseParser(
    abc.ABC,
    Generic[ResponseType],
):
    @abc.abstractmethod
    def parse(self, response: httpx.Response) -> ResponseType:
        raise NotImplementedError()


class StrResponseParser(ResponseParser[Union[str, dict[Any, Any]]]):
    def parse(self, response: httpx.Response) -> Union[str, dict[Any, Any]]:
        if response.headers["content-type"] == "application/json":
            result_dict: dict[Any, Any] = response.json()
            return result_dict
        else:
            result: str = response.text
            return result


class DictResponseParser(ResponseParser[dict[Any, Any]]):
    def parse(self, response: httpx.Response) -> dict[Any, Any]:
        result: dict[Any, Any] = response.json()
        return result


class ListResponseParser(ResponseParser[list[dict[Any, Any]]]):
    def parse(self, response: httpx.Response) -> list[dict[Any, Any]]:
        result: list[dict[Any, Any]] = response.json()
        return result


class AddictResponseParser(ResponseParser[addict.Dict]):
    """Converts a json response to an addict Dict."""

    def parse(self, response: httpx.Response) -> addict.Dict:
        val = addict.Dict(response.json())
        val.freeze()
        return val


class ListAddictResponseParser(ResponseParser[list[addict.Dict]]):
    """Converts a json response with list of dicts to list of addict dicts."""

    def parse(self, response: httpx.Response) -> list[addict.Dict]:
        val = []
        for rec in response.json():
            rec = addict.Dict(rec)
            rec.freeze()
            val.append(rec)
        return val

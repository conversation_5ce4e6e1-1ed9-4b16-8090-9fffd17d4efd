from dataclasses import dataclass

from api_sdk.messages.base import DomainEvent
from se_elastic_schema.models.reference.financial_instrument import FinancialInstrument

from app.messages.audit.events import RecordViewEvent, SearchEvent
from app.schemas.track import ModuleTitle


@dataclass
class MarketDataRecordEvent(DomainEvent):
    audit_module = ModuleTitle.MARKET

    instrument: FinancialInstrument = None
    instrument_id: str = None

    @property
    def record(self):
        return self.instrument


@dataclass
class InstrumentsSearched(MarketDataRecordEvent, SearchEvent):
    pass


@dataclass
class InstrumentRecordViewed(MarketDataRecordEvent, RecordViewEvent):
    pass


@dataclass
class InstrumentPricesViewed(MarketDataRecordEvent, RecordViewEvent):
    audit_description = "User retrieved market prices"


@dataclass
class InstrumentStatsViewed(MarketDataRecordEvent, RecordViewEvent):
    audit_description = "User retrieved market stats"

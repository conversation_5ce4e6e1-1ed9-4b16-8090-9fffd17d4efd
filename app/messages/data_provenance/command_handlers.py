import collections
import dataclasses
import logging
from dataclasses import dataclass
from typing import Callable, Dict, List, Union

from api_sdk.messages.base import MessageBus
from api_sdk.repository.elastic import AbstractEsRepository
from api_sdk.utils.utils import nested_dict_get
from se_elastic_schema.static.mifid2 import RTS22ExternalStatus

from app.messages.data_provenance.commands import ArchiveQuarantinedRecordsCommand, ProcessQuarantinedRecordsCommand
from app.messages.data_provenance.events import QuarantinedRecordsProcessed
from app.messages.registry import registry
from app.messages.rts22.commands import UpdateRts22TransactionsCommand
from app.repository.data_provenance.quarantine import QuarantineRepository
from app.repository.quarantine.handler import QuarantineHandler, QuarantineRequest, UserAction
from app.schemas.rts22 import QuarantinedRts22Transaction, Rts22Transaction, Rts22TransactionUpdateType

log = logging.getLogger(__name__)


def _get_ref_no(record):
    return nested_dict_get(record, "reportDetails.transactionRefNo")


def _get_report_status(record):
    return nested_dict_get(record, "reportDetails.reportStatus")


def _is_reported(record):
    return bool(nested_dict_get(record, "workflow.isReported"))


def _set_is_reported_false(record):
    record["workflow"]["isReported"] = False


def _get_arm_status(record) -> RTS22ExternalStatus:
    raw = nested_dict_get(record, "workflow.arm.status")
    if raw:
        return RTS22ExternalStatus(raw)


def _get_nca_status(record) -> RTS22ExternalStatus:
    raw = nested_dict_get(record, "workflow.nca.status")
    if raw:
        return RTS22ExternalStatus(raw)


@dataclass
class State:
    ref_no: str = None
    newt: Dict = None
    canc: Dict = None
    quarantined_newt: List[Dict] = dataclasses.field(default_factory=list)
    quarantined_canc: List[Dict] = dataclasses.field(default_factory=list)

    @property
    def newt_reported(self):
        if self.newt and _is_reported(self.newt):
            return self.newt

    @property
    def newt_not_reported(self):
        if self.newt and not _is_reported(self.newt):
            return self.newt

    @property
    def canc_reported(self):
        if self.canc and _is_reported(self.canc):
            return self.canc

    @property
    def canc_not_reported(self):
        if self.canc and not _is_reported(self.canc):
            return self.canc

    @property
    def quarantined_newt_not_reported(self):
        return [r for r in self.quarantined_newt if not _is_reported(r)]

    @property
    def quarantined_newt_reported(self):
        return [r for r in self.quarantined_newt if _is_reported(r)]

    @property
    def quarantined_canc_not_reported(self):
        return [r for r in self.quarantined_canc if not _is_reported(r)]

    @property
    def quarantined_canc_reported(self):
        return [r for r in self.quarantined_canc if _is_reported(r)]


@dataclass
class Reconciliation:
    ref_no: str = None
    tags: List[str] = dataclasses.field(default_factory=list)
    is_denied: bool = None
    update_newt: List[Callable] = dataclasses.field(default_factory=list)
    update_canc: List[Callable] = dataclasses.field(default_factory=list)
    create_canc: bool = None

    @property
    def sink_record_audit_id(self):
        raise NotImplementedError()


_SCENARIO_1 = "PR-1502 Scenario-1"
_SCENARIO_2 = "PR-1502 Scenario-2"
_SCENARIO_3 = "PR-1502 Scenario-3"


def reconcile_state(item: State) -> Reconciliation:
    ref_no = item.ref_no
    result = Reconciliation(ref_no=ref_no)

    if item.quarantined_newt:

        if item.newt_not_reported and item.canc_not_reported:
            result.tags.append(_SCENARIO_1)

        elif item.newt_reported and not item.canc:
            result.tags.append(_SCENARIO_2)
            newt_arn = _get_arm_status(item.newt)
            newt_nca = _get_nca_status(item.newt)

            if newt_arn in (RTS22ExternalStatus.PENDING, RTS22ExternalStatus.SUBMITTED):
                result.tags.append("arm_status in (PENDING, SUBMITTED)")
                result.is_denied = True
                return result

            elif newt_arn == RTS22ExternalStatus.ACCEPTED:
                result.tags.append("arm_status in (ACCEPTED)")
                result.create_canc = True
                result.update_newt.append(_set_is_reported_false)

            elif newt_arn in (RTS22ExternalStatus.REJECTED, RTS22ExternalStatus.REJECTION_CLEARED):
                result.tags.append("arm_status in (REJECTED, REJECTION_CLEARED)")
                result.update_newt.append(_set_is_reported_false)

            if newt_nca in (RTS22ExternalStatus.PENDING, RTS22ExternalStatus.SUBMITTED, RTS22ExternalStatus.ACCEPTED):
                result.tags.append("nca_status in (PENDING, SUBMITTED, ACCEPTED)")
                result.create_canc = True
                result.update_newt.append(_set_is_reported_false)

            elif newt_nca in (RTS22ExternalStatus.REJECTED, RTS22ExternalStatus.REJECTION_CLEARED):
                result.tags.append("nca_status in (REJECTED, REJECTION_CLEARED)")
                result.update_newt.append(_set_is_reported_false)

        elif item.newt_reported and item.canc_reported:
            result.tags.append(_SCENARIO_3)
            newt_arn = _get_arm_status(item.newt)
            canc_arn = _get_arm_status(item.canc)

            if (
                newt_arn == RTS22ExternalStatus.ACCEPTED
                and canc_arn == RTS22ExternalStatus.ACCEPTED
                or (
                    newt_arn == RTS22ExternalStatus.ACCEPTED
                    and canc_arn in (RTS22ExternalStatus.REJECTED, RTS22ExternalStatus.REJECTION_CLEARED)
                )
            ):
                result.update_newt.append(_set_is_reported_false)
                result.update_canc.append(_set_is_reported_false)
            elif (
                newt_arn in (RTS22ExternalStatus.REJECTED, RTS22ExternalStatus.REJECTION_CLEARED)
                and canc_arn == RTS22ExternalStatus.ACCEPTED
            ):
                result.update_newt.append(_set_is_reported_false)
            elif newt_arn in (RTS22ExternalStatus.PENDING, RTS22ExternalStatus.SUBMITTED) or canc_arn in (
                RTS22ExternalStatus.PENDING,
                RTS22ExternalStatus.SUBMITTED,
            ):
                result.is_denied = True
            else:
                result.tags.append("Not Recognised")
                result.is_denied = True

    return result


async def handle_rts22_quarantined_records(  # noqa: C901
    command: ProcessQuarantinedRecordsCommand, mb: MessageBus, repo: QuarantineRepository
):
    rts22_sink_audit_records = [
        record
        for record in repo.scan_sink_record_audits(ids=command.ids)
        if record["recordType"] == Rts22Transaction.__config__.model_name
    ]

    if not rts22_sink_audit_records:
        return

    rts22_sink_audit_record_ids = [record["&id"] for record in rts22_sink_audit_records]

    # Map in-quarantine record key to the SinkRecordAudit &id
    q_key_to_sra_id_lookup = {record["recordKey"]: record["&id"] for record in rts22_sink_audit_records}

    # Map transactionRefNo to SinkRecordAudit &id so that we know which records to not process when
    # the processing is denied.
    ref_no_to_sra_id_lookup = collections.defaultdict(set)

    states = collections.defaultdict(State)

    # Find the quarantined records referred to in the sink audit records
    q_record_keys = [r["recordKey"] for r in rts22_sink_audit_records]
    for q_record in repo.scan_records(QuarantinedRts22Transaction, **{"&key": q_record_keys}):
        ref_no = _get_ref_no(q_record)
        report_status = _get_report_status(q_record)

        states[ref_no].ref_no = ref_no
        getattr(states[ref_no], f"quarantined_{report_status.lower()}").append(q_record)

        ref_no_to_sra_id_lookup[ref_no].add(q_key_to_sra_id_lookup[q_record["&key"]])

    # Find the non-quarantined NEWT and CANC records
    trx_ref_nos = list(states.keys())
    for trx in repo.scan_records(Rts22Transaction, **{"reportDetails.transactionRefNo": trx_ref_nos}):
        ref_no = _get_ref_no(trx)
        report_status = _get_report_status(trx)
        setattr(states[ref_no], report_status.lower(), trx)

    # Prepare bulk payloads
    cancs_to_create = []
    cancs_to_update = []
    cancs_to_delete = []
    newts_to_update = []
    sra_ids_to_process = set(rts22_sink_audit_record_ids)

    for item in states.values():
        log.info(f"Reconciling state of {item.ref_no}")
        result = reconcile_state(item)
        if result.is_denied:
            log.warning(f"{item.ref_no} q-processing DENIED: {result.tags}")
            # While these may not satisfy any of Scenario 1, 2, or 3's conditions
            # they should still go through the usual quarantine process...
            # sra_ids_to_process -= ref_no_to_sra_id_lookup[result.ref_no]
            continue

        log.info(f"{result.ref_no}: {result.tags}")

        if _SCENARIO_1 in result.tags:
            cancs_to_delete.append(result.ref_no)

        if result.create_canc:
            assert not item.canc
            cancs_to_create.append(item.newt["&id"])

        elif result.update_canc:
            canc = Rts22Transaction(**item.canc)
            for update_func in result.update_canc:
                update_func(canc)
            cancs_to_update.append(canc)

        if result.update_newt:
            newt = Rts22Transaction(**item.newt)
            for update_func in result.update_newt:
                update_func(newt)
            newts_to_update.append(newt)

    log.info(f"CANCs to create: {len(cancs_to_create)}")
    log.info(f"CANCs to update: {len(cancs_to_update)}")
    log.info(f"CANCs to delete: {len(cancs_to_delete)}")
    log.info(f"NEWTs to update: {len(newts_to_update)}")
    log.info(f"RTS22Transaction Sink-record audit IDs to process: {len(sra_ids_to_process)}")

    if not command.is_dry_run:

        # Execute bulk payloads.
        # TODO Could the order matter? Then might have to do individually in the for item in states.values loop.

        for trx in newts_to_update + cancs_to_update:
            await repo.save_existing(trx)

        if cancs_to_create:
            await mb.request(
                UpdateRts22TransactionsCommand(
                    update_type=Rts22TransactionUpdateType.CREATE_CANCELLATION,
                    transaction_ids=cancs_to_create,
                )
            )

        if cancs_to_delete:
            for cancellation in repo.scan_rts22_cancellations(ref_nos=cancs_to_delete):
                log.debug(f"Deleting cancellation {cancellation['&id']}")
                try:
                    await repo.delete_existing(Rts22Transaction(**cancellation), refresh=False)
                except Exception:
                    pass

        if newts_to_update or cancs_to_update or cancs_to_delete:
            repo.refresh_index(index=repo.index_for_record_model(Rts22Transaction)[0])

        await handle_legacy_quarantine_processing(
            command=command,
            mb=mb,
            repo=repo,
            action="PROCESS",
        )


async def handle_non_rts22_sink_audit_records(
    command: ProcessQuarantinedRecordsCommand,
    mb: MessageBus,
    repo: QuarantineRepository,
):
    non_rts22_sink_audit_records = [
        record
        for record in repo.scan_sink_record_audits(ids=command.ids)
        if record["recordType"] != Rts22Transaction.__config__.model_name
    ]

    if not non_rts22_sink_audit_records:
        return

    non_rts_22_sink_audit_record_ids = [record["&id"] for record in non_rts22_sink_audit_records]

    log.info(f"Sink-record audit IDs to process: {len(non_rts_22_sink_audit_record_ids)}")

    await handle_legacy_quarantine_processing(
        command=command,
        mb=mb,
        repo=repo,
        action="PROCESS",
    )


async def handle_legacy_quarantine_processing(
    command: Union[ArchiveQuarantinedRecordsCommand, ProcessQuarantinedRecordsCommand],
    mb: MessageBus,
    repo: AbstractEsRepository,
    action: str = "PROCESS",
):
    records_to_process = set(command.ids)

    quarantine_handler = QuarantineHandler(
        repo=repo,
        dry_run=command.is_dry_run,
    )

    sra_map = quarantine_handler.handle_quarantine(
        QuarantineRequest(
            realm=command.tenancy.realm,
            action=UserAction[action],
            sink_record_audit_ids=command.ids,
        ),
        repo=repo,
    )
    log.info(sra_map)

    await mb.publish(
        QuarantinedRecordsProcessed(
            ids=list(records_to_process),
            in_reply_to=command,
        )
    )


@registry.command_handler
async def process_quarantined_records(
    command: ProcessQuarantinedRecordsCommand,
    mb: MessageBus,
    repo: QuarantineRepository,
):
    assert command.ids

    config = await repo.get_tenant_configuration()
    if config.has_feature_flag("useNewRts22QuarantineRules"):
        await handle_rts22_quarantined_records(command=command, mb=mb, repo=repo)
        await handle_non_rts22_sink_audit_records(command=command, mb=mb, repo=repo)
    else:
        await handle_legacy_quarantine_processing(command=command, mb=mb, repo=repo)


@registry.command_handler
async def archive_quarantined_records(
    command: ArchiveQuarantinedRecordsCommand,
    mb: MessageBus,
    repo: QuarantineRepository,
):
    assert command.ids

    await handle_legacy_quarantine_processing(command=command, mb=mb, action="ARCHIVE", repo=repo)

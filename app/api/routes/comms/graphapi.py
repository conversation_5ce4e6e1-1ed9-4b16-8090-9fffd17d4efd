from typing import Dict, Optional

from api_sdk.auth import Tenancy
from api_sdk.di.request import ReqDep
from fastapi import APIRouter
from pydantic import BaseModel
from starlette.requests import Request

from app.models.app import App
from app.version import __version__

router = APIRouter()


class VersionResponse(BaseModel):
    version: str
    description: str
    elastic: Optional[Dict]
    api_version: str
    schema_version: Optional[str]


@router.get(
    path="/graph-api-permission",
    name="comms:graph-api-permission",
    response_model=VersionResponse,
)
def get_permissions(request: Request, tenancy: Tenancy = ReqDep(Tenancy)):
    app: App = request.app
    version = VersionResponse(
        description=f"SteelEye Data Service: V{app.config.API_VERSION}",
        version=f"v{__version__}",
        api_version=app.config.API_VERSION,
        schema_version=app.elastic_repo.get_schema_version(realm=tenancy.realm),
    )
    if app.debug:
        version.elastic = app.elastic_repo.info.dict()
    return version

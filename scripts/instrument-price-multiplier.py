import json
import os

from elasticsearch6 import Elasticsearch
from elasticsearch6.helpers import scan


def process():
    es = Elasticsearch(
        hosts=[
            {"host": "localhost", "port": 9201, "use_ssl": False},
        ],
        timeout=600,
    )

    results = scan(
        es,
        index="awc84m1arn3ebilwsafz,axy9lf00_iqe0ynvz1n1,venue_direct_instrument",
        preserve_order=True,
        query={
            "_source": ["instrumentIdCode", "derivative.priceMultiplier"],
            "query": {
                "bool": {
                    "must_not": {"exists": {"field": "&expiry"}},
                    "filter": [
                        {
                            "terms": {
                                "&model": [
                                    "FcaFirdsInstrument",
                                    "FirdsInstrument",
                                    "VenueDirectInstrument",
                                ]
                            }
                        },
                        {"term": {"venue.tradingVenue": "XEUR"}},
                    ],
                }
            },
        },
        size=1000,
    )

    firds = dict()
    venue = list()

    if os.path.exists("json_data.json"):
        with open("json_data.json", "r") as fp:
            data = json.load(fp)
            firds = data["firds"]
            venue = data["venue"]
    else:

        for result in results:
            try:
                if result["_type"] != "VenueDirectInstrument":
                    firds[result["_source"]["instrumentIdCode"]] = result["_source"][
                        "derivative"
                    ]["priceMultiplier"]
                else:
                    venue.append(
                        {
                            "instrumentIdCode": result["_source"]["instrumentIdCode"],
                            "priceMultiplier": result["_source"]["derivative"][
                                "priceMultiplier"
                            ],
                        }
                    )
            except Exception as ex:
                print(
                    f"Exception {ex} with instrument {result['_index']}/{result['_type']}/{result['_id']}"
                )

        with open("json_data.json", "w") as fp:
            json.dump({"firds": firds, "venue": venue}, fp)

    venue_no_match = []
    venue_match_wrong = []

    for item in venue:
        if item["instrumentIdCode"] not in firds:
            venue_no_match.append(item)
        else:
            if float(firds[item["instrumentIdCode"]]) != float(item["priceMultiplier"]):
                venue_match_wrong.append(
                    {
                        "instrumentIdCode": item["instrumentIdCode"],
                        "venue": item["priceMultiplier"],
                        "firds": firds[item["instrumentIdCode"]],
                    }
                )

    print()
    print("Unmatched Venue Direct Instruments")
    print("Instrument", "PriceMultiplier")
    for item in venue_no_match:
        print(f"{item['instrumentIdCode']}, {item['priceMultiplier']}")

    print()
    print("Matched instruments with mismatched price multiplier")
    print("Instrument", "PriceMultiplierVenue", "PriceMultiplierFirds")
    for item in venue_match_wrong:
        print(f"{item['instrumentIdCode']}, {item['venue']}, {item['firds']}")
    print()
    print(
        "> Note: The comparison was performed via conversion to float with the default threshold in python"
    )
    print(
        "'''float(firds[item['instrumentIdCode']]) != float(item['priceMultiplier'])'''"
    )


if __name__ == "__main__":
    process()

{"composed_of": ["{tenant}-analyzer-component_template"], "index_patterns": ["{tenant}-firds_instrument-index-*"], "priority": 100, "template": {"mappings": {"dynamic": "strict", "properties": {"&ancestor": {"type": "keyword"}, "&cascadeId": {"type": "keyword"}, "&content": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "&expiry": {"type": "date"}, "&hash": {"type": "keyword"}, "&id": {"type": "keyword"}, "&invocationId": {"type": "keyword"}, "&key": {"type": "keyword"}, "&link": {"type": "keyword"}, "&model": {"type": "keyword"}, "&parent": {"type": "keyword"}, "&realm": {"type": "keyword"}, "&status": {"type": "keyword"}, "&taskId": {"type": "keyword"}, "&timestamp": {"type": "date"}, "&traitFqn": {"type": "keyword"}, "&uniqueProps": {"type": "keyword"}, "&updater": {"type": "keyword"}, "&user": {"type": "keyword"}, "&validationErrors": {"dynamic": true, "properties": {"action": {"type": "keyword"}, "category": {"type": "keyword"}, "code": {"type": "keyword"}, "field_name": {"type": "keyword"}, "field_path": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "message": {"type": "keyword"}, "modules_affected": {"type": "keyword"}, "severity": {"type": "keyword"}, "source": {"type": "keyword"}}, "type": "nested"}, "&version": {"type": "integer"}, "bond": {"properties": {"debtSeniority": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "fixedRate": {"type": "double"}, "floatingRateBondIndexBenchmarkBasePointSpread": {"type": "integer"}, "floatingRateBondIndexBenchmarkId": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "floatingRateBondIndexBenchmarkName": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "floatingRateBondIndexBenchmarkTerm": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "maturityDate": {"type": "date"}, "nominalUnitOrMinTradedValue": {"type": "double"}, "nominalValueCurrency": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "totalIssuedNominalAmount": {"type": "double"}}}, "cfiAttribute1": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "cfiAttribute2": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "cfiAttribute3": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "cfiAttribute4": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "cfiCategory": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "cfiGroup": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "commoditiesOrEmissionAllowanceDerivativeInd": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "commodityAndEmissionAllowances": {"properties": {"baseProduct": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "contractDeliveryCapacity": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "deliveryDaysOfTheWeek": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "deliveryDuration": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "deliveryEnd": {"type": "date"}, "deliveryInterconnectionPoint": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "deliveryLoadIntervals": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "deliveryLoadType": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "deliveryPointorZone": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "deliveryStart": {"type": "date"}, "finalPriceType": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "furtherSubProduct": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "loadDescription": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "pricePerTimeIntPerQty": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "quantityUnitOfMeasure": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "subProduct": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "transactionType": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}}, "derivative": {"properties": {"creditAttachmentPoint": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "creditDetachmentPoint": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "creditDocClause": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "creditRunningCoupon": {"type": "integer"}, "creditTenor": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "creditTier": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "dayCount": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "deliveryType": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "expiryDate": {"type": "date"}, "isUserDefinedSpread": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "legs": {"properties": {"legOptionDelta": {"type": "double"}, "legPrice": {"type": "double"}, "legRatioQty": {"type": "double"}, "legSecurityId": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "legSide": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}}, "optionExerciseStyle": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "optionType": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "priceDisplayFactor": {"type": "double"}, "priceMultiplier": {"type": "double"}, "strikePrice": {"type": "double"}, "strikePriceCurrency": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "strikePricePending": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "underlyingIndexFactor": {"type": "integer"}, "underlyingIndexName": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "underlyingIndexSeries": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "underlyingIndexTerm": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "underlyingIndexTermValue": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "underlyingIndexVersion": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "underlyingInstrumentIdCodeFarLeg": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "underlyingInstrumentIdCodeNearLeg": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "underlyingInstruments": {"properties": {"underlyingInstrumentClassification": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "underlyingInstrumentCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "underlyingIssuer": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}}}}, "ext": {"properties": {"additionalIdentifiers": {"dynamic": "true", "type": "object"}, "additionalNames": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "aii": {"properties": {"daily": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "dailyWithoutStrike": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "exchangeProductCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "mic": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "underlyingSymbolExpiryCode": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "withoutStrike": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}}, "alternativeInstrumentIdentifier": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "bestExAssetClassMain": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "bestExAssetClassSub": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "cmeMifirEligible": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "compositeFigi": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "emirEligible": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "exchangeSymbol": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "exchangeSymbolBbg": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "exchangeSymbolESignal": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "exchangeSymbolLocal": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "exchangeSymbolRoot": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "figi": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "instrumentIdCodeType": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "instrumentUniqueIdentifier": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "issuerName": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "lei": {"properties": {"&hash": {"type": "keyword"}, "&id": {"type": "keyword"}, "&key": {"type": "keyword"}, "entity": {"properties": {"associatedEntity": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "businessRegisterEntityID": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "entityExpirationDate": {"type": "date"}, "entityExpirationReason": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "entityStatus": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "headquartersAddress": {"properties": {"city": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "country": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "line1": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "line2": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "line3": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "line4": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "postalCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "region": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}}, "legalAddress": {"properties": {"city": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "country": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "line1": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "line2": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "line3": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "line4": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "postalCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "region": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}}, "legalForm": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "legalJurisdiction": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "legalName": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "otherAddress": {"properties": {"city": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "country": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "line1": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "line2": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "line3": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "line4": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "postalCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "region": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}}, "otherEntityName": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "successorEntity": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}}, "lei": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "registration": {"properties": {"initialRegistrationDate": {"type": "date"}, "lastUpdateDate": {"type": "date"}, "managingLOU": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "nextRenewalDate": {"type": "date"}, "registrationStatus": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "validationSources": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}}}}, "localCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "mifirEligible": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "notionalCurrency2Type": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "onFIRDS": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "otcFlag": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "priceNotation": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "pricingReferences": {"dynamic": "true", "type": "object"}, "quantityNotation": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "spot": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "strikePriceType": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "toTv": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "tradedIn": {"dynamic": "true", "type": "object"}, "tradingLocation": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "tradingVenueCountry": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "tradingVenueCountryCode": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "uToTv": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "underlyingIndexClassification": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "underlyingIndexId": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "underlyingIndexTermId": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "underlyingInstruments": {"dynamic": "true", "type": "object"}, "underlyingOnFIRDS": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "underlyingOtcFlag": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "underlyingTradingVenueCountry": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "underlyingTradingVenueCountryCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "underlyingVenueInEEA": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "underlyingVenueOutsideEEA": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "venueIn": {"dynamic": "true", "type": "object"}, "venueInEEA": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "venueName": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "venueOutsideEEA": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "venueType": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}}, "fxDerivatives": {"properties": {"fxType": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "notionalCurrency2": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}}, "instrumentClassification": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "instrumentClassificationEMIRAssetClass": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "instrumentClassificationEMIRContractType": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "instrumentClassificationEMIRProductType": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "instrumentFullName": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "instrumentIdCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "interestRateDerivatives": {"properties": {"irContractTerm": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg1FixedRate": {"type": "double"}, "leg1FixedRateDayCount": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg1FloatingReferenceRate": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg1FloatingReferenceRateTerm": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg1FloatingReferenceRateTermValue": {"type": "integer"}, "leg1IndexSource": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg1PaymentFrequencyTerm": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg1PaymentFrequencyTermValue": {"type": "integer"}, "leg1ReferenceRate": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg1ReferenceRateISO": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg1ResetFrequencyTerm": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg1ResetFrequencyTermValue": {"type": "integer"}, "leg2FixedRate": {"type": "double"}, "leg2FixedRateDayCount": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg2FloatingRate": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg2FloatingReferenceRateTerm": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg2FloatingReferenceRateTermValue": {"type": "integer"}, "leg2IRContractTerm": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg2IndexSource": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg2NotionalCurrency2": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg2PaymentFrequencyTerm": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg2PaymentFrequencyTermValue": {"type": "integer"}, "leg2ReferenceRate": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg2ReferenceRateISO": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg2ResetFrequencyTerm": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "leg2ResetFrequencyTermValue": {"type": "integer"}, "notionalCurrency2": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "referenceRate": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "startDate": {"type": "date"}}}, "isCreatedThroughFallback": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "issuerOrOperatorOfTradingVenueId": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "notionalCurrency1": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "paymentFrequency": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "sourceIndex": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "sourceKey": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "systematicInternaliserCalculations": {"properties": {"from": {"type": "date"}, "sourceKey": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "to": {"type": "date"}, "turnoverExecutedInEurope": {"type": "double"}, "turnoverExecutedInEuropeEUR": {"type": "double"}}}, "transparencyCalculations": {"properties": {"averageDailyTransactionValue": {"type": "double"}, "averageDailyTransactionValueCurrency": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "averageDailyTurnover": {"type": "double"}, "averageDailyTurnoverCurrency": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "averageNumberOfTransactions": {"type": "double"}, "averageVolumeOfTransactions": {"type": "double"}, "from": {"type": "date"}, "liquid": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "liquidBand": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "methodology": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "mostRelevantMIC": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "mostRelevantMICAverageNumberOfDailyTransactions": {"type": "double"}, "post": {"properties": {"lisThresholdAmount": {"type": "double"}, "lisThresholdCurrency": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "sstiThresholdAmount": {"type": "double"}, "sstiThresholdCurrency": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}}, "pre": {"properties": {"lisThresholdAmount": {"type": "double"}, "lisThresholdCurrency": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "sstiThresholdAmount": {"type": "double"}, "sstiThresholdCurrency": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}}, "sourceKey": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "to": {"type": "date"}}}, "venue": {"properties": {"admissionToTradingApprovalDate": {"type": "date"}, "admissionToTradingOrFirstTradeDate": {"type": "date"}, "admissionToTradingRequestDate": {"type": "date"}, "financialInstrumentShortName": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "issuerRequestForAdmissionToTrading": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "terminationDate": {"type": "date"}, "tradingVenue": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}}, "venueDetails": {"properties": {"city": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "country": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "countryCode": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "created": {"type": "date"}, "inEEA": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "boolean"}, "lei": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "name": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "nca": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "operatingMic": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "type": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}}}}, "settings": {"gc_deletes": 0, "index.lifecycle.name": "{tenant}-default-ilm_policy", "index.lifecycle.rollover_alias": "{tenant}-firds_instrument-alias", "mapping.ignore_malformed": true, "max_inner_result_window": 1000, "number_of_replicas": 0, "number_of_shards": 3, "refresh_interval": -1}}}
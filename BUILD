python_requirements(
    name="3rdparty",
    source="requirements_3rdparty.txt",
    module_mapping={
        "conductor-python": ["conductor.client"],
        "faust-streaming": ["faust"],
        "pycryptodome": ["Crypto"],
        "mode-streaming": ["mode"],
        "python-gnupg": ["gnupg"],
        "python-slugify": ["slugify"],
    },
)

python_requirements(
    name="se_libs",
    source="requirements_se_libs.txt",
    module_mapping={
        "templated-email-generator": ["generator"],
    },
)

python_requirements(
    name="test_libs",
    source="requirements_tests.txt",
)

python_requirements(
    name="type_stubs",
    source="requirements_type_stubs.txt",
)

python_requirements(
    name="lexica_models",
    source="requirements_lexica_models.txt",
)

python_requirements(
    name="tools_libs",
    source="requirements_tools.txt",
)

python_requirements(
    name="machine_learning",
    source="requirements_machine_learning.txt",
)

python_source(name="pants-plugins", source="pants-plugins/macros.py")

###
@tenant=andurand
@ticket=eu-9373
@repo=stack-2024-02

http://localhost:9200/{{tenant}}:surveillance_watch,{{tenant}}:watch_execution/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            "should": [
                {
                    "terms": {
                        "&id": [
                            "e5ebc47c-2e3d-4405-9473-14c0df7d897a",
                            "7ae66a11-013d-4003-93c8-db111c90acc3",
                            "0894bc51-608c-4b3a-98cc-342ad0af4c99",
                            "bd5f8b8d-25d5-469f-b804-58b51f4e9221",
                            "e39bace6-1f9e-5e47-9c28-e1a7161490a3",
                            "3771d12d-0852-588c-a14d-0cc552426158",
                            "b833c700-e004-51cf-a7b4-213b214db391",
                            "99554256-fd26-4d8e-8bdc-5cd2cc8b39db",
                            "597d45fc-55cc-55c6-9f0c-5b59246b6643",
                            "beeb1d71-9284-51e8-9612-bb98b602c2e9",
                            "abcda268-9179-5e25-96da-2dad6c11cfb7"
                        ]
                    }
                },
                {
                    "terms": {
                        "detail.watchId": [
                            "e5ebc47c-2e3d-4405-9473-14c0df7d897a",
                            "7ae66a11-013d-4003-93c8-db111c90acc3",
                            "0894bc51-608c-4b3a-98cc-342ad0af4c99",
                            "bd5f8b8d-25d5-469f-b804-58b51f4e9221",
                            "e39bace6-1f9e-5e47-9c28-e1a7161490a3",
                            "3771d12d-0852-588c-a14d-0cc552426158",
                            "b833c700-e004-51cf-a7b4-213b214db391",
                            "99554256-fd26-4d8e-8bdc-5cd2cc8b39db",
                            "597d45fc-55cc-55c6-9f0c-5b59246b6643",
                            "beeb1d71-9284-51e8-9612-bb98b602c2e9",
                            "abcda268-9179-5e25-96da-2dad6c11cfb7"
                        ]
                    }
                }
            ],
            "minimum_should_match": "1"
        }
    },
    "aggs": {
        "&model": {
            "terms": {
                "field": "&model",
                "size": 100
            }
        },
        "featureFlags": {
            "terms": {
                "field": "featureFlags",
                "size": 100
            }
        }
    }
}

###
# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "{{tenant}}:surveillance_watch,{{tenant}}:watch_execution",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "Remove data from archived watches"
  }
}

###
# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###
# @name process

POST http://localhost:9200/{{tenant}}:surveillance_watch,{{tenant}}:watch_execution/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "should": [
                {
                    "terms": {
                        "&id": [
                            "e5ebc47c-2e3d-4405-9473-14c0df7d897a",
                            "7ae66a11-013d-4003-93c8-db111c90acc3",
                            "0894bc51-608c-4b3a-98cc-342ad0af4c99",
                            "bd5f8b8d-25d5-469f-b804-58b51f4e9221",
                            "e39bace6-1f9e-5e47-9c28-e1a7161490a3",
                            "3771d12d-0852-588c-a14d-0cc552426158",
                            "b833c700-e004-51cf-a7b4-213b214db391",
                            "99554256-fd26-4d8e-8bdc-5cd2cc8b39db",
                            "597d45fc-55cc-55c6-9f0c-5b59246b6643",
                            "beeb1d71-9284-51e8-9612-bb98b602c2e9",
                            "abcda268-9179-5e25-96da-2dad6c11cfb7"
                        ]
                    }
                },
                {
                    "terms": {
                        "detail.watchId": [
                            "e5ebc47c-2e3d-4405-9473-14c0df7d897a",
                            "7ae66a11-013d-4003-93c8-db111c90acc3",
                            "0894bc51-608c-4b3a-98cc-342ad0af4c99",
                            "bd5f8b8d-25d5-469f-b804-58b51f4e9221",
                            "e39bace6-1f9e-5e47-9c28-e1a7161490a3",
                            "3771d12d-0852-588c-a14d-0cc552426158",
                            "b833c700-e004-51cf-a7b4-213b214db391",
                            "99554256-fd26-4d8e-8bdc-5cd2cc8b39db",
                            "597d45fc-55cc-55c6-9f0c-5b59246b6643",
                            "beeb1d71-9284-51e8-9612-bb98b602c2e9",
                            "abcda268-9179-5e25-96da-2dad6c11cfb7"
                        ]
                    }
                }
            ],
            "minimum_should_match": "1"
        }
    }

}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

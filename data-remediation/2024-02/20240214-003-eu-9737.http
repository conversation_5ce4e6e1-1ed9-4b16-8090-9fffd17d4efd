@tenant=thornbridge
@ticket=eu-9737
@repo=stack-2024-02

http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            "filter": [
                {
                    "term": {
                        "&id": "1707782400000:NEWT:202402135424150801"
                    }
                }
            ]
        }
    },
    "aggs": {
        "&models": {
            "terms": {
                "field": "&model",
                "size": 100
            }
        },
        "_index": {
            "terms": {
                "field": "_index",
                "size": 100
            }
        },
        "sourceKey": {
            "terms": {
                "field": "sourceKey"
            }
        }
    }
}

###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "awxrlekwhphtf3kqjcoh",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "<PERSON>",
    "taken_because": "delete transaction"
  }
}

###
# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###
# @name process

POST http://localhost:9200/awxrlekwhphtf3kqjcoh/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
        "query": {
        "bool": {
            "filter": [
                {
                    "term": {
                        "&id": "1707782400000:NEWT:202402135424150801"
                    }
                }
            ]
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

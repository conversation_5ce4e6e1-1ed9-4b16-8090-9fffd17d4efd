###
@tenant=barings-uat
@ticket=eu-9797
@repo=stack-2024-02

### snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  "indices": "barings-uat_steeleye_co_order,barings-uat_steeleye_co_rts22transaction,barings-uat_steeleye_co_sinkrecordaudit,barings-uat_steeleye_co_sinkfileaudit,barings-uat_steeleye_co_quarantinedorder",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "cleanup uat environment for barings"
  }
}

### check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###
# @name process

POST http://localhost:9200/barings-uat_steeleye_co_order,barings-uat_steeleye_co_rts22transaction,barings-uat_steeleye_co_sinkrecordaudit,barings-uat_steeleye_co_sinkfileaudit,barings-uat_steeleye_co_quarantinedorder/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
     "query": {
        "match_all": {}
    }
}

###
@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

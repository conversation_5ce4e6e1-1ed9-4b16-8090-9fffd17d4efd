@tenant=dcme
@ticket=eng-7944
@repo=stack-2024-02

DELETE http://localhost:9200/.abaci_2_0_0/Policy/dcme:policy:2024

### check participants
http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "size": 1,
    "query": {
        "bool": {
            "filter": [
                {
                    "terms": {
                        "&model": [
                            "Call",
                            "Email",
                            "Message",
                            "Text",
                            "Meeting",
                            "ChatEvent",
                            "CommunicationAlert"
                        ]
                    }
                }
                ,
                {
                    "bool": {
                        "should": [
                            {
                                "nested": {
                                    "path": "participants",
                                    "query": {
                                        "bool": {
                                            "must_not": [
                                                {
                                                    "term": {
                                                        "participants.value.counterparty.name": "Daiwa Deutschland"
                                                    }
                                                }
                                            ]
                                        }
                                    },
                                    "ignore_unmapped": true
                                }
                            },
                            {
                                "nested": {
                                    "path": "hit.participants",
                                    "query": {
                                        "bool": {
                                            "must_not": [
                                                {
                                                    "term": {
                                                        "hit.participants.value.counterparty.name": "Daiwa Deutschland"
                                                    }
                                                }
                                            ]
                                        }
                                    },
                                    "ignore_unmapped": true
                                }
                            }
                        ],
                        "minimum_should_match": "1"
                    }
                }
            ]
        }
    },
    "aggs": {
        "hit.participants": {
            "nested": {
                "path": "hit.participants"
            },
            "aggs": {
                "status": {
                    "terms": {
                        "field": "hit.participants.value.counterparty.name",
                        "size": 1000
                    }
                }
            }
        },
        "participants": {
            "nested": {
                "path": "participants"
            },
            "aggs": {
                "status": {
                    "terms": {
                        "field": "participants.value.counterparty.name",
                        "size": 1000
                    }
                }
            }
        }

    }
}

### set my JobTitle

http://localhost:9200/{{tenant}}:account_user/_search
Content-Type: application/json

{
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ],
            "filter": [
                {
                    "term": {
                        "email": "<EMAIL>"
                    }
                }
            ]
        }
    }
}

###
###
# @name process

POST http://localhost:9200/{{tenant}}:account_user/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "lang": "painless",
        "source": "ctx._source['jobTitle']='DCMD FO & Non FO';ctx._source['&version']=ctx._source['&version']+1"
    },
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ],
            "filter": [
                {
                    "term": {
                        "email": "<EMAIL>"
                    }
                }
            ]
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json


###

POST http://localhost:9200/.abaci_2_0_0/Policy/dcme:policy:2024
Content-Type: application/json

{
    "urn": "dcme:policy:2024",
    "&id": "dcme:policy:2024",
    "evaluations": [
        {
            "evaluatees": [
                {
                    "negate": true,
                    "attribute": "$subject#JOB_TITLE",
                    "function": "IN",
                    "value": [
                        "DCME and Non DCMD",
                        "DCMD Front Office",
                        "DCMD non Front Office",
                        "DCMD FO & Non FO"
                    ]
                }
            ],
            "expression": {
                "bool": {
                    "should": [
                        {
                            "bool": {
                                "must_not": [
                                    {
                                        "terms": {
                                            "&model": [
                                                "Call",
                                                "Email",
                                                "Message",
                                                "Text",
                                                "Meeting",
                                                "ChatEvent",
                                                "CommunicationAlert"
                                            ]
                                        }
                                    }
                                ]
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            }
        },
        {
            "evaluatees": [
                {
                    "negate": false,
                    "attribute": "$subject#JOB_TITLE",
                    "function": "IN",
                    "value": [
                        "DCME and Non DCMD"
                    ]
                }
            ],
            "expression": {
                "bool": {
                    "should": [
                        {
                            "bool": {
                                "must_not": [
                                    {
                                        "terms": {
                                            "&model": [
                                                "Call",
                                                "Email",
                                                "Message",
                                                "Text",
                                                "Meeting",
                                                "ChatEvent",
                                                "CommunicationAlert"
                                            ]
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            "bool": {
                                "filter": [
                                    {
                                        "terms": {
                                            "&model": [
                                                "Call",
                                                "Email",
                                                "Message",
                                                "Text",
                                                "Meeting",
                                                "ChatEvent",
                                                "CommunicationAlert"
                                            ]
                                        }
                                    },
                                    {
                                        "bool": {
                                            "should": [
                                                {
                                                    "nested": {
                                                        "path": "participants",
                                                        "query": {
                                                            "bool": {
                                                                "must_not": [
                                                                    {
                                                                        "term": {
                                                                            "participants.value.counterparty.name": "Daiwa Deutschland"
                                                                        }
                                                                    }
                                                                ]
                                                            }
                                                        },
                                                        "ignore_unmapped": true
                                                    }
                                                },
                                                {
                                                    "nested": {
                                                        "path": "hit.participants",
                                                        "query": {
                                                            "bool": {
                                                                "must_not": [
                                                                    {
                                                                        "term": {
                                                                            "hit.participants.value.counterparty.name": "Daiwa Deutschland"
                                                                        }
                                                                    }
                                                                ]
                                                            }
                                                        },
                                                        "ignore_unmapped": true
                                                    }
                                                }
                                            ],
                                            "minimum_should_match": "1"
                                        }
                                    }
                                ]
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            }
        },
        {
            "evaluatees": [
                {
                    "negate": false,
                    "attribute": "$subject#JOB_TITLE",
                    "function": "IN",
                    "value": [
                        "DCMD FO & Non FO"
                    ]
                }
            ],
            "expression": {
                "bool": {
                    "should": [
                        {
                            "bool": {
                                "must_not": [
                                    {
                                        "terms": {
                                            "&model": [
                                                "Call",
                                                "Email",
                                                "Message",
                                                "Text",
                                                "Meeting",
                                                "ChatEvent",
                                                "CommunicationAlert"
                                            ]
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            "bool": {
                                "filter": [
                                    {
                                        "terms": {
                                            "&model": [
                                                "Call",
                                                "Email",
                                                "Message",
                                                "Text",
                                                "Meeting",
                                                "ChatEvent",
                                                "CommunicationAlert"
                                            ]
                                        }
                                    },
                                    {
                                        "bool": {
                                            "should": [
                                                {
                                                    "nested": {
                                                        "path": "participants",
                                                        "query": {
                                                            "bool": {
                                                                "filter": [
                                                                    {
                                                                        "term": {
                                                                            "participants.value.counterparty.name": "Daiwa Deutschland"
                                                                        }
                                                                    }
                                                                ]
                                                            }
                                                        },
                                                        "ignore_unmapped": true
                                                    }
                                                },
                                                {
                                                    "nested": {
                                                        "path": "hit.participants",
                                                        "query": {
                                                            "bool": {
                                                                "filter": [
                                                                    {
                                                                        "term": {
                                                                            "hit.participants.value.counterparty.name": "Daiwa Deutschland"
                                                                        }
                                                                    }
                                                                ]
                                                            }
                                                        },
                                                        "ignore_unmapped": true
                                                    }
                                                }
                                            ],
                                            "minimum_should_match": "1"
                                        }
                                    }
                                ]
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            }
        },
        {
            "evaluatees": [
                {
                    "negate": false,
                    "attribute": "$subject#JOB_TITLE",
                    "function": "IN",
                    "value": [
                        "DCMD Front Office"
                    ]
                }
            ],
            "expression": {
                "bool": {
                    "should": [
                        {
                            "bool": {
                                "must_not": [
                                    {
                                        "terms": {
                                            "&model": [
                                                "Call",
                                                "Email",
                                                "Message",
                                                "Text",
                                                "Meeting",
                                                "ChatEvent",
                                                "CommunicationAlert"
                                            ]
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            "bool": {
                                "filter": [
                                    {
                                        "terms": {
                                            "&model": [
                                                "Call",
                                                "Email",
                                                "Message",
                                                "Text",
                                                "Meeting",
                                                "ChatEvent",
                                                "CommunicationAlert"
                                            ]
                                        }
                                    },
                                    {
                                        "bool": {
                                            "should": [
                                                {
                                                    "nested": {
                                                        "path": "participants",
                                                        "query": {
                                                            "bool": {
                                                                "filter": [
                                                                    {
                                                                        "term": {
                                                                            "participants.value.counterparty.name": "Daiwa Deutschland"
                                                                        }
                                                                    },
                                                                    {
                                                                        "term": {
                                                                            "participants.value.structure.role": true
                                                                        }
                                                                    }
                                                                ]
                                                            }
                                                        },
                                                        "ignore_unmapped": true
                                                    }
                                                },
                                                {
                                                    "nested": {
                                                        "path": "hit.participants",
                                                        "query": {
                                                            "bool": {
                                                                "filter": [
                                                                    {
                                                                        "term": {
                                                                            "hit.participants.value.counterparty.name": "Daiwa Deutschland"
                                                                        }
                                                                    },
                                                                    {
                                                                        "term": {
                                                                            "hit.participants.value.structure.role": true
                                                                        }
                                                                    }
                                                                ]
                                                            }
                                                        },
                                                        "ignore_unmapped": true
                                                    }
                                                }
                                            ],
                                            "minimum_should_match": "1"
                                        }
                                    }
                                ]
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            }
        },
        {
            "evaluatees": [
                {
                    "negate": false,
                    "attribute": "$subject#JOB_TITLE",
                    "function": "IN",
                    "value": [
                        "DCMD non Front Office"
                    ]
                }
            ],
            "expression": {
                "bool": {
                    "should": [
                        {
                            "bool": {
                                "must_not": [
                                    {
                                        "terms": {
                                            "&model": [
                                                "Call",
                                                "Email",
                                                "Message",
                                                "Text",
                                                "Meeting",
                                                "ChatEvent",
                                                "CommunicationAlert"
                                            ]
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            "bool": {
                                "filter": [
                                    {
                                        "terms": {
                                            "&model": [
                                                "Call",
                                                "Email",
                                                "Message",
                                                "Text",
                                                "Meeting",
                                                "ChatEvent",
                                                "CommunicationAlert"
                                            ]
                                        }
                                    },
                                    {
                                        "bool": {
                                            "should": [
                                                {
                                                    "nested": {
                                                        "path": "participants",
                                                        "query": {
                                                            "bool": {
                                                                "filter": [
                                                                    {
                                                                        "term": {
                                                                            "participants.value.counterparty.name": "Daiwa Deutschland"
                                                                        }
                                                                    },
                                                                    {
                                                                        "term": {
                                                                            "participants.value.structure.role": false
                                                                        }
                                                                    }
                                                                ]
                                                            }
                                                        },
                                                        "ignore_unmapped": true
                                                    }
                                                },
                                                {
                                                    "nested": {
                                                        "path": "hit.participants",
                                                        "query": {
                                                            "bool": {
                                                                "filter": [
                                                                    {
                                                                        "term": {
                                                                            "hit.participants.value.counterparty.name": "Daiwa Deutschland"
                                                                        }
                                                                    },
                                                                    {
                                                                        "term": {
                                                                            "hit.participants.value.structure.role": false
                                                                        }
                                                                    }
                                                                ]
                                                            }
                                                        },
                                                        "ignore_unmapped": true
                                                    }
                                                }
                                            ],
                                            "minimum_should_match": "1"
                                        }
                                    }
                                ]
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            }
        }


    ],
    "resourceUrns": [
        "dcme:*"
    ],
    "effect": "DENY",
    "action": "READ",
    "&key": "Policy:dcme:policy:2024:1702364623387",
    "enabled": true,
    "&timestamp": "1702364623387",
    "&model": "Policy"
}



















### check policies
http://localhost:9200/.abaci_2_0_0/_search
Content-Type: application/json

{
  "query": {
    "bool": {
      "must_not": {
        "exists": {
          "field": "&expiry"
        }
      },
      "filter": [
        {
            "term": {
                "&model": "Policy"
            }
        }
        ,
        {
            "term": {
                "resourceUrns": "dcme:*"
            }
        }
      ]
    }
  }
}

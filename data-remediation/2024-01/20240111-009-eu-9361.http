@tenant=caxton
@ticket=eu-9365
@repo=stack-2024-01

### Algos in use
http://localhost:9200/*:tenant_configuration/_search
Content-Type: application/json

{
    "size": 0,
    "aggs": {
        "subscribedMarketAbuseReports": {
            "terms": {
                "field": "subscribedMarketAbuseReports",
                "size": 200
            }
        }
    }
}

###
http://localhost:9200/{{tenant}}:tenant_configuration/_search
Content-Type: application/json

{
    "_source": [
        "subscribedMarketAbuseReports"
    ],
    "size": 1,
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            }
        }
    },
    "aggs": {
        "&model": {
            "terms": {
                "field": "&model",
                "size": 100
            }
        }
    }
}

###
# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "{{tenant}}:tenant_configuration",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "update algos"
  }
}

###
# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###
# @name process

POST http://localhost:9200/{{tenant}}:tenant_configuration/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "lang": "painless",
        "source": "ctx._source['subscribedMarketAbuseReports']=params.subscribedMarketAbuseReports;",
        "params": {
            "subscribedMarketAbuseReports": [
                "MARKING_THE_CLOSE",
                "LAYERING_V2",
                "SPOOFING_V2",
                "PAINTING_THE_TAPE",
                "WASH_TRADING",
                "PHISHING_V2",
                "INSIDER_TRADING_V3_REFINITIV",
                "INSIDER_TRADING_V3",
                "FRONT_RUNNING_V2",
                "POTAM",
                "SUSPICIOUS_LARGE_ORDER_VOLUME_V2"
            ]
        }
    },
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            }
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json


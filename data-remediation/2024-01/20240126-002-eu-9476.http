@tenant=crestline
@ticket=eu-9476
@repo=stack-2024-01
@watch=755b2257-43a5-489f-899d-0aea08f710c2

http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "size": 1,
    "query": {
        "bool": {
            "should": [
                {
                    "term": {
                        "&id": "{{watch}}"
                    }
                },
                {
                    "term": {
                        "detail.watchId": "{{watch}}"
                    }
                }
            ],
            "minimum_should_match": "1"
        }
    },
    "aggs": {
        "&model": {
            "terms": {
                "field": "&model",
                "size": 100
            }
        },
        "_index": {
            "terms": {
                "field": "_index",
                "size": 100
            }
        }
    }
}

###
# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "crestline_steeleye_co_surveillancewatchexecution,crestline_steeleye_co_surveillancewatch",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "cleanup on client request"
  }
}

###
# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###
# @name process
POST http://localhost:9200/crestline_steeleye_co_surveillancewatchexecution,crestline_steeleye_co_surveillancewatch/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "should": [
                {
                    "term": {
                        "&id": "{{watch}}"
                    }
                },
                {
                    "term": {
                        "detail.watchId": "{{watch}}"
                    }
                }
            ],
            "minimum_should_match": "1"
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json


@tenant=shell
@ticket=eu-9999
@repo=stack-2024-01

http://localhost:9200/{{tenant}}:tenant_configuration/_search
Content-Type: application/json

{
    "_source": [
        "subscribedMarketAbuseReports",
        "subscribedModules",
        "featureFlags"
    ],
    "size": 1,
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            }
        }
    },
    "aggs": {
        "&model": {
            "terms": {
                "field": "&model",
                "size": 100
            }
        },
        "featureFlags": {
            "terms": {
                "field": "featureFlags",
                "size": 100
            }
        }
    }
}

###
# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "*:tenant_configuration",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "Q4 algo changes EU-9415, EU-9416 and EU-9417"
  }
}

###
# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json


###
# @name process

POST http://localhost:9200/{{tenant}}:tenant_configuration/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "lang": "painless",
        "source": "ctx._source['subscribedMarketAbuseReports']=params.subscribedMarketAbuseReports;",
        "params": {
          "subscribedMarketAbuseReports": [
                "WASH_TRADING",
                "FRONT_RUNNING_V2",
                "SUSPICIOUS_LARGE_ORDER_VOLUME_V2",
                "INSIDER_TRADING_V2",
                "INSIDER_TRADING_NEWS",
                "POTAM",
                "MARKING_THE_CLOSE",
                "MARKING_THE_OPEN",
                "LAYERING_V2",
                "PHISHING_V2",
                "SPOOFING_V2",
                "QUOTE_STUFFING",
                "INSIDER_TRADING_V3",
                "RESTRICTED_LIST_V2"
            ]
        }
    },
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            }
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json


@host={{$dotenv ES8_prod-shared-3_HOST}} 
@auth={{$dotenv ES8_prod-shared-3_AUTH}}
#
#
#
@tenant=dcme
@ticket=eu-11642
@repo=stack-2024-08

###
GET https://{{tenant}}.steeleye.co/api/v4.0/version

###
GET {{host}}
Content-Type: application/json
Authorization: {{auth}}

###
POST {{host}}/{{tenant}}-*-alias/_flush
Content-Type: application/json
Authorization: {{auth}}

###
POST {{host}}/{{tenant}}-*-alias/_refresh
Content-Type: application/json
Authorization: {{auth}}

###
GET {{host}}/{{tenant}}-*-alias/_search
Content-Type: application/json
Authorization: {{auth}}

{
    "_source": [
        "hit.metadata"
    ],
    "size": 0,
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ],
            "filter": [
                {
                    "terms": {
                        "slug": [
                            "comms-s42967",
                            "comms-s41437",
                            "comms-s41178",
                            "comms-s40857",
                            "comms-se40544",
                            "comms-se40543",
                            "comms-se40542",
                            "comms-s40314",
                            "comms-s40315",
                            "comms-s40312",
                            "comms-s40311",
                            "comms-s40259",
                            "comms-s39753",
                            "comms-s39661",
                            "comms-s39563",
                            "comms-s36294"
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "key": {
            "terms": {
                "field": "hit.metadata.source.fileInfo.location.key",
                "size": 100
            }
        }
    }
}

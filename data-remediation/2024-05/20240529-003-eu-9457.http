@tenant=*
@ticket=eu-10739
@repo=stack-2024-05
@tenants=bluedrive:*,bluedrive_*,ryebay:*,ryebay_*,ardevora:*,ardevora_*,mgts:*,mgts_*,hywin:*,hywin_*,olivetree:*,olivetree_*,onefinancial:*,onefinancial_*,portsea:*,portsea_*,monsas:*,monsas_*,steadfast:*,steadfast_*,alanda:*,alanda_*,shore:*,shore_*

### indices from aliases
http://localhost:9200/_cat/aliases/{{tenants}}?v&h=index

###
http://localhost:9200/_cat/indices/*:sequence,*_sequence,*excl*?v&h=index

###
@indices1=axydftywsfhj0v1iil16,olivetree_quarantined_order_20230415,axydfqmisfhj0v1iil12,olivetree_uat_steeleye_co_marketabuseaudit,olivetree_uat_steeleye_co_tenantsurveillancenotificationsetting,axyde2eosfhj0v1iil1j,olivetree_uat_steeleye_co_sinkfileaudit,axydezgrsfhj0v1iil1h,mgts_uat_steeleye_co_usercomment,olivetree_uat_steeleye_co_quarantinedemiruti,axydfsrvsfhj0v1iil15,mgts_watch_execution_20230414,axydfhsusfhj0v1iil2d,axyder98sfhj0v1iil1d,olivetree_uat_steeleye_co_systemevent,axydfq6nsfhj0v1iil13,axydfcdgsfhj0v1iil2a,olivetree_uat_steeleye_co_accountperson,olivetree_uat_steeleye_co_sinkaudit,olivetree_uat_steeleye_co_tenantdatasource,olivetree_order_20230415,olivetree_uat_steeleye_co_cascadeaudit,olivetree_uat_steeleye_co_workflowaudit,olivetree_uat_steeleye_co_tradeanalyticsaudit,olivetree_uat_steeleye_co_accountfirm,olivetree_uat_steeleye_co_surveillancewatchpendingchange,axyde4exsfhj0v1iil1k,mgts_quarantined_order_20230414,axydfabmsfhj0v1iil1_,olivetree_uat_steeleye_co_marrestrictedlist,olivetree_uat_steeleye_co_position,olivetree_uat_steeleye_co_rts22transactionreport,olivetree_uat_steeleye_co_quarantinedrts22transaction,mgts_case_20230414,olivetree_uat_steeleye_co_tenantconfiguration,mgts_uat_steeleye_co_userbookmark,olivetree_uat_steeleye_co_usercomment,olivetree_uat_steeleye_co_rts22transaction,olivetree_case_20230415,olivetree_uat_steeleye_co_insightsreport,axydf01ksfhj0v1iil2n,olivetree_uat_steeleye_co_usererror,axydf3sqsfhj0v1iil2o,olivetree_uat_steeleye_co_marketperson,axydexu5sfhj0v1iil1g,olivetree_uat_steeleye_co_quarantinedtransaction,axydffinsfhj0v1iil2c,mgts_uat_steeleye_co_rating,axydfyz8sfhj0v1iil2m,olivetree_uat_steeleye_co_useraudit,mgts_order_20230414,olivetree_watch_execution_20230415,axydfej2sfhj0v1iil2b,mgts_uat_steeleye_co_position,olivetree_uat_steeleye_co_quarantinedemirposition,axydfyxmsfhj0v1iil1-,olivetree_uat_steeleye_co_surveillancewatch,olivetree_uat_steeleye_co_usercred,olivetree_uat_steeleye_co_marketcounterparty,axydfupjsfhj0v1iil2k,axydfag_sfhj0v1iil1s,olivetree_uat_steeleye_co_tenantsurveillanceworkflowsetting,olivetree_uat_steeleye_co_accountuser,olivetree_uat_steeleye_co_userbookmark,axydeuggsfhj0v1iil1e,olivetree_uat_steeleye_co_rating,olivetree_uat_steeleye_co_instrument,olivetree_uat_steeleye_co_sinkrecordaudit,axydfnfasfhj0v1iil2h,mgts_uat_steeleye_co_systemevent
###
# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}_before_cleanup
Content-Type: application/json

{
    "ignore_unavailable": true,
    "include_global_state": false,
    "metadata": {
        "taken_by": "Luis F",
        "taken_because": "offboarding and removal of not required tenants"
    }
}

###
# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}_before_cleanup
Content-Type: application/json

### delete... freetrade:surv_exclude_by_id,freetrade:sequence
DELETE http://localhost:9200/{{indices1}}

### check
http://localhost:9200/_cat/aliases/{{tenants}}?v&h=index

### check 2
http://localhost:9200/_cat/indices?v&h=index

### delete aliases from empty index
http://localhost:9200/_cat/aliases/{{tenants}}?v&h=alias

###
http://localhost:9200/empty_index/_settings

###
http://localhost:9200/empty_index/_count

###
PUT http://localhost:9200/empty_index/_settings
Content-Type: application/json

{
  "settings": {
    "index.blocks.read_only": false
  }
}

###
@aliases=mgts:attachment,mgts:call,mgts:chat_event,mgts:email,mgts:emir_collateral,mgts:emir_report,mgts:emir_uti,mgts:insights_report,mgts:lexicon,mgts:market_suggestion,mgts:meeting,mgts:message,mgts:quarantined_emir_position,mgts:quarantined_emir_uti,mgts:quarantined_rts22_transaction,mgts:quarantined_transaction,mgts:rts22_transaction,mgts:rts22_transaction_report,mgts:sink_record_audit,mgts:text,mgts:trade_analytics_audit,mgts:transaction_report,mgts:transcript,mgts:user_session,mgts:workflow_audit,olivetree:attachment,olivetree:call,olivetree:chat_event,olivetree:email,olivetree:emir_collateral,olivetree:emir_report,olivetree:emir_uti,olivetree:lexicon,olivetree:market_suggestion,olivetree:meeting,olivetree:message,olivetree:text,olivetree:transaction,olivetree:transaction_report,olivetree:transcript,olivetree:user_session

DELETE http://localhost:9200/empty_index/_aliases/{{aliases}}

###
PUT http://localhost:9200/empty_index/_settings
Content-Type: application/json

{
  "settings": {
    "index.blocks.read_only": true
  }
}

###
http://localhost:9200/_cat/aliases/shore:*,shore_*?v&h=index,alias


###
@tenant=pggm
@ticket=eu-10505
@repo=stack-2024-04
@watchid=9c0ca1a5-34a8-49a6-bd5b-bce6b6b35b81

###
http://localhost:9200/{{tenant}}:watch_execution,{{tenant}}:surveillance_watch/_search?version=true
Content-Type: application/json

{
    "_source": [
        "featureFlags",
        "subscribedModules",
        "subscribedMarketAbuseReports"
    ],
    "size": 1,
    "query": {
        "bool": {
            "should": [
                {
                    "term": {
                        "&id": "{{watchid}}"
                    }
                },
                {
                    "term": {
                        "detail.watchId": "{{watchid}}"
                    }
                }
            ]
        }
    },
    "aggs": {
        "&model": {
            "terms": {
                "field": "&model",
                "size": 100
            }
        }
    }
}

### snapshots
http://localhost:9200/_cat/snapshots/{{repo}}?v&s=start_epoch


###
# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
    "indices": "{{tenant}}:watch_execution,{{tenant}}:surveillance_watch",
    "ignore_unavailable": true,
    "include_global_state": false,
    "metadata": {
        "taken_by": "Luis F",
        "taken_because": "cleanup of watch and related alerts/tags"
    }
}

###
# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###
# @name process

POST http://localhost:9200/{{tenant}}:watch_execution,{{tenant}}:surveillance_watch/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "should": [
                {
                    "term": {
                        "&id": "{{watchid}}"
                    }
                },
                {
                    "term": {
                        "detail.watchId": "{{watchid}}"
                    }
                }
            ]
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

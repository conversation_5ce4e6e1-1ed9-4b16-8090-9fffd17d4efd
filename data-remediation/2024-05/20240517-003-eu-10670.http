# prod-i3
@tenant=*
@ticket=eu-10662
@repo=stack-2024-05

GET http://localhost:9200/{{tenant}}:surveillance_watch/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ],
            "filter": {
                "terms": {
                    "&id": [
                        "d69b5ae0-a7dd-5b3e-a6dd-2b64e10681b0",
                        "c5524fb6-c2d2-4f03-965c-a65b19128465",
                        "bc59f014-cb51-5be0-9fb1-bc2383e36a43",
                        "780849b6-3061-4935-a0e3-16dfcfa9d236",
                        "63298fcd-f655-4489-afbf-6a01ed15c74e",
                        "39065f81-edd8-5351-91e6-f0edca262f16",
                        "7e032e5a-5703-4bee-b933-7638c89604f5",
                        "5a2f0039-f217-4c98-97b5-2231c3f579d2",
                        "4ce8f44c-996b-46c4-bb34-bf59e4796e18",
                        "3e3ac9ee-c536-4fd5-949d-5d54e43fb697",
                        "1e0c8ed8-1bb4-4eb4-9263-2d5896886767"
                    ]
                }
            }
        }
    },
    "aggs": {
        "indices": {
            "terms": {
                "field": "_index",
                "size": 1000
            }
        }
    }
}

###
# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "*:watch_execution,*:surveillance_watch",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "rewind watches and reprocess"
  }
}

###
# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###
# @name process
POST http://localhost:9200/axsxa7wwjontyhjmws4x,mar_steeleye_co_surveillancewatch,awjbhq-x9zoh89gmqm-_,awwabjrtf0unlugszwr0,awxrllyrhphtf3kqjcod,axfl-nue8kmzqddk7djt,axmlj4z7ehgfth-8r7gc,ciam_steeleye_co_surveillancewatch,olivetree_steeleye_co_surveillancewatch/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "source": "ctx._source.executionDetails.lastExecution=2031467533000L;",
        "lang": "painless"
    },
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ],
            "filter": {
                "terms": {
                    "&id": [
                        "d69b5ae0-a7dd-5b3e-a6dd-2b64e10681b0",
                        "c5524fb6-c2d2-4f03-965c-a65b19128465",
                        "bc59f014-cb51-5be0-9fb1-bc2383e36a43",
                        "780849b6-3061-4935-a0e3-16dfcfa9d236",
                        "63298fcd-f655-4489-afbf-6a01ed15c74e",
                        "39065f81-edd8-5351-91e6-f0edca262f16",
                        "7e032e5a-5703-4bee-b933-7638c89604f5",
                        "5a2f0039-f217-4c98-97b5-2231c3f579d2",
                        "4ce8f44c-996b-46c4-bb34-bf59e4796e18",
                        "3e3ac9ee-c536-4fd5-949d-5d54e43fb697",
                        "1e0c8ed8-1bb4-4eb4-9263-2d5896886767"
                    ]
                }
            }
        }
    }
}

###
@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json


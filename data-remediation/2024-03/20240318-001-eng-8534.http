###
@tenant=barings-uat
@ticket=eng-8534
@repo=stack-2024-03


### check policies
http://localhost:9200/.abaci_2_0_0/_search
Content-Type: application/json

{
    "query": {
        "bool": {
        "must_not": {
            "exists": {
                "field": "&expiry"
            }
        },
        "filter": [
                {
                    "term": {
                        "&model": "Policy"
                    }
                },
                {
                    "term": {
                        "resourceUrns": "{{tenant}}:*"
                    }
                }
            ]
        }
    }
}

###
GET http://localhost:9200/.abaci_2_0_0/Policy/barings-uat:policy:2024-03-18
Content-Type: application/json

### search 
# rule 2: All other Users - should only see Aladdin Data

http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
	"size": 0,
    # "_source": [
    #     "sourceKey",
    #     "key",
    #     "dataSource.sourceKey",
    #     "voiceFile.fileInfo.location.key",
    #     "metadata.source.fileInfo.location.key",
    #     "fileInfo.location.key",
    #     "voiceTranscript.fileInfo.location.key",
    #     "hit.sourceKey",
    #     "hit.key",
    #     "hit.dataSource.sourceKey",
    #     "hit.voiceFile.fileInfo.location.key",
    #     "hit.metadata.source.fileInfo.location.key",
    #     "hit.fileInfo.location.key",
    #     "hit.voiceTranscript.fileInfo.location.key",
    #     "metadata.client"
    # ],
	"query": {
		"bool": {
			"filter": [
				{
					"terms": {
						"&model": [
                            "OrderState",
                            "SinkFileAudit",
                            "Order",
                            "QuarantinedOrder",
                            "SinkRecordAudit",
                            "MarketAbuseAlert",
                            "SinkAudit",
							"MarketCounterparty"
						]
					}
				}
			],
            "should": [
				{
                    "wildcard": {
                        "hit.sourceKey": "s3://barings-uat.steeleye.co/flows/order-feed-aladdin/*"
                    }
                },
                {
                    "wildcard": {
                        "hit.key": "flows/order-feed-aladdin/*"
                    }
                },
				{
                    "wildcard": {
                        "sourceKey": "s3://barings-uat.steeleye.co/flows/order-feed-aladdin/*"
                    }
                },
                {
                    "wildcard": {
                        "key": "flows/order-feed-aladdin/*"
                    }
                },
                {
                    "wildcard": {
                        "input": "s3://barings-uat.steeleye.co/flows/order-feed-aladdin/*"
                    }
                },
				{
                    "wildcard": {
                        "hit.sourceKey": "s3://barings-uat.steeleye.co/flows/mymarket-aladdin-broker-firm/*"
                    }
                },
                {
                    "wildcard": {
                        "hit.key": "flows/mymarket-aladdin-broker-firm/*"
                    }
                },
				{
                    "wildcard": {
                        "sourceKey": "s3://barings-uat.steeleye.co/flows/mymarket-aladdin-broker-firm/*"
                    }
                },
                {
                    "wildcard": {
                        "key": "flows/mymarket-aladdin-broker-firm/*"
                    }
                },
                {
                    "wildcard": {
                        "input": "s3://barings-uat.steeleye.co/flows/mymarket-aladdin-broker-firm/*"
                    }
                }
			],
			"minimum_should_match": 1
		}
	},
	"aggs": {
		"sourceKey": {
			"terms": {
				"field": "sourceKey",
				"size": 10000
			}
		},
        "model": {
            "terms": {
                "field": "&model",
                "size": 10000
            }
        }
	}
}

###
# rule 1: all Korea Users - should only see data for SE Blotter -> aka exclude all data from aladdin

http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
	"size": 0,
	"query": {
		"bool": {
			"filter": [
				{
					"terms": {
						"&model": [
                            "OrderState",
                            "SinkFileAudit",
                            "Order",
                            "QuarantinedOrder",
                            "SinkRecordAudit",
                            "MarketAbuseAlert",
                            "SinkAudit",
							"MarketCounterparty"
						]
					}
				}
			],
			"must_not": [
				{
					"bool": {
						"should": [
							{
								"wildcard": {
									"hit.sourceKey": "s3://barings-uat.steeleye.co/flows/order-feed-aladdin/*"
								}
							},
							{
								"wildcard": {
									"hit.key": "flows/order-feed-aladdin/*"
								}
							},
							{
								"wildcard": {
									"sourceKey": "s3://barings-uat.steeleye.co/flows/order-feed-aladdin/*"
								}
							},
							{
								"wildcard": {
									"key": "flows/order-feed-aladdin/*"
								}
							},
							{
								"wildcard": {
									"input": "s3://barings-uat.steeleye.co/flows/order-feed-aladdin/*"
								}
							},
							{
								"wildcard": {
									"hit.sourceKey": "s3://barings-uat.steeleye.co/flows/mymarket-aladdin-broker-firm/*"
								}
							},
							{
								"wildcard": {
									"hit.key": "flows/mymarket-aladdin-broker-firm/*"
								}
							},
							{
								"wildcard": {
									"sourceKey": "s3://barings-uat.steeleye.co/flows/mymarket-aladdin-broker-firm/*"
								}
							},
							{
								"wildcard": {
									"key": "flows/mymarket-aladdin-broker-firm/*"
								}
							},
							{
								"wildcard": {
									"input": "s3://barings-uat.steeleye.co/flows/mymarket-aladdin-broker-firm/*"
								}
							}
						],
						"minimum_should_match": 1
					}
				}
			]
		}
	},
	"aggs": {
        "model": {
            "terms": {
                "field": "&model",
                "size": 10000
            }
        }
	}
}

###
# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
    "indices": ".abaci_2_0_0",
    "ignore_unavailable": true,
    "include_global_state": false,
    "metadata": {
        "taken_by": "Luis F",
        "taken_because": "add DAP to barings-uat"
    }
}

###
# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

### policy for barings-uat
POST http://localhost:9200/.abaci_2_0_0/Policy/barings-uat:policy:2024-03-18
Content-Type: application/json

{
	"urn": "barings-uat:policy:2024-03-18",
	"&id": "barings-uat:policy:2024-03-18",
	"evaluations": [ 
		{
			"evaluatees": [ // rule 1: all Korea Users - should only see data for SE Blotter -> aka exclude all data from aladdin
				{
					"attribute": "$subject#COUNTRY_CODE",
					"function": "IN",
					"value": [
						"KR"
					]
				}
			],
			"expression": {
				"bool": {
					"should": [
						{
							"bool": {
								"must_not": {
									"terms": {
										"&model": [
											"OrderState",
											"SinkFileAudit",
											"Order",
											"QuarantinedOrder",
											"SinkRecordAudit",
											"MarketAbuseAlert",
											"SinkAudit",
											"MarketCounterparty"
										]
									}
								}
							}
						},
						{
							"bool": {
								"filter": [
									{
										"terms": {
											"&model": [
												"OrderState",
												"SinkFileAudit",
												"Order",
												"QuarantinedOrder",
												"SinkRecordAudit",
												"MarketAbuseAlert",
												"SinkAudit",
												"MarketCounterparty"
											]
										}
									}
								],
								"must_not": [
									{
										"bool": {
											"should": [
												{
													"wildcard": {
														"hit.sourceKey": "s3://barings-uat.steeleye.co/flows/order-feed-aladdin/*"
													}
												},
												{
													"wildcard": {
														"hit.key": "flows/order-feed-aladdin/*"
													}
												},
												{
													"wildcard": {
														"sourceKey": "s3://barings-uat.steeleye.co/flows/order-feed-aladdin/*"
													}
												},
												{
													"wildcard": {
														"key": "flows/order-feed-aladdin/*"
													}
												},
												{
													"wildcard": {
														"input": "s3://barings-uat.steeleye.co/flows/order-feed-aladdin/*"
													}
												},
												{
													"wildcard": {
														"hit.sourceKey": "s3://barings-uat.steeleye.co/flows/mymarket-aladdin-broker-firm/*"
													}
												},
												{
													"wildcard": {
														"hit.key": "flows/mymarket-aladdin-broker-firm/*"
													}
												},
												{
													"wildcard": {
														"sourceKey": "s3://barings-uat.steeleye.co/flows/mymarket-aladdin-broker-firm/*"
													}
												},
												{
													"wildcard": {
														"key": "flows/mymarket-aladdin-broker-firm/*"
													}
												},
												{
													"wildcard": {
														"input": "s3://barings-uat.steeleye.co/flows/mymarket-aladdin-broker-firm/*"
													}
												}
											],
											"minimum_should_match": 1
										}
									}
								]
							}
						}
					],
					"minimum_should_match": 1
				}
			}
		},
		{
			"evaluatees": [ // rule 2: all non Korea Users - should only see data from aladdin
				{
					"negate": true,
					"attribute": "$subject#COUNTRY_CODE",
					"function": "IN",
					"value": [
						"KR"
					]
				}
			],
			"expression": {
				"bool": {
					"should": [
						{
							"bool": {
								"must_not": {
									"terms": {
										"&model": [
											"OrderState",
											"SinkFileAudit",
											"Order",
											"QuarantinedOrder",
											"SinkRecordAudit",
											"MarketAbuseAlert",
											"SinkAudit",
											"MarketCounterparty"
										]
									}
								}
							}
						},
						{
							"bool": {
								"filter": [
									{
										"terms": {
											"&model": [
												"OrderState",
												"SinkFileAudit",
												"Order",
												"QuarantinedOrder",
												"SinkRecordAudit",
												"MarketAbuseAlert",
												"SinkAudit",
												"MarketCounterparty"
											]
										}
									},
									{
										"bool": {
											"should": [
												{
													"wildcard": {
														"hit.sourceKey": "s3://barings-uat.steeleye.co/flows/order-feed-aladdin/*"
													}
												},
												{
													"wildcard": {
														"hit.key": "flows/order-feed-aladdin/*"
													}
												},
												{
													"wildcard": {
														"sourceKey": "s3://barings-uat.steeleye.co/flows/order-feed-aladdin/*"
													}
												},
												{
													"wildcard": {
														"key": "flows/order-feed-aladdin/*"
													}
												},
												{
													"wildcard": {
														"input": "s3://barings-uat.steeleye.co/flows/order-feed-aladdin/*"
													}
												},
												{
													"wildcard": {
														"hit.sourceKey": "s3://barings-uat.steeleye.co/flows/mymarket-aladdin-broker-firm/*"
													}
												},
												{
													"wildcard": {
														"hit.key": "flows/mymarket-aladdin-broker-firm/*"
													}
												},
												{
													"wildcard": {
														"sourceKey": "s3://barings-uat.steeleye.co/flows/mymarket-aladdin-broker-firm/*"
													}
												},
												{
													"wildcard": {
														"key": "flows/mymarket-aladdin-broker-firm/*"
													}
												},
												{
													"wildcard": {
														"input": "s3://barings-uat.steeleye.co/flows/mymarket-aladdin-broker-firm/*"
													}
												}
											],
											"minimum_should_match": 1
										}
									}
								]
							}
						}
					],
					"minimum_should_match": 1
				}
			}
		}
	],
	"resourceUrns": [
		"barings-uat:*"
	],
	"effect": "DENY",
	"action": "READ",
	"&key": "Policy:barings-uat:policy:2024-03-18:1710773870000",
	"enabled": true,
	"&timestamp": "1710773870000",
	"&model": "Policy",
	"&user": "luis.ferro - ENG-8534"
}
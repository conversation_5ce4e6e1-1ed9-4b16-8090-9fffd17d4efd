###
@tenant=citadel
@ticket=eu-10054
@repo=stack-2024-03

GET http://localhost:9200/{{tenant}}:watch_execution/_search
Content-Type: application/json

{
    "query": {
        "bool": {
            "filter": [
                {
                    "terms": {
                        "detail.watchId": [
                            "b342f45c-2b81-49be-8c29-2e91df9a99de",
                            "0e89ef15-859d-4268-b662-b284080920b3",
                            "fc2ea208-d906-4a53-8f00-2735904c81f7"
                        ]
                    }
                }
                ,
                {
                    "term": {
                        "workflow.status": "RESOLVED_WITH_DISMISSAL"
                    }
                }
            ]
        }
    },
    "aggs": {
        "status": {
            "terms": {
                "field": "workflow.status"
            }
        },
        "&model": {
            "terms": {
                "field": "&model"
            }
        }
    }
}

### citadel_poc_steeleye_co_surveillancewatchexecution


###
# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
    "indices": "citadel_poc_steeleye_co_surveillancewatchexecution",
    "ignore_unavailable": true,
    "include_global_state": false,
    "metadata": {
        "taken_by": "Luis F",
        "taken_because": "cleanup test alerts"
    }
}

###
# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###
# @name process

POST http://localhost:9200/citadel_poc_steeleye_co_surveillancewatchexecution/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "filter": [
                {
                    "terms": {
                        "detail.watchId": [
                            "b342f45c-2b81-49be-8c29-2e91df9a99de",
                            "0e89ef15-859d-4268-b662-b284080920b3",
                            "fc2ea208-d906-4a53-8f00-2735904c81f7"
                        ]
                    }
                }
                ,
                {
                    "term": {
                        "workflow.status": "RESOLVED_WITH_DISMISSAL"
                    }
                }
            ]
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json


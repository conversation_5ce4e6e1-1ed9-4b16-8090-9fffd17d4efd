###
@tenant=cairn
@ticket=eu-9998
@repo=stack-2024-03

###
http://localhost:9200/{{tenant}}:account_person/_search?version=true
Content-Type: application/json

{
    "_source": [
        "structure.firm"
    ],
    "size": 2,
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ],
            "filter": [
                {
                    "terms": {
                        "&id": [
                            "4092d718-64de-489b-a93a-4a4116ece30a",
                            "a6edd8b3-9628-4925-93f0-93ca59ff2b3e"
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "&model": {
            "terms": {
                "field": "&model",
                "size": 100
            }
        },
        "subscribedMarketAbuseReports": {
            "terms": {
                "field": "subscribedMarketAbuseReports",
                "size": 100
            }
        }
    }
}

###
# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
    "indices": "cairn_steeleye_co_accountperson",
    "ignore_unavailable": true,
    "include_global_state": false,
    "metadata": {
        "taken_by": "Luis <PERSON>",
        "taken_because": "change 2 employees"
    }
}

###
# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###
# @name process

POST http://localhost:9200/{{tenant}}:account_person/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "lang": "painless",
        "source": "ctx._source['structure']=params.structure;",
        "params": {
            "structure": {
                "firm": "Polus Capital Management Limited"
            }
        }
    },
     "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                },
                {
                    "exists": {
                        "field": "structure.firm"
                    }
                }
            ],
            "filter": [
                {
                    "terms": {
                        "&id": [
                            # "4092d718-64de-489b-a93a-4a4116ece30a"
                            # ,
                            "a6edd8b3-9628-4925-93f0-93ca59ff2b3e"
                        ]
                    }
                }
            ]
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

###
POST http://localhost:9200/{{tenant}}:account_person/_flush
@tenant=apollo
@ticket=eu-10845
@repo=stack-2024-06

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
    "indices": "{{tenant}}:rts22_transaction,{{tenant}}:rts22_transaction_report",
    "ignore_unavailable": true,
    "include_global_state": false,
    "metadata": {
        "taken_by": "Luis F",
        "taken_because": "cleanup bad reports"
    }
}

###
# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

### check transactions for the offending reports, should be 0 documents
http://localhost:9200/{{tenant}}:rts22_transaction/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ],
            "filter": [
                {
                    "bool": {
                        "filter": [
                            {
                                "term": {
                                    "&model": "RTS22Transaction"
                                }
                            },
                            {
                                "terms": {
                                    "workflow.reportId": [
                                        "SteelEye_TRMiFIR_2138006DHU9CTXV86Z88.20240606150632",
                                        "SteelEye_TRMiFIR_2138006DHU9CTXV86Z88.20240606150405"
                                    ]
                                }
                            }
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "reports": {
            "terms": {
                "field": "workflow.reportId"
            }
        }
    }
}

### delete reports
# @name process
POST http://localhost:9200/{{tenant}}:rts22_transaction_report/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "filter": [
                {
                    "bool": {
                        "filter": [
                            {
                                "term": {
                                    "&model": "RTS22TransactionReport"
                                }
                            },
                            {
                                "terms": {
                                    "reportId": [
                                        "SteelEye_TRMiFIR_2138006DHU9CTXV86Z88.20240606150632",
                                        "SteelEye_TRMiFIR_2138006DHU9CTXV86Z88.20240606150405"
                                    ]
                                }
                            }
                        ]
                    }
                }
            ]
        }
    }
}

###
@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

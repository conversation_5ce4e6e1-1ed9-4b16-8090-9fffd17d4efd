@tenant=freetrade
@ticket=eu-10781
@repo=stack-2024-06
@watch=86492dba-ccdd-495d-953c-832c7b47d33d
###
# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
    "indices": "{{tenant}}:surveillance_watch",
    "ignore_unavailable": true,
    "include_global_state": false,
    "metadata": {
        "taken_by": "Luis F",
        "taken_because": "fix bad watch"
    }
}

###
# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

### check transactions for the offending reports, should be 0 documents
http://localhost:9200/{{tenant}}:surveillance_watch/_search
Content-Type: application/json

{
    "_source": [
        "query.refineOptions",
        "query.filters",
        "query.name"
    ],
    "size": 1,
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ],
            "filter": [
                {
                    "term": {
                        "&id": "{{watch}}"
                    }
                }
            ]
        }
    },
    "aggs": {
        "reports": {
            "terms": {
                "field": "workflow.reportId"
            }
        }
    }
}

### delete reports
# @name process
POST http://localhost:9200/{{tenant}}:surveillance_watch/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "lang": "painless",
        "source": "ctx._source['query']['refineOptions']=params.refineOptions;ctx._source['query']['filters']=params.filters;",
        "params": {
            "refineOptions": "[{\"id\":\"timestamps.orderSubmitted.hour\",\"key\":\"refineMapper_95\",\"title\":\"\",\"property\":{\"id\":\"timestamps.orderSubmitted.hour\",\"nestedQueryPath\":null,\"analyzer\":null,\"type\":\"time\",\"label\":\"\",\"defaultCondition\":\"between\"},\"condition\":\"between\",\"value\":{\"min\":0,\"max\":0},\"type\":\"time\",\"enumType\":null,\"defaultOrMoreFilter\":true,\"listItems\":null},{\"id\":\"timestamps.orderSubmitted.hour\",\"key\":\"refineMapper_96\",\"title\":\"\",\"property\":{\"id\":\"timestamps.orderSubmitted.hour\",\"nestedQueryPath\":null,\"analyzer\":null,\"type\":\"time\",\"label\":\"\",\"defaultCondition\":\"between\"},\"condition\":\"between\",\"value\":{\"min\":10,\"max\":12},\"type\":\"time\",\"enumType\":null,\"defaultOrMoreFilter\":true,\"listItems\":null}]",
            "filters": "{\"bool\":{\"must\":[{\"script\":{\"script\":{\"inline\":\"0 <= LocalDateTime.ofInstant(Instant.ofEpochMilli(doc['timestamps.orderSubmitted'].value),ZoneId.of('Z')).getHour() && LocalDateTime.ofInstant(Instant.ofEpochMilli(doc['timestamps.orderSubmitted'].value),ZoneId.of('Z')).getHour() < 0\"}}},{\"script\":{\"script\":{\"inline\":\"10 <= LocalDateTime.ofInstant(Instant.ofEpochMilli(doc['timestamps.orderSubmitted'].value),ZoneId.of('Z')).getHour() && LocalDateTime.ofInstant(Instant.ofEpochMilli(doc['timestamps.orderSubmitted'].value),ZoneId.of('Z')).getHour() < 12\"}}}]}}"
        }
    },
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ],
            "filter": [
                {
                    "term": {
                        "&id": "{{watch}}"
                    }
                }
            ]
        }
    }
}

###
@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

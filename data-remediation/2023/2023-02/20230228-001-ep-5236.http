@tenant=mar
@ticket=ep-5236
@repo=stack-2023-02

GET http://localhost:9200/iris:order/_search
Content-Type: application/json

{
    "size": 1,
    "query": {
      "bool": {
        "must_not": {
          "exists": {
            "field": "&expiry"
          }
        },
        "filter": {
          "range": {
            "date": {
              "gte": "2023-01-01"
            }
          }
        }
      }
    },
    "aggs": {
      "max": {
          "max": { "field": "date" }
      },
      "min": {
          "min":  { "field": "date" }
      },
      "indices": {
        "terms": {
          "field": "_index",
          "size": 25
        }
      }
    }
}

###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  "indices": "mar_order_20230223",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "<PERSON>",
    "taken_because": "Backup before copy"
  }
}

###

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json


###
# @name process

POST http://localhost:9200/_reindex?wait_for_completion=false&refresh=true
Content-Type: application/json

{
  "source": {
    "query": {
      "bool": {
        "must_not": {
          "exists": {
            "field": "&expiry"
          }
        },
        "filter": {
          "range": {
            "date": {
              "gte": "2023-01-01"
            }
          }
        }
      }
    },
    "index": "iris_order_20230221"
  },
  "dest": {
    "index": "mar_order_20230223"
  }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json
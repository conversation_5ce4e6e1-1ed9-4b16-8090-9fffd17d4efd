@tenant=fil-uat
@ticket=rm-2304
@repo=stack-2023-02

###
GET http://localhost:9200/{{tenant}}:account_person/_search
Content-Type: application/json

{
    "size": 0,
    "from": 0,
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            }
        }
    },
    "aggs": {
        "Indices": {
            "terms": {
                "field": "_index",
                "size": 10000
            }
        }
    }
}

###
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  "indices": "axb8zhmurdit_gdzzf7o",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Priyam",
    "taken_because": "delete all account person records"
  }
}

###
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###
POST http://localhost:9200/axb8zhmurdit_gdzzf7o/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            }
        }
    }
}

###
GET http://localhost:9200/_tasks/PbP4eO8GTY-u0mkZbXyQRw:*********
Content-Type: application/json

@tenant=fil
@ticket=eu-7306
@repo=stack-2023-06

### List all indices on the stack

http://localhost:9200/_cat/indices/?v
Content-Type: application/json


### Snapshot of indices marked for delete
# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  "indices": "eu-6660_fil_email_v1", 
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "snapshot before cleaning up"
  }
}

###

GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

### cleanup

DELETE http://localhost:9200/eu-6660_fil_email_v1
Content-Type: application/json

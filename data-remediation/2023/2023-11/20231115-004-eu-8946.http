@tenant=cpi
@ticket=eu-8946
@repo=stack-2023-11
# 3211

http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ],
            "filter": [
                {
                    "terms": {
                        "reportDetails.transactionRefNo": [
                            "20231110246736986202311102",246736986 ->
                            "20230922240801738202309222",240801738
                            "20230915240580961202309152",240580961
                        ]
                    }
                },
                {
                    "terms": {
                        "sourceKey": [
                            "s3://cpi.steeleye.co/feeds/trade/capricorn-trade-blotter/202309/CPI Transaction Report 2023-09-16.xlsx",
                            "s3://cpi.steeleye.co/feeds/trade/capricorn-trade-blotter/202309/CPI+Transaction+Report+2023-09-22.xlsx",
                            "s3://cpi.steeleye.co/feeds/trade/capricorn-trade-blotter/202311/CPI+Transaction+Report+2023-11-10.xlsx"
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "index": {
            "terms": {
                "field": "_index",
                "size": 10
            }
        },
        "&model": {
            "terms": {
                "field": "&model",
                "size": 10
            }
        },
        "sourceKey": {
            "terms": {
                "field": "sourceKey",
                "size": 100
            }
        }

    }
}


###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  "indices": "awv_9c2rf0unlugszwou",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "delete 3 transaction for reload"
  }
}

### 

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###
# @name process

POST http://localhost:9200/awv_9c2rf0unlugszwou/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ],
            "filter": [
                {
                    "terms": {
                        "reportDetails.transactionRefNo": [
                            "20231110246736986202311102",
                            "20230922240801738202309222",
                            "20230915240580961202309152"
                        ]
                    }
                },
                {
                    "terms": {
                        "sourceKey": [
                            "s3://cpi.steeleye.co/feeds/trade/capricorn-trade-blotter/202309/CPI Transaction Report 2023-09-16.xlsx",
                            "s3://cpi.steeleye.co/feeds/trade/capricorn-trade-blotter/202309/CPI+Transaction+Report+2023-09-22.xlsx",
                            "s3://cpi.steeleye.co/feeds/trade/capricorn-trade-blotter/202311/CPI+Transaction+Report+2023-11-10.xlsx"
                        ]
                    }
                }
            ]
        }
    }
}

###
@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

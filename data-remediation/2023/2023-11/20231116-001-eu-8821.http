@tenant=insight
@ticket=eu-8821
@repo=stack-2023-11

GET http://localhost:9200/{{tenant}}:watch_execution/_search?version=true
Content-Type: application/json

{
    "_source": [
        "&id",
        "scenarioId",
        "slug",
        "detected",
        "detail.watchId",
        "detail.watchName",
        "workflow.status",
        "hit.tradersAlgosWaiversIndicators.investmentDecisionMakerWithinFirm",
        "hit.tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.structure.desks.name",
        "workflow.resolutionCategory",
        "workflow.caseSlug"
    ],
    "size": 1,
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ],
            "filter": [
                {
                    "terms": {
                        "&model": [
                            "MarketAbuseAlert"
                            # ,
                # "MarketAbuseScenarioTag"
                        ]
                    }
                },
                {
                    "range": {
                        "detected": {
                            "gte": "2023-07-01",
                            "lt": "2023-10-01"
                        }
                    }
                }
                # ,
                # {
                #     "exists": {
                #         "field": "hit.tradersAlgosWaiversIndicators.investmentDecisionMakerWithinFirm.name"
                #     }
                # }
            ]
        }
    },
    "aggs": {
        "index": {
            "terms": {
                "field": "_index",
                "size": 1000
            }
        },
        "model": {
            "terms": {
                "field": "&model",
                "size": 1000
            }
        }
    }
}

###

GET http://localhost:9200/{{tenant}}:surveillance_watch/_search?version=true
Content-Type: application/json

{
}
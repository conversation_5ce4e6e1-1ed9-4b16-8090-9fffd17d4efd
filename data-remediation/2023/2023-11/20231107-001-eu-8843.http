@tenant=*
@ticket=eu-8843

http://localhost:9200/_snapshot

###
PUT http://localhost:9200/_snapshot/test-2023-11
Content-Type: application/json

{
    "type": "s3",
    "settings": {
        "bucket": "dev-blue.elastic.eu-west-1.steeleye.co",
        "base_path": "dev-blue/es-backups/test-2023-11/",
        "server_side_encryption": "true",
        "compress": "true",
        "region": "eu-west-1",
        "max_snapshot_bytes_per_sec": "10gb"
    }
}

###
DELETE http://localhost:9200/_snapshot/test-2023-11
Content-Type: application/json

### check repo

http://localhost:9200/_cat/snapshots/stack-2023-11?v&s=start_epoch

### 
curator-20231107131207    SUCCESS 1699362731  13:12:11   1699363619 13:26:59    14.7m    1198              1279             0         1279



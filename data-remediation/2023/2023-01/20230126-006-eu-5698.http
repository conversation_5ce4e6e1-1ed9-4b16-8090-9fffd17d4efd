
@tenant=globalshares
@ticket=eu-5698
@repo=stack-2023-01

@filename=GShares.SteelEyeGS.02.01to.06.01.2023.csv

### find all data from file
http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            "should": [
                {
                    "wildcard": {
                        "sourceKey": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "dataSource.sourceKey": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "voiceFile.fileInfo.location.key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "metadata.source.fileInfo.location.key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "fileInfo.location.key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "voiceTranscript.fileInfo.location.key": "*/{{filename}}"
                    }
                }
            ],
            "minimum_should_match": "1"

        }
    },
    "aggs": {
        "sourceKey": {
            "terms": {
                "field": "sourceKey",
                "size": 10000
            }
        },
        "key": {
            "terms": {
                "field": "key",
                "size": 10000
            }
        },
        "dataSourceSourceKey": {
            "terms": {
                "field": "dataSource.sourceKey",
                "size": 10000
            }
        },
        "Indeces": {
            "terms": {
                "field": "_index",
                "size": 10000
            }
        },
        "Models": {
            "terms": {
                "field": "&model",
                "size": 10000
            }
        }
    }
}

###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "axx-wr2fyesxvxsoyvjo,axx-wwvjyesxvxsoyvjx",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "cleanup to allow clean reload"
  }
}

###

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###


# @name process

POST http://localhost:9200/axx-wr2fyesxvxsoyvjo,axx-wwvjyesxvxsoyvjx/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "should": [
                {
                    "wildcard": {
                        "sourceKey": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "dataSource.sourceKey": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "voiceFile.fileInfo.location.key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "metadata.source.fileInfo.location.key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "fileInfo.location.key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "voiceTranscript.fileInfo.location.key": "*/{{filename}}"
                    }
                }
            ],
            "minimum_should_match": "1"

        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

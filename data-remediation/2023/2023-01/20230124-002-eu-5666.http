
@tenant=shell-uat
@ticket=eu-5666
@repo=stack-2023-01

### find all data from file
http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            "should": [
                {
                    "wildcard": {
                        "sourceKey": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "dataSource.sourceKey": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "voiceFile.fileInfo.location.key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "metadata.source.fileInfo.location.key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "fileInfo.location.key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "voiceTranscript.fileInfo.location.key": "*/{{filename}}"
                    }
                }
            ],
            "minimum_should_match": "1"

        }
    },
    "aggs": {
        "Indeces": {
            "terms": {
                "field": "_index",
                "size": 10000
            }
        },
        "Models": {
            "terms": {
                "field": "&model",
                "size": 10000
            }
        }
    }
}



###
# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "shell-uat_steeleye_co_order,shell-uat_steeleye_co_sinkfileaudit,shell-uat_steeleye_co_quarantinedorder,shell-uat_steeleye_co_sinkrecordaudit",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "remove traces of files"
  }
}

###

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

####
# @name process
@filename=SteelEyeDemoEQFIFXFU+%281%29.csv

POST http://localhost:9200/shell-uat_steeleye_co_order,shell-uat_steeleye_co_sinkfileaudit,shell-uat_steeleye_co_quarantinedorder,shell-uat_steeleye_co_sinkrecordaudit/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "should": [
                {
                    "wildcard": {
                        "sourceKey": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "dataSource.sourceKey": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "voiceFile.fileInfo.location.key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "metadata.source.fileInfo.location.key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "fileInfo.location.key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "voiceTranscript.fileInfo.location.key": "*/{{filename}}"
                    }
                }
            ],
            "minimum_should_match": "1"

        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

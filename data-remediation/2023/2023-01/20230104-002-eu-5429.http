
@tenant=insight
@ticket=eu-5430
@repo=stack-2022-12

### find all data from file
http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "_source": [
        "&key",
        "&id",
        "&timestamp"
    ],
    "query": {
        "bool": {
            "filter": [
                {
                    "term": {
                        "&id": "HFX1566467:2:HFX1566467HFX156646720220329SELL:FILL:2022-03-29T15:37:06Z"
                    }
                },
                {
                    "terms": {
                        "&model": [
                            "QuarantinedOrder",
                            "OrderState"
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "models": {
            "terms": {
                "field": "&model"
            }
        },
        "index": {
            "terms": {
                "field": "_index"
            }
        }
    }
}

###

SinkRecordAudit:insight.steeleye.co:flows/order-universal-steeleye-trade-blotter/cts/mast/steeleyeblotter.insight.cts.20220330092130.requeue.csv:1671878446000:47:quarantinedorder:hfx1566467:2:hfx1566467hfx156646720220329sell:fill:2022-03-29t15:37:06z:1671878452695:1671878458505

###
# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}a
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "axzwswnovs6rc-gflgyr",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "check fix"
  }
}

###

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}a
Content-Type: application/json

####
# @name process

POST http://localhost:9200/axzwswnovs6rc-gflgyr/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "lang": "painless",
        "source": "ctx._source['&key']='QuarantinedOrder:HFX1566467:2:HFX1566467HFX156646720220329SELL:FILL:2022-03-29T15:37:06Z:1662826697646';"
    },
    "query": {
        "bool": {
            "filter": [
                {
                    "term": {
                        "&id": "HFX1566467:2:HFX1566467HFX156646720220329SELL:FILL:2022-03-29T15:37:06Z"
                    }
                },
                {
                    "terms": {
                        "&model": [
                            "QuarantinedOrder"
                        ]
                    }
                }
            ]
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

###

http://localhost:9200/{{tenant}}:sink_record_audit/_search
Content-Type: application/json

{
    "size": 1,
    "query": {
        "bool": {
            "filter": [
                {
                    "term": {
                        "status": "QUARANTINED"
                    }
                },
                {
                    "terms": {
                       "matchedKey": [
                            "OrderState:HFX1566467:2:HFX1566467HFX156646720220329SELL:FILL:2022-03-29T15:37:06Z:1662826697646"
                       ]
                    }
                }
            ]
        }
    }
}

###


recordKey

"recordKey": "QuarantinedOrder:HFX1566467:2:HFX1566467HFX156646720220329SELL:FILL:2022-03-29T15:37:06Z:1671878452695"




####
# @name process

POST http://localhost:9200/axzwsyqvvs6rc-gflgya/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "lang": "painless",
        "source": "ctx._source['recordKey']='QuarantinedOrder:HFX1566467:2:HFX1566467HFX156646720220329SELL:FILL:2022-03-29T15:37:06Z:1662826697646';"
    },
    "query": {
        "bool": {
            "filter": [
                {
                    "term": {
                        "&id": "insight.steeleye.co:flows/order-universal-steeleye-trade-blotter/cts/mast/steeleyeblotter.insight.cts.20220330092130.requeue.csv:1671878446000:47:quarantinedorder:hfx1566467:2:hfx1566467hfx156646720220329sell:fill:2022-03-29t15:37:06z:1671878452695"
                    }
                },
                {
                    "terms": {
                        "&model": [
                            "SinkRecordAudit"
                           
                        ]
                    }
                }
            ]
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

###

http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "_source": [
        "&key",
        "&id",
        "&timestamp"
    ],
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            },
            "filter": [
                {
                    "bool": {
                        "should": [
                            {
                                "term": {
                                    "&id": "500419EZ2Y5ZHXG2P9:2:500419CS0JC2S20200922SELL:FILL:2020-09-22T11:58:02.000000Z:0.0"
                                }
                            },
                            {
                                "term": {
                                    "matchedKey": "OrderState:500419EZ2Y5ZHXG2P9:2:500419CS0JC2S20200922SELL:FILL:2020-09-22T11:58:02.000000Z:0.0:1614962115055"
                                }
                            },
                            {
                                "term": {
                                    "&key": "OrderState:500419EZ2Y5ZHXG2P9:2:500419CS0JC2S20200922SELL:FILL:2020-09-22T11:58:02.000000Z:0.0:1614962115055"
                                }
                            }
                        ],
                        "minimum_should_match": "1"
                    }
                },
                {
                    "terms": {
                        "&model": [
                            "QuarantinedOrder"
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "models": {
            "terms": {
                "field": "&model"
            }
        },
        "index": {
            "terms": {
                "field": "_index"
            }
        }
    }
}

###


http://localhost:9200/{{tenant}}:quarantined_order/_search
Content-Type: application/json

{
    # "_source": [
    #     "&key"
    # ],
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            },
            "filter": [
                {
                    "wildcard": {
                        "&key": "QuarantinedOrderState*"
                    }
                }
            ]
        }
    },
    "aggs": {
        "models": {
            "terms": {
                "field": "&model"
            }
        },
        "index": {
            "terms": {
                "field": "_index"
            }
        }
    }
}

###

http://localhost:9200/{{tenant}}:quarantined_order/_search
Content-Type: application/json

{
    "size": 0,
    "aggs": {
        "indices": {
            "terms": {
                "field": "_index"
            }
        }
    }
}

### Check bulk

http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "_source": [
        "&key",
        "&id"
    ],
    "query": {
        "bool": {
            "filter": [
                {
                    "term": {
                        "&id": "3644162:1:36441624742040807423644162F1221565620220922BUYI:PARF:2022-09-22T11:53:06.590000Z"
                    }
                }
            ]
        }
    }
}

###


GET http://localhost:9200/axzwsyqvvs6rc-gflgya/_search
Content-Type: application/json

{
    "query": {
        "bool": {
            "filter": [
                {
                    "term": {
                        "&id": "insight.steeleye.co:flows/order-universal-steeleye-trade-blotter/thinkfolio/steeleyeblotter.insight.thinkfolio.cancels.20220916084131.csv:1671829367000:58:quarantinedorder:3635562:1:36355623635562secfn2order20220915buyi:came:2022-09-13t08:46:16z:1671829426813"
                    }
                },
                {
                    "terms": {
                        "&model": [
                            "SinkRecordAudit"
                        ]
                    }
                }
            ]
        }
    }
}
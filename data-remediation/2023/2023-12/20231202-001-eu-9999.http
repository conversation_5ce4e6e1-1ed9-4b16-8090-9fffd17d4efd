@tenant=osmia
@ticket=eu-9087
@repo=stack-2023-11

http://localhost:9211/*:tenant_configuration/_search
Content-Type: application/json

{
    "_source": [
        "voice.transcriptionProvider",
        "subscribedMarketAbuseReports",
        "subscribedModules",
        "featureFlags"
    ],
    "size": 1,
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            }
        }
    },
    "aggs": {
        "tenantId": {
            "terms": {
                "field": "tenantId",
                "size": 10000
            }
        }
    }
}

### check for empty index
http://localhost:9200/_cat/indices/empty_index*?v&h=index&format=json
Content-Type: application/json

### tenant data per index (number of docs)

http://localhost:9211/{{tenant}}:*/_search
Content-Type: application/json

{
    "size": 0,
    "aggs": {
        "_index": {
            "terms": {
                "field": "_index",
                "size": 1000
            }
        }
    }
}

###

http://localhost:9211/_cat/indices/{{tenant}}:*?v&h=index,pri,rep,docs.count

###
http://localhost:9211/_cat/aliases/{{tenant}}:*?v&h=index,alias

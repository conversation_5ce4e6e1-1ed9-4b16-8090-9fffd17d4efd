# Check the watches
@tenant=iris
@ticket=eu-6147
@repo=stack-2023-03
@lastexecution1=1676404800000

http://localhost:9200/{{tenant}}:surveillance_watch/_search
Content-Type: application/json

{
    "size": 5,
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            },
            "filter": [
                {
                    "term": {
                        "&model": "SurveillanceWatch"
                    }
                },
                {
                    "terms": {
                        "&id": [
                            "f7dbcf34-c0a6-4905-a428-d91347792cf3"
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "indices": {
            "terms": {
                "field": "_index",
                "size": 10000
            }
        },
        "models": {
            "terms": {
                "field": "&model",
                "size": 10000
            }
        }
    }
}

###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "{{tenant}}:surveillance_watch",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "Reset some watches executions"
  }
}

###

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json


### change watches1

# @name process

POST http://localhost:9200/{{tenant}}:surveillance_watch/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "lang": "painless",
        "source": "ctx._source['executionDetails']=params.executionDetails;ctx._source['status']='ACTIVE';",
        "params": {
            "executionDetails": {
                "lastExecution": "{{lastexecution1}}"
            }
        }
    },
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            },
            "filter": [
                {
                    "term": {
                        "&model": "SurveillanceWatch"
                    }
                },
                {
                    "terms": {
                        "&id": [
                            "f7dbcf34-c0a6-4905-a428-d91347792cf3"
                        ]
                    }
                }
            ]
        }
    }
}


###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

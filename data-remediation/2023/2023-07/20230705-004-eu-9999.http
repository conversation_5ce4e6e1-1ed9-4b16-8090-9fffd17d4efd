@tenant=pinafore
@ticket=eu-9999
@repo=stack-2023-07

### Get repo list
http://localhost:9200/_snapshot

### Get repo settings
http://localhost:9200/_snapshot/stack-2023-07
Content-Type: application/json

### Get snapshots
http://localhost:9200/_cat/snapshots/stack-2023-07?v&s=start_epoch

### get snapshot info
http://localhost:9200/_snapshot/stack-2023-07/auto-20230705-200700
Content-Type: application/json

### get snapshot extended status
http://localhost:9200/_snapshot/stack-2023-07/auto-20230705-200700/_status?pretty
Content-Type: application/json

###
http://localhost:9200/_snapshot/stack-2023-07/purge-saran-email-20230701062405/_status?pretty
Content-Type: application/json

###
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  "indices": "{{tenant}}:rts22_transaction",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "<PERSON>",
    "taken_because": "before cancellations"
  }
}

###
# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}/_status
Content-Type: application/json

###

http://localhost:9200/{{tenant}}:rts22_transaction/_search
Content-Type: application/json

{
    # "_source": [
    #     "reportDetails.transactionRefNo",
    #     "&id"
    # ],
    "size": 100,
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            }
            ,
            "filter": [
                {
                    "term": {
                        "reportDetails.transactionRefNo": "O1183O1183T1321INT392120220311BUYI"
                    }
                }
            ]
        }
    }
}

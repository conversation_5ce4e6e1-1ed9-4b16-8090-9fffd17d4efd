@tenant=lighthouse
@ticket=eu-7523
@repo=stack-2023-07

http://localhost:9200/{{tenant}}:rts22_transaction/_search
Content-Type: application/json

{
    "_source": [
        "&id",
        "sourceKey",
        "parties",
        "marketIdentifiers",
        "workflow",
        "workflow"
    ],
    "size": 0,
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            },
            "filter": [
                {
                    "term": {
                        "workflow.arm.status": "PENDING"
                    }
                },
                {
                    "terms": {
                        "&model": [
                            "RTS22Transaction"
                        ]
                    }
                },
                {
                    "bool": {
                        "should": [
                            {
                                "bool": {
                                    "filter": [
                                        {
                                            "nested": {
                                                "path": "marketIdentifiers",
                                                "query": {
                                                    "bool": {
                                                        "filter": [
                                                            {
                                                                "terms": {
                                                                    "marketIdentifiers.labelId": [
                                                                        "id:cervesir",
                                                                        "id:rcervesi"
                                                                    ]
                                                                }
                                                            }
                                                        ]
                                                    }
                                                }
                                            }
                                        },
                                        {
                                            "range": {
                                                "date": {
                                                    "gte": "2022-12-09",
                                                    "lt": "2023-04-29"
                                                }
                                            }
                                        }
                                    ]
                                }
                            },
                            {
                                "bool": {
                                    "filter": [
                                        {
                                            "nested": {
                                                "path": "marketIdentifiers",
                                                "query": {
                                                    "bool": {
                                                        "filter": [
                                                            {
                                                                "terms": {
                                                                    "marketIdentifiers.labelId": [
                                                                        "id:flopes",
                                                                        "id:lopesf"
                                                                    ]
                                                                }
                                                            }
                                                        ]
                                                    }
                                                }
                                            }
                                        },
                                        {
                                            "range": {
                                                "date": {
                                                    "gte": "2023-04-17",
                                                    "lt": "2023-04-29"
                                                }
                                            }
                                        }
                                    ]
                                }
                            },
                            {
                                "bool": {
                                    "filter": [
                                        {
                                            "nested": {
                                                "path": "marketIdentifiers",
                                                "query": {
                                                    "bool": {
                                                        "filter": [
                                                            {
                                                                "terms": {
                                                                    "marketIdentifiers.labelId": [
                                                                        "id:aeccles",
                                                                        "id:antony.eccles",
                                                                        "id:ecclesa"
                                                                    ]
                                                                }
                                                            }
                                                        ]
                                                    }
                                                }
                                            }
                                        },
                                        {
                                            "range": {
                                                "date": {
                                                    "gte": "2021-12-16",
                                                    "lt": "2023-05-16"
                                                }
                                            }
                                        }
                                    ]
                                }
                            },
                            {
                                "bool": {
                                    "filter": [
                                        {
                                            "nested": {
                                                "path": "marketIdentifiers",
                                                "query": {
                                                    "bool": {
                                                        "filter": [
                                                            {
                                                                "terms": {
                                                                    "marketIdentifiers.labelId": [
                                                                        "id:aeccles",
                                                                        "id:antony.eccles",
                                                                        "id:ecclesa"
                                                                    ]
                                                                }
                                                            }
                                                        ]
                                                    }
                                                }
                                            }
                                        },
                                        {
                                            "range": {
                                                "date": {
                                                    "gte": "2021-12-16",
                                                    "lt": "2023-05-16"
                                                }
                                            }
                                        }
                                    ]
                                }
                            },
                            {
                                "bool": {
                                    "filter": [
                                        {
                                            "nested": {
                                                "path": "marketIdentifiers",
                                                "query": {
                                                    "bool": {
                                                        "filter": [
                                                            {
                                                                "terms": {
                                                                    "marketIdentifiers.labelId": [
                                                                        "account:roryhill",
                                                                        "id:hillr",
                                                                        "id:rhill",
                                                                        "id:roryhill"
                                                                    ]
                                                                }
                                                            }
                                                        ]
                                                    }
                                                }
                                            }
                                        },
                                        {
                                            "range": {
                                                "date": {
                                                    "gte": "2021-11-17",
                                                    "lt": "2023-05-16"
                                                }
                                            }
                                        }
                                    ]
                                }
                            },
                            {
                                "bool": {
                                    "filter": [
                                        {
                                            "nested": {
                                                "path": "marketIdentifiers",
                                                "query": {
                                                    "bool": {
                                                        "filter": [
                                                            {
                                                                "terms": {
                                                                    "marketIdentifiers.labelId": [
                                                                        "account:roryhill",
                                                                        "id:hillr",
                                                                        "id:rhill",
                                                                        "id:roryhill"
                                                                    ]
                                                                }
                                                            }
                                                        ]
                                                    }
                                                }
                                            }
                                        },
                                        {
                                            "range": {
                                                "date": {
                                                    "gte": "2021-11-17",
                                                    "lt": "2023-05-16"
                                                }
                                            }
                                        }
                                    ]
                                }
                            }
                        ],
                        "minimum_should_match": "1"
                    }
                }
            ]
        }
    },
    "aggs": {
        "reports": {
            "terms": {
                "field": "workflow.reportId",
                "size": 10000
            }
        },
        "marketIdentifiers": {
            "nested": {
                "path": "marketIdentifiers"
            },
            "aggs": {
                "inner": {
                    "filter": {
                        "terms": {
                            "marketIdentifiers.labelId": [
                                "account:roryhill",
                                "id:aeccles",
                                "id:antony.eccles",
                                "id:cervesir",
                                "id:ecclesa",
                                "id:flopes",
                                "id:hillr",
                                "id:lopesf",
                                "id:rcervesi",
                                "id:rhill",
                                "id:roryhill"
                            ]
                        }
                    },
                    "aggs": {
                        "labelId": {
                            "terms": {
                                "field": "marketIdentifiers.path"
                            }
                        }
                    }
                }
            }
        },
        "nca": {
            "terms": {
                "field": "workflow.nca.status"
            }
        },
        "arm": {
            "terms": {
                "field": "workflow.arm.status"
            }
        },


        # "reportDispositions": {
        #     "nested": {
        #         "path": "marketIdentifiers"
        #     },
        #     "aggs": {
        #         "status": {
        #             "terms": {
        #                 "field": "marketIdentifiers.path",
        #                 "size": 1000
        #             }
        #         }
        #     }
        # },
        "models": {
            "terms": {
                "field": "&model"
            }
        },
        "indices": {
            "terms": {
                "field": "_index"
            }
        }
        # ,
        # "sourceKey": {
        #     "terms": {
        #         "field": "sourceKey"
        #     }
        # }
        # ,
        # "trnos": {
        #     "terms": {
        #         "field": "reportDetails.transactionRefNo",
        #         "size": 10000
        #     }
        # }
    }
}

###

http://localhost:9200/{{tenant}}:account_person/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ],
            "should": [
                {
                    "terms": {
                        "name": [
                            "Rory Hill",
                            "Antony Eccles",
                            "Fabio Lopes",
                            "Roberto Cervesi"
                        ]
                    }
                },
                {
                    "terms": {
                        "&uniqueProps": [
                            "id:flopes",
                            "account:roryhill",
                            "id:aeccles",
                            "id:antony.eccles",
                            "id:cervesir",
                            "id:ecclesa",
                            "id:flopes",
                            "id:hillr",
                            "id:lopesf",
                            "id:rcervesi",
                            "id:rhill",
                            "id:roryhill"
                        ]
                    }
                }
            ],
            "minimum_should_match": "1"
        }
    },
    "aggs": {
        "&uniqueProps": {
            "terms": {
                "field": "&uniqueProps",
                "size": 1000
            }
        }
    }
}


@tenant=captain
@ticket=eu-7701
@repo=stack-2023-07

# add feature flags
# > watch-builder
# > lexica-management
# and mar
# > INSIDER_TRADING_V3

###
http://localhost:9200/{{tenant}}:tenant_configuration/_search
Content-Type: application/json

{
    "_source": [
        "featureFlags",
        "subscribedModules",
        "subscribedMarketAbuseReports"
    ],
    "size": 1,
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            }
        }
    }
}

###
# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "{{tenant}}:tenant_configuration",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "add feature flags"
  }
}

###
# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###
# @name process

POST http://localhost:9200/{{tenant}}:tenant_configuration/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "lang": "painless",
        "source": "ctx._source['featureFlags']=params.featureFlags;ctx._source['subscribedMarketAbuseReports']=params.subscribedMarketAbuseReports;",
        "params": {
          "subscribedModules": [
            "Market",
            "Communications",
            "Case Manager",
            "Comms Surveillance"
          ],
          "featureFlags": [
            "ssoEnabled",
            "ai-poc",
            "watch-builder",
            "lexica-management"
          ],
          "subscribedMarketAbuseReports": [
            "INSIDER_TRADING_V3"
          ]
        }
    },
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            }
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

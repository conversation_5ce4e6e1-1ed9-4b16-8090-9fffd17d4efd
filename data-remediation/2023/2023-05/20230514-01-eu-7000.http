@tenant=iguana
@ticket=eu-7000
@repo=stack-2023-05

GET http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            "filter": [
                {
                    "term": {
                        "workflow.isReported": false
                    }
                },
                {
                    "term": {
                        "workflow.status": "REPORTABLE"
                    }
                },
                {
                    "bool": {
                        "must_not": [
                            {
                                "exists": {
                                    "field": "&expiry"
                                }
                            },
                            {
                                "match": {
                                    "transactionDetails.priceCurrency": "GBP"
                                }
                            }
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "model": {
            "terms": {
                "field": "&model"
            }
        },
        "indices": {
            "terms": {
                "field": "_index"
            }
        }
    }
}



###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "iguana_steeleye_co_rts22transaction",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "<PERSON>riya<PERSON>",
    "taken_because": "Mark as non-reportable"
  }
}

###

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json


####
# @name process

POST http://localhost:9200/iguana_steeleye_co_rts22transaction/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "lang": "painless",
        "source": "ctx._source.workflow['status']='NON_REPORTABLE';"
    },
    "query": {
        "bool": {
            "filter": [
                {
                    "term": {
                        "workflow.isReported": false
                    }
                },
                {
                    "term": {
                        "workflow.status": "REPORTABLE"
                    }
                },
                {
                    "bool": {
                        "must_not": [
                            {
                                "exists": {
                                    "field": "&expiry"
                                }
                            },
                            {
                                "match": {
                                    "transactionDetails.priceCurrency": "GBP"
                                }
                            }
                        ]
                    }
                }
            ]
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json


###

GET http://localhost:9200/_tasks/jV4lfKuRQtiDT50qECf89w:271703130
Content-Type: application/json

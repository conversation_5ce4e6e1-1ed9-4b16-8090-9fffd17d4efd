### List tenants as per schema publisher
http://localhost:9200/_cat/aliases/*:account_firm?v&h=alias

### Abaci
# @name abaci
http://localhost:9200/.abaci/_search
Content-Type: application/json

{
    "size": 10,
    "_source": [
        "&id", "version"
    ],
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            },
            "filter": [
                {
                    "term": {
                        "&model": "Schema"
                    }
                }
                # ,
                # {
                #     "term": {
                #         "version": "2019.7.2"
                #     }
                # }
            ]
        }
    },
    "aggs": {
        "versions": {
            "terms": {
                "field": "version",
                "size": 10000
            }
        },
        "ids": {
            "terms": {
                "field": "&id",
                "size": 10000
            }
        }
    }
}

@tenant=insight
@ticket=eu-8307
@repo=stack-2023-09

###
http://localhost:9200/{{tenant}}:tenant_configuration/_search
Content-Type: application/json

{
    "_source": [
        "subscribedMarketAbuseReports"
    ],
    "size": 1,
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            }
        }
    },
    "aggs": {
        "&model": {
            "terms": {
                "field": "&model",
                "size": 100
            }
        }
    }
}

###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}a
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "{{tenant}}:tenant_configuration",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "adding ramping algo"
  }
}

### 

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}a
Content-Type: application/json

###
# @name process

POST http://localhost:9200/{{tenant}}:tenant_configuration/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "lang": "painless",
        "source": "ctx._source['subscribedMarketAbuseReports']=params.subscribedMarketAbuseReports;",
        "params": {
            "subscribedMarketAbuseReports": [
                "PARKING",
                "INSIDER_TRADING_V2",
                "FRONT_RUNNING",
                "FRONT_RUNNING_CLIENT_VS_CLIENT",
                "INSIDER_TRADING_NEWS",
                "POTAM",
                "RESTRICTED_LIST",
                "WASH_TRADING",
                "PAINTING_THE_TAPE",
                "SUSPICIOUS_LARGE_ORDER_VOLUME",
                "SUSPICIOUS_LARGE_ORDER_VOLUME_V2",
                "PARKING",
                "LAYERING",
                "SPOOFING",
                "INSIDER_TRADING_V3",
                "PAINTING_THE_TAPE_V2"
            ]
        }
    },
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            }
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json


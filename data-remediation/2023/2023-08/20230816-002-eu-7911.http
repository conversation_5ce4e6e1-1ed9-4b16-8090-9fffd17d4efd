@tenant=pinafore
@ticket=eu-7911
@repo=stack-2023-08

http://localhost:9200/{{tenant}}:transaction/_search
Content-Type: application/json

{
    "_source": [
        "reportDetails",
        "addedToReport",
        "&status",
        "disposition"
    ],
    "size": 1,
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            },
            "filter": [
                {
                    "term": {
                        "&model": "Transaction"
                    }
                }
            ]
        }
    },
    "aggs": {
        "addedToReport": {
            "terms": {
                "field": "addedToReport"
            }
        },
        "&model": {
            "terms": {
                "field": "&model"
            }
        },
        "sourceKey": {
            "terms": {
                "field": "sourceKey",
                "size": 100
            }
        }
    }
}

###
GET http://localhost:9200/_cat/snapshots/stack-2023-08?v&s=start_epoch
Content-Type: application/json



###
http://localhost:9200/_snapshot/stack-2023-08/auto-20230815-132021/_status?pretty
Content-Type: application/json

###
http://localhost:9200/_snapshot/stack-2023-08/auto-20230815-132021
Content-Type: application/json

###
http://localhost:9200/_snapshot/stack-2023-08/_current

###

http://localhost:9200/_tasks


###


# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "{{tenant}}:*",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "pinafore backup"
  }
}

### 

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###
# @name process

POST http://localhost:9200/{{tenant}}:transaction,{{tenant}}:transaction_report/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "match_all": {}
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/lF9I3ruaS1mYn9AghFkQww:12359102
Content-Type: application/json

### requeue the existing files

http://localhost:9200/pinafore:transaction*/_search
Content-Type: application/json

{
    # "_source": [
    #     "reportDetails",
    #     "addedToReport",
    #     "&status",
    #     "disposition"
    # ],
    "size": 100,
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            },
            "filter": [
                {
                    "term": {
                        "&model": "TransactionStatus"
                    }
                }
                # ,
                # {
                #     "term": {
                #         "externalStatus": "ARM_ACCEPTED"
                #     }
                # }
            ]
        }
    },
    "aggs": {
        "externalStatus": {
            "terms": {
                "field": "externalStatus"
            }
        },
        "addedToReport": {
            "terms": {
                "field": "addedToReport"
            }
        },
        "&model": {
            "terms": {
                "field": "&model"
            }
        },
        "sourceKey": {
            "terms": {
                "field": "sourceKey",
                "size": 100
            }
        }
    }
}

###

http://localhost:9200/pinafore:transaction_report/_search
Content-Type: application/json

{
    "query": {
        "bool": {
            "filter": [
                {
                    "term": {
                        "&model": "TransactionStatus"
                    }
                },
                {
                    "term": {
                        "transactionRefNo": "20220516938762022040621908202205162:NEWT"
                    }
                }
            ]
        }
    }
}

###
# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}a
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "{{tenant}}:transaction*",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "pinafore trv1 backup"
  }
}

### 

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}a
Content-Type: application/json

###

# @name process

POST http://localhost:9200/{{tenant}}:transaction_report/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "lang": "painless",
        "source": "ctx._source['externalStatus']=params.externalStatus;ctx._source['externalChecks']=params.externalChecks;ctx._source['externalResponse']=params.externalResponse;",
        "params": {
            "externalStatus": "ARM_ACCEPTED",
            "externalChecks": [],
            "externalResponse": "MessageInformation(fileID=549300919UMG84ROIT89_20200707082125733, responseType=ARM, reports=6, armFailed=0, queuedToNCAs=6, ncAsFailed=null, acceptedByNCAs=null, reportDateTime=2020-07-07T07:24:15.853Z)"
        }
    },
    "query": {
        "bool": {
            "filter": [
                {
                    "term": {
                        "&model": "TransactionStatus"
                    }
                },
                {
                    "term": {
                        "transactionRefNo": "20220516938762022040621908202205162:NEWT"
                    }
                }
            ]
        }
    }
}

###
@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

### Mark report as accepted

http://localhost:9200/pinafore:transaction_report/_search
Content-Type: application/json

{
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            },
            "filter": [
                {
                    "term": {
                        "&model": "TransactionReport"
                    }
                },
                {
                    "term": {
                        "&id": "SteelEye_TRMiFIR_NO-LEI.2023081646"
                    }
                }
            ]
        }
    }
}

###


# @name process

POST http://localhost:9200/{{tenant}}:transaction_report/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "lang": "painless",
        "source": "ctx._source['reportDispositions']=params.reportDispositions;",
        "params": {
            "reportDispositions": [
                {
                    "reportStatus": "PENDING",
                    "updateTime": 1692181534410
                },
                {
                    "reportStatus": "GENERATED",
                    "updateTime": 1692181534410
                },
                {
                    "reportStatus": "SUBMITTED",
                    "updateTime": 1692181534410
                }
            ]
        }
    },
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            },
            "filter": [
                {
                    "term": {
                        "&model": "TransactionReport"
                    }
                },
                {
                    "term": {
                        "&id": "SteelEye_TRMiFIR_NO-LEI.2023081646"
                    }
                }
            ]
        }
    }
}

###
@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json


```
POST https://pinafore.uat.steeleye.co/api/v1/transactions/cancel
["1652659200000:NEWT:20220516938762022040621908202205162"]

```

###

@id=1652659200000:NEWT:20220516938762022040621908202205162

http://localhost:9200/pinafore:*/_search
Content-Type: application/json

{
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            },
            "filter": [
               
                {
                    "term": {
                        "&id": "{{id}}"
                    }
                }
            ]
        }
    }
}

###



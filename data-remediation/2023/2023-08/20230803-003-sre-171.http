@tenant=chelverton
@ticket=sre-171
@repo=stack-2023-08

###
http://localhost:9200/{{tenant}}:tenant_configuration/_search
Content-Type: application/json

{
    "size": 111,
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            }
        }
    },
    "aggs": {
        "&id": {
            "terms": {
                "field": "&id",
                "size": 100
            }
        }
    }
}

###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}a
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "{{tenant}}:account_firm,{{tenant}}:tenant_configuration",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "remove extra account_firm, fix tenant_configuration"
  }
}

### 

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}a
Content-Type: application/json


###

DELETE http://localhost:9200/chelverton_uat_steeleye_co_accountfirm/AccountFirm/chelverton.uat.steeleye.co?refresh=true
Content-Type: application/json

###


###
# @name process

POST http://localhost:9200/{{tenant}}:tenant_configuration/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "lang": "painless",
        "source": "ctx._source['&id']='chelverton.uat.steeleye.co';"
    },
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            }
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

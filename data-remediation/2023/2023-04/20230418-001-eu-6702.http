@tenant=bux
@ticket=eu-02
@repo=stack-2023-04

# {"action": "current", "fromDate": "1681689600000", "toDate": "1681775999999"}

http://localhost:9200/{{tenant}}:emir*/_search
Content-Type: application/json

{
    "size": 1,
    "query": {
        "bool": {
            "filter": [
                {
                    "term": {
                        "&model": "EmirPosition"
                    }
                }
                ,
                {
                    "terms": {
                        "dataSource.sourceKey": [
                            "s3://bux.steeleye.co/flows/emir-universal-cme/202304/BUX.Financial.Services.Ltd_EMIR.Positions.18042023.110700.csv",
                            "s3://bux.steeleye.co/flows/emir-universal-cme/202304/BUX.Financial.Services.Ltd_EMIR.Trade.18042023.103818.csv"
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "workflow.internal.status": {
            "terms": {
                "field": "workflow.internal.status"
            }
        },
        "models": {
            "terms": {
                "field": "&model"
            }
        },
        "indices": {
            "terms": {
                "field": "_index"
            }
        }
    }
}

###


# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "bux_emir_uti_v1",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "make all positions not reported yet"
  }
}
### 

GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###
# @name process

POST http://localhost:9200/bux_emir_uti_v1/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "lang": "painless",
        "source": "ctx._source['workflow']['internal']['status']='NOT_REPORTED';ctx._source['workflow'].remove('reportId');ctx._source['workflow'].remove('reportFilename');ctx._source['workflow'].remove('reportIndex');ctx._source['workflow'].remove('tradeRepository');"
    },
    "query": {
        "bool": {
            "filter": [
                {
                    "term": {
                        "&model": "EmirPosition"
                    }
                }
                ,
                {
                    "terms": {
                        "dataSource.sourceKey": [
                            "s3://bux.steeleye.co/flows/emir-universal-cme/202304/BUX.Financial.Services.Ltd_EMIR.Positions.18042023.110700.csv",
                            "s3://bux.steeleye.co/flows/emir-universal-cme/202304/BUX.Financial.Services.Ltd_EMIR.Trade.18042023.103818.csv"
                        ]
                    }
                }
            ]
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

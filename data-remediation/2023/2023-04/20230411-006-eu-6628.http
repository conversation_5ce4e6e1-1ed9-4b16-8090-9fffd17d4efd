
@tenant=globalshares
@ticket=eu-6628
@repo=stack-2023-04

### check cases
@watch=7b2c9e87-ca0d-5b2b-91ef-9ed4b6567831

GET http://localhost:9200/{{tenant}}:watch_execution/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            # "must_not": [
            #     {
            #         "terms": {
            #             "&model": [
            #                 "SurveillanceWatchExecution"
            #             ]
            #         }
            #     }
            # ],
            "filter": [
                # {
                #     "term": {
                #         "watch.&id": "{{watch}}"
                #     }
                # }
                {
                    "term": {
                        "detail.watchId": "{{watch}}"
                    }
                }
            ]
        }
    },
    "aggs": {
        "scenarioId": {
            "terms": {
                "field": "scenarioId",
                "size": 10000
            }
        },
        # "workflow.caseSlug": {
        #     "terms": {
        #         "field": "workflow.caseSlug"
        #     }
        # },
        # "workflow.resolutionCategory": {
        #     "terms": {
        #         "field": "workflow.resolutionCategory"
        #     }
        # },
        # "workflow.resolutionSubCategories": {
        #     "terms": {
        #         "field": "workflow.resolutionSubCategories"
        #     }
        # },
        # "workflow.resolvedViaCase": {
        #     "terms": {
        #         "field": "workflow.resolvedViaCase"
        #     }
        # },
        # "workflow.status": {
        #     "terms": {
        #         "field": "workflow.status"
        #     }
        # },
        "model": {
            "terms": {
                "field": "&model"
            }
        },
        "indices": {
            "terms": {
                "field": "_index"
            }
        }
    }
}



###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "axx-we64yesxvxsoyvjp",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "removal as per ticket"
  }
}

###

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json


####
# @name process

POST http://localhost:9200/axx-we64yesxvxsoyvjp/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "filter": [
                {
                    "term": {
                        "detail.watchId": "{{watch}}"
                    }
                }
            ]
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json


@tenant=*
@ticket=eu-6676
@repo=stack-2023-04

###
# @name fetch

http://localhost:9200/{{tenant}}:transaction_report/_search
Content-Type: application/json

{
    "_source": [
        "generated",
        "reportDispositions"
    ],
    "size": 3,
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                },
                {
                    "nested": {
                        "path": "reportDispositions",
                        "query": {
                            "bool": {
                                "filter": [
                                    {
                                        "term": {
                                            "reportDispositions.reportStatus": "SUBMITTED"
                                        }
                                    }
                                ]
                            }
                        }
                    }
                }
            ],
            "filter": [
                {
                    "term": {
                        "&model": "TransactionReport"
                    }
                },
                {
                    "range": {
                        "generated": {
                            "gte": "now-7d"
                        }
                    }
                }
            ]
        }
    }
}


http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "_source": [
        "generated",
        "reportDispositions"
    ],
    "size": 1,
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ],
            "filter": [
                {
                    "term": {
                        "&id": "{{report}}"
                    }
                }
            ]
        }
    },
    "aggs": {
        "ids": {
            "terms": {
                "field": "&id",
                "size": 10000
            }
        }
    }
}

@generated = {{fetch.response.body.$.hits.hits.0._source.generated}}
@index = {{fetch.response.body.$.hits.hits.0._index}}

###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}b
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "{{index}}",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "restore deleted report"
  }
}
### 

GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}b
Content-Type: application/json

###
# @name process

POST http://localhost:9200/{{index}}/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "lang": "painless",
        "source": "ctx._source['reportDispositions']=params.reportDispositions;",
        "params": {
            "reportDispositions": [
                {
                    "reportStatus": "PENDING",
                    "updateTime": {{generated}}
                },
                {
                    "reportStatus": "GENERATED",
                    "updateTime": {{generated}}
                },
                {
                    "reportStatus": "SUBMITTED",
                    "updateTime": {{generated}}
                }
            ]
        }
    },
    "query": {
        "bool": {
            "filter": [
                {
                    "term": {
                        "&id": "{{report}}"
                    }
                }
            ]
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

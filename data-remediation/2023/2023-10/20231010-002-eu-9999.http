@tenant=oppen<PERSON>
@ticket=eu-9999
@repo=stack-2023-10

http://localhost:9200/{{tenant}}:*/_flush
Content-Type: application/json

###

GET http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "query": {
        "term": {
            "_type": "$lock"
        }
    },
    "aggs": {
        "indices": {
            "terms": {
                "field": "_index"
            }
        }
    }
}


###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  "indices": "axttup_qjontyhjmws5p",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "lock removal"
  }
}

###
# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###
# @name process

POST http://localhost:9200/axttup_qjontyhjmws5p/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "term": {
            "_type": "$lock"
        }
    }
}

###

@task = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task}}
Content-Type: application/json

# "task": "r42WQj3KRnOnoFLoLUM2tQ:43481309"

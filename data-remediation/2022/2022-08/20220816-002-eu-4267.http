### delete by &id

@tenant=axis
@ticket = eu-4267
@repo = stack-2022-08

http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            "filter": [
                {
                    "terms": {
                        "&id": [
                            "1660521600000:NEWT:20220815AUTO0388651GS150820220007AXISIDB2",
                            "1660521600000:NEWT:20220815AUTO0388650GS150820220006AXISIDB1"
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "Index": {
            "terms": {
                "field": "_index",
                "size": 10000
            }
        },
        "Model": {
            "terms": {
                "field": "&model",
                "size": 10000
            },
            "aggs": {
                "Expiry": {
                    "terms": {
                        "field": "&expiry"
                    }
                }
            }
        },
        "sourceKey": {
            "terms": {
                "field": "sourceKey",
                "size": 10000
            }
        },
        "trno": {
            "terms": {
                "field": "reportDetails.transactionRefNo"
            }
        },
        "id": {
            "terms": {
                "field": "id"
            }
        }
    }
}

###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  "indices": "aw3ubrttmon4eufqcfvp",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "remove transcactions"
  }
}

###

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###

### Fix transactions for each report
# @name process

POST http://localhost:9200/aw3ubrttmon4eufqcfvp/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "filter": [
                {
                    "terms": {
                        "&id": [
                            "1660521600000:NEWT:20220815AUTO0388651GS150820220007AXISIDB2",
                            "1660521600000:NEWT:20220815AUTO0388650GS150820220006AXISIDB1"
                        ]
                    }
                }
            ]
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

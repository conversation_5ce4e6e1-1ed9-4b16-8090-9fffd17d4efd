
@tenant=iris
@ticket=eu-4930
@repo=stack-2022-10

GET http://localhost:9200/{{tenant}}:email/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            "filter": [
                {
                    "range": {
                        "timestamps.timestampStart": {
                            "lt": "2022-04-12"
                        }
                    }
                }
            ]
        }
    },
    "aggs": {
        "ids": {
            "terms": {
                "field": "&id",
                "size": 10000
            }
        }
    }
}

### snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  "indices": "{{tenant}}:email",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "remove old emails"
  }
}

### check_snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json


####
# @name process

POST http://localhost:9200/{{tenant}}:email/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "filter": [
                {
                    "range": {
                        "timestamps.timestampStart": {
                            "lt": "2022-04-12"
                        }
                    }
                }
            ]
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

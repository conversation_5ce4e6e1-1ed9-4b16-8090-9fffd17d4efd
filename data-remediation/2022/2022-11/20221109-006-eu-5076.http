@tenant = bux
@ticket = eu-5076
@repo = stack-2022-10
@filename=549300W3IVMGOR2OUL22_20221104162119040.csv_NCARes_GB_20221105115512078.xml



### find all data from file
http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            "should": [
                {
                    "wildcard": {
                        "sourceKey": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "dataSource.sourceKey": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "voiceFile.fileInfo.location.key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "metadata.source.fileInfo.location.key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "fileInfo.location.key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "voiceTranscript.fileInfo.location.key": "*/{{filename}}"
                    }
                }
            ],
            "minimum_should_match": "1"

        }
    },
    "aggs": {
        "sourceKey": {
            "terms": {
                "field": "sourceKey",
                "size": 10000
            }
        },
        "key": {
            "terms": {
                "field": "key",
                "size": 10000
            }
        },
        "dataSourceSourceKey": {
            "terms": {
                "field": "dataSource.sourceKey",
                "size": 10000
            }
        },
        "Indeces": {
            "terms": {
                "field": "_index",
                "size": 10000
            }
        },
        "Models": {
            "terms": {
                "field": "&model",
                "size": 10000
            }
        }
    }
}


###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  "indices": "axqmkpv2jontyhjmws3s",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "cleanup"
  }
}

###

# @name check_snapshot
http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json


###

# @name process2
POST http://localhost:9200/axqmkpv2jontyhjmws3s/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "should": [
                {
                    "wildcard": {
                        "sourceKey": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "dataSource.sourceKey": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "voiceFile.fileInfo.location.key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "metadata.source.fileInfo.location.key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "fileInfo.location.key": "*/{{filename}}"
                    }
                },
                {
                    "wildcard": {
                        "voiceTranscript.fileInfo.location.key": "*/{{filename}}"
                    }
                }
            ],
            "minimum_should_match": "1"
        }
    }
}

###

@task2 = {{process2.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task2}}
Content-Type: application/json




@tenant=oanda
@ticket=eu-5373
@repo=stack-2022-12

###

GET http://localhost:9200/{{tenant}}:order/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                },
                {
                    "terms": {
                        "sourceKey": [
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-oj-orders-2022-12-20.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-oj-orders-2022-12-19.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-oj-orders-2022-12-16.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-oj-orders-2022-12-15.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-oj-orders-2022-12-14.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-oj-orders-2022-12-13.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-ogm-orders-2022-12-20.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-ogm-orders-2022-12-19.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-ogm-orders-2022-12-16.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-ogm-orders-2022-12-15.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-ogm-orders-2022-12-14.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-ogm-orders-2022-12-13.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-oj-deals-2022-12-20.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-oj-deals-2022-12-19.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-oj-deals-2022-12-16.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-oj-deals-2022-12-15.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-oj-deals-2022-12-14.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-oj-deals-2022-12-13.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-ogm-deals-2022-12-20.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-ogm-deals-2022-12-19.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-ogm-deals-2022-12-16.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-ogm-deals-2022-12-15.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-ogm-deals-2022-12-14.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-ogm-deals-2022-12-13.csv"
                        ]
                    }
                }
            ],
            "filter": [
                {
                    "terms": {
                        "&model": [
                            "Order",
                            "OrderState"
                        ]
                    }
                }
                ,
                {
                    "terms": {
                        "dataSourceName": [
                            "MetaTrader5 - Orders",
                            "MetaTrader5 - Deals"
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "&model": {
            "terms": {
                "field": "&model"
            }
        },
        "_index": {
            "terms": {
                "field": "_index"
            }
        }
    }
} 

###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}a
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "{{tenant}}:order",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "remove order / orderstate due to bug for replay"
  }
}

###

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}a
Content-Type: application/json


####
# @name process

POST http://localhost:9200/{{tenant}}:order/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
   "query": {
        "bool": {
            "must_not": [
                {
                    "terms": {
                        "sourceKey": [
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-oj-orders-2022-12-20.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-oj-orders-2022-12-19.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-oj-orders-2022-12-16.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-oj-orders-2022-12-15.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-oj-orders-2022-12-14.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-oj-orders-2022-12-13.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-ogm-orders-2022-12-20.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-ogm-orders-2022-12-19.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-ogm-orders-2022-12-16.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-ogm-orders-2022-12-15.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-ogm-orders-2022-12-14.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-orders/mt5-ogm-orders-2022-12-13.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-oj-deals-2022-12-20.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-oj-deals-2022-12-19.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-oj-deals-2022-12-16.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-oj-deals-2022-12-15.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-oj-deals-2022-12-14.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-oj-deals-2022-12-13.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-ogm-deals-2022-12-20.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-ogm-deals-2022-12-19.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-ogm-deals-2022-12-16.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-ogm-deals-2022-12-15.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-ogm-deals-2022-12-14.csv",
                            "s3://oanda.steeleye.co/flows/order-metatrader-mt5-deals/mt5-ogm-deals-2022-12-13.csv"
                        ]
                    }
                }
            ],
            "filter": [
                {
                    "terms": {
                        "&model": [
                            "Order",
                            "OrderState"
                        ]
                    }
                },
                {
                    "terms": {
                        "dataSourceName": [
                            "MetaTrader5 - Orders",
                            "MetaTrader5 - Deals"
                        ]
                    }
                }
            ]
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

### restore snapshot


POST http://localhost:9200/{{tenant}}:order/_close
Content-Type: application/json

### Restore snapshot / index

POST http://localhost:9200/_snapshot/{{repo}}/{{ticket}}/_restore
Content-Type: application/json

{
  "indices": "oanda_order_v1"
}

### Open index
POST http://localhost:9200/{{tenant}}:order/_open
Content-Type: application/json

###


GET http://localhost:9200/{{tenant}}:order/_search
Content-Type: application/json

{
    "size": 1,
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            },
            "filter": [
                {
                    "terms": {
                        "&model": [
                            "Order",
                            "OrderState"
                        ]
                    }
                },
                {
                    "terms": {
                        "dataSourceName": [
                            "MetaTrader5 - Orders",
                            "MetaTrader5 - Deals"
                        ]
                    }
                },
                {
                    "range": {
                        "&timestamp": {
                            "lte": 1670975999999
                        }
                    }
                }
            ]
        }
    },
    "aggs": {
        "&model": {
            "terms": {
                "field": "&model"
            }
        }
    }
} 

###


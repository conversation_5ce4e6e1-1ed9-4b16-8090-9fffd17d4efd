### audit error find for cleanup before requeue oppenheimer query by isin eu-4566, eu-4857

@tenant=thornbridge
@ticket=eu-5321
@repo=stack-2022-11

http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            "filter": [
                {
                    "terms": {
                        "&id": [
                            "1670371200000:NEWT:20221207123430036490632",
                            "1670371200000:NEWT:20221207118377146160042",
                            "1670371200000:NEWT:20221207113985335912662",
                            "1670371200000:NEWT:20221207113985325912652",
                            "1670371200000:NEWT:20221207113985305912622",
                            "1670371200000:NEWT:20221207113985285912612",
                            "1670371200000:NEWT:20221207110329015585512",
                            "1670371200000:NEWT:2022120799811375054082",
                            "1670371200000:NEWT:2022120799043655019522",
                            "1670371200000:NEWT:2022120786359094294552",
                            "1670371200000:NEWT:2022120785116424171972",
                            "1670371200000:NEWT:2022120784802274157062",
                            "1670371200000:NEWT:2022120768049993167832",
                            "1670371200000:NEWT:2022120759637292546622",
                            "1670371200000:NEWT:2022120759286802515391",
                            "1670371200000:NEWT:2022120759286632515371",
                            "1670371200000:NEWT:2022120759284522515161",
                            "1670371200000:NEWT:2022120759284552515181",
                            "1670371200000:NEWT:2022120759284492515151",
                            "1670371200000:NEWT:2022120759284142515141",
                            "1670371200000:NEWT:2022120759283862515081",
                            "1670371200000:NEWT:2022120759283982515111",
                            "1670371200000:NEWT:2022120759283832515071",
                            "1670371200000:NEWT:202212073219200711"                        ]
                    }
                }
            ],
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ]
        }
    },
    "aggs": {
        "sourceKey": {
            "terms": {
                "field": "sourceKey",
                "size": 10000
            }
        },
        "Index": {
            "terms": {
                "field": "_index",
                "size": 10000
            }
        }
    }
}

###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  "indices": "awxrlekwhphtf3kqjcoh",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "cleanup"
  }
}

###

# @name check_snapshot
http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###


# @name process2
POST http://localhost:9200/awxrlekwhphtf3kqjcoh/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "filter": [
                {
                    "terms": {
                        "&id": [
                            "1670371200000:NEWT:20221207123430036490632",
                            "1670371200000:NEWT:20221207118377146160042",
                            "1670371200000:NEWT:20221207113985335912662",
                            "1670371200000:NEWT:20221207113985325912652",
                            "1670371200000:NEWT:20221207113985305912622",
                            "1670371200000:NEWT:20221207113985285912612",
                            "1670371200000:NEWT:20221207110329015585512",
                            "1670371200000:NEWT:2022120799811375054082",
                            "1670371200000:NEWT:2022120799043655019522",
                            "1670371200000:NEWT:2022120786359094294552",
                            "1670371200000:NEWT:2022120785116424171972",
                            "1670371200000:NEWT:2022120784802274157062",
                            "1670371200000:NEWT:2022120768049993167832",
                            "1670371200000:NEWT:2022120759637292546622",
                            "1670371200000:NEWT:2022120759286802515391",
                            "1670371200000:NEWT:2022120759286632515371",
                            "1670371200000:NEWT:2022120759284522515161",
                            "1670371200000:NEWT:2022120759284552515181",
                            "1670371200000:NEWT:2022120759284492515151",
                            "1670371200000:NEWT:2022120759284142515141",
                            "1670371200000:NEWT:2022120759283862515081",
                            "1670371200000:NEWT:2022120759283982515111",
                            "1670371200000:NEWT:2022120759283832515071",
                            "1670371200000:NEWT:202212073219200711"                        ]
                    }
                }
            ]
        }
    }
}

###

@task2 = {{process2.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task2}}
Content-Type: application/json

###


# @name check_data
http://localhost:9200/cedarknight:*/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            "filter": [
                {
                    "terms": {
                        "reportDetails.transactionRefNo": [
                            "2022032549917602784014A202203252",
                            "2022032549917602784021A202203252",
                            "2022032549917632784032A202203252",
                            "2022032549917632784033A202203252",
                            "2022032549917632784036A202203252",
                            "2022032549917602784114A202203252",
                            "2022032549917632784143A202203252",
                            "2022032549917632784166A202203252",
                            "2022032549917582784168A202203252",
                            "2022032549917632784242A202203252",
                            "2022032549917602784254A202203252",
                            "2022032549917632784268A202203252",
                            "2022032549917602784271A202203252",
                            "2022032549917602784272A202203252",
                            "2022032549917602784378A202203252",
                            "2022032549917602784385A202203252",
                            "2022032549917632784426A202203252",
                            "2022032549917602784439A202203252",
                            "2022032549917602784462A202203252",
                            "2022032549917632784476A202203252",
                            "2022032549917602784521A202203252",
                            "2022032549917582784545A202203252",
                            "2022032549917602784643A202203252",
                            "2022032549917632784644A202203252",
                            "2022032549917602784686A202203252",
                            "2022032549917632784712A202203252",
                            "2022032549917632784713A202203252",
                            "2022032549917632784714A202203252",
                            "2022032549917602784717A202203252",
                            "2022032549917632784718A202203252",
                            "2022032549917602784755A202203252",
                            "2022032549917602784814A202203252",
                            "2022032549917602784815A202203252",
                            "2022032549917632784825A202203252",
                            "2022032549917632784832A202203252",
                            "2022032549917632784949A202203252",
                            "2022032549917602784952A202203252",
                            "2022032549917602784953A202203252",
                            "2022032549917632785184A202203252",
                            "2022032549917632785231A202203252",
                            "2022032549917602785267A202203252",
                            "2022032549917602785387A202203252",
                            "2022032549917632785388A202203252",
                            "2022032549917582785398A202203252",
                            "2022032549917602785429A202203252",
                            "2022032549917602785437A202203252",
                            "2022032549917602785452A202203252",
                            "2022032549917632785495A202203252",
                            "2022032549917632785518A202203252",
                            "2022032549917602785517A202203252",
                            "2022032549917632785584A202203252",
                            "2022032549917632785586A202203252",
                            "2022032549917602785637A202203252",
                            "PER025032022A018PER025032022A018A202203252022032",
                            "PER025032022A019PER025032022A019A202203252022032",
                            "PER025032022A007PER025032022A007A202203252022032",
                            "PER025032022A001PER025032022A001A202203252022032",
                            "PER025032022A002PER025032022A002A202203252022032",
                            "PER025032022A008PER025032022A008A202203252022032",
                            "PER025032022A003PER025032022A003A202203252022032",
                            "PER025032022A009PER025032022A009A202203252022032",
                            "PER025032022A004PER025032022A004A202203252022032",
                            "PER025032022A010PER025032022A010A202203252022032",
                            "PER025032022A005PER025032022A005A202203252022032",
                            "PER025032022A011PER025032022A011A202203252022032",
                            "PER025032022A006PER025032022A006A202203252022032",
                            "PER025032022A012PER025032022A012A202203252022032",
                            "PER025032022A013PER025032022A013A202203252022032",
                            "PER025032022A014PER025032022A014A202203252022032",
                            "PER025032022A015PER025032022A015A202203252022032",
                            "PER025032022A016PER025032022A016A202203252022032",
                            "PER025032022A017PER025032022A017A202203252022032",
                            "PER025032022A020PER025032022A020A202203252022032",
                            "PER025032022A021PER025032022A021A202203252022032",
                            "PER025032022A022PER025032022A022A202203252022032",
                            "PER025032022A024PER025032022A024A202203252022032",
                            "PER025032022A023PER025032022A023A202203252022032",
                            "PER025032022A025PER025032022A025A202203252022032",
                            "PER025032022A026PER025032022A026A202203252022032",
                            "PER025032022A032PER025032022A032A202203252022032",
                            "PER025032022A033PER025032022A033A202203252022032",
                            "PER025032022A027PER025032022A027A202203252022032",
                            "PER025032022A029PER025032022A029A202203252022032",
                            "PER025032022A030PER025032022A030A202203252022032",
                            "PER025032022A031PER025032022A031A202203252022032",
                            "PER025032022A028PER025032022A028A202203252022032"
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "Models": {
            "terms": {
                "field": "&model",
                "size": 10000
            }
        },
        "Indices": {
            "terms": {
                "field": "_index",
                "size": 10000
            }
        }
    }
}

###

# @name snapshot
PUT http://localhost:9200/_snapshot/stack-2022-03/eu-2907
Content-Type: application/json

{
  "indices": "aw71b7vgkkjso5v4rbik",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "remove 86 transactions and expiry related"
  }
}

###

# @name check-snapshot
GET http://localhost:9200/_snapshot/stack-2022-03/eu-2907
Content-Type: application/json


### Cleanup before reprocessing

# @name process

POST http://localhost:9200/aw71b7vgkkjso5v4rbik/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "filter": [
                {
                    "terms": {
                        "reportDetails.transactionRefNo": [
                            "2022032549917602784014A202203252",
                            "2022032549917602784021A202203252",
                            "2022032549917632784032A202203252",
                            "2022032549917632784033A202203252",
                            "2022032549917632784036A202203252",
                            "2022032549917602784114A202203252",
                            "2022032549917632784143A202203252",
                            "2022032549917632784166A202203252",
                            "2022032549917582784168A202203252",
                            "2022032549917632784242A202203252",
                            "2022032549917602784254A202203252",
                            "2022032549917632784268A202203252",
                            "2022032549917602784271A202203252",
                            "2022032549917602784272A202203252",
                            "2022032549917602784378A202203252",
                            "2022032549917602784385A202203252",
                            "2022032549917632784426A202203252",
                            "2022032549917602784439A202203252",
                            "2022032549917602784462A202203252",
                            "2022032549917632784476A202203252",
                            "2022032549917602784521A202203252",
                            "2022032549917582784545A202203252",
                            "2022032549917602784643A202203252",
                            "2022032549917632784644A202203252",
                            "2022032549917602784686A202203252",
                            "2022032549917632784712A202203252",
                            "2022032549917632784713A202203252",
                            "2022032549917632784714A202203252",
                            "2022032549917602784717A202203252",
                            "2022032549917632784718A202203252",
                            "2022032549917602784755A202203252",
                            "2022032549917602784814A202203252",
                            "2022032549917602784815A202203252",
                            "2022032549917632784825A202203252",
                            "2022032549917632784832A202203252",
                            "2022032549917632784949A202203252",
                            "2022032549917602784952A202203252",
                            "2022032549917602784953A202203252",
                            "2022032549917632785184A202203252",
                            "2022032549917632785231A202203252",
                            "2022032549917602785267A202203252",
                            "2022032549917602785387A202203252",
                            "2022032549917632785388A202203252",
                            "2022032549917582785398A202203252",
                            "2022032549917602785429A202203252",
                            "2022032549917602785437A202203252",
                            "2022032549917602785452A202203252",
                            "2022032549917632785495A202203252",
                            "2022032549917632785518A202203252",
                            "2022032549917602785517A202203252",
                            "2022032549917632785584A202203252",
                            "2022032549917632785586A202203252",
                            "2022032549917602785637A202203252",
                            "PER025032022A018PER025032022A018A202203252022032",
                            "PER025032022A019PER025032022A019A202203252022032",
                            "PER025032022A007PER025032022A007A202203252022032",
                            "PER025032022A001PER025032022A001A202203252022032",
                            "PER025032022A002PER025032022A002A202203252022032",
                            "PER025032022A008PER025032022A008A202203252022032",
                            "PER025032022A003PER025032022A003A202203252022032",
                            "PER025032022A009PER025032022A009A202203252022032",
                            "PER025032022A004PER025032022A004A202203252022032",
                            "PER025032022A010PER025032022A010A202203252022032",
                            "PER025032022A005PER025032022A005A202203252022032",
                            "PER025032022A011PER025032022A011A202203252022032",
                            "PER025032022A006PER025032022A006A202203252022032",
                            "PER025032022A012PER025032022A012A202203252022032",
                            "PER025032022A013PER025032022A013A202203252022032",
                            "PER025032022A014PER025032022A014A202203252022032",
                            "PER025032022A015PER025032022A015A202203252022032",
                            "PER025032022A016PER025032022A016A202203252022032",
                            "PER025032022A017PER025032022A017A202203252022032",
                            "PER025032022A020PER025032022A020A202203252022032",
                            "PER025032022A021PER025032022A021A202203252022032",
                            "PER025032022A022PER025032022A022A202203252022032",
                            "PER025032022A024PER025032022A024A202203252022032",
                            "PER025032022A023PER025032022A023A202203252022032",
                            "PER025032022A025PER025032022A025A202203252022032",
                            "PER025032022A026PER025032022A026A202203252022032",
                            "PER025032022A032PER025032022A032A202203252022032",
                            "PER025032022A033PER025032022A033A202203252022032",
                            "PER025032022A027PER025032022A027A202203252022032",
                            "PER025032022A029PER025032022A029A202203252022032",
                            "PER025032022A030PER025032022A030A202203252022032",
                            "PER025032022A031PER025032022A031A202203252022032",
                            "PER025032022A028PER025032022A028A202203252022032"
                        ]
                    }
                }
            ]
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

### Check data
@tenant=axis
@ticket=eu-3926
@repo=stack-2022-07

GET http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            "filter": [
                {
                    "terms": {
                        "&id": [
                            "1657238400000:NEWT:20220708AUTO0388040JC080720220011ABLO2"
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "Index": {
            "terms": {
                "field": "_index",
                "size": 10000
            }
        },
        "Model": {
            "terms": {
                "field": "&model",
                "size": 10000
            }
        }
    }
}

###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  "indices": "aw3ubrttmon4eufqcfvp",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "remove some trades"
  }
}

###

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###

# @name process

POST http://localhost:9200/aw3ubrttmon4eufqcfvp/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "filter": [
                {
                    "terms": {
                        "&id": [
                            "1657238400000:NEWT:20220708AUTO0388040JC080720220011ABLO2"
                        ]
                    }
                }
            ]
        }
    }
}

###

@task = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task}}
Content-Type: application/json

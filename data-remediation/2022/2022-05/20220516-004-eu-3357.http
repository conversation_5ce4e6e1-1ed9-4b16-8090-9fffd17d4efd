
@tenant=fil-uat
@ticket=eu-3357
@repo=stack-2022-05

http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "size": 0,
    "_source": [
        "sourceKey"
    ],
    "query": {
        "bool": {
            "filter": [
                {
                    "wildcard": {
                        "sourceKey": "s3://fil-uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/FIL_SEblotter_*.csv"
                    }
                },
                {
                    "terms": {
                        "&model": [
                            "OrderState",
                            "Order",
                            "QuarantinedOrder"
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "Indeces": {
            "terms": {
                "field": "_index",
                "size": 10000
            }
        },
        "Models": {
            "terms": {
                "field": "&model",
                "size": 10000
            }
        }
    }
}


###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  "indices": "fil-uat_steeleye_co_order,axb8zisirdit_gdzzf72",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "data cleanup"
  }
}

###

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json


### Add the values

# @name process

POST http://localhost:9200/fil-uat_steeleye_co_order,axb8zisirdit_gdzzf72/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "filter": [
                {
                    "wildcard": {
                        "sourceKey": "s3://fil-uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/FIL_SEblotter_*.csv"
                    }
                },
                {
                    "terms": {
                        "&model": [
                            "OrderState",
                            "Order",
                            "QuarantinedOrder"
                        ]
                    }
                }
            ]
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

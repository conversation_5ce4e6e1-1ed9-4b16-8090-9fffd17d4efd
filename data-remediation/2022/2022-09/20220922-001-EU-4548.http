# Reinstante normal limit

@tenant=singer
@ticket=eu-4548a
@repo=stack-2022-09

###

http://{{tenant}}.steeleye.co/api/v1/version
Content-Type: application/json

###

http://{{tenant}}.uat.steeleye.co/api/v1/version
Content-Type: application/json

###

http://localhost:9200
Content-Type: application/json

###

GET http://localhost:9200/{{tenant}}:tenant_configuration/_search
Content-Type: application/json

{
    "_source": [
        "subscribedMarketAbuseReports"
    ],
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ]
        }
    }
}

###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  "indices": "{{tenant}}:tenant_configuration",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis <PERSON>",
    "taken_because": "change contractual algos for mar"
  }
}

###

# @name check_snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###

# @name process

POST http://localhost:9200/{{tenant}}:tenant_configuration/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "lang": "painless",
        "source": "ctx._source['subscribedMarketAbuseReports']=params.subscribedMarketAbuseReports;",
        "params": {
            "subscribedMarketAbuseReports": [
                "PAINTING_THE_TAPE",
                "WASH_TRADING",
                "FRONT_RUNNING",
                "INSIDER_TRADING_V2",
                "INSIDER_TRADING_V3",
                "INSIDER_TRADING_NEWS",
                "POTAM",
                "RESTRICTED_LIST",
                "MARKING_THE_CLOSE",
                "MARKING_THE_OPEN",
                "LAYERING_V2",
                "PHISHING",
                "SPOOFING_V2"
          ]
        }
    },
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ]
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

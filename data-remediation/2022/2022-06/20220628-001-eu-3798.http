### Check data
@tenant=thornbridge
@ticket=eu-3798
@repo=stack-2022-06

GET http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            "filter": [
                {
                    "terms": {
                        "&id": [
                            "1656288000000:NEWT:20220627BJCHBAU222190586200000011",
                            "1656288000000:NEWT:20220627BJCHBAU222190586200000021",
                            "1656288000000:NEWT:20220627BJCHBAU222190586200000031",
                            "1656288000000:NEWT:20220627BJCHBAU222190586200000041"
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "Index": {
            "terms": {
                "field": "_index",
                "size": 10000
            }
        },
        "Model": {
            "terms": {
                "field": "&model",
                "size": 10000
            }
        }
    }
}

###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  "indices": "awxrlekwhphtf3kqjcoh",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "remove some trades"
  }
}

###

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

###

# @name process

POST http://localhost:9200/awxrlekwhphtf3kqjcoh/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "filter": [
                {
                    "terms": {
                        "&id": [
                            "1656288000000:NEWT:20220627BJCHBAU222190586200000011",
                            "1656288000000:NEWT:20220627BJCHBAU222190586200000021",
                            "1656288000000:NEWT:20220627BJCHBAU222190586200000031",
                            "1656288000000:NEWT:20220627BJCHBAU222190586200000041"
                        ]
                    }
                }
            ]
        }
    }
}

###

@task = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task}}
Content-Type: application/json

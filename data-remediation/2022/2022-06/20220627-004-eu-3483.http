### Freetrade daily reset until 17 Jun

#Reference ticket: EU-3304 (20220512), EU-3460 (20220524)

# Check the watches
@tenant=freetrade
@ticket=eu-3483x
@repo=stack-2022-06
@watches="e4dcddc5-d229-4ced-907b-4b59e67570b0","bb341be2-91fc-4ae3-9564-691f9067d462"
# current day, 13:00:00 UTC
@lastexecution=1656331200000

# Execution failed due to bastion being broken.

http://localhost:9200/{{tenant}}:surveillance_watch/_search
Content-Type: application/json

{
    "size": 2,
    "_source": [
        "executionDetails",
        "status",
        "backtest",
        "query.name"
    ],
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            },
            "filter": [
                {
                    "term": {
                        "&model": "SurveillanceWatch"
                    }
                },
                {
                    "terms": {
                        "&id": [{{watches}}]
                    }
                }
            ]
        }
    },
    "aggs": {
        "indices": {
            "terms": {
                "field": "_index",
                "size": 10000
            }
        },
        "models": {
            "terms": {
                "field": "&model",
                "size": 10000
            }
        }
    }
}

###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  // may need to add the tags / alerts if those are required for cleanup
  "indices": "{{tenant}}:surveillance_watch,{{tenant}}:watch_execution",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "Update last execution for watchs daily"
  }
}

###

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

### change watches

# @name process

POST http://localhost:9200/{{tenant}}:surveillance_watch/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "script": {
        "lang": "painless",
        "source": "ctx._source['executionDetails']['lastExecution']='{{lastexecution}}';"
    },
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            },
            "filter": [
                {
                    "term": {
                        "&model": "SurveillanceWatch"
                    }
                },
                {
                    "terms": {
                        "&id": [{{watches}}]
                    }
                }
            ]
        }
    }
}


###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

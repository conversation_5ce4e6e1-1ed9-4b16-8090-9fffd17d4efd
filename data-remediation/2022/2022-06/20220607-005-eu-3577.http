

@tenant=benjamin
@ticket=eu-3577
@repo=stack-2022-06

http://localhost:9200/{{tenant}}:*/_search
Content-Type: application/json

{
    "size": 0,
    "query": {
        "bool": {
            "filter": [
                {
                    "terms": {
                        "&model": ["MarketAbuseScenarioTag","MarketAbuseAlert","SurveillanceWatchExecution"]
                    }
                }
            ]
        }
    },
    "aggs": {
        "Indeces": {
            "terms": {
                "field": "_index",
                "size": 10000
            }
        },
        "Models": {
            "terms": {
                "field": "&model",
                "size": 10000
            }
        }
    }
}


###

# @name snapshot
PUT http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json

{
  "indices": "benjamin_uat_steeleye_co_surveillancewatchexecution",
  "ignore_unavailable": true,
  "include_global_state": false,
  "metadata": {
    "taken_by": "Luis F",
    "taken_because": "data cleanup"
  }
}

###

# @name check-snapshot
GET http://localhost:9200/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json


### Add the values

# @name process

POST http://localhost:9200/{{tenant}}:*/_delete_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json

{
    "query": {
        "bool": {
            "filter": [
                {
                    "terms": {
                        "&model": ["MarketAbuseScenarioTag","MarketAbuseAlert","SurveillanceWatchExecution"]
                    }
                }
            ]
        }
    }
}

###

@task1 = {{process.response.body.$.task}}

# @name checktask
GET http://localhost:9200/_tasks/{{task1}}
Content-Type: application/json

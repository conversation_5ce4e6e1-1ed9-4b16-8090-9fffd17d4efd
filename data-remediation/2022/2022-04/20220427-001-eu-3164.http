### old users to delete

http://localhost:9200/*:account_user/_search
Content-Type: application/json

{
    "size": 0,
    "_source": [
        "name",
        "email"
    ],
    "query": {
        "bool": {
           "filter": [
                {
                    "term": {
                        "&model": "AccountUser"
                    }
                }
            ]
        }
    },
    "aggs": {
        "emails": {
            "terms": {
                "field": "email",
                "size": 10000
            }
        },
        "permissions": {
            "terms": {
                "field": "permissions",
                "size": 10000
            }
        },
        "roles": {
            "terms": {
                "field": "roles",
                "size": 10000
            }
        }
    }
}

### admins

http://localhost:9200/*:account_user/_search
Content-Type: application/json

{
    "size": 10000,
    "_source": [
        "roles",
        "permissions",
        "activated",
        "name",
        "email",
        "jobTitle",
        "status"
    ],
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ],
            "filter": [
                {
                    "terms": {
                        "permissions": [
                            "ADMIN",
                            "TRADE_SURVEILLANCE_ADMIN",
                            "TRANSACTION_REPORTING_ADMIN",
                            "COMMS_SURVEILLANCE_ADMIN"
                        ]
                    }
                },
                {
                    "term": {
                        "&model": "AccountUser"
                    }
                }
            ]
        }
    },
    "aggs": {
        "permissions": {
            "terms": {
                "field": "permissions",
                "size": 10000
            }
        },
        "roles": {
            "terms": {
                "field": "roles",
                "size": 10000
            }
        }
    }
}





### steeleye

http://localhost:9200/*:account_user/_search
Content-Type: application/json

{
    "size": 10000,
    "_source": [
        "roles",
        "permissions",
        "activated",
        "name",
        "email",
        "jobTitle",
        "status"
    ],
    "query": {
        "bool": {
            "must_not": [
                {
                    "exists": {
                        "field": "&expiry"
                    }
                }
            ],
            "filter": [
                {
                    "term": {
                        "&model": "AccountUser"
                    }
                },
                {
                    "wildcard": {
                        "email": "*@steel-eye.com"
                    }
                }
            ]
        }
    },
    "aggs": {
        "permissions": {
            "terms": {
                "field": "permissions",
                "size": 10000
            }
        },
        "roles": {
            "terms": {
                "field": "roles",
                "size": 10000
            }
        }
    }
}

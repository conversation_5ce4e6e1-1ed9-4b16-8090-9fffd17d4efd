@host={{$dotenv ES8_uat-shared-steeleye_HOST}} 
@auth={{$dotenv ES8_uat-shared-steeleye_AUTH}}
#
#
#
@tenant=iris
@ticket=eu-11388
@repo=stack-2024-07

###
GET https://{{tenant}}.steeleye.co/api/v4.0/version

###
GET {{host}}
Content-Type: application/json
Authorization: {{auth}}

###
POST {{host}}/{{tenant}}-*-alias/_flush
Content-Type: application/json
Authorization: {{auth}}

###
POST {{host}}/{{tenant}}-*-alias/_refresh
Content-Type: application/json
Authorization: {{auth}}

###
GET {{host}}/{{tenant}}-rts22_transaction-alias/_search
Content-Type: application/json
Authorization: {{auth}}

{
    "_source": [
        "workflow"
    ],
    "size": 2,
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            },
            "filter": [
                {
                    "terms": {
                        "&id": [
                            "QB166QB16620240610BUYI:2024-06-10:NEWT",
                            "QB9A6174QB9A617420240610BUYI:2024-06-10:NEWT"
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "_index": {
            "terms": {
                "field": "_index",
                "size": 100
            }
        }
    }
}

###
PUT {{host}}/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json
Authorization: {{auth}}

{
    "indices": "iris-rts22_transaction-index-2024.06.28-000001",
    "ignore_unavailable": true,
    "include_global_state": false,
    "metadata": {
        "taken_by": "Luis F",
        "taken_because": "mark 2 transactions as reported"
    }
}

###
GET {{host}}/_snapshot/{{repo}}/{{ticket}}
Content-Type: application/json
Authorization: {{auth}}

### 
# @name process
POST {{host}}/iris-rts22_transaction-index-2024.06.28-000001/_update_by_query?conflicts=proceed&wait_for_completion=false&refresh=true
Content-Type: application/json
Authorization: {{auth}}

{
    "script": {
        "lang": "painless",
        "source": "ctx._source['workflow']['nca']=params.status;ctx._source['workflow']['arm']=params.status;ctx._source['workflow']['isReported']=true;ctx._source.workflow.reportId='SteelEye_TRMiFIR_549300IL8TQT0JMDJJ80.20240708121703';",
        "params": {
            "status": {
                "status": "Accepted",
                "response": "549300IL8TQT0JMDJJ80_20240708133134830"
            }
        }
    },
    "query": {
        "bool": {
            "must_not": {
                "exists": {
                    "field": "&expiry"
                }
            },
            "filter": [
                {
                    "terms": {
                        "&id": [
                            "QB166QB16620240610BUYI:2024-06-10:NEWT",
                            "QB9A6174QB9A617420240610BUYI:2024-06-10:NEWT"
                        ]
                    }
                }
            ]
        }
    }
}

###
@task1 = {{process.response.body.$.task}}

# @name checktask
GET {{host}}/_tasks/{{task1}}
Content-Type: application/json
Authorization: {{auth}}

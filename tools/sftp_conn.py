import io
from paramiko import RSAKey
import fsspec
import paramiko
paramiko.common.logging.basicConfig(level=paramiko.common.DEBUG)


# Works
# d = {
#     "host": "sftp.bloomberg.com",
#     "port": 22,
#     "username": "mc1235098645",
#     "private_key_ascii": """-----BEGIN RSA PRIVATE KEY-----
# MIIJKwIBAAKCAgEA9QBqmIyWanlHIPp5Z26w3tzkKOY8xHNsqNJOzmrml90m5+zN
# z4JceXWv3RQp43NZagGo3hbwjbqTCvCL3qXPIyITQO3UYFOxz03Vt4oEoDY5BTE6
# L+KP3wgOqckdIAFyyCuJfdJM4UvQqpl4nga7Zi+mr2nRqbttsL5gZhn97Quh5Sod
# kXp5NL9u1xsVffrUq+uhO8/6MgOQO4b6PtURdfFOIHNL/uzpOzkq58e4X8lMs7kL
# uenh2zJgwrVIWcu+9FkAEXcSQQuC0atIVaAw+Hiz6DnJKo9TAZjbNCfbA/YwFzGj
# ED3a1SvfHlc7rVPy3Qh8Nokl9vu7Pk7G0ks94JPT6P/xXmLM6azDwawDSJsuF9kT
# Yo92PVCkdEYJl2aFvNohUBn5SIQlNQ2sdWmbZ945qCfaa6eqs89XxiJjd562KzJV
# hHueU3DpLeuUgBuJjzwKJza/r+jsU7i8tTSLpcVUDPohvMQnUYuDK5rhGB/b2W9R
# PTn9cCOw3ezBJowA9JiKQwQ//YczhveaIgZzR0n3K6eXnfbVEFGFQiq1CArLUkvW
# srmjowbees3glye4dNU43Jzj5jJxd77MfGki1OtIb21QqrQynzUhkAHL7cd6ZpCS
# Wc1eHOagfGKQtiyW2BOcfNG1wbXYeLA5NrThnLA4+s1kDhhEyMdTa3iaUlcCAwEA
# AQKCAgEA29Dz0srQaddGKiCQrGxz5IDrdIaeHuQwqYeHjUv8eGTDc3LTdUrRDWi5
# 0PE2ZAwqPSIgEKMkUQUEyNvpomnB3xPDko3P7l0pO4XqKjJ61APSRQZV3oC39LOV
# 9F+s8fpasZ3eTjVRJRKQtX/c6GvB130JYHjg1BiF4f3tH65SVUwKhUG0JcJs3Rak
# q/pw3gceqOFQWAnRwO3RipcgDx+7+sX2Asdvd9m3qJsyE6LtmYNSfi2Yh8WADtq0
# Vf2wXGChgzoXBZR/nDxnsxaqX6jKRySExeaSxsyw/M3YXoMjzfokS6sNUpUssfYa
# yW4a7PhU/luOlp+WoHJbHRww5pz7dbG1AUoTdd785rxq7aXqst9lYtPOOIW1GOth
# JfVdqs6rHao+ZhGLWX6ZxvFoReAOipEj6e/tiQu2oVDhR8SLVq0ehwOsa1fpj/19
# ZXX++DzBUzLGyw1RJeHkz5jstUBWYS05tJzKxsy0u/2dYbEOZAaJ/7/X8rkj251n
# 5DdabemZwG4Iqp/iAMYzLmvXb7S7oAusoGhC31z6qntCP+YGHzZgdj7eqR5ELbuO
# zOCCETKK4HSRTyQAqDZUkqEVLYn/gYMV+/Dqq+twv+SxBQn6LA1OcS87giNIscwE
# xEG0xU5BVTVlBZ4NG003jnOY6MmGKVGfdqaH9PG14OnSKF0+N4ECggEBAP7V/I4N
# qspALI2+l180wqHIldJxxSQkPT3cFOahOMOEtirkSeWUuDB3SIVXLbW0eVSBJ2eK
# f5d/ygAL8mTjYtMgZhCjzV1lqHzcRGI2M27YB/SFxQkH19noyWSGr/JTdPShDuuU
# Pg+sjJn5CegMgbiXlExhqcXUonYTjcl5YLKmILtafx0dNF79RoFT9jkFNiCyrZsE
# 76UKyGVRL1GrS4KuEE5Y8qHs0PX3+h66mWlFXtxmhBdTN044sjv606+oaLokDFli
# hzguELLHB4mbfBjxJRj4sFrlRX+/ey7jmsckevCdPMWIV4nLRageQrBmrGoW3aah
# EsOx4DhsFrbxXGUCggEBAPYe7elntQxP+G5JLdlYEDSAdOPL++44uECBYiF227Ew
# V1PKx9v9AEvu0qPaKM6h2kSxxHGiOJQ+gCw2DjqqYrUzme7T2ljahd0S9bZZblbC
# 37rCvREavz8aLF4eae4W4v1NxMSlcTpioQ73A1VqiwcBXoT7wVrYHdnnJTsKT1cO
# tyFYD4Qp9TdPln8Gl03nL0rJ1qKbJXBB7JKik0O7EVKvxGV41uKBeoE1CtKcIHvx
# BI81qiPVUIqnNotaaErw3StCEP0NkajkrDryxGYUw0pjpV6bU+j8aCJxwbaqxxg8
# FW1rPqQX/QovNlOneQ6nt60fOMfAbI4irMqWuaBnUgsCggEBAOQPZ21Nx+om0nir
# 99YpZoHY/FQQo0h78h7ZRg88e4tmyozKL2P523jsV4eVhH/YGrlIuJYOOa/Ammu+
# RViKsteVaCI+Jb0PDGFp0v092nu5dC0eRj6VuVP347xGWVH55HJFbQ/hZEUsu8vU
# N0xnXPRxGzPcN06nxBj92OdU6/gVereQ/q90gnj8G8wD8xgGlPFgVsOkAD/ozXz2
# 0e056mLyGf+z1LibMkreeQZlIoQ2o/WE/XPOcnFbhg7to+LRSaBEQJUB+1eIykLk
# oddBwiakMPoPHezKwI7aM4i8Arlz6V5KqadyJD1aIGWbZz1tNaHgAs9r2VovZyW3
# fgCNMo0CggEBAOSjovybfRNtDyrVDKTGLHe5hd1A2E9bnb8+8ryHwFuAvv92d+gj
# xqzDtibFOfeLYEtEoWu2+bytu0BXDyzssDoJL80wPb0hdA4F2QGHXvt2mYTT9/P1
# 1wh19hWRR3MJC+Go50yPLPrCOUC/s/cbvP/LA9WiUuNjtYeCNZH9Z+tGHlX5iqCK
# oeS6KOLkapbSV5qwmpYAbu00MOOf6BKZq+QWqOpjMazF/9ocPmuc4oiBRG0Nu3sC
# ZYoJAKDAYfaMf8n52/JZMqGlnWI9uLnkO+/VVgDnZzIfmVW2O/DIAaC/4F//qveh
# 2U4mwd9AyzMUF4vaX1MhrTb1e3PQRa1/jjUCggEBAJ3WDU3bAcGKoy27+6JF91az
# /Se0JZSkB3jQBR4nkOuVKDJWO9/5kMfPVXRSh83ymanUkV5G9ZXZpX29z9fZ/t2H
# 96UIFQPar3DRqMf7hh/JI1qppoCCXWkCEeVnkVdTA4zostyMUA7tdxK+HVm1K2qp
# JyNvebnnN2iTi975YmBBFS/Sm8/6oOObOo2fq8fEd0whCDtbrxSlDqmLpQLorxxe
# ap+wZYiy0Fv8kfebo/7syARDUwM5JRLMJczbPn+hUceD/OMRp0cP2sUtJQXpgkTv
# 1kfi7s31Dr0jyzqn/h2mOe7Uvo1clxpx4VuoXvUjw3EXJfmMsaCvm/4kafXDnFQ=
# -----END RSA PRIVATE KEY-----""",
# }

# d = {
#     "host": "sftp.bloomberg.com",
#     "port": 22,
#     "username": "emn26734872",
#     "private_key_ascii": """-----BEGIN RSA PRIVATE KEY-----
# MIIJKAIBAAKCAgEA0oTuMaLRsW+52x8fX3lHxI8v6QwpOEhFsxuxlSsk4fEiu2Fi
# nzqS2eft7/w13BByXQ6O968h/n8+Jl6PJnoTFKghG7aWa4W+95QR3C8Skp0B+GpS
# QhK7u0DxxHnI1UDGivkbJdKUfJ2TUIv9guCfuEdBdn3vg/X92DXqqi5VLbWdpdaN
# JL9F86YMWUt7n3yecVI8FSTSO3tJsAE4ZAamzTFBTBdtcVvIhXtCnjH4/y/2bIiy
# fW3gSXpq4JvaNN82s/CSgqUVMqNJRDKzD3xQFoSAzzwW6XrQLfJYD/8t+l+O0Jsj
# tXmPgGNCDk7boQX1jt+5FwzaqJh3l79GBVJi2oe08ASOH9ckHIPtv0RISmMJxSxd
# Q9D9Iz2B9zerT8RA58JhRWWar4I2jjcd6eph4QS78Am7iv7gOeAUM3jPuTmQIMKQ
# S/Cj5fqgxgEpEKbszrUbusBrP8OHjlH1hyfP1ju+wviZKHax/KeqrJM/q6tK+7ou
# ni/Zc3b8fdlXaaF2vAmnWXCXlPKsvO1bCg6KmmOFhSbyhQXL/hJBGs+84mcGbGZ6
# Y/zGPZr9UQsHrTFbbwoiGwk1hhJ1+wkyH/BIusv5/CS6QUCt43gIGFUQ/lSr0b65
# hPm0yWFSkxnga46qninqHjCbXMDoc+76KWhRKC0AS3u3Gmn4hHh3pjCRaRECAwEA
# AQKCAgBPcqVmDBlxCkYs4OD5Kg8gqh0dqV5BBF1xi8kID6E6t8Mwzj7m8rcLD5Fn
# WVqGM5rFbcIczXZZ0nGFD3icv8NFr3HXQKAh8faw/quC2iz7Mc/VjWJ/1wwMTiUv
# zfoyKOEE/5/0NTWeleVLp5lD9Duy0wlsi3iB56smFqr6EMRKnNjjBkZ8EMd4fTj5
# 1hfQh7pP8AYGwL+qfQvBJaS/cxcuJyGKcuj1t/rEDVGF3DwpKlOogCpEVdwoPs/o
# 9khyXa8KV4cjUOPgGWmEGFIlTPf4l4b5LqdC8/tu65+6YcZbUAbYyUO/sCGtBUif
# l7khWttiUSJMBajbqbawiRoG1iHBwcbes1JcEBpAgKF9W+PHEJ4Ths0u7X2gxQhk
# GQDHQ44H1rO7tka0qkSEjyUCtqM0tbmtO/aQg1EioakEtrRAM64BuPoBSQw+IZgr
# xRL103+cjEqY+eA8lNyEITDQkQ/bL1ne2PHigp0Hf95WdVttn8wWHqEqUqNQZVoC
# SiIpiUsGeW/YgtHWZ696bEgl6NwcgF0raNwPPYwrEOUQlgXRLY2zgr7rU/NT/b6b
# /2XZrM5A264ahMTkDG7X8jUwg0z44Ppt8L1x+JUVW8H7xW/1nOMT5AoB0xa+HfJL
# WVqibyAavhPfcTWkBXukSmuNoUa9cmUn3sHxwe6X5JBx8biG1QKCAQEA8g7NkicC
# kJQGPVd0yr6blhyrr4BjZTe3fiuvbrD61FF+CLRj4shOOeO52s1jVn8a0sZNbHrg
# yTH6hOYj/E3oEpp82ZOUAYA0GIaegFBpaW9X81/d3Bb+XBFelo9pm3LA2+R0JbdR
# 0YfE1wVsmoas2tW+bWFhmR7yqerPWO39vG9/CTEV672tSaAeQ72tmLoVAqYtbiVF
# 7AoA7JAabxSDgh72DJAlPwJgT6m8wEKMmB88vyRJ6lEM+ctojPKZbLalPe5KbhKN
# CU81hvp99v6T7TqtfC1gmgvvsUmEoh5EhVLIJWpw3kHU6OfHV1nu3/WqYOd6ObH7
# BwyhapAUsNeplwKCAQEA3qUVjvg8QAH3wNEVBUdzPUelBlIuohB691o+Sh902DLH
# 1dABoPADpDQUcb6gOt87EFjoXsyr+HNShwnu590BmpKssAKgPb8QpOVyEOsWsKqa
# JXMQkTjvh8PDjRn6tasSgVNq5XbUb3NKrL28Y2WCETQgAsrYoBGHt/sXElmMEdxr
# pZyniiryhePD2sN1pJvvvvuktXwWOvqRpVU9Klge7y75Oq7XsU59Z/Mqp4MxkRG9
# 9BlbjNeRWswKvMhKQiPMG6Co+dN9YPp8n3dz2Ub5pZb+8e2YB5ddu5UTW132H0/O
# K4OQPwOZGMpIz/nSiuEMHacqaxpt1Z9d/xblaLjHlwKCAQBR+8uKpeXLfCdE3Xs0
# NCzUQe+waIbBwIZImJk5n33FLbjHT/nI1NkR6FyJjCkKIdC+9OVCSbgazO+x1ZT4
# npZsvqBsvPxTKc37RJUolpOtX0Xlq2GkarFKE6P60KYPPWWu1Ii2QYnXsulrfm3T
# RKwFDoIJ4ALDbuT3wgzqbOkUypq3TUXEa6Dmn/HANd03Lkdxd0FJ3hv5n0XC2efq
# GYOVLdFeXfAPkEl4kd/ZKnEeEO5tOuhCEaOYc1l866olVRNQj4XsFCJqtcZGak8B
# uiX2Inqh5WijpElRvbnLgv7oPSqG6a9Cx/9Jxz8QCejLOBS175DJO8vvDksZoaUw
# ZEsPAoIBAQCOxNt7L3ErNlfR3AEXvoaGMDABSqe+osTHYqX40A0UKhP85oJ02hPr
# sr29ZpjGx6jPRcPULnjEO+ca0uLQ6eaCYTW9NCHfFmss4jjv4mBNb0ijZRKlnRX0
# QDNggt1Cq22VYpF0jWuSACNNe+mW71ulfoBcagjZNASMlMVpreRk+6xHqZnBvuJ/
# uts3qAkDQj9Nf35SJjz3GwBUPcGYKqPDiqKk38NOXl+KTeL3QeiB9xeQtMYBHaaG
# y7rC7ci4es4ClVZlxRlsG5PYijfOh3XEmnvnVQ0dKRIhHGoqN6mA0KMLQjU4Qy1k
# OSrRuogpKEClrzFDNtJhnu0ei2cWbQ8HAoIBADFJu3SzEyPkxo3U0h7i6W405REc
# E+4aL9gWRKY/RPOo95QDkIRbsDsVK3lRp1C4Z1HXnI9HN/lLnwqsJinMPL35kKvc
# u3RwKYpSohgvmi2NeaLzP+HzRzJ62DmOWsc3NPPvhtj+MTeFDhJ3TcQWisqIw8pE
# csGW9M8wx8/zLw7gjyELdYRiK5g/ecnSl/38vvag4yeq5PVoG4FrUdH8SeoG3Vef
# +WkLI5IakJHpcSS0ij7CCtfuchWrNs4ld5qgA2qgoJ4zgHrw7VqikASdi+r0wJIW
# NgeA11R9c17RA31AJAMZIN3CvMj4JLRRHoTAIpERepvnFPZnS/0xDntJp3I=
# -----END RSA PRIVATE KEY-----"""
# }



# d = {
#     "host": "mft.efgbank.com",
#     "port": 22,
#     "username": "mft480ext",
#     "private_key_ascii": """-----BEGIN RSA PRIVATE KEY-----
# MIIJKgIBAAKCAgEAt2vbVgxVoRAYrlEbt2gDsq2KfXRs+S+30dSW8b0vT03E05MR
# Qr1xg8wqCs/oIQg/pFPMqNmcrz6mOBFf1eM1aeE7cnOTpetYOXzTJrg8KfbB7xeo
# Hryk3gzKV3xUcxXR28yrEIWPw3smlZCxpJSn2jUm/QX+322uRClBr3DGjM3sO39A
# 26cyOeHpAX7kpQrz8AsojYa1p6VCmd/SgNHm7u3CRzJJtlwpXIy7P6iSJgr69xuz
# asH8mHNkCxZEpxtXMidmwQDSvCbot3Qru9VDttGvFJMUtRMlaHpoPu13q9AHp2bt
# isWhhg6uYDEQahHNq6q06tIJMhDrNLuCUXnA67iQbUqfK9Zm1tggrlrO0EGOfF9e
# ff7Gy2ktike+ADXTtQZFMxE0W4n8coWC60pSiW2wNpCeFMA8NbMj9LZzZTrM4gxx
# 275JkbpcVOzjwQdQeS3OVNWHd9+JHMWJFQsXeA2o6bjtXZ5chjmMQeZW5Q+W6U3Q
# v9wfahEAyF2+zqWoiZvlguTCBmbcJjM8nc4UHxdj2j2yOFkwdW3jwdb5JTSFP9Id
# U/gpZwFH9KX6EmlpmkLpO+mysZ2mMqq/QOsXvG1z4FhFyjm4dTk9S+H3Rj/ZEvOE
# TIwqNka2UXKl4KUb5oOdu+8hGV5yGINv3Stkc98HkuYjjoR2jLqVN0MTo/MCAwEA
# AQKCAgEAnUAQyRLGDtF392p98p1wHi19/9Ri4LeyRmiWCCYDe1/XEPSS5b74pN/S
# Y3afMkKaHU3ithfqpqt1Ybgzi3HBh0ab6qF/wc2k05RaimWE3w04cry7W66rQduB
# VcO3uvrH8kLjncSD3jmkeLGWOt1fkknjZSVQv0iPEMy2Da0H7AtXLiMI/ce33bv6
# QVgCuiRLwk4utDb7YgDiun0TQXGzJMAdwWAxpfAZXckhd5ATzB25z0Smay5oWmMD
# MCYlMYbVGKCDDZChGsHY3f7wx5Ys+X2HNoJqTFtfxNS8rsZzxI8TIMezeze4JPBN
# OxaVVfpxc21ksbTkQVIT9B+VuDhD1d9McpkD6o1MiNwv6YwPeeEHUgo2N1kDdkC4
# /xmawi7Ve2abvrNVpFp0w3P0FQkzP5bSPGS1Hw8iVyTDBv2pJRSeorlg3yrUoKlw
# 3CSiehP/M2KVL41D4GP/Swm/aRHsE4tTQoyBx2f6GlZ0Nf11Wor24ayd7nuPOyF8
# xhkF+BivsVvt1v9U8zGAyOj3S6PWqC+agC2PXRpzUL05fIOz5970gAcSCd3Y6ryF
# uo0pn7JmdsVTPU7ukZAoIVd1XKVRgTDcfizSghiyT6Trkuu9CbO0S1wRivSEhTyd
# KpFiaCKGUfOr2YL0rR22LQKxrfLBukT0l5uPuD6hOfe2oCZcSRECggEBAOSlFefR
# 3ww6EfH7OyT/FUQ7FDj3oMAlDsyoxMkDe6qubxD6+fXP+n0mw8QxvYyzC2igxL+1
# yi/xuiD7i8mVQYSTHWKGylyCGXCuUGhwavY4g7JzMEqa3Vxq0evKwAJ14aQJefoO
# kUyXOJT+CCct7uc6amrshRvG1UDV2i7gMpcReIxLvlT9FaQKBEtIlQxWbKBXDxPB
# JQBFtuT7xMvO64LG/HRB+DJFlLQluVVLNfmcTN+zJ/UrjhcXu7lDvS5I9UZm/y1M
# OtJF525HpAD2fXbIKOok8xd5SHvXLy8TE5aeQqXo8fXMVjN3B5qPMkG/BblDhVJQ
# bNO83acIKtfr4J8CggEBAM1dqxsTDLkCy7rr+BJnhOUZXSc62jkIcGI86Q8JRQyC
# a81mHq9HXbSS/O3FO2wHD/d5aMylGJkfsE9An4ZXY2REsCa3jUva78o+C0WpdjRo
# fVxVjAhFQJ33KIAeflzDNNHI/+YSOIqpsNJaWsyx+Cbg6roa+RHzWK0HrPlshiNi
# 0Jmmo8KSfaO85fJEjOAFUm0fjF5jAdxyOY+4rVSS9VPmavjOmw96yYBH00p3q9t+
# axrLHt8M0XajYVuBF03AfLEgLzYSJEoKDumghH9z9f1V/e2AhhIfZI+QppzaqkKv
# hS8Zy/BIDMu9OYeWpKQnsv1yo3qV/wGXxfHXPmyJ2C0CggEAabT1Eq8RutpiD4jC
# CFGyKFBUAh5iAFNhqzbfe7uTgg6RJL2TsJSNzyyL13nTk2f3f1DMhYFrT40nKzaA
# oxrRJpTJQhLlHa28Sz8bx50pplnscSUWD3fXnXF5SdT7vmd3gVosPXJctJc5PpDP
# fiWbTOXR60scIwJi902nSIK4gOU/4UTnYu17AtLqt0+AMSsHn32AZ/akv6J7cxP/
# IOayNUz/zFPvhnZq0HqcW5WhB5y+YbG3aH7Re1WqoXVy0iJC22Ck2vFiRHKzE2Yl
# qZhL2w2g9NdOV+znmPGcS9w68yiJ0aCHtMbal4KRTmCjsIe7ArL3wV2TYrTvmNiK
# zjasKwKCAQEAvUMr48HEoeTvuQ6rCtsYYMLlVLgqURFz8TVNXCwg1IyFqUAJ22Ob
# +kTUkJSKVN6VWm8rpB4q80N/Cyr1WavDz6514NicgWH+R5aycLriUx5RGf1FFoIs
# MOiNDLYNopPMY+In4/K65R/J87KNp8QEfGAgMu+Ux3RLLvu58AyR+p6gvFoU5cZ3
# v6ThDksawYJmGmdOx9Xa6zzhy5BZPW5KZc4em7pcsQFqHq0TDrgHTDvPnVvA0bNQ
# f1PG77uirGdPAzY0DFLf2tQ2Nh1Wvtcxxw2u/WjLJnyEAsM+qaPj8Z0s0uGlT4Y3
# NLTpBgpRJW/oxycVfm4qG1+yb1T9FK5c6QKCAQEAui1KHaszZiUKtvj4zaTT30Xy
# ZIqav8HYfcWPs3WVWKrwoHjfe/jPylJDp3T9sLLsoxb0YdXdwCgFbpUl/svbTgU7
# ByTgodPx1XUJMcgUEZGrpGjk/uLYRVrnZoEU/mp2WMXjeFtXAAYGMSY78Y7RJnYO
# ulzgx/Wq5fbkGZA185U5P23RxbTM0Q+/7CjQ7gzd3r5YTlrM5QQFgfCKwPh/hZEW
# v915zYw4s+34StDeW8En8Mr4p8cu5YniE1EGILvU475p21LSSLuLGmw3fRmTKgEN
# YNnyH3uP9cfoisJnxvn1nky6c01t1v0BYq7/4PB7i9J/RjhG1O6o0E/e2GcQzg==
# -----END RSA PRIVATE KEY-----""",
# }
# d={
#     "host": "sftp.mvr.cloud",
#     "port": 22,
#     "username": "sftp-1180",
#     "private_key_ascii": """-----BEGIN RSA PRIVATE KEY-----
# MIIJKgIBAAKCAgEAupOyzHdJAXiiWXNPmdWuct3eCF9o5E4qgGdEitu8cABFkPy4
# 6ywkEA+VJwJrlwc5X5wNhxBGy2npocCQa0ISAczFKZOSg5D8uX6OiXvMm0uSGtrK
# /PAA0rRUATR2XSuvjgGElHxiCxFnc7F/vjDY5dTh83yWsDHQVNDwdnKV9nHWCdEi
# rHdnekvxnIRg/J5WQ6PSTvcQ/mWvCcD+KUxocD0T1jbynBsVpP+ZIPV6r5HZaF1b
# pw6ZvnadUtmcbFiwQQ3ppB/9h+gSs6+I7zovIAP6Pc+pogo0rs9FrSfU7LUpy51H
# Lrug4/GVeQ2fC9iaZqgcrNCscMFNemipS7F62L9WRDa5SbYQHr7gq0xjrnlpxtGR
# E3Ok5xmKPncl7TV6oQL0a4JLow0VjF4ADcKYP47czHzjBqHqWlJh9dp0VzMjinxf
# XaRx+bH0L4E9bIC1gMucLSUvj1cvdgYYwItlTigppMWj2e7HUivxUuFPELiUh3ic
# UTRThL0+V185cGa+ZrFQ/YMsvClpFHqwonqMswYovm2rC4u8jodvAGFf1mCAQdTe
# bOF3OW9fxtTexXd4Rnl3vPYbrx92DJbm2kxUiOwMxu74dAJHuw66cQkL5Ji4tW/T
# 1ZtRE+MGMZY2pdM/nSmLRPPos8hjREJ+pnE3thE4SdREu7jtL7SKGIv9YXsCAwEA
# AQKCAgEAhcox1aLYDCEIMiKY+xQaUy478D8ShVxz572ljflB6AKZ3/0JUUJG3el+
# qAl5Q0Sf5KqPnMGUMntRWHcwWGl5ZicDXuv+jS+juhRBe971eoq3S2CHXNdxj6ZV
# 8sM++VLBQZBfFOcYmx0w1Op8UtLORgjguewmVlRyMs/dDuLeHGnG9SUtuwSohC7/
# I6Bdt+iGrVxFC/DpE7Owz8q/tree0JVLqzmdwUk+vSG4GcVN46Yvg0Im3eV/Y+/E
# ost++saiJRQn7VQvRoypFIaUKA/OCSQxBq/p0VOGMbBaI2/DpLXIMcUnbSCIn8v1
# xmd5HFKHbYOxhE7iyLwkpdl1RN0Q3C+VYZ1lWd2ItjY82HaPJ2KKUnATL/yxGZzM
# hZgt+5dHTo8pwgSzwB3f3ZKeKfAVRrSA1++/hgyTkjz9m4mAEb/JKJHn9ig4WQNJ
# n2AN1XAThdZoYfY+qQWJkNIfLBFlUd2SURMrp0QF7sYqASeB52Ci+xeTueduNdqa
# YvRtBcmSw3kAxullpvZgwftSSM2DwX6UD0k1dMg0ddjN8RsUTwJK9mdCuqV2WxIA
# 3CMKvzZuuPtRXcpxJ/LLhwHcPJTJp/91GoNoBxPVCqqmUfpCbc8SmWZk/gKqre1u
# D3aZHEJAXmcWHVJO17IRZACZp1hVAxlPMnYDmCVlCdlVbszvsBkCggEBAO/Xg8jm
# YF6SnXsTiDBZ+qVUI0ESV8LXGeTU024hBharOe8jnpV5CcvaxlUotQUBwmBLzoYs
# ITHQygwpkBNs0kT1teI3Ftrgg9UykufQT4MP5IB0onivGxP1vgjI34+SGza/1t/K
# fSu+q2SoeQcf3ByuyIY8Y8Rsl7ZxypBNINjTKv6TmUgTV1DJg9C1z+mFh9njUJT8
# DO611RhdR/GnFRiNExZWLUwz95MkiIQ6nEeoyw5virIsEWYnBe+n0EJLTsx5C3W/
# p7IzPeR1NsGzUpsL9UEEUbi8qXviXJ73uTQVu98xsQoExXtuvxA8u+W6NZYByAZM
# Pmy56u3wJ7vj54cCggEBAMcliemqPAMO2+YVij4im7Qhug9bGfmFR2LK90CsBkRg
# O7VL2FiDaTSXfFWNgRfufiGFuBCoaERrWKGxlS4XNNUxPnotousF98YMUawZRne7
# 5tYEoJtnjBgGRCQ//6kIkjJW+Bn/lvlyWScT/kVOlUUaLbBrMg0zQM2Oer2GNY5i
# ZQHL4GiXu7XHI266IlVuN4JrK1lD3yrelnRqDOMciMnSPm/dov3MT4acGOL0Cj06
# TZmSli6yaZ6vSbEz5wBgZFF1UHOMViuuEH3M/U7xX+TdfGVFfhR8JlTsiToojBYr
# VLfVirHBDoGMmZ/DQ8SJ7jNqK9wnxZs5ejOlRqU3C20CggEBAJb7bJKDtBbU5R4d
# ZZZKjmaVWLwV9/lHv501/QNO9q4+i/Hm0mX2FByoVEI3pfzI5rwH/LKqBRt+/DI7
# 197imlue8ii91T1kKJmFhMU5dVRkJMxYoA6o7Lrb8A1HprSSnV8R1Qp8Rd/L38k1
# 5Tp+6+mCfelDCusUEU1gxMuiQhM8m8w2zj95LXZF5Rz11zSCQsr/NjMTmHCNNuBn
# mGqfa8BQzrs49LsL28ZdMTNMUiUqNOmZfg47f5h26F29nnSdQ7WkXXp6c+7SIxw2
# TYoSgOXp2TJcrbVFzsuV7AiGmagYJnSSq4hLCKZzSQPHt+EeH62W4XxVctavEx1G
# GT1GcvECggEBAMS5jHdLN2U99HRhquKehXl4J7irogkfY9lv7rfwN3CsL890UQ9/
# IRpW2vgYhQIDEo5C7gVmlzmfYTq5lkilnAdOUg2Xr2zDxHPcD4IWpcn50Jpyj8V0
# kKva5M16ey+by+/2gbgNCDL6hp9X3MLSUhqlNaUk/5iSnN1ydkVqotU41dbkc5Ze
# 7YswYQwsy2XhLuyiuXvA1GAg6jet23Uu5uGX0x3mr/BPN/Adm2f283Zwdd/TMpCG
# plefiQiH4RY6Il9lqEe8xhL4LEpZiZHGV5haQ1w3Pck2qxcRYq4VB+2OeG3xgMT/
# lTjcj5WG6xbnumsH4rPkhgxExzbledgTfDECggEAfd1HuRZJliQtcpwIhijJ1DxB
# K910MEfpOu6d9rw8mcCCL8oMXYO+Y7cJWy1tufkT28hmSW6dkWFenLkiCKgHZk+H
# yfMh3vGqe5pJlFTTd7e3PTM5hUSPUqfSw7RmTDFxAeOIvq8tDq+Bb5Z7DpxNVQ0k
# TziS/kBts4euQetx4Wep9quDQwWO+pllC/Aj4pwjDtTe6eWrn/3FU0qJ7IKZM7Jq
# XJPfbEg0SHWERLzekeC2o/UegGpjgK9wrky4xKT1L09zPUAIkV8FG/8b1T6Bjz8w
# 0LD6yEIX8wLqERg6MSoUPPZPQ47cCXXlGIKX15p1uxG+2226m4Bx1N4dPApicw==
# -----END RSA PRIVATE KEY-----""",
# }

#without ssh
# d = {
#     "host": "sftp.bloomberg.com",
#     "port": 22,
#     "username": "mc1275720800",
#     "password": "{]2Fcf7,GRiEsq.0y8c"
# }
# d = {
#     "host": "feeds-collab.platform.refinitiv.com",
#     "port": 22,
#     "username": "TRMC24269_FtpUser",
#     "password": "7Ba;-t6Mv8W_P$1m)l"
# }
#onsim
# d = {
#     "host": "sftp.onsim.uk",
#     "port": 22,
#     "username": "2656-STEELEYE",
#     "private_key_ascii": """-----BEGIN RSA PRIVATE KEY-----
# MIIJKQIBAAKCAgEAyd29uTBp1b80s1gaCiiHdz0gILBDq8S1xdjSfL6PNM3dS9VS
# N3af0YWuWdRjYlNjmBSmxBuB+Q+v6vfb2IFwj/PsM0XkktmHxstj3xexZ/CeTj4c
# lqfIxr10iqkwc3jXEW9BdTvMuptCYzSueYDWfDuWBtmcBBBxxadpTeje20MELtqq
# dA/qiLaEqX3SY7kZR/NbgJxhMnIim4mHoeEern++z3xdJSR6HhbiWu+4jHjbQW6s
# tXo+gOR5/GMxjLdkb4e/l5DQSHoTp8czSWzx9M324QJpq2D/LguCOXrjah2+GHi+
# 4SYd6u+FCln3DeRp0/CdbH+KDMc5QUV4He5R/H1TVsxrj43JHqptZNdVY1/MePBx
# +bjNYYeEMbHV/6WzndB8Kn0h2/9fJrs0zyS3bgEgGViPQBg2Ck3N0yF72Gzl3Osv
# oGaqurEpSMrqCdvH+Jt2fJGhYsS9L9Vi9xGeU6qQdoCwtld4KAwWvIx5PpcaxQmK
# wfxkp+D9yX0jm1CdIFGtXDIB0YcVmqJGO+/fQ1oc7kCjV3q0FcWZXnPs7xghxuOK
# iq2EzLrHlnpN4NdDYQDlr/OOxi7/SAt1mphG7w0a744vwN6TXDrZdgBGCvURb6ey
# 6fZc9S4lkZ1Jgcui36LyOdtgQ1YAMOX1ZPvvuY6eWwSkZLjzQQ3X5D5k9dcCAwEA
# AQKCAgAdMOCPmA2l6ClxycqyL1wvhPpRX8vZWe/DNXBys4e35hEbNzTVamUAAmnM
# +aaJgG+FfW8EKsonvvn8a20yKL2jQ22jmHbOOYCl4aGGkEGKO0RZIig2LPseqiTh
# LEgiWpYXIlR/5G7diTgXanXUgioB+wfd47fo6ovZPjU/G+6mHFHNiLdfYzHSJTqX
# 2+cBnrGmMu3HclWn2tNgVIn1gb1TFIv+Kwi5J0Y1da+z4Lh6L7vdAWkeMRg6wU9W
# jO2MYLS4YBP3kexcnrKa73ZeJs/yS5povVWUf8lG2kkxkLC0Fqr3lPZdTorC/3Ho
# pkvQiXg4OrI5D1RiNqjz5FzTTzQxohXTyTnXdjgj9Wt8Ee3KVQEO/DlN3Q7SWDOZ
# R9Z/FBibRfcgw7xSXLW2sDgqkfoFxisSPiLApbhpxG0byIU37ViMvI7Se+sWKyxg
# EDA8C0UEnPwQFvZzqyL/8dcYjYEyQZuomZXoLYZQi6grupmg/WIGpR3NjbBcx5BL
# rbFWPHMq8LpU7pG2nnFeaaJY+6K17lZ/somW5MpJPT+5Di+hSQsTC4PN+KtT/JOY
# y2FhJc9Uts5OKPWIXj8I3+ZCNqfOmsFJJ9hBo+WEOKyTkoEzIkK+zi0kk5OEx3EW
# rp5iAz2G+3z46wT0dDxfZtopzAzy2R+M7bSmm6FGqgpLovY4CQKCAQEA6OTlvf4y
# /N9687FgRDE2hYNVcc3vjZw66Uw95OjKYFQ2O+t2lr6lYNbs+TWO9qdByGwlvvq5
# cxiCCYMXyTyUxYgsofT7euQwhqoppeUqctrNzcgCj1kXdm11/bmKSwq3Ke+Mbe/o
# YU7AEpKk+5lg4/WKWrjwOfC/lX9YSRWG4xzkbMDp2DNIDuegkqJPB28e/jO6reVB
# 5iSKIQj0g85BIuTh4SFpkCNu9WfSKruZo/Zw6lcf3kWlI5dkvpd9aViglWOVhesg
# IQvTAe3dlLn9lceYfSZSuziypJ9qRv+CwHAYQz0XnM8wnRpo7CqDOyHesUOfz4QI
# pLcLymKIbEjoJQKCAQEA3eTJvOK9kTh1PJkF6guPPR1nv5JYEFKixKHbO+2WqbvO
# KMzr5q6vNBKg1x/+8LN/qKXk0X8qKuGGFUQbp77jM/RdFXUgL/C7NtOjYkakez1K
# TN3NC6QlxO72yZ00W1e7AVHXpNn8oMLWNux1ftjoZYKjfRO2YeJna0JHOc7tBZlR
# evH3axrDDnOzXlytE8ZZ+mIcEjVAw3M1UpTV7/5HSo+9hyDiUwUE2Pg3hVlf/jUg
# HPVY6nqNuQ9dIS80CdeALta3Rt9e9cr06scfGoPStw5iM/pgl37nuKo2XTPbEqqT
# DLm98jtnIcr3nsVvS1UcUI2YTNP1h+8bDXYEBbU3SwKCAQEA36TfkgGHSRn6YgYV
# Yaa4qqGh7bVKs3wxflN+pFF/9d0ob4X/zWaqPUJnMjFiZi3bl/FyleEDMSl5xlSj
# BNYuLzpDUguFa0+x4b+KWwqkJsNDGrdU7f8p7snSoqIH0WQq3BRzkCbvynHrNGqq
# EAoL5xyljdVGqv0rnQ4644POkk3udRkT9k/YhKedpPJEXekxo/5c6BzdmXnBQ1Ps
# Kp5oInAVETIawxWjufnG8s5QDgncCM09hx7uJC1/yx6CiiVXn92Peph9s/481P9s
# 5yNsBucy4gtpKuyZcEi9sAwxrUZ3pioGgMjd8B+qXg+9EY3G4gcEcixieHpm0iSo
# yQOCGQKCAQAFd9Dc3b2MKZcCAkdaCCTkfagD2YhgBplKPS7Y4Db5Qc6HxNz/V0is
# ANWD158BsXqw3j2wR/DzE5GpTn9b9+FDc38juzoq1ETYyghZsKH3A4CkOkcGRWpE
# 2xqrxzr2EUhjdO/aeSC3mXWL7vHKSXAYqEp9F5JEAT9JLl3w979xhuCyJepR8lvi
# LEm8Ro5hUV8AQ5CrhjJtXpZuzYokGuEI3lfuT4okP5hp/Fvp5lTuP8Ll08n4JUL+
# z3kdCzu+DlhzTwk/Ltv5NKIqcvO6RItZxXn4C/B7az4sl801KMT6RcfP5Xff00Ts
# WUMW34ipk5RaC9M3+XUwf1zsTP82YMvXAoIBAQCW0H0Fpc4rQJ56YHgG9NSO3aYt
# txjq4gVP9wwBh5EWYZBwGX3D9Wyys9Hb84F26kG8U0j5EFMKyKd0IkTzfc4KS8S4
# KTRegQI4Er2pl3yrue4biXXe7lWzbznPLGyZa2L/xBEFIB6nlDeZFT8V5J9GetV0
# PQlN9qcETMNeeh50RczvF68Jt0rFoxoOq6pXGog4PzvGN2/XNOOLe0++d0SwkCiH
# mH1nNAj+vsQLVLUAxLky9sb4hyB/HzPhp31McMsUL3EBCYns0cjqf2ixK7jNVOfA
# gCYiW7JcyuoQ2j0xqnbYnXwusJyVN/dT1hSXGXPD3du03bsGFWCm7uh1Rq70
# -----END RSA PRIVATE KEY-----""",
# }
# d = {
#     "host": "sftp.mvr.cloud",
#     "port": 22,
#     "username": "d5819884-5f24-439b-af30-d24c3b029595.SFTPBulkDownload",
#     "private_key_ascii": """-----BEGIN OPENSSH PRIVATE KEY-----
# b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAABlwAAAAdzc2gtcn
# NhAAAAAwEAAQAAAYEA001AgwOTLF+3e3qnuXdrcG/4FQ6tdnWQKRqWjVr+zLBHAciS8d9C
# OcyP3dL4IgFiCG6ieO2dDWDZ8bUsIl2VNRs6LG3Hgsb43v7xC2rP9f55m6I5paOQ6ai3F3
# smNxdEXClsDpySffQq9o6e8qBnpGAdVJ5fN2697ox5x+f50CzOQNZR7f5M5NjmXPiufOrT
# xWow3k7WQc57UzjGQYK8Tk3K34PDRvnsiZJgfGnyfVgAz0UbDTif3Dz+DkJ7l2FxKx+g8F
# 0d2bHQqh5aNmieySqhUxweZhlLAZ0FC157//r2WGUiKR0Hp6M+q3VS1lnqJyeejSN3+RSf
# purl266aMPIq4GV7mzx8YO3NJqpIdJw/JmxZLzSSPqFj4/SChJaNmOFiAm2UCPt8qEG9r/
# aKPBgM6VZi8lR7xQwMu5FoOamqBygMHoyvbqpCFAhaMLOevyYM75Ok79QRYvNhLimJqd7D
# ubE1Nle7OZlVY93gaQleuadHj7dsGIXYDQR9fYg9AAAFmII/0aOCP9GjAAAAB3NzaC1yc2
# EAAAGBANNNQIMDkyxft3t6p7l3a3Bv+BUOrXZ1kCkalo1a/sywRwHIkvHfQjnMj93S+CIB
# YghuonjtnQ1g2fG1LCJdlTUbOixtx4LG+N7+8Qtqz/X+eZuiOaWjkOmotxd7JjcXRFwpbA
# 6ckn30KvaOnvKgZ6RgHVSeXzduve6Mecfn+dAszkDWUe3+TOTY5lz4rnzq08VqMN5O1kHO
# e1M4xkGCvE5Nyt+Dw0b57ImSYHxp8n1YAM9FGw04n9w8/g5Ce5dhcSsfoPBdHdmx0KoeWj
# ZonskqoVMcHmYZSwGdBQtee//69lhlIikdB6ejPqt1UtZZ6icnno0jd/kUn6bq5duumjDy
# KuBle5s8fGDtzSaqSHScPyZsWS80kj6hY+P0goSWjZjhYgJtlAj7fKhBva/2ijwYDOlWYv
# JUe8UMDLuRaDmpqgcoDB6Mr26qQhQIWjCznr8mDO+TpO/UEWLzYS4pianew7mxNTZXuzmZ
# VWPd4GkJXrmnR4+3bBiF2A0EfX2IPQAAAAMBAAEAAAGBAKx5JC1Mn7basjPyHDHBDkuKuZ
# 7GikRlFbTXZQggvFZrlKoaBAe1yqLPHKdd6vgm1NQ5Khk3Krie04b98PZkGMRuYv+c4/sV
# WlL7GdnN583NV4n30sInTsPK2Rgrhzf0n/HyRadvmguBYAg8xfUZhT+gzJAjbWkb1aB/vW
# +1JC4ORJSb2+g9aJpriS1eN17zdsRvN1BeXXj9h6CYeQtEIVxxFyV2sdCfc7mfZ25mNG6n
# hJGqwLf7/lCQ4HbXQXaBK5h9gxwYtwOBd/Jus3sfxYunvgSY8p5paaxNWc4Bx39xu2uk7n
# ZF1wJyp155A/eMRbvWu96UR4DZw5xun1r1nggqLPipPXHOMd55kthmUibPkRzrBhQdIFqM
# Qe6eYYLCWG/Xmr78T3FLshxXKphbl74Ha9YYETdFwtGABMplJlSwcId7RX4KW8GlHp0Fnf
# 1YrM9/ZznKX4GHBwq5eMxadAsqPlD/BfJZlS1KuyaeLfOyz8h5vVMQNEoUCLZwrai7AQAA
# AMEAo/XeZWe5pygAtxyM3e2qRIlAwTBWx/oapNWY/HJ9K9YOAzpe03ntPv5L/15txtKypX
# f5EURjad7EN5381eflFLO+xiKyc/16LuAogjxiHasn3h9HsCmbEUcMSBv9uRwB0nxVSxuO
# 4fAWefSW9wc+HMxL3b3erq76F/arMoTfrEgO3eJEYQ9N2nCqSRPnKeBZXAPOFzJ6NYzFok
# dwYvBWkiLaB+Ntt990BGKTZTRZHnnUEbcWxmgWGRtWcEppWaYlAAAAwQD0A02kQb0VKZSa
# +Pl/mWgG4yMmvwGB0lwjid8MLxUcndZ+++Ym1kymoXPoTAUSiSZVtMPSwXCWzWHF/uBE0i
# enbmNBoazf0s0JSiJ2IGOZLdpT1oIIlPzFajAl2B6plHaYLmrL6eSaKTRLu8HfrajukWHt
# bV1ZhBj5L4QqBe9c+CoWAb+eakcgqlcLfx4sDKn4l5Zn80lk76j8WT28Ly5Uc2wcpU3lMO
# o8xO51qGiTEN7O9CEd3J5t9i7gorLUiOEAAADBAN2ukyEYT1Zp4KLHNR5G/mz+d2eklaKS
# JJUNLO3uKs3XhqMS6VspirdNEfT1Uhafw5QOlCTch6HTJCjxoa2KRjHrH7CnAeUAU7vGia
# tRGJV6FxVQmQw19El7NmimAPENHqO+ZA2twhHhQ65ScD1T/eJXsXnAnZ5yXi/C5BOUN3j/
# E+wT7VZz40P0kMHmVoGtWVRXvb49zA1VyP6DYWjod2N6XKRXx37xPT4um7MP79SDIYUQ3M
# f85iNsoGdmlhke3QAAAB9zYXRoaXNoYmFsYWppQE1hY0Jvb2stUHJvLmxvY2FsAQI=
# -----END OPENSSH PRIVATE KEY-----""",
# }

#robeco not working
d = {
    "host": "sftp.bloomberg.com",
    "port": 22,
    "username": " mc1262065488",
    "private_key_ascii": """**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************""",
}

def with_proxy():
    pkey = RSAKey.from_private_key(io.StringIO(d['private_key_ascii']))
    proxy = paramiko.proxy.ProxyCommand('corkscrew proxy.enterprise.steeleye.co 8080 %s %d' % (d['host'], d['port']))

    authentication_kwargs = dict()
    authentication_kwargs["pkey"] = pkey
    authentication_kwargs["sock"] = proxy
    # authentication_kwargs["password"]=d["password"]

    fs = fsspec.filesystem(
        "sftp", host=d['host'], port=d['port'], username=d['username'], **authentication_kwargs
    )
    if fs:
        print("fs found:")
        files = fs.ls("/", detail=True)
        # files = [f for f in files if 'FE7B1AE7_BFF4994E_B542E44D_7BBB4DB8' in f['name'] and 'meta' in f['name']]
        print("Files in the remote directory:")
        k = []
        for file in files:
            k.append(file['name'])
#             Uncomment the following if you want to download the file
            print(f"Downloading {file}")
            fs.get(file["name"], f"/Users/<USER>/Documents/jira-files/sbi-covalis-errors{file['name']}")
        print(sorted(k))


if __name__ == '__main__':
    with_proxy()


# def with_proxy():
#     pkey = RSAKey.from_private_key(io.StringIO(d['private_key_ascii'])) # rsa
#     proxy = paramiko.proxy.ProxyCommand('corkscrew proxy.enterprise.steeleye.co 8080 %s %d' % (d['host'], d['port']))
#     # pkey = paramiko.Ed25519Key.from_private_key(io.StringIO(d['private_key_ascii'])) # openssh
#     # proxy = paramiko.proxy.ProxyCommand('corkscrew proxy.enterprise.steeleye.co 8080 %s %d' % (d['host'], d['port']))
#
#     authentication_kwargs = dict()
#     authentication_kwargs["pkey"] = pkey
#     authentication_kwargs["sock"] = proxy
#     # authentication_kwargs["password"]=d["password"]
#
#     fs = fsspec.filesystem(
#         "sftp", host=d['host'], port=d['port'], username=d['username'], **authentication_kwargs
#     )
#     if fs:
#         print("fs found:")
#         files = fs.ls("/", detail=True)
#         files = [f for f in files if '241125' in f['name'] and 'gpg' in f['name']]
#         print("Files in the remote directory:")
#         k = []
#         for file in files:
#             k.append(file['name'])
# #             Uncomment the following if you want to download the file
#             print(f"Downloading {file}")
#             fs.get(file["name"], f"/Users/<USER>/Documents/jira-files/huntersmoon/{file['name']}")
#         print(sorted(k))
#
#
# if __name__ == '__main__':
#     with_proxy()
import os
import logging
import typer
from pathlib import Path
import json
import yaml
import httpx
import re
from git import Repo, GitCommandError

log = logging.getLogger(__name__)

PROJECTS_ROOT = Path(__file__).resolve().parents[6]
SE_MONO_ROOT = PROJECTS_ROOT.joinpath("se-mono")
SE_FLEET_ROOT = PROJECTS_ROOT.joinpath("se-fleet")
WORKFLOWS_DEFINITION = "src/py/workflows/aries-workflow-deployer/definitions/workflows"
TASK_DEFINITION = "src/py/workflows/aries-workflow-deployer/definitions/tasks"
WORKFLOWS_ROOT = SE_MONO_ROOT / WORKFLOWS_DEFINITION

"""
How to get colored logs when running via the IDE (PyCharm instructions):
1. Go to Run -> Edit Configurations
2. Select the configuration you are using to run this script
3. You may need to access the "Modify options" dropdown at the top right of the dialog depending on your version
3. In the "Emulate terminal in output console" section, check the box to enable it.

Before you start:
 - Make sure you have your se-mono and se-fleet in the same source folder
 - Make sure se-fleet is in the correct branch ( mains, prod, or other) as this script depends on
 your local branch
 - If you are running against a cell with need for Access Request, make sure you have access.
 
Checks performed:
 1. List workflows, subworkflows and tasks required by the target workflow.
 2. Check if workflow deployer of the cell contains all workflows and tasks
 3. Check if workflow deployer version contains the related workflow/task
 4. Check if all workflows are registered for the tenant
 5. Check if all tasks have their corresponding workers
 
 The results of the tests will be printed on the console
"""


def workflow_checker_run(
    tenant_name: str = typer.Option("irises8", prompt="Tenant Name"),
    workflow_name: str = typer.Option("asc_voice", prompt="Workflow Name"),
    cell_name: str = typer.Option("dev-shared-2", prompt="Cell Name"),
    fleet_name: str = typer.Option("nonprod-eu-ie-1", prompt="Fleet Name"),
):
    # Need to manually set these env vars at this point, they are required in some imports ahead
    os.environ.setdefault(
        "DATA_PLATFORM_CONFIG_API_URL", f"https://config-api.{fleet_name}.steeleye.co"
    )
    os.environ.setdefault("STACK", cell_name)

    check_workflow_isdefined_condifgdb(
        cell_name=cell_name,
        fleet_name=fleet_name,
        tenant_name=tenant_name,
        workflow_name=workflow_name,
    )

    realm_suffix = cell_name.split("-")[0]
    realm_name = tenant_name + ("." + realm_suffix if realm_suffix != "prod" else "")
    if not crosscheck_cell_fleet(
        realm_name=realm_name, cell_name=cell_name, fleet_name=fleet_name
    ):
        raise typer.BadParameter("Cell or Fleet are incorrect for the given realm")

    required_workflows = set()
    required_tasks = set()
    deployed_workflows = {}
    deployed_tasks = {}
    missing_workflows = []
    missing_tasks = []
    registered_workflows = []
    missing_registered_workflows = []
    workflows_to_process = [workflow_name]
    blocklisted_workflows = [
        "aries_auditable_workflow_failure",
        "aries_workflow_failure_oma_event",
        "aries_workflow_failure_sink_file_audit",
        "comms_voice_sub_workflow",
        "elastic_ingestion",
        "elastic_ingestion_with_analytics",
        "email_ingestion_with_analytics",
    ]

    while workflows_to_process:
        current = workflows_to_process.pop()
        if current in required_workflows:
            continue  # Already processed

        workflow_dependencies, task_dependencies = get_dependencies_from_json(current)

        required_workflows.add(current)
        required_tasks.update(task_dependencies)

        for dep in workflow_dependencies:
            if dep not in required_workflows:
                workflows_to_process.append(dep)

    # Drop duplicates that may come from different files
    required_workflows = list(set(required_workflows))
    required_tasks = list(set(required_tasks))

    typer.echo("\nAll required workflows:")
    for wf in sorted(required_workflows):
        typer.echo(f"    - {wf}")
    # We only retrieve the tasks with type SIMPLE
    typer.echo("\nAll required tasks:")
    for tk in sorted(required_tasks):
        typer.echo(f"    - {tk}")

    # 1. get all workflows deployed for that cell and store in deployed_workflows
    # 2. check what workflows from required_workflows are not in deployed_workflows, and reports them in missing_workflows
    typer.echo(
        "\nChecking if all required workflows and tasks exist in workflow-deployer.yaml..."
    )
    workflow_deployer_yaml = check_workflow_deployer(
        cell_name=cell_name, fleet_name=fleet_name
    )
    for i in workflow_deployer_yaml["spec"]["values"]["workflows"]:
        deployed_workflows.update({i["name"]: i["version"]})
    for i in workflow_deployer_yaml["spec"]["values"]["tasks"]:
        deployed_tasks.update({i["name"]: i["version"]})

    for req_wf in required_workflows:
        if req_wf not in deployed_workflows.keys():
            missing_workflows.append(req_wf)
    for req_tk in required_tasks:
        if req_tk not in deployed_tasks.keys():
            missing_tasks.append(req_tk)

    if missing_workflows:
        typer.secho(
            "[WARNING!] Missing workflows in workflow-deployer.yaml:",
            fg=typer.colors.RED,
        )
        for wf in missing_workflows:
            typer.echo(f"    - {wf}")
    else:
        typer.secho(
            "[OK!] No missing workflows in workflow-deployer.yaml:",
            fg=typer.colors.GREEN,
        )
    if missing_tasks:
        typer.secho(
            "[WARNING!] Missing tasks in workflow-deployer.yaml:", fg=typer.colors.RED
        )
        for tk in missing_tasks:
            typer.echo(f"    - {tk}")
    else:
        typer.secho(
            "[OK!] No missing tasks in workflow-deployer.yaml:", fg=typer.colors.GREEN
        )

    # check if the version listed in workflow deployer contains the definition for the workflow
    typer.echo(
        "\nChecking if version assigned to workflow/task exists in that workflow-deployer image..."
    )
    check_definition_exist_for_tag(deployed_workflows, required_workflows, "workflow")
    check_definition_exist_for_tag(deployed_tasks, required_tasks, "task")

    # Check if the workflows are assigned to the tenant config file
    typer.echo("\nChecking if all workflows are registered for the tenant...")
    tenant_config_yaml = check_tenant_config(
        cell_name=cell_name, fleet_name=fleet_name, tenant_name=tenant_name
    )
    for i in tenant_config_yaml["spec"]["values"]["workflows"]:
        registered_workflows.append(i["name"])
    for req_wf in required_workflows:
        if (
            (req_wf not in registered_workflows)
            and (req_wf not in blocklisted_workflows)
            and not req_wf.endswith(("sub_workflow", "subworkflow"))
        ):
            missing_registered_workflows.append(req_wf)
    if missing_registered_workflows:
        typer.secho(
            "[WARNING!] Missing workflows in tenant config yaml:", fg=typer.colors.RED
        )
        for wf in missing_registered_workflows:
            typer.echo(f"    - {wf}")
    else:
        typer.secho(
            "[OK!] No missing workflows in tenant config", fg=typer.colors.GREEN
        )

    typer.echo(
        "\nChecking if any param-key or workflow-schedule are missing in tenant definition"
    )
    for req_wf in required_workflows:
        find_extra_workflow_keys(fleet_name, cell_name, tenant_name, req_wf)

    # Check if worker for task is registered in cell:
    typer.echo("\nChecking if all tasks have their corresponding workers")
    for req_tk2 in required_tasks:
        check_worker_chart(
            cell_name=cell_name, fleet_name=fleet_name, task_name=req_tk2
        )


def check_workflow_isdefined_condifgdb(
    cell_name: str, fleet_name: str, tenant_name: str, workflow_name: str
):
    if fleet_name != "nonprod-eu-ie-1":
        os.environ["DATA_PLATFORM_CONFIG_API_URL"] = (
            f"https://config-api.{fleet_name}.steeleye.co"
        )
    if cell_name != "dev-shared-2":
        os.environ["STACK"] = cell_name
    # Need to import here and not at the beginning of the code, because they will hold the env vars
    # defined previously, and can't change after
    from aries_se_api_client.client import AriesApiClient
    from data_platform_config_api_client.tenant_workflow import TenantWorkflowAPI
    from aries_config_api_compatible_client.tenant_workflow import (
        CompatibleTenantWorkflowAPIClient,
    )

    config_api_client = AriesApiClient(host=os.environ["DATA_PLATFORM_CONFIG_API_URL"])
    tenant_workflow_api = TenantWorkflowAPI(config_api_client)
    typer.echo("\nChecking if workflow is configured in configdb...")
    try:
        CompatibleTenantWorkflowAPIClient.get(
            tenant_workflow_api=tenant_workflow_api,
            tenant_name=tenant_name,
            workflow_name=workflow_name,
        )
        typer.secho(
            f"[OK!] Workflow is {workflow_name} registered in configdb",
            fg=typer.colors.GREEN,
        )
    except httpx.HTTPStatusError:
        typer.secho(
            f"[WARNING!] Workflow {workflow_name} is not set in configdb",
            fg=typer.colors.RED,
        )


def get_dependencies_from_json(json_name: str):
    """
    Loads a workflow JSON file and extracts dependent workflows and task names.
    Parameters:
        json_name (str): Name of the JSON file (without the .json extension).
    Returns:
        tuple: (list of dependent workflow names, list of task names)
    """
    json_files = list(WORKFLOWS_ROOT.rglob(f"{json_name}.json"))
    matched_file = next((f for f in json_files), None)

    if not matched_file:
        typer.echo(
            f"Error: No file named '{json_name}.json' found under {WORKFLOWS_ROOT}"
        )
        raise typer.Exit(code=1)

    with matched_file.open("r", encoding="utf-8") as f:
        json_str = f.read()  # JSON as string
    content = json.loads(json_str)  # JSON Parsed for python

    workflow_hits = get_subworkflow(content)
    tasks_hits = get_tasks(json_str)
    return workflow_hits, tasks_hits


def get_subworkflow(data, path=""):
    """
    Recursively searches JSON content for 'subWorkflowParam' and 'failureWorkflow' references.
    Parameters:
        data (dict or list): Parsed JSON content.
        path (str): Internal parameter used for recursion; tracks the path to current node.
    Returns:
        list of str: All referenced subworkflow names found.
    """
    results = []

    if isinstance(data, dict):
        # Detect common subworkflow pattern
        if "subWorkflowParam" in data and isinstance(data["subWorkflowParam"], dict):
            name = data["subWorkflowParam"].get("name")
            if name:
                results.append(name)
        elif (
            "name" in data
            and "type" in data
            and data["type"] == "SUB_WORKFLOW"
            and isinstance(data["name"], str)
        ):
            # Detect custom embedded subworkflow definitions by structure
            results.append(data["name"])

        # Detect failure workflow
        if "failureWorkflow" in data and isinstance(data["failureWorkflow"], str):
            results.append(data["failureWorkflow"])

        # Iterate over all keys
        for key, value in data.items():
            results.extend(
                get_subworkflow(value, path=f"{path}.{key}" if path else key)
            )

    elif isinstance(data, list):
        for idx, item in enumerate(data):
            results.extend(get_subworkflow(item, path=f"{path}[{idx}]"))

    return results


def get_tasks(data: str):
    """
    Extracts all task 'name' entries defined in the workflow definition JSON.
    Only returns tasks with type SIMPLE.
    Tasks are placed differently inside the definition, therefore we use regex instead of parsing
    like we do for the workflows
    Parameters:
        data (str): workflow JSON as str
    Returns:
        list of str: Task names found in the 'tasks' array.
    """
    json_str = re.sub(r"\s+", " ", data)
    pattern = r'"name"\s*:\s*"([^"]+)"[^{}]*?"type"\s*:\s*"SIMPLE"'
    matches = re.findall(pattern, json_str)
    return list(set(matches))


def crosscheck_cell_fleet(realm_name, cell_name, fleet_name):
    response = httpx.get(f"https://{realm_name}.steeleye.co/api/v4.0/version").json()
    return cell_name == response["cell"] and fleet_name == response["fleet"]


def check_workflow_deployer(cell_name, fleet_name):
    if cell_name in ["dev-shared-2", "dev-shared-1", "sit-shared-2", "sit-shared-2"]:
        path = SE_FLEET_ROOT.joinpath(
            f"cells/{cell_name}/aries/workflows/workflow_deployer.yaml"
        )
    else:
        path = SE_FLEET_ROOT.joinpath(
            f"fleets/{fleet_name}/cells/{cell_name}/aries/aries-workflow-deployer.yaml"
        )

    if not path.exists():
        raise FileNotFoundError(f"YAML file not found: {path}")

    with path.open("r", encoding="utf-8") as f:
        return yaml.safe_load(f)


def check_tenant_config(cell_name, fleet_name, tenant_name):
    if cell_name in ["dev-shared-2", "dev-shared-1", "sit-shared-2", "sit-shared-2"]:
        path = SE_FLEET_ROOT.joinpath(
            f"cells/{cell_name}/aries/config/tenants/{tenant_name}.yaml"
        )
    else:
        path = SE_FLEET_ROOT.joinpath(
            f"fleets/{fleet_name}/cells/{cell_name}/aries/config/tenants/{tenant_name}.yaml"
        )

    if not path.exists():
        raise FileNotFoundError(f"YAML file not found: {path}")

    with path.open("r", encoding="utf-8") as f:
        return yaml.safe_load(f)


def check_definition_exist_for_tag(
    deployed_items: dict, required_items: list, item_type: str
):
    """
    Verifies that each required item (workflow or task) is defined in the Git tag specified by the deployed items mapping.
    For each item:
    - Attempts to locate a JSON definition file in the corresponding Git tag.
    - First attempts a quick match using the filename.
    - If that fails, performs a full scan of all definition files in the path.
    Args:
        deployed_items (dict): Mapping of item names to their corresponding image tags from workflow-deployer.yaml.
        required_items (list): List of item names that are expected to be deployed and defined.
        item_type (str): The type of item being checked ("workflow" or "task").
    """
    # Group items by normalized tag to avoid redundant Git operations
    items_by_tag = {}
    for item_name in required_items:
        tag_name = deployed_items.get(item_name)
        if not tag_name:
            # Skip items not found in the mapping (already validated elsewhere)
            typer.echo(
                f"[info] Skipping {item_type} {item_name} since it's not in workflow-deployer.yaml"
            )
            continue

        normalized_tag = tag_name.replace(".arm64", "").replace(".amd64", "")
        items_by_tag.setdefault(normalized_tag, []).append(item_name)

    repo = Repo(".", search_parent_directories=True)

    definition_path = (
        WORKFLOWS_DEFINITION if item_type == "workflow" else TASK_DEFINITION
    )

    for tag_name, items in sorted(items_by_tag.items()):
        # Fetch tags once per unique tag
        try:
            repo.git.fetch("--tags")
        except GitCommandError as e:
            raise RuntimeError(f"Failed to fetch tags: {e}")

        # List all paths under the definition_path directory for the given tag
        try:
            tree_paths = repo.git.ls_tree(
                "-r", "--name-only", tag_name, definition_path
            ).splitlines()
        except GitCommandError as e:
            raise RuntimeError(f"Tag or path not found: {e}")

        # Pre-index JSON files by their base filename (without extension)
        json_files_map = {
            Path(rel_path).stem: rel_path
            for rel_path in tree_paths
            if rel_path.endswith(".json")
        }

        for item_name in items:
            match_path = None
            match_payload = None

            # Fast path: check if file named <item_name>.json exists
            rel_path = json_files_map.get(item_name)
            if rel_path:
                try:
                    blob_text = repo.git.show(f"{tag_name}:{rel_path}")
                    payload = json.loads(blob_text)
                    if payload.get("name") == item_name:
                        match_path = rel_path
                        match_payload = payload
                except (GitCommandError, json.JSONDecodeError):
                    # Fall through to full scan on failure
                    pass

            # Fallback path: scan all JSON files and check their "name" field if fast path failed
            if not match_payload:
                for rel_path in tree_paths:
                    if not rel_path.endswith(".json"):
                        continue
                    try:
                        blob_text = repo.git.show(f"{tag_name}:{rel_path}")
                        payload = json.loads(blob_text)
                    except (GitCommandError, json.JSONDecodeError):
                        continue

                    if payload.get("name") == item_name:
                        match_path = rel_path
                        match_payload = payload
                        break

            if match_path:
                typer.secho(
                    f"[OK!] Workflow deployer deploys {item_type}: {item_name} with version {tag_name}, and it is defined in this tag",
                    fg=typer.colors.GREEN,
                )
            else:
                typer.secho(
                    f"[WARNING!] {item_type.capitalize()}: {item_name} is not defined in tag: {tag_name} that is mentioned by workflow deployer",
                    fg=typer.colors.RED,
                )


def check_worker_chart(cell_name: str, fleet_name: str, task_name: str):
    """
    Validates whether a worker is defined for the given task in the appropriate cell.
    The function searches for YAML files in the expected workers directory based on the given `cell_name` and `fleet_name`.
    It inspects each file to find a matching `TASK_NAME` under `spec.values.env`.
    Notes:
        - Malformed YAML files or files without a dictionary structure are silently skipped.
        - Outputs [OK!] if a worker with the specified task name is found.
        - Outputs [WARNING!] if no matching worker is found.
    """
    if cell_name in ["dev-shared-2", "dev-shared-1", "sit-shared-2", "sit-shared-2"]:
        path = SE_FLEET_ROOT.joinpath("cells/base/aries/workers")
    else:
        path = SE_FLEET_ROOT.joinpath(
            f"fleets/{fleet_name}/cells/{cell_name}/aries/workers"
        )

    yaml_files = path.rglob("*.yaml")
    found_worker = False
    for yaml_file in yaml_files:
        try:
            with open(yaml_file, "r") as f:
                content = yaml.safe_load(f)
        except (yaml.YAMLError, UnicodeDecodeError):
            continue  # skip bad/malformed files

        if not isinstance(content, dict):
            continue  # skip files that don't contain a top-level dict

        # Navigate nested structure safely
        worker_task_name = (
            content.get("spec", {}).get("values", {}).get("env", {}).get("TASK_NAME")
        )

        if worker_task_name == task_name:
            found_worker = True
            break

    if found_worker:
        typer.secho(f"[OK!] Worker exists for task: {task_name}", fg=typer.colors.GREEN)
    else:
        typer.secho(
            f"[WARNING!] Worker not defined for task: {task_name}", fg=typer.colors.RED
        )


def load_workflow_params(tenant_file: Path, workflow_name: str) -> dict:
    """
    Load the workflow params for a given workflow name in a tenant YAML file.
    Returns a dict of the parameters, or None if workflow is not defined.
    """
    try:
        with open(tenant_file, "r") as f:
            data = yaml.safe_load(f)
    except (yaml.YAMLError, UnicodeDecodeError):
        return None

    workflows = data.get("spec", {}).get("values", {}).get("workflows", [])
    for wf in workflows:
        if wf.get("name") == workflow_name:
            return wf  # Return full workflow dict

    return None


def load_workflow_schedule(tenant_file: Path, workflow_name: str) -> dict:
    """
    Load the workflow params for a given workflow name in a tenant YAML file.
    Returns a dict of the parameters, or None if workflow is not defined.
    """
    try:
        with open(tenant_file, "r") as f:
            data = yaml.safe_load(f)
    except (yaml.YAMLError, UnicodeDecodeError):
        return None

    workflows = data.get("spec", {}).get("values", {}).get("workflow_schedules", [])
    if not workflows:
        return None

    for wf in workflows:
        if wf.get("workflow_name") == workflow_name:
            return wf  # Return full workflow dict

    return None


def find_extra_workflow_keys(
    fleet_name: str, cell_name: str, tenant_name: str, workflow_name: str
):
    """
    Checks whether other tenants in the same cell define extra configuration keys or scheduling
    for a given workflow that are missing in the specified target tenant.
    This function:
    - Loads the workflow parameters and schedule for the target tenant.
    - Iterates over all other tenant YAML files in the same cell.
    - Compares their workflow parameters to identify keys present in others but missing in the target.
    - Checks whether other tenants have a schedule defined for the same workflow if the target does not.
    Behavior:
        - Skips malformed or missing tenant files gracefully.
        - If the target tenant does not define the workflow, the function exits early (assumes nothing to validate).
        - Outputs `[WARNING!]` for any extra keys or schedule found in other tenants.
        - Outputs `[OK!]` if no discrepancies are found.
    """
    no_missing_keys = True
    no_missing_schedule = True

    if cell_name in ["dev-shared-2", "dev-shared-1", "sit-shared-2", "sit-shared-2"]:
        tenants_path = SE_FLEET_ROOT.joinpath(f"cells/{cell_name}/aries/config/tenants")
    else:
        tenants_path = SE_FLEET_ROOT.joinpath(
            f"fleets/{fleet_name}/cells/{cell_name}/aries/config/tenants"
        )

    file_path = tenants_path.joinpath(f"{tenant_name}.yaml")
    if not file_path.exists():
        raise FileNotFoundError(f"Target tenant file not found: {file_path}")

    target_params = load_workflow_params(file_path, workflow_name)
    target_schedule = load_workflow_schedule(file_path, workflow_name)

    # Didn't find the workflow in the tenant config. nothing wrong, will catch ahead on the code
    if target_params is None:
        return

    target_keys = set(target_params.keys())

    for tenant_file in tenants_path.glob("*.yaml"):
        if tenant_file.name == f"{tenant_name}.yaml":
            continue

        other_tenant = tenant_file.stem  # Get the filename without extension

        other_params = load_workflow_params(tenant_file, workflow_name)
        if other_params is not None:
            other_keys = set(other_params.keys())
            extra_keys = other_keys - target_keys

            if extra_keys:
                no_missing_keys = False
                typer.secho(
                    f"[WARNING!] Tenant '{other_tenant}' has extra keys for workflow '{workflow_name}': {extra_keys}",
                    fg=typer.colors.RED,
                )

        # if the target tenant doesn't have a scheduled configured for the WF, but another tenant has
        if not target_schedule:
            other_schedule = load_workflow_schedule(tenant_file, workflow_name)
            if other_schedule:
                no_missing_schedule = False
                typer.secho(
                    f"[WARNING!] Tenant '{other_tenant}' has schedule configured for workflow '{workflow_name}'",
                    fg=typer.colors.RED,
                )

    if no_missing_keys:
        typer.secho(
            f"[OK!] Tenant '{tenant_name}' has all keys for workflow '{workflow_name}':",
            fg=typer.colors.GREEN,
        )
        for key in target_keys:
            typer.echo(f"    - {key}")
    if no_missing_schedule:
        typer.secho(
            f"[OK!] Tenant '{tenant_name}' isn't missing schedule setup for workflow '{workflow_name}'",
            fg=typer.colors.GREEN,
        )


if __name__ == "__main__":
    typer.run(workflow_checker_run)

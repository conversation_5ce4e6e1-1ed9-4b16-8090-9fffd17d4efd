import pandas as pd
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.order.transformations.ice.pof.fix.ice_pof_fix_order_transformations import (
    IcePofFixOrderTransformations,
)
from swarm_tasks.order.transformations.ice.pof.fix.static import SourceColumns
from swarm_tasks.order.transformations.ice.pof.fix.static import TempColumns


class ArrowIcePofFixOrderTransformations(IcePofFixOrderTransformations):
    def _get_party_client(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=PartyPrefix.ID
            + self.source_frame.loc[:, SourceColumns.FF_9121]
            .apply(
                lambda client_id: client_id[0]
                if isinstance(client_id, list)
                else client_id
            )
            .values,
            index=self.source_frame.index,
            columns=[TempColumns.CLIENT],
        )

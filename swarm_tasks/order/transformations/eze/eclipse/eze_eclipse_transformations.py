import os
from ast import literal_eval
from typing import List

import numpy
import pandas as pd
from se_core_tasks.currency.convert_minor_to_major import CastTo
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import HierarchyEnum
from se_elastic_schema.static.mifid2 import OptionType
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.static import OrderType
from se_trades_tasks.order_and_tr.static import AssetClass
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.generic.instrument_fallback.static import BestExAssetClassMain
from swarm_tasks.generic.instrument_fallback.static import BestExAssetClassSub
from swarm_tasks.order.transformations.eze.eclipse.static import DATA_SOURCE
from swarm_tasks.order.transformations.eze.eclipse.static import DerivedCols
from swarm_tasks.order.transformations.eze.eclipse.static import PARTIES_CLNT_NORE
from swarm_tasks.order.transformations.eze.eclipse.static import SourceAllocations
from swarm_tasks.order.transformations.eze.eclipse.static import SourceCols
from swarm_tasks.order.transformations.eze.eclipse.static import SourceExecutions
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as ParamsGetTenantLEI
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)


class EzeEclipseOrderTransformations(AbstractOrderTransformations):
    """Transformations class for EzeEclipse Orders"""

    def __init__(self, source_frame: pd.DataFrame, **kwargs):

        source_frame = self.cleanup_unstacked_source_df(source_frame)

        super().__init__(source_frame, **kwargs)

    def process(self):
        self.pre_process()
        self.timestamps_order_received()
        self.timestamps_order_submitted()
        self.timestamps_order_status_updated()
        self.timestamps_trading_date_time()
        self.transaction_details_trading_date_time()
        self.hierarchy()
        self.id()
        self.date()
        self.execution_details_order_status()
        self.execution_details_order_type()
        self.execution_details_outgoing_order_addl_info()
        self.transaction_details_record_type()
        self.execution_details_limit_price()
        self.price_forming_data_price()
        self.transaction_details_price()
        self.transaction_details_price_currency()
        self.transaction_details_price_notation()
        self.order_identifiers_aggregated_order_id_code()
        self.order_identifiers_parent_order_id()
        self.execution_details_trading_capacity()
        self.transaction_details_trading_capacity()
        self.execution_details_buy_sell_indicator()
        self.transaction_details_buy_sell_indicator()
        self.buy_sell()
        self.meta_model()
        self.order_identifiers_order_id_code()
        self.report_details_transaction_ref_no()
        self.order_identifiers_transaction_ref_no()
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()
        self.price_forming_data_initial_quantity()
        self.price_forming_data_traded_quantity()
        self.transaction_details_quantity()
        self.transaction_details_quantity_notation()
        self.market_identifiers_parties()
        self.market_identifiers_instrument()
        self.market_identifiers()
        self.source_key()
        self.source_index()
        self.data_source_name()
        self.post_process()
        return self.target_df

    def _pre_process(self):
        # extract columns from executions
        self._pre_process_for_executions()

        # derive columns for instrumentIdentifiers
        self._pre_process_for_instrument_identifiers()

        # fields for PartyIdentifiers
        self._pre_process_for_party_identifiers()

        # timestamps
        self._pre_process_for_timestamps()

    def _buy_sell(self) -> pd.DataFrame:
        """Populates OrderColumns.BUY_SELL"""
        value_map = {
            BuySellIndicator.BUYI.value: "0",
            BuySellIndicator.SELL.value: "1",
        }

        return pd.concat(
            [
                MapValue.process(
                    source_frame=self.target_df,
                    params=ParamsMapValue(
                        source_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                        target_attribute=add_prefix(
                            ModelPrefix.ORDER, OrderColumns.BUY_SELL
                        ),
                        case_insensitive=True,
                        value_map=value_map,
                    ),
                ),
                MapValue.process(
                    source_frame=self.target_df,
                    params=ParamsMapValue(
                        source_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                        target_attribute=add_prefix(
                            ModelPrefix.ORDER_STATE, OrderColumns.BUY_SELL
                        ),
                        case_insensitive=True,
                        value_map=value_map,
                    ),
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """Returns a data frame containing dataSourceName"""
        return pd.DataFrame(
            data=DATA_SOURCE,
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """Populates OrderColumns.Date"""
        return ConvertDatetime.process(
            self.pre_process_df,
            params=ParamsConvertDatetime(
                source_attribute=DerivedCols.ORDER_SUBMITTED_DT,
                target_attribute=OrderColumns.DATE,
                convert_to=ConvertTo.DATE.value,
            ),
        )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        pass

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR"""
        value_map = {
            "Buy": BuySellIndicator.BUYI.value,
            "Sell": BuySellIndicator.SELL.value,
            "Sell Short": BuySellIndicator.SELL.value,
            "Buy to Cover": BuySellIndicator.BUYI.value,
            "Cover": BuySellIndicator.BUYI.value,
            "Short": BuySellIndicator.SELL.value,
        }
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceCols.SIDE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map=value_map,
            ),
            auditor=self.auditor,
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        pass

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """Populates EXECUTION_DETAILS_LIMIT_PRICE by calling ConvertMinorToMajor"""
        return pd.concat(
            [
                ConvertMinorToMajor.process(
                    source_frame=self.source_frame,
                    params=ParamsConvertMinorToMajor(
                        source_price_attribute=SourceCols.ORDER_LIMIT_PRICE,
                        source_ccy_attribute=SourceCols.LOCAL_CURRENCY,
                        target_price_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE,
                        ),
                        cast_to=CastTo.ABS.value,
                    ),
                ),
                ConvertMinorToMajor.process(
                    source_frame=self.source_frame,
                    params=ParamsConvertMinorToMajor(
                        source_price_attribute=SourceCols.ROUTE_LIMIT_PRICE,
                        source_ccy_attribute=SourceCols.LOCAL_CURRENCY,
                        target_price_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE,
                        ),
                        cast_to=CastTo.ABS.value,
                    ),
                ),
            ],
            axis=1,
        )

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        pass

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        pass

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        pass

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        pass

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        pass

    def _execution_details_order_status(self) -> pd.DataFrame:
        """Populates _order.OrderColumns.EXECUTION_DETAILS_ORDER_STATUS and _orderState.OrderColumns.EXECUTION_DETAILS_ORDER_STATUS"""
        return pd.DataFrame(
            data=[[OrderStatus.NEWO.value, OrderStatus.PARF.value]],
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                ),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                ),
            ],
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """Populates _order.OrderColumns.EXECUTION_DETAILS_ORDER_TYPE"""
        cases = [
            Case(
                query=f"`{SourceCols.LIMIT}`.str.fullmatch('true', case=False, na=False)",
                value=OrderType.LIMIT,
            ),
            Case(
                query=f"~(`{SourceCols.LIMIT}`.str.fullmatch('true', case=False, na=False))",
                value=OrderType.MARKET,
            ),
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                cases=cases,
            ),
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Populates EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceCols.Note,
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
            ),
            auditor=self.auditor,
        )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        pass

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        pass

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_stop_price(self) -> pd.DataFrame:
        pass

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY with "AOTC"
        """
        return pd.DataFrame(
            data=TradingCapacity.AOTC.value,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY],
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        pass

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _financing_type(self) -> pd.DataFrame:
        pass

    def _hierarchy(self) -> pd.DataFrame:
        """Populates _order.OrderColumns.HIERARCHY"""
        cases = [
            Case(
                query=f"`{SourceCols.ORIGINAL_ORDER_ID}` == '{SourceCols.ORDER_ID}'",
                value=HierarchyEnum.PARENT.value,
            ),
            Case(
                query=f"~(`{SourceCols.ORIGINAL_ORDER_ID}` == '{SourceCols.ORDER_ID}')",
                value=HierarchyEnum.CHILD.value,
            ),
            Case(
                query=f"`{SourceCols.ORIGINAL_ORDER_ID}`.isnull()",
                value=HierarchyEnum.STANDALONE.value,
            ),
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.HIERARCHY,
                ),
                cases=cases,
            ),
        )

    def _id(self) -> pd.DataFrame:
        """Populates OrderColumns.ID"""
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.source_frame.loc[:, SourceCols.ORDER_ID].values,
                    index=self.source_frame.index,
                    columns=[add_prefix(ModelPrefix.ORDER, OrderColumns.ID)],
                ),
                pd.DataFrame(
                    data=self.source_frame.loc[:, SourceCols.ORDER_ID].values,
                    index=self.source_frame.index,
                    columns=[add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID)],
                ),
            ],
            axis=1,
        )

    def _is_discretionary(self) -> pd.DataFrame:
        pass

    def _is_iceberg(self):
        pass

    def _is_repo(self):
        pass

    def _is_synthetic(self):
        pass

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        pass

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        pass

    def _jurisdiction_country(self) -> pd.DataFrame:
        pass

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        pass

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_parties() has been called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        temp_df = pd.concat(
            [
                self.target_df.loc[
                    :,
                    [
                        OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                        OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                    ],
                ],
                self.source_frame.loc[
                    :,
                    [
                        SourceCols.ISIN,
                        SourceCols.BLOOMBERG_ID,
                        SourceCols.UNDERLYING_ISIN,
                    ],
                ],
                self.pre_process_df.loc[
                    :,
                    [
                        DerivedCols.ASSET_CLASS,
                        DerivedCols.EXPIRY_DATE,
                        DerivedCols.STRIKE_PRICE,
                        DerivedCols.OPTION_TYPE,
                    ],
                ],
            ],
            axis=1,
        )
        return InstrumentIdentifiers.process(
            source_frame=temp_df,
            params=ParamsInstrumentIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                asset_class_attribute=DerivedCols.ASSET_CLASS,
                currency_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                notional_currency_2_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
                expiry_date_attribute=DerivedCols.EXPIRY_DATE,
                isin_attribute=SourceCols.ISIN,
                option_strike_price_attribute=DerivedCols.STRIKE_PRICE,
                option_type_attribute=DerivedCols.OPTION_TYPE,
                underlying_isin_attribute=SourceCols.UNDERLYING_ISIN,
                venue_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                bbg_figi_id_attribute=SourceCols.BLOOMBERG_ID,
                retain_task_inputs=True,
            ),
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.parties by calling PartyIdentifiers"""
        self.source_frame.loc[
            :, DerivedCols.AGGREGATED_DESTINATION_DISPLAY_NAME_WITH_PREFIX
        ] = self.pre_process_df.loc[
            :, DerivedCols.AGGREGATED_DESTINATION_DISPLAY_NAME_WITH_PREFIX
        ]

        self.pre_process_df[DerivedCols.COUNTERPARTY] = self._get_counterparty()[
            DerivedCols.COUNTERPARTY
        ]
        parties_df = pd.concat(
            [
                self.pre_process_df,
                self.target_df.loc[
                    :, OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
                ],
            ],
            axis=1,
        )
        return GenericOrderPartyIdentifiers.process(
            source_frame=parties_df,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                buy_sell_side_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                executing_entity_identifier=DerivedCols.TENANT_LEI,
                trader_identifier=DerivedCols.AGGREGATED_TRADER_WITH_PREFIX,
                client_identifier=DerivedCols.AGGREGATED_PORTFOLIO_NAME_WITH_PREFIX,
                counterparty_identifier=DerivedCols.COUNTERPARTY,
                buyer_identifier=DerivedCols.TENANT_LEI,
                seller_identifier=DerivedCols.COUNTERPARTY,
                execution_within_firm_identifier=DerivedCols.EXECUTION_WITHIN_FIRM_WITH_PREFIX,
                investment_decision_within_firm_identifier=DerivedCols.AGGREGATED_INVESTMENT_DECISION_WITHIN_FIRM_WITH_PREFIX,
                use_buy_mask_for_buyer_seller=True,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """Returns a data frame containing _order.__meta_model__ and _orderState.__meta_model__.
        The columns are populated with the static values Order and OrderState respectively"""
        return pd.DataFrame(
            data=[["Order", "OrderState"]],
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.META_MODEL,
                ),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.META_MODEL,
                ),
            ],
        )

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        pass

    def _order_class(self) -> pd.DataFrame:
        pass

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """Populates OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceCols.ORIGINAL_ORDER_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID],
        )

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        pass

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """Populates OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceCols.ORDER_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
        )

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        """Populates OrderColumns.ORDER_IDENTIFIERS_PARENT_ORDER_ID"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceCols.ORIGINAL_ORDER_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_PARENT_ORDER_ID],
        )

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a dataframe containing _orderState.orderIdentifiers.transactionRefNo"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceCols.ROUTE_ID].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                )
            ],
        )

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """Populates OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceCols.QUANTITY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY],
        )

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price(self) -> pd.DataFrame:
        """Populates PRICE_FORMING_DATA_PRICE by calling ConvertMinorToMajor on SourceCols.EXECUTION_PRICE"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_price_attribute=SourceCols.EXECUTION_PRICE,
                source_ccy_attribute=SourceCols.LOCAL_CURRENCY,
                target_price_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                ),
                cast_to=CastTo.ABS.value,
            ),
        )

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        pass

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """Populates _order.OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[
                :, DerivedCols.AGGREGATED_QTY_EXECUTIONS
            ].values,
            index=self.pre_process_df.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                )
            ],
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a dataframe containing _orderState.reportDetails.transactionRefNo"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceCols.ROUTE_ID].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO,
                )
            ],
        )

    def _source_index(self) -> pd.DataFrame:
        """
        Static value: index from the source_frame
        """
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceKey column from the SWARM_FILE_URL"""
        return pd.DataFrame(
            data=os.getenv("SWARM_FILE_URL"),
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_KEY],
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_order_received(self) -> pd.DataFrame:
        """Populates OrderColumns.TIMESTAMPS_ORDER_RECEIVED"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceCols.CREATE_DATETIME,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                convert_to=ConvertTo.DATETIME.value,
            ),
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """Populates OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED"""
        order_ts_updated = pd.DataFrame(
            data=self.pre_process_df.loc[:, SourceCols.LAST_MODIFIED_DATE_TIME].values,
            index=self.pre_process_df.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
                )
            ],
        )
        order_state_ts_updated = pd.DataFrame(
            data=self.pre_process_df.loc[:, DerivedCols.EXEC_DATE_TIME].values,
            index=self.pre_process_df.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
                )
            ],
        )
        return pd.concat([order_ts_updated, order_state_ts_updated], axis=1)

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Populates OrderColumns.TIMESTAMPS_ORDER_SUBMITTED"""
        order_ts_submitted = ConvertDatetime.process(
            self.pre_process_df,
            params=ParamsConvertDatetime(
                source_attribute=DerivedCols.ORDER_SUBMITTED_DT,
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                ),
                convert_to=ConvertTo.DATETIME.value,
            ),
        )
        order_state_ts_submitted = pd.DataFrame(
            data=self.pre_process_df.loc[:, SourceExecutions.CREATE_DATETIME].values,
            index=self.pre_process_df.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                )
            ],
        )
        return pd.concat([order_ts_submitted, order_state_ts_submitted], axis=1)

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """Populates _orderState.OrderColumns.TIMESTAMPS_TRADING_DATE_TIME"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, DerivedCols.EXEC_DATE_TIME].values,
            index=self.pre_process_df.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
                )
            ],
        )

    def _timestamps_validity_period(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        pass

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Populates TRANSACTION_DETAILS_BUY_SELL_INDICATOR"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        pass

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        pass

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        pass

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_price(self) -> pd.DataFrame:
        """Populates OrderColumns.PRICE_FORMING_DATA_PRICE"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :,
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                ),
            ].values,
            index=self.target_df.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TRANSACTION_DETAILS_PRICE,
                )
            ],
        )

    def _transaction_details_price_average(self) -> pd.DataFrame:
        pass

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """
        Populates from SourceCols.LOCAL_CURRENCY and converts to Major currency if applicable
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceCols.LOCAL_CURRENCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
            ),
        )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION"""
        cases = [
            Case(
                query=f"`{SourceCols.ASSET_CLASS}`.str.match('Fixed Income|Corp Bond', case=False, na=False)",
                value=PriceNotation.PERC.value,
            ),
            Case(
                query=f"~(`{SourceCols.ASSET_CLASS}`.str.match('Fixed Income|Corp Bond', case=False, na=False))",
                value=PriceNotation.MONE.value,
            ),
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION,
                ),
                cases=cases,
            ),
        )

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        pass

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        pass

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_QUANTITY"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[
                :, DerivedCols.AGGREGATED_QTY_EXECUTIONS
            ].values,
            index=self.pre_process_df.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY,
                )
            ],
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION"""
        return pd.DataFrame(
            data=QuantityNotation.UNIT.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION],
        )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE with static value "Market Side"
        """
        return pd.DataFrame(
            data=OrderRecordType.MARKET_SIDE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE],
        )

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        pass

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY with "AOTC"
        """
        return pd.DataFrame(
            data=TradingCapacity.AOTC.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """Populates _orderState.OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, DerivedCols.EXEC_DATE_TIME].values,
            index=self.pre_process_df.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
                )
            ],
        )

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE"""
        cases = [
            Case(
                query=f"`{DerivedCols.EXEC_VENUE}`.notnull()",
                attribute=DerivedCols.EXEC_VENUE,
            ),
            Case(
                query=f"~(`{DerivedCols.EXEC_VENUE}`.notnull())",
                value="XOFF",
            ),
        ]
        return MapConditional.process(
            source_frame=self.pre_process_df,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                cases=cases,
            ),
        )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        pass

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_venue(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_VENUE"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_VENUE],
        )

    def _post_process(self):
        """Populates temorary columns which will be used in downstream tasks. For e.g. parties_fallback etc"""

        # Fields for parties_fallback
        self.target_df.loc[
            :, DerivedCols.AGGREGATED_PORTFOLIO_NAME
        ] = self.pre_process_df.loc[:, DerivedCols.AGGREGATED_PORTFOLIO_NAME]
        self.target_df.loc[:, DerivedCols.AGGREGATED_TRADER] = self.pre_process_df.loc[
            :, DerivedCols.AGGREGATED_TRADER
        ]
        self.target_df.loc[
            :, DerivedCols.EXECUTION_WITHIN_FIRM
        ] = self.pre_process_df.loc[:, DerivedCols.EXECUTION_WITHIN_FIRM]
        self.target_df.loc[
            :, DerivedCols.AGGREGATED_DESTINATION_DISPLAY_NAME
        ] = self.pre_process_df.loc[:, DerivedCols.AGGREGATED_DESTINATION_DISPLAY_NAME]
        # Remove the `id:` prefix from the counterparty
        self.target_df.loc[:, DerivedCols.COUNTERPARTY] = self.pre_process_df.loc[
            :, DerivedCols.COUNTERPARTY
        ].str.replace(
            rf"^({PartyPrefix.ID})",
            "",
            regex=True,
        )
        self.target_df.loc[
            :, DerivedCols.AGGREGATED_INVESTMENT_DECISION_WITHIN_FIRM
        ] = self.pre_process_df.loc[
            :, DerivedCols.AGGREGATED_INVESTMENT_DECISION_WITHIN_FIRM
        ]

        # Fields for Instrument Fallback
        self.target_df.loc[:, DerivedCols.INST_FB_IS_CREATED_THRU_FB] = True
        self.target_df.loc[:, SourceCols.PRICE_MULTIPLIER] = self.source_frame.loc[
            :, SourceCols.PRICE_MULTIPLIER
        ]
        self.target_df.loc[:, DerivedCols.INST_FB_EXCH_ROOT_SYMBOL] = (
            self.source_frame.loc[:, SourceCols.UNDERLYING_SYMBOL]
            .fillna(self.source_frame.loc[:, SourceCols.SYMBOL])
            .fillna(self.source_frame.loc[:, SourceCols.OCC_CODE])
            .str.split(" ")
            .str[0]
            .str.replace(r"[^\w\s]", "", regex=True)
        )
        self.target_df.loc[:, DerivedCols.INST_FB_INST_FULL_NAME] = (
            self.source_frame.loc[:, SourceCols.SECURITY_NAME]
            .dropna()
            .fillna(self.source_frame.loc[:, SourceCols.ISSUER].dropna())
        )
        self.target_df.loc[:, DerivedCols.INST_FB_EXT_BEST_EX_ASSET_CLS_MAIN] = (
            self.source_frame.loc[:, SourceCols.ASSET_CLASS]
            .str.upper()
            .map(
                {
                    "EQUITY": BestExAssetClassMain.EQUITY,
                    "OPTION": BestExAssetClassMain.EQUITY_DERIVATIVES,
                    "OPTIONS": BestExAssetClassMain.EQUITY_DERIVATIVES,
                    "FUTURE": BestExAssetClassMain.EQUITY_DERIVATIVES,
                    "FUTURES": BestExAssetClassMain.EQUITY_DERIVATIVES,
                    "FX": BestExAssetClassMain.CURRENCY_DERIVATIVES,
                    "FIXED INCOME": BestExAssetClassMain.DEBT_INSTRUMENTS,
                    "CORP BOND": BestExAssetClassMain.DEBT_INSTRUMENTS,
                    "MONEY MARKET": BestExAssetClassMain.OTHER_INSTRUMENTS,
                    "CASH": BestExAssetClassMain.OTHER_INSTRUMENTS,
                    "RIGHT/WARRANT/OPTION": BestExAssetClassMain.EQUITY_DERIVATIVES,
                }
            )
        )
        self.target_df.loc[:, DerivedCols.INST_FB_EXT_BEST_EX_ASSET_CLS_SUB] = (
            self.source_frame.loc[:, SourceCols.ASSET_TYPE]
            .str.strip()
            .str.upper()
            .map(
                {
                    "LISTED EQUITY OPTIONS": BestExAssetClassSub.FUTURE_OPTIONS_ADMITTED,
                    "OTC OPTIONS": BestExAssetClassSub.FUTURE_OPTIONS_ADMITTED,
                    "COMMODITY FUTURES": BestExAssetClassSub.FUTURE_OPTIONS_ADMITTED,
                    "Index Futures": BestExAssetClassSub.FUTURE_OPTIONS_ADMITTED,
                    "SINGLE STOCK FUTURES": BestExAssetClassSub.FUTURE_OPTIONS_ADMITTED,
                    "FOREIGN EXCHANGE": BestExAssetClassSub.SWAPS_FORWARDS_CURRENCY_DERIVATIVES,
                    "AGENCY BOND": BestExAssetClassSub.BONDS,
                    "CONVERTIBLE": BestExAssetClassSub.BONDS,
                    "CORP BOND": BestExAssetClassSub.BONDS,
                    "GOVERNMENT BOND": BestExAssetClassSub.BONDS,
                    "MUNICIPAL BOND": BestExAssetClassSub.BONDS,
                }
            )
        )
        self.target_df.loc[:, DerivedCols.TENANT_LEI] = self.pre_process_df.loc[
            :, DerivedCols.TENANT_LEI
        ]

        # instrumentIdCode
        self.target_df.loc[:, DerivedCols.INST_FB_INST_ID_CODE] = self.source_frame.loc[
            :, SourceCols.ISIN
        ]

        # instrumentUniqueIdentifier
        self.target_df.loc[:, DerivedCols.INST_FB_INST_UNIQUE_ID] = (
            self.source_frame.loc[:, SourceCols.ISIN]
            .fillna(self.source_frame.loc[:, SourceCols.OCC_CODE])
            .fillna(self.source_frame.loc[:, SourceCols.SYMBOL])
        )

        # NEW_O_COL_IN_FILE for RemoveDupNEWO task
        self.target_df.loc[:, DerivedCols.NEW_O_COL_IN_FILE] = False

        # set a flag to denote the rows for which only newo needs to be created
        only_newo_mask = self.source_frame[SourceCols.EXECUTIONS].isnull()
        self.target_df.loc[:, DerivedCols.ONLY_NEWO] = False
        if only_newo_mask.any():
            self.target_df.loc[only_newo_mask, DerivedCols.ONLY_NEWO] = True

    def _pre_process_for_instrument_identifiers(self):
        """Populate temporary columns required for instrumentIdentifiers"""
        occ_code_not_na_mask = self.source_frame[SourceCols.OCC_CODE].notnull()
        self.pre_process_df.loc[occ_code_not_na_mask, DerivedCols.OPTION_TYPE] = (
            self.source_frame.loc[occ_code_not_na_mask, SourceCols.OCC_CODE]
            .str[12]
            .str.upper()
            .map({"C": OptionType.CALL.value, "P": "PUT"})
        )
        self.pre_process_df.loc[
            occ_code_not_na_mask, DerivedCols.STRIKE_PRICE_OCC_CODE
        ] = (
            self.source_frame.loc[occ_code_not_na_mask, SourceCols.OCC_CODE]
            .str[-8:]
            .astype(float)
            / 1000
        )
        self.pre_process_df.loc[
            occ_code_not_na_mask, DerivedCols.STRIKE_PRICE
        ] = self.source_frame.loc[occ_code_not_na_mask, SourceCols.STRIKE_PRICE].fillna(
            self.pre_process_df.loc[
                occ_code_not_na_mask, DerivedCols.STRIKE_PRICE_OCC_CODE
            ]
        )

        self.pre_process_df.loc[
            :, DerivedCols.EXPIRY_DATE_EXP_DATE
        ] = ConvertDatetime.process(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceCols.EXPIRATION_DATE,
                target_attribute=DerivedCols.EXPIRY_DATE_EXP_DATE,
                convert_to=ConvertTo.DATE.value,
            ),
        )

        self.pre_process_df.loc[
            occ_code_not_na_mask, DerivedCols.EXPIRY_DATE_OCC_CODE
        ] = self.source_frame.loc[occ_code_not_na_mask, SourceCols.OCC_CODE].str[6:12]
        self.pre_process_df.loc[
            occ_code_not_na_mask, DerivedCols.EXPIRY_DATE_OCC_CODE
        ] = ConvertDatetime.process(
            self.pre_process_df.loc[occ_code_not_na_mask],
            params=ParamsConvertDatetime(
                source_attribute=DerivedCols.EXPIRY_DATE_OCC_CODE,
                target_attribute=DerivedCols.EXPIRY_DATE_OCC_CODE,
                convert_to=ConvertTo.DATE.value,
            ),
        )
        self.pre_process_df.loc[:, DerivedCols.EXPIRY_DATE] = self.pre_process_df.loc[
            :, DerivedCols.EXPIRY_DATE_EXP_DATE
        ].fillna(self.pre_process_df.loc[:, DerivedCols.EXPIRY_DATE_OCC_CODE])

        self.pre_process_df.loc[:, DerivedCols.ASSET_CLASS] = (
            self.source_frame.loc[:, SourceCols.ASSET_CLASS]
            .str.upper()
            .map(
                {
                    "EQUITY": AssetClass.EQUITY,
                    "OPTIONS": AssetClass.OPTION,
                    "OPTION": AssetClass.OPTION,
                    "FUTURES": AssetClass.FUTURE,
                    "FUTURE": AssetClass.FUTURE,
                    "FIXED INCOME": AssetClass.BOND,
                    "CORP BOND": AssetClass.BOND,
                    "RIGHT/WARRANT/OPTION": AssetClass.OPTION,
                }
            )
        )

    def _pre_process_for_party_identifiers(self):
        """Populate temporary columns required for partyIdentifiers"""
        self.pre_process_df.loc[:, DerivedCols.TENANT_LEI] = GetTenantLEI.process(
            source_frame=self.pre_process_df,
            params=ParamsGetTenantLEI(
                target_column_prefix=PartyPrefix.LEI,
                target_lei_column=DerivedCols.TENANT_LEI,
            ),
        )

        # aggregate DestinationDisplayName
        self.pre_process_df.loc[:, DerivedCols.SOURCE_INDEX] = self.source_frame.loc[
            :, DerivedCols.SOURCE_INDEX
        ]
        agg_series = (
            self.source_frame.groupby(DerivedCols.SOURCE_INDEX)[
                SourceCols.DESTINATION_DISPLAY_NAME
            ]
            .agg(list)
            .apply(lambda x: ",".join([y for y in x if not pd.isna(y)]))
            .replace("", pd.NA)
        )
        self.pre_process_df.loc[
            :, DerivedCols.AGGREGATED_DESTINATION_DISPLAY_NAME
        ] = pd.merge(self.pre_process_df, agg_series, on=DerivedCols.SOURCE_INDEX)[
            SourceCols.DESTINATION_DISPLAY_NAME
        ].apply(
            lambda x: ",".join(dict.fromkeys(x.split(",")))
            if pd.notna(x)
            else pd.NA  # Remove duplicates
        )

        self.pre_process_df.loc[
            :, DerivedCols.AGGREGATED_DESTINATION_DISPLAY_NAME_WITH_PREFIX
        ] = (
            PartyPrefix.ID
            + self.pre_process_df.loc[
                :, DerivedCols.AGGREGATED_DESTINATION_DISPLAY_NAME
            ]
        )

        # aggregate Trader
        agg_series = (
            self.source_frame.groupby(DerivedCols.SOURCE_INDEX)[SourceCols.TRADER]
            .agg(list)
            .apply(lambda x: ",".join([y for y in x if not pd.isna(y)]))
            .replace("", pd.NA)
        )

        self.pre_process_df.loc[:, DerivedCols.AGGREGATED_TRADER] = pd.merge(
            self.pre_process_df, agg_series, on=DerivedCols.SOURCE_INDEX
        )[SourceCols.TRADER].apply(
            lambda x: ",".join(dict.fromkeys(x.split(",")))
            if pd.notna(x)
            else pd.NA  # Remove duplicates
        )

        self.pre_process_df.loc[:, DerivedCols.AGGREGATED_TRADER_WITH_PREFIX] = (
            PartyPrefix.ID + self.pre_process_df.loc[:, DerivedCols.AGGREGATED_TRADER]
        )

        # Extract Allocations
        allocations_df = (
            self.source_frame.loc[:, SourceCols.ALLOCATIONS]
            .apply(lambda x: literal_eval(x))
            .explode()
            .apply(pd.Series)
        )

        # Aggregate PortFolioName
        self.pre_process_df.loc[:, DerivedCols.AGGREGATED_PORTFOLIO_NAME] = (
            allocations_df.groupby(DerivedCols.SWARM_RAW_INDEX)[
                SourceAllocations.PORTFOLIO_NAME
            ]
            .agg(set)
            .apply(lambda x: ",".join([y for y in x if not pd.isna(y)]))
            .replace("", pd.NA)
        )
        self.pre_process_df.loc[
            :, DerivedCols.AGGREGATED_PORTFOLIO_NAME_WITH_PREFIX
        ] = (
            PartyPrefix.ID
            + self.pre_process_df.loc[:, DerivedCols.AGGREGATED_PORTFOLIO_NAME]
        )

        # Aggregate InvestmentDecisionWithinFirm
        if SourceAllocations.MANAGER in allocations_df.columns:
            self.pre_process_df.loc[
                :, DerivedCols.AGGREGATED_INVESTMENT_DECISION_WITHIN_FIRM
            ] = (
                allocations_df.groupby(DerivedCols.SWARM_RAW_INDEX)[
                    SourceAllocations.MANAGER
                ]
                .agg(set)
                .apply(lambda x: ",".join([y for y in x if not pd.isna(y)]))
                .replace("", pd.NA)
            ).fillna(
                self.pre_process_df.loc[:, DerivedCols.AGGREGATED_TRADER]
            )
        else:
            self.pre_process_df.loc[
            :, DerivedCols.AGGREGATED_INVESTMENT_DECISION_WITHIN_FIRM
            ] = self.pre_process_df.loc[:, DerivedCols.AGGREGATED_TRADER]

        self.pre_process_df.loc[
            :, DerivedCols.AGGREGATED_INVESTMENT_DECISION_WITHIN_FIRM_WITH_PREFIX
        ] = (
            PartyPrefix.ID
            + self.pre_process_df.loc[
                :, DerivedCols.AGGREGATED_INVESTMENT_DECISION_WITHIN_FIRM
            ]
        )

        # Execution Within Firm
        self.pre_process_df.loc[
            :, DerivedCols.EXECUTION_WITHIN_FIRM_WITH_PREFIX
        ] = self.pre_process_df.loc[
            :, DerivedCols.AGGREGATED_TRADER_WITH_PREFIX
        ].fillna(
            PARTIES_CLNT_NORE
        )
        self.pre_process_df.loc[
            :, DerivedCols.EXECUTION_WITHIN_FIRM
        ] = self.pre_process_df.loc[:, DerivedCols.AGGREGATED_TRADER].fillna(
            PARTIES_CLNT_NORE
        )

    def _pre_process_for_timestamps(self):
        """Populate temporary columns required for timestamp fields"""
        self.source_frame.loc[:, SourceExecutions.CREATE_DATETIME] = pd.to_datetime(
            self.source_frame[SourceExecutions.CREATE_DATETIME]
        )
        # Take earliest CREATE_DATETIME from all routes
        create_dt_not_na_mask = self.source_frame[
            SourceExecutions.CREATE_DATETIME
        ].notnull()
        self.pre_process_df[DerivedCols.CREATE_DATETIME] = (
            self.source_frame.loc[create_dt_not_na_mask]
            .groupby("__source_index__")[SourceExecutions.CREATE_DATETIME]
            .transform(min)
        )
        self.pre_process_df[DerivedCols.CREATE_DATETIME] = ConvertDatetime.process(
            source_frame=self.pre_process_df,
            params=ParamsConvertDatetime(
                source_attribute=DerivedCols.CREATE_DATETIME,
                target_attribute=DerivedCols.CREATE_DATETIME,
                convert_to=ConvertTo.DATETIME,
            ),
        )

        # Order Submitted DateTime
        self.pre_process_df.loc[
            :, DerivedCols.ORDER_SUBMITTED_DT
        ] = self.source_frame.loc[:, SourceExecutions.CREATE_DATETIME].fillna(
            self.source_frame.loc[:, SourceCols.CREATE_DATETIME]
        )

        # LAST_MODIFIED_DATE_TIME
        self.pre_process_df[
            SourceCols.LAST_MODIFIED_DATE_TIME
        ] = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceCols.LAST_MODIFIED_DATE_TIME,
                target_attribute=SourceCols.LAST_MODIFIED_DATE_TIME,
                convert_to=ConvertTo.DATETIME,
            ),
        )

        # Route CREATE_DATETIME
        self.pre_process_df[SourceExecutions.CREATE_DATETIME] = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceExecutions.CREATE_DATETIME,
                target_attribute=SourceExecutions.CREATE_DATETIME,
                convert_to=ConvertTo.DATETIME,
            ),
        )

    def _pre_process_for_executions(self):
        """Extract executions from the source frame and populate required columns"""

        self.source_frame.loc[:, SourceCols.EXECUTIONS] = self.source_frame.loc[
            :, SourceCols.EXECUTIONS
        ].map(lambda x: literal_eval(x) if not pd.isna(x) else pd.NA)
        executions_df = (
            self.source_frame.loc[:, SourceCols.EXECUTIONS].explode().apply(pd.Series)
        )

        for column in [
            SourceExecutions.EXECUTION_ID,
            SourceExecutions.EXEC_DATE_TIME,
            SourceExecutions.QUANTITY,
            SourceExecutions.VENUE,
        ]:
            if column not in executions_df.columns:
                executions_df.loc[:, column] = pd.NA

        executions_df.loc[:, SourceExecutions.EXEC_DATE_TIME] = pd.to_datetime(
            executions_df[SourceExecutions.EXEC_DATE_TIME]
        )
        self.source_frame.loc[:, SourceCols.CREATE_DATETIME] = pd.to_datetime(
            self.source_frame[SourceCols.CREATE_DATETIME]
        )
        if not executions_df.dropna(how="all").empty:

            self.pre_process_df.loc[
                :, DerivedCols.AGGREGATED_QTY_EXECUTIONS
            ] = executions_df.groupby(DerivedCols.SWARM_RAW_INDEX)[
                SourceExecutions.QUANTITY
            ].sum()
            exec_dt_not_na_mask = executions_df[
                SourceExecutions.EXEC_DATE_TIME
            ].notnull()
            self.pre_process_df.loc[:, DerivedCols.EXEC_DATE_TIME] = (
                executions_df.loc[
                    executions_df.loc[exec_dt_not_na_mask]
                    .groupby(executions_df.loc[exec_dt_not_na_mask].index)[
                        SourceExecutions.EXEC_DATE_TIME
                    ]
                    .idxmin()
                ]
                .groupby(DerivedCols.SWARM_RAW_INDEX)
                .first()[SourceExecutions.EXEC_DATE_TIME]
            )
            self.pre_process_df.loc[
                :, DerivedCols.EXEC_DATE_TIME
            ] = ConvertDatetime.process(
                source_frame=self.pre_process_df,
                params=ParamsConvertDatetime(
                    source_attribute=DerivedCols.EXEC_DATE_TIME,
                    target_attribute=DerivedCols.EXEC_DATE_TIME,
                    convert_to=ConvertTo.DATETIME,
                ),
            )

            # Group by Venue for all executions and take the most frequent one
            venue_not_na_mask = executions_df[SourceExecutions.VENUE].notnull()
            self.pre_process_df.loc[:, DerivedCols.EXEC_VENUE] = pd.NA
            if venue_not_na_mask.any():
                self.pre_process_df.loc[:, DerivedCols.EXEC_VENUE] = (
                    executions_df.groupby(DerivedCols.SWARM_RAW_INDEX)[
                        SourceExecutions.VENUE
                    ]
                    .agg(pd.Series.mode)
                    .apply(
                        lambda x: x[0]
                        if isinstance(x, numpy.ndarray) and len(x) > 0
                        else (
                            x
                            if pd.notna(x) and not isinstance(x, numpy.ndarray)
                            else pd.NA
                        )
                    )
                )
        else:
            self.pre_process_df.loc[
                :,
                [
                    DerivedCols.AGGREGATED_QTY_EXECUTIONS,
                    DerivedCols.EXEC_DATE_TIME,
                    DerivedCols.EXEC_VENUE,
                ],
            ] = pd.NA

    @staticmethod
    def cleanup_unstacked_source_df(source_frame):
        """
        If no rows have Routes, the results that output from Nested JSON File Splitter
            come with columns with different names than expected.
        This method corrects those differences so that the dataframe can be processed
            correctly.
        """

        unstack_columns: List[str] = [
            SourceCols.CREATE_DATETIME,
            SourceCols.ORDER_ID,
            SourceCols.LAST_MODIFIED_DATE_TIME,
            SourceCols.LIMIT,
            SourceCols.ORDER_LIMIT_PRICE,
            SourceCols.QUANTITY,
        ]

        non_unstack_columns = [column[:-2] for column in unstack_columns]

        for unstack_column, non_unstack_column in zip(
            unstack_columns, non_unstack_columns
        ):
            not_null_mask = source_frame.loc[:, non_unstack_column].notnull()
            source_frame.loc[not_null_mask, unstack_column] = source_frame.loc[
                not_null_mask, non_unstack_column
            ]

        source_frame = source_frame.drop(non_unstack_columns, axis=1)

        if SourceExecutions.CREATE_DATETIME not in source_frame.columns:
            source_frame.loc[:, SourceExecutions.CREATE_DATETIME] = pd.NA

        if DerivedCols.SOURCE_INDEX not in source_frame.columns:
            source_frame.loc[:, DerivedCols.SOURCE_INDEX] = source_frame.index.values

        return source_frame

    def _get_counterparty(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.pre_process_df.loc[
                :, DerivedCols.AGGREGATED_DESTINATION_DISPLAY_NAME_WITH_PREFIX
            ].values,
            index=self.pre_process_df.index,
            columns=[DerivedCols.COUNTERPARTY],
        )

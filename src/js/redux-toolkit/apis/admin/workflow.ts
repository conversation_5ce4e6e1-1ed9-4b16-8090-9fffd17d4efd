/* eslint-disable iris/no-horizontal-module-import */
import omit from 'lodash/omit';
import {
  ADMIN_WORKFLOW_ENDPOINT,
  getWorkflowByModuleEndpoint,
  APPLY_WORKFLOW_TO_USERS_ENDPOINT,
  getSingleWorkflowEndpoint,
  getAppliedNotAppliedUsersEndpoint,
} from 'js/constants/endpoints';

import {
  Transition,
  TransitionRestrictionType,
} from 'js/view/modules/admin/components/workflow/workflow-graph/edges/transition-edge';
import { TransitionStateType } from 'js/view/modules/admin/components/workflow/workflow-graph/nodes/transition-node';

import { RecordsResponse, PaginationParams, FilterPaginationParams } from 'js/types/api.types';
import { PROFILE_TAG } from '../account/profile';

import apiV4 from '../rtk-apiV4';

const WORKFLOW_TAG = 'workflow';

const RESOLVED_SUB_STATUSES = [
  TransitionStateType.RESOLVED_WITH_BREACH,
  TransitionStateType.RESOLVED_WITH_DISMISSAL,
  TransitionStateType.RESOLVED_WITH_INVESTIGATION,
  TransitionStateType.RESOLVED_WITH_INVESTIGATION_WITH_BREACH,
];

// TODO: Add id in future
type Workflow = {
  id?: string;
  description: string;
  module: string;
  workflowName: string;
  templateId: string;
  templateName: string;
  isDefault: boolean;
  isSteelEyeProvided: boolean;
  transitions: Transition[];
  userCount?: number;
  updatedDateTime?: string;
  createdDateTime?: string;
  createdBy?: string;
  updatedBy?: string;
};

type WorkflowPutPayload = {
  workflowName: string;
  description: string;
  isDefault?: boolean;
  transitions: Transition[];
};

const getTransitionsTransformed = (transitions: Transition[]) => {
  // Remove transitionCondition if the transition is not conditional
  const cleanedTransitions = transitions.map(t => {
    if (t.transitionRestrictionType === TransitionRestrictionType.CONDITIONAL) {
      return t;
    } else {
      return omit(t, 'transitionCondition');
    }
  });

  return cleanedTransitions.reduce((acc, transition) => {
    if (transition.toStatus === TransitionStateType.RESOLVED) {
      const childTransitions = cleanedTransitions.filter(
        ({ fromStatus, toStatus }) =>
          fromStatus === transition.fromStatus && RESOLVED_SUB_STATUSES.includes(toStatus)
      );
      return [
        ...acc,
        {
          ...transition,
          childTransitions,
        },
      ];
    }

    if (transition.fromStatus === TransitionStateType.RESOLVED) {
      const childTransitions = cleanedTransitions.filter(
        ({ fromStatus, toStatus }) =>
          toStatus === TransitionStateType.UNRESOLVED && RESOLVED_SUB_STATUSES.includes(fromStatus)
      );

      return [
        ...acc,
        {
          ...transition,
          childTransitions,
        },
      ];
    }

    if (
      RESOLVED_SUB_STATUSES.includes(transition.toStatus) ||
      RESOLVED_SUB_STATUSES.includes(transition.fromStatus)
    ) {
      return acc;
    }

    return [...acc, transition];
  }, []);
};

const extendedApi = apiV4
  .enhanceEndpoints({
    addTagTypes: [WORKFLOW_TAG, PROFILE_TAG],
  })
  .injectEndpoints({
    endpoints: builder => ({
      getWorkflows: builder.query<
        RecordsResponse<Workflow>,
        FilterPaginationParams & { module: string; includeTransitions?: boolean }
      >({
        query: ({ module, search = '', includeTransitions = false, f = '' }) => ({
          url: getWorkflowByModuleEndpoint(module),
          method: 'GET',
          params: {
            sort: 'isDefault:desc', // This is so that the default workflow is always on top
            search,
            includeTransitions,
            f,
          },
        }),
        providesTags: workflow =>
          workflow?.results?.length
            ? [
                ...workflow.results.map(({ id }) => ({ type: WORKFLOW_TAG, id }) as const),
                { type: WORKFLOW_TAG, id: 'LIST_WORKFLOW' },
              ]
            : [{ type: WORKFLOW_TAG, id: 'LIST_WORKFLOW' }],
      }),
      postWorkflow: builder.mutation<Workflow, Workflow>({
        query: body => ({
          url: ADMIN_WORKFLOW_ENDPOINT,
          body,
          method: 'POST',
        }),
        invalidatesTags: [{ type: WORKFLOW_TAG, id: 'LIST_WORKFLOW' }],
      }),
      applyWorkflowToUsers: builder.mutation<
        Workflow,
        {
          assignWorkflows?: { [key: string]: string[] };
          replace?: string;
          deleting?: string;
        }
      >({
        query: ({ assignWorkflows, replace, deleting }) => ({
          url: APPLY_WORKFLOW_TO_USERS_ENDPOINT,
          body: {
            // send assignWorkflows only if it is not empty
            ...(Object.keys(assignWorkflows).length && { assignWorkflows }),
            replace,
            deleting,
          },
          method: 'POST',
        }),
        invalidatesTags: (result, error, { assignWorkflows }) => [
          { type: WORKFLOW_TAG, id: 'APPLIED' },
          { type: WORKFLOW_TAG, id: 'NOT_APPLIED' },
          { type: WORKFLOW_TAG, id: 'LIST_WORKFLOW' },
          ...(assignWorkflows
            ? Object.values(assignWorkflows)
                .flat()
                .map(userId => ({ type: 'Profile' as const, id: userId }))
            : []),
        ],
      }),
      getSingleWorkflow: builder.query<Workflow, { workflowId: string }>({
        query: ({ workflowId }) => ({
          url: getSingleWorkflowEndpoint(workflowId),
          method: 'GET',
        }),
        transformResponse: (response: Workflow) => {
          return {
            ...response,
            transitions: getTransitionsTransformed(response.transitions),
          };
        },
        providesTags: result => (result ? [{ type: WORKFLOW_TAG, id: result.id }] : []),
      }),
      getAppliedNotAppliedUsers: builder.query<
        RecordsResponse,
        { applied: boolean; workflowId: string; f?: string } & PaginationParams
      >({
        query: ({ workflowId, f, ...query }) => ({
          url: getAppliedNotAppliedUsersEndpoint(workflowId),
          method: 'GET',
          params: {
            f: f || '',
            ...query,
          },
        }),
        providesTags: (result, error, { applied }) => [
          { type: WORKFLOW_TAG, id: applied ? 'APPLIED' : 'NOT_APPLIED' },
        ],
      }),
      deleteWorkflow: builder.mutation<{ status: string }, { workflowId: string }>({
        query: ({ workflowId }) => ({
          url: getSingleWorkflowEndpoint(workflowId),
          method: 'DELETE',
        }),
        invalidatesTags: [WORKFLOW_TAG],
      }),
      putSingleWorkflow: builder.mutation<
        { status: string },
        { workflowId: string; body: WorkflowPutPayload }
      >({
        query: ({ workflowId, body }) => ({
          url: getSingleWorkflowEndpoint(workflowId),
          method: 'PUT',
          body,
        }),
        invalidatesTags: (result, error, { workflowId, body: { isDefault } }) => [
          { type: WORKFLOW_TAG, id: workflowId },
          { type: WORKFLOW_TAG, id: 'LIST_WORKFLOW' },
          isDefault ? { type: WORKFLOW_TAG, id: 'APPLIED' } : 'InvalidateNothing',
          isDefault ? { type: WORKFLOW_TAG, id: 'NOT_APPLIED' } : 'InvalidateNothing',
        ],
      }),
    }),
    overrideExisting: true,
  });

export const {
  usePostWorkflowMutation,
  useGetWorkflowsQuery,
  useApplyWorkflowToUsersMutation,
  useGetSingleWorkflowQuery,
  useGetAppliedNotAppliedUsersQuery,
  useDeleteWorkflowMutation,
  usePutSingleWorkflowMutation,
} = extendedApi;

export { Workflow };

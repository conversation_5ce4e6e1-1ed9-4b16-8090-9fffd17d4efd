import includes from 'lodash/includes';
import isEmpty from 'lodash/isEmpty';
import { defineMessages } from 'react-intl';

import { TENANT_CONFIGURATION_ENDPOINT, TENANT_DATA_SOURCES_ENDPOINT } from 'constants/endpoints';
import { ALL_SUBSCRIBED_MARKET_ABUSE_REPORTS } from 'constants/tenant';
import { DEFAULT_TAKE } from 'constants/search';
import {
  GET_TENANT_DATA_SOURCES_LOADING,
  GET_TENANT_DATA_SOURCES_FULFILLED,
  GET_TENANT_CONFIGURATIONS_LOADING,
  GET_TENANT_CONFIGURATIONS_FULFILLED,
} from 'actions/auth/tenant-actions-constants';

import { addErrorNotification } from 'actions/notification';

import apiV21 from 'util/api/apiV2.1';

import * as modelsEnums from '@steeleye/schema/models/enums';

const TENANT_MESSAGES = defineMessages({
  configNotFound: {
    defaultMessage:
      'Tenant Permissions settings is missing. Please contact our customer support to get access to the subscribed modules and reports.',
    id: 'actions.tenant.configNotFound',
  },
  unableToLoadConfig: {
    defaultMessage: 'Error loading tenant permissions',
    id: 'actions.tenant.unableToLoadConfig',
  },
});

/**
 * This gets tenants data sources. The API is paginated but we don't have
 * support for it in frontend yet.
 * - If the API returns error return empty results.
 * @returns {array} tenant data sources
 */
export const getTenantDataSources = () => async dispatch => {
  dispatch({ type: GET_TENANT_DATA_SOURCES_LOADING });

  const response = await apiV21.get(TENANT_DATA_SOURCES_ENDPOINT, {
    query: { size: DEFAULT_TAKE },
  });

  dispatch({ payload: response?.results || [], type: GET_TENANT_DATA_SOURCES_FULFILLED });
};

/**
 * This gets the configuration for the tenant.
 * - If the API return empty data or it throws an error
 *   show an error notification and only allow access to
 *   Dashboard and the account settings page..
 *
 * @return {object} tenant configuration object
 */
export const getTenantConfigurations = () => async dispatch => {
  dispatch({ type: GET_TENANT_CONFIGURATIONS_LOADING });

  const tenantConfig = await apiV21.get(TENANT_CONFIGURATION_ENDPOINT);

  const defaultTenantConfig = {
    featureFlags: [],
    subscribedMarketAbuseReports: [],
    subscribedModules: [],
  };

  if (tenantConfig?.status === 404) {
    dispatch(addErrorNotification(TENANT_MESSAGES.configNotFound));
    dispatch({ payload: defaultTenantConfig, type: GET_TENANT_CONFIGURATIONS_FULFILLED });
  } else if (!tenantConfig || tenantConfig.errors) {
    dispatch({ payload: defaultTenantConfig, type: GET_TENANT_CONFIGURATIONS_FULFILLED });
    dispatch(addErrorNotification(TENANT_MESSAGES.unableToLoadConfig));
  } else {
    // If subscribed modules has `Market Abuse` but not the subscribed
    // market abuse reports config, then all reports are shown.
    if (
      !isEmpty(tenantConfig.subscribedModules) &&
      includes(tenantConfig.subscribedModules, modelsEnums.REFERENCE_MODULE_MARKET_ABUSE) &&
      isEmpty(tenantConfig.subscribedMarketAbuseReports)
    ) {
      tenantConfig.subscribedMarketAbuseReports = ALL_SUBSCRIBED_MARKET_ABUSE_REPORTS;
    }

    dispatch({ payload: tenantConfig, type: GET_TENANT_CONFIGURATIONS_FULFILLED });
  }
  return tenantConfig;
};

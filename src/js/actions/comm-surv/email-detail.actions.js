import api from 'util/api/apiV2.1';
import omitBy from 'lodash/omitBy';

import {
  EMAIL_THREAD_SUMMARY_LOADING,
  EMAIL_THREAD_SUMMARY_REJECTED,
  EMAIL_THREAD_SUMMARY_FULFILLED,
  EMAIL_AROUND_LIST_LOADING,
  EMAIL_AROUND_LIST_REJECTED,
  EMAIL_AROUND_LIST_FULFILLED,
} from 'constants/actions/comms-surv';

import {
  getCommsSurvEmailsAroundEndpoint,
  getCommsSurvThreadsSummaryEndpoint,
} from 'constants/endpoints';
/**
 * Calls an endpoint to get surveillance emails records around the given record ID and dispatches loading,
 * rejected and fulfilled actions.
 * @param {*}
 * {
 *  id - email record ID
 *  query - query to filter the records
 * }
 */
export const getCommsSurvEmailsAround =
  ({ id, query }) =>
  async dispatch => {
    dispatch({ payload: { id }, type: EMAIL_AROUND_LIST_LOADING });
    const response = await api.get(getCommsSurvEmailsAroundEndpoint(id), {
      query: {
        ...omitBy(query, value => !value),
        take: 15,
      },
    });
    if (response.errors) {
      dispatch({
        payload: { errors: response.errors, id },
        type: EMAIL_AROUND_LIST_REJECTED,
      });
    } else {
      dispatch({
        payload: { data: response, id },
        type: EMAIL_AROUND_LIST_FULFILLED,
      });
    }
  };

/**
 * Calls an endpoint to get surveillance email thread records summary for the given thread ID and dispatches loading,
 * rejected and fulfilled actions.
 * @param {*}
 * {
 *  id - thread ID
 *  query - query to filter the records
 * }
 */
export const getCommsSurvThreadsSummary =
  ({ id, query }) =>
  async dispatch => {
    dispatch({ payload: { id }, type: EMAIL_THREAD_SUMMARY_LOADING });
    const response = await api.get(getCommsSurvThreadsSummaryEndpoint(id), {
      query,
    });

    if (response.errors) {
      dispatch({
        payload: { errors: response.errors, id },
        type: EMAIL_THREAD_SUMMARY_REJECTED,
      });
    } else {
      dispatch({
        payload: { data: response, id },
        type: EMAIL_THREAD_SUMMARY_FULFILLED,
      });
    }
  };

import get from 'lodash/get';
import apiv21 from 'util/api/apiV2.1';

import { SURVEILLANCE_ALERTS_BULK_UPDATE_ENDPOINT } from 'constants/endpoints';
import * as modelsEnums from '@steeleye/schema/models/enums';

export const PUT_ALERTS_ACTION_LOADING = 'reports/PUT_ALERTS_ACTION_LOADING';
export const PUT_ALERTS_ACTION_REJECTED = 'reports/PUT_ALERTS_ACTION_REJECTED';
export const PUT_ALERTS_ACTION_FULFILLED = 'reports/PUT_ALERTS_ACTION_FULFILLED';

/**
 * Dismisses the selected scenarios.
 * @param {[string]} scenarioIds
 * @param {object} dismissData
 */
export const dismissSelectedAlerts = (scenarioIds, dismissData) => async (_, getState) => {
  const { user } = getState();
  const updatedBy = get(user, 'attributes.NAME', user.principal);
  return apiv21.put(SURVEILLANCE_ALERTS_BULK_UPDATE_ENDPOINT, {
    body: {
      scenarioIds,
      workflowUpdate: {
        ...dismissData,
        resolutionSubCategories: undefined,
        status: modelsEnums.SURVEILLANCE_ALERT_HIT_STATUS_RESOLVED_WITH_DISMISSAL,
        updatedBy,
      },
    },
  });
};

/**
 * Restores the selected scenarios to unresolved state.
 * @param {string[]} scenarioIds
 */
export const restoreSelectedAlerts = scenarioIds => async (_, getState) => {
  const { user } = getState();
  const updatedBy = get(user, 'attributes.NAME', user.principal);
  return apiv21.put(SURVEILLANCE_ALERTS_BULK_UPDATE_ENDPOINT, {
    body: {
      scenarioIds,
      workflowUpdate: {
        status: modelsEnums.SURVEILLANCE_ALERT_HIT_STATUS_UNRESOLVED,
        updatedBy,
      },
    },
  });
};

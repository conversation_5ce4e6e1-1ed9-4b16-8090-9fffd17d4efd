// TODO: When we have archive endpoints with v4 version ready, we should remove this mock data and the mimic of the API calls
import { defineMessages } from 'react-intl';
import { addSuccessNotification } from '../notification';

import {
  GET_ACTIVE_SEARCHES_FULFILLED,
  GET_ACTIVE_SEARCHES_LOADING,
  //   GET_ACTIVE_SEARCHES_REJECTED,
  GET_DELETED_SEARCHES_FULFILLED,
  GET_DELETED_SEARCHES_LOADING,
  //   GET_DELETED_SEARCHES_REJECTED,
} from './constants';

const mockData = [
  {
    name: 'Search 1',
    startedBy: 'User 1',
    started: '2021-01-01',
    status: 'Completed',
    completed: '2021-01-02',
    from: '2021-01-01',
    to: '2021-01-02',
    availableUntil: '2024-06-03',
    filters: ['Filter 1'],
    calls: 1000,
    chats: 25000,
    emails: 300,
    meetings: 2333,
    total: 28733,
    id: 'depefremfrpoim',
  },
  {
    name: 'Search 2',
    startedBy: 'User 2',
    started: '2021-01-02',
    status: 'Completed',
    completed: '2021-01-03',
    from: '2021-01-02',
    to: '2021-01-03',
    availableUntil: '2024-07-04',
    filters: ['Filter 2'],
    calls: 2,
    chats: 3,
    emails: 4,
    meetings: 5,
    total: 14,
    id: 'fmrkfmrmlmfr',
  },
  {
    name: 'Search 1',
    startedBy: 'User 1',
    started: '2021-01-01',
    status: 'Completed',
    completed: '2021-01-02',
    from: '2021-01-01',
    to: '2021-01-02',
    availableUntil: '2024-06-03',
    filters: ['Filter 1'],
    calls: 1,
    chats: 2,
    emails: 3,
    meetings: 4,
    total: 10,
    id: 'depefremfrpoim',
  },
  {
    name: 'Search 2',
    startedBy: 'User 2',
    started: '2021-01-02',
    status: 'Completed',
    completed: '2021-01-03',
    from: '2021-01-02',
    to: '2021-01-03',
    availableUntil: '2024-07-04',
    filters: ['Filter 2'],
    calls: 2,
    chats: 3,
    emails: 4,
    meetings: 5,
    total: 14,
    id: 'fmrkfmrmlmfr',
  },
  {
    name: 'Search 1',
    startedBy: 'User 1',
    started: '2021-01-01',
    status: 'Completed',
    completed: '2021-01-02',
    from: '2021-01-01',
    to: '2021-01-02',
    availableUntil: '2024-06-03',
    filters: ['Filter 1'],
    calls: 1,
    chats: 2,
    emails: 3,
    meetings: 4,
    total: 10,
    id: 'depefremfrpoim',
  },
  {
    name: 'Search 2',
    startedBy: 'User 2',
    started: '2021-01-02',
    status: 'Completed',
    completed: '2021-01-03',
    from: '2021-01-02',
    to: '2021-01-03',
    availableUntil: '2024-07-04',
    filters: ['Filter 2'],
    calls: 2,
    chats: 3,
    emails: 4,
    meetings: 5,
    total: 14,
    id: 'fmrkfmrmlmfr',
  },
  {
    name: 'Search 1',
    startedBy: 'User 1',
    started: '2021-01-01',
    status: 'Completed',
    completed: '2021-01-02',
    from: '2021-01-01',
    to: '2021-01-02',
    availableUntil: '2024-06-03',
    filters: ['Filter 1'],
    calls: 1,
    chats: 2,
    emails: 3,
    meetings: 4,
    total: 10,
    id: 'depefremfrpoim',
  },
  {
    name: 'Search 2',
    startedBy: 'User 2',
    started: '2021-01-02',
    status: 'Completed',
    completed: '2021-01-03',
    from: '2021-01-02',
    to: '2021-01-03',
    availableUntil: '2024-07-04',
    filters: ['Filter 2'],
    calls: 2,
    chats: 3,
    emails: 4,
    meetings: 5,
    total: 14,
    id: 'fmrkfmrmlmfr',
  },
];

const MESSAGES = defineMessages({
  restoreSuccess: {
    defaultMessage: 'Search restored successfully',
    id: 'modules.archive.actions.searches.restoreSuccess',
  },
});

export const getActiveSearches = () => async dispatch => {
  dispatch({ type: GET_ACTIVE_SEARCHES_LOADING });
  const response = await new Promise(resolve => {
    setTimeout(() => {
      resolve(mockData);
    }, 3000);
  });
  dispatch({ type: GET_ACTIVE_SEARCHES_FULFILLED, payload: { data: response } });
};

export const getDeletedSearches = () => async dispatch => {
  dispatch({ type: GET_DELETED_SEARCHES_LOADING });
  const response = await new Promise(resolve => {
    setTimeout(() => {
      resolve(mockData);
    }, 3000);
  });
  dispatch({ type: GET_DELETED_SEARCHES_FULFILLED, payload: { data: response } });
};

export const deleteSearch =
  ({ id, onDeleteDone }) =>
  async () => {
    await new Promise(resolve => {
      setTimeout(() => {
        resolve(id);
        onDeleteDone();
      }, 3000);
    });
  };

export const restoreSearch =
  ({ id, onRestoreDone }) =>
  async dispatch => {
    await new Promise(resolve => {
      setTimeout(() => {
        resolve(id);
        onRestoreDone();
        dispatch(addSuccessNotification(MESSAGES.restoreSuccess));
      }, 3000);
    });
  };

import { createAction } from 'redux-actions';
import uniqueId from 'lodash/uniqueId';

import * as notificationActionConstants from 'constants/actions/notification';
import * as notificationConstants from 'constants/notification';
import * as validationUtil from 'util/validation';

export const removeNotification = createAction(notificationActionConstants.REMOVE_NOTIFICATION);

export const notificationAdded = createAction(notificationActionConstants.NOTIFICATION_ADDED);

function addNotification(type, message) {
  return dispatch => {
    const notification = {
      id: uniqueId('notification_'),
      message,
      type,
    };

    dispatch(notificationAdded(notification));
  };
}

export function addErrorNotification(err, model) {
  const message = validationUtil.getErrorMessageFromError(err, model);

  return addNotification(notificationConstants.NOTIFICATION_TYPE_DANGER, message);
}

export function addCustomErrorNotification(message) {
  return addNotification(notificationConstants.NOTIFICATION_TYPE_DANGER, message);
}

export function addSuccessNotification(message) {
  return addNotification(notificationConstants.NOTIFICATION_TYPE_SUCCESS, message);
}

export function addWarningNotification(message) {
  return addNotification(notificationConstants.NOTIFICATION_TYPE_WARNING, message);
}

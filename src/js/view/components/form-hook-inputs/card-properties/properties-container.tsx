import React from 'react';

import styles from './property.less';
import Card, { CardProps } from './card';

type PropertiesContainerProps = CardProps & {
  children: React.ReactNode;
};

const PropertiesContainer = (props: PropertiesContainerProps) => {
  const { children, ...rest } = props;
  return (
    <Card {...rest}>
      <div className={styles.propertyContainer}>{children}</div>
    </Card>
  );
};

export default PropertiesContainer;

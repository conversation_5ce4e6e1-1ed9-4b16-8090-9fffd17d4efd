@import 'less/site.less';

.svg line {
  stroke: @blue;
}

.svg circle {
  stroke-width: 1px;
}

.svg :global(.node):global(.selectable) circle {
  cursor: pointer;
}

.svg :global(.node):global(.selected):global(.selected) circle {
  stroke: @green;
  fill: @green;
}
.svg :global(.node):global(.selected):global(.selected) text {
  fill: @white;
}

.svg :global(.root.root) circle {
  stroke: @green;
  fill: @green;
}

.svg text {
  font-size: 10px;
  user-select: none;
  pointer-events: none;
  text-anchor: middle;
}

.svg :global(.root.root) text {
  fill: @white;
}

.svg :global(.market) circle {
  stroke: @yellow;
  fill: rgb(79, 79, 47);
}

.svg :global(.market) text {
  fill: @yellow;
}

.svg :global(.account) circle {
  stroke: @blue;
  fill: rgb(31, 64, 82);
}

.svg :global(.account) text {
  fill: @blue;
}
.svg :global(.accountInternal) circle {
  stroke: @purple;
  fill: rgb(56, 36, 88);
}

.svg :global(.accountInternal) text {
  fill: @purple;
}

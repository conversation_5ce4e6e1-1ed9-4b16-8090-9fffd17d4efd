import React, { useState, useEffect, useMemo } from 'react';
import log from 'loglevel';
import omit from 'lodash/omit';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import queryString from 'query-string';
import { defineMessages, useIntl } from 'react-intl';
import { useLocation, useNavigate, useParams } from 'react-router';
import cx from 'classnames';

// Hooks
import useIsMounted from 'js/hooks/useIsMounted';
import { useGlobalTheme } from 'js/hooks/useGlobalTheme';

// Components
import Pagination from 'view/components/pagination';
import OrdersRow from 'view/components/orders-detail-table-new/orders-row';
import HeaderCell from 'view/components/grid-table/header-cell/header-cell';
import ButtonWithIcon from 'view/components/button-with-icon/button-with-icon';
import NoResultsRow from 'view/components/grid-table/no-results-row/no-results-row';
import SortingHeaderCell from 'view/components/grid-table/sorting-header-cell/sorting-header-cell';
import HeaderCheckboxCell from 'view/components/grid-table/header-checkbox-cell/header-checkbox-cell';
import ConfirmAnythingModal from 'view/components/modal/confirm-anything-modal/confirm-anything-modal';
import FetchErrorMessage from 'view/components/fetch-error-message/fetch-error-message.jsx';

// Constants & Utils
import makeSortQuery from 'util/makeSortQuery';
import { userPermissions } from 'js/redux/selectors/permissions';
import { priceFieldsOfPercentageVsPriceFields } from 'view/modules/trades-surveillance/config/surveillance/query-builder-best-ex-price-fields';
import { PROPERTY_MESSAGES } from 'constants/property';
import { getCaseSlug } from 'js/constants/routes';

// Actions
import { addToCase as addToCaseAction } from 'actions/case';
import { getOrdersList } from 'js/actions/orders/orders.actions';

import styles from './listing-tables.less';

const MESSAGES = defineMessages({
  addToCase: {
    defaultMessage: 'Add to case',
    id: 'components.OrdersListing.addToCase',
  },
  averageMarketPrice: {
    defaultMessage: 'Market Price (avg)',
    id: 'components.OrderDetailsList.averageMarketPrice',
  },
  averagePercentVsMarket: {
    defaultMessage: '% vs. Market Price (avg)',
    id: 'components.OrderDetailsList.averagePercentVsMarket',
  },
  averagePrice: {
    defaultMessage: 'Average price',
    id: 'components.OrdersListing.averagePrice',
  },
  buySell: {
    defaultMessage: 'Buy/Sell',
    id: 'components.OrdersListing.buySell',
  },
  counterParty: {
    defaultMessage: 'Counterparty',
    id: 'components.OrdersListing.counterParty',
  },
  currency: {
    defaultMessage: 'Currency',
    id: 'components.OrdersListing.currency',
  },
  InstrumentFullName: {
    defaultMessage: 'Instrument full name',
    id: 'components.OrdersListing.InstrumentFullName',
  },
  ISIN: {
    defaultMessage: 'ISIN',
    id: 'components.OrdersListing.ISIN',
  },
  lifeCycle: {
    defaultMessage: 'Life cycle',
    id: 'components.OrdersListing.lifeCycle',
  },
  orderID: {
    defaultMessage: 'Order ID',
    id: 'components.OrdersListing.orderID',
  },
  orderQuantity: {
    defaultMessage: 'Order quantity',
    id: 'components.OrdersListing.orderQuantity',
  },
  orderSubmitted: {
    defaultMessage: 'Order submitted',
    id: 'components.OrdersListing.orderSubmitted',
  },
  orderType: {
    defaultMessage: 'Order type',
    id: 'components.OrdersListing.orderType',
  },
  remainingQuantity: {
    defaultMessage: 'Remaining quantity',
    id: 'components.OrdersListing.remainingQuantity',
  },
  tradedQuantity: {
    defaultMessage: 'Traded quantity',
    id: 'components.OrdersListing.tradedQuantity',
  },
  trader: {
    defaultMessage: 'Trader',
    id: 'components.OrdersListing.trader',
  },
  youAreAbout: {
    defaultMessage:
      'You are about to add {count} {count, plural, one {order} other {orders}} to the case. Continue?',
    id: 'components.OrdersListing.youAreAbout',
  },
});

const FIELD_COUNT = 15;

export const OrdersListing = ({
  addToCase,
  caseItemsInThePage,
  orderList,
  inCaseMode,
  activeCaseId,
  refineFilters,
  fetchOrdersList,
  ignoredQueryFields,
  hasNewBestExecution,
  fQueryString,
}) => {
  const { isClassicTheme } = useGlobalTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const { isin, instrumentUniqueIdentifier } = useParams();
  const isMounted = useIsMounted();
  const { formatMessage } = useIntl();
  const { data, loading, errors } = orderList;
  const orderItemsInPage = data?.results
    ?.filter(value => caseItemsInThePage.includes(value['&id']))
    .map(row => row['&id']);

  const [selectedRowsIds, setSelectedRowsIds] = useState([]);
  const [confirmAddModalOpen, setConfirmAddModalOpen] = useState(false);
  const [addingToCase, setAddingToCase] = useState(false);

  const query = useMemo(() => queryString.parse(location.search), [location.search]);

  const memoizedQueries = useMemo(
    () => ({
      ...query,
      skip: query.ordersSkip || 0,
      sort: makeSortQuery({
        fieldName: query.ordersSortFieldName || 'timestamps.orderSubmitted',
        order: query.ordersSortOrder || 'descending',
      }),
      take: query.ordersTake || 25,
      ...(isin && { instrumentIds: [instrumentUniqueIdentifier] }),
    }),
    // Trends chart creating a lot of query type and array fileds are causing an infinite loop
    // Json.stringify is the best option for this case
    [
      JSON.stringify(
        omit(query, [
          'selectedRowsIds',
          'ordersExpandedRows',
          'assetClassSortOrder',
          'assetClassSortFieldName',
          'priceSpreads',
          ...ignoredQueryFields,
        ])
      ),
    ]
  );

  useEffect(() => {
    fetchOrdersList({
      fQueryString,
      query: {
        ...memoizedQueries,
        ...(memoizedQueries?.profile && { [memoizedQueries?.profile]: memoizedQueries.value }),
      },
      refineFilters,
    });
  }, [refineFilters, memoizedQueries, fetchOrdersList, fQueryString]);

  const handleSelectedRowsIds = id => {
    setSelectedRowsIds(
      selectedRowsIds.includes(id)
        ? selectedRowsIds.filter(ids => ids !== id)
        : [...selectedRowsIds, id]
    );
  };

  const selectAll = () => {
    if (inCaseMode) {
      setSelectedRowsIds(
        selectedRowsIds.length === data?.results?.length - orderItemsInPage?.length
          ? []
          : data?.results.map(row => row['&id']).filter(id => !orderItemsInPage.includes(id))
      );
    } else {
      setSelectedRowsIds(
        selectedRowsIds.length < data?.results?.length ? data?.results.map(row => row['&id']) : []
      );
    }
  };

  const addToItemsCase = async () => {
    setAddingToCase(true);
    try {
      await addToCase(selectedRowsIds, activeCaseId);
    } catch (error) {
      log.error(error);
    }
    if (isMounted.current) {
      setAddingToCase(false);
      setSelectedRowsIds([]);
      setConfirmAddModalOpen(false);
    }
  };

  const expandedRowsArray =
    typeof query.ordersExpandedRows === 'string'
      ? [query.ordersExpandedRows]
      : query.ordersExpandedRows || [];

  const expandRows = rowId => {
    const expandedArrays = expandedRowsArray?.includes(rowId)
      ? expandedRowsArray.filter(sbar => sbar !== rowId)
      : [...expandedRowsArray, rowId];

    const newPath = `${location.pathname}?${queryString.stringify({
      ...query,
      ordersExpandedRows: expandedArrays,
    })}`;
    navigate(newPath, { replace: true });
  };

  const setPagination = newPagination => {
    const newPath = `${location.pathname}?${queryString.stringify({
      ...query,
      ordersSkip: newPagination.skip || 0,
      ordersTake: newPagination.take || query.ordersTake || 25,
    })}`;
    navigate(newPath, { replace: true });
  };

  const setSort = (fieldName, order) => {
    const newPath = `${location.pathname}?${queryString.stringify({
      ...query,
      ordersSortFieldName: fieldName,
      ordersSortOrder: order,
    })}`;
    navigate(newPath, { replace: true });
  };

  const sortProps = {
    currentSort: {
      fieldName: query.ordersSortFieldName || 'timestamps.orderSubmitted',
      order: query.ordersSortOrder || 'descending',
    },
    onClick: setSort,
  };

  let fields = FIELD_COUNT;
  fields = inCaseMode ? fields + 1 : fields;
  fields = isin ? fields - 2 : fields;
  fields = hasNewBestExecution ? fields + 2 : fields;

  const percentVsPriceField = query.priceSpreads ?? query.missingPrice ?? 'percentVsNearestQuote';
  const priceField = priceFieldsOfPercentageVsPriceFields[percentVsPriceField];
  if (errors) {
    return <FetchErrorMessage />;
  }

  return (
    <>
      <div>
        <section>
          <div
            className={cx({
              [styles.table]: isClassicTheme,
              [styles.newTable]: !isClassicTheme,
              [styles.tableLoading]: !!loading,
            })}
            style={{
              gridTemplateColumns: `repeat(${fields}, auto)`,
            }}
          >
            {inCaseMode && (
              <>
                <HeaderCheckboxCell
                  onChange={selectAll}
                  disabled={orderItemsInPage?.length === data?.results?.length}
                  checked={
                    inCaseMode
                      ? selectedRowsIds.length ===
                          data?.results?.length - orderItemsInPage?.length &&
                        data?.results?.length !== orderItemsInPage?.length
                      : selectedRowsIds.length === data?.results?.length
                  }
                />
                {/* <HeaderCell centered /> */}
              </>
            )}
            <SortingHeaderCell fieldName="id" {...sortProps}>
              {formatMessage(MESSAGES.orderID)}
            </SortingHeaderCell>
            <SortingHeaderCell fieldName="timestamps.orderSubmitted" {...sortProps}>
              {formatMessage(MESSAGES.orderSubmitted)}
            </SortingHeaderCell>
            {!isin && (
              <>
                <SortingHeaderCell
                  fieldName="instrumentDetails.instrument.instrumentIdCode"
                  {...sortProps}
                >
                  {formatMessage(MESSAGES.ISIN)}
                </SortingHeaderCell>
                <SortingHeaderCell
                  fieldName="instrumentDetails.instrument.instrumentFullName"
                  {...sortProps}
                >
                  {formatMessage(MESSAGES.InstrumentFullName)}
                </SortingHeaderCell>
              </>
            )}
            <SortingHeaderCell fieldName="executionDetails.buySellIndicator" {...sortProps}>
              {formatMessage(MESSAGES.buySell)}
            </SortingHeaderCell>
            <SortingHeaderCell fieldName="executionDetails.orderType" {...sortProps}>
              {formatMessage(MESSAGES.orderType)}
            </SortingHeaderCell>
            <SortingHeaderCell centered fieldName="priceFormingData.initialQuantity" {...sortProps}>
              {formatMessage(MESSAGES.orderQuantity)}
            </SortingHeaderCell>
            <HeaderCell centered>{formatMessage(MESSAGES.remainingQuantity)}</HeaderCell>
            <HeaderCell centered>{formatMessage(MESSAGES.tradedQuantity)}</HeaderCell>
            <HeaderCell centered>{formatMessage(MESSAGES.averagePrice)}</HeaderCell>
            {hasNewBestExecution && (
              <>
                <HeaderCell centered>{formatMessage(PROPERTY_MESSAGES[priceField])}</HeaderCell>
                <HeaderCell centered>
                  {formatMessage(PROPERTY_MESSAGES[percentVsPriceField])}
                </HeaderCell>
              </>
            )}
            <SortingHeaderCell
              fieldName="transactionDetails.priceCurrency,bestExecutionData.transactionVolume.nativeCurrency"
              {...sortProps}
            >
              {formatMessage(MESSAGES.currency)}
            </SortingHeaderCell>
            <SortingHeaderCell fieldName="counterparty.name" {...sortProps}>
              {formatMessage(MESSAGES.counterParty)}
            </SortingHeaderCell>
            <SortingHeaderCell fieldName="trader.name" {...sortProps}>
              {formatMessage(MESSAGES.trader)}
            </SortingHeaderCell>
            <HeaderCell centered>{formatMessage(MESSAGES.lifeCycle)}</HeaderCell>
            <HeaderCell centered>
              {inCaseMode && selectedRowsIds.length > 0 && (
                <ButtonWithIcon
                  color="blue"
                  iconName="plus"
                  text={formatMessage(MESSAGES.addToCase)}
                  onClick={() => setConfirmAddModalOpen(true)}
                />
              )}
            </HeaderCell>
            {data?.results?.map(row => {
              return (
                <OrdersRow
                  row={row}
                  key={row['&id']}
                  inCaseMode={inCaseMode}
                  setExpandedRows={expandRows}
                  refineFilters={refineFilters}
                  expandedRows={expandedRowsArray}
                  selectedRowsIds={selectedRowsIds}
                  caseItemsInThePage={orderItemsInPage}
                  handleSelectedRowsIds={handleSelectedRowsIds}
                  avgPriceField={priceField}
                  avgPercentVsPriceField={percentVsPriceField}
                  percentVsPriceField={percentVsPriceField}
                />
              );
            })}
            {!data?.results?.length && !loading && <NoResultsRow />}
          </div>
          <Pagination
            showPagination
            showPageSizeSelector
            total={data?.header.totalHits}
            skip={Number(query.ordersSkip) || 0}
            take={Number(query.ordersTake) || 25}
            onPageSizeAndPaginationChange={setPagination}
          />
        </section>
      </div>
      {confirmAddModalOpen && (
        <ConfirmAnythingModal
          iconName="erase"
          onConfirm={addToItemsCase}
          confirming={addingToCase}
          onClose={() => setConfirmAddModalOpen(false)}
          titleText={formatMessage(MESSAGES.addToCase)}
          contentText={formatMessage(MESSAGES.youAreAbout, { count: selectedRowsIds.length })}
        />
      )}
    </>
  );
};

OrdersListing.propTypes = {
  activeCaseId: PropTypes.string,
  addingToCase: PropTypes.bool,
  addToCase: PropTypes.func.isRequired,
  caseItemsInThePage: PropTypes.object,
  fetchOrdersList: PropTypes.func.isRequired,
  fQueryString: PropTypes.string,
  hasNewBestExecution: PropTypes.bool,
  ignoredQueryFields: PropTypes.array,
  inCaseMode: PropTypes.bool,
  orderList: PropTypes.shape({
    data: PropTypes.shape({
      header: PropTypes.shape({
        totalHits: PropTypes.number,
      }),
      results: PropTypes.array,
    }),
    loading: PropTypes.bool,
  }).isRequired,
  refineFilters: PropTypes.array,
};

OrdersListing.defaultProps = {
  activeCaseId: null,
  addingToCase: false,
  caseItemsInThePage: [],
  fQueryString: undefined,
  hasNewBestExecution: false,
  ignoredQueryFields: [],
  inCaseMode: false,
  refineFilters: [],
};

const mapStateToProps = state => ({
  activeCaseId: getCaseSlug(state.activeCase.caseDetails),
  caseItemsInThePage: state.activeCase.recordsAdded,
  hasNewBestExecution: userPermissions(state).hasNewBestExecution,
  inCaseMode: state.activeCase.inCaseMode,
  orderList: state.orders.ordersListing,
});

const mapDispatchToProps = {
  addToCase: addToCaseAction,
  fetchOrdersList: getOrdersList,
};

export default connect(mapStateToProps, mapDispatchToProps)(OrdersListing);

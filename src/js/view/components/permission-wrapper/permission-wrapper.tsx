import React, { useRef, type ReactNode, useState } from 'react';
import { defineMessages, useIntl } from 'react-intl';
import Tooltip from 'view/components/tooltip';
import styles from './permission-wrapper.less';

const messages = defineMessages({
  noPermission: {
    defaultMessage: 'Your Role does not allow you to perform this action.',
    id: 'module.all.permissions.noPermission',
  },
});

type PermissionWrapper = {
  children: ReactNode;
  permitted: boolean;
  message?: {
    defaultMessage: string;
    id: string;
  };
};

const TooltipComponent = ({ text }: { text: string }) => (
  <div>
    <div className={styles.tooltipContent}>{text}</div>
  </div>
);

function PermissionWrapper({ children, permitted, message }: PermissionWrapper) {
  const { formatMessage } = useIntl();
  const tooltipBtn = useRef(null);
  const [target, setTarget] = useState(null);
  const [showTooltip, setShowTooltip] = useState(false);

  const handleMouseEnter = () => {
    if (!showTooltip && tooltipBtn) {
      setShowTooltip(true);
      setTarget(tooltipBtn.current.getBoundingClientRect());
    }
  };

  const handleMouseOut = () => {
    if (showTooltip && tooltipBtn) {
      setShowTooltip(false);
    }
  };

  if (permitted) return children;

  const reason = message?.id ? formatMessage(message) : message;

  return (
    <div ref={tooltipBtn} onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseOut}>
      <Tooltip
        target={target}
        data={reason || formatMessage(messages.noPermission)}
        show={showTooltip}
        color="red"
      >
        {(data: string) => <TooltipComponent text={data} />}
      </Tooltip>
      <div>{children}</div>
    </div>
  );
}

export default PermissionWrapper;

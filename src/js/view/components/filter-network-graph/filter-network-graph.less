@import 'less/site.less';

.chart {
  position: relative;
  &:after {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: '';
    background-color: @widget-base;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s linear;
  }
}

.chartLoading {
  &:after {
    pointer-events: auto;
    opacity: 0.3;
  }
}

.overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: @widget-base;
}

.container {
  overflow: hidden;
  background-color: @widget-base;
  padding: 4px 8px 8px;
  height: 300px;
  display: flex;
  flex-direction: column;
  position: relative;
}
.darkContainer {
  .container();
  background-color: @dark-background;
  border-radius: 4px;
  border: 1px solid @gray-dark;
}

.graph-container {
  height: 300px;
  margin-bottom: 25px;
  display: flex;
  flex-grow: 1;
}

.titleContainer {
  display: flex;
  justify-content: space-between;
  .dropdown {
    display: flex;
    justify-content: space-between;
    /* Prevent the component from wrapping if the text is wide */
    height: 21px;
    overflow: hidden;
  }
  .subTitle {
    font-size: 12px;
    color: @gray;
    padding: 0 5px;
  }
  .title {
    font-size: 15px;
    font-weight: bold;
    text-transform: capitalize;
  }
}

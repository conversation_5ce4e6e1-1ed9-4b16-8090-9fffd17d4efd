import React from 'react';
import PropTypes from 'prop-types';
import { defineMessages, useIntl } from 'react-intl';

import UploadPlus from 'view/components/button/upload-plus';

import './text-area.less';

const MESSAGES = defineMessages({
  add: {
    defaultMessage: 'Add your files',
    id: 'module.fileZone.text.addFiles',
  },
  dragdrop: {
    defaultMessage: '(or drag & drop)',
    id: 'module.fileZone.text.dragDrop',
  },
  selectedFileZone: {
    defaultMessage: '{selectedFileZone} selected',
    id: 'module.fileZone.text.selectedFileZone',
  },
});

const FileZoneTextArea = ({ selectedFileZone }) => {
  const { formatMessage } = useIntl();
  return (
    <div styleName="fileZoneTextArea">
      <div styleName="upperButton">
        <UploadPlus />
      </div>
      <span styleName="textLarge">{formatMessage(MESSAGES.add)}</span>
      <br />
      <span styleName="textSmall">{formatMessage(MESSAGES.dragdrop)}</span>
      {selectedFileZone && (
        <div styleName="selectedFileZone">
          {formatMessage(MESSAGES.selectedFileZone, { selectedFileZone })}
        </div>
      )}
    </div>
  );
};

FileZoneTextArea.propTypes = {
  selectedFileZone: PropTypes.string,
};

export default FileZoneTextArea;

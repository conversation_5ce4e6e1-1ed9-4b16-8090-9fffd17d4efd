@import 'less/site.less';
@import 'less/layout.less';

.oldContainer {
  padding: 1em;
  background-color: @widget-base;
  box-shadow: 1px -2px 10px none #000;

  &.loading {
    opacity: 0.3;
  }
}

.darkContainer {
  .oldContainer();
  background-color: @dark-background;
  border-radius: 4px;
  border: 1px solid @gray-dark;
  &.loading {
    opacity: 0.3;
  }
}

.header {
  display: flex;
  justify-content: space-between;
}

.titleContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.title {
  margin-left: 0.5em;
  font-weight: bold;
  font-size: large;
}

.expandCard {
  cursor: pointer;
  color: @blue;
  text-decoration: underline;
}

.graphsContainer {
  display: grid;
  grid-template-columns: repeat(4, minmax(256px, 1fr));
  align-content: start;
  grid-gap: 1em;
  margin-top: 1em;
}

.graph {
  display: flex;
}

.expand {
  cursor: pointer;
  color: @blue;
  text-decoration: underline;
}

.graphContainerEnter {
  max-height: 0;
  opacity: 0;
}

.graphContainerEnterActive {
  max-height: 300px;
  opacity: 1;
  transition:
    opacity 200ms ease-in 200ms,
    max-height 200ms ease-in;
}

.graphContainerExit {
  max-height: 300px;
  opacity: 1;
  transition:
    opacity 200ms ease-in,
    max-height 200ms ease-in 200ms;
}

.graphContainerExitActive {
  max-height: 0;
  opacity: 0;
}

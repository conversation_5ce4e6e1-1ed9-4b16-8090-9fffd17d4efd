import React from 'react';
import PropTypes from 'prop-types';

const FifteenForward = ({ color = '#0396C8' }) => (
  <svg viewBox="0 -1 19 22">
    <defs>
      <path
        d="M18.7090909,9.80108647 L16.2249231,9.80108647 L16.2249231,9.5783592 C16.111816,4.76093126 12.3248216,0.890909091 7.66962524,0.890909091 C2.94111869,0.890909091 -0.890909091,4.88044346 -0.890909091,9.80108647 C-0.890909091,14.7217295 2.94111869,18.7090909 7.66962524,18.7090909 L7.66962524,16.4829047 C4.12350715,16.4829047 1.24870085,13.4907539 1.24870085,9.80108647 C1.24870085,6.11141907 4.12350715,3.11926829 7.66962524,3.11926829 C11.1434804,3.11926829 13.9722061,5.9908204 14.0842659,9.5783592 L14.0842659,9.80108647 L11.2859116,9.80108647 L14.9451369,14.0318182 L18.7090909,9.80108647 Z"
        id="15-forward-path-1"
      />
    </defs>
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(-321.000000, -197.000000)">
        <g transform="translate(307.000000, 149.000000)">
          <g transform="translate(10.000000, 44.000000)">
            <g transform="translate(4.890909, 4.109091)">
              <text
                fontFamily="DINAlternate-Bold, DIN Alternate"
                fontSize="6"
                fontWeight="bold"
                fill={color}
              >
                <tspan x="6.10909091" y="12.8909091">
                  15
                </tspan>
              </text>
              <mask fill="white">
                <use xlinkHref="#15-forward-path-1" />
              </mask>
              <use
                fill={color}
                fillRule="nonzero"
                transform="translate(8.909091, 9.800000) rotate(270.000000) translate(-8.909091, -9.800000) "
                xlinkHref="#15-forward-path-1"
              />
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
);

FifteenForward.propTypes = {
  color: PropTypes.string,
};

export default FifteenForward;

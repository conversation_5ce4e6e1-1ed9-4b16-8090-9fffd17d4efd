import React from 'react';
import PropTypes from 'prop-types';

const DashheadToolbar = ({ className, children, testId }) => (
  <div data-test-id={testId} className={`dashhead-toolbar ${className}`}>
    {children}
  </div>
);

DashheadToolbar.propTypes = {
  children: PropTypes.any,
  className: PropTypes.string,
  testId: PropTypes.string,
};
DashheadToolbar.defaultProps = {
  className: '',
};
export default DashheadToolbar;

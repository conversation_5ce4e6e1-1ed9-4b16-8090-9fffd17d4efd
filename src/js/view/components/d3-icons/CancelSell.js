const raw = `
  <svg x="-16" y="-16" width="32" height="32" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <defs>
          <path d="M12.1285304,12.1738618 C11.4999043,12.7420461 10.481235,12.7420461 9.85394926,12.1738618 L6.30067018,8.50307678 L2.7473911,12.1726504 C2.11876496,12.7408346 1.10009574,12.7408346 0.472809957,12.1726504 C-0.15581618,11.6044661 -0.15581618,10.6837412 0.472809957,10.1167684 L4.16950694,6.30060574 L0.471469603,2.4820201 C-0.157156534,1.91383587 -0.157156534,0.994322388 0.471469603,0.426138166 C1.10009574,-0.142046055 2.11742461,-0.142046055 2.74605074,0.426138166 L6.30067018,4.09813471 L9.85394926,0.426138166 C10.4825754,-0.142046055 11.4999043,-0.142046055 12.1285304,0.426138166 C12.7571565,0.994322388 12.7571565,1.91504735 12.1285304,2.4820201 L8.43183341,6.30060574 L12.1285304,10.1167684 C12.7571565,10.6849526 12.7571565,11.6056776 12.1285304,12.1738618 Z" id="cancelSell-path-1"></path>
      </defs>
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
          <g transform="translate(-29.000000, -132.000000)">
              <g transform="translate(30.000000, 133.000000)">
                  <circle stroke="#E84556" fill="#15181f" fill-rule="nonzero" cx="15" cy="15" r="15"></circle>
                  <g fill-rule="evenodd" transform="translate(6.000000, 6.000000)">
                      <g transform="translate(2.700000, 2.700000)">
                          <mask id="cancelSell-mask-2" fill="white">
                              <use xlink:href="#cancelSell-path-1"></use>
                          </mask>
                          <use fill="#000000" fill-rule="nonzero" xlink:href="#cancelSell-path-1"></use>
                          <g mask="url(#cancelSell-mask-2)" fill="#E84556">
                              <g transform="translate(-2.700000, -2.700000)">
                                  <rect x="0" y="0" width="18" height="18"></rect>
                              </g>
                          </g>
                      </g>
                  </g>
              </g>
          </g>
      </g>
  </svg>
  `;

const svg = selection => {
  selection.html(raw);
};

export default svg;

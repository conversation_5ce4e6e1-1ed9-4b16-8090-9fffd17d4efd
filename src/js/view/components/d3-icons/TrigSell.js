const raw = `
  <svg x="-16" y="-16" width="32" height="32" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
          <g transform="translate(-29.000000, -1210.000000)">
              <g transform="translate(30.000000, 1211.000000)">
                  <circle stroke="#E84556" fill="#15181f" fill-rule="nonzero" cx="15" cy="15" r="15"></circle>
                  <g fill-rule="evenodd" transform="translate(6.000000, 6.000000)" fill="#E84556">
                      <path d="M9.29986844,7.79624305 C8.47246844,7.79624305 7.79986844,8.46824305 7.79986844,9.29624305 C7.79986844,10.124243 8.47246844,10.796243 9.29986844,10.796243 C10.1272684,10.796243 10.7998684,10.124243 10.7998684,9.29624305 C10.7998684,8.46824305 10.1272684,7.79624305 9.29986844,7.79624305 L9.29986844,7.79624305 Z M9.29986844,11.396243 C8.14186844,11.396243 7.19986844,10.454243 7.19986844,9.29624305 C7.19986844,8.13824305 8.14186844,7.19624305 9.29986844,7.19624305 C10.4578684,7.19624305 11.3998684,8.13824305 11.3998684,9.29624305 C11.3998684,10.454243 10.4578684,11.396243 9.29986844,11.396243 L9.29986844,11.396243 Z"></path>
                      <path d="M9.29986844,17.396243 C4.83346844,17.396243 1.19986844,13.766243 1.19986844,9.29624305 C1.19986844,7.57424305 1.74106844,5.92424305 2.76526844,4.52624305 C2.86366844,4.39424305 3.05146844,4.36424305 3.18466844,4.46024305 C3.31846844,4.55624305 3.34726844,4.74824305 3.24946844,4.88024305 C2.30146844,6.17624305 1.79986844,7.70024305 1.79986844,9.29624305 C1.79986844,13.436243 5.16406844,16.796243 9.29986844,16.796243 C13.4356684,16.796243 16.7998684,13.436243 16.7998684,9.29624305 C16.7998684,5.16224305 13.4356684,1.79624305 9.29986844,1.79624305 C7.70506844,1.79624305 6.17746844,2.30024305 4.88146844,3.24764305 C4.74766844,3.34424305 4.55986844,3.31424305 4.46266844,3.18224305 C4.36426844,3.05024305 4.39306844,2.86424305 4.52746844,2.76224305 C5.92666844,1.74224305 7.57666844,1.19624305 9.29986844,1.19624305 C13.7662684,1.19624305 17.3998684,4.83224305 17.3998684,9.29624305 C17.3998684,13.766243 13.7662684,17.396243 9.29986844,17.396243"></path>
                      <path d="M9.29986844,14.396243 C6.48766844,14.396243 4.19986844,12.110243 4.19986844,9.29624305 C4.19986844,8.39024305 4.44526844,7.49624305 4.91026844,6.71024305 C4.99486844,6.56624305 5.17966844,6.52424305 5.32066844,6.60224305 C5.46346844,6.69224305 5.51146844,6.87224305 5.42686844,7.01624305 C5.01646844,7.71224305 4.79986844,8.49824305 4.79986844,9.29624305 C4.79986844,11.780243 6.81826844,13.796243 9.29986844,13.796243 C11.7814684,13.796243 13.7998684,11.780243 13.7998684,9.29624305 C13.7998684,6.81824305 11.7814684,4.79624305 9.29986844,4.79624305 C8.50126844,4.79624305 7.71226844,5.01224305 7.01746844,5.42624305 C6.87646844,5.51024305 6.69106844,5.46224305 6.60706844,5.31824305 C6.52246844,5.17424305 6.56986844,4.99424305 6.71266844,4.91024305 C7.49926844,4.44224305 8.39386844,4.19624305 9.29986844,4.19624305 C12.1120684,4.19624305 14.3998684,6.48824305 14.3998684,9.29624305 C14.3998684,12.110243 12.1120684,14.396243 9.29986844,14.396243"></path>
                      <path d="M2.22406844,2.99624305 L2.99986844,2.99624305 L2.99986844,2.22224305 L1.79986844,1.02224305 L1.79986844,1.49624305 C1.79986844,1.66424305 1.66546844,1.79624305 1.49986844,1.79624305 L1.02406844,1.79624305 L2.22406844,2.99624305 L2.22406844,2.99624305 Z M9.29986844,9.59624305 C9.22306844,9.59624305 9.14626844,9.56624305 9.08746844,9.51224305 L3.17566844,3.59624305 L2.09986844,3.59624305 C2.02006844,3.59624305 1.94386844,3.56624305 1.88746844,3.51224305 L0.0874684363,1.71224305 C0.00226843625,1.62164305 -0.0235315637,1.49624305 0.0226684363,1.38224305 C0.0688684363,1.27424305 0.178668436,1.19624305 0.299868436,1.19624305 L1.19986844,1.19624305 L1.19986844,0.296243049 C1.19986844,0.176243049 1.27306844,0.0682430488 1.38526844,0.0202430488 C1.49686844,-0.0217569512 1.62646844,0.00224304878 1.71226844,0.0862430488 L3.51226844,1.88624305 C3.56806844,1.94024305 3.59986844,2.01824305 3.59986844,2.09624305 L3.59986844,3.17624305 L9.51226844,9.08624305 C9.62926844,9.20024305 9.62926844,9.39224305 9.51226844,9.51224305 C9.45346844,9.56624305 9.37666844,9.59624305 9.29986844,9.59624305 L9.29986844,9.59624305 Z"></path>
                      <path d="M17.6998684,17.996243 C17.6074684,17.996243 17.5156684,17.954243 17.4574684,17.876243 L15.0574684,14.576243 C14.9602684,14.438243 14.9890684,14.252243 15.1234684,14.156243 C15.2572684,14.060243 15.4450684,14.090243 15.5422684,14.221643 L17.9422684,17.522243 C18.0394684,17.654243 18.0106684,17.846243 17.8762684,17.942243 C17.8228684,17.978243 17.7616684,17.996243 17.6998684,17.996243"></path>
                      <path d="M0.899868436,17.996243 C0.838068436,17.996243 0.776868436,17.978243 0.723468436,17.942243 C0.589068436,17.846243 0.560268436,17.654243 0.657468436,17.522243 L3.05746844,14.221643 C3.15526844,14.090243 3.34306844,14.060243 3.47626844,14.156243 C3.61066844,14.252243 3.63946844,14.438243 3.54226844,14.576243 L1.14226844,17.876243 C1.08406844,17.954243 0.992268436,17.996243 0.899868436,17.996243"></path>
                  </g>
              </g>
          </g>
      </g>
  </svg>
  `;

const svg = selection => {
  selection.html(raw);
};

export default svg;

import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withRouter } from 'util/withRouter';
import { getFormValues } from 'redux-form';
import isEqual from 'lodash/isEqual';

import SearchResultsTable from 'view/components/table/search-results-table';
import SelectableSearchResultsTable from 'view/components/table/selectable-search-results-table';
import CaseModeTableRow from 'view/components/case-mode/case-mode-table-row';
import FlexColumn from 'view/components/layout/flex-column';
import FlexColumnContainer from 'view/components/layout/flex-column-container';
import RefinesFilter from 'util/filters/refines';
import ListingView from 'util/listing-view';
import * as widgetConstants from 'constants/widget';
import * as widgetSetUtil from 'util/widget-set';
import * as searchUtil from 'util/search';
import * as widgetSetActions from 'actions/widget-set';
import * as widgetSetSelectors from 'js/redux/selectors/widget-set';
import * as entityUtil from 'util/entity';
import * as routerConstants from 'constants/router';
import { withWidgetErrorHandler } from 'view/components/layout/hoc';

class ListingWidget extends React.Component {
  UNSAFE_componentWillMount() {
    const { fetchDataOnMount, filters, view, fetchOptions } = this.props;
    if (fetchDataOnMount) {
      this.handleFetchData(filters, view, fetchOptions);
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const { filters, view, fetchOptions } = this.props;
    if (
      !isEqual(nextProps.filters, filters) ||
      !isEqual(nextProps.view, view) ||
      !isEqual(nextProps.fetchOptions, fetchOptions)
    ) {
      this.handleFetchData(nextProps.filters, nextProps.view, nextProps.fetchOptions);
    }
  }

  shouldComponentUpdate(nextProps) {
    const { layout, widget, view, activeCase, selectable } = this.props;
    return (
      nextProps.layout !== layout ||
      nextProps.widget.data !== widget.data ||
      nextProps.widget.loading !== widget.loading ||
      nextProps.view !== view ||
      nextProps.activeCase.inCaseMode !== activeCase.inCaseMode ||
      nextProps.activeCase.childKeys !== activeCase.childKeys ||
      nextProps.selectable !== selectable ||
      widgetSetUtil.getSelection(nextProps.widget) !== widgetSetUtil.getSelection(widget)
    );
  }

  handleSearch = newView => {
    const { widgetSetId, widgetId, view, updateView } = this.props;
    updateView(
      widgetSetId,
      widgetId,
      new ListingView(
        newView.skip,
        newView.sortPropertyId,
        newView.sortAscending,
        newView.take || view.take,
        view.useScore
      )
    );
  };

  handleDetailClick = ({ id, model }) => {
    const { searchFormValue, view, widget, onDetailClick, history, locationState, location } =
      this.props;
    const searchTerm = searchFormValue?.term || '';

    if (onDetailClick) {
      const entity = widgetSetUtil
        .createSearchDataShim(widget, view)
        .items.find(datum => datum['&id'] === id && datum['&model'] === model);
      onDetailClick({ id, model, entity, searchTerm });
    } else {
      const pathname = entityUtil.resolvePageDetailUrl({
        modelName: model,
        entityId: id,
        searchTerm,
      });

      history.push(pathname, {
        ...locationState,
        from: location,
      });
    }
  };

  updateSelectedItems = ({ ids, replace }) => {
    const { widget, updateWidget, widgetSetId, widgetId, onSelectionUpdated } = this.props;

    updateWidget(widgetSetId, widgetId, {
      selection: widgetSetUtil.createUpdatedSelection(widget, ids, replace),
    });

    if (onSelectionUpdated) onSelectionUpdated();
  };

  handleFetchData = (filters, view, fetchOptions) => {
    const { fetchData, widgetSetId, widgetId } = this.props;
    fetchData(widgetSetId, widgetId, filters, view, fetchOptions);
  };

  render() {
    const {
      widget,
      view,
      activeCase,
      refineOpen,
      refinesFilter,
      model,
      layout,
      bodyCellProps,
      selectable,
      allSelectableIds,
      rowComponent,
      showPagination,
      includeChevron,
      checkboxDisabledCallback,
      allowSelectAll,
      className,
      selectableInCaseMode,
      noResultsComponent,
      hasActionButton,
      hasExpandCollapseButton,
      tableHeadClassName,
      tableLayoutClassName,
    } = this.props;

    const selection = widgetSetUtil.getSelection(widget);
    const search = widgetSetUtil.createSearchDataShim(widget, view);
    const maxRows = view ? view.take : this.props.maxRows;
    const selectableIds = allSelectableIds || searchUtil.getSelectableIds(search, activeCase);
    const listingIsSelectable = activeCase.inCaseMode ? selectableInCaseMode : selectable;

    return (
      <FlexColumnContainer className={className}>
        <FlexColumn>
          {listingIsSelectable && (
            <SelectableSearchResultsTable
              search={search}
              filters={refinesFilter.filters}
              selection={selection}
              allSelectableIds={selectableIds}
              refining={refineOpen}
              model={model}
              layout={layout}
              rowComponent={activeCase.inCaseMode ? CaseModeTableRow : rowComponent}
              bodyCellProps={bodyCellProps}
              updateSelectedItems={this.updateSelectedItems}
              updateDetailItem={this.handleDetailClick}
              onSearch={this.handleSearch}
              maxRows={maxRows}
              showPagination={showPagination}
              includeChevron={includeChevron}
              checkboxDisabledCallback={checkboxDisabledCallback}
              allowSelectAll={allowSelectAll}
              noResultsComponent={noResultsComponent}
              hasExpandCollapseButton={hasExpandCollapseButton}
              tableHeadClassName={tableHeadClassName}
              layoutClassName={tableLayoutClassName}
            />
          )}
          {!listingIsSelectable && (
            <SearchResultsTable
              search={search}
              filters={refinesFilter.filters}
              refining={refineOpen}
              model={model}
              layout={layout}
              bodyCellProps={bodyCellProps}
              updateDetailItem={this.handleDetailClick}
              onSearch={this.handleSearch}
              rowComponent={rowComponent}
              maxRows={maxRows}
              showPagination={showPagination}
              includeChevron={includeChevron}
              hasActionButton={hasActionButton}
              hasExpandCollapseButton={hasExpandCollapseButton}
              tableHeadClassName={tableHeadClassName}
              layoutClassName={tableLayoutClassName}
            />
          )}
        </FlexColumn>
      </FlexColumnContainer>
    );
  }
}

ListingWidget.propTypes = {
  activeCase: PropTypes.object.isRequired,
  allowSelectAll: PropTypes.bool,
  allSelectableIds: PropTypes.arrayOf(PropTypes.string),
  bodyCellProps: PropTypes.object,
  checkboxDisabledCallback: PropTypes.func,
  className: PropTypes.string,
  fetchData: PropTypes.func.isRequired,
  fetchDataOnMount: PropTypes.bool,
  fetchOptions: PropTypes.object,
  filters: PropTypes.array.isRequired,
  hasActionButton: PropTypes.bool,
  hasExpandCollapseButton: PropTypes.bool,
  history: routerConstants.ROUTER_HISTORY_PROP_TYPES.isRequired,
  includeChevron: PropTypes.bool,
  layout: PropTypes.object.isRequired,
  location: routerConstants.ROUTER_LOCATION_PROP_TYPES.isRequired,
  locationState: PropTypes.object,
  maxRows: PropTypes.number,
  model: PropTypes.object.isRequired,
  noResultsComponent: PropTypes.oneOfType([PropTypes.node, PropTypes.func]),
  onDetailClick: PropTypes.func,
  onSelectionUpdated: PropTypes.func,
  refineOpen: PropTypes.bool,
  refinesFilter: PropTypes.instanceOf(RefinesFilter).isRequired,
  rowComponent: PropTypes.any,
  searchFormValue: PropTypes.shape({ term: PropTypes.string }),
  selectable: PropTypes.bool,
  selectableInCaseMode: PropTypes.bool,
  showPagination: PropTypes.bool,
  updateView: PropTypes.func.isRequired,
  updateWidget: PropTypes.func.isRequired,
  view: PropTypes.instanceOf(ListingView).isRequired,
  widget: widgetConstants.TABLE_WIDGET_PROP_TYPES.isRequired,
  widgetId: PropTypes.string.isRequired,
  widgetSetId: PropTypes.string.isRequired,
  tableHeadClassName: PropTypes.string,
  tableLayoutClassName: PropTypes.string,
};

ListingWidget.defaultProps = {
  allowSelectAll: true,
  allSelectableIds: undefined,
  bodyCellProps: undefined,
  checkboxDisabledCallback: undefined,
  className: '',
  fetchDataOnMount: true,
  fetchOptions: undefined,
  hasActionButton: false,
  hasExpandCollapseButton: false,
  includeChevron: false,
  locationState: undefined,
  maxRows: 50,
  noResultsComponent: undefined,
  onDetailClick: undefined,
  onSelectionUpdated: undefined,
  refineOpen: false,
  rowComponent: undefined,
  searchFormValue: {},
  selectable: false,
  selectableInCaseMode: true,
  showPagination: true,
  tableHeadClassName: '',
  tableLayoutClassName: '',
};

export default withWidgetErrorHandler(
  withRouter(
    connect(
      (state, ownProps) => ({
        activeCase: state.activeCase,
        widget: widgetSetSelectors.getWidget(state, ownProps.widgetSetId, ownProps.widgetId),
        filters: widgetSetSelectors.getFiltersForWidget(
          state,
          ownProps.widgetSetId,
          ownProps.widgetId
        ),
        refinesFilter: widgetSetSelectors.getFilter(state, ownProps.widgetSetId, RefinesFilter),
        view: widgetSetSelectors.getView(
          state,
          ownProps.widgetSetId,
          ownProps.widgetId,
          ownProps.defaultView
        ),
        searchFormValue: getFormValues(`Search-${ownProps.location.pathname}`)(state),
      }),
      dispatch => ({
        updateWidget: bindActionCreators(widgetSetActions.updateWidget, dispatch),
        updateView: bindActionCreators(widgetSetActions.updateView, dispatch),
      })
    )(ListingWidget)
  )
);

@import 'less/site.less';

.container {
  padding: 10px;
  background-color: #313540;
  position: relative;
  .title {
    font-size: 20px;
    color: @white;
  }
  .collapse-expand-button {
    top: 15px;
    position: absolute;
    right: 20px;
  }
}
.darkContainer {
  .container();
  background-color: @dark-background;
  border-radius: 4px;
  border-top: 1px solid @gray-dark;
  border-bottom: 1px solid @gray-dark;
}
.filterContainer {
  border: 1px solid @gray-dark;
  margin-top: 10px;
  padding: 8px;
}
.panelClassName {
  border: 1px solid @gray-dark;
  min-width: 330px;
  span {
    max-width: 140px;
  }
}
.flexColumnContainerClassNameWithBorder {
  border-bottom: 1px solid @gray-dark;
  padding-bottom: 10px;
}
.flexColumnContainerClassName {
  padding: 0;
}

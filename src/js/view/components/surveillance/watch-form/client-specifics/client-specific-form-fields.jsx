import React from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';

import { CLIENTS } from 'js/constants/tenant-ids';
import { validators } from 'util/validation';

import { Field } from 'redux-form';
import JurisdictionSection from 'js/view/components/surveillance/watch-form/jurisdiction-section';

const ClientSpecificFormFields = ({ client, ...props }) => {
  const fieldLists = useSelector(state => state.maxiRefine.fieldLists);
  switch (client) {
    case CLIENTS.UBS:
      return (
        <Field
          required
          name="jurisdiction"
          component={JurisdictionSection}
          validate={validators.jurisdiction}
          fieldLists={fieldLists}
          {...props}
        />
      );

    default:
      return null;
  }
};

ClientSpecificFormFields.propTypes = {
  client: PropTypes.string,
};

export default ClientSpecificFormFields;

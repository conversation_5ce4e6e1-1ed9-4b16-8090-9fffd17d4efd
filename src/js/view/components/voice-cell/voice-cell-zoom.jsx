import React, { useState, useRef, useEffect, useCallback, useReducer } from 'react';
import PropTypes from 'prop-types';
import queryString from 'query-string';
import { useLocation } from 'react-router';
import { defineMessages, useIntl } from 'react-intl';
import { useSelector, useDispatch } from 'react-redux';

import { convertMillisecondsToTimeString, convertTimeStringToSeconds } from 'js/util/time';

import { useDownloadCommsAttachment } from 'js/hooks/useDownloadCommsAttachment';
import usePersistedState from 'js/hooks/persistedState/usePersistedState';
import useComponentSize from 'js/hooks/useComponentSize';
import LoadingCentered from 'js/view/components/loading-centered/loading-centered';

import HighlightedText from 'view/components/highlighted-text/highlighted-text';
import IconButton from 'view/components/icon-button/icon-button';
import { getPreSignedUrlZoom, getWaveformDataZoom } from 'js/actions/meetings/zoom.actions';

import './voice-cell.less';
import {
  getTriggersFromMatchHighlight,
  getVoiceTranscriptionText,
  TRANSCRIPTION_NOT_ATTEMPTED,
} from 'js/util/surveillance';
import Waveform from './waveform/waveform.jsx';
import HighlightedTextWithTriggers from '../highlighted-text-with-triggers/highlighted-text-with-triggers';

const messages = defineMessages({
  callNotConnected: {
    defaultMessage: 'Meet not connected',
    id: 'component.VoiceCellZoom.callNotConnected',
  },
  collapse: {
    defaultMessage: 'Collapse',
    id: 'component.VoiceCellZoom.collapse',
  },
  expand: {
    defaultMessage: 'Expand',
    id: 'component.VoiceCellZoom.expand',
  },
  matched: {
    defaultMessage: 'Matched',
    id: 'component.ExpandableCommsCell.matched',
  },
  meetingDuration: {
    defaultMessage: 'Meeting Duration: {duration} - {displayText}',
    id: 'component.VoiceCellZoom.meetingDuration',
  },
  meetingDurationNotFound: {
    defaultMessage: 'Not Found',
    id: 'component.VoiceCellZoom.meetingDuration.notFound',
  },
  noAudioAvailable: {
    defaultMessage: 'No audio available - Meeting Duration: {duration} - {displayText}',
    id: 'component.VoiceCellZoom.noAudioAvailable',
  },
  transcriptionNotAttempted: {
    defaultMessage: 'Transcription Not Attempted',
    id: 'component.VoiceCellZoom.transcriptionNotAttempted',
  },
  transcriptNotAvailable: {
    defaultMessage: 'Transcript not available',
    id: 'component.VoiceCellZoom.transcriptNotAvailable',
  },
});

const voiceReducer = (state, action) => {
  switch (action.type) {
    case 'AUDIO_TIME_UPDATED':
      return { ...state, playbackTime: action.payload.time };

    case 'PLAYBACK_STARTED':
      return {
        ...state,
        playing: true,
      };

    case 'PLAYBACK_TIME_CHANGED':
    case 'AUDIO_ELEMENT_SPEED_CHANGED':
      return {
        ...state,
      };

    case 'PLAYBACK_PAUSED':
      return { ...state, lastTimeClicked: { time: action.payload.ts }, playing: false };

    case 'PLAYBACK_ENDED':
      return {
        ...state,
        lastTimeClicked: { time: 0 },
        playbackTime: 0,
        playing: false,
      };
    case 'PLAYBACK_CLICKED':
      // This should only happen if we're trying to play it without audio having loaded
      return {
        ...state,
        playing: !state.playing,
      };

    case 'WORD_CLICKED':
    case 'TIME_CLICKED':
      return {
        ...state,
        lastTimeClicked: { time: action.payload.time },
        playbackTime: action.payload.time,
      };

    case 'SPEEDPICKER_CLICKED':
      return { ...state, speedPickerOpen: !state.speedPickerOpen };

    case 'SPEED_CHANGED':
      return { ...state, speed: action.payload.speed, speedPickerOpen: false };

    case 'AUDIO_READY':
      return { ...state, audioReady: true };

    case 'HIGHLIGHTED_TOKENS_FOUND':
      return { ...state, highlightedTimes: action.payload.highlightedTokens.map(el => el.time) };

    default:
      return state;
  }
};

/**
 * A cell that can play and download the audio file of a voice communication
 */
const VoiceCell = ({ data, defaultHeight, highlights, highlightedBodyText }) => {
  const location = useLocation();
  const dispatch = useDispatch();
  const audioElement = useRef(null);
  const { '&id': meetingId } = data || {};
  const meetingDuration =
    data?.meetingDurationInSeconds ??
    (convertTimeStringToSeconds(data?.meetingDuration) || data?.meetingDuration);
  const audioOnlyRecording = data.recordings?.find(el => el.type === 'Audio Only');
  const fileName = 'Audio Recording';
  const fileType = audioOnlyRecording?.file?.type;
  const recordingId = audioOnlyRecording?.file?.id;
  const voiceFileId = recordingId;
  const voiceFileName = fileName;
  const voiceFileType = fileType;
  const attachmentKey = audioOnlyRecording?.sourceKey;
  const querySearch = queryString.parse(location.search).search;
  const searchTerms = [...(highlights || []), ...(querySearch ? [querySearch] : [])];
  const { saveFile, isFetching } = useDownloadCommsAttachment({
    attachmentKey,
    commsId: meetingId,
  });

  const [audioError, setAudioError] = useState(null);
  const [resumeTime, setResumeTime] = useState(null);
  const [expiresAt, setExpiresAt] = useState(null);
  const [videoPlayLoading, setVideoPlayLoading] = useState(false);
  const [videoSource, setVideoSource] = useState();

  const rawWaveformData = useSelector(
    state => state.comms.waveform?.[meetingId]?.data?.waveform_data
  );

  const loadingWaveformData =
    useSelector(state => state.comms?.waveform?.[meetingId]?.loading) || false;

  const videoSourceError = useSelector(state => state.comms?.video?.[meetingId]?.errors);
  const loadingVideoData = useSelector(state => state.comms?.video?.[meetingId]?.loading) || false;

  const waveformData = {
    blocks: rawWaveformData,
    duration: meetingDuration,
  };

  const [voiceState, voiceDispatch] = useReducer(voiceReducer, {
    audioReady: false,
    highlightedTimes: null,
    lastTimeClicked: { time: 0 },
    playbackTime: 0,
    playing: false,
    speed: 1,
    speedPickerOpen: false,
  });

  const [expanded, setExpanded] = usePersistedState(data['&id'], false);
  const expandCellRef = useRef(null);
  const { height } = useComponentSize(expandCellRef);
  const { formatMessage } = useIntl();

  const handlePreSignedUrl = useCallback(
    // eslint-disable-next-line no-shadow
    ({ newTime, recordingId }) => {
      dispatch(getPreSignedUrlZoom({ meetingId, recordingId })).then(res => {
        const url = res?.pre_signed_url || '';
        const expires = url && new URL(url).searchParams.get('Expires') * 1;
        setExpiresAt(expires);
        setResumeTime(newTime);
        setVideoSource(url);
        if (!newTime && url) {
          voiceDispatch({ type: 'VIDEO_READY' });
        } else {
          voiceDispatch({
            payload: { ts: audioElement.current.currentTime },
            type: 'PLAYBACK_PAUSED',
          });
        }
      });
    },
    [meetingId, dispatch]
  );

  const handleGenerateWaveForm = useCallback(() => {
    dispatch(getWaveformDataZoom({ meetingId })).then(res => {
      if (res.blocks && res.duration) {
        voiceDispatch({
          payload: { time: res.duration },
          type: 'VIDEO_TIME_UPDATED',
        });
      }
    });
  }, [meetingId, dispatch]);

  const handlePlayClick = () => {
    if (audioElement.current && videoSource) {
      if (audioElement.current.paused) {
        setVideoPlayLoading(true);

        audioElement.current.play().then(() => {
          setVideoPlayLoading(false);

          voiceDispatch({
            payload: { ts: new Date().getTime() },
            type: 'PLAYBACK_STARTED',
          });
        });
      } else {
        audioElement.current.pause();
        voiceDispatch({
          payload: { ts: audioElement.current.currentTime },
          type: 'PLAYBACK_PAUSED',
        });
      }
    } else if (videoSource) {
      // This would only happen if the audio element is not initialized, e.g. if the audio file didn't load properly
      // TODO handle audio loading state properly
      voiceDispatch({
        payload: { ts: new Date().getTime() },
        type: 'PLAYBACK_CLICKED',
      });
    }
    if ((!videoSource || !rawWaveformData) && recordingId) {
      handlePreSignedUrl({ newTime: null, recordingId });
      handleGenerateWaveForm();
    }
  };

  const handleClickTime = s => {
    voiceDispatch({ payload: { time: s }, type: 'TIME_CLICKED' });
  };
  useEffect(() => {
    if (audioElement.current) {
      // Move actual audio playback if we've clicked the time
      audioElement.current.currentTime = voiceState.lastTimeClicked.time;
      voiceDispatch({
        payload: { point: voiceState.lastTimeClicked.time, ts: new Date().getTime() },
        type: 'PLAYBACK_TIME_CHANGED',
      });
    }
  }, [voiceState.lastTimeClicked]);

  useEffect(() => {
    let timer;
    if (expiresAt) {
      clearTimeout(timer);
      const timeWindow = expiresAt - Math.floor(new Date().getTime() / 1000);
      if (timeWindow > 0) {
        timer = setTimeout(() => {
          if (audioElement.current) audioElement?.current?.pause();
          handlePreSignedUrl({
            newTime: audioElement?.current?.currentTime,
            recordingId,
          });
        }, timeWindow * 1000);
      }
    }
    return () => {
      clearTimeout(timer);
    };
  }, [expiresAt, handlePreSignedUrl, recordingId]);

  useEffect(() => {
    if (audioElement.current && resumeTime) {
      audioElement.current.currentTime = resumeTime;
    }
  }, [videoSource, resumeTime]);

  useEffect(() => {
    if (audioElement.current) {
      audioElement.current.playbackRate = voiceState.speed;
      voiceDispatch({
        payload: { point: audioElement.current.currentTime, ts: new Date().getTime() },
        type: 'AUDIO_ELEMENT_SPEED_CHANGED',
      });
    }
  }, [voiceState.speed]);

  const onExpandClick = () => {
    setExpanded(prev => !prev);
    expandCellRef.current.scrollTop = 0; // Scroll to top
  };
  const expandedHeight = height + 20 > defaultHeight ? height + 20 : defaultHeight;

  const { triggers: bodyTriggers, plainText: bodyText } =
    getTriggersFromMatchHighlight(highlightedBodyText);
  const hasTriggers = !!bodyTriggers?.length;
  return (
    <div styleName="outer">
      <div
        styleName="container"
        style={{ height: expanded ? `${expandedHeight}px` : `${defaultHeight}px` }}
      >
        <div
          ref={expandCellRef}
          styleName={expanded ? 'text text-expanded' : 'text text-collapsed'}
        >
          {voiceFileId ? (
            <div styleName="audioCell">
              {/* TODO find a better way to apply the styles */}
              {/* this <div /> is required even if its contents are null to apply styles to wavesurfer */}
              <div styleName={`${searchTerms.length ? 'matchedLexica' : 'matchedLexicaShort'}`}>
                {searchTerms.length ? (
                  <>
                    {`${formatMessage(messages.matched)}: `}
                    <HighlightedText text={searchTerms.join(', ')} highlights={searchTerms} />
                    <span styleName="contentDivider" />
                  </>
                ) : null}
              </div>
              {videoPlayLoading ? (
                <LoadingCentered size="smaller" />
              ) : (
                <IconButton
                  size="twenty"
                  color="blue"
                  onClick={handlePlayClick}
                  iconName={voiceState.playing ? 'controller-paus' : 'controller-play'}
                  isDisabled={isFetching || loadingVideoData || !recordingId || loadingWaveformData}
                />
              )}

              {!videoSource && (
                <div styleName="meetingDuration">
                  {formatMessage(messages.meetingDuration, {
                    displayText: data?.body?.displayText || '',
                    duration:
                      !!meetingDuration || meetingDuration === 0
                        ? convertMillisecondsToTimeString(meetingDuration * 1000)
                        : formatMessage(messages.meetingDurationNotFound),
                  })}
                </div>
              )}

              {/* eslint-disable-next-line jsx-a11y/media-has-caption */}
              <audio
                ref={audioElement}
                src={videoSource}
                onTimeUpdate={event => {
                  voiceDispatch({
                    payload: { time: event.target.currentTime },
                    type: 'AUDIO_TIME_UPDATED',
                  });
                }}
                onEnded={() => {
                  voiceDispatch({
                    type: 'PLAYBACK_ENDED',
                  });
                }}
                onError={err => setAudioError(err)}
              />
              {!audioError && !videoSourceError && videoSource && (
                <div styleName="audio">
                  <Waveform
                    loadingVideoData={loadingVideoData}
                    loading={loadingWaveformData}
                    data={waveformData}
                    playbackTime={voiceState.playbackTime}
                    onClickTime={handleClickTime}
                    onClickPause={handlePlayClick}
                    playing={voiceState.playing}
                    audioElement={audioElement}
                    height={18}
                    highlightedTimes={voiceState.highlightedTimes}
                  />
                </div>
              )}
              <IconButton
                size="twenty"
                color="blue"
                onClick={() =>
                  saveFile({
                    fileExtension: voiceFileType,
                    fileName: voiceFileId || voiceFileName,
                  })
                }
                iconName="download"
                loading={isFetching || loadingVideoData}
                isDisabled={!voiceFileId && !attachmentKey}
              />
            </div>
          ) : (
            <div styleName="noAudioCell">
              {formatMessage(messages.noAudioAvailable, {
                displayText: data?.body?.displayText || '',
                duration:
                  !!meetingDuration || meetingDuration === 0
                    ? convertMillisecondsToTimeString(meetingDuration * 1000)
                    : 'Not Found',
              })}
            </div>
          )}
          {data.body?.text ? (
            <>
              {hasTriggers ? (
                <HighlightedTextWithTriggers text={bodyText} triggers={bodyTriggers} />
              ) : (
                <HighlightedText
                  key="contentText"
                  text={
                    getVoiceTranscriptionText(data) === TRANSCRIPTION_NOT_ATTEMPTED
                      ? formatMessage(messages.transcriptionNotAttempted)
                      : getVoiceTranscriptionText(data)
                  }
                  highlights={searchTerms}
                />
              )}
            </>
          ) : (
            <span styleName="transcriptNotAvailable">
              {formatMessage(messages.transcriptNotAvailable)}
            </span>
          )}
        </div>
        <button type="button" onClick={onExpandClick} styleName="expandButton">
          {expanded
            ? `- ${formatMessage(messages.collapse)}`
            : `+ ${formatMessage(messages.expand)}`}
        </button>
      </div>
    </div>
  );
};

VoiceCell.propTypes = {
  data: PropTypes.shape({
    '&id': PropTypes.string,
    body: PropTypes.shape({
      displayText: PropTypes.string,
      text: PropTypes.string,
    }),
    meetingDetails: PropTypes.shape({
      type: PropTypes.string,
    }),
    meetingDuration: PropTypes.string,
    meetingDurationInSeconds: PropTypes.string,
    recordingId: PropTypes.string,
    recordings: PropTypes.arrayOf(
      PropTypes.shape({
        type: PropTypes.string.isRequired,
      })
    ),
  }).isRequired,
  defaultHeight: PropTypes.number,
  highlightedBodyText: PropTypes.string,
  highlights: PropTypes.arrayOf(PropTypes.string),
};

VoiceCell.defaultProps = {
  defaultHeight: 33,
  highlights: [],
};

export default VoiceCell;

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { FormattedMessage, useIntl } from 'react-intl';

import BooleanFormControl from 'view/components/form-control/boolean';
import ExportModalManager from 'view/components/export/modal-manager';
import ExportDropdownHeading from 'view/components/export/dropdown-heading';

import useToggle from 'js/hooks/useToggle';

import { INSIGHTS_MESSAGES } from 'constants/insights';
import Modal from '../modal/basic-modal/basic-modal';
import Button from '../button/basic-button';

import './insights-export.less';

const InsightsExport = ({
  className,
  exportType,
  includeDetailed,
  onExport,
  onIncludeDetailed,
  total,
  testId,
  disabled,
}) => {
  const intl = useIntl();
  const [showModalManager, setShowModalManager] = useState(false);
  const { isOpen, toggleOpen } = useToggle();

  const handleConfirmIncludeDetailed = () => setShowModalManager(true);

  const handleConfirm = () => onExport(exportType);

  const handleReset = () => {
    toggleOpen();
    setShowModalManager(false);
  };

  const formControlInput = {
    name: 'includeDetailedInsight',
    onChange: onIncludeDetailed,
    value: includeDetailed,
  };

  const exportBtnTitle = intl.formatMessage(INSIGHTS_MESSAGES.exportInsights);
  return (
    <span>
      <ExportDropdownHeading
        onClick={toggleOpen}
        disabled={disabled}
        className={className}
        title={exportBtnTitle}
        testId={testId}
      />
      <Modal show={isOpen} onHide={toggleOpen} dialogClassName="export-insight-dialog">
        {showModalManager ? (
          <ExportModalManager
            total={total}
            exportType={exportType}
            onCancel={handleReset}
            onConfirm={handleConfirm}
          />
        ) : (
          <div>
            <Modal.Header>
              <Modal.Title>
                <div styleName="modalTitle">
                  <p styleName="titleText">
                    <FormattedMessage id="insights.export.title" defaultMessage="Export Insight" />
                  </p>
                </div>
              </Modal.Title>
            </Modal.Header>

            <Modal.Body>
              <div styleName="content">
                <BooleanFormControl
                  input={formControlInput}
                  labelMessage={INSIGHTS_MESSAGES.includeDetailed}
                />
              </div>
            </Modal.Body>

            <Modal.Footer>
              <div styleName="buttons">
                <Button color="red" onClick={toggleOpen}>
                  <span className="icon icon-cross" />
                  <FormattedMessage id="form.button.cancel.label" defaultMessage="Cancel" />
                </Button>
                <Button color="green" onClick={() => handleConfirmIncludeDetailed()}>
                  <span className="icon icon-check" />
                  <FormattedMessage id="form.button.confirm.label" defaultMessage="Confirm" />
                </Button>
              </div>
            </Modal.Footer>
          </div>
        )}
      </Modal>
    </span>
  );
};

InsightsExport.propTypes = {
  className: PropTypes.string.isRequired,
  disabled: PropTypes.bool,
  exportType: PropTypes.array.isRequired,
  includeDetailed: PropTypes.bool.isRequired,
  onExport: PropTypes.func.isRequired,
  onIncludeDetailed: PropTypes.func.isRequired,
  testId: PropTypes.string,
  total: PropTypes.number.isRequired,
};

export default InsightsExport;

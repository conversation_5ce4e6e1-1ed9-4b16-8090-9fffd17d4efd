import React, { useState } from 'react';
import { useLocation } from 'react-router';
import PropTypes from 'prop-types';
import { useDispatch } from 'react-redux';

// Actions
import { downloadAttachment } from 'actions/comments/comments';

// Components
import SmallButton from 'view/components/small-button/small-button';
import PermissionWrapper from 'view/components/permission-wrapper/permission-wrapper';

// Hooks
import useUserPermissions from 'js/hooks/useUserPermissions';

// Constants
import { moduleMapper } from 'js/constants/roles-permissions';

// Styles
import styles from './user-comments.less';

const Attachment = ({ attachment, commentId }) => {
  const location = useLocation();
  const [downloading] = useState(false);
  const dispatch = useDispatch();
  const permissions = useUserPermissions(moduleMapper[location.pathname.split('/')[1]]);

  return (
    <div className={styles.downloadButton}>
      <PermissionWrapper permitted={permissions?.canDownload}>
        <SmallButton
          onClick={() => dispatch(downloadAttachment({ attachment, commentId }))}
          noBorder
          isDisabled={downloading || !permissions?.canDownload}
        >
          <span className={downloading ? styles.invisible : styles.filename}>
            {attachment.fileName}
          </span>
        </SmallButton>
      </PermissionWrapper>
    </div>
  );
};

Attachment.propTypes = {
  attachment: PropTypes.shape({
    fileInfo: PropTypes.shape({
      location: PropTypes.shape({
        key: PropTypes.string.isRequired,
      }),
    }),
    fileName: PropTypes.string.isRequired,
    fileType: PropTypes.string.isRequired,
  }).isRequired,
  commentId: PropTypes.string.isRequired,
};

export default Attachment;

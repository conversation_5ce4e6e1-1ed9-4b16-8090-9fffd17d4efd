import React, { type ReactNode } from 'react';
import type { MessageDescriptor } from 'react-intl';
import Label from 'view/components/form-control/label';
import FormControlContainer from 'view/components/form-control/container';
import RadioButtonInput from 'view/components/input/radio-button-input';
import './enum-radio-field.less';

type OptionType = {
  description?: string;
  label: string;
  value: string | number | boolean;
  subLabel?: string;
};

type EnumRadioFieldProps = {
  bsSize?: 'lg' | 'large' | 'sm' | 'small';
  disabled?: boolean;
  horizontal?: boolean;
  groupMessage?: ReactNode;
  Icon?: React.FC;
  iconClassName?: string;
  input: {
    name: string;
    onChange: (...args: unknown[]) => unknown;
    value: unknown;
  };
  labelMessage?: MessageDescriptor;
  meta?: {
    error?: MessageDescriptor;
    touched?: boolean;
    warning?: MessageDescriptor;
  };
  options: OptionType[];
  showMandatory?: boolean;
  showOptional?: boolean;
  className?: string;
};

const EnumRadioField: React.FC<EnumRadioFieldProps> = ({
  bsSize,
  disabled,
  horizontal = false,
  groupMessage,
  Icon,
  iconClassName,
  input,
  labelMessage,
  meta = {},
  options,
  showMandatory,
  showOptional,
  className,
}) => {
  return (
    <FormControlContainer {...meta} className={className}>
      {labelMessage && (
        <Label
          message={labelMessage}
          Icon={Icon}
          bsSize={bsSize}
          showMandatory={showMandatory}
          showOptional={showOptional}
          iconClassName={iconClassName}
        />
      )}
      <div styleName={`options${horizontal ? '-horizontal' : ''}`}>
        {groupMessage || null}
        {options.map(option => (
          <RadioButtonInput
            key={String(option.value)}
            labelMessage={option.label}
            name={input.name}
            checked={input.value === option.value}
            onChange={() => input.onChange(option.value)}
            description={option.description}
            disabled={disabled}
            subLabel={option.subLabel}
          />
        ))}
      </div>
    </FormControlContainer>
  );
};

export default EnumRadioField;

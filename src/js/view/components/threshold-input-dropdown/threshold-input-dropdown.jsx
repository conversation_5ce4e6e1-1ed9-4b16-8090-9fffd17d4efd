import React from 'react';
import PropTypes from 'prop-types';
import isNumber from 'lodash/isNumber';

import DropdownButton from 'js/view/components/basic/dropdown-button';

import ThresholdInputBox from 'view/components/threshold-value/threshold-input-box';
import InputGroup from '../basic/input-group';
import MenuItem from '../basic/menuitem';

import './threshold-input-dropdown.less';

const ThresholdInputDropdown = ({
  value,
  params,
  onValueChange,
  options,
  onUnitChange,
  className,
  testId,
}) => {
  const { maxValue, minValue, unit } = params;

  const displayVal = isNumber(value) ? (Math.round(value * 100) / 100).toString() : value;

  return (
    <InputGroup styleName="current-value-input" className={className}>
      <ThresholdInputBox
        displayValue={displayVal}
        max={maxValue}
        min={minValue}
        onValueChange={onValueChange}
        unit={unit}
        testId={testId}
      />
      <DropdownButton
        id="threshold-input-dropdown"
        bsSize="small"
        bsStyle="default"
        className="btn-default-outline"
        onSelect={onUnitChange}
        styleName="threshold-input-dropdown"
        title={unit}
        testId={`${testId}.Dropdown`}
      >
        {options.map(option => (
          <MenuItem key={option.value} eventKey={option.value} active={unit === option.value}>
            {option.label}
          </MenuItem>
        ))}
      </DropdownButton>
    </InputGroup>
  );
};

ThresholdInputDropdown.propTypes = {
  className: PropTypes.string,
  onUnitChange: PropTypes.func.isRequired,
  onValueChange: PropTypes.func.isRequired,
  options: PropTypes.array.isRequired,
  params: PropTypes.shape({
    maxValue: PropTypes.number.isRequired,
    minValue: PropTypes.number.isRequired,
    unit: PropTypes.string.isRequired,
  }).isRequired,
  testId: PropTypes.string,
  value: PropTypes.number.isRequired,
};

export default ThresholdInputDropdown;

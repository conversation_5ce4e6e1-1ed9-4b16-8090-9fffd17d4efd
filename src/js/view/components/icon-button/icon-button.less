@import 'less/site.less';

// btn basic styles
.btn-basic(@bgColor,@color,@bgHoverColor,@hoverColor) {
  border-radius: 4px;
  text-align: center;
  outline: none;
  border: 1px solid transparent;
  background-color: @bgColor;
  color: @color;
  font-weight: normal;
  white-space: nowrap;
  box-shadow: 0 0 10px 0 @black;
  display: flex;
  align-items: center;
  justify-content: center;
  svg {
    filter: brightness(4);
  }
  &:hover {
    background-color: @bgHoverColor;
    color: @hoverColor;
    svg {
      filter: brightness(4);
    }
  }
  &:disabled {
    cursor: default;
    svg {
      filter: grayscale(0.6) brightness(0.7);
    }
    &:hover {
      svg {
        filter: grayscale(0.6) brightness(0.7);
      }
    }
  }
}
.btn-basic-outline(@bgColor,@color,@bgHoverColor,@hoverColor) {
  background-color: @bgColor;
  color: @color;
  border: 1px solid @color;
  border-radius: 4px;
  font-weight: normal;
  white-space: nowrap;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
  &:hover {
    background-color: @bgHoverColor;
    color: @hoverColor;
    svg {
      filter: brightness(4);
    }
  }
  &:disabled {
    cursor: default;
    svg {
      filter: grayscale(0.6) brightness(0.7);
    }
    &:hover {
      svg {
        filter: grayscale(0.6) brightness(0.7);
      }
    }
  }
}

// solid button classes
.blue {
  .btn-basic(@blue,@full-white,@dark-blue,@full-white);
}
.green {
  .btn-basic(@green, @full-white, @dark-green, @full-white);
}
.red {
  .btn-basic(@red, @full-white, @dark-red, @full-white);
}
.yellow {
  .btn-basic(@yellow, @full-white, @dark-yellow, @full-white);
}
.white {
  .btn-basic(@full-white, @gray-dark, @full-white, @gray-dark);
}
.default {
  .btn-basic(@gray-dark, @full-white, @gray-dark, @full-white);
}

// outline button classes
.blue-outline {
  .btn-basic-outline(transparent,@blue,@blue,@full-white);
}
.green-outline {
  .btn-basic-outline(transparent,@green,@green,@full-white);
}
.red-outline {
  .btn-basic-outline(transparent,@red,@red,@full-white);
}
.yellow-outline {
  .btn-basic-outline(transparent,@yellow,@yellow,@full-white);
}
.white-outline {
  .btn-basic-outline(transparent,@full-white,@full-white,@gray-dark);
}
.default-outline {
  .btn-basic-outline(transparent,@gray-dark,@gray-dark,@full-white);
}

.medium {
  width: 35px;
  min-width: 35px;
  height: 35px;
}
.large {
  width: max-content;
  height: 35px;
  & > * {
    margin-right: 5px;
  }
}

.smaller {
  width: 19px;
  min-width: 19px;
  height: 19px;
  font-size: 9px;
  & > span::before {
    top: 1px; /* push it up to make sure it looks vertically centered */
  }
}

.twenty {
  width: 20px;
  min-width: 20px;
  height: 20px;
  font-size: 12px;
  & > span::before {
    top: 2px; /* push it up to make sure it looks vertically centered */
  }
}

.spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 20px;
  min-width: 20px;
  height: 20px;
}

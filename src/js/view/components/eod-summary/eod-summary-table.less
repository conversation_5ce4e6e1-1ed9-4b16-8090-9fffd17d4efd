@import 'less/site.less';
@import 'less/tables.less';

.tableBody,
.tableHeader {
  display: grid;
  grid-template-columns: 90px repeat(4, minmax(80px, auto)) 100px;
}

.tableHeader {
  position: sticky;
  top: 0;
}

.cells {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid @gray-darker;
  padding: 5px;
  font-size: 12px;
  color: @gray-light;
}

.headerCell {
  background-color: @widget-base;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 10px 0;
  width: 100%;
  border-bottom: 1px solid @gray-dark;
}

.headerCellWithPartition,
.cellWithPartition {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 0;
  border-left: 1px solid @gray-dark;
  color: @gray-light;
}

.cellWithPartition {
  padding: 0;
}

.partitionCellContainer {
  padding-left: 0;
}

.placeholderForScrolling {
  grid-column: ~'1/-1';
  height: 0;
}

.centered {
  grid-column-start: 1;
  grid-column-end: 7;
}

.error {
  width: 500px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: @red;
  font-size: 14px;
  span {
    margin-right: 3px;
  }
}

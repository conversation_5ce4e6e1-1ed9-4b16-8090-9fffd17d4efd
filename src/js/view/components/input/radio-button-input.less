@import 'less/site.less';

.radio-container {
  margin-right: 10px;
  margin-left: 0 !important;
  &:first-of-type {
    margin-right: 10px;
  }
  .option {
    font-size: 14px;
    margin-left: 8px;
    position: relative;
    bottom: 4px;
  }
  .description {
    margin: 5px 0;
    font-size: 15px;
    opacity: 0.4;
    color: @gray-light;
    margin-left: 25px;
  }

  .sub-label {
    margin: 0 5px;
    font-size: 10px;
    color: @gray-light;
    opacity: 0.4;
    display: inline-block;
    font-style: italic;
    position: relative;
    bottom: 4px;
  }
  :global .disabled .custom-control-indicator {
    background-color: #2d313c !important;
  }
}

.disabled {
  opacity: 0.5;
}

.active {
  color: @active;
}

.labelMessage {
  margin-left: 8px;
  position: relative;
  bottom: 3px;
}

.red {
  color: @red;
}

.green {
  color: @green;
}

.yellow {
  color: @yellow;
}

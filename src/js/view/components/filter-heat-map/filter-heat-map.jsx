import React, { useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import take from 'lodash/take';
import { useLocation } from 'react-router';

import AutosizeContainer from 'view/components/widget/autosize-container';
import useFormatNumber from 'js/hooks/useFormatNumber';
import HeatMap from 'view/components/heat-map';
import useMultiSelectUrlFilter from 'js/hooks/useMultiSelectUrlFilter';
import FilterGraphWithTitleContainer from 'view/components/filter-graph-with-title-container/filter-graph-with-title-container';

import styles from './filter-heat-map.less';

const Y_LABELS = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];

const X_LABELS = [
  '',
  '',
  '',
  '',
  '',
  '',
  '6',
  '',
  '',
  '',
  '',
  '',
  '12',
  '',
  '',
  '',
  '',
  '',
  '18',
  '',
  '',
  '',
  '',
  '',
];

const ToolTip = data => {
  const { safeFormatNumber } = useFormatNumber();
  if (!data) {
    return null;
  }
  return (
    <>
      <p>
        <strong>
          {moment.weekdays(data.day)} {data.count}
        </strong>
      </p>
      {data.details &&
        take(data.details, 10).map(datum => (
          <p key={datum.channel}>{`${datum.channel} : ${safeFormatNumber(datum.count)}`}</p>
        ))}
    </>
  );
};

const FilterHeatMap = ({
  data,
  loading,
  queryName,
  replaceUrl,
  subTitle,
  testId,
  title,
  urlArrayFormat,
  customHeight,
}) => {
  const { getSelectedKeys, onFilterUpdated } = useMultiSelectUrlFilter({
    replaceUrl,
    selectedKeysQueryName: queryName,
    urlArrayFormat,
  });

  const location = useLocation();

  const selectedKeys = useMemo(
    () => getSelectedKeys().map(key => parseInt(key, 10)),
    [location.search]
  );

  const showLoader = loading && !data?.length;

  const handleFiltering = useCallback(
    node => {
      onFilterUpdated(node.id?.toString(), queryName);
    },
    [onFilterUpdated, queryName]
  );

  return (
    <FilterGraphWithTitleContainer
      hasData={!!data?.length}
      showLoader={showLoader}
      subTitle={subTitle}
      testId={testId}
      title={title}
      loading={loading}
      customHeight={customHeight}
    >
      <div className={styles.heatMapContainer}>
        <AutosizeContainer>
          <HeatMap
            nodes={data}
            xLabels={X_LABELS}
            yLabels={Y_LABELS}
            selectedIds={selectedKeys}
            onToggleNode={handleFiltering}
            renderTooltip={ToolTip}
          />
        </AutosizeContainer>
      </div>
    </FilterGraphWithTitleContainer>
  );
};

FilterHeatMap.propTypes = {
  customHeight: PropTypes.string,
  data: PropTypes.array,
  loading: PropTypes.bool,
  queryName: PropTypes.string.isRequired,
  replaceUrl: PropTypes.bool,
  subTitle: PropTypes.string,
  testId: PropTypes.string,
  title: PropTypes.string,
  urlArrayFormat: PropTypes.string,
};

FilterHeatMap.defaultProps = {
  data: [],
  loading: false,
  replaceUrl: true,
  testId: 'Components.FilterDonutWithTitle',
  urlArrayFormat: 'comma',
};

export default FilterHeatMap;

import React from 'react';
import PropTypes from 'prop-types';

import Link from 'view/components/new-link/new-link';
import { EMPTY_DISPLAY_VALUE } from 'js/constants/property';

import styles from './link-cell.less';

/**
 * Renders text in a link in a css grid element that is truncated if it is too long
 * Should normally be used inside a CSS grid table
 * @example
 * <LinkCell to="/some/path">
 *   This is a link cell
 * </LinkCell>
 */
const LinkCell = ({
  locationState,
  to,
  query,
  className,
  children,
  title,
  replace,
  centered,
  onClick,
  color,
}) => (
  <Link
    to={to}
    query={query}
    className={className || styles.cell}
    replace={replace}
    color={color}
    onClick={onClick}
    locationState={locationState}
  >
    <div
      className={centered ? styles.centeredText : styles.text}
      title={
        title ||
        (typeof children === 'string' || typeof children === 'number' ? children : undefined)
      }
      data-test-id="Components.GridTable.LinkCell"
    >
      {children || EMPTY_DISPLAY_VALUE}
    </div>
  </Link>
);

LinkCell.propTypes = {
  centered: PropTypes.bool,
  children: PropTypes.node,
  className: PropTypes.string,
  color: PropTypes.string,
  locationState: PropTypes.object,
  onClick: PropTypes.func,
  query: PropTypes.object,
  replace: PropTypes.bool,
  title: PropTypes.string,
  to: PropTypes.oneOfType([PropTypes.string, PropTypes.object, PropTypes.func]),
};

LinkCell.defaultProps = {
  centered: undefined,
  children: null,
  className: undefined,
  replace: undefined,
  title: undefined,
};

export default LinkCell;

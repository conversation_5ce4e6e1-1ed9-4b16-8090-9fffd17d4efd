@import 'less/tables.less';

.cell {
  .standardCell();
}

.text {
  .standardText();
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.child {
  overflow: hidden;
  text-overflow: ellipsis;
}
.centeredText {
  .standardText();
  width: 100%;
  text-align: center;
}

.verticalDivider {
  display: inline-block;
  overflow: hidden;
  width: 1px;

  min-height: 20px;
  background-color: @gray;
  margin: 0 10px;
}

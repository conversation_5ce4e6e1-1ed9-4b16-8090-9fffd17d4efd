import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useLocation } from 'react-router';
import queryString from 'query-string';
import { connect } from 'react-redux';

import { withRouter } from 'util/withRouter';
import * as permissionsSelectors from 'js/redux/selectors/permissions';
import { getAllSuggestions } from 'actions/trade-reconstruction/suggestions.actions';

import SuggestionsNotification from 'view/components/suggested-comms/suggestions-notification/suggestions-notification';
import AddToCaseButton from 'view/components/button/add-to-case-button';

import styles from './suggestions-controls.less';

const SuggestionsControls = ({
  userPermissions,
  suggestionsCount,
  getSuggestions,
  match,
  flex,
}) => {
  const location = useLocation();
  const query = queryString.parse(location.search);
  const { id } = match.params;
  useEffect(() => {
    getSuggestions(id);
  }, [id, getSuggestions]);

  const suggestionsVisible = !!query.suggestions;
  return (
    <div className={styles.container}>
      {userPermissions.hasCaseManager && suggestionsVisible && <AddToCaseButton />}
      {userPermissions.hasComms && suggestionsCount !== undefined && (
        <SuggestionsNotification count={suggestionsCount} flex={flex} />
      )}
    </div>
  );
};

SuggestionsControls.propTypes = {
  flex: PropTypes.bool,
  getSuggestions: PropTypes.func.isRequired,
  match: PropTypes.shape({
    params: PropTypes.shape({
      id: PropTypes.string.isRequired,
    }),
  }),
  suggestionsCount: PropTypes.number,
  userPermissions: PropTypes.shape({
    hasCaseManager: PropTypes.bool,
    hasComms: PropTypes.bool,
  }),
};

const mapStateToProps = (state, ownProps) => ({
  suggestionsCount:
    state.tradeReconstruction.suggestions[ownProps.match.params.id]?.data?.header?.totalHits,
  userPermissions: permissionsSelectors.userPermissions(state),
});

const mapDispatchToProps = {
  getSuggestions: getAllSuggestions,
};

export default withRouter(connect(mapStateToProps, mapDispatchToProps)(SuggestionsControls));

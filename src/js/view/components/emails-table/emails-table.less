@import 'less/site.less';
@import 'less/tables.less';

.table {
  .standardTable();
  grid-template-columns: 56px repeat(2, auto);
  min-width: 100%;
  max-height: 65vh;
  overflow-x: hidden;
  overflow-y: auto;
}
.newTable {
  .standardTable();
  grid-template-columns: 56px repeat(2, auto);
  min-width: 100%;
  max-height: 65vh;
  overflow-x: hidden;
  overflow-y: auto;
  border: 1px solid @gray-dark;
  border-radius: 4px;

  > .headerCell {
    background-color: @dark-background;
  }
}
.tableLoading {
  .standardTableLoading();
}

.headerCell {
  .standardHeaderCell();
}

.headerCellCentered {
  .centeredHeaderCell();
}

.cell {
  .standardCell();
}

.cellWithButton {
  justify-content: space-between;
}

.icon {
  padding-right: 8px;
}

.capitalize {
  text-transform: capitalize;
}

.warning-list {
  margin: 0;
  padding: 0 0 0 16px;
}

.errorMessage {
  color: @red;
  font-size: 14px;
}

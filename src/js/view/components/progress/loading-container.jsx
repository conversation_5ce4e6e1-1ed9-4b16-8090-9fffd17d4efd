import React from 'react';
import PropTypes from 'prop-types';

import './loading-container.less';

const LoadingContainer = ({ loading, children, className }) => (
  <div styleName={`container${loading ? '-loading' : ''}`} className={className}>
    {children}
  </div>
);

LoadingContainer.propTypes = {
  children: PropTypes.any,
  className: PropTypes.string,
  loading: PropTypes.bool,
};

export default LoadingContainer;

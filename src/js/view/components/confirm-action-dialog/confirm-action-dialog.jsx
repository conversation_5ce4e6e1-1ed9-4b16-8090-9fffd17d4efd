import React from 'react';
import PropTypes from 'prop-types';
import { useIntl } from 'react-intl';

import { MESSAGES as formMessages } from 'constants/form';

import Button from 'js/view/components/button/basic-button';
import Modal from 'js/view/components/modal/basic-modal/basic-modal';

import './confirm-action-dialog.less';

const ConfirmActionDialog = ({ children, title, onConfirm, onClose }) => {
  const { formatMessage } = useIntl();
  return (
    <Modal backdrop="static" dialogClassName="confirm-action-dialog" show onHide={onClose}>
      <Modal.Header>
        <Modal.Title>
          <div styleName="modalTitle">
            <p styleName="titleText">{title}</p>
          </div>
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div styleName="content">{children}</div>
      </Modal.Body>
      <Modal.Footer>
        <div styleName="buttons">
          <Button color="red" onClick={onClose}>
            <span className="icon icon-cross" />
            {formatMessage(formMessages.cancel)}
          </Button>
          <Button color="green" onClick={onConfirm}>
            <span className="icon icon-check" />
            {formatMessage(formMessages.confirm)}
          </Button>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

ConfirmActionDialog.propTypes = {
  children: PropTypes.node.isRequired,
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
};

export default ConfirmActionDialog;

import React from 'react';
import SmallButton from './small-button.jsx';

const FakeIcon = () => (
  <svg x="-16" y="-16" viewBox="0 0 32 32" version="1.1">
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(-29.000000, -746.000000)" stroke="#1BC98E">
        <g transform="translate(30.000000, 747.000000)">
          <g>
            <circle fill="#313540" cx="15" cy="15" r="15" />
            <path
              d="M15,30 C23.2842712,30 30,23.2842712 30,15 C30,6.71572875 23.2842712,0 15,0 C15,4.4771525 15,9.4771525 15,15 C15,20.5228475 15,25.5228475 15,30 Z"
              fill="#1BC98E"
            />
          </g>
        </g>
      </g>
    </g>
  </svg>
);

export default {
  component: SmallButton,
  title: 'SmallButton',
  argTypes: {
    onClick: { action: 'clicked' },
    color: { control: { type: 'select', options: ['blue', 'red', 'green', 'yellow', 'gray'] } },
    size: { control: { type: 'select', options: ['tiny', 'smaller', 'row', 'small', 'medium'] } },
  },
};

const Template = args => <SmallButton {...args} />;

export const Default = Template.bind({});
Default.args = {
  children: 'Click me',
  color: 'blue',
  size: 'small',
  isDisabled: false,
  selected: false,
  iconName: '',
  darker: false,
};

export const Link = Template.bind({});
Link.args = {
  to: '/abc',
  darker: false,
  children: 'Link button',
};

export const Blue = Template.bind({});
Blue.args = {
  children: 'Blue button',
  color: 'blue',
};

export const Green = Template.bind({});
Green.args = {
  children: 'Green button',
  color: 'green',
};

export const Red = Template.bind({});
Red.args = {
  children: 'Red button',
  color: 'red',
};

export const Yellow = Template.bind({});
Yellow.args = {
  children: 'Yellow button',
  color: 'yellow',
};

export const Gray = Template.bind({});
Gray.args = {
  children: 'Grey button',
  color: 'gray',
};

export const Darker = Template.bind({});
Darker.args = {
  children: 'Darker button',
  darker: true,
  color: 'blue',
  selected: false,
};

export const Medium = Template.bind({});
Medium.args = {
  children: 'Medium',
  size: 'medium',
};

export const Small = Template.bind({});
Small.args = {
  children: 'Small',
  size: 'small',
};

export const Row = Template.bind({});
Row.args = {
  children: 'Row',
  size: 'row',
};

export const Smaller = Template.bind({});
Smaller.args = {
  children: 'Smaller',
  size: 'smaller',
};

export const Tiny = Template.bind({});
Tiny.args = {
  children: 't',
  size: 'tiny',
};

export const Disabled = Template.bind({});
Disabled.args = {
  children: 'Disabled',
  isDisabled: true,
};

export const Selected = Template.bind({});
Selected.args = {
  children: 'Selected',
  selected: true,
};

export const NoBorder = Template.bind({});
NoBorder.args = {
  children: 'No border',
  noBorder: true,
};

export const WithIcon = Template.bind({});
WithIcon.args = {
  children: 'With icon',
  iconName: 'paper-plane',
};

export const WithSvgIcon = Template.bind({});
WithSvgIcon.args = {
  children: 'With SVG icon',
  svgIcon: <FakeIcon />,
};

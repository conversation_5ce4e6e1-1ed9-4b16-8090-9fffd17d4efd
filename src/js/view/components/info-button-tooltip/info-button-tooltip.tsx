import React, { useRef, useState } from 'react';
import cx from 'classnames';

import Tooltip from 'view/components/tooltip';

import styles from './info-button-tooltip.less';

interface SizeClassNames {
  medium?: string;
  small?: string;
  xSmall?: string;
}

interface ColorClassNames {
  blue?: string;
  green?: string;
  red?: string;
  yellow?: string;
  gray?: string;
}

interface TooltipComponentProps {
  text: string | React.ReactNode;
}

const TooltipComponent: React.FC<TooltipComponentProps> = ({ text }) => {
  return (
    <div>
      <div className={styles.tooltipContent}>{text}</div>
    </div>
  );
};

interface InfoButtonTooltipProps {
  text: string | React.ReactNode;
  size?: keyof SizeClassNames;
  selected?: boolean;
  color?: keyof ColorClassNames;
  className?: string;
}

const InfoButtonTooltip: React.FC<InfoButtonTooltipProps> = ({
  text,
  size = 'medium',
  selected,
  color = 'blue',
  className,
}) => {
  const tooltipBtn = useRef<HTMLButtonElement>(null);
  const [showTooltip, setShowTooltip] = useState(false);
  const [target, setTarget] = useState<DOMRect | null>(null);

  const handleMouseMove = () => {
    if (!showTooltip && tooltipBtn.current) {
      setShowTooltip(true);
      setTarget(tooltipBtn.current.getBoundingClientRect());
    }
  };

  const handleMouseOut = () => {
    setTimeout(() => {
      if (showTooltip && tooltipBtn.current) {
        setShowTooltip(false);
      }
    }, 100);
  };

  return (
    <>
      <button
        ref={tooltipBtn}
        styleName="button"
        className={cx(className, styles.button, {
          [styles.medium]: size === 'medium',
          [styles.small]: size === 'small',
          [styles.xSmall]: size === 'xSmall',
          [styles.selected]: selected,
          [styles.blue]: color === 'blue',
          [styles.green]: color === 'green',
          [styles.red]: color === 'red',
          [styles.yellow]: color === 'yellow',
          [styles.gray]: color === 'gray',
        })}
        onMouseEnter={handleMouseMove}
        onMouseLeave={handleMouseOut}
        type="button"
      >
        <span className="icon icon-info" />
      </button>

      <Tooltip
        color={color}
        show={showTooltip}
        target={target}
        data={text}
        onMouseEnteredToolTip={() => setShowTooltip(true)}
        onMouseExitedToolTip={() => setShowTooltip(false)}
      >
        {(tooltipText: string) => <TooltipComponent text={tooltipText} />}
      </Tooltip>
    </>
  );
};

export default InfoButtonTooltip;

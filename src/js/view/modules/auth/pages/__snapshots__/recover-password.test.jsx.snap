// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`should render the initial step correctly 1`] = `
<ModalDialog>
  <ModalHeader
    size="normal"
  >
    <Row>
      <Col
        xs={8}
        xsPush={2}
      >
        <ModalTitle />
      </Col>
    </Row>
  </ModalHeader>
  <ReduxForm
    destroyOnUnmount={true}
    enableReinitialize={true}
    forceUnregisterOnUnmount={false}
    form="RegisteredEmail"
    getFormState={[Function]}
    initialValues={
      Object {
        "email": "",
      }
    }
    keepDirtyOnReinitialize={false}
    persistentSubmitErrors={false}
    pure={true}
    shouldAsyncValidate={[Function]}
    shouldError={[Function]}
    shouldValidate={[Function]}
    shouldWarn={[Function]}
    submitAsSideEffect={false}
    touchOnBlur={false}
    touchOnChange={false}
    updateUnregisteredFields={false}
  />
</ModalDialog>
`;

exports[`should render the success step correctly 1`] = `
<ModalDialog>
  <ModalHeader
    size="normal"
  >
    <Row>
      <Col
        xs={8}
        xsPush={2}
      >
        <ModalTitle />
      </Col>
    </Row>
  </ModalHeader>
  <ReduxForm
    destroyOnUnmount={true}
    enableReinitialize={false}
    forceUnregisterOnUnmount={false}
    form="RecoverPasswordSuccess"
    getFormState={[Function]}
    keepDirtyOnReinitialize={false}
    persistentSubmitErrors={false}
    pure={true}
    shouldAsyncValidate={[Function]}
    shouldError={[Function]}
    shouldValidate={[Function]}
    shouldWarn={[Function]}
    submitAsSideEffect={false}
    touchOnBlur={false}
    touchOnChange={false}
    updateUnregisteredFields={false}
  />
</ModalDialog>
`;

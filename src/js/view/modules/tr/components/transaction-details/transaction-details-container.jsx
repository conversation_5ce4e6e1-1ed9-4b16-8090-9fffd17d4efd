import React from 'react';
import PropTypes from 'prop-types';
import { useIntl } from 'react-intl';
import get from 'lodash/get';

import { PROPERTY_MESSAGES as messages } from 'constants/property';
import { yesOrNo } from 'js/util/string';
import { MARKET_ASSIGN_TO_IDENTIFIER_TYPES } from 'constants/market';
import { getFormattedTime } from 'util/time';

import TransactionDetails from './transaction-details';

const SOURCE_KEY_REPLACEMENT_STRING = /s3:.*\/feeds\//;

const formatTimeWithMicroSeconds = (timeValue, format = 'D MMM YYYY, HH:mm:ss', isUtc = false) => {
  if (!timeValue) return null;
  else {
    // get full precision of timestamp, e.g., "2023-03-03T10:09:48.718000Z" returns "3 Mar 2023, 15:39:48.718000"
    const microseconds = timeValue?.split('.')?.[1]?.replace('Z', '');
    return microseconds
      ? `${getFormattedTime(timeValue, format, isUtc)}.${microseconds}`
      : getFormattedTime(timeValue, format, isUtc);
  }
};

const mapTransactionDataToCards = (comments = [], data, { errors = [], intl }) => {
  const { formatMessage } = intl;

  const matchError = name => errors.find(err => err.field_path === name);
  const matchErrors = names => errors.filter(err => names.includes(err.field_path));
  const matchMarketIdentifier = name => {
    /**
     * The structure of the marketIdentifiers in RTS22 Transaction is now an array of objects:
     * [{ labelId: string, path: string, type: string }]
     *
     * We are only interested in the value of 'labelId' where 'path === name'
     */
    const found = data?.transaction?.marketIdentifiers
      ? data.transaction.marketIdentifiers.find(id => id.path === name)
      : undefined;

    return found?.labelId || undefined;
  };

  const getAssignIdentifier = name => {
    const labelInfo = matchMarketIdentifier(name)?.split(':');
    return {
      id: labelInfo && `${labelInfo[0]}:${labelInfo[1]}`,
      identifierType: MARKET_ASSIGN_TO_IDENTIFIER_TYPES.TRADE_FILE_IDENTIFIER,
      label: labelInfo?.[0],
    };
  };

  const getMifirEligibilityDescription = mifirEligibility => {
    if (!mifirEligibility) return;

    let mifirEligibilityMessage;

    const { totv, utotv, eligible } = mifirEligibility;

    if (totv && utotv) {
      mifirEligibilityMessage = formatMessage(messages.mifirEligibleToTVandUtoTvOnExecutionDate);
    } else if (!eligible) {
      mifirEligibilityMessage = formatMessage(
        messages.mifirEligibleNeitherToTvOrUToTvOnExecutionDate
      );
    } else if (totv) {
      mifirEligibilityMessage = formatMessage(messages.mifirEligibleToTvOnExecutionDate);
    } else if (utotv) {
      mifirEligibilityMessage = formatMessage(messages.mifirEligibleUtoTvOnExecutionDate);
    } else {
      mifirEligibilityMessage = formatMessage(
        messages.mifirEligibleNeitherToTvOrUToTvOnExecutionDate
      );
    }

    return mifirEligibilityMessage;
  };

  const showFirdsRegisterLink = mifirEligibility => {
    if (!mifirEligibility) return;

    const { onFirds, underlyingOnFirds } = mifirEligibility;

    return onFirds || underlyingOnFirds;
  };

  const displayFileIdentifierEdit = marketIdentifier => {
    const val = matchMarketIdentifier(marketIdentifier);
    if (!val)
      return {
        name: marketIdentifier,
        type: 'fileIdentifier',
      };
  };

  const getIdentifierType = record => {
    const model = record['&model'] || record['&key']?.split(':')[0];
    switch (model) {
      case 'AccountFirm':
      case 'MarketCounterparty':
        return 'firmIdentifiers';
      case 'AccountPerson':
      case 'MarketPerson':
        return 'officialIdentifiers';
      default:
        return 'officialIdentifiers';
    }
  };

  const getUnderlyingInstrumentDerivativePath = fieldName => {
    let derivativeIndex = (
      get(data.transaction, 'instrumentDetails.instrument.ext.underlyingInstruments') || []
    ).findIndex(instrument => 'derivative' in instrument);

    // If the value isn't present in the record then return the zero index position path
    // else it will lead to conflicts while editing the values
    if (derivativeIndex < 0) {
      derivativeIndex = 0;
    }
    return `instrumentDetails.instrument.ext.underlyingInstruments[${derivativeIndex}].derivative.${fieldName}`;
  };

  // This is a generic function to get the value of the instrument derivative fields.
  // In future, if we have more fields to consider both base instrument and underlying instrument use this function.
  const getInstrumentDerivative = fieldName => {
    const basePath = `instrumentDetails.instrument.derivative.${fieldName}`;
    const baseValue = get(data.transaction, basePath);
    // if base value is present then return the base path and value
    if (baseValue) {
      return { path: basePath, value: baseValue };
    } else {
      // else look for the underlying instrument value
      const underlyingPath = getUnderlyingInstrumentDerivativePath(fieldName);
      const underlyingValue = get(data.transaction, underlyingPath);
      if (underlyingValue) {
        return { path: underlyingPath, value: underlyingValue };
      } else {
        return { path: basePath, value: null };
      }
    }
  };

  // This is a generic function to get the field data of the instrument derivative fields.
  // In future, if we have more fields to consider both base instrument and underlying instrument use this function.
  const getInstrumentDerivativeFieldData = ({
    fieldName,
    message,
    getValue,
    editType,
    errorPaths,
  }) => {
    const { path, value } = getInstrumentDerivative(fieldName) || {};
    return {
      label: formatMessage(message),
      error: errorPaths ? matchErrors([path, ...errorPaths]) : matchError(path),
      value: getValue ? getValue(value) : value,
      edit: {
        name: path,
        type: editType,
      },
    };
  };

  const mappedData = data
    ? {
        comments,
        generalFields: {
          title: formatMessage(messages.generalFields),
          errors: matchErrors(['reportDetails']),
          data: [
            {
              label: formatMessage(messages.transactionRefNo),
              error: matchError('reportDetails.transactionRefNo'),
              value: get(data.transaction, 'reportDetails.transactionRefNo'),
            },
            {
              label: formatMessage(messages.tradingVenueTransactionIdCode),
              error: matchError('reportDetails.tradingVenueTransactionIdCode'),
              value: get(data.transaction, 'reportDetails.tradingVenueTransactionIdCode'),
              edit: {
                name: 'reportDetails.tradingVenueTransactionIdCode',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.investmentFirmCoveredDirective),
              error: matchError('reportDetails.investmentFirmCoveredDirective'),
              value: yesOrNo(get(data.transaction, 'reportDetails.investmentFirmCoveredDirective')),
            },
            {
              label: formatMessage(messages.fileIdentifier),
              error: matchError('parties.executingEntity'),
              value: matchMarketIdentifier('parties.executingEntity'),
              edit: displayFileIdentifierEdit('parties.executingEntity'),
            },
            {
              label: formatMessage(messages.executingEntity),
              error: matchError('parties.executingEntity.name'),
              value: get(data.transaction, 'parties.executingEntity.name'),
              assign: {
                ...getAssignIdentifier('parties.executingEntity'),
                type: 'all',
              },
            },
            {
              label: formatMessage(messages.sourceKey),
              error: matchError('sourceKey'),
              value: get(data.transaction, 'sourceKey', '').replace(
                SOURCE_KEY_REPLACEMENT_STRING,
                ''
              ),
            },
          ],
        },

        // Has array data.. needs revisit.
        buyers: (get(data.transaction, 'parties.buyer') || [{}]).map((buyer, index) => {
          const identifierType = getIdentifierType(buyer);
          return {
            title: formatMessage(messages.buyer),
            errors: matchErrors(['parties.buyer']),
            data: [
              {
                label: formatMessage(messages.fileIdentifier),
                error: matchError('buyerFileIdentifier'),
                value: matchMarketIdentifier('parties.buyer'),
                edit: displayFileIdentifierEdit('parties.buyer'),
              },
              {
                label: formatMessage(messages.name),
                error: matchError(`parties.buyer.${index}.&key`),
                value: get(buyer, 'name'),
                assign: {
                  ...getAssignIdentifier('parties.buyer'),
                  type: 'any',
                },
              },
              {
                label: formatMessage(messages.branchCountry),
                error: matchError(`parties.buyer.${index}.${identifierType}.branchCountry`),
                value: get(buyer, `${identifierType}.branchCountry`),
              },
            ],
          };
        }),

        // Has array data.. needs revisit.
        buyerDecisionMakers: (get(data.transaction, 'parties.buyerDecisionMaker') || [{}]).map(
          (buyerDecisionMaker, index) => {
            const identifierType = getIdentifierType(buyerDecisionMaker);
            return {
              title: formatMessage(messages.buyerDecisionMaker),
              errors: matchErrors(['parties.buyerDecisionMaker']),
              data: [
                {
                  label: formatMessage(messages.fileIdentifier),
                  error: matchError('buyerDecisionMakerFileIdentifier'),
                  value: matchMarketIdentifier('parties.buyerDecisionMaker'),
                  edit: displayFileIdentifierEdit('parties.buyerDecisionMaker'),
                },
                {
                  label: formatMessage(messages.name),
                  error: matchError('name'),
                  value: get(buyerDecisionMaker, 'name'),
                  assign: {
                    ...getAssignIdentifier('parties.buyerDecisionMaker'),
                    type: 'any',
                  },
                },
                {
                  label: formatMessage(messages.branchCountry),
                  error: matchError(
                    `parties.buyerDecisionMaker.${index}.${identifierType}.branchCountry`
                  ),
                  value: get(buyerDecisionMaker, `${identifierType}.branchCountry`),
                },
              ],
            };
          }
        ),

        // Has array data.. needs revisit.
        sellers: (get(data.transaction, 'parties.seller') || [{}]).map((seller, index) => {
          const identifierType = getIdentifierType(seller);
          return {
            title: formatMessage(messages.seller),
            errors: matchErrors(['parties.seller']),
            data: [
              {
                label: formatMessage(messages.fileIdentifier),
                error: matchError('sellerFileIdentifier'),
                value: matchMarketIdentifier('parties.seller'),
                edit: displayFileIdentifierEdit('parties.seller'),
              },
              {
                label: formatMessage(messages.name),
                error: matchError(`parties.seller.${index}.&key`),
                value: get(seller, 'name'),
                assign: {
                  ...getAssignIdentifier('parties.seller'),
                  type: 'any',
                },
              },
              {
                label: formatMessage(messages.branchCountry),
                error: matchError(`parties.seller.${index}.${identifierType}.branchCountry`),
                value: get(seller, `${identifierType}.branchCountry`),
              },
            ],
          };
        }),

        // Has array data.. needs revisit.
        sellerDecisionMakers: (get(data.transaction, 'parties.sellerDecisionMaker') || [{}]).map(
          (sellerDecisionMaker, index) => {
            const identifierType = getIdentifierType(sellerDecisionMaker);
            return {
              title: formatMessage(messages.sellerDecisionMaker),
              errors: matchErrors(['parties.sellerDecisionMaker']),
              data: [
                {
                  label: formatMessage(messages.fileIdentifier),
                  error: matchError('sellerDecisionMakerFileIdentifier'),
                  value: matchMarketIdentifier('parties.sellerDecisionMaker'),
                  edit: displayFileIdentifierEdit('parties.sellerDecisionMaker'),
                },
                {
                  label: formatMessage(messages.name),
                  error: matchError('name'),
                  value: get(sellerDecisionMaker, 'name'),
                  assign: {
                    ...getAssignIdentifier('parties.sellerDecisionMaker'),
                    type: 'any',
                  },
                },
                {
                  label: formatMessage(messages.branchCountry),
                  error: matchError(
                    `parties.sellerDecisionMaker.${index}.${identifierType}.branchCountry`
                  ),
                  value: get(sellerDecisionMaker, `${identifierType}.branchCountry`),
                },
              ],
            };
          }
        ),

        counterparty: {
          title: formatMessage(messages.counterparty),
          errors: matchErrors(['counterparty', 'party']),
          data: [
            {
              label: formatMessage(messages.fileIdentifier),
              error: matchError('counterpartyFileIdentifier'),
              value: matchMarketIdentifier('parties.counterparty'),
              edit: displayFileIdentifierEdit('parties.counterparty'),
            },
            {
              label: formatMessage(messages.name),
              error: matchError('counterparty.name'),
              value: get(data.transaction, 'parties.counterparty.name'),
              assign: {
                ...getAssignIdentifier('parties.counterparty'),
                type: 'firm',
              },
            },
            {
              label: formatMessage(messages.branchCountry),
              error: matchError('counterparty.firmIdentifiers.branchCountry'),
              value: get(data.transaction, 'counterparty.firmIdentifiers.branchCountry'),
            },
          ],
        },

        // Has array data.. needs revisit.
        traders: (get(data.transaction, 'parties.trader') || [{}]).map((trader, index) => {
          const identifierType = getIdentifierType(trader);
          return {
            title: formatMessage(messages.trader),
            errors: matchErrors(['parties.trader']),
            data: [
              {
                label: formatMessage(messages.fileIdentifier),
                error: matchError('traderFileIdentifier'),
                value: matchMarketIdentifier('parties.trader'),
                edit: displayFileIdentifierEdit('parties.trader'),
              },
              {
                label: formatMessage(messages.name),
                error: matchError('name'),
                value: get(trader, 'name'),
                assign: {
                  ...getAssignIdentifier('parties.trader'),
                  type: 'person',
                },
              },
              {
                label: formatMessage(messages.branchCountry),
                error: matchError(`parties.trader.${index}.${identifierType}.branchCountry`),
                value: get(trader, `${identifierType}.branchCountry`),
              },
            ],
          };
        }),

        investmentDecisionMaker: {
          title: formatMessage(messages.investmentDecisionMaker),
          errors: matchErrors([
            'parties.executionWithinFirm',
            'tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier',
            'parties.investmentDecisionWithinFirm',
            'tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier',
          ]),
          data: [
            {
              label: formatMessage(messages.investmentDecisionWithinFirmFileIdentifier),
              error: matchError(
                'tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier'
              ),
              value: matchMarketIdentifier('parties.investmentDecisionWithinFirm'),
            },
            {
              label: formatMessage(messages.investmentDecisionWithinFirm),
              error: matchError('parties.investmentDecisionWithinFirm.name'),
              value: get(data.transaction, 'parties.investmentDecisionWithinFirm.name'),
              assign: {
                ...getAssignIdentifier('parties.investmentDecisionWithinFirm'),
                type: 'person',
              },
            },
            {
              label: formatMessage(messages.decisionMakerSupervisingBranchCountry),
              error: matchError(
                'parties.investmentDecisionWithinFirm.officialIdentifiers.branchCountry'
              ),
              value: get(
                data.transaction,
                'parties.investmentDecisionWithinFirm.officialIdentifiers.branchCountry'
              ),
            },
            {
              label: formatMessage(messages.executionWithinFirmFileIdentifier),
              error: matchError('tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier'),
              value: matchMarketIdentifier('parties.executionWithinFirm'),
            },
            {
              label: formatMessage(messages.executionWithinFirm),
              error: matchError('parties.executionWithinFirm.name'),
              value: get(data.transaction, 'parties.executionWithinFirm.name'),
              assign: {
                ...getAssignIdentifier('parties.executionWithinFirm'),
                type: 'person',
              },
            },
            {
              label: formatMessage(messages.decisionMakerResponsibleBranchCountry),
              error: matchError('parties.executionWithinFirm.officialIdentifiers.branchCountry'),
              value: get(
                data.transaction,
                'parties.executionWithinFirm.officialIdentifiers.branchCountry'
              ),
            },
          ],
        },
        mifirEligibleDetails: {
          title: formatMessage(messages.mifirEligibility),
          errors: matchErrors(['workflow.eligibility']),
          data: [
            {
              label: formatMessage(messages.mifirEligible),
              error: matchError('workflow.eligibility.eligible'),
              value: get(data.transaction, 'workflow.eligibility.eligible'),
            },
            {
              label: formatMessage(messages.mifirEligibleDescription),
              error: matchError('workflow.eligibility.eligible'),
              value: getMifirEligibilityDescription(get(data.transaction, 'workflow.eligibility')),
            },

            {
              label: formatMessage(messages.mifirEligibleFirdsESMARegister),
              error: matchError('workflow.eligibility.eligible'),
              ...(showFirdsRegisterLink(get(data.transaction, 'workflow.eligibility')) && {
                link: {
                  href: 'https://registers.esma.europa.eu/publication/searchRegister?core=esma_registers_firds',
                  text: formatMessage(messages.mifirEligibleFirdsESMARegisterLinkText),
                },
              }),
            },
            {
              label: formatMessage(messages.mifirEligibleFirdsFCARegister),
              error: matchError('workflow.eligibility.eligible'),
              ...(showFirdsRegisterLink(get(data.transaction, 'workflow.eligibility')) && {
                link: {
                  href: 'https://data.fca.org.uk/#/viewdata',
                  text: formatMessage(messages.mifirEligibleFirdsFCARegisterLinkText),
                },
              }),
            },
            {
              label: formatMessage(messages.mifirEligibleTotv),
              error: matchError('workflow.eligibility.totv'),
              value: get(data.transaction, 'workflow.eligibility.totv'),
            },
            {
              label: formatMessage(messages.mifirEligibleUtoTV),
              error: matchError('workflow.eligibility.utotv'),
              value: get(data.transaction, 'workflow.eligibility.utotv'),
            },
            {
              label: formatMessage(messages.mifirEligibleOnFirds),
              error: matchError('workflow.eligibility.onFirds'),
              value: get(data.transaction, 'workflow.eligibility.onFirds'),
            },
            {
              label: formatMessage(messages.mifirEligibleUnderlyingOnFirds),
              error: matchError('workflow.eligibility.underlyingOnFirds'),
              value: get(data.transaction, 'workflow.eligibility.underlyingOnFirds'),
            },
            {
              label: formatMessage(messages.mifirEligibleFirstAdmissionToTrading),
              error: matchError('workflow.eligibility.firstAdmissionToTrading'),
              value: getFormattedTime(
                get(data.transaction, 'workflow.eligibility.firstAdmissionToTrading'),
                'D MMM YYYY, HH:mm:ss'
              ),
            },
            {
              label: formatMessage(messages.mifirEligibleTerminationDate),
              error: matchError('workflow.eligibility.terminationDate'),
              value: getFormattedTime(
                get(data.transaction, 'workflow.eligibility.terminationDate'),
                'D MMM YYYY, HH:mm:ss'
              ),
            },
            {
              label: formatMessage(messages.mifirEligibleExecutionVenue),
              error: matchError('workflow.eligibility.executionVenue'),
              value: get(data.transaction, 'workflow.eligibility.executionVenue'),
            },
            {
              label: formatMessage(messages.mifirEligibleApplicableVenues),
              error: matchError('workflow.eligibility.applicableVenues'),
              value: get(data.transaction, 'workflow.eligibility.applicableVenues'),
            },
            {
              label: formatMessage(messages.mifirEligibleDiscountedVenues),
              error: matchError('workflow.eligibility.discountedVenues'),
              value: get(data.transaction, 'workflow.eligibility.discountedVenues'),
            },
            {
              label: formatMessage(messages.status),
              error: matchError('workflow.status'),
              value: get(data.transaction, 'workflow.status'),
            },
            {
              label: formatMessage(messages.validationStatusNonReportableCategory),
              error: matchError('workflow.validationStatusNonReportableCategory'),
              value: get(data.transaction, 'workflow.validationStatusNonReportableCategory'),
            },
            {
              label: formatMessage(messages.validationStatusReportableCategory),
              error: matchError('workflow.validationStatusReportableCategory'),
              value: get(data.transaction, 'workflow.validationStatusReportableCategory'),
            },
            {
              label: formatMessage(messages.validationStatusOverrideCategory),
              error: matchError('workflow.validationStatusOverrideCategory'),
              value: get(data.transaction, 'workflow.validationStatusOverrideCategory'),
            },
            {
              label: formatMessage(messages.documentation),
              error: matchError('workflow.eligibility.eligible'),
              link: {
                href: 'https://portal.steeleye.co/hc/en-us/sections/4410547322641-MiFIR-Eligibility',
                text: formatMessage(messages.documentationLinkText),
              },
            },
          ],
        },

        instrumentDetails: {
          title: formatMessage(messages.instrumentDetails),
          errors: matchErrors(['instrumentDetails.instrument']),
          data: [
            {
              label: formatMessage(messages.instrumentIdCode),
              error: matchError('instrumentDetails.instrument.instrumentIdCode'),
              value: get(data.transaction, 'instrumentDetails.instrument.instrumentIdCode'),
              edit: {
                name: 'instrumentDetails.instrument.instrumentIdCode',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.instrumentFullName),
              error: matchError('instrumentDetails.instrument.instrumentFullName'),
              value: get(data.transaction, 'instrumentDetails.instrument.instrumentFullName'),
              edit: {
                name: 'instrumentDetails.instrument.instrumentFullName',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.instrumentClassification),
              error: matchError('instrumentDetails.instrument.instrumentClassification'),
              value: get(data.transaction, 'instrumentDetails.instrument.instrumentClassification'),
              edit: {
                name: 'instrumentDetails.instrument.instrumentClassification',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.instrumentFileIdentifier),
              error: matchError('instrumentDetails.instrument'),
              value: matchMarketIdentifier('instrumentDetails.instrument'),
            },
            {
              label: formatMessage(messages.figiCode),
              error: matchError('instrumentDetails.instrument.ext.figi'),
              value: matchMarketIdentifier('instrumentDetails.instrument.ext.figi'),
            },
            {
              label: formatMessage(messages.bbgCode),
              error: matchError('instrumentDetails.instrument.ext.exchangeSymbolBbg'),
              value: matchMarketIdentifier('instrumentDetails.instrument.ext.exchangeSymbolBbg'),
            },
            {
              label: formatMessage(messages.ric),
              error: matchError('instrumentDetails.instrument.ext.pricingReferences.RIC'),
              value: matchMarketIdentifier(
                'instrumentDetails.instrument.ext.pricingReferences.RIC'
              ),
            },
            {
              label: formatMessage(messages.isCreatedThroughFallback),
              error: matchError('instrumentDetails.instrument.isCreatedThroughFallback'),
              value: matchMarketIdentifier('instrumentDetails.instrument.isCreatedThroughFallback'),
            },
          ],
        },

        cfiDetails: {
          title: formatMessage(messages.cfiDetails),
          errors: matchErrors(['instrumentDetails.instrument.cfiCategory']),
          data: [
            {
              label: formatMessage(messages.cfiCategory),
              error: matchError('instrumentDetails.instrument.cfiCategory'),
              value: get(data.transaction, 'instrumentDetails.instrument.cfiCategory'),
              edit: {
                name: 'instrumentDetails.instrument.cfiCategory',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.cfiGroup),
              error: matchError('instrumentDetails.instrument.cfiGroup'),
              value: get(data.transaction, 'instrumentDetails.instrument.cfiGroup'),
              edit: {
                name: 'instrumentDetails.instrument.cfiGroup',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.cfiAttribute1),
              error: matchError('instrumentDetails.instrument.cfiAttribute1'),
              value: get(data.transaction, 'instrumentDetails.instrument.cfiAttribute1'),
              edit: {
                name: 'instrumentDetails.instrument.cfiAttribute1',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.cfiAttribute2),
              error: matchError('instrumentDetails.instrument.cfiAttribute2'),
              value: get(data.transaction, 'instrumentDetails.instrument.cfiAttribute2'),
              edit: {
                name: 'instrumentDetails.instrument.cfiAttribute2',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.cfiAttribute3),
              error: matchError('instrumentDetails.instrument.cfiAttribute3'),
              value: get(data.transaction, 'instrumentDetails.instrument.cfiAttribute3'),
              edit: {
                name: 'instrumentDetails.instrument.cfiAttribute3',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.cfiAttribute4),
              error: matchError('instrumentDetails.instrument.cfiAttribute4'),
              value: get(data.transaction, 'instrumentDetails.instrument.cfiAttribute4'),
              edit: {
                name: 'instrumentDetails.instrument.cfiAttribute4',
                type: 'text',
              },
            },
          ],
        },
        bondDetails: {
          title: formatMessage(messages.bondDetails),
          errors: matchErrors(['instrumentDetails.instrument.bond']),
          data: [
            {
              label: formatMessage(messages.totalIssuedNominalAmount),
              error: matchError('instrumentDetails.instrument.bond.totalIssuedNominalAmount'),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.bond.totalIssuedNominalAmount'
              ),
              edit: {
                name: 'instrumentDetails.instrument.bond.totalIssuedNominalAmount',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.maturityDate),
              error: matchError('instrumentDetails.instrument.bond.maturityDate'),
              value: getFormattedTime(
                get(data.transaction, 'instrumentDetails.instrument.bond.maturityDate'),
                'D MMM YYYY, HH:mm:ss'
              ),
              edit: {
                name: 'instrumentDetails.instrument.bond.maturityDate',
                type: 'dateonly',
              },
            },
            {
              label: formatMessage(messages.nominalValueCurrency),
              error: matchError('instrumentDetails.instrument.bond.nominalValueCurrency'),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.bond.nominalValueCurrency'
              ),
              edit: {
                name: 'instrumentDetails.instrument.bond.nominalValueCurrency',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.nominalUnitOrMinTradedValue),
              error: matchError('instrumentDetails.instrument.bond.nominalUnitOrMinTradedValue'),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.bond.nominalUnitOrMinTradedValue'
              ),
              edit: {
                name: 'instrumentDetails.instrument.bond.nominalUnitOrMinTradedValue',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.fixedRate),
              error: matchError('instrumentDetails.instrument.bond.fixedRate'),
              value: get(data.transaction, 'instrumentDetails.instrument.bond.fixedRate'),
              edit: {
                name: 'instrumentDetails.instrument.bond.fixedRate',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.floatingRateBondIndexBenchmarkId),
              error: matchError(
                'instrumentDetails.instrument.bond.floatingRateBondIndexBenchmarkId'
              ),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.bond.floatingRateBondIndexBenchmarkId'
              ),
              edit: {
                name: 'instrumentDetails.instrument.bond.floatingRateBondIndexBenchmarkId',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.floatingRateBondIndexBenchmarkName),
              error: matchError(
                'instrumentDetails.instrument.bond.floatingRateBondIndexBenchmarkName'
              ),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.bond.floatingRateBondIndexBenchmarkName'
              ),
              edit: {
                name: 'instrumentDetails.instrument.bond.floatingRateBondIndexBenchmarkName',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.floatingRateBondIndexBenchmarkTerm),
              error: matchError(
                'instrumentDetails.instrument.bond.floatingRateBondIndexBenchmarkTerm'
              ),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.bond.floatingRateBondIndexBenchmarkTerm'
              ),
              edit: {
                name: 'instrumentDetails.instrument.bond.floatingRateBondIndexBenchmarkTerm',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.floatingRateBondIndexBenchmarkBasePointSpread),
              error: matchError(
                'instrumentDetails.instrument.bond.floatingRateBondIndexBenchmarkBasePointSpread'
              ),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.bond.floatingRateBondIndexBenchmarkBasePointSpread'
              ),
              edit: {
                name: 'instrumentDetails.instrument.bond.floatingRateBondIndexBenchmarkBasePointSpread',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.debtSeniority),
              error: matchError('instrumentDetails.instrument.bond.debtSeniority'),
              value: get(data.transaction, 'instrumentDetails.instrument.bond.debtSeniority'),
              edit: {
                name: 'instrumentDetails.instrument.bond.debtSeniority',
                type: 'enum',
              },
            },
          ],
        },

        derivativeDetails: {
          title: formatMessage(messages.derivativeDetails),
          errors: matchErrors(['instrumentDetails.instrument.derivative']),
          data: [
            {
              label: formatMessage(messages.expiryDate),
              error: matchError('instrumentDetails.instrument.derivative.expiryDate'),
              value: getFormattedTime(
                get(data.transaction, 'instrumentDetails.instrument.derivative.expiryDate'),
                'D MMM YYYY, HH:mm:ss'
              ),
              edit: {
                name: 'instrumentDetails.instrument.derivative.expiryDate',
                type: 'dateonly',
              },
            },
            {
              label: formatMessage(messages.priceMultiplier),
              error: matchError('instrumentDetails.instrument.derivative.priceMultiplier'),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.derivative.priceMultiplier'
              ),
              edit: {
                name: 'instrumentDetails.instrument.derivative.priceMultiplier',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.underlyingInstrumentCode),
              errors: matchErrors([
                'instrumentDetails.instrument.derivative.underlyingInstruments',
                'underlyingInstrumentCode',
              ]),
              value: (
                get(
                  data.transaction,
                  'instrumentDetails.instrument.derivative.underlyingInstruments'
                ) || []
              ).map(underlyingInstrument => underlyingInstrument.underlyingInstrumentCode),
              edit: {
                name: 'instrumentDetails.instrument.derivative.underlyingInstruments',
                type: 'array',
                fieldArray: (
                  get(
                    data.transaction,
                    'instrumentDetails.instrument.derivative.underlyingInstruments'
                  ) || ['']
                ).map((_, index) => ({
                  value: `instrumentDetails.instrument.derivative.underlyingInstruments[${index}].underlyingInstrumentCode`,
                })),
              },
            },
            {
              label: formatMessage(messages.underlyingIssuer),
              errors: matchErrors([
                'instrumentDetails.instrument.derivative.underlyingInstruments',
                'underlyingIssuer',
              ]),
              value: (
                get(
                  data.transaction,
                  'instrumentDetails.instrument.derivative.underlyingInstruments'
                ) || []
              ).map(underlyingInstrument => underlyingInstrument.underlyingIssuer),
              edit: {
                name: 'instrumentDetails.instrument.derivative.underlyingInstruments',
                type: 'array',
                fieldArray: (
                  get(
                    data.transaction,
                    'instrumentDetails.instrument.derivative.underlyingInstruments'
                  ) || ['']
                ).map((_, index) => ({
                  value: `instrumentDetails.instrument.derivative.underlyingInstruments[${index}].underlyingIssuer`,
                })),
              },
            },
            {
              ...getInstrumentDerivativeFieldData({
                message: messages.underlyingIndexName,
                fieldName: 'underlyingIndexName',
                editType: 'text',
              }),
            },
            {
              label: formatMessage(messages.underlyingIndexTerm),
              error: matchError(getUnderlyingInstrumentDerivativePath('underlyingIndexTerm')),
              value: get(
                data.transaction,
                getUnderlyingInstrumentDerivativePath('underlyingIndexTerm')
              ),
              edit: {
                name: getUnderlyingInstrumentDerivativePath('underlyingIndexTerm'),
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.underlyingIndexTermValue),
              error: matchError(getUnderlyingInstrumentDerivativePath('underlyingIndexTermValue')),
              value: get(
                data.transaction,
                getUnderlyingInstrumentDerivativePath('underlyingIndexTermValue')
              ),
              edit: {
                name: getUnderlyingInstrumentDerivativePath('underlyingIndexTermValue'),
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.optionType),
              error: matchError('instrumentDetails.instrument.derivative.optionType'),
              value: get(data.transaction, 'instrumentDetails.instrument.derivative.optionType'),
              edit: {
                name: 'instrumentDetails.instrument.derivative.optionType',
                type: 'enum',
              },
            },
            {
              label: formatMessage(messages.strikePrice),
              error: matchError('instrumentDetails.instrument.derivative.strikePrice'),
              value: get(data.transaction, 'instrumentDetails.instrument.derivative.strikePrice'),
              edit: {
                name: 'instrumentDetails.instrument.derivative.strikePrice',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.strikePricePending),
              error: matchError('instrumentDetails.instrument.derivative.strikePricePending'),
              value: yesOrNo(
                get(data.transaction, 'instrumentDetails.instrument.derivative.strikePricePending')
              ),
              edit: {
                name: 'instrumentDetails.instrument.derivative.strikePricePending',
                type: 'boolean',
              },
            },
            {
              label: formatMessage(messages.strikePriceCurrency),
              error: matchError('instrumentDetails.instrument.derivative.strikePriceCurrency'),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.derivative.strikePriceCurrency'
              ),
              edit: {
                name: 'instrumentDetails.instrument.derivative.strikePriceCurrency',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.strikePriceType),
              error: matchError('instrumentDetails.instrument.ext.strikePriceType'),
              value: get(data.transaction, 'instrumentDetails.instrument.ext.strikePriceType'),
              edit: {
                name: 'instrumentDetails.instrument.ext.strikePriceType',
                type: 'enum',
              },
            },
            {
              label: formatMessage(messages.optionExerciseStyle),
              error: matchError('instrumentDetails.instrument.derivative.optionExerciseStyle'),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.derivative.optionExerciseStyle'
              ),
              edit: {
                name: 'instrumentDetails.instrument.derivative.optionExerciseStyle',
                type: 'enum',
              },
            },
            {
              label: formatMessage(messages.deliveryType),
              error: matchError('instrumentDetails.instrument.derivative.deliveryType'),
              value: get(data.transaction, 'instrumentDetails.instrument.derivative.deliveryType'),
              edit: {
                name: 'instrumentDetails.instrument.derivative.deliveryType',
                type: 'enum',
              },
            },
          ],
        },

        commodityAndEmissionAllowances: {
          title: formatMessage(messages.commodityAndEmissionAllowances),
          errors: matchErrors(['instrumentDetails.instrument.commodityAndEmissionAllowances']),
          data: [
            {
              label: formatMessage(messages.baseProduct),
              error: matchError(
                'instrumentDetails.instrument.commodityAndEmissionAllowances.baseProduct'
              ),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.commodityAndEmissionAllowances.baseProduct'
              ),
              edit: {
                name: 'instrumentDetails.instrument.commodityAndEmissionAllowances.baseProduct',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.subProduct),
              error: matchError(
                'instrumentDetails.instrument.commodityAndEmissionAllowances.subProduct'
              ),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.commodityAndEmissionAllowances.subProduct'
              ),
              edit: {
                name: 'instrumentDetails.instrument.commodityAndEmissionAllowances.subProduct',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.furtherSubProduct),
              error: matchError(
                'instrumentDetails.instrument.commodityAndEmissionAllowances.furtherSubProduct'
              ),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.commodityAndEmissionAllowances.furtherSubProduct'
              ),
              edit: {
                name: 'instrumentDetails.instrument.commodityAndEmissionAllowances.furtherSubProduct',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.transactionType),
              error: matchError(
                'instrumentDetails.instrument.commodityAndEmissionAllowances.transactionType'
              ),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.commodityAndEmissionAllowances.transactionType'
              ),
              edit: {
                name: 'instrumentDetails.instrument.commodityAndEmissionAllowances.transactionType',
                type: 'enum',
              },
            },
            {
              label: formatMessage(messages.finalPriceType),
              error: matchError(
                'instrumentDetails.instrument.commodityAndEmissionAllowances.finalPriceType'
              ),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.commodityAndEmissionAllowances.finalPriceType'
              ),
              edit: {
                name: 'instrumentDetails.instrument.commodityAndEmissionAllowances.finalPriceType',
                type: 'enum',
              },
            },
          ],
        },

        interestRateDerivatives: {
          title: formatMessage(messages.interestRateDerivatives),
          errors: matchErrors(['instrumentDetails.instrument.interestRateDerivatives']),
          data: [
            {
              label: formatMessage(messages.referenceRate),
              error: matchError(
                'instrumentDetails.instrument.interestRateDerivatives.referenceRate'
              ),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.interestRateDerivatives.referenceRate'
              ),
              edit: {
                name: 'instrumentDetails.instrument.interestRateDerivatives.referenceRate',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.irContractTerm),
              error: matchError(
                'instrumentDetails.instrument.interestRateDerivatives.irContractTerm'
              ),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.interestRateDerivatives.irContractTerm'
              ),
              edit: {
                name: 'instrumentDetails.instrument.interestRateDerivatives.irContractTerm',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.leg1FixedRate),
              error: matchError(
                'instrumentDetails.instrument.interestRateDerivatives.leg1FixedRate'
              ),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.interestRateDerivatives.leg1FixedRate'
              ),
              edit: {
                name: 'instrumentDetails.instrument.interestRateDerivatives.leg1FixedRate',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.leg2FixedRate),
              error: matchError(
                'instrumentDetails.instrument.interestRateDerivatives.leg2FixedRate'
              ),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.interestRateDerivatives.leg2FixedRate'
              ),
              edit: {
                name: 'instrumentDetails.instrument.interestRateDerivatives.leg2FixedRate',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.leg2FloatingRate),
              error: matchError(
                'instrumentDetails.instrument.interestRateDerivatives.leg2FloatingRate'
              ),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.interestRateDerivatives.leg2FloatingRate'
              ),
              edit: {
                name: 'instrumentDetails.instrument.interestRateDerivatives.leg2FloatingRate',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.leg2IRContractTerm),
              error: matchError(
                'instrumentDetails.instrument.interestRateDerivatives.leg2IRContractTerm'
              ),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.interestRateDerivatives.leg2IRContractTerm'
              ),
              edit: {
                name: 'instrumentDetails.instrument.interestRateDerivatives.leg2IRContractTerm',
                type: 'text',
              },
            },
          ],
        },

        fxDerivatives: {
          title: formatMessage(messages.fxDerivatives),
          errors: matchErrors(['instrumentDetails.instrument.fxDerivatives']),
          data: [
            {
              label: formatMessage(messages.fxType),
              error: matchError('instrumentDetails.instrument.fxDerivatives.fxType'),
              value: get(data.transaction, 'instrumentDetails.instrument.fxDerivatives.fxType'),
              edit: {
                name: 'instrumentDetails.instrument.fxDerivatives.fxType',
                type: 'enum',
              },
            },
          ],
        },

        ext: {
          title: formatMessage(messages.ext),
          errors: matchErrors(['instrumentDetails.instrument.ext']),
          data: [
            {
              label: formatMessage(messages.instrumentUniqueIdentifier),
              error: matchError('instrumentDetails.instrument.ext.instrumentUniqueIdentifier'),
              value: get(
                data.transaction,
                'instrumentDetails.instrument.ext.instrumentUniqueIdentifier'
              ),
            },
            {
              label: formatMessage(messages.instrumentIdCodeType),
              error: matchError('instrumentDetails.instrument.ext.instrumentIdCodeType'),
              value: get(data.transaction, 'instrumentDetails.instrument.ext.instrumentIdCodeType'),
              edit: {
                name: 'instrumentDetails.instrument.ext.instrumentIdCodeType',
                type: 'enum',
              },
            },
            {
              label: formatMessage(messages.underlyingInstrumentCode),
              errors: matchErrors([
                'instrumentDetails.instrument.ext.underlyingInstruments',
                'underlyingInstrumentCode',
              ]),
              value: (
                get(data.transaction, 'instrumentDetails.instrument.ext.underlyingInstruments') ||
                []
              ).map(underlyingInstrument => underlyingInstrument.instrumentIdCode),
            },
            {
              label: formatMessage(messages.venueInEEA),
              error: matchError('instrumentDetails.instrument.ext.venueInEEA'),
              value: get(data.transaction, 'instrumentDetails.instrument.ext.venueInEEA'),
              edit: {
                name: 'instrumentDetails.instrument.ext.venueInEEA',
                type: 'boolean',
              },
            },
            {
              label: formatMessage(messages.bestExAssetClassMain),
              error: matchError('instrumentDetails.instrument.ext.bestExAssetClassMain'),
              value: get(data.transaction, 'instrumentDetails.instrument.ext.bestExAssetClassMain'),
              edit: {
                name: 'instrumentDetails.instrument.ext.bestExAssetClassMain',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.bestExAssetClassSub),
              error: matchError('instrumentDetails.instrument.ext.bestExAssetClassSub'),
              value: get(data.transaction, 'instrumentDetails.instrument.ext.bestExAssetClassSub'),
              edit: {
                name: 'instrumentDetails.instrument.ext.bestExAssetClassSub',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.mic),
              error: matchError('instrumentDetails.instrument.ext.aii.mic'),
              value: get(data.transaction, 'instrumentDetails.instrument.ext.aii.mic'),
              edit: {
                name: 'instrumentDetails.instrument.ext.aii.mic',
                type: 'text',
              },
            },
          ],
        },

        transactionDetails: {
          title: formatMessage(messages.transactionDetails),
          errors: matchErrors(['transactionDetails']),
          data: [
            {
              label: formatMessage(messages.buySellIndicator),
              error: matchError('transactionDetails.buySellIndicator'),
              value: get(data.transaction, 'transactionDetails.buySellIndicator'),
              buySellIndicator: true,
              edit: {
                name: 'transactionDetails.buySellIndicator',
                type: 'enum',
              },
            },
            {
              label: formatMessage(messages.tradingDateTime),
              error: matchError('transactionDetails.tradingDateTime'),
              value: formatTimeWithMicroSeconds(
                get(data.transaction, 'transactionDetails.tradingDateTime'),
                'D MMM YYYY, HH:mm:ss',
                false
              ),
              edit: {
                name: 'transactionDetails.tradingDateTime',
                type: 'datetimeLocal',
              },
            },
            {
              label: formatMessage(messages.tradingDateTimeUTC),
              error: matchError('transactionDetails.tradingDateTime'),
              value: formatTimeWithMicroSeconds(
                get(data.transaction, 'transactionDetails.tradingDateTime'),
                'D MMM YYYY, HH:mm:ss',
                true
              ),
              edit: {
                name: 'transactionDetails.tradingDateTime',
                type: 'datetime',
              },
            },
            {
              label: formatMessage(messages.tradingCapacity),
              error: matchError('transactionDetails.tradingCapacity'),
              value: get(data.transaction, 'transactionDetails.tradingCapacity'),
              edit: {
                name: 'transactionDetails.tradingCapacity',
                type: 'enum',
              },
            },
            {
              label: formatMessage(messages.quantity),
              error: matchError('transactionDetails.quantity'),
              value: get(data.transaction, 'transactionDetails.quantity'),
              edit: {
                name: 'transactionDetails.quantity',
                type: 'number',
              },
            },
            {
              label: formatMessage(messages.quantityCurrency),
              error: matchError('transactionDetails.quantityCurrency'),
              value: get(data.transaction, 'transactionDetails.quantityCurrency'),
              edit: {
                name: 'transactionDetails.quantityCurrency',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.derivativeNotionalChange),
              error: matchError('transactionDetails.derivativeNotionalChange'),
              value: get(data.transaction, 'transactionDetails.derivativeNotionalChange'),
              edit: {
                name: 'transactionDetails.derivativeNotionalChange',
                type: 'enum',
              },
            },
            {
              label: formatMessage(messages.price),
              error: matchError('transactionDetails.price'),
              value: get(data.transaction, 'transactionDetails.price'),
              edit: {
                name: 'transactionDetails.price',
                type: 'number',
              },
            },
            {
              label: formatMessage(messages.priceCurrency),
              error: matchError('transactionDetails.priceCurrency'),
              value: get(data.transaction, 'transactionDetails.priceCurrency'),
              edit: {
                name: 'transactionDetails.priceCurrency',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.netAmount),
              error: matchError('transactionDetails.netAmount'),
              value: get(data.transaction, 'transactionDetails.netAmount'),
              edit: {
                name: 'transactionDetails.netAmount',
                type: 'number',
              },
            },
            {
              label: formatMessage(messages.settlementDate),
              error: matchError('transactionDetails.settlementDate'),
              value: getFormattedTime(
                get(data.transaction, 'transactionDetails.settlementDate'),
                'D MMM YYYY, HH:mm:ss'
              ),
              edit: {
                name: 'transactionDetails.settlementDate',
                type: 'dateonly',
              },
            },
            {
              label: formatMessage(messages.settlementAmount),
              error: matchError('transactionDetails.settlementAmount'),
              value: get(data.transaction, 'transactionDetails.settlementAmount'),
              edit: {
                name: 'transactionDetails.settlementAmount',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.venue),
              error: matchError('transactionDetails.venue'),
              value: get(data.transaction, 'transactionDetails.venue'),
              edit: {
                name: 'transactionDetails.venue',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.branchMembershipCountry),
              error: matchError('transactionDetails.branchMembershipCountry'),
              value: get(data.transaction, 'transactionDetails.branchMembershipCountry'),
              edit: {
                name: 'transactionDetails.branchMembershipCountry',
                type: 'enum',
              },
            },
            {
              label: formatMessage(messages.upFrontPayment),
              error: matchError('transactionDetails.upFrontPayment'),
              value: get(data.transaction, 'transactionDetails.upFrontPayment'),
              edit: {
                name: 'transactionDetails.upFrontPayment',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.upFrontPaymentCurrency),
              error: matchError('transactionDetails.upFrontPaymentCurrency'),
              value: get(data.transaction, 'transactionDetails.upFrontPaymentCurrency'),
              edit: {
                name: 'transactionDetails.upFrontPaymentCurrency',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.complexTradeComponentId),
              error: matchError('transactionDetails.complexTradeComponentId'),
              value: get(data.transaction, 'transactionDetails.complexTradeComponentId'),
              edit: {
                name: 'transactionDetails.complexTradeComponentId',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.quantityNotation),
              error: matchError('transactionDetails.quantityNotation'),
              value: get(data.transaction, 'transactionDetails.quantityNotation'),
              edit: {
                name: 'transactionDetails.quantityNotation',
                type: 'enum',
              },
            },
            {
              label: formatMessage(messages.priceNotation),
              error: matchError('transactionDetails.priceNotation'),
              value: get(data.transaction, 'transactionDetails.priceNotation'),
              edit: {
                name: 'transactionDetails.priceNotation',
                type: 'enum',
              },
            },
            {
              label: formatMessage(messages.recordType),
              error: matchError('transactionDetails.recordType'),
              value: get(data.transaction, 'transactionDetails.recordType'),
              edit: {
                name: 'transactionDetails.recordType',
                type: 'enum',
              },
            },
            {
              label: formatMessage(messages.swapDirectionality),
              error: matchError('transactionDetails.swapDirectionalities'),
              value: get(data.transaction, 'transactionDetails.swapDirectionalities'),
              edit: {
                name: 'transactionDetails.swapDirectionalities',
                type: 'enumArray',
                fieldArray: (
                  get(data.transaction, 'transactionDetails.swapDirectionalities', '') || []
                ).map((_, index) => ({
                  value: `transactionDetails.swapDirectionalities[${index}]`,
                })),
              },
            },
            {
              label: formatMessage(messages.outgoingOrderAddlInfo),
              error: matchError('transactionDetails.outgoingOrderAddlInfo'),
              value: get(data.transaction, 'transactionDetails.outgoingOrderAddlInfo'),
              edit: {
                name: 'transactionDetails.outgoingOrderAddlInfo',
                type: 'text',
              },
            },
          ],
        },

        transmissionDetails: {
          title: formatMessage(messages.transmissionDetails),
          errors: matchErrors(['transmissionDetails']),
          data: [
            {
              label: formatMessage(messages.orderTransmissionIndicator),
              error: matchError('transmissionDetails.orderTransmissionIndicator'),
              value: yesOrNo(
                get(data.transaction, 'transmissionDetails.orderTransmissionIndicator')
              ),
              edit: {
                name: 'transmissionDetails.orderTransmissionIndicator',
                type: 'boolean',
              },
            },
            {
              label: formatMessage(messages.transmissionIdForSeller),
              error: matchError('parties.sellerTransmittingFirm.firmIdentifiers.lei'),
              value: get(data.transaction, 'parties.sellerTransmittingFirm.firmIdentifiers.lei'),
              edit: {
                name: 'parties.sellerTransmittingFirm.firmIdentifiers.lei',
                type: 'text',
              },
            },
            {
              label: formatMessage(messages.transmissionIdForBuyer),
              error: matchError('parties.buyerTransmittingFirm.firmIdentifiers.lei'),
              value: get(data.transaction, 'parties.buyerTransmittingFirm.firmIdentifiers.lei'),
              edit: {
                name: 'parties.buyerTransmittingFirm.firmIdentifiers.lei',
                type: 'text',
              },
            },
          ],
        },

        transactionFlags: {
          title: formatMessage(messages.transactionFlags),
          errors: matchErrors(['tradersAlgosWaiversIndicators']),
          data: [
            {
              label: formatMessage(messages.waiverIndicator),
              error: matchError('tradersAlgosWaiversIndicators.waiverIndicator'),
              value: get(data.transaction, 'tradersAlgosWaiversIndicators.waiverIndicator'),
              edit: {
                name: 'tradersAlgosWaiversIndicators.waiverIndicator',
                type: 'enum',
              },
            },
            {
              label: formatMessage(messages.shortSellingIndicator),
              error: matchError('tradersAlgosWaiversIndicators.shortSellingIndicator'),
              value: get(data.transaction, 'tradersAlgosWaiversIndicators.shortSellingIndicator'),
              edit: {
                name: 'tradersAlgosWaiversIndicators.shortSellingIndicator',
                type: 'enum',
              },
            },
            {
              label: formatMessage(messages.otcPostTradeIndicator),
              error: matchError('tradersAlgosWaiversIndicators.otcPostTradeIndicator'),
              value: get(data.transaction, 'tradersAlgosWaiversIndicators.otcPostTradeIndicator'),
              edit: {
                name: 'tradersAlgosWaiversIndicators.otcPostTradeIndicator',
                type: 'enum',
              },
            },
            {
              label: formatMessage(messages.commodityDerivativeIndicator),
              error: matchError('tradersAlgosWaiversIndicators.commodityDerivativeIndicator'),
              value: yesOrNo(
                get(data.transaction, 'tradersAlgosWaiversIndicators.commodityDerivativeIndicator')
              ),
              edit: {
                name: 'tradersAlgosWaiversIndicators.commodityDerivativeIndicator',
                type: 'boolean',
              },
            },
            {
              label: formatMessage(messages.securitiesFinancingTxnIndicator),
              error: matchError('tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator'),
              value: yesOrNo(
                get(
                  data.transaction,
                  'tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator'
                )
              ),
              edit: {
                name: 'tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator',
                type: 'boolean',
              },
            },
          ],
        },
        reconciliation: {
          title: formatMessage(messages.reconciliation),
          data: [
            {
              label: formatMessage(messages.seMatch),
              value: yesOrNo(get(data.transaction, 'reconciliation.se.match')),
            },
            {
              label: formatMessage(messages.seSource),
              value: get(data.transaction, 'reconciliation.se.source'),
            },
            {
              label: formatMessage(messages.armMatch),
              value: yesOrNo(get(data.transaction, 'reconciliation.arm.match')),
            },
            {
              label: formatMessage(messages.armSource),
              value: get(data.transaction, 'reconciliation.arm.source'),
            },
            {
              label: formatMessage(messages.ncaMatch),
              value: yesOrNo(get(data.transaction, 'reconciliation.nca.match')),
            },
            {
              label: formatMessage(messages.ncaSource),
              value: get(data.transaction, 'reconciliation.nca.source'),
            },
            ...(data.transaction?.reconciliation?.fieldBreaks || []).flatMap((fieldBreak, i) => [
              {
                label: `${formatMessage(messages.fieldBreakNo, { index: i })} ${formatMessage(
                  messages.field
                )}`,
                value: fieldBreak.field,
              },
              {
                label: `${formatMessage(messages.fieldBreakNo, { index: i })} ${formatMessage(
                  messages.seValue
                )}`,
                value: fieldBreak.value?.se,
              },
              {
                label: `${formatMessage(messages.fieldBreakNo, { index: i })} ${formatMessage(
                  messages.armValue
                )}`,
                value: fieldBreak.value?.arm,
              },

              {
                label: `${formatMessage(messages.fieldBreakNo, { index: i })} ${formatMessage(
                  messages.ncaValue
                )}`,
                value: fieldBreak.value?.nca,
              },
            ]),
          ],
        },
      }
    : null;
  return mappedData;
};

const TransactionDetailsContainer = ({
  comments,
  rawData,
  errors,
  onAssignToggle,
  isEdit,
  ...props
}) => {
  const intl = useIntl();
  return (
    <TransactionDetails
      {...props}
      onAssignToggle={onAssignToggle}
      data={mapTransactionDataToCards(comments, rawData, { errors, intl })}
      isEdit={isEdit}
    />
  );
};

TransactionDetailsContainer.propTypes = {
  comments: PropTypes.arrayOf(
    PropTypes.shape({
      '&timestamp': PropTypes.string,
      comment: PropTypes.string,
    })
  ),
  errors: PropTypes.arrayOf(
    PropTypes.shape({ category: PropTypes.string.isRequired, message: PropTypes.string })
  ),
  isEdit: PropTypes.bool,
  onAssignToggle: PropTypes.func.isRequired,
  rawData: PropTypes.object, // Tolerate unexpected data shapes
};

TransactionDetailsContainer.defaultProps = {
  comments: [],
  errors: null,
  rawData: null,
};

export default TransactionDetailsContainer;

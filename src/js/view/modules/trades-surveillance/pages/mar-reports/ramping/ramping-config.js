import { THRESHOLD_FORM_INPUT_TYPES } from 'js/constants/market-abuse';
import { defineMessages } from 'react-intl';

import { VALUE_TYPE_PERCENT, VALUE_TYPE_SECOND, VALUE_TYPE_INTEGER } from 'constants/d3';

import RampingTable from './ramping-table';

export const RAMPING = 'PAINTING_THE_TAPE_V2';

export const messages = defineMessages({
  evaluateAlgorithmTitle: {
    defaultMessage: 'Running Ramping Detection Algorithm\u2026',
    id: 'modules.mar.ramping.evaluateAlgorithmTitle',
  },
  listingTitle: {
    defaultMessage: 'Ramping Scenarios',
    id: 'modules.mar.ramping.listingTitle',
  },
  toolbarTitle: {
    defaultMessage: 'Ramping',
    id: 'modules.mar.ramping.toolbarTitle',
  },
});

export const placeholderMessages = defineMessages({
  directionality: {
    defaultMessage: 'Select directionality',
    id: 'modules.mar.ramping.placeholder.directionality',
  },
  evaluationType: {
    defaultMessage: 'Select an evaluation type',
    id: 'modules.mar.ramping.placeholder.evaluationType',
  },
  marketDataEvaluationType: {
    defaultMessage: 'Select market volume comparison type',
    id: 'modules.mar.ramping.placeholder.marketDataEvaluationType',
  },
  minimumNotionalCurrency: {
    defaultMessage: 'Select currency type',
    id: 'modules.mar.ramping.placeholder.minimumNotionalCurrency',
  },
});

export const labelMessages = defineMessages({
  directionality: {
    defaultMessage: 'Directionality',
    id: 'modules.mar.ramping.label.directionality',
  },
  evaluationType: {
    defaultMessage: 'Evaluation Type',
    id: 'modules.mar.ramping.label.evaluationType',
  },
  percentageAdv: {
    defaultMessage: 'Market Volume %',
    id: 'modules.mar.ramping.label.percentageAdv',
  },
  marketDataEvaluationType: {
    defaultMessage: 'Market Volume Comparison',
    id: 'modules.mar.ramping.label.marketDataEvaluationType',
  },
  minOrderCount: {
    defaultMessage: 'Minimum Number of Orders',
    id: 'modules.mar.ramping.label.minOrderCount',
  },
  priceImprovement: {
    defaultMessage: 'Price Improvement Flag',
    id: 'modules.mar.ramping.label.priceImprovement',
  },
  sameCounterparty: {
    defaultMessage: 'Same Counterparty',
    id: 'modules.mar.ramping.label.sameCounterparty',
  },
  timeWindow: {
    defaultMessage: 'Time Window',
    id: 'modules.mar.ramping.label.timeWindow',
  },
  off: {
    defaultMessage: 'Off',
    id: 'modules.mar.ramping.label.switch.off',
  },
  on: {
    defaultMessage: 'On',
    id: 'modules.mar.ramping.label.switch.on',
  },
  yes: {
    defaultMessage: 'Yes',
    id: 'modules.mar.ramping.label.switch.yes',
  },
  no: {
    defaultMessage: 'No',
    id: 'modules.mar.ramping.label.switch.no',
  },
  minimumNotional: {
    defaultMessage: `Minimum Notional`,
    id: 'modules.mar.ramping.label.minimumNotional',
  },
  minimumNotionalCurrency: {
    defaultMessage: `Minimum Notional Currency`,
    id: 'modules.mar.ramping.label.minimumNotionalCurrency',
  },
});

export const tableConfig = {
  listingTitle: messages.listingTitle,
  tableComponent: RampingTable,
};

export const tooltipMessages = defineMessages({
  directionality: {
    defaultMessage: `If the threshold "Directionality" is set to "Only Buys or Only Sells", Orders are then further grouped by their Directionality (i.e., "Buy" or "Sell") i.e., if a grouping before this point contained buys and sells and Directionality = "Only Buys or Only Sells" then two groupings would be proliferated. If Directionality = "Both Buys and Sells" then a single grouping would remain.`,
    id: 'modules.mar.ramping.tooltip.directionality',
  },
  evaluationType: {
    defaultMessage: `For a given scenario, imposing an evaluation type means that all the detected items have the same evaluation type value. For example, selecting "Trader", will mean that each item in the scenario would have to have the same "Trader" value in order to be detected. To get more results, select "Executing Entity".`,
    id: 'modules.mar.ramping.tooltip.evaluationType',
  },
  percentageAdv: {
    defaultMessage: `A comparison of all executed quantity in the scenario against the Market Traded Volume per the Market Volume Comparison threshold.`,
    id: 'modules.mar.ramping.tooltip.percentageAdv',
  },
  marketDataEvaluationType: {
    defaultMessage: `This threshold assesses whether the executed volume in a given scenario is material enough to exceed the Market Traded Volume. if the Market Volume Comparison threshold is set to "vs. Market Day Traded Volume" then the Day Traded Volume for the Instrument in the grouping is retrieved. If the Market Volume Comparison threshold is set to "vs. Market Average Daily Traded Volume" then the 10 Day Weighted Moving Average Traded Volume is retrieved. If the Market Volume Comparison threshold is set to "vs. Market Time Window Traded Volume" then the intraday Market Traded Volume between the the earliest "Last Execution Timestamp" - the Time Window threshold and the latest "Last Execution Timestamp" + the Time Window Threshold is retrieved.`,
    id: 'modules.mar.ramping.tooltip.marketDataEvaluationType',
  },
  minOrderCount: {
    defaultMessage: `Defines the minimum number of Orders that must be present in a scenario in order to generate an Alert. If the number of Orders in an alert is less than the figure given in this threshold no Alert will be generated.`,
    id: 'modules.mar.ramping.tooltip.minOrderCount',
  },
  priceImprovement: {
    defaultMessage: `This check determines whether the execution price increases throughout the chronology of the grouping. A comparison is made against the Average Execution Price of the earliest Order in the grouping vs. the Overall Average Execution Price of all Orders in the grouping.`,
    id: 'modules.mar.ramping.tooltip.priceImprovement',
  },
  sameCounterparty: {
    defaultMessage: `This check determines whether there are multiple Counterpaties in a given grouping. If the threshold is set to "true" and there are multiple Counterparties in a grouping, no alert will be generated.`,
    id: 'modules.mar.ramping.tooltip.sameCounterparty',
  },
  timeWindow: {
    defaultMessage: `This determines the Time Window via which Orders are grouped together. This is calculated by adding the Time Window value to each "Last Execution Timestamp" and then looking for any other Orders that have an "Order Time Submitted"; Less than "Last Execution Timestamp" + the Time Window threshold value AND Greater than the "Last Execution Timestamp"`,
    id: 'modules.mar.ramping.tooltip.timeWindow',
  },
  minimumNotional: {
    defaultMessage: `The minimum notional value that an alert will be generated for. This is calculated the sum of the notional values for each individual Trades of the alert.`,
    id: 'modules.mar.ramping.tooltip.minimumNotional',
  },
  minimumNotionalCurrency: {
    defaultMessage: `This is the currency that all Notional Values are converted into to ensure comparison is in one base currency (i.e., whether trades are in various different currencies, they are all converted to a standard currency set).`,
    id: 'modules.mar.ramping.tooltip.minimumNotionalCurrency',
  },
});

const MARKET_AVERAGE_DAILY_TRADED_VOLUME = 'Market Average Daily Traded Volume';
const MARKET_DAY_TRADED_VOLUME = 'Market Day Traded Volume';
const MARKET_TIME_WINDOW_TRADED_VOLUME = 'Market Time Window Traded Volume';

export const thresholds = {
  directionality: {
    label: labelMessages.directionality,
    tooltip: tooltipMessages.directionality,
    choices: [
      {
        label: 'Both Buys and Sells',
        value: 'Both Buys and Sells',
      },
      {
        label: 'Only Buys, or Only Sells',
        value: 'Only Buys, or Only Sells',
      },
    ],
    defaultValue: 'Only Buys, or Only Sells',
    options: {
      placeholderMessage: placeholderMessages.directionality,
    },
    type: THRESHOLD_FORM_INPUT_TYPES.SINGLE_SELECT_AUTOSUGGEST,
  },
  evaluationType: {
    label: labelMessages.evaluationType,
    tooltip: tooltipMessages.evaluationType,
    choices: [
      {
        label: 'Client',
        value: 'Client',
      },
      {
        label: 'Desk',
        value: 'Desk',
      },

      {
        label: 'Executing Entity',
        value: 'Executing Entity',
      },

      {
        label: 'Portfolio Manager',
        value: 'Portfolio Manager',
      },

      {
        label: 'Trader',
        value: 'Trader',
      },
    ],
    options: {
      placeholderMessage: placeholderMessages.evaluationType,
    },
    type: THRESHOLD_FORM_INPUT_TYPES.SINGLE_SELECT_AUTOSUGGEST,
  },
  percentageAdv: {
    label: labelMessages.percentageAdv,
    tooltip: tooltipMessages.percentageAdv,
    defaultValue: 0.1,
    options: {
      displayValueOnly: true,
      maxValue: 1,
      minValue: 0.005,
      preciseChange: true,
      precision: 3,
      step: 0.5,
      unit: VALUE_TYPE_PERCENT,
    },
    type: THRESHOLD_FORM_INPUT_TYPES.RANGE,
  },
  marketDataEvaluationType: {
    label: labelMessages.marketDataEvaluationType,
    tooltip: tooltipMessages.marketDataEvaluationType,
    choices: [
      {
        label: 'vs. Market Average Daily Traded Volume',
        value: MARKET_AVERAGE_DAILY_TRADED_VOLUME,
      },
      {
        label: 'vs. Market Day Traded Volume',
        value: MARKET_DAY_TRADED_VOLUME,
      },
      {
        label: 'vs. Market Time Window Traded Volume',
        value: MARKET_TIME_WINDOW_TRADED_VOLUME,
      },
    ],
    options: {
      placeholderMessage: placeholderMessages.marketDataEvaluationType,
    },
    type: THRESHOLD_FORM_INPUT_TYPES.SINGLE_SELECT_AUTOSUGGEST,
  },
  minOrderCount: {
    label: labelMessages.minOrderCount,
    tooltip: tooltipMessages.minOrderCount,
    defaultValue: 10,
    options: {
      displayValueOnly: true,
      literalPrecision: true,
      maxValue: 100,
      minValue: 1,
      preciseChange: true,
      precision: 1,
      unit: VALUE_TYPE_INTEGER,
    },
    type: THRESHOLD_FORM_INPUT_TYPES.RANGE,
  },
  priceImprovement: {
    label: labelMessages.priceImprovement,
    tooltip: tooltipMessages.priceImprovement,
    isOptional: () => true,
    isProhibited: currentThresholds => currentThresholds.directionality === 'Both Buys and Sells',
    options: {
      labelFalse: labelMessages.off,
      labelTrue: labelMessages.on,
    },
    type: THRESHOLD_FORM_INPUT_TYPES.TOGGLE,
  },
  sameCounterparty: {
    label: labelMessages.sameCounterparty,
    tooltip: tooltipMessages.sameCounterparty,
    isOptional: () => true,
    options: {
      labelFalse: labelMessages.no,
      labelTrue: labelMessages.yes,
    },
    type: THRESHOLD_FORM_INPUT_TYPES.TOGGLE,
  },
  timeWindow: {
    label: labelMessages.timeWindow,
    tooltip: tooltipMessages.timeWindow,
    defaultValue: {
      unit: 'minutes',
      value: 10,
    },
    options: {
      literalPrecision: true,
      maxValue: 60, // 3600 seconds
      minValue: 0,
      preciseChange: true,
      precision: 1,
      unit: VALUE_TYPE_SECOND,
      allowedUnits: ['seconds', 'minutes', 'hours'],
    },
    type: THRESHOLD_FORM_INPUT_TYPES.TIME_RANGE,
  },
  /* TODO uncomment when backend supports them
  minimumNotional: {
    label: labelMessages.minimumNotional,
    tooltip: tooltipMessages.minimumNotional,
    defaultValue: 10000,
    options: {
      displayValueOnly: true,
      literalPrecision: true,
      maxValue: 10000000,
      minValue: 0,
      preciseChange: true,
      precision: 1,
    },
    type: THRESHOLD_FORM_INPUT_TYPES.RANGE,
  },
  minimumNotionalCurrency: {
    label: labelMessages.minimumNotionalCurrency,
    tooltip: tooltipMessages.minimumNotionalCurrency,
    choices: [
      {
        label: 'USD',
        value: 'USD',
      },
      {
        label: 'GBP',
        value: 'GBP',
      },
      {
        label: 'JPY',
        value: 'JPY',
      },
      {
        label: 'CHF',
        value: 'CHF',
      },
      {
        label: 'EUR',
        value: 'EUR',
      },
    ],
    options: {
      placeholderMessage: placeholderMessages.minimumNotionalCurrency,
    },
    type: THRESHOLD_FORM_INPUT_TYPES.SINGLE_SELECT_AUTOSUGGEST,
  }, */
};

import React from 'react';
import { defineMessages } from 'react-intl';

// Common Components
import MainGrid from 'view/components/layout/main-grid';

// MAR Components
import InterTradingVenueManipulationTable from 'view/modules/trades-surveillance/components/market-abuse-table/report-tables/inter-trading-venue-manipulation-table';
import MarketAbusePageToolbar from 'js/view/modules/trades-surveillance/components/toolbars/market-abuse-page';
import SingleReportListing from 'view/modules/trades-surveillance/components/single-report-listing/single-report-listing';
import Thresholds from 'js/view/modules/trades-surveillance/components/thresholds/thresholds';

// Constants
import {
  getOrdersTrendsEndpoint,
  MAR_INTER_TRADING_VENUE_MANIPULATION as reportEndpoint,
} from 'constants/endpoints';

// Schema and configs
import { SURVEILLANCE_MARKET_ABUSE_REPORT_TYPE_INTER_TRADING_VENUE_MANIPULATION as INTER_TRADING_VENUE_MANIPULATION } from '@steeleye/schema/models/enums';

// HOCs
import { withPageErrorHandler } from 'view/components/layout/hoc';
import useThresholdChoices from 'js/hooks/useThresholdChoices';

import { useMARReportHandler } from './useMARReportHandler';

// TODO remove widget pattern
const WIDGET_SET_ID = 'TBR_WIDGET_SET';

const messages = defineMessages({
  evaluateAlgorithmTitle: {
    defaultMessage: 'Running Inter-Trading Venue Manipulation Detection Algorithm\u2026',
    id: 'module.marketAbuse.interTradingVenueManipulation.modal.title',
  },
  listingTitle: {
    defaultMessage: 'Inter-Trading Venue Manipulation Scenarios',
    id: 'module.marketAbuse.interTradingVenueManipulation.resultsTable.title',
  },
  toolbarTitle: {
    defaultMessage: 'Inter-Trading Venue Manipulation',
    id: 'module.marketAbuse.interTradingVenueManipulation.heading.title',
  },
});

const InterTradingVenueManipulationReportPage = () => {
  const {
    showScenarios,
    toolbarProps,
    showThresholds,
    listingProps,
    thresholdProps,
    isSubscribed,
  } = useMARReportHandler({
    isOrderRecordRequired: true,
    reportEndpoint,
    reportType: INTER_TRADING_VENUE_MANIPULATION,
    widgetSetId: WIDGET_SET_ID,
  });

  useThresholdChoices({
    endpoint: getOrdersTrendsEndpoint('ASSET_CLASS'),
    reportType: INTER_TRADING_VENUE_MANIPULATION,
    thresholdName: 'assetClass',
  });

  if (!isSubscribed) {
    return null;
  }

  return (
    <>
      <MarketAbusePageToolbar
        evaluateAlgorithmTitle={messages.evaluateAlgorithmTitle}
        reportType={INTER_TRADING_VENUE_MANIPULATION}
        toolbarTitle={messages.toolbarTitle}
        {...toolbarProps}
      />
      <MainGrid>
        {
          // Load form only when it's confirmed that init value is ready
          // so that value isn't overridden by default threshold values.
          showThresholds && <Thresholds {...thresholdProps} />
        }
        {showScenarios && (
          <SingleReportListing
            {...listingProps}
            title={messages.listingTitle}
            tableComponent={InterTradingVenueManipulationTable}
          />
        )}
      </MainGrid>
    </>
  );
};

export default withPageErrorHandler(InterTradingVenueManipulationReportPage);

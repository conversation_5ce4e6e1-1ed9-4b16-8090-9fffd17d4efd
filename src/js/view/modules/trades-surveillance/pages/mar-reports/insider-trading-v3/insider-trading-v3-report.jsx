import React from 'react';

// Common Components
import MainGrid from 'view/components/layout/main-grid';

// MAR Components
import MarketAbusePageToolbar from 'js/view/modules/trades-surveillance/components/toolbars/market-abuse-page';
import SingleReportListing from 'view/modules/trades-surveillance/components/single-report-listing/single-report-listing';
import Thresholds from 'js/view/modules/trades-surveillance/components/thresholds/thresholds';

// Constants
import {
  getOrdersTrendsEndpoint,
  MAR_INSIDER_TRADING_V3 as reportEndpoint,
} from 'constants/endpoints';

// HOCs
import { withPageErrorHandler } from 'view/components/layout/hoc';

import useThresholdChoices from 'js/hooks/useThresholdChoices';

import { useMARReportHandler } from '../useMARReportHandler';

import * as config from './insider-trading-v3-config';
import InsiderTradingTable from './insider-trading-v3-table';

export { config };

// TODO remove widget pattern
const WIDGET_SET_ID = 'TBR_WIDGET_SET';

const InsiderTradingV3ReportPage = () => {
  const { INSIDER_TRADING_V3, messages } = config;
  const {
    showScenarios,
    toolbarProps,
    showThresholds,
    listingProps,
    thresholdProps,
    isSubscribed,
  } = useMARReportHandler({
    daysAllowed: 31,
    isOrderRecordRequired: true,
    reportEndpoint,
    reportType: INSIDER_TRADING_V3,
    widgetSetId: WIDGET_SET_ID,
  });

  useThresholdChoices({
    endpoint: getOrdersTrendsEndpoint('ASSET_CLASS'),
    reportType: INSIDER_TRADING_V3,
    thresholdName: 'assetClass',
  });

  if (!isSubscribed) {
    return null;
  }

  return (
    <>
      <MarketAbusePageToolbar
        evaluateAlgorithmTitle={messages.evaluateAlgorithmTitle}
        reportType={INSIDER_TRADING_V3}
        toolbarTitle={messages.toolbarTitle}
        {...toolbarProps}
      />
      <MainGrid>
        {
          // Load form only when it's confirmed that init value is ready
          // so that value isn't overridden by default threshold values.
          showThresholds && <Thresholds {...thresholdProps} />
        }
        {showScenarios && (
          <SingleReportListing
            {...listingProps}
            title={messages.listingTitle}
            tableComponent={InsiderTradingTable}
          />
        )}
      </MainGrid>
    </>
  );
};

export default withPageErrorHandler(InsiderTradingV3ReportPage);

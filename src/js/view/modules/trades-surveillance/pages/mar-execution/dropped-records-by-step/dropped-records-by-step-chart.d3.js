import { select, scaleLinear, scaleBand, axisBottom, axisLeft, max, mouse } from 'd3';

import renderTooltipContent from 'view/components/d3-bits/render-tooltip-content.d3';

const droppedRecordsByStepChart = el => {
  const name = 'droppedRecordsByStepChart';

  const margin = { top: 20, right: 20, bottom: 30, left: 120 };

  const svg = select(el);
  const xScale = scaleLinear();
  const yScale = scaleBand();

  const selection = svg.append('g');
  const xAxisGroup = selection.append('g');
  const yAxisGroup = selection.append('g');
  const barsGroup = selection.append('g').attr('clip-path', 'url(#clip)');
  const tooltip = selection.append('g').attr('class', `${name}-tooltip`).style('display', 'none');
  tooltip.append('rect').attr('rx', 4);
  tooltip.append('text').style('font-size', '12px');

  const plotAreaClip = svg
    .append('defs')
    .append('clipPath')
    .attr('id', 'clip')
    .append('rect')
    .attr('x', margin.left + 1)
    .attr('y', margin.top);

  return function render({ data, width, height }) {
    xScale
      .domain([0, max(data, d => d.recordsDropped)])
      .range([0, width - margin.left - margin.right])
      .nice();

    yScale
      .domain(data.map(d => d.stepType))
      .range([0, height - margin.top - margin.bottom])
      .padding(0.2);

    plotAreaClip
      .attr('width', width - margin.left - margin.right)
      .attr('height', height - margin.top - margin.bottom)
      .attr('x', margin.left + 1);

    function showTooltip(d) {
      tooltip.style('display', null);
      const tooltipContent = [
        { id: 'recordsDropped', text: `Records Dropped: ${d.recordsDropped || 0}` },
        { id: 'groupingsDropped', text: `Groupings Dropped: ${d.groupingsDropped || 0}` },
        {
          id: 'stepDuration',
          text: `Duration: ${d.stepDuration}`,
        },
      ];

      renderTooltipContent({
        content: tooltipContent,
        options: {
          yOffsetBase: 0,
        },
        tooltip,
      });
    }

    function hideTooltip() {
      tooltip.style('display', 'none');
      tooltip.select(`.${name}-tooltip`).text('');
    }

    function moveTooltip() {
      const [mouseX, mouseY] = mouse(selection.node());
      const tooltipWidth = tooltip.node().getBBox().width;

      // Position adjustments if the tooltip goes out of the SVG boundaries
      const xOffset = mouseX + tooltipWidth + 32 > width ? -tooltipWidth - 32 : 32;
      tooltip.attr('transform', `translate(${mouseX + xOffset}, ${mouseY})`);
    }

    const bars = barsGroup.selectAll(`.${name}-bar`).data(data);
    bars
      .join('rect')
      .attr('class', `${name}-bar`)
      .attr('x', xScale(0) - 4)
      .attr('y', d => yScale(d.stepType))
      .attr('rx', 5)
      .attr('width', d => xScale(d.recordsDropped) - xScale(0) + 4)
      .attr('height', yScale.bandwidth())
      .attr('transform', `translate(${margin.left},${margin.top})`)
      .on('mouseenter', showTooltip)
      .on('mouseout', hideTooltip)
      .on('touchmove mousemove', moveTooltip);

    const xAxis = axisBottom(xScale).tickSize(-height + margin.top + margin.bottom);
    xAxisGroup
      .attr('transform', `translate(${margin.left},${height - margin.bottom})`)
      .attr('class', `${name}-xAxis`)
      .call(xAxis)
      .call(g => g.selectAll('.tick text').attr('y', 8));

    const yAxis = axisLeft(yScale).tickSize(-width + margin.right + margin.left);
    yAxisGroup
      .attr('transform', `translate(${margin.left},${margin.top})`)
      .attr('class', `${name}-yAxis`)
      .call(yAxis);
  };
};

export default droppedRecordsByStepChart;

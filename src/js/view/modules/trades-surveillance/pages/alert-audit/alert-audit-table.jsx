import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { defineMessages, useIntl } from 'react-intl';
import queryString from 'query-string';
import { useLocation } from 'react-router';

import FetchErrorMessage from 'view/components/fetch-error-message/fetch-error-message.jsx';
import NoResultsRow from 'view/components/grid-table/no-results-row/no-results-row';
import FilterPagination from 'view/components/filter-pagination/filter-pagination';
import FilterSortingHeaderCell from 'view/components/grid-table/filter-sorting-header-cell/filter-sorting-header-cell';
import LoadingCentered from 'view/components/loading-centered/loading-centered';

import TextCell from 'view/components/grid-table/text-cell/text-cell';

import { getAuditByRecord } from 'js/actions/audit/audit.actions';
import { safeFormatDateInUTC } from 'util/time';
import { EMPTY_DISPLAY_VALUE } from 'constants/property';

import './alert-audit-table.less';

const messages = defineMessages({
  category: {
    defaultMessage: 'Category',
    id: 'modules.tradesSurv.alertAuditTable.category',
  },
  description: {
    defaultMessage: 'Description',
    id: 'modules.tradesSurv.alertAuditTable.description',
  },
  event: {
    defaultMessage: 'Event',
    id: 'modules.tradesSurv.alertAuditTable.event',
  },
  module: {
    defaultMessage: 'Module',
    id: 'modules.tradesSurv.alertAuditTable.module',
  },
  page: {
    defaultMessage: 'Page',
    id: 'modules.tradesSurv.alertAuditTable.page',
  },
  searchText: {
    defaultMessage: 'Search Text',
    id: 'modules.tradesSurv.alertAuditTable.searchText',
  },
  timestamp: {
    defaultMessage: 'Timestamp',
    id: 'modules.tradesSurv.alertAuditTable.timestamp',
  },
  user: {
    defaultMessage: 'User',
    id: 'modules.tradesSurv.alertAuditTable.user',
  },
  userAudit: {
    defaultMessage: 'User Audit',
    id: 'modules.tradesSurv.alertAuditTable.userAudit',
  },
  userId: {
    defaultMessage: 'User ID',
    id: 'modules.tradesSurv.alertAuditTable.userId',
  },
});

const Row = ({ data }) => (
  <>
    <TextCell text={safeFormatDateInUTC(data.timestamp, 'DD-MMM-YY HH:mm:ss', true)} />
    <TextCell text={data.userName} />
    <TextCell text={data.user} />
    <TextCell text={data.uri} />
    <TextCell text={data.searchText || EMPTY_DISPLAY_VALUE} />
    <TextCell text={data.eventDetails?.category && data.eventDetails.category} />
    <TextCell text={data.eventDetails?.module && data.eventDetails.module} />
    <TextCell text={data.eventDetails?.event && data.eventDetails.event} />
    <TextCell text={data.eventDetails?.description && data.eventDetails.description} />
  </>
);

Row.propTypes = {
  data: PropTypes.shape({
    '&id': PropTypes.string.isRequired,
    action: PropTypes.string,
    body: PropTypes.string,
    eventDetails: PropTypes.shape({
      category: PropTypes.string,
      description: PropTypes.string,
      event: PropTypes.string,
      module: PropTypes.string,
    }),
    realm: PropTypes.string.isRequired,
    recordDetails: PropTypes.shape({
      model: PropTypes.string,
      originalRecordKey: PropTypes.string,
      recordKey: PropTypes.string,
    }),
    searchText: PropTypes.string,
    statusCode: PropTypes.string,
    timestamp: PropTypes.string.isRequired,
    uri: PropTypes.string.isRequired,
    user: PropTypes.string.isRequired,
    userName: PropTypes.string,
  }).isRequired,
};

/**
 * Fetches and renders a table of alert audits
 */
const AlertAuditTable = ({
  data,
  itemsCount,
  errors,
  fetchAuditByRecord,
  loading,
  refineFilters,
  alertId,
  hasAppliedPreRefineFiltersOnMount,
}) => {
  const { formatMessage } = useIntl();
  const location = useLocation();
  useEffect(() => {
    const allQueries = queryString.parse(location.search, { arrayFormat: 'comma' });
    if (alertId && hasAppliedPreRefineFiltersOnMount) {
      fetchAuditByRecord({
        query: allQueries,
        recordId: alertId,
        refineFilters,
      });
    }
  }, [location.search, fetchAuditByRecord, refineFilters, alertId]);

  if (!data && !errors && !loading) return null;

  if (!data && !errors && loading) return <LoadingCentered />;

  if (errors) {
    return <FetchErrorMessage />;
  }

  return (
    <>
      <div styleName={`table ${loading ? 'tableLoading' : ''}`}>
        <FilterSortingHeaderCell fieldName="timestamp">
          {formatMessage(messages.timestamp)}
        </FilterSortingHeaderCell>
        <FilterSortingHeaderCell fieldName="userName">
          {formatMessage(messages.user)}
        </FilterSortingHeaderCell>
        <FilterSortingHeaderCell fieldName="user">
          {formatMessage(messages.userId)}
        </FilterSortingHeaderCell>
        <FilterSortingHeaderCell fieldName="uri">
          {formatMessage(messages.page)}
        </FilterSortingHeaderCell>
        <FilterSortingHeaderCell fieldName="searchText">
          {formatMessage(messages.searchText)}
        </FilterSortingHeaderCell>
        <FilterSortingHeaderCell fieldName="eventDetails.category">
          {formatMessage(messages.category)}
        </FilterSortingHeaderCell>
        <FilterSortingHeaderCell fieldName="eventDetails.module">
          {formatMessage(messages.module)}
        </FilterSortingHeaderCell>
        <FilterSortingHeaderCell fieldName="eventDetails.event">
          {formatMessage(messages.event)}
        </FilterSortingHeaderCell>
        <FilterSortingHeaderCell fieldName="eventDetails.description">
          {formatMessage(messages.description)}
        </FilterSortingHeaderCell>
        {data.length ? (
          data.map(row => {
            const id = row['&id'];
            return <Row key={id} data={row} />;
          })
        ) : (
          <NoResultsRow />
        )}
      </div>
      <FilterPagination total={itemsCount} />
    </>
  );
};

AlertAuditTable.propTypes = {
  alertId: PropTypes.string,
  data: PropTypes.arrayOf(
    PropTypes.shape({
      '&id': PropTypes.string.isRequired,
    })
  ),
  errors: PropTypes.object,
  fetchAuditByRecord: PropTypes.func.isRequired,
  itemsCount: PropTypes.number.isRequired,
  loading: PropTypes.bool,
  refineFilters: PropTypes.array,
};

AlertAuditTable.defaultProps = {
  data: null,
  loading: false,
  refineFilters: [],
};

const mapStateToProps = (state, ownProps) => {
  const { alertId } = ownProps || {};
  const { data, errors, loading } = state.audit.singleRecord[alertId] || {};
  return {
    data: data?.results,
    errors,
    itemsCount: data?.header?.totalHits || 0,
    loading,
  };
};

const mapDispatchToProps = {
  fetchAuditByRecord: getAuditByRecord,
};

export default connect(mapStateToProps, mapDispatchToProps)(AlertAuditTable);

@import 'less/site.less';

.columns {
  display: grid;
  grid-gap: 16px;
  grid-template-columns: 1fr 1fr 1fr;
  position: relative;
  &:after {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: '';
    background-color: @gray-base;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
  }
}

.columnsLoading {
  &:after {
    pointer-events: auto;
    opacity: 0.7;
  }
}

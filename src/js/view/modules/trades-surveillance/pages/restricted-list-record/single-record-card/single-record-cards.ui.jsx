import React from 'react';
import PropTypes from 'prop-types';

import DataCard from 'view/components/data-card/data-card';
import DataCardEdit from 'view/components/data-card-edit/data-card-edit';
import RecordComments from './record-comments/record-comments';

import styles from './single-record-cards.less';

const SingleRecordCards = ({ data, loading, dispatch, isEdit, comments }) => {
  if (!data) return null;

  const columnsClassName = `${styles.columns} ${loading ? styles.columnsLoading : ''}`;

  if (isEdit) {
    return (
      <div className={columnsClassName}>
        <DataCardEdit dispatch={dispatch} {...data.recordDetails} darker />
        <DataCardEdit dispatch={dispatch} {...data.watchReason} darker />
      </div>
    );
  }
  return (
    <div className={columnsClassName}>
      <DataCard {...data.recordDetails} darker />
      <DataCard {...data.watchReason} darker />
      <RecordComments comments={comments} />
    </div>
  );
};

SingleRecordCards.propTypes = {
  comments: PropTypes.array,
  data: PropTypes.object,
  dispatch: PropTypes.func.isRequired,
  isEdit: PropTypes.bool,
  loading: PropTypes.bool,
};

export default SingleRecordCards;

import React, { useState, useEffect } from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import useFString from 'js/hooks/fString/useFString';
import { defineMessages, useIntl } from 'react-intl';
import { SURVEILLANCE_ALERT_HIT_STATUS_UNRESOLVED } from '@steeleye/schema/models/enums';

// Hooks
import { useGlobalTheme } from 'js/hooks/useGlobalTheme';
import useAlertStatusToggle from 'js/hooks/useAlertStatusToggle';
import usePersistedState from 'js/hooks/persistedState/usePersistedState';
import usePersistedPagination from 'js/hooks/usePersistedPagination';
import useUserPermissions from 'js/hooks/useUserPermissions';
import useUserSurveillanceWorkflows from 'js/hooks/useUserSurveillanceWorkflows';

// Components
import Pagination from 'view/components/pagination';
import NoResultsRow from 'view/components/grid-table/no-results-row/no-results-row';
import CollapsibleTableContainer from 'view/components/table/collapsible-table-container';
import SortingHeaderCell from 'view/components/grid-table/sorting-header-cell/sorting-header-cell';
import HeaderCheckboxCell from 'view/components/grid-table/header-checkbox-cell/header-checkbox-cell';
import AlertTableRow from 'view/modules/trades-surveillance/components/legacy/table/non-mar-abuse-alert-table-row';
import PermissionWrapper from 'view/components/permission-wrapper/permission-wrapper';
import ButtonWithIcon from 'js/view/components/button-with-icon/button-with-icon';

// Constants and Utils
import {
  RESOLVED,
  RESOLVED_WITH_DISMISSAL,
  RESOLVED_WITH_INVESTIGATION,
  RESOLVED_WITH_BREACH,
  RESOLVED_WITH_INVESTIGATION_WITH_BREACH,
  ALERT_ACTION_BUTTON_MESSAGES,
  UNRESOLVED,
} from 'constants/surveillance';
import withRouter from 'util/withRouter';
import makeSortQuery from 'util/makeSortQuery';
import { PROPERTY_MESSAGES as messages } from 'constants/property';
import { TRADES_SURVEILLANCE } from 'js/constants/roles-permissions';
import { checkIsTransitionAllowed, getFromStatusMap } from 'js/util/workflow';

// Actions
import * as alertActions from 'js/actions/trade-surv/single-alert.actions';

import styles from './alerts-tables.less';

const FIELD_COUNT = 22;
const DEFAULT_PAGINATION_SIZE = 10;
const SORT_QUERY = 'ResolvedAlertsTable/sortQuery';
const PAGINATION_QUERY = 'ResolvedAlertsTable/paginationQuery';

const MESSAGES = defineMessages({
  alertsCount: {
    defaultMessage: '{count} {count, plural, one {Alert} other {Alerts}}',
    id: 'module.tradeSurv.alerts.listing.count.resolved',
  },
  title: {
    defaultMessage: 'Resolved',
    id: 'module.tradeSurv.alerts.listing.title.resolved',
  },
});

const resolvedStatuses = [
  RESOLVED,
  RESOLVED_WITH_DISMISSAL,
  RESOLVED_WITH_INVESTIGATION,
  RESOLVED_WITH_BREACH,
  RESOLVED_WITH_INVESTIGATION_WITH_BREACH,
];

const ResolvedAlertsTable = ({
  query,
  updateAlerts,
  alertListData,
  refineFilters,
  fetchAlertsList,
  triggerRefreshUI,
  onUIRefreshRequired,
  testId,
}) => {
  const { isClassicTheme } = useGlobalTheme();
  const { isOpen, toggle } = useAlertStatusToggle(RESOLVED);
  const [sortQuery, setSortQuery] = usePersistedState(SORT_QUERY, {
    fieldName: 'detected',
    order: 'descending',
  });
  const [paginationQuery, setPaginationQuery] = usePersistedPagination(
    PAGINATION_QUERY,
    DEFAULT_PAGINATION_SIZE
  );
  const permissions = useUserPermissions(TRADES_SURVEILLANCE);

  const { omitF, fStringConfig, filters } = useFString();

  const [allSelected, setAllSelected] = useState(false);
  const [selectedAlertIds, setSelectedAlertIds] = useState([]);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  const { loading, data } = alertListData || {};
  const { results, header } = data || {};
  const { totalHits } = header || {};

  const { tSurvWorkflow } = useUserSurveillanceWorkflows({ userIsAuthenticated: true });

  const activeAlertStatus = filters.find(f => f.id === 'workflow.status')?.values || [];

  const isSelected = alert => selectedAlertIds.includes(alert['&id']);

  const toggleAlertSelection = alert => {
    if (isSelected(alert)) {
      setSelectedAlertIds(selectedAlertIds.filter(id => id !== alert['&id']));
      setAllSelected(false);
    } else {
      const newSelectedAlerts = [...selectedAlertIds];
      newSelectedAlerts.push(alert['&id']);
      setSelectedAlertIds(newSelectedAlerts);
      if (newSelectedAlerts.length === results?.length) {
        setAllSelected(true);
      }
    }
  };

  const toggleSelectAll = () => {
    if (allSelected) {
      setSelectedAlertIds([]);
      setAllSelected(false);
    } else {
      setSelectedAlertIds(results?.map(alert => alert['&id']));
      setAllSelected(true);
    }
  };

  const fromStatusMap = getFromStatusMap(results?.filter(alert => isSelected(alert)));

  const handleRestoreClick = () => {
    setIsUpdatingStatus(true);
    updateAlerts({
      alertIds: selectedAlertIds,
      workflowUpdate: {
        status: SURVEILLANCE_ALERT_HIT_STATUS_UNRESOLVED,
      },
      fromStatus: fromStatusMap,
    }).then(() => {
      setIsUpdatingStatus(false);
      onUIRefreshRequired();
    });
  };

  const renderHeaderButtons = () => {
    const isAllowed = checkIsTransitionAllowed(fromStatusMap, tSurvWorkflow, UNRESOLVED);
    if (!selectedAlertIds.length) {
      return null;
    }
    return (
      <PermissionWrapper
        permitted={permissions?.canAlertWorkflow && isAllowed}
        message={
          !isAllowed && permissions?.canAlertWorkflow
            ? formatMessage(messages.notAllowedDueToWorkflow, { owner: tSurvWorkflow.createdBy })
            : ''
        }
      >
        <ButtonWithIcon
          key="restore"
          color="green"
          iconName="ccw"
          disabled={isUpdatingStatus || !permissions?.canAlertWorkflow || !isAllowed}
          onClick={handleRestoreClick}
          text={formatMessage(ALERT_ACTION_BUTTON_MESSAGES.restore)}
        />
      </PermissionWrapper>
    );
  };

  const handleSort = (fieldName, order) => {
    setSortQuery({ fieldName, order });
  };

  const handlePagination = change => {
    if (change.take && change.take !== paginationQuery.take) {
      // Reset to first page if the page size changes
      setPaginationQuery({ skip: 0, take: change.take });
    } else {
      setPaginationQuery({ ...paginationQuery, ...change });
    }
  };

  // update table when subcategories selected
  const updateTable = resolvedStatuses.filter(status => activeAlertStatus.includes(status));

  useEffect(() => {
    const apiQuery = {
      ...paginationQuery,
      ...omitF(query, [fStringConfig.find(f => f.key === 'STATUS')]),
      sort: makeSortQuery(sortQuery),
    };

    fetchAlertsList({
      query: apiQuery,
      statuses: resolvedStatuses.some(element => activeAlertStatus.includes(element))
        ? resolvedStatuses.filter(status => activeAlertStatus.includes(status))
        : resolvedStatuses,
      statusType: RESOLVED,
    });
    setSelectedAlertIds([]);
  }, [
    fetchAlertsList,
    paginationQuery,
    query,
    refineFilters,
    sortQuery,
    triggerRefreshUI,
    updateTable.length,
  ]);

  const { formatMessage } = useIntl();
  const sortProps = { currentSort: sortQuery, onClick: handleSort };
  const currentColorStyle = isClassicTheme ? 'gray' : 'black';

  return (
    <CollapsibleTableContainer
      noPadding
      total={totalHits}
      open={isOpen}
      title={MESSAGES.title}
      countMessage={MESSAGES.alertsCount}
      loading={loading || isUpdatingStatus}
      onToggleVisibility={toggle}
      headerButtons={renderHeaderButtons()}
      selectedCount={selectedAlertIds.length}
      titleBarClassName={styles.collapsibleDarker}
    >
      <section data-test-id={testId}>
        <div
          className={cx({
            [styles.table]: isClassicTheme,
            [styles.newTable]: !isClassicTheme,
            [styles.tableLoading]: !!loading,
          })}
          style={{ gridTemplateColumns: `repeat(${FIELD_COUNT}, minmax(64px, auto))` }}
        >
          <HeaderCheckboxCell
            black={!isClassicTheme}
            checked={allSelected}
            onChange={toggleSelectAll}
          />
          <SortingHeaderCell
            color={currentColorStyle}
            fieldName="hit.timestamps.orderSubmitted"
            {...sortProps}
          >
            {formatMessage(messages.orderSubmittedShort)}
          </SortingHeaderCell>
          <SortingHeaderCell
            color={currentColorStyle}
            fieldName="hit.timestamps.tradingDateTime"
            {...sortProps}
          >
            {formatMessage(messages.tradingDateTime)}
          </SortingHeaderCell>
          <SortingHeaderCell color={currentColorStyle} fieldName="slug" {...sortProps}>
            {formatMessage(messages.alertId)}
          </SortingHeaderCell>
          <SortingHeaderCell color={currentColorStyle} fieldName="detected" {...sortProps}>
            {formatMessage(messages.detectedOn)}
          </SortingHeaderCell>
          <SortingHeaderCell
            color={currentColorStyle}
            fieldName="hit.orderIdentifiers.transactionRefNo"
            {...sortProps}
          >
            {formatMessage(messages.tradeId)}
          </SortingHeaderCell>
          <div styleName="headerCell">{formatMessage(messages.trader)}</div>
          <SortingHeaderCell
            color={currentColorStyle}
            fieldName="hit.counterparty.name"
            {...sortProps}
          >
            {formatMessage(messages.counterparty)}
          </SortingHeaderCell>
          <SortingHeaderCell
            color={currentColorStyle}
            fieldName="hit.instrumentDetails.instrument.instrumentFullName"
            {...sortProps}
          >
            {formatMessage(messages.instrument)}
          </SortingHeaderCell>
          <SortingHeaderCell
            color={currentColorStyle}
            fieldName="hit.transactionDetails.priceCurrency"
            {...sortProps}
          >
            {formatMessage(messages.priceCurrency)}
          </SortingHeaderCell>
          <SortingHeaderCell
            color={currentColorStyle}
            fieldName="hit.priceFormingData.price"
            {...sortProps}
          >
            {formatMessage(messages.price)}
          </SortingHeaderCell>
          <SortingHeaderCell
            color={currentColorStyle}
            fieldName="hit.priceFormingData.tradedQuantity"
            {...sortProps}
          >
            {formatMessage(messages.tradedQuantity)}
          </SortingHeaderCell>
          <div styleName="headerCell">{formatMessage(messages.timeSinceDetection)}</div>
          <SortingHeaderCell color={currentColorStyle} fieldName="detail.queryType" {...sortProps}>
            {formatMessage(messages.queryType)}
          </SortingHeaderCell>
          <SortingHeaderCell color={currentColorStyle} fieldName="detail.watchName" {...sortProps}>
            {formatMessage(messages.watchName)}
          </SortingHeaderCell>
          <SortingHeaderCell
            color={currentColorStyle}
            fieldName="detail.watchPriority"
            {...sortProps}
          >
            {formatMessage(messages.watchPriority)}
          </SortingHeaderCell>
          <SortingHeaderCell
            color={currentColorStyle}
            fieldName="detail.marketAbuseReportType"
            {...sortProps}
          >
            {formatMessage(messages.reportType)}
          </SortingHeaderCell>
          <SortingHeaderCell
            color={currentColorStyle}
            fieldName="hit.flags.TCAFlagStatus.keyword"
            {...sortProps}
          >
            {formatMessage(messages.TCAFlag)}
          </SortingHeaderCell>
          <SortingHeaderCell
            color={currentColorStyle}
            fieldName="workflow.resolvedByName"
            {...sortProps}
          >
            {formatMessage(messages.resolvedBy)}
          </SortingHeaderCell>
          <SortingHeaderCell color={currentColorStyle} fieldName="resolved" {...sortProps}>
            {formatMessage(messages.resolvedDate)}
          </SortingHeaderCell>
          <SortingHeaderCell
            color={currentColorStyle}
            fieldName="workflow.resolutionCategory"
            {...sortProps}
          >
            {formatMessage(messages.resolutionCategory)}
          </SortingHeaderCell>
          <div styleName="headerCell">{formatMessage(messages.resolutionSubcategories)}</div>

          {results?.map(alert => (
            <AlertTableRow
              data={alert}
              showCheckBox
              key={alert['&id']}
              isSelected={isSelected(alert)}
              toggleSelection={() => toggleAlertSelection(alert)}
              showResolutionCategory
            />
          ))}
          {!results?.length && !loading && <NoResultsRow />}
        </div>
        <div styleName={totalHits > paginationQuery.take ? 'paginationContainer' : ''}>
          <Pagination
            total={totalHits}
            showPageSizeSelector={totalHits > paginationQuery.take}
            {...paginationQuery}
            onPageSizeAndPaginationChange={handlePagination}
          />
        </div>
      </section>
    </CollapsibleTableContainer>
  );
};

ResolvedAlertsTable.propTypes = {
  alertListData: PropTypes.object,
  fetchAlertsList: PropTypes.func.isRequired,
  match: PropTypes.object,
  onUIRefreshRequired: PropTypes.func,
  query: PropTypes.object,
  refineFilters: PropTypes.array,
  testId: PropTypes.string,
  triggerRefreshUI: PropTypes.number,
  updateAlerts: PropTypes.func.isRequired,
};

ResolvedAlertsTable.defaultProps = {
  alertListData: null,
  query: {},
  refineFilters: [],
  testId: 'TSurv.ResolvedAlertsTable',
};

const mapDispatchToProps = {
  updateAlerts: alertActions.updateAlerts,
};

export default withRouter(connect(null, mapDispatchToProps)(ResolvedAlertsTable));

@import 'less/site.less';

.dropdown {
  position: relative;
  z-index: 100;
  & > div:first-child {
    margin: 0;
  }
}

.inputGroupMultiSelect {
  max-width: initial;
  width: 100%;
  box-shadow: 0 0 4px 2px @blue;
  border-radius: 6px;

  input:not([type='checkbox']) {
    max-width: 100%;
    min-width: initial;
    padding: 0 10px;
    font-size: 14px;
    line-height: 1.1;
    background-color: @dark-background;
    border: none;

    &:focus,
    &:active {
      background-color: @widget-base;
      color: @gray-light;
    }

    span {
      color: @dark-background;
      width: 100%;
    }
  }

  & > span {
    background-color: @dark-background;
    color: @gray;

    & > *:before {
      /* Subtle shift to stop the chevron looking off-centre */
      transform: translateY(-2px);
    }
  }

  & > span:first-child {
    display: none; // Hide the unnecessary 'All' option selector
  }

  & > span:last-child:hover {
    color: @gray;
    background-color: @dark-background;
  }

  button {
    margin-right: 40px;

    &:hover {
      color: @gray;
      background-color: inherit;
    }
  }
}

.multiSelect {
  position: absolute;
  right: 80px;
  margin: 0;
  margin-top: 8px;
  padding: 0;
  width: 380px;
  & > div > div {
    min-width: initial;
  }
}

.expandBtn {
  position: absolute;
  right: 80px;
  margin: 0;
  margin-top: 8px;
  padding: 0;
}

import React, { useState } from 'react';
import { connect } from 'react-redux';
import { defineMessages, useIntl } from 'react-intl';
import { submit } from 'redux-form';
import PropTypes from 'prop-types';

import Button from 'js/view/components/button/basic-button';
import DeleteElementButton from 'view/components/button/delete-element';

import * as formConstants from 'constants/form';
import * as marConstants from 'constants/market-abuse/thresholds';

import {
  PRICE_CHECK_NATURE_OF_CHECKS,
  PRICE_CHECK_STRATEGIES,
} from 'constants/market-abuse/thresholds';

import AddPriceCheckThresholdModal from '../modal/add-price-check-threshold';
import PriceCheckEditableTable, { TYPE } from './price-check-editable-table';

const messages = defineMessages({
  addPriceCheck: {
    defaultMessage: 'Add New Price Check',
    id: 'mar.button.addNewPriceCheck.title',
  },
  caption: {
    defaultMessage: '(click cells to edit)',
    id: 'mar.priceCheckThresholds.caption',
  },
  error: {
    defaultMessage: 'At least one price check must be present',
    id: 'mar.priceCheckThreshold.isrequired',
  },
});

const daysFromValidator = (newValue, row) => {
  const { daysTo } = row;
  const daysFrom = +newValue;
  if (daysFrom < 0) {
    return { message: marConstants.VALIDATION_MESSAGES.minZero.defaultMessage, valid: false };
  }
  if (daysFrom > 10) {
    return { message: marConstants.VALIDATION_MESSAGES.maxTen.defaultMessage, valid: false };
  }
  if (daysFrom > +daysTo) {
    return { message: marConstants.VALIDATION_MESSAGES.daysFrom.defaultMessage, valid: false };
  }
  return true;
};

const daysToValidator = (newValue, row) => {
  const { daysFrom } = row;
  const daysTo = +newValue;
  if (daysTo < 0) {
    return { message: marConstants.VALIDATION_MESSAGES.minZero.defaultMessage, valid: false };
  }
  if (daysTo > 10) {
    return { message: marConstants.VALIDATION_MESSAGES.maxTen.defaultMessage, valid: false };
  }
  if (daysTo < +daysFrom) {
    return { message: marConstants.VALIDATION_MESSAGES.daysTo.defaultMessage, valid: false };
  }
  return true;
};

const thresholdValidator = (newValue, row) => {
  const { natureOfCheck } = row;
  const numericValue = +newValue;
  const isPercentagePriceMovemement =
    natureOfCheck === marConstants.PRICE_CHECK_NATURE_OF_CHECK_MOVEMENT ||
    natureOfCheck === marConstants.PRICE_CHECK_NATURE_OF_CHECK_MOVEMENT_CLOSE_TO_CLOSE;
  if (
    isPercentagePriceMovemement &&
    !(
      marConstants.PRICE_CHECK_MOVEMENT_MIN_VALUE <= numericValue &&
      numericValue <= marConstants.PRICE_CHECK_MOVEMENT_MAX_VALUE
    )
  ) {
    return {
      message: marConstants.VALIDATION_MESSAGES.percentageThreshold.defaultMessage,
      valid: false,
    };
  } else if (
    !isPercentagePriceMovemement &&
    (marConstants.PRICE_CHECK_VOLATILITY_MIN_VALUE > numericValue ||
      marConstants.PRICE_CHECK_VOLATILITY_MAX_VALUE < numericValue)
  ) {
    return {
      message: marConstants.VALIDATION_MESSAGES.volatilityThreshold.defaultMessage,
      valid: false,
    };
  }
  return true;
};

const getColumns = handleRowDelete => [
  {
    dataField: 'strategy',
    editor: {
      options: PRICE_CHECK_STRATEGIES.map(strategy => ({ label: strategy, value: strategy })),
      type: TYPE.SELECT,
    },
    text: 'Strategy',
  },
  {
    dataField: 'daysFrom',
    editor: { type: TYPE.TEXT },
    text: 'Days From',
    validator: daysFromValidator,
  },
  {
    dataField: 'daysTo',
    editor: { type: TYPE.TEXT },
    text: 'Days To',
    validator: daysToValidator,
  },
  {
    dataField: 'natureOfCheck',
    editor: {
      // TODO remove LEGACY suffix
      options: PRICE_CHECK_NATURE_OF_CHECKS.map(check => ({ label: check, value: check })),
      type: TYPE.SELECT,
    },
    text: 'Nature of Check',
  },
  {
    dataField: 'threshold',
    editor: { type: TYPE.TEXT },
    formatter: (cellContent, row) => {
      if (
        row.natureOfCheck === marConstants.PRICE_CHECK_NATURE_OF_CHECK_MOVEMENT &&
        +cellContent < 0
      ) {
        return +cellContent * 100;
      }
      return +cellContent;
    },
    text: 'Threshold',
    validator: thresholdValidator,
  },
  {
    dataField: 'delete',
    editable: false,
    formatter: (cellContent, row) => {
      return <DeleteElementButton bsSize="sm" onClick={() => handleRowDelete(row.index)} />;
    },
    isDummyField: true,
    style: () => {
      return { textAlign: 'center' };
    },
    text: 'Delete',
  },
];

const PriceCheckThresholds = ({ onChangeComplete, value, onSubmit, tooltips }) => {
  const [showAddModal, setShowModal] = useState(false);
  const { formatMessage } = useIntl();

  const handleRowDelete = index => {
    let error;
    const newPriceChecks = value.filter(d => d.index !== index).map((d, i) => ({ ...d, index: i }));
    if (!newPriceChecks.length) {
      error = formatMessage(messages.error);
    }
    onChangeComplete(newPriceChecks, error);
  };

  const handleRowUpdate = (oldValue, newValue, row) => {
    const newPriceChecks = value.map(s => {
      if (s.index === row.index) return row;
      return s;
    });
    onChangeComplete(newPriceChecks);
  };

  const handleAddSubmit = values => {
    const newPriceChecks = value.concat({ ...values, index: value.length });
    onChangeComplete(newPriceChecks);
    handleCancelAdd();
  };

  const handleRowAddClick = () => {
    setShowModal(true);
  };

  const handleCancelAdd = () => {
    setShowModal(false);
  };

  const handleConfirmClick = () => {
    onSubmit(formConstants.MAR_PRICE_THRESHOLD_FORM);
  };
  return (
    <div style={{ width: '100%' }}>
      <AddPriceCheckThresholdModal
        formSubmit={handleAddSubmit}
        onCancelClick={handleCancelAdd}
        onConfirmClick={handleConfirmClick}
        show={showAddModal}
        data={{
          daysFrom: 0,
          daysTo: 0,
          index: 0,
          natureOfCheck: 'Volatility from Trade Date',
          strategy: 'Forward Looking',
          threshold: 0,
        }}
      />
      <Button
        color="blue"
        onClick={handleRowAddClick}
        testId="Tsurv.Components.PriceCheckThresholds.AddPriceCheck"
      >
        <span className="icon icon-plus" />
        &nbsp;
        {formatMessage(messages.addPriceCheck)}
      </Button>
      <PriceCheckEditableTable
        columns={getColumns(handleRowDelete)}
        caption={formatMessage(messages.caption)}
        data={value || []}
        onRowUpdate={handleRowUpdate}
        tooltips={tooltips}
      />
    </div>
  );
};

PriceCheckThresholds.propTypes = {
  onChangeComplete: PropTypes.func,
  onSubmit: PropTypes.func,
  tooltips: PropTypes.object,
  value: PropTypes.array,
};

export default connect(null, {
  onSubmit: submit,
})(PriceCheckThresholds);

import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import { defineMessages } from 'react-intl';

import { useSelector } from 'react-redux';

import FetchErrorMessage from 'view/components/fetch-error-message/fetch-error-message';

import {
  ESCALATED,
  IN_PROGRESS,
  IN_REVIEW,
  RESOLVED,
  RESOLVED_WITH_BREACH,
  RESOLVED_WITH_DISMISSAL,
  RESOLVED_WITH_INVESTIGATION,
  RESOLVED_WITH_INVESTIGATION_WITH_BREACH,
  UNDER_INVESTIGATION,
  UNRESOLVED,
} from 'constants/surveillance';
import useFString from 'js/hooks/fString/useFString';
import useAlertStatusToggle from 'js/hooks/useAlertStatusToggle';
import { alertStatusLabelMap } from 'js/util/surveillance-parsing';

import MarScenariosTable from './mar-scenarios-table';

const messages = defineMessages({
  [RESOLVED]: {
    defaultMessage: 'Resolved',
    id: 'module.tradeSurv.watchDetails.resolved',
  },
  [UNDER_INVESTIGATION]: {
    defaultMessage: 'Under Investigation',
    id: 'module.tradeSurv.watchDetails.underInvestigation',
  },
  [UNRESOLVED]: {
    defaultMessage: 'Unresolved',
    id: 'module.tradeSurv.watchDetails.unresolved',
  },
});

const statusOptions = {
  RESOLVED: [
    RESOLVED,
    RESOLVED_WITH_BREACH,
    RESOLVED_WITH_DISMISSAL,
    RESOLVED_WITH_INVESTIGATION,
    RESOLVED_WITH_INVESTIGATION_WITH_BREACH,
  ],
  UNDER_INVESTIGATION: [UNDER_INVESTIGATION, IN_PROGRESS, IN_REVIEW, ESCALATED],
  UNRESOLVED: [UNRESOLVED],
};

const MarScenariosTableWrapper = ({
  fetchMarScenarios,
  marScenariosData,
  onRefresh,
  queries,
  refineFilters,
  reportType,
  status,
  watchId,
  statusQuery,
  assignedQuery,
  testId,
}) => {
  const { isOpen, toggle } = useAlertStatusToggle(status);
  const { omitF, fStringConfig } = useFString();
  const { data, loading, errors } = marScenariosData?.[status] || {};
  // update table when subcategories selected
  const updateTable = [
    ...statusOptions[status].filter(subStatus => statusQuery.includes(subStatus)),
    ...(status === UNRESOLVED ? assignedQuery : []),
  ].length;

  const statusSummary = useSelector(
    state => state.tradesSurv.singleWatch.summaryByStatus?.[watchId]?.data
  );

  const itemsCount = useMemo(
    () =>
      alertStatusLabelMap[status] ? statusSummary?.[alertStatusLabelMap[status]]?.total || 0 : 0,
    [status, statusSummary]
  );

  if (errors) return <FetchErrorMessage />;
  return (
    <MarScenariosTable
      scenarios={data?.results}
      loading={loading}
      itemsCount={itemsCount}
      model={reportType}
      onFetchData={options =>
        fetchMarScenarios({
          ...options,
          query:
            options.query && omitF(options.query, [fStringConfig.find(f => f.key === 'STATUS')]),
          refineFilters: [], // TODO we can stop the <MarScenariosTable /> from sending this once useFString is used everywhere. Here, the refineFilters are duplicated in options.query.f
          status,
          statuses: statusOptions[status].some(element => statusQuery.includes(element))
            ? statusOptions[status].filter(subStatus => statusQuery.includes(subStatus))
            : statusOptions[status],
          unresolvedAlertsAssigneeStatus: assignedQuery,
        })
      }
      isOpen={isOpen}
      toggle={toggle}
      onRefresh={onRefresh}
      refineFilters={refineFilters}
      query={queries}
      watchId={watchId}
      status={status}
      title={messages[status]}
      updateTable={updateTable}
      testId={testId}
    />
  );
};

MarScenariosTableWrapper.propTypes = {
  assignedQuery: PropTypes.array.isRequired,
  fetchMarScenarios: PropTypes.func.isRequired,
  marScenariosData: PropTypes.object,
  onRefresh: PropTypes.func.isRequired,
  queries: PropTypes.object,
  refineFilters: PropTypes.array,
  reportType: PropTypes.string.isRequired,
  status: PropTypes.string.isRequired,
  statusQuery: PropTypes.array.isRequired,
  testId: PropTypes.string,
  watchId: PropTypes.string.isRequired,
};

MarScenariosTableWrapper.defaultProps = {
  marScenariosData: {},
  queries: {},
  refineFilters: [],
};

export default MarScenariosTableWrapper;

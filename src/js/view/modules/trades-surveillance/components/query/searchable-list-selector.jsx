import React, { useState, useEffect } from 'react';
import moment from 'moment';
import Checkbox from 'js/view/components/basic/checkbox';
import { useIntl, defineMessages } from 'react-intl';
import uniq from 'lodash/uniq';
import differenceBy from 'lodash/differenceBy';
import orderBy from 'lodash/orderBy';

import SearchInput from 'view/components/input/search-input-component';

import './searchable-list-selector.less';

const MESSAGES = defineMessages({
  noItemsFound: {
    defaultMessage: 'No items found',
    id: 'view.tradeSurv.newWatch.query.SearchableListSelector.noItemsFound',
  },
});

const SearchableListSelector = ({
  selected,
  fieldType,
  fetchData,
  idSelector,
  fetchOptions,
  nameSelector,
  renderActions,
  onItemSelected,
  allowSelection,
  allowSelectAll,
  searchPlaceholder,
  listingData,
  testId,
  noSearchBar,
}) => {
  const { formatMessage } = useIntl();
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchData({ ...fetchOptions, searchTerm });
  }, [JSON.stringify(fetchOptions), searchTerm]);

  const handleSearch = event => {
    let term = '';
    if (typeof event === 'string') {
      term = event;
    } else {
      event.stopPropagation();
      term = event.target.value;
    }
    setSearchTerm(term);
  };

  const handleSelected = item => {
    let newList;
    const isSelected = selected.filter(i => idSelector(i) === idSelector(item)).length > 0;
    if (isSelected) {
      newList = selected.filter(i => idSelector(i) !== idSelector(item));
    } else {
      newList = selected.concat([item]);
    }
    onItemSelected(newList);
  };

  const handleSelectAll = e => {
    let updatedSelections = selected;
    if (e.target.checked) {
      updatedSelections = uniq(updatedSelections.concat(listingData?.data));
    } else {
      updatedSelections = [];
    }
    onItemSelected(updatedSelections);
  };

  let results = listingData?.data || [];
  const allSelected = results.length && differenceBy(results, selected, '&id').length === 0;
  if (results.length && results[0].count) results = orderBy(results, ['count'], ['desc']);
  return (
    <div styleName={`container ${listingData?.loading ? 'loading' : ''}`}>
      <div styleName="search-container">
        {allowSelectAll && allowSelection && (
          <Checkbox
            checked={allSelected}
            onChange={handleSelectAll}
            data-test-id={`${testId}.AllSelect`}
          />
        )}
        <SearchInput
          className="search-input"
          searchPlaceholder={searchPlaceholder}
          input={{
            value: searchTerm,
            name: 'field-search',
            onChange: handleSearch,
          }}
          testId={`${testId}.SearchInput`}
          disabled={noSearchBar}
        />
      </div>
      {results.length > 0 ? (
        <ul styleName="checkbox-list" data-test-id={`${testId}.SearchableList`}>
          {results.map(item => {
            const isSelected = selected.filter(l => idSelector(l) === idSelector(item)).length > 0;
            return (
              <li key={idSelector(item)} styleName="list-item">
                {allowSelection && (
                  <Checkbox checked={isSelected} onChange={() => handleSelected(item)}>
                    <span styleName={`content ${isSelected ? 'selected' : ''}`}>
                      {fieldType === 'date'
                        ? moment(item.name).format('DD-MM-YYYY, HH:MM:ss')
                        : nameSelector(item)}
                    </span>
                  </Checkbox>
                )}
                {typeof item.count === 'number' && (
                  <span styleName="count">({item.count.toLocaleString()})</span>
                )}
                {renderActions && renderActions(item)}
              </li>
            );
          })}
        </ul>
      ) : (
        <p styleName="no-items-text">{formatMessage(MESSAGES.noItemsFound)}</p>
      )}
    </div>
  );
};

SearchableListSelector.defaultProps = {
  allowSelection: true,
  idSelector: item => item.id,
  nameSelector: item => item.name,
  selected: [],
  testId: 'TSurv.SearchableListSelector',
};

export default SearchableListSelector;

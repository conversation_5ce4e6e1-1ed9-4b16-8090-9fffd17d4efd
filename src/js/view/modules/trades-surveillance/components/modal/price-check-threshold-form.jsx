import React from 'react';
import { reduxForm, Field } from 'redux-form';
import PropTypes from 'prop-types';

import AutosuggestFormControl from 'view/components/form-control/autosuggest';
import Row from 'js/view/components/basic-grid/row';
import Col from 'js/view/components/basic-grid/col';
import Form from 'view/components/form';
import TextField from 'view/components/form-control/text';

import * as formConstants from 'constants/form';
import * as thresholdsConstants from 'constants/market-abuse/thresholds';
import * as validationUtil from 'util/validation';

const STRATEGY_OPTIONS = thresholdsConstants.PRICE_CHECK_STRATEGIES.map(l => ({
  label: l,
  value: l,
}));

const validators = {
  daysFrom: (value, allValues) => {
    if (!value || !allValues.daysTo) {
      return undefined;
    }
    return +value > +allValues.daysTo
      ? thresholdsConstants.VALIDATION_MESSAGES.daysFrom
      : undefined;
  },
  daysTo: (value, allValues) => {
    if (!value || !allValues.daysFrom) {
      return undefined;
    }
    return +value < +allValues.daysFrom
      ? thresholdsConstants.VALIDATION_MESSAGES.daysTo
      : undefined;
  },
  maxValue: max => value => {
    // eslint-disable-next-line no-unused-expressions
    return value && +value > max ? thresholdsConstants.VALIDATION_MESSAGES.maxTen : undefined;
  },
  minValue: min => value => {
    // eslint-disable-next-line no-unused-expressions
    return value && +value < min ? thresholdsConstants.VALIDATION_MESSAGES.minZero : undefined;
  },
  threshold: (value, allValues) => {
    const isPercentagePriceMovemement =
      allValues.natureOfCheck === thresholdsConstants.PRICE_CHECK_NATURE_OF_CHECK_MOVEMENT ||
      allValues.natureOfCheck ===
        thresholdsConstants.PRICE_CHECK_NATURE_OF_CHECK_MOVEMENT_CLOSE_TO_CLOSE;
    if (!value) {
      return undefined;
    }
    if (
      isPercentagePriceMovemement &&
      !(
        thresholdsConstants.PRICE_CHECK_MOVEMENT_MIN_VALUE <= +value &&
        +value <= thresholdsConstants.PRICE_CHECK_MOVEMENT_MAX_VALUE
      )
    ) {
      return thresholdsConstants.VALIDATION_MESSAGES.percentageThreshold;
    } else if (
      !isPercentagePriceMovemement &&
      (thresholdsConstants.PRICE_CHECK_VOLATILITY_MIN_VALUE > +value ||
        thresholdsConstants.PRICE_CHECK_VOLATILITY_MAX_VALUE < +value)
    ) {
      return thresholdsConstants.VALIDATION_MESSAGES.volatilityThreshold;
    }
  },
};

const maxValue10 = validators.maxValue(10);
const minValue0 = validators.minValue(0);

const PriceCheckThreshold = ({ handleSubmit }) => {
  const CHECK_OPTIONS = thresholdsConstants.PRICE_CHECK_NATURE_OF_CHECKS.map(check => ({
    label: check,
    value: check,
  }));

  return (
    <Form onSubmit={handleSubmit} testId="Tsurv.Components.PriceCheckThreshold.Form">
      <Row key="daysFromAndTo">
        <Col key="strategy" xs={6}>
          <Field
            labelMessage={thresholdsConstants.THRESHOLD_MESSAGES.INSIDER_TRADING_STRATEGY}
            name="strategy"
            component={AutosuggestFormControl}
            isFullWidth
            options={STRATEGY_OPTIONS}
            validate={validationUtil.validators.required}
          />
        </Col>
        <Col key="daysFrom" xs={3}>
          <Field
            labelMessage={thresholdsConstants.THRESHOLD_MESSAGES.INSIDER_TRADING_DAYS_FROM}
            name="daysFrom"
            component={TextField}
            max={10}
            type="number"
            validate={[
              validationUtil.validators.required,
              minValue0,
              maxValue10,
              validators.daysFrom,
            ]}
          />
        </Col>
        <Col key="daysTo" xs={3}>
          <Field
            labelMessage={thresholdsConstants.THRESHOLD_MESSAGES.INSIDER_TRADING_DAYS_TO}
            name="daysTo"
            component={TextField}
            max={10}
            type="number"
            validate={[
              validationUtil.validators.required,
              minValue0,
              maxValue10,
              validators.daysTo,
            ]}
          />
        </Col>
      </Row>
      <Row key="natureOfCheckAndThreshold">
        <Col key="natureOfCheck" xs={6}>
          <Field
            labelMessage={thresholdsConstants.THRESHOLD_MESSAGES.INSIDER_TRADING_NATURE_OF_CHECKS}
            name="natureOfCheck"
            component={AutosuggestFormControl}
            isFullWidth
            options={CHECK_OPTIONS}
            validate={validationUtil.validators.required}
          />
        </Col>
        <Col key="threshold" xs={6}>
          <Field
            labelMessage={thresholdsConstants.THRESHOLD_MESSAGES.INSIDER_TRADING_THRESHOLD}
            name="threshold"
            component={TextField}
            type="number"
            validate={[validationUtil.validators.required, validators.threshold]}
          />
        </Col>
      </Row>
    </Form>
  );
};

PriceCheckThreshold.propTypes = {
  handleSubmit: PropTypes.func.isRequired,
};

const PriceCheckThresholdForm = reduxForm({
  form: formConstants.MAR_PRICE_THRESHOLD_FORM,
  touchOnBlur: false,
})(PriceCheckThreshold);

export default PriceCheckThresholdForm;

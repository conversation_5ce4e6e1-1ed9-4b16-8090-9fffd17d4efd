import React from 'react';

import includes from 'lodash/includes';
import cloneDeep from 'lodash/cloneDeep';
import drop from 'lodash/drop';
import countBy from 'lodash/countBy';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import moment from 'moment';
import {
  SURVEILLANCE_RANGE_TYPE_LESS_THAN,
  SURVEILLANCE_RANGE_TYPE_GREATER_THAN,
  SURVEILLANCE_RANGE_TYPE_LESS_THAN_OR_EQUAL_TO,
  SURVEILLANCE_RANGE_TYPE_GREATER_THAN_OR_EQUAL_TO,
} from '@steeleye/schema/models/enums';

import {
  SURVE<PERSON>LANCE_RANGE_TYPE_BELOW,
  SURVEILLANCE_RANGE_TYPE_ABOVE,
  SURVEILLANCE_RANGE_TYPE_BETWEEN,
  SURVEILLANCE_RANGE_TYPE_ABOVE_BELOW,
} from '@steeleye/schema/models/enums/surveillance-range-type';

import { defineMessages, injectIntl, FormattedMessage, useIntl } from 'react-intl';

// Components
import IconButton from 'view/components/button/icon-button';

// Constants and Utils
import {
  BOOLEAN_FIELDS,
  BESTEX_FILTER_FIELD,
  FIELD_VALUE_FILTER_KEYS,
} from 'constants/comms-surveillance';
import {
  NUMBER_DATA_TYPE,
  FIELD_DISPLAY_MESSAGES,
  QUERY_PREVIEW_MESSAGES,
  EMAIL_ZONING_QUERY_SECTION_MESSAGES as zoningQuerySectionMessages,
  CLASSIFIER_QUERY_SECTION_MESSAGES as classifierQuerySectionMessages,
} from 'constants/surveillance';
import { MESSAGES } from 'constants/form';
import { getEnumDisplayValue } from 'util/enum';
import { HOUR_IN_MILLISECONDS } from 'constants/time';
import { PROPERTY_MESSAGES } from 'constants/property';
import { convertMillisecondsToTimeString } from 'util/time';
import { METRIC_TYPE_TITLE_MESSAGES } from 'constants/best-execution';
import { unsetFilterFields, filterPartData } from 'util/query-filter-remove';
import { VALUE_RANGE_OPTIONS } from 'js/view/components/surveillance/value-range-slider/value-range-slider';

import './styles.less';

const messages = defineMessages({
  values: {
    defaultMessage: 'values',
    id: 'modules.tSurv.queryPreview.values',
  },
  excludeConvertedCurrency: {
    defaultMessage: 'Exclude Converted Currency',
    id: 'modules.tSurv.queryPreview.excludeConvertedCurrency',
  },
  yes: {
    defaultMessage: 'Yes',
    id: 'modules.tSurv.queryPreview.yes',
  },
  no: {
    defaultMessage: 'No',
    id: 'modules.tSurv.queryPreview.no',
  },
});

const FILTER_SECTION_LABEL_MESSAGE = {
  analytics: PROPERTY_MESSAGES.classifier,
  attachments: PROPERTY_MESSAGES.attachments,
  bestEx: PROPERTY_MESSAGES.bestEx,
  counterparties: PROPERTY_MESSAGES.firms,
  dataSources: PROPERTY_MESSAGES.dataSource,
  firms: PROPERTY_MESSAGES.firms,
  instruments: PROPERTY_MESSAGES.instruments,
  lexica: PROPERTY_MESSAGES.lexicon,
  orderDetails: PROPERTY_MESSAGES.orderDetails,
  people: PROPERTY_MESSAGES.people,
  time: PROPERTY_MESSAGES.time,
  tradeDetails: PROPERTY_MESSAGES.tradeDetailsSurv,
  tradeDetailsQuantity: PROPERTY_MESSAGES.tradeDetailsQuantitySurv,
  traders: PROPERTY_MESSAGES.people,
};

const TradeDetailsMessageTypes = VALUE_RANGE_OPTIONS.reduce((acc, cur) => {
  acc = {
    ...acc,
    [cur.key]: cur.title,
  };
  return acc;
}, {});

const QueryPreview = ({
  intl,
  query,
  onCompletePress,
  boolQueryChanged,
  updateWatchQuery,
  editable,
}) => {
  const { formatMessage } = useIntl();
  const getAllFilters = () => {
    const timeFilter = getTimeFilter();
    const bestExFilter = getBestExFilter();
    const lexicaFilters = getLexicaFilters();
    const peopleFilters = getPeopleFilters();
    const commsFirmFilters = getCommsFirmFilters();
    const fieldValueFilters = getFieldValueFilters();
    const classifierFilters = getClassifierFilters();
    const attachmentsFilters = getAttachmentsFilters();
    const commsDataSourceFilters = getCommsDataSourceFilters();

    return {
      ...timeFilter,
      ...bestExFilter,
      ...lexicaFilters,
      ...peopleFilters,
      ...commsFirmFilters,
      ...classifierFilters,
      ...fieldValueFilters,
      ...attachmentsFilters,
      ...commsDataSourceFilters,
    };
  };

  const getQuantityFilters = quantity => {
    const { field, rangeType, value, rangeMin, rangeMax, currency } = quantity;
    const titleKey = currency ? field.replace(`.${currency}`, '') : field;
    const title = formatMessage(FIELD_DISPLAY_MESSAGES[titleKey], {
      currency: currency || '',
    });
    // TODO: replace these when @steeleye/schema/models/enums/surveillance-range-type has the values
    const isOperator =
      rangeType === SURVEILLANCE_RANGE_TYPE_LESS_THAN ||
      rangeType === SURVEILLANCE_RANGE_TYPE_GREATER_THAN ||
      rangeType === SURVEILLANCE_RANGE_TYPE_LESS_THAN_OR_EQUAL_TO ||
      rangeType === SURVEILLANCE_RANGE_TYPE_GREATER_THAN_OR_EQUAL_TO;

    const contents = `${
      isOperator
        ? `${formatMessage(TradeDetailsMessageTypes[rangeType])} ${value.toLocaleString()}`
        : `${rangeMin.toLocaleString()} - ${rangeMax.toLocaleString()}`
    }  ${currency ? `(${currency})` : ''}`;

    return {
      title,
      contents,
      key: `${field}${rangeType}`,
      ...quantity,
    };
  };

  const getFieldValueFilters = () => {
    const allFilters = {};
    Object.keys(query || {}).forEach(q => {
      if (FIELD_VALUE_FILTER_KEYS.indexOf(q) > -1) {
        if (!isEmpty(query[q])) {
          allFilters[q] = query[q].map(f => {
            if (f.fieldType === NUMBER_DATA_TYPE) {
              return getQuantityFilters(f);
            } else {
              const title = `${
                f.model ? formatMessage(FIELD_DISPLAY_MESSAGES[f.model]) : ''
              } ${formatMessage(FIELD_DISPLAY_MESSAGES[f.field])}`;
              const isBooleanField = BOOLEAN_FIELDS.indexOf(f.field) > -1;
              let contents;
              if (isBooleanField) {
                contents = f.values.map(v => (v === 1 ? 'Yes' : 'No')).join(', ');
              } else {
                contents =
                  f.values.length > 1
                    ? `${f.values.length} ${formatMessage(messages.values)}`
                    : f.values[0];
              }
              return { title, contents, ...f, key: `${title}.${f.field}.${f.model}` };
            }
          });
        }
      }
    });
    return allFilters;
  };

  const getTimeFilter = () => {
    const time = query?.time;
    if (!time) return {};
    const rangeType = getEnumDisplayValue(intl, time.rangeType, 'surveillance/range_type');

    if (!time.timeOfDayStartInMilliseconds) {
      time.timeOfDayStartInMilliseconds = time.hourStart * HOUR_IN_MILLISECONDS;
    }
    if (!time.timeOfDayEndInMilliseconds) {
      time.timeOfDayEndInMilliseconds = time.hourEnd * HOUR_IN_MILLISECONDS;
    }

    const hourStartTime = moment(
      convertMillisecondsToTimeString(time.timeOfDayStartInMilliseconds),
      'HH:mm:ss'
    ).format('h:mm:ss a');
    const hourEndTime = moment(
      convertMillisecondsToTimeString(time.timeOfDayEndInMilliseconds),
      'HH:mm:ss'
    ).format('h:mm:ss a');

    const days = time.daysOfWeek ? time.daysOfWeek.join(', ') : null;
    const contents = `${rangeType} ${hourStartTime} and ${hourEndTime} ${
      days ? ` on ${days}` : ''
    }`;
    const title = formatMessage(PROPERTY_MESSAGES.tradingTime);
    return {
      time: [{ title, contents, ...time, key: 'time' }],
    };
  };

  const getRangeMessage = ({ rangeType, rangeMaxDecimal, rangeMinDecimal, rangeMax, rangeMin }) => {
    const availableRangeMax = rangeMaxDecimal ?? rangeMax;
    const availableRangeMin = rangeMinDecimal ?? rangeMin;
    const rangeTypeMsg = getEnumDisplayValue(intl, rangeType, 'surveillance/range_type');
    switch (rangeType) {
      case SURVEILLANCE_RANGE_TYPE_BETWEEN:
        return `${rangeTypeMsg} : ${availableRangeMin}% and ${availableRangeMax}%`;
      case SURVEILLANCE_RANGE_TYPE_ABOVE_BELOW:
        return `${rangeTypeMsg} : ${availableRangeMin}% and ${availableRangeMax}%`;
      case SURVEILLANCE_RANGE_TYPE_ABOVE:
        return `${rangeTypeMsg} : ${availableRangeMin}%`;
      case SURVEILLANCE_RANGE_TYPE_BELOW:
        return `${rangeTypeMsg} : ${availableRangeMax}%`;
      default:
        return `${rangeTypeMsg} : ${availableRangeMin}% and ${availableRangeMax}%`;
    }
  };

  const getBestExFilter = () => {
    const bestEx = get(query, BESTEX_FILTER_FIELD.BESTEX, []);
    if (isEmpty(bestEx)) return {};
    const priceField = METRIC_TYPE_TITLE_MESSAGES[bestEx.priceField];
    const priceFilterValue = getRangeMessage({ ...bestEx });
    const speed = get(bestEx, BESTEX_FILTER_FIELD.SPEED_OF_EXECUTION, false);
    const bestExFilters = [];
    if (priceField && priceFilterValue) {
      bestExFilters.push({
        title: formatMessage(priceField),
        contents: `${priceFilterValue} (${formatMessage(messages.excludeConvertedCurrency)} - ${
          bestEx.excludeConvertedCurrency ? formatMessage(messages.yes) : formatMessage(messages.no)
        })`,
        field: BESTEX_FILTER_FIELD.PRICE,
        key: 'price',
      });
    }
    if (speed !== false && speed >= 0) {
      bestExFilters.push({
        title: formatMessage(PROPERTY_MESSAGES.speedOfExecution),
        contents: `${speed / 1000} seconds`,
        field: BESTEX_FILTER_FIELD.SPEED,
        key: 'speed',
      });
    }
    return {
      bestEx: bestExFilters,
    };
  };

  const getCommsFirmFilters = () => {
    const firms = query?.firms || [];
    if (firms.length === 0) return {};
    return {
      firms: firms.map(f => ({ title: 'Name', contents: [f], field: 'name', key: f })),
    };
  };

  const getCommsDataSourceFilters = () => {
    const dataSources = query?.dataSources || [];
    if (dataSources.length === 0) return {};
    return {
      dataSources: dataSources.map(f => ({ title: 'Name', contents: [f], field: 'name', key: f })),
    };
  };

  const getLexicaFilters = () => {
    const lexica = query?.lexica || [];
    if (lexica.length === 0) return {};
    const categoryCounts = countBy(lexica, l => l.category);
    return {
      lexica: Object.keys(categoryCounts).map(k => ({
        contents: [`${categoryCounts[k]} ${formatMessage(messages.values)}`],
        field: 'lexica',
        key: k,
        title: k,
      })),
    };
  };

  const getPeopleFilters = () => {
    const people = query?.people;
    if (isEmpty(people)) return {};
    const filters = [];
    if (people.betweenFilter) {
      filters.push({
        contents: people.betweenFilter.values,
        key: 'between',
        title: 'Between',
      });
    }
    people.filters.forEach(f => {
      const fieldKey = f.field.startsWith('participants.value')
        ? drop(f.field.split('.'), 2).join('.')
        : f.field;
      const title = `${
        f.model ? formatMessage(FIELD_DISPLAY_MESSAGES[f.model]) : ''
      } ${formatMessage(FIELD_DISPLAY_MESSAGES[fieldKey])}`;
      const isBooleanField = BOOLEAN_FIELDS.indexOf(fieldKey) > -1;
      let contents;
      if (isBooleanField) {
        contents = f.values.map(v => (v === 1 ? 'Yes' : 'No')).join(', ');
      } else {
        contents =
          f.values.length > 1
            ? `${f.values.length} ${formatMessage(messages.values)}`
            : f.values[0];
      }
      filters.push({ title, contents, ...f, key: `${title}.${f.field}.${f.model}` });
    });

    return {
      people: filters,
    };
  };

  const getAttachmentsFilters = () => {
    const attachments = query?.attachments || [];
    if (!attachments.length) return {};

    const data = attachments.map(attachment => {
      const [singleValue] = attachment.values;

      return {
        contents:
          attachment.values.length > 1
            ? `${attachment.values.length} ${formatMessage(messages.values)}`
            : singleValue,
        field: 'attachments',
        key: attachment.field,
        title: attachment.field,
      };
    });

    return {
      attachments: data,
    };
  };

  const getClassifierFilters = () => {
    const classifiers = query?.analytics || {};
    const filters = [];

    if (!isEmpty(classifiers.excludedClassifications)) {
      filters.push({
        contents: formatMessage(classifierQuerySectionMessages.confidenceScoreFilterContent, {
          percent: Math.round(classifiers.confidenceScore * 100),
        }),
        field: 'analytics',
        key: 'classifier',
        title: formatMessage(classifierQuerySectionMessages.excludeFilterTitle, {
          count: classifiers.excludedClassifications.length,
        }),
      });
    }

    if (!isEmpty(classifiers.excludedZones)) {
      filters.push({
        field: 'analytics',
        key: 'zoning',
        title: formatMessage(zoningQuerySectionMessages.zoningExcludeFilterTitle, {
          count: classifiers.excludedZones.length,
        }),
      });
    }

    if (isEmpty(filters)) {
      return {};
    }

    return {
      analytics: filters,
    };
  };

  const handleFilterRemove = (part, filter) => {
    let clonedQuery = cloneDeep(query);

    const funcName = includes(FIELD_VALUE_FILTER_KEYS, part) ? 'default' : part;
    clonedQuery[part] = filterPartData(clonedQuery, part, filter)[funcName]();
    clonedQuery = unsetFilterFields(clonedQuery, part, filter)[funcName]();

    updateWatchQuery({ query: clonedQuery });
  };

  const filters = getAllFilters();
  if (isEmpty(filters)) {
    return (
      <p styleName="muted-text">
        <FormattedMessage {...QUERY_PREVIEW_MESSAGES.noFiltersSelected} />
      </p>
    );
  }
  return (
    <div styleName="query-preview-container">
      <div styleName="preview-container" data-test-id="TSurv.Components.QueryPreview.Container">
        {Object.keys(filters)
          .sort()
          .map(part => (
            <div styleName="query-part" key={part}>
              <span styleName="muted-text">
                <FormattedMessage {...FILTER_SECTION_LABEL_MESSAGE[part]} />
              </span>
              {filters[part]?.map(f => (
                <div styleName="filter-container" key={f.key}>
                  <div styleName="field-value-filter">
                    <span styleName="field" title={f.title}>
                      {f.title}
                    </span>
                    <span styleName="values" title={f.contents}>
                      {f.contents}
                    </span>
                  </div>
                  {editable && (
                    <div styleName="remove-button" onClick={() => handleFilterRemove(part, f)}>
                      <span className="icon icon-cross large" />
                    </div>
                  )}
                </div>
              ))}
            </div>
          ))}
      </div>
      {editable && (
        <React.Fragment>
          <div styleName="divider" />
          <div className="m-t">
            <IconButton
              iconName="check"
              onClick={onCompletePress}
              message={MESSAGES.complete}
              disabled={!boolQueryChanged}
              className="btn-success-outline"
              testId="TSurv.Components.QueryPreview.CompleteButton"
            />
          </div>
        </React.Fragment>
      )}
    </div>
  );
};

export default injectIntl(QueryPreview);

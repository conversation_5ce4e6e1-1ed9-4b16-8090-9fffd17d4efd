import React from 'react';
import PropTypes from 'prop-types';
import { defineMessages, useIntl } from 'react-intl';

import useIncrementRefresh from 'js/hooks/useIncrementRefresh';
import ROUTES from 'constants/routes';

import ButtonWithIcon from 'view/components/button-with-icon/button-with-icon';
import AnimatedDots from 'js/view/components/animated-dots/animated-dots';
import Link from 'view/components/new-link/new-link';

import styles from './ingestion-status.less';

const messages = defineMessages({
  pending: {
    defaultMessage: 'List Upload Pending',
    id: 'components.restrictedLists.pending',
  },
  processing: {
    defaultMessage: 'List Upload Processing',
    id: 'components.restrictedLists.processing',
  },
  queued: {
    defaultMessage: 'List Upload Queued',
    id: 'components.restrictedLists.queued',
  },
  failed: {
    defaultMessage: 'List Upload Failed',
    id: 'components.restrictedLists.failed',
  },
  refresh: {
    defaultMessage: 'Refresh',
    id: 'components.restrictedLists.refresh',
  },
});

const listUploadStatusMessages = {
  PROCESSING: messages.processing,
  QUEUED: messages.queued,
  FAILED: messages.failed,
};

const IngestionStatus = ({ ingestionRecord }) => {
  const incrementRefresh = useIncrementRefresh();
  const { formatMessage } = useIntl();

  const statusText = formatMessage(
    listUploadStatusMessages[ingestionRecord?.status] || messages.pending
  );
  const auditFileId = ingestionRecord?.['&id'];
  const isUploadPending = ['QUEUED', 'PROCESSING'].includes(ingestionRecord?.status);

  return (
    <>
      {isUploadPending ? (
        <div className={styles.container}>
          <div className={styles.pendingText}>
            {auditFileId ? (
              <Link
                to={ROUTES.DATA_PROVENANCE.AUDIT_FILE.getSingleRoute(auditFileId)}
                className={styles.yellow}
                underline
              >
                {statusText}
              </Link>
            ) : (
              statusText
            )}
            <AnimatedDots />
          </div>
          <ButtonWithIcon
            onClick={incrementRefresh}
            color="red"
            iconName="cycle"
            text={formatMessage(messages.refresh)}
          />
        </div>
      ) : (
        <div className={styles.container}>
          <div className={styles.pendingText}>
            {auditFileId ? (
              <Link
                to={ROUTES.DATA_PROVENANCE.AUDIT_FILE.getSingleRoute(auditFileId)}
                className={styles.red}
                underline
              >
                {statusText}
              </Link>
            ) : (
              statusText
            )}
          </div>
        </div>
      )}
    </>
  );
};

IngestionStatus.propTypes = {
  ingestionRecord: PropTypes.shape({
    '&id': PropTypes.string,
    status: PropTypes.string.isRequired,
  }),
};

export default IngestionStatus;

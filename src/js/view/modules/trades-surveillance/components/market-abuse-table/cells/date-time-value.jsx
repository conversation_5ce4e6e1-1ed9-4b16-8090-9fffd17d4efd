import React from 'react';
import dayjs from 'util/dayjs';
import PropTypes from 'prop-types';
import { EMPTY_DISPLAY_VALUE } from 'constants/property';

/**
 *  Displays the date and time in 2 lines, if the input is invalid then display the fallback value
 *
 * @param {*} props
 * {
 *  value : any valid date or timestamp
 *  fallbackValue : fallback string to display. Default value : "-"
 * }
 */
const DateTimeValue = ({
  value,
  fallbackValue,
  showMs,
  showTimezone,
  timezone,
  utc,
  showTime,
  format,
  showDate,
  isTimezone,
}) => {
  let dayjsTime;

  if (utc) {
    dayjsTime = dayjs.utc(value, format);
  } else if (isTimezone && timezone) {
    dayjsTime = dayjs.utc(value, format).tz(timezone);
  } else {
    dayjsTime = dayjs.utc(value, format).local();
  }

  if (!value || !dayjsTime.isValid()) {
    return fallbackValue;
  }

  const formattedDate = dayjsTime.format('DD-MMM-YYYY');
  const formattedTime = showMs ? dayjsTime.format('HH:mm:ss:SSS') : dayjsTime.format('HH:mm:ss');
  const title = `${showDate ? formattedDate : ''} ${showTime ? formattedTime : ''}`;
  return (
    <div title={title} style={{ textAlign: `${timezone ? 'left' : 'center'}` }}>
      {showTimezone && <div>{timezone}</div>}
      {showDate && <div>{formattedDate}</div>}
      {showTime && <div>{formattedTime}</div>}
    </div>
  );
};

DateTimeValue.propTypes = {
  fallbackValue: PropTypes.string,
  format: PropTypes.string,
  isTimezone: PropTypes.bool,
  showDate: PropTypes.bool,
  showMs: PropTypes.bool,
  showTime: PropTypes.bool,
  showTimezone: PropTypes.bool,
  timezone: PropTypes.string,
  utc: PropTypes.bool,
  value: PropTypes.any,
};

DateTimeValue.defaultProps = {
  fallbackValue: EMPTY_DISPLAY_VALUE,
  showDate: true,
  showTime: true,
};

export default DateTimeValue;

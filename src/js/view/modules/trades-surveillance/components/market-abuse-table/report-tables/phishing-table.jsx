import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useIntl } from 'react-intl';
import cx from 'classnames';

import { getFormattedTime } from 'js/util/time';

import { safeFetchRecordKeys } from 'util/surveillance';
import AlertStatus from 'view/modules/trades-surveillance/components/market-abuse-table/cells/alert-status';
import CheckboxCell from 'view/components/grid-table/checkbox-cell/checkbox-cell';
import CollapseExpandButton from 'view/components/button/collapse-expand';
import HeaderCheckboxCell from 'view/components/grid-table/header-checkbox-cell/header-checkbox-cell';
import HoverGroup from 'view/components/grid-table/hover-group/hover-group';
import LinkCell from 'view/components/grid-table/link-cell/link-cell';
import NoResultsRow from 'view/components/grid-table/no-results-row/no-results-row';
import SortingHeaderCell from 'view/components/grid-table/sorting-header-cell/sorting-header-cell';
import NumericValue from 'view/modules/trades-surveillance/components/market-abuse-table/cells/numeric-value';
import DateTimeValue from 'view/modules/trades-surveillance/components/market-abuse-table/cells/date-time-value';
import InstrumentIdLink from 'view/modules/trades-surveillance/components/market-abuse-table/cells/instrument-id-link';
import ResolutionCategory from 'js/view/components/resolution-category/resolution-category';
import ResolutionSubcategories from 'js/view/components/resolution-subcategories/resolution-subcategories';
import HeaderCell from 'view/components/grid-table/header-cell/header-cell';
import ExecutionsDetailTable from 'view/modules/trades-surveillance/components/market-abuse-table/report-tables/orders/executions-detail-table';

import ROUTES, { getWorkflowCaseSlug } from 'constants/routes';
import { PROPERTY_MESSAGES as messages } from 'constants/property';

import { useGlobalTheme } from 'js/hooks/useGlobalTheme';

import styles from './report-table.less';

const Row = ({
  data,
  disabled,
  isExpanded,
  hasStatusColumns,
  hasWorkflowColumns,
  loading,
  location,
  isClassicTheme,
  selected,
  showResolutionColumn,
  toggleExpand,
  toggleSelection,
}) => {
  const {
    instrumentFullName,
    instrumentId,
    instrumentIdCode,
    date,
    bestExAssetClassMain,
    totalBuyVolume,
    totalSellVolume,
    buySellRatio,
    sellBuyRatio,
  } = data?.additionalFields?.topLevel || {};

  function handleExpand() {
    toggleExpand(isExpanded ? '' : data['&id']);
  }

  const { status, assigneeName } = data?.workflow || {};
  const caseId = getWorkflowCaseSlug(data?.workflow);

  const singleMarScenarioLink = data.scenarioId
    ? ROUTES.MARKET_ABUSE.SCENARIOS.getScenarioRoute(data.scenarioId)
    : undefined;

  const currentHoverColorStyle = isClassicTheme ? 'gray' : 'green';

  const resolutionSubCategoryCustom = data.workflow?.resolutionSubCategoryCustom
    ? [data.workflow.resolutionSubCategoryCustom]
    : [];
  const resolutionSubCategories = data.workflow?.resolutionSubCategories || [];
  const resolutionDetails = [...resolutionSubCategories, ...resolutionSubCategoryCustom];

  return (
    <HoverGroup styleName="row" color={currentHoverColorStyle} selected={selected}>
      {!caseId ? (
        <CheckboxCell
          item={data}
          checked={selected}
          onChange={toggleSelection}
          disabled={disabled}
        />
      ) : (
        <LinkCell
          to={{
            pathname: ROUTES.CASE_MANAGER.VIEW.getViewRoute(caseId),
            state: { from: location },
          }}
        >
          {caseId}
        </LinkCell>
      )}
      {hasWorkflowColumns && (
        <>
          <LinkCell to={singleMarScenarioLink}>{assigneeName}</LinkCell>
          <LinkCell to={singleMarScenarioLink}>{data.slug}</LinkCell>
        </>
      )}
      {hasStatusColumns && (
        <LinkCell to={singleMarScenarioLink}>
          <AlertStatus status={status} />
        </LinkCell>
      )}
      {showResolutionColumn && (
        <>
          <LinkCell to={singleMarScenarioLink}>{data.workflow?.resolvedByName}</LinkCell>
          <LinkCell to={singleMarScenarioLink}>{getFormattedTime(data.resolved)}</LinkCell>
          <LinkCell to={singleMarScenarioLink}>
            <ResolutionCategory workflow={data.workflow} />
          </LinkCell>
          <LinkCell to={singleMarScenarioLink}>
            <ResolutionSubcategories subcategories={resolutionDetails} />
          </LinkCell>
        </>
      )}
      <LinkCell to={singleMarScenarioLink}>
        <DateTimeValue value={date} />
      </LinkCell>
      <LinkCell to={singleMarScenarioLink}>{bestExAssetClassMain}</LinkCell>
      <div styleName="cell">
        <InstrumentIdLink
          instrumentId={instrumentId || instrumentIdCode}
          instrumentName={instrumentFullName}
        />
      </div>
      <LinkCell to={singleMarScenarioLink}>{instrumentFullName}</LinkCell>
      <LinkCell to={singleMarScenarioLink}>
        <NumericValue value={totalBuyVolume} />
      </LinkCell>
      <LinkCell to={singleMarScenarioLink}>
        <NumericValue value={totalSellVolume} />
      </LinkCell>
      <LinkCell to={singleMarScenarioLink}>
        <NumericValue value={buySellRatio} />
      </LinkCell>
      <LinkCell to={singleMarScenarioLink}>
        <NumericValue value={sellBuyRatio} />
      </LinkCell>
      <div className={styles.cell}>
        <CollapseExpandButton
          onClick={handleExpand}
          expanded={isExpanded}
          loading={isExpanded && loading}
        />
      </div>
    </HoverGroup>
  );
};

Row.propTypes = {
  data: PropTypes.object.isRequired,
  disabled: PropTypes.bool.isRequired,
  hasStatusColumns: PropTypes.bool.isRequired,
  hasWorkflowColumns: PropTypes.bool.isRequired,
  isClassicTheme: PropTypes.bool,
  isExpanded: PropTypes.bool.isRequired,
  loading: PropTypes.bool.isRequired,
  location: PropTypes.object.isRequired,
  selected: PropTypes.bool.isRequired,
  showResolutionColumn: PropTypes.bool,
  toggleExpand: PropTypes.func.isRequired,
  toggleSelection: PropTypes.func.isRequired,
};

const PhishingReportTable = ({
  allSelected,
  currentSort,
  data,
  expandedId,
  initializeSort,
  hasStatusColumns,
  hasWorkflowColumns,
  loading,
  selectedRows,
  setCurrentSort,
  setExpandedId,
  showResolutionColumn,
  toggleSelectAll,
  toggleSelection,
}) => {
  const { isClassicTheme } = useGlobalTheme();
  const handleSort = (fieldName, order) => {
    setCurrentSort({ fieldName, order });
  };

  // On component mount
  useEffect(() => {
    initializeSort(currentSort);
    // this needs to run only once on load.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { formatMessage } = useIntl();

  const commonProps = { currentSort, onClick: handleSort };

  let fieldsCount = 8;
  if (hasStatusColumns) fieldsCount += 1;
  if (hasWorkflowColumns) fieldsCount += 2;
  if (showResolutionColumn) fieldsCount += 4;

  const currentColorStyle = isClassicTheme ? 'gray' : 'black';

  return (
    <div
      className={cx({
        [styles.table]: isClassicTheme,
        [styles.newTable]: !isClassicTheme,
        [styles.tableLoading]: !!loading,
      })}
      style={{
        gridTemplateColumns: `100px repeat(${fieldsCount}, auto) 120px`,
      }}
    >
      <HeaderCheckboxCell
        black={!isClassicTheme}
        checked={allSelected}
        onChange={toggleSelectAll}
      />
      {hasWorkflowColumns && (
        <>
          <SortingHeaderCell
            color={currentColorStyle}
            fieldName="workflow.assigneeName"
            {...commonProps}
          >
            {formatMessage(messages.assignee)}
          </SortingHeaderCell>
          <SortingHeaderCell color={currentColorStyle} fieldName="slug" {...commonProps}>
            {formatMessage(messages.alertId)}
          </SortingHeaderCell>
        </>
      )}
      {hasStatusColumns && (
        <SortingHeaderCell color={currentColorStyle} fieldName="workflow.status" {...commonProps}>
          {formatMessage(messages.status)}
        </SortingHeaderCell>
      )}
      {showResolutionColumn && (
        <>
          <SortingHeaderCell
            color={currentColorStyle}
            fieldName="workflow.resolvedByName"
            {...commonProps}
          >
            {formatMessage(messages.resolvedBy)}
          </SortingHeaderCell>
          <SortingHeaderCell color={currentColorStyle} fieldName="resolved" {...commonProps}>
            {formatMessage(messages.resolvedDate)}
          </SortingHeaderCell>
          <SortingHeaderCell
            color={currentColorStyle}
            fieldName="workflow.resolutionCategory"
            {...commonProps}
          >
            {formatMessage(messages.resolutionCategory)}
          </SortingHeaderCell>
          <HeaderCell color={currentColorStyle}>
            {formatMessage(messages.resolutionSubcategories)}
          </HeaderCell>
        </>
      )}
      <SortingHeaderCell
        color={currentColorStyle}
        fieldName="additionalFields.topLevel.date"
        {...commonProps}
      >
        {formatMessage(messages.date)}
      </SortingHeaderCell>
      <SortingHeaderCell
        color={currentColorStyle}
        fieldName="additionalFields.topLevel.bestExAssetClassMain"
        {...commonProps}
      >
        {formatMessage(messages.assetClass)}
      </SortingHeaderCell>
      <SortingHeaderCell
        color={currentColorStyle}
        fieldName="additionalFields.topLevel.instrumentId"
        {...commonProps}
      >
        {formatMessage(messages.instrumentIdCode)}
      </SortingHeaderCell>
      <SortingHeaderCell
        color={currentColorStyle}
        fieldName="additionalFields.topLevel.instrumentFullName"
        {...commonProps}
      >
        {formatMessage(messages.instrument)}
      </SortingHeaderCell>
      <SortingHeaderCell
        color={currentColorStyle}
        fieldName="additionalFields.topLevel.totalBuyVolume"
        {...commonProps}
      >
        {formatMessage(messages.totalBuyVolume)}
      </SortingHeaderCell>
      <SortingHeaderCell
        color={currentColorStyle}
        fieldName="additionalFields.topLevel.totalSellVolume"
        {...commonProps}
      >
        {formatMessage(messages.totalSellVolume)}
      </SortingHeaderCell>
      <SortingHeaderCell
        color={currentColorStyle}
        fieldName="additionalFields.topLevel.buySellRatio"
        {...commonProps}
      >
        {formatMessage(messages.buySellRatio)}
      </SortingHeaderCell>
      <SortingHeaderCell
        color={currentColorStyle}
        fieldName="additionalFields.topLevel.sellBuyRatio"
        {...commonProps}
      >
        {formatMessage(messages.sellBuyRatio)}
      </SortingHeaderCell>
      <div className={styles.headerCell} />

      {data.length > 0 &&
        data.map(row => {
          const id = row['&id'];
          const isExpanded = expandedId === id;
          return (
            <>
              <Row
                selected={!!selectedRows[id]}
                toggleSelection={() => toggleSelection(id)}
                toggleExpand={setExpandedId}
                isExpanded={isExpanded}
                key={id}
                data={row}
                loading={loading}
                hasStatusColumns={hasStatusColumns}
                hasWorkflowColumns={hasWorkflowColumns}
                showResolutionColumn={showResolutionColumn}
                isClassicTheme={isClassicTheme}
              />
              {/* TODO use the records sent directly instead of fetching again using keys */}
              {isExpanded && (
                <div className={styles.expandedRow}>
                  <ExecutionsDetailTable
                    orderIds={safeFetchRecordKeys(row.records?.executions)}
                    type="executions"
                    id={expandedId}
                    listKey="executions"
                  />
                </div>
              )}
            </>
          );
        })}
      {data.length === 0 && !loading && <NoResultsRow />}
    </div>
  );
};

PhishingReportTable.propTypes = {
  allSelected: PropTypes.bool.isRequired,
  currentSort: PropTypes.object,
  data: PropTypes.arrayOf(PropTypes.shape({ '&id': PropTypes.string.isRequired })).isRequired,
  expandedId: PropTypes.string,
  hasStatusColumns: PropTypes.bool,
  hasWorkflowColumns: PropTypes.bool,
  initializeSort: PropTypes.func.isRequired,
  loading: PropTypes.bool.isRequired,
  selectedRows: PropTypes.object,
  setCurrentSort: PropTypes.func.isRequired,
  setExpandedId: PropTypes.func.isRequired,
  showResolutionColumn: PropTypes.bool,
  toggleSelectAll: PropTypes.func.isRequired,
  toggleSelection: PropTypes.func.isRequired,
};

PhishingReportTable.defaultProps = {
  currentSort: {
    fieldName: 'additionalFields.topLevel.date',
    order: 'descending',
  },
  hasStatusColumns: false,
  hasWorkflowColumns: false,
};

export default PhishingReportTable;

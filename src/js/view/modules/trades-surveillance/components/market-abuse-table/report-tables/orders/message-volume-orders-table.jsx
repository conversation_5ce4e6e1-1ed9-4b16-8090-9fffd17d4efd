import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { useIntl } from 'react-intl';
import isEmpty from 'lodash/isEmpty';

import { getLinkPath } from 'util/order';
import { getTraderName } from 'util/surveillance';

import { ENTITY_COUNT_MESSAGES } from 'constants/entity';
import { PROPERTY_MESSAGES as messages } from 'constants/property';
import { TITLE_MESSAGES } from 'constants/market-abuse';

import HoverGroup from 'view/components/grid-table/hover-group/hover-group';
import LinkCell from 'view/components/grid-table/link-cell/link-cell';
import NoResultsRow from 'view/components/grid-table/no-results-row/no-results-row';
import Pagination from 'view/components/pagination';
import SortingHeaderCell from 'view/components/grid-table/sorting-header-cell/sorting-header-cell';
import TableContainer from 'view/components/table/table-container';

import BuySellIndicator from 'view/components/property/buy-sell-indicator';
import NumericValue from 'view/modules/trades-surveillance/components/market-abuse-table/cells/numeric-value';
import DateTimeValue from 'view/modules/trades-surveillance/components/market-abuse-table/cells/date-time-value';
import { fetchChildList, setOrdersListsUIState } from 'actions/mar/orders-lists.actions';

import styles from './orders-table.less';

const DEFAULT_PAGINATION_SIZE = 10;

const Row = ({ data }) => {
  const ordersLink = getLinkPath(data);
  return (
    <HoverGroup>
      <LinkCell to={ordersLink}>{data?.orderIdentifiers?.orderIdCode}</LinkCell>
      <LinkCell to={ordersLink}>
        <DateTimeValue value={data?.timestamps?.orderSubmitted} />
      </LinkCell>
      <LinkCell to={ordersLink}>{data?.instrumentDetails?.instrument?.instrumentIdCode}</LinkCell>
      <LinkCell to={ordersLink}>{data?.instrumentDetails?.instrument?.instrumentFullName}</LinkCell>
      <LinkCell to={ordersLink}>
        <BuySellIndicator entity={data} forTable />
      </LinkCell>
      <LinkCell to={ordersLink}>{data?.executionDetails?.orderType}</LinkCell>
      <LinkCell to={ordersLink}>
        <NumericValue value={data?.priceFormingData?.initialQuantity} />
      </LinkCell>
      <LinkCell to={ordersLink}>
        <NumericValue value={data?.remainingQuantity} />
      </LinkCell>
      <LinkCell to={ordersLink}>
        <NumericValue value={data?.tradedQuantity} />
      </LinkCell>
      <LinkCell to={ordersLink}>
        <NumericValue value={data?.avgPrice} />
      </LinkCell>
      <LinkCell to={ordersLink}>{data?.transactionDetails?.priceCurrency}</LinkCell>
      <LinkCell to={ordersLink}>{data?.counterparty?.name}</LinkCell>
      <LinkCell to={ordersLink}>{getTraderName(data)}</LinkCell>
    </HoverGroup>
  );
};

Row.propTypes = {
  data: PropTypes.object.isRequired,
};

const OrdersStateTable = ({
  data,
  fetchListing,
  id,
  listKey,
  loading,
  onSetUIState,
  orderStateKeys,
  pagination,
  sort,
  totalHits,
}) => {
  const handleSort = (fieldName, order) => {
    onSetUIState(id, listKey, { sort: { fieldName, order } });
  };

  const handlePagination = change => {
    if (change.take && change.take !== pagination.take) {
      // Reset to first page if the page size changes
      onSetUIState(id, listKey, { pagination: { skip: 0, take: change.take } });
    } else {
      onSetUIState(id, listKey, { pagination: { ...pagination, ...change } });
    }
  };

  useEffect(() => {
    // Check if orders IDs are available to fetch the records
    if (!isEmpty(orderStateKeys)) {
      fetchListing({ id, listKey, orderIds: orderStateKeys, pagination, sort });
    }
  }, [sort, listKey, id, fetchListing, pagination, orderStateKeys]);

  const { formatMessage } = useIntl();
  const currency = data?.[0]?.transactionDetails?.priceCurrency || 'EUR';
  const sortProps = { currentSort: sort, onClick: handleSort };
  return (
    <TableContainer
      countMessage={ENTITY_COUNT_MESSAGES.order}
      loading={loading}
      open
      title={TITLE_MESSAGES.orders}
      total={totalHits}
      className={styles.container}
    >
      <section>
        <div
          className={`${styles.table} ${loading ? styles.tableLoading : ''}`}
          style={{ gridTemplateColumns: 'repeat(13, auto)' }}
        >
          <SortingHeaderCell fieldName="orderIdentifiers.orderIdCode" {...sortProps}>
            {formatMessage(messages.orderIdCode)}
          </SortingHeaderCell>
          <SortingHeaderCell fieldName="timestamps.orderSubmitted" {...sortProps}>
            {formatMessage(messages.orderSubmittedShort)}
          </SortingHeaderCell>
          <SortingHeaderCell
            fieldName="instrumentDetails.instrument.instrumentIdCode"
            {...sortProps}
          >
            {formatMessage(messages.isin)}
          </SortingHeaderCell>
          <SortingHeaderCell
            fieldName="instrumentDetails.instrument.instrumentFullName"
            {...sortProps}
          >
            {formatMessage(messages.instrumentFullName)}
          </SortingHeaderCell>
          <SortingHeaderCell fieldName="executionDetails.buySellIndicator" {...sortProps}>
            {formatMessage(messages.buySell)}
          </SortingHeaderCell>
          <SortingHeaderCell fieldName="executionDetails.orderType" {...sortProps}>
            {formatMessage(messages.orderType)}
          </SortingHeaderCell>
          <SortingHeaderCell fieldName="priceFormingData.initialQuantity" {...sortProps}>
            {formatMessage(messages.orderQuantity)}
          </SortingHeaderCell>
          <div className={styles.headerCell}>{formatMessage(messages.remainingQuantity)}</div>
          <div className={styles.headerCell}>{formatMessage(messages.tradedQuantity)}</div>
          <div className={styles.headerCell}>{formatMessage(messages.averagePrice)}</div>
          <SortingHeaderCell fieldName="transactionDetails.priceCurrency" {...sortProps}>
            {formatMessage(messages.currency)}
          </SortingHeaderCell>
          <SortingHeaderCell fieldName="counterparty.name" {...sortProps}>
            {formatMessage(messages.counterparty)}
          </SortingHeaderCell>
          <div className={styles.headerCell}>{formatMessage(messages.trader)}</div>
          {totalHits > 0
            ? data.map(row => <Row key={row['&id']} data={row} currency={currency} />)
            : !loading && <NoResultsRow />}
        </div>
        <div styleName={totalHits >= DEFAULT_PAGINATION_SIZE ? 'paginationContainer' : ''}>
          <Pagination
            {...pagination}
            onPageSizeAndPaginationChange={handlePagination}
            showPageSizeSelector
            showPagination
            total={totalHits}
            currency={currency}
          />
        </div>
      </section>
    </TableContainer>
  );
};

OrdersStateTable.propTypes = {
  data: PropTypes.array,
  errors: PropTypes.object,
  executionsDetails: PropTypes.object.isRequired,
  fetchListing: PropTypes.func.isRequired,
  id: PropTypes.string.isRequired,
  listKey: PropTypes.string.isRequired,
  loading: PropTypes.bool,
  newsTimestamp: PropTypes.string.isRequired,
  onSetUIState: PropTypes.func.isRequired,
  orderStateKeys: PropTypes.array,
  pagination: PropTypes.object,
  sort: PropTypes.object,
  totalHits: PropTypes.number,
};

OrdersStateTable.defaultProps = {
  data: [],
  errors: undefined,
  loading: false,
  orderStateKeys: [],
  pagination: { skip: 0, take: DEFAULT_PAGINATION_SIZE },
  sort: {
    fieldName: 'timestamps.orderSubmitted',
    order: 'descending',
  },
};

const mapStateToProps = (state, { id, listKey }) => {
  const { data, header, errors, loading, uiState } = state.mar?.ordersLists?.[id]?.[listKey] || {};
  return {
    data,
    errors,
    loading,
    pagination: uiState?.pagination,
    sort: uiState?.sort,
    totalHits: header?.totalHits || 0,
  };
};

const mapDispatchToProps = {
  fetchListing: fetchChildList,
  onSetUIState: setOrdersListsUIState,
};

export default connect(mapStateToProps, mapDispatchToProps)(OrdersStateTable);

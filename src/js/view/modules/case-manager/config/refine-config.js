const refineTwoConfig = {
  preFilters: [
    {
      condition: 'is',
      id: 'eventDetails.module',
      type: 'string',
      values: ['Case Manager'],
    },
  ],
  defaultProperties: [
    {
      fieldId: 'user',
      messageKeys: ['user'],
      type: 'string',
    },
    {
      fieldId: 'recordId',
      messageKeys: ['recordId'],
      type: 'string',
    },
    {
      fieldId: 'eventDetails.category',
      messageKeys: ['category'],
      type: 'string',
    },
    {
      fieldId: 'eventDetails.event',
      messageKeys: ['event'],
      type: 'string',
    },
  ],
  moreProperties: [
    {
      fieldId: 'timestamp',
      messageKeys: ['timestamp'],
      type: 'datetime',
    },
  ],
  additionalProperties: [
    {
      fieldId: 'action',
      messageKeys: ['action'],
      type: 'string',
    },
    {
      fieldId: 'eventDetails.module',
      messageKeys: ['module'],
      type: 'string',
    },
    {
      fieldId: 'realm',
      messageKeys: ['realm'],
      type: 'string',
    },
    {
      fieldId: 'recordDetails.model',
      messageKeys: ['model'],
      type: 'string',
    },
    {
      fieldId: 'searchText',
      messageKeys: ['searchTerm'],
      type: 'string',
    },
    {
      fieldId: 'statusCode',
      messageKeys: ['statusCode'],
      type: 'number',
    },
    {
      fieldId: 'uri',
      messageKeys: ['page'],
      type: 'string',
      noSearchBar: true,
    },
  ],
  prominentFilters: [
    {
      fieldId: 'user',
      messageKeys: ['user'],
      type: 'string',
    },
    {
      fieldId: 'eventDetails.category',
      messageKeys: ['category'],
      type: 'string',
    },
    {
      fieldId: 'eventDetails.module',
      messageKeys: ['module'],
      type: 'string',
    },
    {
      fieldId: 'searchText', // TODO: Not sure!
      messageKeys: ['searchText'],
      type: 'string',
    },
    {
      fieldId: 'eventDetails.description',
      messageKeys: ['description'],
      type: 'string',
    },
  ],
};

const userAuditRefineConfig = {
  defaultProperties: ['user', 'eventDetails.category', 'eventDetails.event'],
  moreProperties: ['timestamp'],
  refineTwoConfig,
};

export default userAuditRefineConfig;

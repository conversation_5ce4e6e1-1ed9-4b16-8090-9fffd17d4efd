import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Routes, Navigate, Route } from 'react-router';

import startsWith from 'lodash/startsWith';

import NotFound from 'view/modules/error/pages/not-found';

import DashboardNew from 'view/modules/case-manager/pages/new/dashboard/dashboard-new';

import CaseDetailNew from 'view/modules/case-manager/pages/new/case-detail/case-detail-new';
import ExitCaseModeRoute from 'view/components/case-mode/exit-case-mode-route';
import Insights from 'view/modules/insights';
import Reports from 'view/modules/insights/reports';
import CaseFormNew from 'view/modules/case-manager/components/case-form/case-form-new';
import Retention from 'view/modules/case-manager/pages/new/retention/retention';
import Timeline from 'view/modules/case-manager/pages/new/timeline/timeline';
import EditCase from 'view/modules/case-manager/pages/new/edit-case/edit-case';
import CasesAudit from 'view/modules/case-manager/pages/new/audit/audit';
import NewCasesAudit from 'view/modules/case-manager/pages/new/new-case-audit/case-audit';

import { withRouter } from 'util/withRouter';
import ROUTES from 'constants/routes';
import { RootState } from 'js/redux-toolkit/store';
import * as caseActions from 'actions/case';
import * as routingUtil from 'util/routing';
import * as accountUserActions from 'js/actions/auth/account-user.actions';
import * as userSelectors from 'js/redux/selectors/user';
import { CASE_MODULE } from 'constants/insights';
import { CASE_MANAGER } from 'constants/roles-permissions';
import useUserPermissions from 'js/hooks/useUserPermissions';
import useRolesAndPermissions from 'view/modules/admin/hooks/useRolesAndPermissions';

type RouteProps = {
  urlOfLastCaseVisited?: string;
  inCaseMode: boolean;
  userId: string;
  getAccountUser: any;
  location: any;
  caseVisited: any;
  navigate: any;
};

const CaseManagerRoutes = ({
  urlOfLastCaseVisited,
  inCaseMode,
  userId,
  getAccountUser,
  location,
  navigate,
  caseVisited,
}: RouteProps) => {
  const permissions = useUserPermissions(CASE_MANAGER);
  const { allowAddToCaseRoute } = useRolesAndPermissions();

  useEffect(() => {
    getAccountUser(userId);

    if (!urlOfLastCaseVisited) {
      return;
    }

    if (location.pathname === ROUTES.CASE_MANAGER) {
      navigate(urlOfLastCaseVisited);
    }
  }, [getAccountUser, urlOfLastCaseVisited, location.pathname, navigate, userId]);

  useEffect(() => {
    return () => {
      const url = routingUtil.createFullPathFromLocation(location);

      // Only update the case url if it is not for the create/update page:
      if (
        !startsWith(url, ROUTES.CASE_MANAGER.ADD) &&
        !startsWith(url, ROUTES.CASE_MANAGER.EDIT_CASE.INDEX)
      ) {
        caseVisited({ url });
      }
    };
  }, [caseVisited, location]);

  return (
    <Routes>
      <Route path={ROUTES.CASE_MANAGER.relDashboard} element={<DashboardNew />} />

      {!inCaseMode && allowAddToCaseRoute && (
        <Route path={ROUTES.CASE_MANAGER.relAdd} element={<CaseFormNew />} />
      )}

      {!inCaseMode && permissions?.canCreateInsights && (
        <Route
          path={ROUTES.CASE_MANAGER.INSIGHTS.relIndex}
          element={<Insights moduleName={CASE_MODULE} />}
        />
      )}
      {!inCaseMode && permissions?.canCreateInsights && (
        <Route
          path={ROUTES.CASE_MANAGER.INSIGHTS.relSingle}
          element={<Insights moduleName={CASE_MODULE} />}
        />
      )}
      {!inCaseMode && (
        <Route
          path={`${ROUTES.CASE_MANAGER.relReports}/*`}
          element={<Reports moduleName={CASE_MODULE} />}
        />
      )}
      <Route path={ROUTES.CASE_MANAGER.VIEW.relIndex} element={<CaseDetailNew />} />
      {permissions?.canEditCase && (
        <Route path={ROUTES.CASE_MANAGER.EDIT_CASE.relSingle} element={<EditCase />} />
      )}
      <Route path={ROUTES.CASE_MANAGER.RETENTION.relIndex} element={<Retention />} />
      <Route path={ROUTES.CASE_MANAGER.TIMELINE.relIndex} element={<Timeline />} />
      {permissions?.canViewAudit && (
        <>
          <Route path={ROUTES.CASE_MANAGER.CASE_AUDIT.relIndex} element={<NewCasesAudit />} />
          <Route path={ROUTES.CASE_MANAGER.relAudit} element={<CasesAudit />} />
        </>
      )}
      <Route
        path={ROUTES.CASE_MANAGER.relIndex}
        element={<Navigate to={ROUTES.CASE_MANAGER.relDashboard} replace />}
      />
      {inCaseMode && <Route element={<ExitCaseModeRoute />} />}
      {!inCaseMode && <Route path="*" element={<NotFound />} />}
    </Routes>
  );
};

export default connect(
  (state: RootState) => ({
    inCaseMode: state.activeCase.inCaseMode,
    urlOfLastCaseVisited: state.activeCase.urlOfLastCaseVisited,
    userId: userSelectors.userIdSelector(state),
  }),
  dispatch => ({
    caseVisited: bindActionCreators(caseActions.caseVisited, dispatch),
    getAccountUser: bindActionCreators(accountUserActions.getAccountUser, dispatch),
  })
)(withRouter(CaseManagerRoutes));

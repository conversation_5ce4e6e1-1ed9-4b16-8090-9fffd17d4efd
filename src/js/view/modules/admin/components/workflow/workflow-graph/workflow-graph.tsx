/* eslint-disable react-hooks/exhaustive-deps */
import React, { useCallback, useEffect, useMemo, useRef, useState, type FC } from 'react';
import { useIntl, defineMessages } from 'react-intl';
import cx from 'classnames';
import ReactFlow, {
  ReactFlowProvider,
  type EdgeTypes,
  type NodeTypes,
  useReactFlow,
  Edge,
} from 'reactflow';

import { findLatestEdit } from 'js/util/time';

import useComponentSize from 'js/hooks/useComponentSize';

import Spinner from 'js/view/components/progress/spinner';
import TransitionNode, { TransitionStateType } from './nodes/transition-node';
import TransitionEdge, { Transition, CustomEdgeDataType } from './edges/transition-edge';
import GradientEdges from './edges/gradient-edges';

import initialEdges from './edges/inital-edges';
import initialNodes from './nodes/inital-nodes';

import styles from './workflow-graph.less';
import 'reactflow/dist/style.css';

const nodeTypes: NodeTypes = {
  transitionNode: TransitionNode,
};

const edgeTypes: EdgeTypes = {
  customEdge: TransitionEdge,
};

type FlowChartProps = {
  transitionRules: Transition[];
  isEdit?: boolean;
  showLastEdited?: boolean;
};

const getTransitionRule = (
  fromTransition: TransitionStateType,
  toTransition: TransitionStateType,
  transitionRules: Transition[]
) => {
  const transitionRule = transitionRules?.find(
    rule => rule.fromStatus === fromTransition && rule.toStatus === toTransition
  );
  return transitionRule;
};

const MESSAGES = defineMessages({
  lastEdited: {
    defaultMessage: 'Last Edited: {time}',
    id: 'module.admin.workflow.workflowGraph.lastEdited',
  },
});

const FlowChart: FC<FlowChartProps> = ({
  transitionRules,
  isEdit = false,
  showLastEdited = false,
}) => {
  const { formatMessage } = useIntl();
  const containerRef = useRef(null);
  const { setViewport } = useReactFlow();
  const { height, width } = useComponentSize(containerRef);
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);

  useEffect(() => {
    // Approximate dimensions of the graph's layout
    const graphWidth = 1110; // Set this based on your specific graph's width
    const graphHeight = 350; // Set this based on your specific graph's height

    // Calculate zoom to fit the graph within the current viewport
    const zoomX = width / graphWidth;
    const zoomY = height / graphHeight;
    const zoom = Math.min(zoomX, zoomY, 2); // Ensure the graph does not scale above 100% size

    // Center the graph within the viewport
    const x = (width - graphWidth * zoom) / 2;
    const y = (height - graphHeight * zoom) / 2;

    setViewport({
      x,
      y,
      zoom,
    });
  }, [width, height, setViewport]);

  const getDynamicStylesForEdge = useCallback(
    (id: string) => {
      const exists = id.split('<->').includes(selectedNodeId);
      return exists
        ? {}
        : {
            opacity: 0.05,
            pointerEvents: 'none',
          };
    },
    [selectedNodeId]
  );

  const edges = useMemo(
    () =>
      initialEdges.map(edge => ({
        ...edge,
        style: {
          ...edge.style,
          ...(selectedNodeId ? { ...getDynamicStylesForEdge(edge.id) } : {}),
        },
        data: {
          ...edge.data,
          transitions: edge.data.transitions?.map(transition => ({
            ...transition,
            ...getTransitionRule(transition.fromStatus, transition.toStatus, transitionRules),
          })),
          isEdit,
        },
      })) as Edge<CustomEdgeDataType>[],
    [getDynamicStylesForEdge, isEdit, selectedNodeId, JSON.stringify(transitionRules)]
  );

  const nodes = useMemo(
    () =>
      initialNodes.map(node => ({
        ...node,
        data: {
          ...node.data,
          onClick: (id: string) => {
            setSelectedNodeId(id === selectedNodeId ? null : id);
          },
          selected: selectedNodeId === node.data.nodeType,
        },
      })),
    [selectedNodeId]
  );

  const lastEditedDateTime = useMemo<string>(() => {
    const updatedDateTimeArray =
      transitionRules?.map(rule => rule.updatedDateTime).filter(Boolean) || [];
    if (!updatedDateTimeArray.length) return null;
    return findLatestEdit(updatedDateTimeArray);
  }, [transitionRules]);

  return (
    <div style={{ height: '100%' }} ref={containerRef}>
      <GradientEdges />
      <ReactFlow
        nodes={nodes}
        edges={edges}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        proOptions={{ hideAttribution: true }}
        nodesDraggable={false}
        nodesConnectable={false}
        elementsSelectable={false}
        panOnDrag={false}
        zoomOnScroll={false}
        zoomOnPinch={false}
        zoomOnDoubleClick={false}
        draggable={false}
        className="nowheel cursor-default"
      >
        <div className={styles.lastEdited}>
          <span>
            {showLastEdited && lastEditedDateTime
              ? formatMessage(MESSAGES.lastEdited, {
                  user: 'Devesh',
                  time: lastEditedDateTime,
                })
              : ''}
          </span>
        </div>
      </ReactFlow>
    </div>
  );
};

type WorkflowGraphProps = {
  loading?: boolean;
  transitionRules?: Transition[];
  isEdit?: boolean;
  highlightOnEdit?: boolean;
  showLastEdited?: boolean;
};

const WorkflowGraph: FC<WorkflowGraphProps> = ({
  loading = false,
  transitionRules,
  isEdit = false,
  highlightOnEdit = false,
  showLastEdited = false,
}) => {
  return (
    <div
      className={cx(styles.container, {
        [styles.editMode]: highlightOnEdit && isEdit,
        [styles.centered]: loading,
      })}
    >
      <ReactFlowProvider>
        {loading ? (
          <Spinner size="large" />
        ) : (
          <FlowChart
            transitionRules={transitionRules}
            isEdit={isEdit}
            showLastEdited={showLastEdited}
          />
        )}
      </ReactFlowProvider>
    </div>
  );
};

export default WorkflowGraph;

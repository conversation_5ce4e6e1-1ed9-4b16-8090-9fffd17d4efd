@import 'less/site.less';

.detailBox {
  width: 100%;

  & > div {
    box-shadow: 0 0 5px 5px @black;
    margin: 0;
    padding: 0;

    & > div {
      background-color: @charcoal-blue;
    }
  }
}

.userAccessCount {
  gap: 8px;
  font-size: 13px;
  margin-top: 5px;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 5px;
  flex-wrap: wrap;
}

.appliedCount,
.notAppliedCount {
  display: flex;
  align-items: center;
}

.appliedCount {
  color: @green;
}

.notAppliedCount {
  color: @gray;
}

.greenIndicator {
  width: 10px;
  height: 10px;
  border-radius: 100px;
  margin-right: 5px;
  background-color: @green;
  box-shadow: 0 0 5px 0 @green;
}

.greyIndicator {
  width: 10px;
  height: 10px;
  border-radius: 100px;
  margin-right: 5px;
  background-color: @gray-dark;
  box-shadow: 0 0 5px 0 @gray-dark;
}

.propertiesContainer {
  background-color: @charcoal-blue;
  margin-top: 10px;
}

.editMode {
  border: 1px solid @green;
}

.horizontalDivider,
.errorHorizontalDivider {
  grid-column: ~'1/-1';
  border-bottom: 1px solid fade(@gray, 20);
  padding-bottom: 5px;
}

.verticalDivider {
  width: 1px;
  margin: 2px 5px;
  margin-top: 7px;
  background-color: fade(@gray, 20);
}

.name {
  font-size: 13px;
  margin-top: 5px;
  display: flex;
  align-items: center;
  height: 20px;
  width: 100%;
}

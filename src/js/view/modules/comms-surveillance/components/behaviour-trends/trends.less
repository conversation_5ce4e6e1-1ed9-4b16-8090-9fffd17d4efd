@import 'less/site.less';

.title {
  display: flex;
  align-items: center;
  & > div {
    padding-left: 8px;
  }
}

.charts {
  display: grid;
  padding: 10px 0;
  grid-gap: 14px;
  background-color: @dark-background;
  border-radius: 4px;
  & > * {
    box-shadow: 0 0 6px 6px darken(@dark-background, 5%);
    min-width: 0;
  }
}

.wordCloudContainer {
  .wordCloud {
    height: 320px;
  }
}

.multiLaneBarChart {
  height: 320px;
}

.resetButton {
  button {
    height: 35px;
  }
}

@import 'less/site.less';
@import 'less/scrollbars.less';

.similar-alerts {
  margin-top: 10px;
}
.similar-alerts-title {
  font-size: 15px;
  display: block;
  font-style: italic;
}
.similar-alerts-item {
  text-decoration: underline;
  cursor: pointer;
}
.alert-list {
  .thinScrollbar();
  padding-inline-start: 15px;
  max-height: 270px;
  overflow-y: auto;
}
.alert-list-item {
  margin-bottom: 4px;
  font-size: 14px;
}
.alertUNRESOLVED {
  color: @red;
}

.alertUNDER_INVESTIGATION,
.alertIN_PROGRESS,
.alertESCALATED,
.alertIN_REVIEW {
  color: @yellow;
}

.alertRESOLVED,
.alertRESOLVED_WITH_BREACH,
.alertRESOLVED_WITH_INVESTIGATION,
.alertRESOLVED_WITH_INVESTIGATION_WITH_BREACH,
.alertRESOLVED_WITH_DISMISSAL {
  color: @green;
}

.alertNOT_DETECTED {
  color: @blue;
}

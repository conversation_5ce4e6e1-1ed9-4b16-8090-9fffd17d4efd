@import 'less/site.less';
@import 'less/tables.less';
@import 'less/layout.less';

.tableSection {
  .tableSection();
  width: 100%;
  margin: 0 auto;
}

.table {
  .standardTable();
  border: 1px solid @gray-darker;
  min-width: 100%;
  border-radius: 4px;
  margin: 5px 0 10px 0;
}

.tableLoading {
  .standardTableLoading();
}

.headerCell {
  .standardHeaderCell();
}

.cell {
  .standardCell();
}

.languageCell {
  .cell();
  align-items: center;
  & > :first-child {
    margin-right: 3px;
  }
}
.buttonWrapper {
  display: flex;
  grid-column: span 2;
}

.buttonCell {
  .standardCell();
  justify-content: flex-end;
  padding: 5px 10px 5px 0;
  button {
    width: 170px;
  }
}

.rightAlignCell {
  .cell();
  justify-content: flex-end;
}

.red {
  color: @red;
}

.textCell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.actionBar {
  padding: 0 0 @grid-gutter-width / 2 0;
  display: flex;
  justify-content: flex-end;
}

.active {
  display: contents;
  &:hover > * {
    cursor: auto;
  }
}

.termTypePillsContainer {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.termTypePills {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 2px;
  & > button {
    margin-right: 10px;
    width: 100px;
    height: 50px;
  }
  & > button:last-of-type {
    margin-right: 0;
  }
}

.title {
  width: 100%;
  text-align: center;
  font-size: 28px;
  color: @white;
  margin-top: 0;
}

.fullRow {
  grid-column: ~'1/-1';
}

.centered {
  justify-content: center;
}

.toggleCell {
  padding: 0 14px;
  grid-column: span 2;
  & > div:first-child {
    min-width: 250px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.listToggleOuterContainer {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
}
.listToggleContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 1px solid @green;
  border-radius: 8px;
  margin-bottom: 8px;
}

.listToggleText {
  margin: 0 20px;
  width: 100px;
  &:last-of-type {
    text-align: right;
  }
}

.titleContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.innerTitleContainer {
  display: flex;
  justify-content: left;
  align-items: center;
  width: 100%;

  & > * {
    width: fit-content;
  }
  .separator {
    margin: 0 5px 0 10px;
  }
}
.search {
  width: 340px;
  margin-left: 5px;
}
.titleWidgets {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.divider {
  .standardDivider();
  margin-left: 10px;
}
.languageSelector {
  display: flex;
  justify-content: center;
  align-self: center;
  margin-right: -5px;
}
.fullWidth {
  width: 100%;
  margin: 0;
}
.tableTitle {
  width: 100px;
}
.edited {
  .active();
  color: @yellow;
}
.editedHint {
  color: @yellow;
  margin-left: 10px;
  margin-bottom: 10px;
  font-size: 12px;
}
.triggerButton {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 170px;
  transform: translateX(44vw);
  width: max-content;
  .verticalLine {
    width: 1px;
    border-right: 2px dotted @blue;
    flex-grow: 1;
  }
  .circleButton {
    width: 130px;
    height: 130px;
    border-radius: 100px;
    border: 1px solid @blue;
    color: @blue;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 17px;
    box-shadow: 0 0 10px 0 @blue;
    .large {
      font-size: 35px;
      height: 30px;
      margin-bottom: 20px;
      margin-top: -20px;
    }
    &:hover {
      background-color: @blue;
      color: @white;
      cursor: pointer;
    }
  }
}

import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { useDispatch } from 'react-redux';
import { defineMessages, useIntl } from 'react-intl';

import { fetchAttachmentsList } from 'actions/comm-surv/query-builder';
import { attachmentsFieldClicked, attachmentsSizeChanged } from 'actions/comm-surv/bespoke.actions';

import SmallButton from 'view/components/small-button/small-button';
import IncludeExcludeList from 'view/components/include-exclude-list/include-exclude-list';
import ValueRangeTableSlider from 'view/components/surveillance/value-range-slider/value-range-slider';

import './bespoke.less';

const messages = defineMessages({
  fileType: {
    defaultMessage: 'File Type',
    id: 'modules.cSurv.bespoke.attachments.fileType',
  },
  fileName: {
    defaultMessage: 'File Name',
    id: 'modules.cSurv.bespoke.attachments.fileName',
  },
  fileSize: {
    defaultMessage: 'File Size',
    id: 'modules.cSurv.bespoke.attachments.fileSize',
  },
});

const trends = {
  fileNames: 'fileNames',
  fileTypes: 'fileTypes',
};

const Attachments = ({ filter }) => {
  const { formatMessage } = useIntl();

  const trend = trends[filter.slice.split('/')[1]];

  const dispatch = useDispatch();
  const fetchLists = useCallback(
    ({ searchTerm }) => {
      if (trend) {
        return dispatch(
          fetchAttachmentsList({
            searchTerm,
          })
        ).then(response => {
          if (['fileNames', 'fileTypes'].includes(trend) && response[trend]) {
            return response[trend].map(item => ({
              label: item.name,
              id: item.id,
              count: item.count,
            }));
          }
          return [];
        });
      } else {
        return Promise.resolve([]);
      }
    },
    [dispatch, trend]
  );

  const fileNamesSelected = filter.slice.split('/')[1] === 'fileNames';
  const fileTypesSelected = filter.slice.split('/')[1] === 'fileTypes';

  return (
    <div styleName="filterListEdit">
      <div styleName="singleList">
        <SmallButton
          onClick={() => dispatch(attachmentsFieldClicked({ id: filter.id, field: 'fileTypes' }))}
          selected={filter.slice.split('/')[1] === 'fileTypes'}
          color="blue"
        >
          {formatMessage(messages.fileType)}
        </SmallButton>
        <SmallButton
          onClick={() => dispatch(attachmentsFieldClicked({ id: filter.id, field: 'fileNames' }))}
          selected={filter.slice.split('/')[1] === 'fileNames'}
          color="blue"
        >
          {formatMessage(messages.fileName)}
        </SmallButton>
        <SmallButton
          onClick={() => dispatch(attachmentsFieldClicked({ id: filter.id, field: 'fileSize' }))}
          selected={filter.slice.split('/')[1] === 'fileSize'}
          color="blue"
        >
          {formatMessage(messages.fileSize)}
        </SmallButton>
        <div style={{ gridColumn: '1 / -1', minWidth: 0 }}>
          {(fileNamesSelected || fileTypesSelected) && (
            <IncludeExcludeList
              key={trend}
              dispatch={dispatch}
              title={
                fileTypesSelected
                  ? formatMessage(messages.fileType)
                  : formatMessage(messages.fileName)
              }
              listId={filter.id}
              included={filter.selected}
              excluded={filter.excluded}
              fetchData={fetchLists}
            />
          )}
          {filter.slice.split('/')[1] === 'fileSize' && (
            <ValueRangeTableSlider
              key={filter.id}
              start={2000000}
              end={8000000}
              min={0}
              max={10000000}
              fieldType="NUMBER"
              field="sizeInBytes"
              selectedValues={{
                field: 'sizeInBytes',
                fieldType: 'NUMBER',
                rangeType: filter.range?.rangeType,
                rangeMax: filter.range?.max,
                rangeMin: filter.range?.min,
                value: filter.range?.value,
              }}
              onItemSelected={selection => {
                dispatch(
                  attachmentsSizeChanged({
                    id: filter.id,
                    rangeType: selection.rangeType,
                    min: selection.rangeMin,
                    max: selection.rangeMax,
                    value: selection.value,
                  })
                );
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
};

Attachments.propTypes = {
  filter: PropTypes.shape({
    id: PropTypes.string.isRequired,
    slice: PropTypes.string.isRequired,
    between: PropTypes.arrayOf(
      PropTypes.shape({
        field: PropTypes.string.isRequired,
        selected: PropTypes.arrayOf(PropTypes.string).isRequired,
        excluded: PropTypes.arrayOf(PropTypes.string).isRequired,
      })
    ),
    selected: PropTypes.arrayOf(PropTypes.string).isRequired,
    excluded: PropTypes.arrayOf(PropTypes.string).isRequired,
    range: PropTypes.shape({
      value: PropTypes.number,
      min: PropTypes.number,
      max: PropTypes.number,
      rangeType: PropTypes.string,
    }),
  }),
};

export default Attachments;

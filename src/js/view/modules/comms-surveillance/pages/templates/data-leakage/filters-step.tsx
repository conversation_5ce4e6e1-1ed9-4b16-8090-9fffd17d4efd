import React, { useEffect, type FC } from 'react';
import { defineMessages, useIntl } from 'react-intl';
import { useParams } from 'react-router';
import { useAppDispatch, useAppSelector } from 'js/redux-toolkit/hooks';

import SvgPrediction from 'view/components/svg-icons/prediction';

import CommsSurveillanceFilters from '../../bespoke/comms-surveillance-filters';

import styles from './filters-step.less';

const messages = defineMessages({
  alertPrediction: {
    defaultMessage: 'Alert Prediction',
    id: 'module.cSurv.infoBarrier.filtersStep.alertPrediction',
  },
  filters: {
    defaultMessage: 'Filters',
    id: 'module.cSurv.infoBarrier.filtersStep.filters',
  },
  withThisContinuedVolumeMonth: {
    defaultMessage:
      'With this continued volume, you would receive {perMonth} alerts per month ({perDay} per day)',
    id: 'module.cSurv.infoBarrier.filtersStep.withThisContinuedVolumeMonth',
  },
  withThisContinuedVolumeWeek: {
    defaultMessage:
      'With this continued volume, you would receive {perWeek} alerts per week ({perDay} per day)',
    id: 'module.cSurv.infoBarrier.filtersStep.withThisContinuedVolumeWeek',
  },
});

const FiltersStep: FC<{ filtersToShow: string[] }> = ({ filtersToShow }) => {
  const dispatch = useAppDispatch();
  const { formatMessage, formatNumber } = useIntl();
  const { id: watchId } = useParams();

  const watchBuilder = useAppSelector(state => state.commsSurv.watchBuilder);
  const query = useAppSelector(state =>
    state.commsSurv.watchBuilder.queries.find((q: { id: string }) => q.id === watchId)
  );

  useEffect(() => {
    // Call data leakage prediction endpoint
  }, [watchId, watchBuilder, query, dispatch]);

  const predictions = {
    data: {
      averageHitPerDay: 0,
      averageHitPerWeek: 0,
    },
    loading: false,
  };

  const showPrediction = false;

  return query ? (
    <>
      <CommsSurveillanceFilters
        uiState={watchBuilder.uiState}
        query={query}
        editingQuery={watchBuilder.editingQuery}
        filtersToShow={filtersToShow}
      />
      {showPrediction && (
        <div
          className={`${styles.alertPredictionBox} ${
            predictions.loading ? styles.alertPredictionBoxLoading : ''
          }`}
        >
          <h3>
            <SvgPrediction /> {formatMessage(messages.alertPrediction)}
          </h3>
          <p>
            {predictions.data.averageHitPerWeek &&
              formatMessage(messages.withThisContinuedVolumeWeek, {
                perDay: (
                  <span className={styles.red}>
                    {formatNumber(predictions.data.averageHitPerDay)}
                  </span>
                ),
                perWeek: (
                  <span className={styles.red}>
                    {formatNumber(predictions.data.averageHitPerWeek)}
                  </span>
                ),
              })}
          </p>
        </div>
      )}
    </>
  ) : null;
};

export default FiltersStep;

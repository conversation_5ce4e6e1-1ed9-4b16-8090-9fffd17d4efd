import React, { useEffect } from 'react';
import { defineMessages, useIntl } from 'react-intl';
import { useSelector, useDispatch } from 'react-redux';
import { useParams } from 'react-router';

import PropTypes from 'prop-types';
import SvgPrediction from 'view/components/svg-icons/prediction';
import { getRandomSamplingPrediction } from 'actions/comm-surv/behaviours.actions';
import { formatNumberApproximately } from 'js/util/string';
import { MAX_ABSOLUTE_COMMS, RANDOM_SAMPLING_COMMS_ALERTS } from 'constants/csurv2-templates';

import CommsSurveillanceFilters from '../../bespoke/comms-surveillance-filters';

import styles from './filters-step.less';

const sharedMessages = defineMessages({
  alertPrediction: {
    defaultMessage: 'Alert Prediction',
    id: 'module.cSurv.randomSampling.filtersStep.alertPrediction',
  },
  filters: {
    defaultMessage: 'Filters',
    id: 'module.cSurv.randomSampling.filtersStep.filters',
  },
  tooHigh: {
    defaultMessage: 'Note that the number of alerts generated on each execution is capped at {max}',
    id: 'module.cSurv.randomSampling.filtersStep.tooHigh',
  },
  withThisContinuedVolumeMonth: {
    defaultMessage:
      'With this continued volume, you would receive {perMonth} alerts per month ({perDay} per day)',
    id: 'module.cSurv.randomSampling.filtersStep.withThisContinuedVolumeMonth',
  },
  withThisContinuedVolumeWeek: {
    defaultMessage:
      'With this continued volume, you would receive {perWeek} alerts per week ({perDay} per day)',
    id: 'module.cSurv.randomSampling.filtersStep.withThisContinuedVolumeWeek',
  },
});

const commsMessages = defineMessages({
  currentlyThePlatform: {
    defaultMessage: 'Currently, the platform contains {count} Communication records.',
    id: 'module.cSurv.randomSampling.filtersStep.currentlyThePlatformComms',
  },
  ...sharedMessages,
});

const cSurvAlertsMessages = defineMessages({
  currentlyThePlatform: {
    defaultMessage: 'Currently, the platform contains {count} resolved alerts.',
    id: 'module.cSurv.randomSampling.filtersStep.currentlyThePlatformCSurvAlerts',
  },
  ...sharedMessages,
});

const FiltersStep = ({ filtersToShow, templateType }) => {
  const messages =
    templateType === RANDOM_SAMPLING_COMMS_ALERTS ? cSurvAlertsMessages : commsMessages;

  const dispatch = useDispatch();
  const { formatMessage, formatNumber } = useIntl();
  const { id: watchId } = useParams();

  const watchBuilder = useSelector(state => state.commsSurv.watchBuilder);
  const query = useSelector(state =>
    state.commsSurv.watchBuilder.queries.find(q => q.id === watchId)
  );

  useEffect(() => {
    const scheduleDetails = {
      ...watchBuilder.schedules.find(schedule => schedule.id === watchId),
      id: undefined,
    };

    if (query?.template.thresholds.samplePercentage)
      dispatch(getRandomSamplingPrediction({ query, scheduleDetails }));
  }, [watchId, watchBuilder, query, dispatch]);

  const predictions = useSelector(state => state.commsSurv.templates.randomSamplingPrediction);

  const showPrediction =
    query?.template.thresholds.samplePercentage &&
    Number.isFinite(predictions.data?.averageHitPerDay) &&
    (Number.isFinite(predictions.data?.averageHitPerMonth) ||
      Number.isFinite(predictions.data?.averageHitPerWeek));

  const predictionIsInadvisedlyHigh =
    predictions.data?.averageHitPerMonth > MAX_ABSOLUTE_COMMS ||
    (predictions.data?.averageHitPerMonth === null &&
      predictions.data?.averageHitPerDay > MAX_ABSOLUTE_COMMS);

  return query ? (
    <>
      <CommsSurveillanceFilters
        uiState={watchBuilder.uiState}
        query={query}
        editingQuery={watchBuilder.editingQuery}
        filtersToShow={filtersToShow}
      />
      {showPrediction && (
        <div
          className={`${styles.alertPredictionBox} ${
            predictions.loading ? styles.alertPredictionBoxLoading : ''
          }`}
        >
          <h3>
            <SvgPrediction /> {formatMessage(messages.alertPrediction)}
          </h3>
          <p>
            {formatMessage(messages.currentlyThePlatform, {
              count: formatNumberApproximately(predictions.data.totalHits),
            })}
          </p>
          <p>
            {!!predictions.data.averageHitPerMonth &&
              formatMessage(messages.withThisContinuedVolumeMonth, {
                perDay: (
                  <span className={styles.red}>
                    {formatNumber(predictions.data.averageHitPerDay)}
                  </span>
                ),
                perMonth: (
                  <span className={styles.red}>
                    {formatNumber(predictions.data.averageHitPerMonth)}
                  </span>
                ),
              })}
            {!predictions.data.averageHitPerMonth &&
              formatMessage(messages.withThisContinuedVolumeWeek, {
                perDay: (
                  <span className={styles.red}>
                    {formatNumber(predictions.data.averageHitPerDay)}
                  </span>
                ),
                perWeek: (
                  <span className={styles.red}>
                    {formatNumber(predictions.data.averageHitPerWeek)}
                  </span>
                ),
              })}
          </p>
          <p>
            <span className={styles.red}>
              {predictionIsInadvisedlyHigh &&
                formatMessage(messages.tooHigh, {
                  max: formatNumber(MAX_ABSOLUTE_COMMS),
                })}
            </span>
          </p>
        </div>
      )}
    </>
  ) : null;
};

FiltersStep.propTypes = {
  filtersToShow: PropTypes.array,
  templateType: PropTypes.String,
};

export default FiltersStep;

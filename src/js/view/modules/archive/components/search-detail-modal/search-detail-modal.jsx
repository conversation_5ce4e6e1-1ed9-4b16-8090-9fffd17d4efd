import React from 'react';
import PropTypes from 'prop-types';
import { useIntl } from 'react-intl';

import Modal from 'js/view/components/modal/basic-modal/basic-modal';
import Button from 'js/view/components/button/basic-button';
import SearchDetailCardContainer from 'view/modules/archive/components/search-detail-cards/search-detail-card-container';

import { MESSAGES } from 'js/view/modules/archive/constants/messages';

import styles from './search-detail-modal.less';

const SearchDetailModal = ({ show, onCloseModal, data }) => {
  const { formatMessage } = useIntl();

  const handleCloseModal = () => {
    onCloseModal();
  };

  return (
    <Modal dialogClassName={styles.modalBody} show={show} onHide={onCloseModal}>
      <Modal.Header>
        <Modal.Title>
          <div className={styles.modalTitle}>
            <span className="icon icon-magnifying-glass" style={{ fontSize: 32 }} />
            <p className={styles.titleText}>{formatMessage(MESSAGES.searchDetails)}</p>
          </div>
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <SearchDetailCardContainer data={data} />
      </Modal.Body>
      <Modal.Footer>
        <Button color="green" onClick={handleCloseModal} className={styles.confirmButton}>
          <span className={`${styles.thumbsUp} icon icon-thumbs-up`} />
          {formatMessage(MESSAGES.okayButton)}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

SearchDetailModal.propTypes = {
  data: PropTypes.object,
  onCloseModal: PropTypes.func.isRequired,
  show: PropTypes.bool.isRequired,
};

export default SearchDetailModal;

@import 'less/site.less';
@import 'less/layout.less';
@import 'less/modal.less';

.modalBody {
  width: auto;
}

.modalTitle {
  .modalTitle();
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.titleText {
  .titleText();
  padding-left: 0;
}

.confirmButton {
  color: @green;
  display: flex;
  border-radius: 4px;
  border: 1px solid @green;
  justify-content: center;
  align-items: center;
  background-color: @dark-green-background;
  padding: 5px 10px 5px 10px;
}

.thumbsUp {
  margin-right: 5px;
}

.svgIcon {
  height: 50px;
  width: 50px;
}

.buttonContainer {
  display: flex;
}

.redColored {
  color: @red;
  display: inline-block;
  margin-right: 4px;
}

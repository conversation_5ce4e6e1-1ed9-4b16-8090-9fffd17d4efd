import { defineMessages } from 'react-intl';
import { MODULES } from './constants';

export const COMMS_MESSAGES = defineMessages({
  deleteSearch: {
    defaultMessage: 'Delete Comms Archive Search',
    id: 'modules.pages.Archive.COMMS_MESSAGES.deleteSearch',
  },
  description: {
    defaultMessage:
      'Search your complete Comms Archive, retrieve historic searches, and perform data restorations..',
    id: 'modules.pages.Archive.COMMS_MESSAGES.description',
  },
  restoreSearch: {
    defaultMessage: 'Restore Comms Archive Search',
    id: 'modules.pages.Archive.COMMS_MESSAGES.restoreSearch',
  },
});

const ORDERS_MESSAGES = defineMessages({
  deleteSearch: {
    defaultMessage: 'Delete Orders Archive Search',
    id: 'modules.pages.Archive.ORDERS_MESSAGES.deleteSearch',
  },
  description: {
    defaultMessage:
      'Search your complete Orders & trades Archive, retrieve historic searches, and perform data restorations..',
    id: 'modules.pages.Archive.ORDERS_MESSAGES.description',
  },
  restoreSearch: {
    defaultMessage: 'Restore Orders Archive Search',
    id: 'modules.pages.Archive.ORDERS_MESSAGES.restoreSearch',
  },
});

export const MESSAGES = defineMessages({
  archiveAudit: {
    defaultMessage: 'Archive Audit',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.archiveAudit',
  },
  category: {
    defaultMessage: 'Category',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.modalLabel.category',
  },
  chats: {
    defaultMessage: 'Chats',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.modalLabel.chats',
  },
  completed: {
    defaultMessage: 'Completed',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.modalLabel.completed',
  },
  contents: {
    defaultMessage: 'CONTENTS',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.contents',
  },
  custom: {
    defaultMessage: 'Custom',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.modalLabel.custom',
  },
  dataSource: {
    defaultMessage: 'Data Source',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.modalLabel.dataSource',
  },
  deleteSearch: {
    defaultMessage: 'Delete this search',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.deleteSearch',
  },
  deleteSearchWarning: {
    defaultMessage: 'this Search is an irreversible action.{br} Do you wish to proceed?',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.deleteSearchWarning',
  },
  deleting: {
    defaultMessage: 'Deleting',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.deleting',
  },
  description: {
    defaultMessage: 'Description',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.modalLabel.description',
  },
  emails: {
    defaultMessage: 'Emails',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.modalLabel.emails',
  },
  filters: {
    defaultMessage: 'FILTERS',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.filters',
  },
  meetings: {
    defaultMessage: 'Meetings',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.modalLabel.meetings',
  },
  okayButton: {
    defaultMessage: 'Okay',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.okay',
  },
  participants: {
    defaultMessage: 'Participants',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.modalLabel.participants',
  },
  provenance: {
    defaultMessage: 'PROVENANCE',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.provenance',
  },
  rationale: {
    defaultMessage: 'RATIONALE',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.rationale',
  },
  reason: {
    defaultMessage: 'Reason',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.modalLabel.reason',
  },
  requested: {
    defaultMessage: 'Requested',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.modalLabel.requested',
  },
  requestedBy: {
    defaultMessage: 'Requested By',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.modalLabel.requestedBy',
  },
  searchDetails: {
    defaultMessage: 'Search Details',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.searchDetails',
  },
  searches: {
    defaultMessage: 'SEARCHES',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.searches',
  },
  search: {
    defaultMessage: 'SEARCH',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.search',
  },
  searchSubText: {
    defaultMessage: 'Define your Archival Search Criteria',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.searchSubText',
  },
  searchTheArchive: {
    defaultMessage: 'Search The Archive',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.searchTheArchive',
  },
  size: {
    defaultMessage: 'Size',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.modalLabel.size',
  },
  status: {
    defaultMessage: 'Status',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.modalLabel.status',
  },
  title: {
    defaultMessage: 'THE ARCHIVE',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.title',
  },
  voice: {
    defaultMessage: 'Voice',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.modalLabel.voice',
  },
  scope: {
    defaultMessage: 'Scope',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.scope',
  },
  searchTerms: {
    defaultMessage: 'Search Terms',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.searchTerms',
  },
  timeRange: {
    defaultMessage: 'Time Range',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.timeRange',
  },
  access: {
    defaultMessage: 'Access',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.access',
  },
  roles: {
    defaultMessage: 'Roles',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.roles',
  },
  summary: {
    defaultMessage: 'Summary',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.summary',
  },
  partialSearch: {
    defaultMessage: 'Partial Search',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.partialSearch',
  },
  execute: {
    defaultMessage: 'Execute',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.execute',
  },
  next: {
    defaultMessage: 'Next',
    id: 'modules.pages.Archive.GENERIC_MESSAGES.next',
  },
});

export const MODULES_MESSAGES = {
  [MODULES.COMMS]: COMMS_MESSAGES,
  [MODULES.ORDERS]: ORDERS_MESSAGES,
};

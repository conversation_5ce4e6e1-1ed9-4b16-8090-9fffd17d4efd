import React, { useRef, useEffect } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import logo from 'img/steeleye-logo/SteelEye-icon-white.png';
import { defineMessages, useIntl } from 'react-intl';

import usePDF from 'js/hooks/usePDF';

import DashheadToolbar from 'view/components/dashhead/toolbar';
import DashheadToolbarItem from 'view/components/dashhead/toolbar-item';
import Dashhead from 'view/components/dashhead';
import ButtonWithIcon from 'view/components/button-with-icon/button-with-icon';
import PDFTimeline from 'js/view/modules/new-best-execution/components/pdf-components/pdf-timeline/pdf-timeline';
import PDFHeader from 'js/view/modules/new-best-execution/components/pdf-components/pdf-heading/pdf-heading';
import VolumeAndLiquidity from 'js/view/modules/new-best-execution/components/pdf-components/volume-liquidity/volume-liquidity';
import PriceVolumeTimeline from 'view/modules/new-best-execution/components/graphs/price-volume-timeline/price-volume-timeline';
import TCABenchmarks from 'js/view/modules/new-best-execution/components/pdf-components/tca-benchmarks/tca-benchmarks';
import Footer from 'js/view/modules/new-best-execution/components/pdf-components/pdf-footer/pdf-footer';
import OrderExecutionsDetails from 'view/components/orders-detail-table-new/order-details-list';
import PrevalReval from 'js/view/modules/new-best-execution/components/pdf-components/preval-reval/preval-reval';
import Pdf from 'js/view/components/svg-icons/pdf';

import { getPrevalRevalById } from 'js/actions/orders/orders.actions';

import styles from './single-order-preview.less';

const messages = defineMessages({
  days: {
    defaultMessage: '14 days',
    id: 'bestEx.singleOrderPreview.days',
  },
  generateReport: {
    defaultMessage: 'Generate Report',
    id: 'bestEx.singleOrderPreview.generateReport',
  },
  hours: {
    defaultMessage: '1 hour',
    id: 'bestEx.singleOrderPreview.hours',
  },
  labelDays: {
    defaultMessage: 'Days',
    id: 'bestEx.singleOrderPreview.labelDays',
  },
  labelMinutes: {
    defaultMessage: 'Minutes',
    id: 'bestEx.singleOrderPreview.labelMinutes',
  },
});

const SingleOrderPreview = ({ data, fetchPrevalReval, prevalRevalData, id }) => {
  const componentRef = useRef();
  const { formatMessage } = useIntl();
  const { generatePDF, loading } = usePDF({ exportName: 'order-report' });
  const orderId = data?.['&id'];
  const instrumentId = data?.instrumentDetails?.instrument?.ext?.instrumentUniqueIdentifier;
  const isSingleExecution = data?.executionDetails?.orderStatus !== 'NEWO';
  const venue = data?.instrumentDetails?.instrument?.venue?.tradingVenue;
  const instrumentIdCode = data?.instrumentDetails?.instrument?.instrumentIdCode;

  useEffect(() => {
    fetchPrevalReval({ id });
  }, [fetchPrevalReval, id]);
  const daysInterval = prevalRevalData?.[id]?.data?.find(d => d.key === '14days');
  const hoursInterval = prevalRevalData?.[id]?.data?.find(d => d.key === '1hour');

  return (
    <>
      <Dashhead showGoBack>
        <DashheadToolbar>
          <DashheadToolbarItem>
            <ButtonWithIcon
              onClick={() =>
                new Promise((resolve, reject) => {
                  generatePDF({ numberOfPages: 2, ref: componentRef, reject, resolve });
                })
              }
              Icon={Pdf}
              filename="generateReport"
              text={formatMessage(messages.generateReport)}
              iconClassName={styles.pdfIcon}
              innerClassName={styles.innerClassName}
            />
          </DashheadToolbarItem>
        </DashheadToolbar>
      </Dashhead>

      <div styleName={`wrapper ${loading ? 'tableLoading' : ''}`}>
        <div styleName="pdfBody" ref={componentRef}>
          <div styleName="pdfBodyMain">
            <div styleName="header">
              <img src={logo} alt="Steeleye Logo" />
            </div>
            <div styleName="main">
              <PDFHeader data={data} />
              <div styleName="full-horizontal-divider" />
              <PDFTimeline data={data} />
              <div styleName="tables">
                <div styleName="tableFirst">
                  <VolumeAndLiquidity data={data} />
                  <TCABenchmarks data={data} useInPDF />
                </div>
                <div styleName="tableSecond">
                  <PriceVolumeTimeline
                    data={data}
                    oldStyles={false}
                    isSingleExecution={isSingleExecution}
                    instrumentName={data?.instrumentDetails?.instrument?.instrumentFullName}
                    instrumentUniqueIdentifier={instrumentId}
                    isin={instrumentIdCode}
                    venue={venue}
                    customHeight={550}
                    className={styles.timeline}
                    useInPDF
                  />
                </div>
              </div>
              <div>
                <PrevalReval
                  data={daysInterval}
                  orderDate={data?.timestamps?.orderReceived}
                  count={formatMessage(messages.days)}
                  label={formatMessage(messages.labelDays)}
                />
              </div>
              <div>
                <PrevalReval
                  data={hoursInterval}
                  orderDate={data?.timestamps?.orderReceived}
                  count={formatMessage(messages.hours)}
                  label={formatMessage(messages.labelMinutes)}
                />
              </div>
              <Footer />
            </div>
          </div>
          <div styleName="pdfBodyMain">
            <div styleName="header">
              <img src={logo} alt="Steeleye Logo" />
            </div>
            <div styleName="main">
              <PDFHeader data={data} />
              <div styleName="full-horizontal-divider" />
              <div styleName="listContainer">
                <OrderExecutionsDetails
                  shrinkValues
                  ignoreDateQuery
                  orderId={orderId}
                  orderCurrency={
                    data?.transactionDetails?.priceCurrency ||
                    data?.bestExecutionData?.transactionVolume?.nativeCurrency
                  }
                />
              </div>
              <Footer />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

SingleOrderPreview.propTypes = {
  data: PropTypes.object,
  fetchPrevalReval: PropTypes.func,
  id: PropTypes.string,
  prevalRevalData: PropTypes.object,
};

const mapStateToProps = state => {
  return {
    prevalRevalData: state.orders.prevalRevalById,
    tcaMetricsSummary: state.bestEx.tcaMetricsSummary,
  };
};

const mapDispatchToProps = {
  fetchPrevalReval: getPrevalRevalById,
};

export default connect(mapStateToProps, mapDispatchToProps)(SingleOrderPreview);

import { defineMessages } from 'react-intl';

export const MESSAGES = defineMessages({
  assetClassProfile: {
    defaultMessage: 'Asset Class Profile',
    id: 'module.trades-surveillance.single-mar-scenario.asset-class-profile',
  },
  averageExecutedNotional: {
    defaultMessage: 'Average Executed Notional: {count}',
    id: 'module.trades-surveillance.single-mar-scenario.average-executed-notional',
  },
  averageQuantity: {
    defaultMessage: 'Average Quantity: {count}',
    id: 'module.trades-surveillance.single-mar-scenario.average-quantity',
  },
  behaviouralContext: {
    defaultMessage: 'Behavioural Context',
    id: 'module.trades-surveillance.single-mar-scenario.behavioural-context',
  },
  behaviouralContextDescription: {
    defaultMessage: `"Behavioural Context" provides Order and Trade stats involving the same Client, Trader, Portfolio Manager, and Desk as detected in this scenario, including analysis on historic order timings (by month, weekday, hour), and asset class splits.`,
    id: 'module.trades-surveillance.single-mar-scenario.behavioural-context-description',
  },
  buys: {
    defaultMessage: '{count} {count, plural, one {buy} other {buys}}',
    id: 'module.trades-surveillance.single-mar-scenario.buys',
  },
  client: {
    defaultMessage: 'Client',
    id: 'module.trades-surveillance.single-mar-scenario.client',
  },
  commsSurveillance: {
    defaultMessage: 'Comms Surveillance',
    id: 'module.trades-surveillance.single-mar-scenario.comms-surveillance',
  },
  description: {
    defaultMessage: `Context-mode gives you visibility of trade-flow relevant to an alert. Context is available by Client, Desk, Trader, Portfolio Manager, via any of the below views.`,
    id: 'module.trades-surveillance.single-mar-scenario.description',
  },
  descriptionLine3: {
    defaultMessage: 'This {behaviour} Watch has been configured to run at the {entityType} level.',
    id: 'module.trades-surveillance.single-mar-scenario.description-line-3',
  },
  desk: {
    defaultMessage: 'Desk',
    id: 'module.trades-surveillance.single-mar-scenario.desk',
  },
  giveMeContext: {
    defaultMessage: 'Give me Context!',
    id: 'module.trades-surveillance.single-mar-scenario.give-me-context',
  },
  instrumentContext: {
    defaultMessage: 'Instrument Context',
    id: 'module.trades-surveillance.single-mar-scenario.instrument-context',
  },
  instrumentContextDescription: {
    defaultMessage: `"Instrument Context" searches for any Orders on the same security, derivatives of the same security, or related securities involving the same Client, Trader, Portfolio Manager, and Desk as detected in this scenario.`,
    id: 'module.trades-surveillance.single-mar-scenario.instrument-context-description',
  },
  loading: {
    defaultMessage: 'Loading...',
    id: 'module.trades-surveillance.single-mar-scenario.loading',
  },
  net: {
    defaultMessage: 'Net',
    id: 'module.trades-surveillance.single-mar-scenario.net',
  },
  noClientDetected: {
    defaultMessage: 'No Clients detected in this scenario',
    id: 'module.trades-surveillance.single-mar-scenario.noClientDetected',
  },
  noDeskDetected: {
    defaultMessage: 'No Desks detected in this scenario',
    id: 'module.trades-surveillance.single-mar-scenario.noDeskDetected',
  },
  noPMDetected: {
    defaultMessage: 'No Portfolio Managers detected in this scenario',
    id: 'module.trades-surveillance.single-mar-scenario.noPortfolioManagersDetected',
  },
  noResultsFound: {
    defaultMessage: 'No results found',
    id: 'module.trades-surveillance.single-mar-scenario.noResultsFound',
  },
  noTraderDetected: {
    defaultMessage: 'No Traders detected in this scenario',
    id: 'module.trades-surveillance.single-mar-scenario.noTraderDetected',
  },
  orders: {
    defaultMessage: '{count} {count, plural, one {Order} other {Orders}}',
    id: 'module.trades-surveillance.single-mar-scenario.orders',
  },
  pm: {
    defaultMessage: 'PM',
    id: 'module.trades-surveillance.single-mar-scenario.pm',
  },
  portfolio_manager: {
    defaultMessage: 'PM',
    id: 'module.trades-surveillance.single-mar-scenario.portfolio_manager',
  },
  portfolio_manager_desk: {
    defaultMessage: 'Desk',
    id: 'module.trades-surveillance.single-mar-scenario.portfolio_manager_desk',
  },
  portfolioManager: {
    defaultMessage: 'Portfolio Manager',
    id: 'module.trades-surveillance.single-mar-scenario.portfolio-manager',
  },
  portfolioManagerDesk: {
    defaultMessage: 'Portfolio Manager Desk',
    id: 'module.trades-surveillance.single-mar-scenario.portfolio-manager-desk',
  },
  preSelectedText: {
    defaultMessage: 'Your alert is evaluated by "{entityType}", so its pre-selected',
    id: 'module.trades-surveillance.single-mar-scenario.pre-selected-text',
  },
  relatedSecurities: {
    defaultMessage: 'Related Securities',
    id: 'module.trades-surveillance.single-mar-scenario.related-securities',
  },
  resolved: {
    defaultMessage: '{count} Resolved',
    id: 'module.trades-surveillance.single-mar-scenario.resolved',
  },
  sameSecurity: {
    defaultMessage: 'Same Security',
    id: 'module.trades-surveillance.single-mar-scenario.same-security',
  },
  sells: {
    defaultMessage: '{count} {count, plural, one {sell} other {sells}}',
    id: 'module.trades-surveillance.single-mar-scenario.sells',
  },
  surveillanceContext: {
    defaultMessage: 'Surveillance Context',
    id: 'module.trades-surveillance.single-mar-scenario.surveillance-context',
  },
  surveillanceContextDescription: {
    defaultMessage: `"Surveillance Context" searches for any alerts within Comms, and Trade Surveillance, that contain the same Client, Trader, Portfolio Manager, and Desk as detected in this scenario.`,
    id: 'module.trades-surveillance.single-mar-scenario.surveillance-context-description',
  },
  timingProfile: {
    defaultMessage: 'Timing Profile',
    id: 'module.trades-surveillance.single-mar-scenario.timing-profile',
  },
  totalNotional: {
    defaultMessage: 'Total Notional: {count}',
    id: 'module.trades-surveillance.single-mar-scenario.total-notional',
  },
  totalQuantity: {
    defaultMessage: 'Total Quantity: {count}',
    id: 'module.trades-surveillance.single-mar-scenario.total-quantity',
  },
  trader: {
    defaultMessage: 'Trader',
    id: 'module.trades-surveillance.single-mar-scenario.trader',
  },
  tradeSurveillance: {
    defaultMessage: 'Trade Surveillance',
    id: 'module.trades-surveillance.single-mar-scenario.trade-surveillance',
  },
  underInvestigation: {
    defaultMessage: '{count} Under Investigation',
    id: 'module.trades-surveillance.single-mar-scenario.underInvestigation',
  },
  unresolved: {
    defaultMessage: '{count} Unresolved',
    id: 'module.trades-surveillance.single-mar-scenario.unresolved',
  },
});

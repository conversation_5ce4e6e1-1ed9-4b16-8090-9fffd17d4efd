@import 'less/site.less';

.container {
  .innerContainer {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 15px;
  }
}

.fileContainer {
  background-color: fade(@gray-light, 10);
  font-size: @font-size-base;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid @pale-neutral;
  border-radius: 10px;
  padding: 10px;
  color: fade(@content-label-and-text, 55);
  .filenameRow {
    word-break: break-all;
  }
  .filenameRow > :first-child {
    margin-right: 10px;
  }
  .fileSizeRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .downloadBtn {
      border: 1px solid @blue;
      background-color: transparent;
      border-radius: 4px;
      color: @blue;
      padding: 1px 5px;
      &:hover {
        background-color: @blue;
        color: @gray-light;
      }
      &:disabled {
        cursor: not-allowed;
        padding: 5px;
      }
      transition: all 0.3s;
    }
    .fileSize {
      color: fade(@content-label-and-text, 20);
      font-size: 11px;
      font-style: italic;
    }
  }
}

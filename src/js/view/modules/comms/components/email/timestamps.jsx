import React, { memo } from 'react';
import PropTypes from 'prop-types';
import { defineMessages } from 'react-intl';
import isEqual from 'lodash/isEqual';

import { withWidgetErrorHandler } from 'view/components/layout/hoc';
import PropertyListWithHiddenFields from 'view/components/property/property-list-with-hidden-fields';
import { getFormattedTime } from 'js/util/time';

const messages = defineMessages({
  timeReceived: {
    defaultMessage: 'Time Received',
    id: 'module.comms.email.timestamps.timeReceived',
  },
  timeSent: {
    defaultMessage: 'Time Sent',
    id: 'module.comms.email.timestamps.timeSent',
  },
  timestamps: {
    defaultMessage: 'TIMESTAMPS',
    id: 'module.comms.email.timestamps.title',
  },
});

const TimeStamps = memo(
  ({ data, searchTerms }) => (
    <PropertyListWithHiddenFields
      title={messages.timestamps}
      list={[
        {
          message: messages.timeSent,
          value: getFormattedTime(data?.timestampStart, 'DD MMM YYYY, HH:mm:ss'),
        },
        {
          message: messages.timeReceived,
          value: getFormattedTime(data?.timestampEnd, 'DD MMM YYYY, HH:mm:ss'),
        },
      ]}
      searchTerms={searchTerms}
    />
  ),
  (nextProps, prevProps) =>
    isEqual(nextProps.data, prevProps.data) && isEqual(nextProps.searchTerms, prevProps.searchTerms)
);

TimeStamps.propTypes = {
  data: PropTypes.object,
  searchTerms: PropTypes.array,
};

TimeStamps.defaultProps = {
  data: {},
  searchTerms: [],
};

export default withWidgetErrorHandler(TimeStamps);

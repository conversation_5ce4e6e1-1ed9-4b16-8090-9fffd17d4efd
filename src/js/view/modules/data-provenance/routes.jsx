import React from 'react';
import { Routes, Route, Navigate } from 'react-router';

import NotFound from 'view/modules/error/pages/not-found';

import ROUTES from 'constants/routes';

import Dashboard from './pages/dashboard/dashboard';
import Archived from './pages/new-archived';
import Audit from './pages/new-audit';
import AuditFileDetails from './pages/detail/single-file-audit';
import AuditRecordDetail from './pages/detail/record';
import CascadeAudit from './pages/new-cascade-audit';
import CascadeAuditDetail from './pages/detail/cascade-audit';
import FileAudit from './pages/new-file-audit';
import QuarantineDetail from './pages/quarantine/detail';
import QuarantineListing from './pages/quarantine/new-listing';
import SinkAuditDetail from './pages/detail/sink-audit';

const DataProvenanceRoutes = () => {
  return (
    <Routes>
      <Route path={ROUTES.DATA_PROVENANCE.relArchived} element={<Archived />} />
      <Route path={ROUTES.DATA_PROVENANCE.AUDIT.relIndex} element={<Audit />} />
      <Route path={ROUTES.DATA_PROVENANCE.AUDIT_FILE.relSearch} element={<AuditFileDetails />} />
      <Route path={ROUTES.DATA_PROVENANCE.AUDIT_FILE.relSingle} element={<AuditFileDetails />} />
      <Route path={ROUTES.DATA_PROVENANCE.AUDIT_RECORD.relSearch} element={<AuditRecordDetail />} />
      <Route path={ROUTES.DATA_PROVENANCE.AUDIT_RECORD.relSingle} element={<AuditRecordDetail />} />
      <Route path={ROUTES.DATA_PROVENANCE.CASCADE_AUDIT.relIndex} element={<CascadeAudit />} />
      <Route
        path={ROUTES.DATA_PROVENANCE.CASCADE_AUDIT.relSearch}
        element={<CascadeAuditDetail />}
      />
      <Route
        path={ROUTES.DATA_PROVENANCE.CASCADE_AUDIT.relSingle}
        element={<CascadeAuditDetail />}
      />
      <Route path={ROUTES.DATA_PROVENANCE.relDashboard} element={<Dashboard />} />
      <Route path={ROUTES.DATA_PROVENANCE.relFileAudit} element={<FileAudit />} />
      <Route path={ROUTES.DATA_PROVENANCE.QUARANTINE.relIndex} element={<QuarantineListing />} />
      <Route path={ROUTES.DATA_PROVENANCE.QUARANTINE.relSearch} element={<QuarantineDetail />} />
      <Route path={ROUTES.DATA_PROVENANCE.QUARANTINE.relSingle} element={<QuarantineDetail />} />
      <Route path={ROUTES.DATA_PROVENANCE.AUDIT.relSearch} element={<SinkAuditDetail />} />
      <Route path={ROUTES.DATA_PROVENANCE.AUDIT.relSingle} element={<SinkAuditDetail />} />
      <Route
        path={ROUTES.DATA_PROVENANCE.relIndex}
        element={<Navigate to={ROUTES.DATA_PROVENANCE.relDashboard} replace />}
      />
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

export default DataProvenanceRoutes;

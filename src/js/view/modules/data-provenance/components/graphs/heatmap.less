@import 'less/site.less';
@import 'less/scrollbars.less';

.heatmapContainer {
  margin-top: 80px;
  padding: 0 1.5em;
  &.heatmapLoading {
    opacity: 0.5;
    pointer-events: none;
    will-change: opacity;
  }
}

.heatmapRow {
  display: flex;
  align-items: center;
  gap: 10px;
}

.heatmapYAxis {
  display: flex;
  justify-content: end;
  margin-bottom: 3px;
}

.heatmapCells {
  display: flex;
  gap: 3px;
}

.heatmapCell {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 35px;
  width: 35px;
  border-radius: 3px;
  margin-bottom: 3px;
  color: @full-white;
  cursor: pointer;
  position: relative;

  .heatmapTooltip {
    position: absolute;
    top: 120%;
    right: -1px;
    background-color: @dark-background;
    border: 1px solid @red;
    width: max-content;
    text-align: center;
    border-radius: 5px;
    padding: 0.5em 0.5em;
    z-index: 10;
    box-shadow: 0 0 10px 1px @red;

    &::after {
      content: '';
      position: absolute;
      right: 10px;
      top: -10%;
      width: 0;
      height: 0;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-bottom: 5px solid @red;
    }
  }

  &.heatmapCellSelected {
    border: 2px solid #00dffc !important;
  }
}

.heatmapXAxis {
  position: relative;
  .heatmapDate {
    position: absolute;
    top: -160%;
    left: -10%;
    transform: rotate(-60deg);
    width: 80px;
  }
}

.collapsibleDarker {
  background-color: @dark-background;
  border: 1px solid @gray-dark;
  border-radius: 4px 4px 0 0;
}
.collapsible {
  background-color: @widget-base;
}

.oldContainer {
  padding: 1em;
  background-color: @widget-base;
  box-shadow: 1px -2px 10px none #000;

  &.loading {
    opacity: 0.3;
  }
}

.darkContainer {
  .oldContainer();
  width: max-content;
  margin: 0 auto;
  background-color: @dark-background;
  border-radius: 4px;
  &.loading {
    opacity: 0.3;
  }
}
.heatmapWrapper {
  .thinScrollbar();
  height: 0;
  overflow-y: auto;
  overflow-x: auto;
  transition: height 0.3s ease;
  background-color: @dark-background;
  border: 1px solid @gray-dark;
}

.loadingCentered,
.noResultsFound {
  display: flex;
  align-items: center;
  margin-top: -80px;
}
.feedName {
  font-size: 10px;
  width: 150px;
}

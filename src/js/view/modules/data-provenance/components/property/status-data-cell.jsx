import React from 'react';
import PropTypes from 'prop-types';

import BodyCellContainer from 'view/components/table/body-cell-container';
import Label from 'js/view/components/basic/label';

import * as propertyConstants from 'constants/property';

import './status-data-cell.less';

export const Status = ({ status }) => {
  if (!status) {
    return propertyConstants.EMPTY_DISPLAY_VALUE;
  }

  return <Label styleName={`status-container ${status.toLowerCase()}`}>{status}</Label>;
};

Status.propTypes = { status: PropTypes.string };

const StatusDataCell = ({ item }) => {
  return (
    <BodyCellContainer>
      <Status status={item?.status} />
    </BodyCellContainer>
  );
};

StatusDataCell.propTypes = {
  item: PropTypes.object,
};

export default StatusDataCell;

@import 'less/site.less';
@import 'less/layout.less';

.horizontalDivider {
  margin-top: 1em;
}

.header {
  .standardHeaderSection();
}

.refineSection {
  .standardRefineSection();
}

.title {
  display: flex;
  font-size: 28px;
  align-items: baseline;
}

.layout {
  .standardContent();
}

.appliedFilters {
  padding-right: 15px;
}
.divider {
  .standardDivider();
}
.toolbarItem {
  .standardToolbarItem();
}

.prominentFilters {
  margin-bottom: 15px;
}

.collapsible {
  background-color: @gray-darker;
}

.collapsibleDarker {
  background-color: @dark-background;
  border: 1px solid @gray-dark;
  border-radius: 4px 4px 0 0;
}

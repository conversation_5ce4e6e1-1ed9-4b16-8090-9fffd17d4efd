import sortBy from 'lodash/sortBy';
import API from '@steeleye/schema';
import { defineMessages } from 'react-intl';

import { CURRENCIES_STRING_ARRAY } from 'constants/currencies';
import * as builderFields from 'util/query-builder/fields';
import * as modelsEnums from '@steeleye/schema/models/enums';
import { TIME_ZONES } from 'constants/time';
import * as propertyConstants from './property';
import * as widgetConstants from './widget';

export const ALERT_SAMPLING = 'ALERT_SAMPLING';
export const CONTENT = 'CONTENT';
export const MARKET_ABUSE = 'MARKET_ABUSE';
export const NON_MARKET_ABUSE = 'NON_MARKET_ABUSE';
export const SCENARIO = 'SCENARIO';
export const STATUS = 'STATUS';
export const RESOLUTION_CATEGORY = 'RESOLUTION_CATEGORY';
export const THREAD = 'THREAD';
export const WATCH = 'WATCH';
export const TRADES = 'TRADES';

// Alert statuses
export const ALERT_STATUS = 'alertStatus';
export const UNDER_INVESTIGATION = 'UNDER_INVESTIGATION';
export const RESOLVED = 'RESOLVED';
export const RESOLVED_WITH_BREACH = 'RESOLVED_WITH_BREACH';
export const RESOLVED_WITH_DISMISSAL = 'RESOLVED_WITH_DISMISSAL';
export const RESOLVED_WITH_INVESTIGATION = 'RESOLVED_WITH_INVESTIGATION';
export const RESOLVED_WITH_INVESTIGATION_WITH_BREACH = 'RESOLVED_WITH_INVESTIGATION_WITH_BREACH';
export const UNRESOLVED = 'UNRESOLVED';
export const IN_PROGRESS = 'IN_PROGRESS';
export const IN_REVIEW = 'IN_REVIEW';
export const ESCALATED = 'ESCALATED';
export const ASSIGNED_OTHER = 'ASSIGNED_OTHER';
export const ASSIGNED = 'ASSIGNED';
export const UNASSIGNED = 'UNASSIGNED';

// Risk Categories
export const COMMUNICATION_AND_MISREPRESENTATION = 'COMMUNICATION_AND_MISREPRESENTATION';
export const CONDUCT_RISK = 'CONDUCT_RISK';
export const CONFLICT_OF_INTEREST = 'CONFLICT_OF_INTEREST';
export const DATA_PRIVACY = 'DATA_PRIVACY';
export const INFORMATION_HANDLING = 'INFORMATION_HANDLING';
export const INSIDER_INFORMATION = 'INSIDER_INFORMATION';
export const MARKET_MANIPULATION = 'MARKET_MANIPULATION';
export const OPERATIONAL_AND_PROCEDURAL = 'OPERATIONAL_AND_PROCEDURAL';

// Records dropped reasons
const RECORDS_DROPPED = 'Records Dropped';
export const RECORDS_DROPPED_BEHAVIOUR_PERIOD = `${RECORDS_DROPPED} - Behaviour Period`;
export const RECORDS_DROPPED_CLIENT_ADV = `${RECORDS_DROPPED} - Client ADV`;
export const RECORDS_DROPPED_CREATE_ALERTS = `${RECORDS_DROPPED} - Create Alerts`;
export const RECORDS_DROPPED_FETCH_DATA = `${RECORDS_DROPPED} - Fetch Orders/OrderStates`;
export const RECORDS_DROPPED_FILTER_DATA = `${RECORDS_DROPPED} - Filter/Evaluate Data Based on Model`;
export const RECORDS_DROPPED_FILTER_MANDATORY_FIELDS = `${RECORDS_DROPPED} - Filter Data on Mandatory Fields`;
export const RECORDS_DROPPED_FILTER_MARKET_DATA = `${RECORDS_DROPPED} - Filter Market Data Based on Strategy`;
export const RECORDS_DROPPED_FILTER_TIMESTAMP = `${RECORDS_DROPPED} - Filter Data on Timestamp`;
export const RECORDS_DROPPED_FILTER_TRADING_HOURS = `${RECORDS_DROPPED} - Filter Trading Hours Data`;
export const RECORDS_DROPPED_MARKET_ADV = `${RECORDS_DROPPED} - Market ADV`;
export const RECORDS_DROPPED_MARKET_DATA = `${RECORDS_DROPPED} - Market Data`;
export const RECORDS_DROPPED_MARKET_DATA_EVENTS = `${RECORDS_DROPPED} - Market Data Events`;
export const RECORDS_DROPPED_MARKET_NEWS_EVENTS = `${RECORDS_DROPPED} - Market/News Events`;
export const RECORDS_DROPPED_MERGE_TENANT_MARKET = `${RECORDS_DROPPED}- Merge Tenant with Market Data`;
export const RECORDS_DROPPED_NEWS_EVENTS = `${RECORDS_DROPPED} - News Events`;
export const RECORDS_DROPPED_OBD_DATA = `${RECORDS_DROPPED} - OBD Data`;
export const RECORDS_DROPPED_OBSERVATION_PERIOD = `${RECORDS_DROPPED} - Observation Period`;
export const RECORDS_DROPPED_POTAM_CASES = `${RECORDS_DROPPED} - POTAM Cases`;
export const RECORDS_DROPPED_POTAM_WINDOWS = `${RECORDS_DROPPED} - Filter on POTAM Windows`;
export const RECORDS_DROPPED_PRICE_PERCENTAGE_MOVEMENT = `${RECORDS_DROPPED} - Price Percentage Movement`;
export const RECORDS_DROPPED_PROCESS_DATA = `${RECORDS_DROPPED} - Process Data Based on Model`;
export const RECORDS_DROPPED_SELERITY_ENTITY = `${RECORDS_DROPPED} - Selerity Entity`;
export const RECORDS_DROPPED_SELERITY_NEWS = `${RECORDS_DROPPED} - Selerity News`;
export const RECORDS_DROPPED_TENANT_DATA = `${RECORDS_DROPPED} - Tenant Data`;
export const RECORDS_DROPPED_TICK_DATA = `${RECORDS_DROPPED}- Tick Data`;
export const RECORDS_DROPPED_TIME_WINDOW_GROUPS = `${RECORDS_DROPPED} - Time Window Groups`;
export const RECORDS_DROPPED_TRADING_HOURS = `${RECORDS_DROPPED} - Trading Hours`;
export const RECORDS_DROPPED_VOLATILITY = `${RECORDS_DROPPED} - Volatility`;

export const TRADES_SURVEILLANCE_QUERY_TYPES = ['MARKET_ABUSE', 'TRADES', 'RESTRICTED_LIST'];

export const TRADES_SURVEILLANCE_QUERY_TYPES_ALL = ['MARKET_ABUSE', 'TRADES', 'RESTRICTED_LIST'];

export const COMMS_SURVEILLANCE_QUERY_TYPES_ALL = ['COMMUNICATIONS', 'ALERT_SAMPLING'];

export const ALERT_STATUS_MAP = {
  [RESOLVED]: [
    RESOLVED,
    RESOLVED_WITH_BREACH,
    RESOLVED_WITH_DISMISSAL,
    RESOLVED_WITH_INVESTIGATION,
    RESOLVED_WITH_INVESTIGATION_WITH_BREACH,
  ],
  [UNDER_INVESTIGATION]: [UNDER_INVESTIGATION, IN_PROGRESS, IN_REVIEW, ESCALATED],
  [UNRESOLVED]: [UNASSIGNED, ASSIGNED],
};

export const WORK_ASSIGNEE_TYPES = {
  myWork: 'myWork',
  otherPeopleWork: 'otherPeopleWork',
  unassigned: 'unassigned',
};

export const NEAR_MISS = 'Near Miss';
export const POLICY_BREACH = 'POLICY_BREACH';
export const TESTING = 'TESTING';
export const FALSE_POSITIVE = 'FALSE_POSITIVE';
export const MARKET_EVENT = 'Market Event';
export const ACCEPTABLE_INTERNAL_DISCUSSION = 'Acceptable Internal Discussion';
export const REGULATORY_BREACH = 'Regulatory Breach';
export const REFERRED_TO_ENFORCEMENT = 'Referred To Enforcement';
export const UNACCEPTABLE_EXTERNAL_DISCUSSION = 'Unacceptable External Discussion';
export const OTHER = 'Other';
export const UNACCEPTABLE_INTERNAL_DISCUSSION = 'Unacceptable Internal Discussion';
export const UNACCEPTABLE_PERSONAL_DISCUSSION = 'Unacceptable Personal Discussion';
export const COMMUNICATION_IS_A_TRADE_CONFIRM = 'Communication is a Trade Confirm';
export const COMMUNICATION_IS_A_NEWSLETTER = 'Communication is a Newsletter';
export const COMMUNICATION_IS_A_PRICING_RUN = 'Communication is a Pricing Run';
export const COMMUNICATION_IS_A_RESEARCH_NOTE = 'Communication is a Research Note';
export const COMMUNICATION_IS_A_SUPPORT_TICKET = 'Communication is a Support Ticket';
export const COMMUNICATION_IS_AN_OUT_OF_OFFICE = 'Communication is an Out of Office';
export const COMMUNICATION_IS_PART_OF_AN_ALREADY_REVIEWED_EMAIL_THREAD =
  'Communication is part of an already reviewed Email Thread';
export const COMMUNICATION_IS_NOT_DIRECTED_AT_DETECTED_EMPLOYEE =
  'Communication is not directed at detected Employee';
export const COMMUNICATION_IS_SPAM = 'Communication is Spam';
export const LEXICA_IS_PART_OF_A_DISCLAIMER = 'Lexica is part of a disclaimer';
export const LEXICA_REQUIRES_GREATER_CONTEXTUAL_SENSITIVITY =
  'Lexica requires greater contextual sensitivity';
export const EXCHANGE_BREACH = 'Exchange Breach';
export const ACCEPTABLE_PERSONAL_DISCUSSION = 'Acceptable Personal Discussion';
export const ACCEPTABLE_EXTERNAL_DISCUSSION = 'Acceptable External Discussion';

export const BREACH = 'BREACH';
export const NO_ACTION_REQUIRED = 'NO_ACTION_REQUIRED';
export const DATA_INTEGRITY = 'DATA_INTEGRITY';

export const INTERNAL_POLICY_BREACH = 'Internal Policy Breach';
export const MARKET_DATA = 'Market Data';
export const ORDER_TRADE_DATA = 'Order / Trade data';
export const THRESHOLDS_SET_TOO_LENIENT = 'Thresholds set too lenient';
export const CONSISTENT_WITH_NORMAL_TRADING_ACTIVITY = 'Consistent with normal trading activity';
export const NO_EVIDENCE_OF_IMPROPER_CONDUCT = 'No evidence of improper conduct';
export const ORDER_TRADE_VOLUME_NOT_MATERIAL = 'Order / Trade volume not material';
export const TEST_TRADES = 'Test Trades';
export const TESTING_NEW_ASSET_CLASS = 'Testing/Tuning new asset class';
export const TESTING_NEW_DATA_FEED = 'Testing/Tuning new data feed';
export const TESTING_NEW_ORDER_TRADE_FLOW = 'Testing/Tuning new order / trade flow';
export const TESTING_NEW_THRESHOLD_CONFIGURATION = 'Testing/Tuning new threshold configuration';
export const PRICE_MOVEMENT_IS_CONSISTENT_WITH_MARKET = 'Price move is consistent with the market';

export const NUMBER_DATA_TYPE = 'NUMBER';

export const SURVEILLANCE_COMMS_QUERY = 'surveillanceCommsQuery';
export const SURVEILLANCE_TRADES_QUERY = 'surveillanceTradesQuery';

export const WATCH_CREATE_EDIT_MESSAGES = defineMessages({
  createWatch: {
    defaultMessage: 'Create new Watch',
    id: 'module.surveillance.watch.new.create.title',
  },
  editWatch: {
    defaultMessage: 'Edit {watchName}',
    id: 'module.surveillance.watch.edit.watchName.title',
  },
});

export const ACTION_LABEL_MESSAGES = defineMessages({
  [modelsEnums.SURVEILLANCE_WATCH_ACTION_TYPE_CONTINUOUS]: {
    defaultMessage: 'Send a summary email every',
    id: 'surveillance.actionType.continuous.label',
  },
  [modelsEnums.SURVEILLANCE_WATCH_ACTION_TYPE_DAILY]: {
    defaultMessage: 'Send a daily summary email',
    id: 'surveillance.actionType.daily.label',
  },
});

export const ACTION_DESCRIPTION_MESSAGES = defineMessages({
  [modelsEnums.SURVEILLANCE_WATCH_ACTION_TYPE_DAILY]: {
    defaultMessage:
      'To avoid sending too many emails to the specified address, we recommend to keep this option enabled',
    id: 'surveillance.actionType.daily.description',
  },
});

export const CONDITION_LABEL_MESSAGES = defineMessages({
  [modelsEnums.SURVEILLANCE_WATCH_CONDITION_TYPE_ON_RUN]: {
    defaultMessage: 'Whenever it runs',
    id: 'surveillance.watchCondition.onRun.label',
  },
  [modelsEnums.SURVEILLANCE_WATCH_CONDITION_TYPE_ON_ALERT]: {
    defaultMessage: 'Only on alerts',
    id: 'surveillance.watchCondition.onAlert.label',
  },
});

export const HISTORIC_BACKTEST_OPTIONS_LABEL_MESSAGES = defineMessages({
  [modelsEnums.SURVEILLANCE_WATCH_BACKTEST_TYPE_FULL]: {
    defaultMessage: 'Yes (this will run the algorithm on all data in the platform)',
    id: 'surveillance.backtest.full.label',
  },
  [modelsEnums.SURVEILLANCE_WATCH_BACKTEST_TYPE_NONE]: {
    defaultMessage: 'No (the algorithm will run as per the schedule, going forwards)',
    id: 'surveillance.backtest.none.label',
  },
  [modelsEnums.SURVEILLANCE_WATCH_BACKTEST_TYPE_PARTIAL]: {
    defaultMessage: 'Partial backtest (run over a partial amount of historic data)',
    id: 'surveillance.backtest.partial.label',
  },
});

export const PARTIAL_BACKTEST_OPTIONS_LABEL_MESSAGES = defineMessages({
  [modelsEnums.SURVEILLANCE_WATCH_BACKTEST_PERIOD_TYPE_LAST_WEEK]: {
    defaultMessage:
      'Last week (the algorithms first execution will run over all data ingested in the last week)',
    id: 'surveillance.backtestPeriod.last_week.label',
  },
  [modelsEnums.SURVEILLANCE_WATCH_BACKTEST_PERIOD_TYPE_LAST_MONTH]: {
    defaultMessage:
      'Last month (the algorithms first execution will run over all data ingested in the last month)',
    id: 'surveillance.backtestPeriod.last_month.label',
  },
  [modelsEnums.SURVEILLANCE_WATCH_BACKTEST_PERIOD_TYPE_LAST_THREE_MONTHS]: {
    defaultMessage:
      'Last 3 months (the algorithms first execution will run over all data ingested in the last 3 months)',
    id: 'surveillance.backtestPeriod.last_three_months.label',
  },
});

export const CONDITION_OPTIONS = [
  {
    label: CONDITION_LABEL_MESSAGES[modelsEnums.SURVEILLANCE_WATCH_CONDITION_TYPE_ON_RUN],
    value: modelsEnums.SURVEILLANCE_WATCH_CONDITION_TYPE_ON_RUN,
  },
  {
    label: CONDITION_LABEL_MESSAGES[modelsEnums.SURVEILLANCE_WATCH_CONDITION_TYPE_ON_ALERT],
    value: modelsEnums.SURVEILLANCE_WATCH_CONDITION_TYPE_ON_ALERT,
  },
];

export const HISTORIC_BACKTEST_OPTIONS = [
  {
    label:
      HISTORIC_BACKTEST_OPTIONS_LABEL_MESSAGES[modelsEnums.SURVEILLANCE_WATCH_BACKTEST_TYPE_FULL],
    value: modelsEnums.SURVEILLANCE_WATCH_BACKTEST_TYPE_FULL,
  },
  {
    label:
      HISTORIC_BACKTEST_OPTIONS_LABEL_MESSAGES[modelsEnums.SURVEILLANCE_WATCH_BACKTEST_TYPE_NONE],
    value: modelsEnums.SURVEILLANCE_WATCH_BACKTEST_TYPE_NONE,
  },
  {
    label:
      HISTORIC_BACKTEST_OPTIONS_LABEL_MESSAGES[
        modelsEnums.SURVEILLANCE_WATCH_BACKTEST_TYPE_PARTIAL
      ],
    value: modelsEnums.SURVEILLANCE_WATCH_BACKTEST_TYPE_PARTIAL,
  },
];

export const PARTIAL_BACKTEST_OPTIONS = [
  {
    label:
      PARTIAL_BACKTEST_OPTIONS_LABEL_MESSAGES[
        modelsEnums.SURVEILLANCE_WATCH_BACKTEST_PERIOD_TYPE_LAST_WEEK
      ],
    value: modelsEnums.SURVEILLANCE_WATCH_BACKTEST_PERIOD_TYPE_LAST_WEEK,
  },
  {
    label:
      PARTIAL_BACKTEST_OPTIONS_LABEL_MESSAGES[
        modelsEnums.SURVEILLANCE_WATCH_BACKTEST_PERIOD_TYPE_LAST_MONTH
      ],
    value: modelsEnums.SURVEILLANCE_WATCH_BACKTEST_PERIOD_TYPE_LAST_MONTH,
  },
  {
    label:
      PARTIAL_BACKTEST_OPTIONS_LABEL_MESSAGES[
        modelsEnums.SURVEILLANCE_WATCH_BACKTEST_PERIOD_TYPE_LAST_THREE_MONTHS
      ],
    value: modelsEnums.SURVEILLANCE_WATCH_BACKTEST_PERIOD_TYPE_LAST_THREE_MONTHS,
  },
];

export const FREQUENCY_DESCRIPTION_MESSAGES = defineMessages({
  [modelsEnums.SURVEILLANCE_WATCH_FREQUENCY_TYPE_CONTINUOUS]: {
    defaultMessage: 'For urgent analysis that require constant supervision',
    id: 'surveillance.frequencyType.continuous.description',
  },
  [modelsEnums.SURVEILLANCE_WATCH_FREQUENCY_TYPE_DAILY]: {
    defaultMessage: 'Recommended for end-of-day risk mitigation',
    id: 'surveillance.frequencyType.daily.description',
  },
});

export const FREQUENCY_LABEL_MESSAGES = defineMessages({
  [modelsEnums.SURVEILLANCE_WATCH_FREQUENCY_TYPE_CONTINUOUS]: {
    defaultMessage: 'Continuous Real Time',
    id: 'surveillance.frequencyType.continuous.label',
  },
  [modelsEnums.SURVEILLANCE_WATCH_FREQUENCY_TYPE_DAILY]: {
    defaultMessage: 'Daily Overnight',
    id: 'surveillance.frequencyType.daily.label',
  },
});

export const QUERY_PREVIEW_MESSAGES = defineMessages({
  confirmBackMessage: {
    defaultMessage: 'Are you sure you want to exit the query? Your changes will not be saved',
    id: 'surveillance.queryPreview.confirmBack',
  },
  noFiltersSelected: {
    defaultMessage: 'No Filters Selected',
    id: 'surveillance.queryPreview.noFiltersSelected',
  },
});

export const ALERTS_GROUP_BY_DESCRIPTION_MESSAGES = defineMessages({
  [SCENARIO]: {
    defaultMessage:
      'Grouping by scenario triages your surveillance alert by the relevant Market Abuse Scenario',
    id: 'module.alerts.groupBy.scenario.description',
  },
  [STATUS]: {
    defaultMessage:
      "Grouping by status triages your surveillance alert by whether they're unresolved, under investigation or resolved",
    id: 'module.alerts.groupBy.status.description',
  },
  [THREAD]: {
    defaultMessage:
      'Grouping by thread shows all alerts grouped by the communication content.{break} (e.g., for a BCC message to 6 people, you would see it grouped by the content of the single message)',
    id: 'module.alerts.groupBy.thread.description',
  },
  [WATCH]: {
    defaultMessage: 'Grouping by watch shows all alerts grouped by the created watch',
    id: 'module.alerts.groupBy.watch.description',
  },
});

export const BUTTON_MESSAGES = defineMessages({
  addNew: {
    defaultMessage: 'Add New',
    id: 'module.button.addNew.label',
  },
  audit: {
    defaultMessage: 'Audit',
    id: 'module.button.audit.label',
  },
  [CONTENT]: {
    defaultMessage: 'Content',
    id: 'module.surveillance.button.content',
  },
  editLexicon: {
    defaultMessage: 'Edit Lexicons',
    id: 'module.button.editLexicon.label',
  },
  editQuery: {
    defaultMessage: 'Edit Query',
    id: 'module.surveillance.watches.button.editQuery',
  },
  editRestrictedList: {
    defaultMessage: 'Edit Restricted List',
    id: 'module.button.editRestrictedList.label',
  },
  editSchedule: {
    defaultMessage: 'Edit Schedule',
    id: 'module.surveillance.watches.button.editSchedule',
  },
  editThresholds: {
    defaultMessage: 'Edit Thresholds',
    id: 'module.surveillance.watches.button.editThresholds',
  },
  [MARKET_ABUSE]: {
    defaultMessage: 'Market Abuse',
    id: 'module.surveillance.button.marketAbuse',
  },
  [NON_MARKET_ABUSE]: {
    defaultMessage: 'Non-Market Abuse',
    id: 'module.surveillance.button.nonMarketAbuse',
  },
  [SCENARIO]: {
    defaultMessage: 'Market Abuse Scenarios',
    id: 'module.surveillance.button.scenario',
  },
  [STATUS]: {
    defaultMessage: 'Status',
    id: 'module.surveillance.button.status',
  },
  stemming: {
    defaultMessage: 'Use Stemming?',
    id: 'module.button.stemming.label',
  },
  [THREAD]: {
    defaultMessage: 'Thread',
    id: 'module.surveillance.button.thread',
  },
  unsurveilledOrders: {
    defaultMessage: 'Unsurveilled Orders',
    id: 'module.button.unsurveilledOrders.label',
  },
  [WATCH]: {
    defaultMessage: 'Watches',
    id: 'module.surveillance.button.watch',
  },
});

export const ALERT_ACTION_BUTTON_MESSAGES = defineMessages({
  addToCase: {
    defaultMessage: 'Add to case',
    id: 'module.surveillance.alerts.button.addToCase',
  },
  asssignAlert: {
    defaultMessage: 'Assign',
    id: 'module.surveillance.alerts.button.asssignAlert',
  },
  dismiss: {
    defaultMessage: 'Dismiss',
    id: 'module.surveillance.alerts.button.dismiss',
  },
  reasssignAlert: {
    defaultMessage: 'Reassign',
    id: 'module.surveillance.alerts.button.reasssignAlert',
  },
  resolve: {
    defaultMessage: 'Resolve',
    id: 'module.surveillance.alerts.button.resolve',
  },
  resolveAll: {
    defaultMessage: 'Resolve all',
    id: 'module.surveillance.alerts.button.resolveAll',
  },
  restore: {
    defaultMessage: 'Restore',
    id: 'module.surveillance.alerts.button.restore',
  },
  unresolve: {
    defaultMessage: 'Unresolve',
    id: 'module.surveillance.alerts.button.unresolve',
  },
});

export const TIME_ZONES_MAP = TIME_ZONES.map(tz => ({ label: tz, value: tz }));

export const generateAlertHourIntervals = () => {
  const intervals = [];
  for (let i = 0; i <= 23; i += 1) {
    intervals.push({
      label: `${i} Hour${i !== 1 ? 's' : ''}`,
      value: i,
    });
  }
  return intervals;
};

export const ALERT_MINUTE_INTERVALS = [
  {
    label: '0 Minutes',
    value: 0,
  },
  {
    label: '5 Minutes',
    value: 5,
  },
  {
    label: '15 Minutes',
    value: 15,
  },
  {
    label: '30 Minutes',
    value: 30,
  },
  {
    label: '45 Minutes',
    value: 45,
  },
];

export const EMAIL_INTERVALS = [
  {
    label: '4 Hours',
    sortKey: '04',
    value: '4',
  },
  {
    label: '8 Hours',
    sortKey: '08',
    value: '8',
  },
  {
    label: '12 hours',
    sortKey: '12',
    value: '12',
  },
  {
    label: '24 hours',
    sortKey: '24',
    value: '24',
  },
  {
    label: '48 hours',
    sortKey: '48',
    value: '48',
  },
];

const TRANSACTION_VALUE_MESSAGE_WITH_CURRENCY = CURRENCIES_STRING_ARRAY.reduce((acc, cur) => {
  acc = {
    ...acc,
    [`bestExecutionData.transactionValue.ecbRefRate.${cur}`]:
      propertyConstants.PROPERTY_MESSAGES.transactionValue,
  };
  return acc;
}, {});

const TRANSACTION_VOLUME_MESSAGE_WITH_CURRENCY = CURRENCIES_STRING_ARRAY.reduce((acc, cur) => {
  acc = {
    ...acc,
    [`bestExecutionData.transactionVolume.ecbRefRate.${cur}`]:
      propertyConstants.PROPERTY_MESSAGES.transactionVolume,
  };
  return acc;
}, {});

export const FIELD_DISPLAY_MESSAGES = {
  ...TRANSACTION_VALUE_MESSAGE_WITH_CURRENCY,
  ...TRANSACTION_VOLUME_MESSAGE_WITH_CURRENCY,
  [modelsEnums.ENTITY_TYPE_ACCOUNT_PERSON]: propertyConstants.PROPERTY_MESSAGES.employee,
  [modelsEnums.ENTITY_TYPE_MARKET_PERSON]: propertyConstants.PROPERTY_MESSAGES.client,
  [modelsEnums.ENTITY_TYPE_MARKET_COUNTERPARTY]: propertyConstants.PROPERTY_MESSAGES.firm,
  [builderFields.IDENTIFIERS_FROM_ID_FIELD]: propertyConstants.PROPERTY_MESSAGES.fromId,
  [builderFields.IDENTIFIERS_BEHALF_OF_FIELD]: propertyConstants.PROPERTY_MESSAGES.onBehalfOf,
  [builderFields.IDENTIFIERS_TO_IDS_FIELD]: propertyConstants.PROPERTY_MESSAGES.toIds,
  [builderFields.IDENTIFIERS_CC_IDS_FIELD]: propertyConstants.PROPERTY_MESSAGES.ccIds,
  [builderFields.IDENTIFIERS_BCC_IDS_FIELD]: propertyConstants.PROPERTY_MESSAGES.bccIds,
  [builderFields.IDENTIFIERS_HOST_ID_FIELD]: propertyConstants.PROPERTY_MESSAGES.hostId,
  [builderFields.IDENTIFIERS_ATTENDEE_IDS_FIELD]: propertyConstants.PROPERTY_MESSAGES.attendeeIds,
  [builderFields.ID_FIELD]: propertyConstants.PROPERTY_MESSAGES.id,
  [builderFields.ORDER_DATE_FIELD]: propertyConstants.PROPERTY_MESSAGES.orderTime,
  [builderFields.TRANSACTION_DATE_FIELD]: propertyConstants.PROPERTY_MESSAGES.tradingDateTime,
  'bestExecutionData.transactionValue.ecbRefRate':
    propertyConstants.PROPERTY_MESSAGES.transactionValueCustomCurrency,
  'bestExecutionData.transactionVolume.ecbRefRate':
    propertyConstants.PROPERTY_MESSAGES.transactionVolumeCustomCurrency,
  Both: propertyConstants.PROPERTY_MESSAGES.both,
  'Both.location.homeAddress.city': propertyConstants.PROPERTY_MESSAGES.bothHomeAddressCity,
  'Both.location.homeAddress.country': propertyConstants.PROPERTY_MESSAGES.bothHomeAddressCountry,
  'Both.location.officeAddress.city': propertyConstants.PROPERTY_MESSAGES.bothOfficeAddressCity,
  'Both.location.officeAddress.country':
    propertyConstants.PROPERTY_MESSAGES.bothOfficeAddressCountry,
  'Both.officialIdentifiers.branchCountry': propertyConstants.PROPERTY_MESSAGES.bothBranchCountry,
  'Both.officialIdentifiers.clientMandate': propertyConstants.PROPERTY_MESSAGES.bothClientMandate,
  'Both.officialIdentifiers.employeeId': propertyConstants.PROPERTY_MESSAGES.bothEmployeeId,
  'Both.officialIdentifiers.traderIds.label': propertyConstants.PROPERTY_MESSAGES.bothTraderId,
  'Both.personalDetails.nationality': propertyConstants.PROPERTY_MESSAGES.bothNationality,
  'Both.structure.instruments.ableToTradeBestExAssetClass':
    propertyConstants.PROPERTY_MESSAGES.bothAbleToTradeBestExAssetClass,
  'Both.structure.isDecisionMaker': propertyConstants.PROPERTY_MESSAGES.bothIsDecisionMaker,
  'Both.structure.role': propertyConstants.PROPERTY_MESSAGES.bothRole,
  'Both.structure.smcr.functionCategory':
    propertyConstants.PROPERTY_MESSAGES.bothSmcrFunctionCategory,
  'Both.structure.smcr.functions.function': propertyConstants.PROPERTY_MESSAGES.bothSmcrFunction,
  'Both.structure.smcr.reviewDate': propertyConstants.PROPERTY_MESSAGES.bothSmcrReviewDate,
  'Both.structure.type': propertyConstants.PROPERTY_MESSAGES.bothType,
  dataSourceName: propertyConstants.PROPERTY_MESSAGES.dataSource,
  'details.clientMandate': propertyConstants.PROPERTY_MESSAGES.clientMandate,
  'details.firmType': propertyConstants.PROPERTY_MESSAGES.firmType,
  'details.leiRegistrationStatus': propertyConstants.PROPERTY_MESSAGES.leiRegistrationStatus,
  'details.retailOrProfessional': propertyConstants.PROPERTY_MESSAGES.retailOrProfessional,
  'executionDetails.limitPrice': propertyConstants.PROPERTY_MESSAGES.limitPrice,
  'executionDetails.orderStatus': propertyConstants.PROPERTY_MESSAGES.orderStatus,
  'executionDetails.orderType': propertyConstants.PROPERTY_MESSAGES.orderType,
  'executionDetails.passiveAggressiveIndicator':
    propertyConstants.PROPERTY_MESSAGES.passiveOrAggressive,
  'executionDetails.stopPrice': propertyConstants.PROPERTY_MESSAGES.stopPrice,
  'executionDetails.validityPeriod': propertyConstants.PROPERTY_MESSAGES.validityPeriod,
  fileNames: propertyConstants.PROPERTY_MESSAGES.fileNames,
  fileTypes: propertyConstants.PROPERTY_MESSAGES.fileTypes,
  'firmIdentifiers.branchCountry': propertyConstants.PROPERTY_MESSAGES.branchCountry,
  'firmIdentifiers.kycApproved': propertyConstants.PROPERTY_MESSAGES.kycApproved,
  'firmLocation.registeredAddress.address': propertyConstants.PROPERTY_MESSAGES.registeredAddress,
  'firmLocation.registeredAddress.city': propertyConstants.PROPERTY_MESSAGES.registeredAddressCity,
  'firmLocation.registeredAddress.country':
    propertyConstants.PROPERTY_MESSAGES.registeredAddressCountry,
  'firmLocation.registeredAddress.postalCode':
    propertyConstants.PROPERTY_MESSAGES.registeredAddressPostalCode,
  'firmLocation.tradingAddress.address': propertyConstants.PROPERTY_MESSAGES.tradingAddress,
  'firmLocation.tradingAddress.city': propertyConstants.PROPERTY_MESSAGES.tradingAddressCity,
  'firmLocation.tradingAddress.country': propertyConstants.PROPERTY_MESSAGES.tradingAddressCountry,
  'firmLocation.tradingAddress.postalCode':
    propertyConstants.PROPERTY_MESSAGES.tradingAddressPostalCode,
  'identifiers.domains.value': propertyConstants.PROPERTY_MESSAGES.domains,
  'instrumentDetails.instrument.bond.debtSeniority':
    propertyConstants.PROPERTY_MESSAGES.debtSeniority,
  'instrumentDetails.instrument.bond.fixedRate': propertyConstants.PROPERTY_MESSAGES.fixedRate,
  'instrumentDetails.instrument.bond.maturityDate':
    propertyConstants.PROPERTY_MESSAGES.maturityDate,
  'instrumentDetails.instrument.cfiCategory': propertyConstants.PROPERTY_MESSAGES.cfiCategory,
  'instrumentDetails.instrument.cfiGroup': propertyConstants.PROPERTY_MESSAGES.cfiGroup,
  'instrumentDetails.instrument.commodityAndEmissionAllowances.baseProduct':
    propertyConstants.PROPERTY_MESSAGES.baseProduct,
  'instrumentDetails.instrument.commodityAndEmissionAllowances.furtherSubProduct':
    propertyConstants.PROPERTY_MESSAGES.furtherSubProduct,
  'instrumentDetails.instrument.commodityAndEmissionAllowances.subProduct':
    propertyConstants.PROPERTY_MESSAGES.subProduct,
  'instrumentDetails.instrument.derivative.deliveryType':
    propertyConstants.PROPERTY_MESSAGES.deliveryType,
  'instrumentDetails.instrument.derivative.expiryDate':
    propertyConstants.PROPERTY_MESSAGES.expiryDate,
  'instrumentDetails.instrument.derivative.optionExerciseStyle':
    propertyConstants.PROPERTY_MESSAGES.optionExerciseStyle,
  'instrumentDetails.instrument.derivative.optionType':
    propertyConstants.PROPERTY_MESSAGES.optionType,
  'instrumentDetails.instrument.derivative.priceMultiplier':
    propertyConstants.PROPERTY_MESSAGES.priceMultiplier,
  'instrumentDetails.instrument.derivative.strikePrice':
    propertyConstants.PROPERTY_MESSAGES.strikePrice,
  'instrumentDetails.instrument.derivative.underlyingIndexName':
    propertyConstants.PROPERTY_MESSAGES.underlyingIndexName,
  'instrumentDetails.instrument.derivative.underlyingIndexSeries':
    propertyConstants.PROPERTY_MESSAGES.underlyingIndexSeries,
  'instrumentDetails.instrument.derivative.underlyingIndexTerm':
    propertyConstants.PROPERTY_MESSAGES.underlyingIndexTerm,
  'instrumentDetails.instrument.derivative.underlyingIndexTermValue':
    propertyConstants.PROPERTY_MESSAGES.underlyingIndexTermValue,
  'instrumentDetails.instrument.derivative.underlyingIndexVersion':
    propertyConstants.PROPERTY_MESSAGES.underlyingIndexVersion,
  'instrumentDetails.instrument.ext.alternativeInstrumentIdentifier':
    propertyConstants.PROPERTY_MESSAGES.alternativeInstrumentIdentifier,
  'instrumentDetails.instrument.ext.bestExAssetClassMain':
    propertyConstants.PROPERTY_MESSAGES.bestExAssetClassMain,
  'instrumentDetails.instrument.ext.bestExAssetClassSub':
    propertyConstants.PROPERTY_MESSAGES.bestExAssetClassSub,
  'instrumentDetails.instrument.ext.instrumentUniqueIdentifier':
    propertyConstants.PROPERTY_MESSAGES.instrumentUniqueIdentifier,
  'instrumentDetails.instrument.ext.mifirEligible':
    propertyConstants.PROPERTY_MESSAGES.mifirEligible,
  'instrumentDetails.instrument.ext.underlyingIndexId':
    propertyConstants.PROPERTY_MESSAGES.underlyingIndexId,
  'instrumentDetails.instrument.fxDerivatives.fxType': propertyConstants.PROPERTY_MESSAGES.fxType,
  'instrumentDetails.instrument.fxDerivatives.notionalCurrency2':
    propertyConstants.PROPERTY_MESSAGES.notionalCurrency2,
  'instrumentDetails.instrument.instrumentClassification':
    propertyConstants.PROPERTY_MESSAGES.instrumentClassificationCFI,
  'instrumentDetails.instrument.instrumentFullName':
    propertyConstants.PROPERTY_MESSAGES.instrumentName,
  'instrumentDetails.instrument.instrumentIdCode':
    propertyConstants.PROPERTY_MESSAGES.instrumentIdCode,
  'instrumentDetails.instrument.interestRateDerivatives.irContractTerm':
    propertyConstants.PROPERTY_MESSAGES.irContractTerm,
  'instrumentDetails.instrument.interestRateDerivatives.leg1FixedRate':
    propertyConstants.PROPERTY_MESSAGES.leg1FixedRate,
  'instrumentDetails.instrument.interestRateDerivatives.leg2FixedRate':
    propertyConstants.PROPERTY_MESSAGES.leg2FixedRate,
  'instrumentDetails.instrument.interestRateDerivatives.leg2FloatingRate':
    propertyConstants.PROPERTY_MESSAGES.leg2FloatingRate,
  'location.homeAddress.city': propertyConstants.PROPERTY_MESSAGES.homeAddressCity,
  'location.homeAddress.country': propertyConstants.PROPERTY_MESSAGES.homeAddressCountry,
  'location.officeAddress.city': propertyConstants.PROPERTY_MESSAGES.officeAddressCity,
  'location.officeAddress.country': propertyConstants.PROPERTY_MESSAGES.officeAddressCountry,
  name: propertyConstants.PROPERTY_MESSAGES.name,
  'officialIdentifiers.clientMandate': propertyConstants.PROPERTY_MESSAGES.clientMandate,
  'officialIdentifiers.branchCountry':
    propertyConstants.PROPERTY_MESSAGES['officialIdentifiers.branchCountry'],
  'officialIdentifiers.employeeId': propertyConstants.PROPERTY_MESSAGES.employeeId,
  'officialIdentifiers.traderIds.label': propertyConstants.PROPERTY_MESSAGES.traderId,
  participants: propertyConstants.PROPERTY_MESSAGES.participants,
  'personalDetails.nationality': propertyConstants.PROPERTY_MESSAGES.nationality,
  'priceFormingData.initialQuantity': propertyConstants.PROPERTY_MESSAGES.initialQuantity,
  sizeInBytes: propertyConstants.PROPERTY_MESSAGES.fileSize,
  'structure.client.accountCurrency': propertyConstants.PROPERTY_MESSAGES.clientAccountCurrency,
  'structure.client.assetsCategory': propertyConstants.PROPERTY_MESSAGES.clientAssetCategory,
  'structure.client.dateAccountClosed': propertyConstants.PROPERTY_MESSAGES.clientAccountClosedDate,
  'structure.client.dateAccountOpened': propertyConstants.PROPERTY_MESSAGES.clientAccountOpenedDate,
  'structure.client.riskScore': propertyConstants.PROPERTY_MESSAGES.clientRiskScore,
  'structure.client.status': propertyConstants.PROPERTY_MESSAGES.clientStatus,
  'structure.department': propertyConstants.PROPERTY_MESSAGES.department,
  'structure.instruments.ableToTradeBestExAssetClass':
    propertyConstants.PROPERTY_MESSAGES.ableToTradeBestExAssetClass,
  'structure.isDecisionMaker': propertyConstants.PROPERTY_MESSAGES.isDecisionMaker,
  'structure.role': propertyConstants.PROPERTY_MESSAGES.role,
  'structure.smcr.functionCategory': propertyConstants.PROPERTY_MESSAGES.smcrFunctionCategory,
  'structure.smcr.functions.function': propertyConstants.PROPERTY_MESSAGES.smcrFunction,
  'structure.smcr.reviewDate': propertyConstants.PROPERTY_MESSAGES.smcrReviewDate,
  'structure.type': propertyConstants.PROPERTY_MESSAGES.type,
  'timestamps.timestampStart': {
    defaultMessage: 'time',
    id: 'property.timestampStart',
  },
  'transactionDetails.basketId': propertyConstants.PROPERTY_MESSAGES.basketId,
  'transactionDetails.buySellIndicator': propertyConstants.PROPERTY_MESSAGES.buySellIndicator,
  'transactionDetails.positionEffect': propertyConstants.PROPERTY_MESSAGES.positionEffect,
  'transactionDetails.priceCurrency': propertyConstants.PROPERTY_MESSAGES.priceCurrency,
  'transactionDetails.quantity': propertyConstants.PROPERTY_MESSAGES.quantity,
  'transactionDetails.recordType': propertyConstants.PROPERTY_MESSAGES.recordType,
  'transactionDetails.shortSellingIndicator':
    propertyConstants.PROPERTY_MESSAGES.shortSellingIndicator,
  'transactionDetails.tradingCapacity': propertyConstants.PROPERTY_MESSAGES.tradingCapacity,
  'transactionDetails.ultimateVenue': propertyConstants.PROPERTY_MESSAGES.ultimateVenue,
  'transactionDetails.venue': propertyConstants.PROPERTY_MESSAGES.venue,
};

export const HUMAN_FORMAT_OPTIONS = {
  maxDecimals: 1,
  separator: '',
};

export const ALERT_STATUS_BUCKET_KEYS = [
  modelsEnums.SURVEILLANCE_ALERT_HIT_STATUS_UNRESOLVED,
  modelsEnums.SURVEILLANCE_ALERT_HIT_STATUS_UNDER_INVESTIGATION,
  modelsEnums.SURVEILLANCE_ALERT_HIT_STATUS_RESOLVED,
];

export const COLOUR_TYPE_BUCKETS = {
  [modelsEnums.SURVEILLANCE_ALERT_HIT_STATUS_UNRESOLVED]: widgetConstants.RED_AGG_BUCKET_NAME,
  [modelsEnums.SURVEILLANCE_ALERT_HIT_STATUS_UNDER_INVESTIGATION]:
    widgetConstants.YELLOW_AGG_BUCKET_NAME,
  [modelsEnums.SURVEILLANCE_ALERT_HIT_STATUS_RESOLVED]: widgetConstants.GREEN_AGG_BUCKET_NAME,
};

export const getTreeCategoryData = () => {
  const root = { children: [], id: 0, name: 'root' };
  const catCount = API.sdp.lexicon.reduce((acc, curr) => {
    const { category } = curr;
    if (acc[category]) return { ...acc, [category]: acc[category] + 1 };
    return { ...acc, [category]: 1 };
  }, {});

  const children = sortBy(
    Object.keys(catCount).map((c, i) => ({
      children: [{ name: 'leaf' }],
      count: catCount[c],
      field: c,
      id: i + 1,
      name: c,
    })),
    ['name']
  );

  return { ...root, children };
};

export const LEXICON_FIELDS = [
  builderFields.COMMS_BODY_TEXT_FIELD_ENGLISH,
  builderFields.COMMS_BODY_TEXT_FIELD_ENGLISH_STEMMED,
  builderFields.COMMS_SUBJECT_FIELD_ENGLISH,
  builderFields.COMMS_SUBJECT_FIELD_ENGLISH_STEMMED,
  'unknown.content',
];

export const SURVEILLANCE_ALERT_HIT_STATUS_RESOLVED_TYPES = [
  modelsEnums.SURVEILLANCE_ALERT_HIT_STATUS_RESOLVED,
  modelsEnums.SURVEILLANCE_ALERT_HIT_STATUS_RESOLVED_WITH_BREACH,
  modelsEnums.SURVEILLANCE_ALERT_HIT_STATUS_RESOLVED_WITH_INVESTIGATION,
  modelsEnums.SURVEILLANCE_ALERT_HIT_STATUS_RESOLVED_WITH_DISMISSAL,
];

export const MODAL_TITLE_MESSAGES = defineMessages({
  resolveAlerts: {
    defaultMessage: 'Resolve Alert',
    id: 'surveillance.resolveAlert.modal.title',
  },
});

export const TIME_QUERY_SECTION_MESSAGES = defineMessages({
  occuring: {
    defaultMessage: 'Communications occuring on',
    id: 'surveillance.timeQuery.page.occuring',
  },
  subTitle: {
    defaultMessage: 'Add time restrictions to your watch..',
    id: 'surveillance.timeQuery.page.subtitle',
  },
  subTitleUTC: {
    defaultMessage:
      'All timestamp fields in SteelEye are stored in UTC format, accordingly this filter references UTC in the time window available for selection',
    id: 'surviellance.timeQuery.page.subtitle.utc',
  },
  title: {
    defaultMessage: 'Time',
    id: 'surveillance.timeQuery.page.title',
  },
});

export const CLASSIFIER_QUERY_SECTION_MESSAGES = defineMessages({
  above: {
    defaultMessage: 'Above',
    id: 'modules.commsSurveillance.classifier.above',
  },
  after: {
    defaultMessage: 'After',
    id: 'commsSurveillance.classifier.after',
  },
  afterAlertsPredicted: {
    defaultMessage:
      '{historicalCount} historic comms detected, {alertsCount} alerts predicted this week',
    id: 'modules.tradesSurveillance.query.backtest.afterAlertsPredicted',
  },
  alertsPredicted: {
    defaultMessage: 'Alerts Predicted:',
    id: 'modules.tradesSurveillance.query.backtest.alertsPredicted',
  },
  before: {
    defaultMessage: 'Before',
    id: 'commsSurveillance.classifier.before',
  },
  beforeAlertsPredicted: {
    defaultMessage:
      '{historicalCount} historic comms detected, {alertsCount} alerts predicted this week',
    id: 'modules.tradesSurveillance.query.backtest.beforeAlertsPredicted',
  },
  confidence: {
    defaultMessage: 'Confidence',
    id: 'modules.commsSurveillance.classifier.confidence',
  },
  confidenceScoreFilterContent: {
    defaultMessage: 'Confidence Score {percent} %',
    id: 'modules.commsSurveillance.classifier.confidence.filter.title',
  },
  excludeFilterTitle: {
    defaultMessage: '{count} Exclusions',
    id: 'modules.commsSurveillance.classifier.exclude.filter.title',
  },
  excludeInfo: {
    defaultMessage:
      'Your communications are classified with a category (such as marketing, sales, pricing runs etc...)',
    id: 'modules.commsSurveillance.classifier.excludeInfo',
  },
  excludeInfoSelection: {
    defaultMessage: 'Select any of the categories below that you wish to exclude from your watch.',
    id: 'modules.commsSurveillance.classifier.excludeInfo.selection',
  },
  historicalCountComms: {
    defaultMessage: 'Historic Comms Detected',
    id: 'modules.tradesSurveillance.query.backtest.historicalCount.comms',
  },
  historicalCountTrades: {
    defaultMessage: 'Historic Trades Detected',
    id: 'modules.tradesSurveillance.query.backtest.historicalCount.trades',
  },
  loading: {
    defaultMessage: 'Loading…',
    id: 'modules.commsSurveillance.classifier.loading',
  },
  modificationImpact: {
    defaultMessage: 'Modification Impact',
    id: 'modules.commsSurveillance.classifier.modificationImpact',
  },
  noCategories: {
    defaultMessage: 'No categories',
    id: 'modules.commsSurveillance.classifier.noCategories',
  },
  perWeek: {
    defaultMessage: '{count} per week',
    id: 'modules.tradesSurveillance.query.backtest.perWeek',
  },
  predictionInfo: {
    defaultMessage: 'Every prediction made by ML comes with an associated confidence score.',
    id: 'modules.commsSurveillance.classifier.predictionInfo',
  },
  predictionScoreInfo: {
    defaultMessage:
      'The higher the score, the more confident the algorithm is of an accurate prediction.',
    id: 'modules.commsSurveillance.classifier.predictionInfo.score',
  },
  predictionTolerance: {
    defaultMessage: 'Choose your tolerance below.',
    id: 'modules.commsSurveillance.classifier.predictionInfo.tolerance',
  },
  subTitle: {
    defaultMessage: 'Predicitive classification of all emails',
    id: 'surveillance.classifierQuery.page.subtitle',
  },
  title: {
    defaultMessage: 'Classifier',
    id: 'surveillance.classifierQuery.page.title',
  },
});

export const EMAIL_ZONING_QUERY_SECTION_MESSAGES = defineMessages({
  authoredContent: {
    defaultMessage: 'Authored Content',
    id: 'modules.commsSurveillance.emailZoning.list.helpText.authoredContent',
  },
  previouslyAuthoredContent: {
    defaultMessage: 'Previously Authored Content',
    id: 'modules.commsSurveillance.emailZoning.list.helpText.previouslyAuthoredContent',
  },
  subTitle: {
    defaultMessage: '...zoning',
    id: 'surveillance.zoningQuery.page.subtitle',
  },
  title: {
    defaultMessage: 'Email Zoning',
    id: 'surveillance.zoningQuery.page.title',
  },
  traditionalEmailFormat: {
    defaultMessage: 'Traditional Email Format',
    id: 'modules.commsSurveillance.emailZoning.list.helpText.traditionalEmailFormat',
  },
  zoningExcludeFilterTitle: {
    defaultMessage: '{count} Zoning Exclusions',
    id: 'modules.commsSurveillance.emailZoning.exclude.filter.title',
  },
  zoningInfo: {
    defaultMessage:
      "SteelEye 'Zones' your emails to differentiate things such as salutations, signatures, disclaimers etc...",
    id: 'modules.commsSurveillance.emailZoning.zoningInfo',
  },
  zoningInfoSelection: {
    defaultMessage: "Select any of the 'Zones' below that you wish to exclude from your watch.",
    id: 'modules.commsSurveillance.emailZoning.zoningInfoSelection',
  },
});

export const EMAIL_ZONING_LIST_MESSAGES = defineMessages({
  attachment: {
    defaultMessage: 'Attachment',
    id: 'modules.commsSurveillance.emailClassifier.list.attachment',
  },
  body: {
    defaultMessage: 'Body',
    id: 'modules.commsSurveillance.emailClassifier.list.body',
  },
  disclaimer: {
    defaultMessage: 'Disclaimer',
    id: 'modules.commsSurveillance.emailClassifier.list.disclaimer',
  },
  intro: {
    defaultMessage: 'Intro',
    id: 'modules.commsSurveillance.emailClassifier.list.intro',
  },
  outro: {
    defaultMessage: 'Outro',
    id: 'modules.commsSurveillance.emailClassifier.list.outro',
  },
  remainder: {
    defaultMessage: 'Remainder of thread',
    id: 'modules.commsSurveillance.emailClassifier.list.remainder',
  },
  signature: {
    defaultMessage: 'Signature',
    id: 'modules.commsSurveillance.emailClassifier.list.signature',
  },
});

export const EMAIL_ZONING_LIST = {
  authoredContent: [
    { classification: 'intro', title: EMAIL_ZONING_LIST_MESSAGES.intro },
    { classification: 'body', title: EMAIL_ZONING_LIST_MESSAGES.body },
    { classification: 'outro', title: EMAIL_ZONING_LIST_MESSAGES.outro },
    { classification: 'signature', title: EMAIL_ZONING_LIST_MESSAGES.signature },
    { classification: 'disclaimer', title: EMAIL_ZONING_LIST_MESSAGES.disclaimer },
    { classification: 'attachment', title: EMAIL_ZONING_LIST_MESSAGES.attachment },
  ],
  previouslyAuthoredContent: [
    { classification: 'remainder', title: EMAIL_ZONING_LIST_MESSAGES.remainder },
  ],
};

export const POPULATE_ACTIVE_CASE_STARTED = 'case-actions/POPULATE_ACTIVE_CASE_STARTED';

export const POPULATE_ACTIVE_CASE_FINISHED = 'case-actions/POPULATE_ACTIVE_CASE_FINISHED';

export const CHANGE_CHILD_KEYS_STARTED = 'case-actions/CHANGE_CHILD_KEYS_STARTED';

export const CHANGE_CHILD_KEYS_FINISHED = 'case-actions/CHANGE_CHILD_KEYS_FINISHED';

export const CHILD_KEYS_ADDED = 'case-actions/CHILD_KEYS_ADDED';
export const CHILD_KEYS_REMOVED = 'case-actions/CHILD_KEYS_REMOVED';
export const FORGET_LAST_UPDATE = 'case-actions/FORGET_LAST_UPDATE';
export const CASE_VISITED = 'case-actions/CASE_VISITED';
export const CASE_MODE_EXITED = 'case-actions/CASE_MODE_EXITED';
export const UPDATE_ACTIVE_CASE = 'case-actions/UPDATE_ACTIVE_CASE';

export const GET_CASE_COMMS_TIMELINE_START = 'case-actions/GET_CASE_COMMS_TIMELINE_START';
export const GET_CASE_COMMS_TIMELINE_FINISHED = 'case-actions/GET_CASE_COMMS_TIMELINE_FINISHED';

export const CREATE_CASE_CLICKED = 'case-actions/CREATE_CASE_CLICKED';
export const createCaseClicked = () => ({ type: CREATE_CASE_CLICKED });

export const CREATE_CASE_MODAL_CLOSED = 'case-actions/CREATE_CASE_MODAL_CLOSED';
export const createCaseModalClosed = () => ({ type: CREATE_CASE_MODAL_CLOSED });

export const ADD_RECORDS_TO_CASE_FULFILLED = 'case-actions/ADD_RECORDS_TO_CASE_FULFILLED';
export const REMOVE_RECORDS_FROM_CASE_FULFILLED = 'case-actions/REMOVE_RECORDS_FROM_CASE_FULFILLED';

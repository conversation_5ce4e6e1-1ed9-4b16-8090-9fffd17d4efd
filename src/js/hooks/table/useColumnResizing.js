import { useState, useRef, useEffect } from 'react';

/**
 * Custom hook to assist in resizing an columns in a grid table
 *
 * @param {*} onResize : function which will be called when the element is being resized with the x and y coordinates
 *
 * @returns {*}
 * isResizing : set to true when the element is being resized.
 * ref : reference which needs to set the node/element which has to be resized.
 * */

const useColumnResizer = onResize => {
  const [isResizing, setIsResizing] = useState(false);
  const resizeHandleRef = useRef();
  const containerRef = useRef();

  const onMouseMove = e => {
    if (!isResizing) return;
    const { x, y } = containerRef.current.getBoundingClientRect();

    onResize({ x: e.x - x, y: e.y - y }, containerRef.current);
    e.stopPropagation();
    e.preventDefault();
  };

  const onMouseUp = e => {
    setIsResizing(false);
    e.stopPropagation();
    e.preventDefault();
  };

  const onMouseDown = e => {
    if (e.button !== 0) return;
    setIsResizing(true);
    e.stopPropagation();
    e.preventDefault();
  };

  // When the element mounts, attach an mousedown listener
  useEffect(() => {
    // using ref.current to clean up throws a warning as it is not safe
    // and the value of ref.current might get changed before clean up.

    const refVariableToCleanUp = resizeHandleRef.current;
    refVariableToCleanUp?.addEventListener('mousedown', onMouseDown);
    refVariableToCleanUp?.addEventListener('mouseup', onMouseUp);
    return () => {
      refVariableToCleanUp?.removeEventListener('mousedown', onMouseDown);
      refVariableToCleanUp?.removeEventListener('mouseup', onMouseUp);
    };
  }, []);

  // Every time the isResizing state changes, assign or remove
  // the corresponding mousemove and mouseup handlers
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mouseup', onMouseUp);
      document.addEventListener('mousemove', onMouseMove);
    } else {
      document.removeEventListener('mouseup', onMouseUp);
      document.removeEventListener('mousemove', onMouseMove);
    }
    return () => {
      document.removeEventListener('mouseup', onMouseUp);
      document.removeEventListener('mousemove', onMouseMove);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isResizing]);

  return { containerRef, isResizing, resizeHandleRef };
};

export default useColumnResizer;

/* eslint-disable no-await-in-loop */
import { useEffect, useState, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { addCustomErrorNotification } from 'actions/notification';
import {
  getCommsAttachmentsDownloadEndpoint,
  getCommsAttachmentsDownloadEndpointWithoutId,
  getAttachmentPreview,
} from 'constants/endpoints';
import log from 'loglevel';

import {
  openDownloadedFile,
  getDownloadIdAndPresignedUrl,
  downloadPresignedFile,
  getFileContent,
} from 'util/api/downloadV3';
import {
  DOWNLOAD_ATTACHMENT_PREVIEW_FULFILLED,
  DOWNLOAD_ATTACHMENT_PREVIEW_LOADING,
  DOWNLOAD_ATTACHMENT_PREVIEW_REJECTED,
} from 'js/constants/actions/download';

import usePermission from './usePermission';

/**
 *
 * Custom hook to assist in downloading comms attachments.
 *
 * @param {*}
 * {
    attachmentId : string - id of the attachment to be downloaded

    fetchOnLoad : bool - to specify if the attachment has to be fetched on load. By default its set to false
    Useful when attachment has to be visualized

    bucket: string - bucket name to be used for downloading the file
  }
 *
 * @returns {*}
 * {
    saveFile : func - when this function is called the file is downloaded and saved on the device.
    fileContent : blob - content of the file, can be used to visualize the file.
    isFetching : bool - this value changes to true when file is being fetched and its set to false when the fetch is complete
    fetchFileOnDemand: func - when this function called if the file is being fetched
  }
 */
export const useDownloadCommsAttachment = ({
  attachmentKey,
  commsId,
  attachmentId,
  fetchOnLoad = false,
  errorText,
  messageId,
}) => {
  const dispatch = useDispatch();
  const [isFetching, setIsFetching] = useState(false);
  const [fileContent, setFileContent] = useState(null);
  const [fetchError, setFetchError] = useState(null);

  const endpoint = getCommsAttachmentsDownloadEndpoint(attachmentId);
  const hasNoPresignedUrl = usePermission('hasNoPresignedUrl');

  const fetchFile = useCallback(
    async ({ useRawDownload = false } = {}) => {
      const result = {};
      setIsFetching(true);
      try {
        let response;
        if (useRawDownload && (messageId || commsId) && attachmentKey) {
          dispatch({
            type: DOWNLOAD_ATTACHMENT_PREVIEW_LOADING,
            payload: { attachmentKey, contentType: 'original' },
          });

          response = await getFileContent({
            endpoint: getAttachmentPreview({ commsId: messageId ?? commsId, attachmentKey }),
            method: 'POST',
          });
          dispatch({
            type: DOWNLOAD_ATTACHMENT_PREVIEW_FULFILLED,
            payload: { attachmentKey, response, contentType: 'original' },
          });
        } else if (attachmentId) {
          const { pre_signed_url, filename, blobResponse } = await getDownloadIdAndPresignedUrl({
            endpoint,
            method: 'POST',
            hasNoPresignedUrl,
          });
          response = { url: pre_signed_url, filename, blobResponse };
        } else if (commsId && attachmentKey) {
          // Some newer comms attachments are stored in a different place - see EP-1624. These will not have an attachmentId.
          const { pre_signed_url, filename, blobResponse } = await getDownloadIdAndPresignedUrl({
            endpoint: getCommsAttachmentsDownloadEndpointWithoutId(commsId, attachmentKey),
            method: 'POST',
            hasNoPresignedUrl,
          });
          response = { url: pre_signed_url, filename, blobResponse };
        }
        result.file = response;
      } catch (e) {
        result.error = e;
        dispatch({
          type: DOWNLOAD_ATTACHMENT_PREVIEW_REJECTED,
          payload: { attachmentKey, errors: e, contentType: 'original' },
        });
        dispatch(addCustomErrorNotification(errorText ?? e.message));
        log.error(e);
      } finally {
        setIsFetching(false);
      }

      return result;
    },
    [
      messageId,
      commsId,
      attachmentKey,
      attachmentId,
      dispatch,
      endpoint,
      errorText,
      hasNoPresignedUrl,
    ]
  );

  const fetchFileOnDemand = useCallback(() => {
    return fetchFile({ useRawDownload: true }).then(result => {
      if (result.error) {
        setFileContent(null);
        setFetchError(result.error);
      } else {
        setFetchError(null);
        setFileContent({ blobResponse: result.file });
      }
      return result;
    });
  }, [setFileContent, setFetchError, fetchFile]);

  useEffect(() => {
    if (fetchOnLoad) {
      fetchFileOnDemand();
    }
  }, [fetchFileOnDemand, fetchOnLoad]);

  const saveFile = async ({ mimeType, forceFetch = false }) => {
    if (forceFetch || (!fetchOnLoad && !fileContent)) {
      fetchFile().then(result => {
        if (result.error) {
          setFileContent(null);
          setFetchError(result.error);
        } else {
          setFetchError(null);
          setFileContent(result.file);
          if (result.file?.url && !hasNoPresignedUrl) {
            downloadPresignedFile(result.file.url);
          } else {
            openDownloadedFile(result.file?.blobResponse, {
              contentType: mimeType,
              fileExtension: (result.file?.filename || '').split('.').pop(),
              filename: result.file?.filename,
            });
          }
        }
      });
    } else if (fileContent?.url && !hasNoPresignedUrl) {
      downloadPresignedFile(fileContent.url);
    } else {
      openDownloadedFile(fileContent?.blobResponse, {
        contentType: mimeType,
        fileExtension: (fileContent?.filename || '').split('.').pop(),
        filename: fileContent?.filename,
      });
    }
  };

  // This function is used to download the file and save it on the device
  const fetchAndSaveFile = async ({ mimeType }) => {
    await fetchFile().then(result => {
      if (result?.file?.url && !hasNoPresignedUrl) {
        downloadPresignedFile(result.file?.url);
      } else {
        openDownloadedFile(result.file?.blobResponse, {
          contentType: mimeType,
          fileExtension: (result.file?.filename || '').split('.').pop(),
          filename: result.file?.filename,
        });
      }
    });
  };

  return {
    error: fetchError,
    fetchAndSaveFile,
    fetchFileOnDemand,
    fileContent,
    isFetching,
    saveFile,
  };
};

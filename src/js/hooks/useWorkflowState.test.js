import { act } from 'react-dom/test-utils';

import useWorkflowState from 'js/hooks/useWorkflowState';

import {
  REPORTED_ARM_REJECTED,
  REPORTED_NCA_ACCEPTED_ARM_ACCEPTED,
  REPORTED_NCA_PENDING_ARM_ACCEPTED,
  REPORTED_NCA_REJECTED_ARM_ACCEPTED,
  UNREPORTED_NON_REPORTABLE_PASSED,
  UNREPORTED_REPORTABLE_PASSED,
  UNREPORTED_REPORTABLE_PASSED_USER_OVERRIDE,
  UNREPORTED_REPORTABLE_FAILED,
  UNREPORTED_NON_REPORTABLE_PASSED_USER_OVERRIDE,
  UNREPORTED_NON_REPORTABLE_FAILED,
  UNREPORTED_REPORTABLE_USER_OVERRIDE_PASSED,
  UNREPORTED_REPORTABLE_USER_OVERRIDE_PASSED_USER_OVERRIDE,
  UNREPORTED_REPORTABLE_USER_OVERRIDE_FAILED,
  UNREPORTED_NON_REPORTABLE_USER_OVERRIDE_PASSED,
  UNREPORTED_NON_REPORTABLE_USER_OVERRIDE_PASSED_USER_OVERRIDE,
  UNREPORTED_NON_REPORTABLE_USER_OVERRIDE_FAILED,
  TR_CLEAR_ARM_REJECTION,
  TR_CLEAR_NCA_REJECTION,
  TR_CREATE_CANCELLATION,
  TR_MAKE_REPORTABLE,
  TR_MAKE_NON_REPORTABLE,
  TR_RESTORE_ELIGIBILITY_STATUS,
  TR_RESTORE_VALIDATION_ERRORS,
  TR_REMOVE_VALIDATION_ERRORS,
} from 'constants/tr';

import testHook from './utils/testHook';

let mockUseWorkflowState;

beforeEach(() => {
  testHook(() => {
    mockUseWorkflowState = useWorkflowState([]);
  });
});

describe.skip('useWorkflowState', () => {
  test('should have a filteredResultsTypes function', () => {
    expect(mockUseWorkflowState.filteredResultsTypes).toBeInstanceOf(Function);
  });

  test('should have initial state', () => {
    expect(mockUseWorkflowState.allowClearArmRejection).toBe(false);
    expect(mockUseWorkflowState.allowClearNcaRejection).toBe(false);
    expect(mockUseWorkflowState.allowCreateCancellation).toBe(false);
    expect(mockUseWorkflowState.allowMakeNonReportable).toBe(false);
    expect(mockUseWorkflowState.allowMakeReportable).toBe(false);
    expect(mockUseWorkflowState.allowRemoveValidation).toBe(false);
    expect(mockUseWorkflowState.allowRestoreEligibilty).toBe(false);
    expect(mockUseWorkflowState.allowRestoreValidation).toBe(false);
  });

  test('should update allowCreateCancellation state to be true when correct status ids passed', () => {
    mockUseWorkflowState = useWorkflowState([
      { generatedStatusId: REPORTED_NCA_ACCEPTED_ARM_ACCEPTED },
      { generatedStatusId: REPORTED_NCA_PENDING_ARM_ACCEPTED },
      { generatedStatusId: REPORTED_NCA_REJECTED_ARM_ACCEPTED },
    ]);

    expect(mockUseWorkflowState.allowCreateCancellation).toBe(true);
  });

  test('should update allowMakeReportable state to be true when correct status ids passed', () => {
    mockUseWorkflowState = useWorkflowState([
      { generatedStatusId: UNREPORTED_NON_REPORTABLE_FAILED },
      { generatedStatusId: UNREPORTED_NON_REPORTABLE_PASSED },
      { generatedStatusId: UNREPORTED_NON_REPORTABLE_PASSED_USER_OVERRIDE },
    ]);

    expect(mockUseWorkflowState.allowMakeReportable).toBe(true);
  });

  test('should update allowMakeNonReportable state to be true when correct status ids passed', () => {
    mockUseWorkflowState = useWorkflowState([
      { generatedStatusId: UNREPORTED_REPORTABLE_FAILED },
      { generatedStatusId: UNREPORTED_REPORTABLE_PASSED },
      { generatedStatusId: UNREPORTED_REPORTABLE_PASSED_USER_OVERRIDE },
    ]);

    expect(mockUseWorkflowState.allowMakeNonReportable).toBe(true);
  });

  test('should update allowRestoreEligibilty state to be true when correct status ids passed', () => {
    mockUseWorkflowState = useWorkflowState([
      { generatedStatusId: UNREPORTED_NON_REPORTABLE_USER_OVERRIDE_FAILED },
      { generatedStatusId: UNREPORTED_NON_REPORTABLE_USER_OVERRIDE_PASSED },
      { generatedStatusId: UNREPORTED_NON_REPORTABLE_USER_OVERRIDE_PASSED_USER_OVERRIDE },
      { generatedStatusId: UNREPORTED_REPORTABLE_USER_OVERRIDE_PASSED_USER_OVERRIDE },
      { generatedStatusId: UNREPORTED_REPORTABLE_USER_OVERRIDE_PASSED },
      { generatedStatusId: UNREPORTED_REPORTABLE_USER_OVERRIDE_FAILED },
    ]);

    expect(mockUseWorkflowState.allowRestoreEligibilty).toBe(true);
  });

  test('should update allowRestoreValidation state to be true when correct status ids passed', () => {
    mockUseWorkflowState = useWorkflowState([
      { generatedStatusId: UNREPORTED_REPORTABLE_USER_OVERRIDE_PASSED_USER_OVERRIDE },
      { generatedStatusId: UNREPORTED_REPORTABLE_PASSED_USER_OVERRIDE },
      { generatedStatusId: UNREPORTED_NON_REPORTABLE_USER_OVERRIDE_PASSED_USER_OVERRIDE },
      { generatedStatusId: UNREPORTED_NON_REPORTABLE_PASSED_USER_OVERRIDE },
    ]);

    expect(mockUseWorkflowState.allowRestoreValidation).toBe(true);
  });

  test('should update allowRemoveValidation state to be true when correct status ids passed', () => {
    mockUseWorkflowState = useWorkflowState([
      { generatedStatusId: UNREPORTED_REPORTABLE_USER_OVERRIDE_FAILED },
      { generatedStatusId: UNREPORTED_REPORTABLE_FAILED },
      { generatedStatusId: UNREPORTED_NON_REPORTABLE_USER_OVERRIDE_FAILED },
      { generatedStatusId: UNREPORTED_NON_REPORTABLE_FAILED },
    ]);

    expect(mockUseWorkflowState.allowRemoveValidation).toBe(true);
  });

  test('should update allowClearArmRejection state to be true when correct status ids passed', () => {
    mockUseWorkflowState = useWorkflowState([{ generatedStatusId: REPORTED_ARM_REJECTED }]);

    expect(mockUseWorkflowState.allowClearArmRejection).toBe(true);
  });

  test('should update allowClearNcaRejection state to be true when correct status ids passed', () => {
    mockUseWorkflowState = useWorkflowState([
      { generatedStatusId: REPORTED_NCA_REJECTED_ARM_ACCEPTED },
    ]);

    expect(mockUseWorkflowState.allowClearNcaRejection).toBe(true);
  });

  test('should show a combo of allowRemoveValidation and allowMakeNonReportable state to be true when correct status ids passed', () => {
    mockUseWorkflowState = useWorkflowState([
      { generatedStatusId: UNREPORTED_REPORTABLE_USER_OVERRIDE_FAILED },
      { generatedStatusId: UNREPORTED_REPORTABLE_FAILED },
      { generatedStatusId: UNREPORTED_NON_REPORTABLE_USER_OVERRIDE_FAILED },
      { generatedStatusId: UNREPORTED_NON_REPORTABLE_FAILED },
      { generatedStatusId: UNREPORTED_REPORTABLE_FAILED },
      { generatedStatusId: UNREPORTED_REPORTABLE_PASSED },
      { generatedStatusId: UNREPORTED_REPORTABLE_PASSED_USER_OVERRIDE },
    ]);

    expect(mockUseWorkflowState.allowRemoveValidation).toBe(true);
    expect(mockUseWorkflowState.allowMakeNonReportable).toBe(true);
  });

  test('should show a combo of allowMakeReportable and allowRemoveValidation state to be true when correct status ids passed', () => {
    mockUseWorkflowState = useWorkflowState([
      { generatedStatusId: UNREPORTED_NON_REPORTABLE_FAILED },
      { generatedStatusId: UNREPORTED_NON_REPORTABLE_PASSED },
      { generatedStatusId: UNREPORTED_NON_REPORTABLE_PASSED_USER_OVERRIDE },
      { generatedStatusId: UNREPORTED_REPORTABLE_USER_OVERRIDE_FAILED },
      { generatedStatusId: UNREPORTED_REPORTABLE_FAILED },
      { generatedStatusId: UNREPORTED_NON_REPORTABLE_USER_OVERRIDE_FAILED },
      { generatedStatusId: UNREPORTED_NON_REPORTABLE_FAILED },
    ]);

    expect(mockUseWorkflowState.allowMakeReportable).toBe(true);
    expect(mockUseWorkflowState.allowRemoveValidation).toBe(true);
  });

  test('should return correct array when calling TR_CREATE_CANCELLATION', () => {
    let filteredTypes;

    const expected = [
      REPORTED_ARM_REJECTED,
      REPORTED_NCA_ACCEPTED_ARM_ACCEPTED,
      REPORTED_NCA_PENDING_ARM_ACCEPTED,
      REPORTED_NCA_REJECTED_ARM_ACCEPTED,
    ];

    act(() => {
      filteredTypes = mockUseWorkflowState.filteredResultsTypes(TR_CREATE_CANCELLATION);
    });

    expect(filteredTypes).toEqual(expect.arrayContaining(expected));
  });

  test('should return correct array when calling TR_MAKE_REPORTABLE', () => {
    let filteredTypes;

    const expected = [
      UNREPORTED_NON_REPORTABLE_FAILED,
      UNREPORTED_NON_REPORTABLE_PASSED,
      UNREPORTED_NON_REPORTABLE_PASSED_USER_OVERRIDE,
    ];

    act(() => {
      filteredTypes = mockUseWorkflowState.filteredResultsTypes(TR_MAKE_REPORTABLE);
    });

    expect(filteredTypes).toEqual(expect.arrayContaining(expected));
  });

  test('should return correct array when calling TR_MAKE_NON_REPORTABLE', () => {
    let filteredTypes;

    const expected = [
      UNREPORTED_REPORTABLE_FAILED,
      UNREPORTED_REPORTABLE_PASSED,
      UNREPORTED_REPORTABLE_PASSED_USER_OVERRIDE,
    ];

    act(() => {
      filteredTypes = mockUseWorkflowState.filteredResultsTypes(TR_MAKE_NON_REPORTABLE);
    });

    expect(filteredTypes).toEqual(expect.arrayContaining(expected));
  });

  test('should return correct array when calling TR_RESTORE_ELIGIBILITY_STATUS', () => {
    let filteredTypes;

    const expected = [
      UNREPORTED_NON_REPORTABLE_USER_OVERRIDE_FAILED,
      UNREPORTED_NON_REPORTABLE_USER_OVERRIDE_PASSED,
      UNREPORTED_NON_REPORTABLE_USER_OVERRIDE_PASSED_USER_OVERRIDE,
      UNREPORTED_REPORTABLE_USER_OVERRIDE_PASSED_USER_OVERRIDE,
      UNREPORTED_REPORTABLE_USER_OVERRIDE_PASSED,
      UNREPORTED_REPORTABLE_USER_OVERRIDE_FAILED,
    ];

    act(() => {
      filteredTypes = mockUseWorkflowState.filteredResultsTypes(TR_RESTORE_ELIGIBILITY_STATUS);
    });

    expect(filteredTypes).toEqual(expect.arrayContaining(expected));
  });

  test('should return correct array when calling TR_RESTORE_VALIDATION_ERRORS', () => {
    let filteredTypes;

    const expected = [
      UNREPORTED_REPORTABLE_USER_OVERRIDE_PASSED_USER_OVERRIDE,
      UNREPORTED_REPORTABLE_PASSED_USER_OVERRIDE,
      UNREPORTED_NON_REPORTABLE_USER_OVERRIDE_PASSED_USER_OVERRIDE,
      UNREPORTED_NON_REPORTABLE_PASSED_USER_OVERRIDE,
    ];

    act(() => {
      filteredTypes = mockUseWorkflowState.filteredResultsTypes(TR_RESTORE_VALIDATION_ERRORS);
    });

    expect(filteredTypes).toEqual(expect.arrayContaining(expected));
  });

  test('should return correct array when calling TR_REMOVE_VALIDATION_ERRORS', () => {
    let filteredTypes;

    const expected = [
      UNREPORTED_REPORTABLE_USER_OVERRIDE_FAILED,
      UNREPORTED_REPORTABLE_FAILED,
      UNREPORTED_NON_REPORTABLE_USER_OVERRIDE_FAILED,
      UNREPORTED_NON_REPORTABLE_FAILED,
    ];

    act(() => {
      filteredTypes = mockUseWorkflowState.filteredResultsTypes(TR_REMOVE_VALIDATION_ERRORS);
    });

    expect(filteredTypes).toEqual(expect.arrayContaining(expected));
  });

  test('should return correct array when calling TR_CLEAR_ARM_REJECTION', () => {
    let filteredTypes;

    const expected = [REPORTED_ARM_REJECTED];

    act(() => {
      filteredTypes = mockUseWorkflowState.filteredResultsTypes(TR_CLEAR_ARM_REJECTION);
    });

    expect(filteredTypes).toEqual(expect.arrayContaining(expected));
  });

  test('should return correct array when calling TR_CLEAR_NCA_REJECTION', () => {
    let filteredTypes;

    const expected = [REPORTED_NCA_REJECTED_ARM_ACCEPTED];

    act(() => {
      filteredTypes = mockUseWorkflowState.filteredResultsTypes(TR_CLEAR_NCA_REJECTION);
    });

    expect(filteredTypes).toEqual(expect.arrayContaining(expected));
  });

  test('should return correct array when calling nothing', () => {
    let filteredTypes;

    const expected = [];

    act(() => {
      filteredTypes = mockUseWorkflowState.filteredResultsTypes();
    });

    expect(filteredTypes).toEqual(expect.arrayContaining(expected));
  });
});

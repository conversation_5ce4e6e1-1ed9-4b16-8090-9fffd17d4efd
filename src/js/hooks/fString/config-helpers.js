export const mergeConfigs = ({ chartsConfig, refineConfig }) => {
  const refineConfigItems = Object.values(refineConfig.refineTwoConfig).flat();
  const mappedChartsConfig = chartsConfig.map(({ fieldName, ...rest }) => ({
    ...rest,
    fieldId: fieldName,
  }));
  const chartsConfigItemsNotInRefineConfig = mappedChartsConfig
    .filter(item => !refineConfigItems.find(el => el.fieldId === item.fieldId))
    .map(item => ({
      condition: 'is',
      type: 'string',
      ...item,
    }));
  const result = refineConfigItems.map(item => ({
    ...mappedChartsConfig.find(el => el.fieldId === item.fieldId),
    ...item,
  }));
  return [...result, ...chartsConfigItemsNotInRefineConfig];
};

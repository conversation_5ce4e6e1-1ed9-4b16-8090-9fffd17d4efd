import SvgChart from 'util/d3-render/svg-chart';
import Axis from 'util/d3-render/axis';
import TimelineBrush from 'util/d3-render/timeline/brush';
import SwimlaneLanes from 'util/d3-render/swimlanes-timeline/lanes';
import * as d3Constants from 'constants/d3';
import * as d3Util from 'util/d3';
import ChartContext from '../timeline/chart-context';

export default class SwimlanesTimeline {
  constructor(parent, margin, transitionMs, timeAxisConfig, dataAxisConfig, onBrushEndHandler) {
    this._svgChart = new SvgChart(parent, margin);
    this._transitionMs = transitionMs;
    this._timeAxis = new Axis(timeAxisConfig, this._svgChart.chartGroup);
    this._dataAxis = new Axis(dataAxisConfig, this._svgChart.chartGroup);
    this._timeAxisConfig = timeAxisConfig;
    this._dataAxisConfig = dataAxisConfig;

    this._brush = new TimelineBrush(
      onBrushEndHandler,
      this._timeAxis.scale,
      this._svgChart,
      timeAxisConfig,
      transitionMs / 2
    );
    if (this._dataAxisConfig.visualisationType === d3Constants.VISUALISATION_TYPE_SWIMLANES) {
      this._visualisation = new SwimlaneLanes(
        this._timeAxis.scale,
        this._dataAxis.scale,
        this._svgChart,
        this._dataAxisConfig,
        transitionMs
      );
    } else {
      throw new Error(
        `Visualisation type of '${this._dataAxisConfig.visualisationType} is not supported'`
      );
    }
  }

  render(width, height, timelineDataList, intl) {
    const transition = this._svgChart.createTransition(this._transitionMs);
    const dataDomain = timelineDataList.getDataDomain(d => d.key);
    const { hasData } = timelineDataList;
    const existingDomainPixelPos = this._timeAxis.getDomainPixelPosOnExistingScale(
      timelineDataList.viewportDomain
    );
    const chartContext = new ChartContext(
      width,
      height,
      this._svgChart.margin,
      timelineDataList.viewportDomain,
      dataDomain,
      transition,
      intl
    );
    this._svgChart.render(chartContext);
    let existingMaxPixel = {};
    this._timeAxis.render(chartContext);
    if (hasData) {
      existingMaxPixel = d3Util.getMaxPixel(
        this._dataAxis,
        this._visualisation.data,
        timelineDataList.viewportDomain,
        null,
        chartContext
      );

      this._visualisation.render(
        chartContext,
        timelineDataList.dataList[0].data,
        timelineDataList.dataList[0].renderVersion,
        timelineDataList.viewportDomain,
        existingDomainPixelPos,
        existingMaxPixel,
        d3Util.getMaxPixel(
          this._dataAxis,
          timelineDataList.dataList[0].data,
          timelineDataList.viewportDomain,
          this._visualisation.viewportDomain,
          chartContext
        )
      );
    }

    this._brush.render(chartContext);
  }
}

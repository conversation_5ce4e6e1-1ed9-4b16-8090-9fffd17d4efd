import * as d3Constants from 'constants/d3';

export default class AxisIconSwatch {
  constructor(axisConfig) {
    this._axisConfig = axisConfig;
  }

  render(labelGroup) {
    const swatch = labelGroup.selectAll('.legend-swatch').data([null]);

    swatch
      .enter()
      .append('rect')
      .classed('legend-swatch', true)
      .attr('width', d3Constants.AXIS_ICON_DIM)
      .attr('height', d3Constants.AXIS_ICON_DIM)
      .attr('x', this._axisConfig.isRight ? 0 : -d3Constants.AXIS_ICON_DIM)
      .attr('y', -(d3Constants.AXIS_ICON_DIM - 2))
      .attr('rx', 2)
      .attr('ry', 2);
  }
}

import { removePrefixfromQuery } from './url-parsing';

describe('removePrefixfromQuery', () => {
  it('should remove prefix from query', () => {
    const urlQuery = {
      prefixSort: 'workflow.assigneeName:asc',
      prefixTake: '10',
      prefixSkip: '0',
    };
    expect(removePrefixfromQuery(urlQuery, 'prefix')).toEqual({
      sort: 'workflow.assigneeName:asc',
      take: '10',
      skip: '0',
    });
  });

  it('should not remove prefix from query', () => {
    const urlQuery = {
      prefixSort: 'workflow.assigneeName:asc',
      prefixTake: '10',
      prefixSkip: '0',
    };
    expect(removePrefixfromQuery(urlQuery, 'notPrefix')).toEqual({
      prefixSort: 'workflow.assigneeName:asc',
      prefixTake: '10',
      prefixSkip: '0',
    });
  });

  it('should not change anything if a query does not have a prefix', () => {
    const urlQuery = {
      prefixSort: 'workflow.assigneeName:asc',
      take: '10',
      skip: '0',
    };
    expect(removePrefixfromQuery(urlQuery, 'prefix')).toEqual({
      sort: 'workflow.assigneeName:asc',
      take: '10',
      skip: '0',
    });
  });
});

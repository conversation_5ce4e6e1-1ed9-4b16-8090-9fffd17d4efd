import { SubmissionError } from 'redux-form';
import log from 'loglevel';

import includes from 'lodash/includes';

import * as notificationActions from 'actions/notification';

export function extractErrorMessage(err) {
  return err && err.response && err.response.data ? err.response.data.errorMessage || '' : '';
}

export function entityAlreadyExists(err) {
  return includes(extractErrorMessage(err), 'has non unique properties');
}

export function handleError(err, dispatch, model) {
  if (!err.handled) {
    if (!(err instanceof SubmissionError)) {
      log.error(err);
      dispatch(notificationActions.addErrorNotification(err, model));
    }

    err.handled = true;
  }

  return err;
}

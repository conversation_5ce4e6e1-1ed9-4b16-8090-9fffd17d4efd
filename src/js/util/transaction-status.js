/* eslint-disable no-nested-ternary */

import first from 'lodash/first';
import isEmpty from 'lodash/isEmpty';
import filter from 'lodash/filter';

export function findValidationByPropertyValuePath(esmaValidations, valuePath) {
  if (!esmaValidations) {
    return undefined;
  }

  const validation = filter(esmaValidations.externalChecks, {
    transactionField: valuePath,
  }).concat(filter(esmaValidations.internalChecks, { transactionField: valuePath }));

  if (!isEmpty(validation)) {
    return first(validation);
  }
  return undefined;
}

export const generateFailureTextDisplay = failure =>
  failure.ruleId && failure.errorText
    ? `${failure.ruleId} - ${failure.errorText}`
    : failure.ruleId
      ? failure.ruleId
      : failure.errorText;

export const totalFailures = transactionStatus => {
  if (!transactionStatus) {
    return [];
  }

  const failures = [];

  if (transactionStatus.externalChecks) {
    transactionStatus.externalChecks.forEach(check => {
      failures.push(check);
    });
  }
  if (transactionStatus.internalChecks) {
    transactionStatus.internalChecks.forEach(check => {
      failures.push(check);
    });
  }

  return failures;
};

import { isProbablyAStringWithoutHtml } from 'util/string';

describe('html test', () => {
  it('returns true for a plain string', () => {
    const testString = 'This is a normal string';
    expect(isProbablyAStringWithoutHtml(testString)).toBe(true);
  });

  it('returns false for html strings', () => {
    const testString1 = '<p>This has tags</p>';
    expect(isProbablyAStringWithoutHtml(testString1)).toBe(false);
    const testString2 = 'This also <span>has tags</span>!';
    expect(isProbablyAStringWithoutHtml(testString2)).toBe(false);
  });

  it('returns true for things that look contains < or > but ar not html', () => {
    const testString1 = 'If 2 > 1';
    expect(isProbablyAStringWithoutHtml(testString1)).toBe(true);
    const testString2 = 'So this < and this > are just characters';
    expect(isProbablyAStringWithoutHtml(testString2)).toBe(true);
    const testString3 = 'So this >word< we wrote';
    expect(isProbablyAStringWithoutHtml(testString3)).toBe(true);
  });

  it('returns true for an empty string', () => {
    const testString = '';
    expect(isProbablyAStringWithoutHtml(testString)).toBe(true);
  });

  it('returns false for anything that is not a string', () => {
    expect(isProbablyAStringWithoutHtml(undefined)).toBe(false);
    expect(isProbablyAStringWithoutHtml(null)).toBe(false);
    expect(isProbablyAStringWithoutHtml({})).toBe(false);
    expect(isProbablyAStringWithoutHtml(42)).toBe(false);
    expect(isProbablyAStringWithoutHtml(['string'])).toBe(false);
  });
});

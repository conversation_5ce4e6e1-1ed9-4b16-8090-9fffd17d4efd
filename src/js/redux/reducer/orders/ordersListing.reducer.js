import {
  GET_ORDERS_LISTING_LOADING,
  GET_ORDERS_LISTING_REJECTED,
  GET_ORDERS_LISTING_FULFILLED,
} from 'js/actions/orders/orders.actions';

const ordersListing = (state = {}, { type, payload }) => {
  switch (type) {
    case GET_ORDERS_LISTING_LOADING:
      return {
        ...state,
        loading: true,
        errors: null,
      };
    case GET_ORDERS_LISTING_REJECTED:
      return {
        ...state,
        errors: payload.errors,
        data: null,
        loading: false,
      };
    case GET_ORDERS_LISTING_FULFILLED:
      if (state.version > payload.version) return state;
      return {
        ...state,
        data: payload.response,
        loading: false,
        version: payload.version,
        errors: null,
      };
    default:
      return state;
  }
};

export default ordersListing;

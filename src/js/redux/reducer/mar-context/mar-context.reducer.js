import { combineReducers } from 'redux';

import timingProfile from './behavioural-context/timing-profile.reducer';
import assetClassProfile from './behavioural-context/asset-class-profile.reducer';
import behaviouralContextTimeline from './behavioural-context/behavioural-context-timeline.reducer';

import surveillanceContextSummary from './surveillance-context/surveillance-context-summary.reducer';
import surveillanceContextTimeline from './surveillance-context/surveillance-context-timeline.reducer';

import instrumentContextSummary from './instrument-context/instrument-context-summary.reducer';
import instrumentContextTimeline from './instrument-context/instrument-context-timeline.reducer';

export default combineReducers({
  assetClassProfile,
  behaviouralContextTimeline,
  instrumentContextSummary,
  instrumentContextTimeline,
  surveillanceContextSummary,
  surveillanceContextTimeline,
  timingProfile,
});

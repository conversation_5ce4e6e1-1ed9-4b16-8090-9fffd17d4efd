import {
  SUR<PERSON><PERSON><PERSON><PERSON>E_CONTEXT_TIMELINE_LOADING,
  SURVEILLANCE_CONTEXT_TIMELINE_REJECTED,
  SURVEILLANCE_CONTEXT_TIMELINE_FULFILLED,
} from 'actions/mar-context/mar-context-constants';

const surveillanceContextTimelineReducerReducer = (state = {}, { type, payload }) => {
  switch (type) {
    case SURVEILLANCE_CONTEXT_TIMELINE_LOADING:
      return {
        ...state,
        [payload.entityType]: {
          ...state[payload.entityType],
          [payload.entityName]: {
            ...state[payload.entityType]?.[payload.entityName],
            loading: true,
          },
        },
      };
    case SURVEILLANCE_CONTEXT_TIMELINE_REJECTED:
      return {
        ...state,
        [payload.entityType]: {
          ...state[payload.entityType],
          [payload.entityName]: {
            ...state[payload.entityType][payload.entityName],
            data: null,
            errors: payload.errors,
            loading: false,
          },
        },
      };
    case SURVEILLANCE_CONTEXT_TIMELINE_FULFILLED:
      return {
        ...state,
        [payload.entityType]: {
          ...state[payload.entityType],
          [payload.entityName]: {
            ...state[payload.entityType][payload.entityName],
            data: payload.data,
            errors: null,
            loading: false,
          },
        },
      };
    default:
      return state;
  }
};

export default surveillanceContextTimelineReducerReducer;

import {
  SAVED_FILTERS_RECEIVED,
  INCLUDE_CLICKED,
  EXCLUDE_CLICKED,
  SINGLE_FILTER_SELECTED,
  MULTIPLE_FILTERS_SELECTED,
  RANGE_CHANGED,
  REFINE_MOUNTED,
  PAGE_UNMOUNTED,
  LEGACY_FILTERS_CHANGED,
  FILTER_CLOSED,
  RESET_CLICKED,
  PAGE_MOUNTED,
} from 'actions/refine/refine.actions';

import ROUTES from 'js/constants/routes';

import isNil from 'lodash/isNil';

import initialState from './filters.state';

const filtersReducer = (state = initialState, action) => {
  switch (action.type) {
    case SINGLE_FILTER_SELECTED: {
      // Only allow one value to be selected at a time per field
      // First, remove any other conditions for this field
      const removed = state.filters.filter(filter => action.payload.field !== filter.id);

      const newFilters = [
        ...removed,
        {
          condition: 'is',
          id: action.payload.field,
          type: action.payload.type,
          values: !isNil(action.payload.value) ? [action.payload.value] : [],
        },
      ];
      // And remove any filters than now have no values in them
      return { ...state, filters: newFilters.filter(filter => filter.values.length) };
    }

    case MULTIPLE_FILTERS_SELECTED: {
      // First, remove any other conditions for this field
      const removed = state.filters.filter(filter => action.payload.field !== filter.id);

      const newFilters = [
        ...removed,
        {
          condition: action.payload.condition || 'is',
          id: action.payload.field,
          type: action.payload.type,
          values: action.payload.values,
        },
      ];

      // And remove any filters than now have no values in them
      return { ...state, filters: newFilters.filter(filter => filter.values.length) };
    }

    case INCLUDE_CLICKED: {
      // First, remove any other conditions that are selected for the value
      const removed = state.filters.map(filter =>
        action.payload.field === filter.id && filter.condition !== 'is'
          ? { ...filter, values: filter.values.filter(value => value !== action.payload.value) }
          : filter
      );

      const alreadyHasIsFilterForThisValue = removed.some(
        filter =>
          action.payload.field === filter.id &&
          filter.values.includes(action.payload.value) &&
          filter.condition === 'is'
      );

      const hasAnyIsFiltersForThisField = removed.some(
        filter => action.payload.field === filter.id && filter.condition === 'is'
      );

      let newFilters;
      if (alreadyHasIsFilterForThisValue) {
        // If it is being deselected, remove the value
        newFilters = removed.map(filter =>
          action.payload.field !== filter.id
            ? filter
            : {
                ...filter,
                values: filter.values.filter(value => value !== action.payload.value),
              }
        );
      } else if (hasAnyIsFiltersForThisField) {
        // If there's already an "is" filter for this field, add the value to the values array
        newFilters = removed.map(filter =>
          action.payload.field === filter.id && filter.condition === 'is'
            ? {
                ...filter,
                values: [...filter.values, action.payload.value],
              }
            : filter
        );
      } else {
        // If there is no "is" filter at all for this field, we need to add one
        newFilters = [
          ...removed,
          {
            condition: 'is',
            id: action.payload.field,
            type: action.payload.type,
            values: [action.payload.value],
          },
        ];
      }

      // Remove any filters than now have no values in them
      return { ...state, filters: newFilters.filter(filter => filter.values.length) };
    }

    case EXCLUDE_CLICKED: {
      // First, remove any other conditions that are selected for the value
      const removed = state.filters.map(filter =>
        action.payload.field === filter.id && filter.condition !== 'isNot'
          ? { ...filter, values: filter.values.filter(value => value !== action.payload.value) }
          : filter
      );

      const alreadyHasIsFilterForThisValue = removed.some(
        filter =>
          action.payload.field === filter.id &&
          filter.values.includes(action.payload.value) &&
          filter.condition === 'isNot'
      );

      const hasAnyIsFiltersForThisField = removed.some(
        filter => action.payload.field === filter.id && filter.condition === 'isNot'
      );

      let newFilters;
      if (alreadyHasIsFilterForThisValue) {
        // If it is being deselected, remove the value
        newFilters = removed.map(filter =>
          action.payload.field !== filter.id
            ? filter
            : {
                ...filter,
                values: filter.values.filter(value => value !== action.payload.value),
              }
        );
      } else if (hasAnyIsFiltersForThisField) {
        // If there's already an "isNot" filter for this field, add the value to the values array
        newFilters = removed.map(filter =>
          action.payload.field === filter.id && filter.condition === 'isNot'
            ? {
                ...filter,
                values: [...filter.values, action.payload.value],
              }
            : filter
        );
      } else {
        // If there is no "isNot" filter at all for this field, we need to add one
        newFilters = [
          ...removed,
          {
            condition: 'isNot',
            id: action.payload.field,
            type: action.payload.type,
            values: [action.payload.value],
          },
        ];
      }

      // Remove any filters than now have no values in them
      return { ...state, filters: newFilters.filter(filter => filter.values.length) };
    }

    case RANGE_CHANGED: {
      const filterPresent = state.filters.some(filter => filter.id === action.payload.field);
      let newFilters;
      if (!filterPresent) {
        newFilters = [
          ...state.filters,
          {
            condition: action.payload.condition,
            id: action.payload.field,
            type: action.payload.type,
            values: [action.payload.value],
          },
        ];
      } else {
        newFilters = state.filters.map(filter =>
          filter.id === action.payload.field
            ? {
                condition: action.payload.condition,
                id: action.payload.field,
                type: action.payload.type,
                values: [action.payload.value],
              }
            : filter
        );
      }
      return { ...state, filters: newFilters };
    }

    case FILTER_CLOSED:
      return {
        ...state,
        filters: state.filters.filter(filter => filter.id !== action.payload.field),
      };

    case REFINE_MOUNTED:
      // Retrieving filters from stash if pathname matches.
      // Reset everything when pathname changes,
      // Only in Case of single-watch-page i.e /comms-surveillance/watches/:id or /trades-surveillance/watches/:id, we don't reset the filters
      // The same filters that was applied from /comms-surveillance/dashboard and /trades-surveillance/dashboard respectively are carried forward
      if (
        (action.payload?.pathname?.startsWith('/comms-surveillance/watches/') &&
          (state.pathname === ROUTES.COMMS_SURVEILLANCE.ALERTS.INDEX ||
            state.pathname === ROUTES.COMMS_SURVEILLANCE.DASHBOARD)) ||
        (action.payload?.pathname?.startsWith('/trades-surveillance/watches/') &&
          (state.pathname === ROUTES.TRADES_SURVEILLANCE.ALERTS.INDEX ||
            state.pathname === ROUTES.TRADES_SURVEILLANCE.DASHBOARD))
      ) {
        return {
          ...state,
          filters:
            state.filters?.filter(item => {
              return item.id !== 'detail.watchName';
            }) || [],
          pathname: action.payload.pathname,
        };
      } else if (state.stash?.[action.payload.pathname]) {
        return {
          ...state,
          filters: state.stash?.[action.payload.pathname] || [],
          pathname: action.payload.pathname,
        };
      }

      // TODO reset everything if the location has changed from the state; otherwise, persist
      return action.payload.pathname !== state.pathname
        ? {
            ...initialState,
            stash: state.stash,
            pathname: action.payload.pathname,
          }
        : state;

    case PAGE_MOUNTED: {
      // Update the pathname so as to reset the filters previously applied
      return {
        ...state,
        pathname: action.payload.pathname,
      };
    }

    case PAGE_UNMOUNTED: {
      // Stashing filters to retrieve it later
      return {
        ...state,
        stash: {
          ...state.stash,
          [state.pathname]: state.filters,
        },
      };
    }

    case RESET_CLICKED:
      return { ...state, filters: initialState.filters };

    case LEGACY_FILTERS_CHANGED:
      // This is part of a pattern that keeps this state in sync with the old "widget" state
      return action.payload.mappedLegacyFilters
        ? { ...state, filters: action.payload.mappedLegacyFilters }
        : state;
    case SAVED_FILTERS_RECEIVED:
      return action.payload.filters ? { ...state, filters: action.payload.filters } : state;
    default:
      return state;
  }
};

export default filtersReducer;

import {
  GET_INSTRUMENT_STATS_LOADING,
  GET_INSTRUMENT_STATS_REJECTED,
  GET_INSTRUMENT_STATS_FULFILLED,
} from 'actions/prices/prices-constants';

const statsReducer = (state = {}, action) => {
  switch (action.type) {
    case GET_INSTRUMENT_STATS_LOADING:
      return {
        ...state,
        [action.payload.instrumentId]: {
          ...state[action.payload.instrumentId],
          loading: true,
        },
      };

    case GET_INSTRUMENT_STATS_REJECTED:
      return {
        ...state,
        [action.payload.instrumentId]: {
          data: null,
          errors: action.payload.errors,
          loading: false,
        },
      };

    case GET_INSTRUMENT_STATS_FULFILLED:
      return {
        ...state,
        [action.payload.instrumentId]: {
          data: action.payload.data,
          errors: null,
          loading: false,
        },
      };

    default:
      return state;
  }
};

export default statsReducer;

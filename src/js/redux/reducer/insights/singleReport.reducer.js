import intersectionBy from 'lodash/intersectionBy';
import { addSelected<PERSON>ield, preventReorder } from 'util/insights';
import { AGGREGATED_RESULTS_VIEW } from 'constants/field-chooser';
import { INSIGHT_DATA } from 'view/modules/insights/config/insight-columns-by-module-settings';
import { mapFStringToArray } from 'js/util/mapFStringToArray';

import {
  ADD_COLUMN,
  SET_CATEGORY,
  SET_PREFILTER,
  SWAP_COLUMNS,
  UPDATE_METRIC,
  ON_FIELD_DROP,
  RESET_INSIGHT,
  REMOVE_COLUMN,
  UPDATE_CURRENCY,
  UPDATE_DATETIME,
  SWITCH_TABLE_VIEW,
  REMOVE_ALL_COLUMNS,
  READ_REPORT_FAILED,
  REPORTS_RESET,
  READ_REPORT_STARTED,
  UPDATE_SEARCH_RESULTS,
  CREATE_INSIGHT_MODULE,
  READ_REPORT_COMPLETED,
  INSIGHTS_REPORTS_LOADING,
  UPDATE_COLUMN_SEARCH_TERM,
  UPDATE_METRIC_SEARCH_TERM,
  UPDATE_PIVOT_SEARCH_RESULTS,
  FILTER_INSIGHTS_DATA_FIELDS,
  UPDATE_DETAIL_SEARCH_RESULTS,
  RECORDS_COUNTS_FOR_INSIGHTS_LOADING,
  RECORDS_COUNTS_FOR_INSIGHTS_FAILED,
  RECORDS_COUNTS_FOR_INSIGHTS_LOADED,
} from 'constants/actions/insights';

// TODO: Create sample single report structure
// TODO: Handle loading states better
// TODO: Breakdown state one for each component

const singleReportReducer = (state = {}, { type, payload }) => {
  const payloadReportId = payload?.reportId || 'new';
  const reportDataInState = state[payload?.module]?.[payloadReportId] || {};
  const selectedFieldsInState = reportDataInState.selectedFields || [];
  const searchDataInState = reportDataInState.search || {};
  switch (type) {
    case CREATE_INSIGHT_MODULE:
      return {
        ...state,
        [payload.module]: {},
      };
    case UPDATE_METRIC:
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            selectedFields: selectedFieldsInState.map(f => {
              if (f.propertyUIId === payload.propertyUIId) {
                return {
                  ...f,
                  chosenAgg: payload.chosenAgg,
                };
              }
              return f;
            }),
          },
        },
      };
    case UPDATE_CURRENCY:
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            selectedFields: selectedFieldsInState.map(f => {
              if (f.propertyUIId === payload.propertyUIId) {
                return {
                  ...f,
                  chosenCurrency: payload.chosenCurrency,
                };
              }
              return f;
            }),
          },
        },
      };
    case UPDATE_DATETIME:
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            selectedFields: selectedFieldsInState.map(f => {
              if (f.propertyUIId === payload.propertyUIId) {
                return {
                  ...f,
                  chosenDateTimeFormat: payload.chosenDateTimeFormat,
                };
              }
              return f;
            }),
          },
        },
      };

    case INSIGHTS_REPORTS_LOADING:
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            insightsReportsLoading: true,
            timestamp: payload.timestamp,
          },
        },
      };

    case ADD_COLUMN:
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            metricSelector: null,
            results: [...(searchDataInState.results || [])],
            selectedFields: addSelectedField({
              newField: payload.incomingData,
              selectedFields: selectedFieldsInState,
            }),
            shouldPreventMetricBeforeColumn: false,
          },
        },
      };
    case REMOVE_COLUMN: {
      const hasMoreColumn = !selectedFieldsInState.filter(
        field => field.propertyUIId !== payload.propertyUIId
      ).length;
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            ...(hasMoreColumn && {
              search: {
                detailResults: [],
                pivotResults: [],
                results: [],
                resultsView: AGGREGATED_RESULTS_VIEW,
              },
            }),
            selectedFields: selectedFieldsInState.filter(
              field => field.propertyUIId !== payload.propertyUIId
            ),
            shouldPreventMetricBeforeColumn: false,
          },
        },
      };
    }
    case REMOVE_ALL_COLUMNS:
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            insightsReportsById: null,
            insightsReportsLoading: false,
            selectedFields: [],
            shouldPreventMetricBeforeColumn: false,
            metricSelector: {
              isVisible: false,
              posX: null,
              posY: null,
            },
            search: {
              detailResults: [],
              pivotResults: [],
              results: [],
              resultsView: AGGREGATED_RESULTS_VIEW,
            },
          },
        },
      };
    case SWAP_COLUMNS: {
      const currentField = selectedFieldsInState.find(
        f => f.propertyUIId === payload.currentField.propertyUIId
      );
      const nextField = selectedFieldsInState.find(
        f => f.propertyUIId === payload.nextField.propertyUIId
      );
      const currentFieldPosition = selectedFieldsInState.indexOf(currentField);
      const nextFieldPosition = selectedFieldsInState.indexOf(nextField);
      const shouldPreventMetricBeforeColumn = preventReorder(
        currentField,
        currentFieldPosition,
        nextField,
        nextFieldPosition
      );

      const swappedSelectedFields = shouldPreventMetricBeforeColumn
        ? selectedFieldsInState
        : selectedFieldsInState.map(f => {
            if (f.propertyUIId === payload.currentField.propertyUIId) {
              return nextField;
            }
            if (f.propertyUIId === payload.nextField.propertyUIId) {
              return currentField;
            }
            return f;
          });
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            selectedFields: [...swappedSelectedFields],
            shouldPreventMetricBeforeColumn,
          },
        },
      };
    }
    case READ_REPORT_STARTED:
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            insightsReportsLoading: true,
            metricSelector: {
              isVisible: false,
              posX: null,
              posY: null,
            },
            search: {
              detailResults: [],
              pivotResults: [],
              results: [],
              resultsView: AGGREGATED_RESULTS_VIEW,
            },
            selectedFields: [],
          },
        },
      };
    case READ_REPORT_COMPLETED: {
      if (reportDataInState.timestamp > payload.timestamp) return state;
      const readResultFields = payload.readResult.fields;

      const dataMapByModule = intersectionBy(
        INSIGHT_DATA[payload.module],
        readResultFields,
        'propertyId'
      );
      const count = {};
      const UIMapped = readResultFields.reduce((acc, curr) => {
        const countId = curr.propertyId;
        count[countId] = count[countId] ? count[countId] + 1 : 1;

        const aggOptions = dataMapByModule?.find(
          ({ propertyId }) => propertyId === curr.propertyId
        )?.aggOptions;
        acc.push({
          ...curr,
          propertyUIId: count[countId] ? countId + count[countId] : countId,
          ...(aggOptions && { aggOptions }),
          ...(curr.filter && { filter: mapFStringToArray(curr.filter) }),
        });
        return acc;
      }, []);

      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            insightsReportsById: payload.readResult,
            insightsReportsLoading: false,
            selectedFields: UIMapped,
            timestamp: payload.timestamp,
          },
        },
      };
    }
    case READ_REPORT_FAILED:
      if (reportDataInState?.timestamp > payload.timestamp) return state;
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            insightsReportsLoading: false,
            selectedFields: [],
            timestamp: payload.timestamp,
          },
        },
      };

    // TODO - possible remove
    // could be useless
    case FILTER_INSIGHTS_DATA_FIELDS:
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            filtered: INSIGHT_DATA[payload.module].filter(
              d => d.displayName.toLowerCase().indexOf(payload) > -1
            ),
          },
        },
      };
    case SET_CATEGORY:
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            selectedCategory: payload.category,
          },
        },
      };
    case SET_PREFILTER:
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            selectedPreFilter: payload.preFilter,
          },
        },
      };
    case UPDATE_METRIC_SEARCH_TERM:
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            metricSearchTerm: payload.metricSearchTerm,
          },
        },
      };
    case ON_FIELD_DROP: {
      const metricSelectorUpdate =
        payload.data.aggOptions || payload.data.formatDateOptions || payload.data.filterOptions
          ? {
              data: payload.data,
              position: {
                x: payload.position.x,
                y: payload.position.y,
              },
              shouldDisplay: true,
            }
          : null;

      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            metricSelector: metricSelectorUpdate,
            shouldPreventMetricBeforeColumn: false,
          },
        },
      };
    }
    case UPDATE_COLUMN_SEARCH_TERM:
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            columnSearchTerm: payload.columnSearchTerm,
          },
        },
      };
    // TODO - possible remove
    // could be useless
    case UPDATE_DETAIL_SEARCH_RESULTS:
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            search: {
              ...searchDataInState,
              detailResults: [...payload.results],
            },
          },
        },
      };
    // could be useless
    case UPDATE_PIVOT_SEARCH_RESULTS:
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            search: {
              ...searchDataInState,
              pivotResults: [...payload.results],
            },
          },
        },
      };
    case UPDATE_SEARCH_RESULTS: {
      if (reportDataInState.timestamp > payload.timestamp) return state;
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            insightsReportsLoading: false,
            search: {
              ...searchDataInState,
              ...payload.search,
              totalDetailResultsHit: payload.totalHits,
            },
            shouldPreventMetricBeforeColumn: false,
            timestamp: payload.timestamp,
          },
        },
      };
    }
    case SWITCH_TABLE_VIEW:
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          [payloadReportId]: {
            ...reportDataInState,
            search: {
              ...searchDataInState,
              resultsView:
                payload.resultsView || searchDataInState.resultsView || AGGREGATED_RESULTS_VIEW,
            },
          },
        },
      };

    case REPORTS_RESET:
      return {
        ...state,
        [payload.module]: {},
      };
    case RECORDS_COUNTS_FOR_INSIGHTS_LOADING:
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          records: {
            loading: true,
            error: null,
            count: 0,
          },
        },
      };
    case RECORDS_COUNTS_FOR_INSIGHTS_LOADED:
      if (state[payload.module]?.timestamp > payload.timestamp) return state;
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          records: {
            loading: false,
            error: null,
            count: payload.count,
          },
          timestamp: payload.timestamp,
        },
      };
    case RECORDS_COUNTS_FOR_INSIGHTS_FAILED:
      if (state[payload.module]?.timestamp > payload.timestamp) return state;
      return {
        ...state,
        [payload.module]: {
          ...state[payload.module],
          records: {
            loading: false,
            error: payload.error,
            count: 0,
          },
          timestamp: payload.timestamp,
        },
      };
    case RESET_INSIGHT:
      return state;

    default:
      return state;
  }
};

export default singleReportReducer;

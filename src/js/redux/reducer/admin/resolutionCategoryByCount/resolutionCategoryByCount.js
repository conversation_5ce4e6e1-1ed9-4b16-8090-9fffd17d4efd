import {
  GET_RESOLUTION_CATEGORY_BY_COUNT_LOADING,
  GET_RESOLUTION_CATEGORY_BY_COUNT_FULFILLED,
  GET_RESOLUTION_CATEGORY_BY_COUNT_REJECTED,
} from 'js/actions/admin/admin-actions-constants';

const initialState = {
  data: [],
  error: null,
  loading: false,
};

const resolutionCategoryByCount = (state = initialState, action) => {
  switch (action.type) {
    case GET_RESOLUTION_CATEGORY_BY_COUNT_LOADING:
      return {
        ...state,
        loading: true,
      };
    case GET_RESOLUTION_CATEGORY_BY_COUNT_FULFILLED:
      return {
        ...state,
        data: action.payload,
        error: null,
        loading: false,
      };
    case GET_RESOLUTION_CATEGORY_BY_COUNT_REJECTED:
      return {
        ...state,
        data: [],
        error: action.payload,
        loading: false,
      };
    default:
      return state;
  }
};

export default resolutionCategoryByCount;

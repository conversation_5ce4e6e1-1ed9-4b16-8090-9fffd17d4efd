import {
  SW_ALERT_SUMMARY_BY_TREND_LOADING,
  SW_ALERT_SUMMARY_BY_TREND_REJECTED,
  SW_ALERT_SUMMARY_BY_TREND_FULFILLED,
} from 'actions/trade-surv/single-watch-actions-constants';

const summaryByTrend = (state = {}, { type, payload }) => {
  switch (type) {
    case SW_ALERT_SUMMARY_BY_TREND_LOADING:
      return {
        ...state,
        [payload.watchId]: {
          ...state[payload.watchId],
          [payload.trend]: {
            ...state[payload.watchId]?.[payload.trend],
            loading: true,
          },
        },
      };

    case SW_ALERT_SUMMARY_BY_TREND_REJECTED:
      return {
        ...state,
        [payload.watchId]: {
          ...state[payload.watchId],
          [payload.trend]: {
            ...state[payload.watchId]?.[payload.trend],
            data: null,
            errors: payload.response,
            loading: false,
          },
        },
      };

    case SW_ALERT_SUMMARY_BY_TREND_FULFILLED:
      return {
        ...state,
        [payload.watchId]: {
          ...state[payload.watchId],
          [payload.trend]: {
            ...state[payload.watchId]?.[payload.trend],
            data: payload.data,
            errors: null,
            loading: false,
          },
        },
      };
    default:
      return state;
  }
};

export default summaryByTrend;

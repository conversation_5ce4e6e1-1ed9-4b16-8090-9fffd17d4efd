import {
  SW_TRADE_ALERT_SUMMARY_BY_DONUT_STATUS_LOADING,
  SW_TRADE_ALERT_SUMMARY_BY_DONUT_STATUS_REJECTED,
  SW_TRADE_ALERT_SUMMARY_BY_DONUT_STATUS_FULFILLED,
} from 'js/actions/trade-surv/single-watch-actions-constants';

const summaryByDonutStatus = (state = {}, { type, payload }) => {
  switch (type) {
    case SW_TRADE_ALERT_SUMMARY_BY_DONUT_STATUS_LOADING:
      return {
        ...state,
        [payload.watchId]: {
          ...state[payload.watchId],
          [payload.trend]: {
            ...state[payload.watchId]?.[payload.trend],
            loading: true,
          },
        },
      };

    case SW_TRADE_ALERT_SUMMARY_BY_DONUT_STATUS_REJECTED:
      return {
        ...state,
        [payload.watchId]: {
          ...state[payload.watchId],
          [payload.trend]: {
            data: null,
            errors: payload.response,
            loading: false,
          },
        },
      };

    case SW_TRADE_ALERT_SUMMARY_BY_DONUT_STATUS_FULFILLED:
      return {
        ...state,
        [payload.watchId]: {
          ...state[payload.watchId],
          [payload.trend]: {
            data: payload.data,
            errors: null,
            loading: false,
          },
        },
      };
    default:
      return state;
  }
};

export default summaryByDonutStatus;

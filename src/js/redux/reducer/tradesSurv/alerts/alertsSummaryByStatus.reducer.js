import {
  ALERTS_SUMMARY_BY_STATUSES_LOADING,
  ALERTS_SUMMARY_BY_STATUSES_REJECTED,
  ALERTS_SUMMARY_BY_STATUSES_FULFILLED,
} from 'constants/actions/trades-surv';

const alertsSummaryByStatusReducer = (state = {}, { type, payload }) => {
  switch (type) {
    case ALERTS_SUMMARY_BY_STATUSES_LOADING:
      return {
        ...state,
        loading: true,
      };
    case ALERTS_SUMMARY_BY_STATUSES_REJECTED:
      return {
        ...state,
        loading: false,
        data: null,
      };
    case ALERTS_SUMMARY_BY_STATUSES_FULFILLED:
      return {
        ...state,
        loading: false,
        data: payload.data,
      };
    default:
      return state;
  }
};

export default alertsSummaryByStatusReducer;

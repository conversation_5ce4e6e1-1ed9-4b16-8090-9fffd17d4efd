import {
  GET_SINGLE_ORDER_LOADING,
  GET_SINGLE_ORDER_REJECTED,
  GET_SINGLE_ORDER_FULFILLED,
} from 'constants/actions/best-execution';

const singleOrder = (state = {}, { type, payload }) => {
  switch (type) {
    case GET_SINGLE_ORDER_LOADING:
      return {
        ...state,
        [payload.orderId]: {
          ...state[payload.orderId],
          loading: true,
        },
      };
    case GET_SINGLE_ORDER_REJECTED:
      return {
        ...state,
        [payload.orderId]: {
          ...state[payload.orderId],
          data: null,
          errors: payload.errors,
          loading: false,
        },
      };
    case GET_SINGLE_ORDER_FULFILLED:
      return {
        ...state,
        [payload.orderId]: {
          ...state[payload.orderId],
          loading: false,
          data: payload.response,
        },
      };
    default:
      return state;
  }
};

export default singleOrder;

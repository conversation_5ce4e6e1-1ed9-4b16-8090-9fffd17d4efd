import {
  COMMS_SURV_DASHBOARD_SUMMARY_BY_PEOPLE_FULFILLED,
  COMMS_SURV_DASHBOARD_SUMMARY_BY_PEOPLE_REJECTED,
  COMMS_SURV_DASHBOARD_SUMMARY_BY_PEOPLE_LOADING,
} from 'constants/actions/comms-surv';

const summaryByPeopleReducer = (state = {}, { type, payload }) => {
  switch (type) {
    case COMMS_SURV_DASHBOARD_SUMMARY_BY_PEOPLE_LOADING:
      return {
        ...state,
        loading: true,
      };

    case COMMS_SURV_DASHBOARD_SUMMARY_BY_PEOPLE_REJECTED:
      return {
        ...state,
        data: null,
        errors: payload.errors,
        loading: false,
      };

    case COMMS_SURV_DASHBOARD_SUMMARY_BY_PEOPLE_FULFILLED:
      return {
        ...state,
        data: payload.data,
        errors: null,
        loading: false,
      };
    default:
      return state;
  }
};

export default summaryByPeopleReducer;

import {
  LEXICA_BACKTEST_SUMMARY_LOADING,
  LEXICA_BACKTEST_SUMMARY_REJECTED,
  LEXICA_BACKTEST_SUMMARY_FULFILLED,
} from 'js/constants/actions/comms-surv';

const lexicaBacktestSummaryReducer = (
  state = {
    withoutFuzziness: null,
    fuzziness: null,
    withoutStemming: null,
    stemming: null,
  },
  action
) => {
  switch (action.type) {
    case LEXICA_BACKTEST_SUMMARY_LOADING:
      return {
        ...state,
        [action.payload.key]: {
          ...state[action.payload.key],
          loading: true,
        },
      };
    case LEXICA_BACKTEST_SUMMARY_REJECTED:
      if (state[action.payload.key]?.timestamp > action.payload.timestamp) return state;
      return {
        ...state,
        [action.payload.key]: {
          ...state[action.payload.key],
          data: null,
          errors: action.payload.errors,
          loading: false,
          timestamp: action.payload.timestamp,
        },
      };
    case LEXICA_BACKTEST_SUMMARY_FULFILLED:
      if (state[action.payload.key]?.timestamp > action.payload.timestamp) return state;
      return {
        ...state,
        [action.payload.key]: {
          ...state[action.payload.key],
          data: action.payload.data,
          errors: null,
          loading: false,
          timestamp: action.payload.timestamp,
        },
      };
    default:
      return state;
  }
};

export default lexicaBacktestSummaryReducer;

import backoff
import logging
from datetime import datetime as dt
from efdh_api.schemas.refinitiv import ExtractionCreate, ExtractionStatus, ExtractionUpdate
from reference_db.models.instrument_list_extraction import InstrumentListExtraction
from sqlalchemy import desc, update
from typing import Any, Dict, List

log = logging.getLogger(__name__)


class RefinitivRepo:
    def __init__(self, session_factory):
        self.session_factory = session_factory

    def incomplete_extractions(self) -> List[Dict[str, Any]]:
        with self.session_factory() as session:
            q = (
                session.query(InstrumentListExtraction)
                .filter(InstrumentListExtraction.status == ExtractionStatus.RUNNING.value)
                .order_by(desc(InstrumentListExtraction.createdDateTime))
            )
            return q.all()  # type: ignore

    def failed_extractions(self) -> List[Dict[str, Any]]:
        with self.session_factory() as session:
            q = (
                session.query(InstrumentListExtraction)
                .filter(InstrumentListExtraction.status == ExtractionStatus.FAILED.value)
                .order_by(desc(InstrumentListExtraction.createdDateTime))
            )
            return q.all()  # type: ignore

    def successful_extractions(self) -> List[Dict[str, Any]]:
        with self.session_factory() as session:
            q = (
                session.query(InstrumentListExtraction)
                .filter(InstrumentListExtraction.status == ExtractionStatus.SUCCESS.value)
                .order_by(desc(InstrumentListExtraction.createdDateTime))
            )
            return q.all()  # type: ignore

    def create_extraction(self, _input: ExtractionCreate) -> str:
        with self.session_factory() as session:
            obj = InstrumentListExtraction(**_input.dict())
            session.add(obj)
            session.commit()
            session.refresh(obj)
            return obj.id

    def update_extraction(self, id: str, query: ExtractionUpdate) -> bool:
        body = {k: v for k, v in query.dict().items() if v is not None}
        with self.session_factory() as session:
            session.execute(
                update(InstrumentListExtraction)
                .where(InstrumentListExtraction.id == id)
                .values(**body)  # type: ignore
            )
            session.commit()
        return True

    @backoff.on_exception(
        backoff.expo,
        exception=Exception,
        max_tries=3,
    )
    def increment_batches(self, id: str) -> int:
        with self.session_factory() as session:
            row = (
                session.query(InstrumentListExtraction)
                .filter(InstrumentListExtraction.id == id)
                .with_for_update()
                .one()
            )

            row.completedBatches += 1
            if row.completedBatches == row.totalBatches:
                row.status = ExtractionStatus.SUCCESS.value
                row.completionDateTime = str(dt.now())
            session.commit()
            return int(row.completedBatches)

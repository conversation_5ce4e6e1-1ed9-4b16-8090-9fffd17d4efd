import datetime
import logging
import pyarrow as pa
import pyarrow.compute as pc
import pyarrow.dataset as ds
from dependency_injector.wiring import Provide, inject
from efdh_api.schemas.order_book import MAX_OBD_LEVELS, OrderBookDataFrame, OrderBookWrapper
from efdh_api.services.instruments_level2_service import InstrumentLevel2Service
from efdh_api.utils.containers import Container
from efdh_utils.efdh_config import efdh_utils_config
from efdh_utils.parquet_handlers import get_level2_tick_parquet, get_tick_filepath_for_date
from efdh_utils.schema.parquet import ResolutionLevel2
from efdh_utils.schema.refinitiv import RefinitivEventType, RefinitivOrderBookDepthColumns
from fastapi import APIRouter, Depends, Query
from time import time
from typing import Annotated

log = logging.getLogger(__name__)

router = APIRouter()

MAX_DATA_POINTS = 1_000


# owner: confirmed used by mar, api, FE


def s_to_ms(tyme: float) -> int:
    """A very trivial function to make s -> ms conversion more readable."""
    return int(tyme * 1_000)


def ns_to_ms(ns: int) -> int:
    """A very trivial function to make ns -> ms conversion more readable."""
    return round(ns / 1_000_000)


def obd_resolution(timespan_ms: int) -> ResolutionLevel2:
    if timespan_ms < 5 * 60 * 1000:  # 5min
        return ResolutionLevel2.TICK
    if timespan_ms < 15 * 60 * 1000:  # 15min
        return ResolutionLevel2.MILLISECOND
    if timespan_ms < 2 * 60 * 60 * 1000:  # 2h
        return ResolutionLevel2.SECOND
    return ResolutionLevel2.MINUTE


@router.get("/{key}", name="obd:ric:get-data", response_model=OrderBookWrapper)
@inject
async def lvl2_quotes_between_timestamps(
    key: str,
    instrument_level2_service: InstrumentLevel2Service = Depends(
        Provide[Container.instrument_level2_service]
    ),
    from_timestamp_ms: int = Query(None, alias="start"),
    interval: float = None,
    to_timestamp_ms: int = Query(None, alias="end"),
):
    start_ms = from_timestamp_ms or s_to_ms(time() - 3600.0)  # 1h ago
    end_ms = to_timestamp_ms or s_to_ms(time())  # now in ms
    timespan_ms = end_ms - start_ms
    interval = (
        interval or timespan_ms / MAX_DATA_POINTS
    )  # work out interval to have max MAX_DATA_POINTS
    info = instrument_level2_service.get_instrument_info(key)
    resolution = obd_resolution(timespan_ms)

    from_timestamp_ns = int(from_timestamp_ms * 1e6)
    to_timestamp_ns = int(to_timestamp_ms * 1e6)

    pa_table_iter = get_level2_tick_parquet(
        from_timestamp_ns=from_timestamp_ns,
        to_timestamp_ns=to_timestamp_ns,
        ric=info.ric,
        resolution=resolution,
        columns=[
            RefinitivOrderBookDepthColumns.DATE_TIME,
            *OrderBookDataFrame.level_columns_for_depth(info.depth),
        ],
    )
    pa_df = pa.Table.from_batches(pa_table_iter)

    return OrderBookWrapper(
        ric=info.ric,
        maxDepth=info.depth,
        data=OrderBookDataFrame(pa_df, interval_ns=int(interval * 1e6)).as_orderbooks(),
    )


@router.get("/{key}/balance")
@inject
def ric_obd_balance(
    key: str,
    instrument_level2_service: InstrumentLevel2Service = Depends(
        Provide[Container.instrument_level2_service]
    ),
    from_timestamp_ms: int = Query(None, alias="start"),
    to_timestamp_ms: int = Query(None, alias="end"),
    depth: Annotated[int | None, Query(le=MAX_OBD_LEVELS)] = MAX_OBD_LEVELS,
):
    # TODO: memory could run out here if someone passes large time frame --
    #  MAX Data points helps partially mitigate but can still timeout / OOM
    from_timestamp_ms = from_timestamp_ms or s_to_ms(time() - 3600.0)  # 1h ago
    to_timestamp_ms = to_timestamp_ms or s_to_ms(time())  # now in ms
    timespan = to_timestamp_ms - from_timestamp_ms
    interval = timespan // MAX_DATA_POINTS  # work out interval to have max MAX_DATA_POINTS

    info = instrument_level2_service.get_instrument_info(key)

    # Don't go deeper than the instrument's order book!
    depth = min(depth, info.depth)
    cumulative_volumes = []
    num_records = 0
    from_date = datetime.date.fromtimestamp(from_timestamp_ms // 1e3)
    to_date = datetime.date.fromtimestamp(to_timestamp_ms // 1e3)
    resolution = obd_resolution(timespan)
    from_timestamp_ns = int(from_timestamp_ms * 1e6)
    to_timestamp_ns = int(to_timestamp_ms * 1e6)

    # from_date = datetime.date.fromtimestamp(from_timestamp_ns / 1e9)
    # to_date = datetime.date.fromtimestamp(to_timestamp_ns / 1e9)
    dates = [from_date + datetime.timedelta(days=i) for i in range((to_date - from_date).days + 1)]

    s3_keys = [
        get_tick_filepath_for_date(
            ric=info.ric, event_type=RefinitivEventType.OBD, date=x, resolution=resolution
        )
        for x in dates
    ]
    # use the master data bucket for OBD data
    for key in s3_keys:
        pa_df = (
            ds.dataset(
                f"s3://{efdh_utils_config.efdh_level2_obd_data_bucket}/{key}", format="parquet"
            )
            .scanner(
                filter=pc.field(RefinitivOrderBookDepthColumns.DATE_TIME)
                >= from_timestamp_ns & pc.field(RefinitivOrderBookDepthColumns.DATE_TIME)
                <= to_timestamp_ns,
                columns=[
                    RefinitivOrderBookDepthColumns.DATE_TIME,
                    *OrderBookDataFrame.level_columns_for_depth(info.depth),
                ],
            )
            .to_table()
        )

        if pa_df.num_rows == 0:
            continue
        for cumulative_vol, _num_records in OrderBookDataFrame(
            pa_df, interval_ns=int(interval * 1e6)
        ).as_cumulative_volumes(depth, num_records=num_records):
            cumulative_volumes.append(cumulative_vol)

    return cumulative_volumes


# @router.get("/{key}/meta", response_model=RicMeta)
# @inject
# def ric_obd_meta(
#     key: str,
#     instrument_info_service: InstrumentInfoService = Depends(
#         Provide[Container.instrument_info_service]
#     ),
#     order_book_service: OrderBookService = ReqDep(OrderBookService),
#     start: Optional[date] = None,
#     end: Optional[date] = None,
# ):
#     info = instrument_info_service.get_instrument_info(key)
#
#     date_span = (
#         order_book_service.date_frame(from_date=start, to_date=end) if start and end else None
#     )
#
#     try:
#         df = order_book_service.meta_between_dates_for_ric(ric=info.ric, date_span=date_span)
#         return RicMeta.from_dataframe(df)
#     except (OBDNotFound, NotFound):
#         log.info("OBD data not found for '%s' between %s and %s", key, start, end)
#         # No data, but the instrument exists, so not quite an error.
#         raise NotFound("Metadata for", key)

import logging
from aries_utils.kafka_rest import KafkaRestClient
from dependency_injector import containers, providers
from efdh_api.services.ecb_euro_ref_rates import ECBEuroRefRateRepository
from efdh_api.services.instrument_repository import InstrumentsRepository
from efdh_api.services.instruments_level2_service import InstrumentLevel2Service
from efdh_api.services.leis import LeiRepository
from efdh_api.services.market_data import MarketDataService
from efdh_api.services.mic import MicService
from efdh_api.services.news import NewsRepo
from efdh_api.services.perm_ids import PermIDsRepo
from efdh_api.services.potam import PotamRepository
from efdh_api.services.refine import RefineSchemaRepository
from efdh_api.services.refinitiv import RefinitivRepo
from efdh_api.services.ric_currency_repo import RicCurrencyRepo
from efdh_api.services.ric_level2 import RicLevel2Service
from efdh_api.services.ric_mapping import RicMappingService
from efdh_api.services.ric_repo import RicRepository
from efdh_api.services.s3 import S3Service
from efdh_api.utils.efdh_config import EFDHConfig
from efdh_api.utils.news_sdk.client import NewsDataClient
from elasticsearch8 import Elasticsearch
from se_db_utils.database import Database, DatabaseConnectionConfig


class Container(containers.DeclarativeContainer):
    """Main container for all dependencies and configuration."""

    # Entry point of all dependency injection
    wiring_config = containers.WiringConfiguration(packages=["efdh_api.api"])

    # add config to container
    config: providers.Resource[EFDHConfig] = providers.Resource(EFDHConfig)

    # configure logging
    providers.Resource(
        logging.basicConfig,
        format="%(asctime)s | %(name)s | %(levelname)s | %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    db = providers.Singleton(
        Database,
        db_url=config.provided.efdh_db_url,
        database_connection_config=DatabaseConnectionConfig.API,
        echo=True if config.provided.debug else False,
    )
    es_client = providers.Resource(
        Elasticsearch,
        hosts=config.provided.elastic_url,
        api_key=config.provided.elastic_api_key,
        verify_certs=False,
        request_timeout=config.provided.elastic_request_timeout,
    )

    kafka_client = providers.Resource(
        KafkaRestClient, kafka_rest_proxy_url=config.provided.kafka_rest_proxy_url
    )

    ecb_euro_ref_rate_repository = providers.Singleton(
        ECBEuroRefRateRepository,
        es_client=es_client,
        config=config,
    )

    refinitiv_repository = providers.Singleton(
        RefinitivRepo,
        session_factory=db.provided.session,
    )

    lei_repository = providers.Singleton(
        LeiRepository,
        es_client=es_client,
        config=config,
    )

    instruments_repository = providers.Singleton(
        InstrumentsRepository,
        es_client=es_client,
        config=config,
    )

    ric_repository = providers.Singleton(
        RicRepository,
        config=config,
        session_factory=db.provided.session,
    )

    s3_service = providers.Singleton(
        S3Service,
    )

    ric_level2_service = providers.Singleton(
        RicLevel2Service,
        session_factory=db.provided.session,
        config=config,
        kafka_client=kafka_client,
    )

    ric_mapping_service = providers.Singleton(
        RicMappingService,
        instruments_repo=instruments_repository,
        ric_repository=ric_repository,
        config=config,
        ric_level2_service=ric_level2_service,
    )

    mic_service = providers.Singleton(MicService, session_factory=db.provided.session)

    news_data_client = providers.Singleton(
        NewsDataClient,
    )

    perm_ids_repo = providers.Singleton(
        PermIDsRepo,
        session_factory=db.provided.session,
        news_data_client=news_data_client,
    )

    refinitiv_repo = providers.Singleton(
        RefinitivRepo,
        session_factory=db.provided.session,
    )

    market_data_service = providers.Singleton(
        MarketDataService,
        ric_level2_service=ric_level2_service,
    )

    news_repo = providers.Singleton(
        NewsRepo,
        session_factory=db.provided.session,
    )

    potam_repository = providers.Singleton(
        PotamRepository,
        es_client=es_client,
        config=config,
    )

    refine_schema_repository = providers.Singleton(
        RefineSchemaRepository,
        es_client=es_client,
        config=config,
    )

    ric_currency_repo = providers.Singleton(
        RicCurrencyRepo,
        session_factory=db.provided.session,
    )

    instrument_level2_service = providers.Singleton(
        InstrumentLevel2Service,
        ric_level2_service=ric_level2_service,
        mic_service=mic_service,
    )

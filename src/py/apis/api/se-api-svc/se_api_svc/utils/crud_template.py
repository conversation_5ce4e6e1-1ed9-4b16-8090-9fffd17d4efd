# type: ignore
import copy
import datetime as dt
from api_sdk.auth import Tenancy
from api_sdk.di.request import ReqDep
from api_sdk.es_dsl.base import SearchBase, SearchModel
from api_sdk.messages.fastapi_message_bus import FastApiMessageBus
from api_sdk.models.search import PageParams, SearchResult
from api_sdk.repository.asyncronous.request_bound import RequestBoundRepository
from api_sdk.schemas.base import APIModel, AuditFields, RecordModel
from fastapi import APIRouter, Depends
from se_api_svc.messages.crud_endpoint import RecordCreated, RecordDeleted, RecordUpdated
from typing import List, Optional, Type

CRUD_LIST = 1
CRUD_CREATE = 2
CRUD_READ = 4
CRUD_UPDATE = 8
CRUD_DELETE = 16
CRUD_ALL = CRUD_LIST | CRUD_CREATE | CRUD_READ | CRUD_UPDATE | CRUD_DELETE


def add_crud_endpoints(
    router: APIRouter,
    model: Type[RecordModel],
    slug: str = "",
    repo_cls: Type = None,
    search_model_cls: Type[SearchBase] = SearchModel,
    create_model_in: Type[APIModel] = None,  # defaults to model
    update_model_in: Type[APIModel] = None,  # defaults to model
    response_model: Type[APIModel] = None,  # defaults to model
    name_prefix: str = None,
    crud: int = CRUD_ALL,
    require_permissions=None,
    route_tags: List[str] = None,
    exlude_none_on_update: bool = True,
):
    repo_cls = repo_cls or RequestBoundRepository
    create_model_in = create_model_in or model
    update_model_in = update_model_in or model
    response_model = response_model or model

    if name_prefix:
        name_prefix = f"{name_prefix.rstrip(':')}:{slug}".rstrip(":")
    else:
        name_prefix = slug

    if slug:
        assert slug[0] != "/", "Do not start slug with /"
        slug = f"/{slug}"

    if crud & CRUD_LIST:

        @router.get(f"{slug}", name=f"{name_prefix}:get-all", tags=route_tags)
        async def list_records(
            search: Optional[str] = None,
            page_params: PageParams = Depends(),
            repo: repo_cls = ReqDep(repo_cls),
            mb: FastApiMessageBus = Depends(),
        ):
            if require_permissions:
                repo.tenancy.require_permissions(require_permissions)
            result = await repo.get_many(
                record_model=model,
                search_model_cls=search_model_cls,
                pagination=page_params.to_pagination(),
                model_qs=search,
            )
            return SearchResult.from_raw_result(result, skipped_hits=page_params.skip)

    if crud & CRUD_CREATE:

        @router.post(
            f"{slug}",
            name=f"{name_prefix}:create",
            response_model=response_model,
            tags=route_tags,
        )
        async def create_record(
            record: create_model_in,
            tenancy: Tenancy = ReqDep(Tenancy),
            repo: repo_cls = ReqDep(repo_cls),
            mb: FastApiMessageBus = Depends(),
        ):
            if require_permissions:
                repo.tenancy.require_permissions(require_permissions)
            audit = {}
            if issubclass(create_model_in, AuditFields):
                audit["createdBy"] = audit["updatedBy"] = tenancy.principal
            obj = model.create_from(record, **audit)
            await repo.save_new(obj)

            await mb.publish(
                RecordCreated(
                    created_record=obj.to_es_dict(),
                    model=model.__config__.model_name,
                )
            )

            return obj

    if crud & CRUD_READ:

        @router.get(
            f"{slug}/" + "{id}",
            name=f"{name_prefix}:get-one",
            response_model=response_model,
            tags=route_tags,
        )
        async def get_record(
            id: str,
            timestamp: Optional[int] = None,
            repo: repo_cls = ReqDep(repo_cls),
            mb: FastApiMessageBus = Depends(),
        ):
            if require_permissions:
                repo.tenancy.require_permissions(require_permissions)
            return await repo.get_one(model, id, timestamp=timestamp)

    if crud & CRUD_UPDATE:

        @router.put(
            f"{slug}/" + "{id}",
            name=f"{name_prefix}:update",
            response_model=response_model,
            tags=route_tags,
        )
        async def update_record(
            id: str,
            timestamp: int,
            record: update_model_in,
            tenancy: Tenancy = ReqDep(Tenancy),
            repo: repo_cls = ReqDep(repo_cls),
            mb: FastApiMessageBus = Depends(),
        ):
            if require_permissions:
                repo.tenancy.require_permissions(require_permissions)
            obj: model = await repo.get_one(model, id, timestamp=timestamp)
            original_record = copy.deepcopy(obj)

            audit = {}
            if issubclass(update_model_in, AuditFields):
                audit["updated"] = dt.datetime.utcnow()
                audit["updatedBy"] = tenancy.principal
            obj.update_from(record, **audit)

            await repo.save_existing(obj, exclude_none=exlude_none_on_update)

            await mb.publish(
                RecordUpdated(
                    old_record=original_record.to_es_dict(),
                    new_record=obj.to_es_dict(),
                    model=model.__config__.model_name,
                )
            )
            return obj

    if crud & CRUD_DELETE:

        @router.delete(
            f"{slug}/" + "{id}",
            name=f"{name_prefix}:delete",
            response_model=response_model,
            tags=route_tags,
        )
        async def delete_record(
            id: str,
            repo: repo_cls = ReqDep(repo_cls),
            mb: FastApiMessageBus = Depends(),
        ):
            if require_permissions:
                repo.tenancy.require_permissions(require_permissions)
            obj: model = await repo.get_one(model, id)
            await repo.delete_existing(obj)

            await mb.publish(
                RecordDeleted(
                    deleted_record=obj.to_es_dict(),
                    model=model.__config__.model_name,
                )
            )

            return obj

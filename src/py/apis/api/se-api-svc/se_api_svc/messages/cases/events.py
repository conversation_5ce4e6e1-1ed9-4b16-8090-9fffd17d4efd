# type: ignore
from addict import Dict
from api_sdk.messages.base import DomainEvent
from dataclasses import dataclass, field
from se_api_svc.messages.audit.events import (
    RecordCreationEvent,
    RecordDeletionEvent,
    RecordModificationEvent,
    RecordViewEvent,
    SearchEvent,
)
from se_api_svc.messages.email_notification.events import EmailNotificationEvent
from se_api_svc.schemas.cases.cases import (
    Case,
    CaseRecord,
    OtherRole,
    Subject,
    WorkerCustom,
)
from se_api_svc.schemas.cases.event import Event
from se_api_svc.schemas.track import ModuleTitle, UserAudit
from typing import Any, Optional


@dataclass
class CaseManagementEvent(DomainEvent):
    audit_module = ModuleTitle.CASE_MANAGER

    case: Optional[Case] = None

    @property
    def record(self):
        return self.case


@dataclass
class CaseRecordEvent(DomainEvent):
    audit_module = ModuleTitle.CASE_MANAGER
    record_description = (
        "Action on record {record_id}({record_type}) in case {case_id}({case_slug})"
    )

    case: Optional[Case] = None
    case_record: Optional[CaseRecord] = None

    @property
    def record(self):
        return self.case

    @property
    def audit_description(self):
        return self.record_description.format(
            case_id=self.case.id_,
            case_slug=self.case.slug,
            record_id=self.case_record.id_,
            record_type=self.case_record.__class__.__name__,
        )


@dataclass
class CaseRecordsEvent(DomainEvent):
    audit_module = ModuleTitle.CASE_MANAGER

    case: Optional[Dict] = None
    case_record_ids: list = field(default_factory=list)
    record_description = (
        "Following {records_count} records were added to the case:'{case_id}' - {case_record_ids}"
    )

    @property
    def record(self):
        return self.case

    @property
    def audit_description(self):
        return self.record_description.format(
            case_id=self.case.id_,
            case_record_ids=self.case_record_ids,
            records_count=len(self.case_record_ids),
        )


@dataclass
class CasesSearched(CaseManagementEvent, SearchEvent):
    pass


@dataclass
class CaseWorkerSearched(CaseManagementEvent, SearchEvent):
    pass


@dataclass
class CaseViewed(CaseManagementEvent, RecordViewEvent):
    case_slug: Optional[str] = None

    @property
    def audit_description(self):
        return f"User viewed case '{self.case_slug}' details"


@dataclass
class SelerityNewsSearched(CaseManagementEvent, SearchEvent):
    audit_description = "User searched Selerity news articles"


@dataclass
class CaseCreated(CaseManagementEvent, RecordCreationEvent):
    case_slug: Optional[str] = None

    @property
    def audit_description(self):
        return f"User created a case '{self.case_slug}'"


@dataclass
class CaseDeleted(CaseManagementEvent, RecordDeletionEvent):
    case_id: str = None
    audit_description = "User deleted a case"


@dataclass
class CaseUpdated(CaseManagementEvent, RecordModificationEvent):
    case_id: str = None
    case_slug: Optional[str] = None

    @property
    def audit_description(self):
        return f"User modified a case '{self.case_slug}'"


@dataclass
class CaseWorkerAdded(CaseManagementEvent, RecordModificationEvent):
    case_id: str = None
    element: WorkerCustom = None
    audit_description = "User added a case worker"

    def new_audit_record(self, **kwargs) -> UserAudit:
        record = super().new_audit_record(**kwargs)
        record.eventDetails.description = (
            f"User added a case worker for case '{self.case_id}': {self.element.to_dict()}"
        )
        return record


@dataclass
class CaseWorkerDeleted(CaseManagementEvent, RecordModificationEvent):
    case_id: str = None
    element: WorkerCustom = None
    audit_description = "User deleted a case worker"

    def new_audit_record(self, **kwargs) -> UserAudit:
        record = super().new_audit_record(**kwargs)
        record.eventDetails.description = (
            f"User removed a case worker for case '{self.case_id}': {self.element.to_dict()}"
        )
        return record


@dataclass
class CaseSubjectAdded(CaseManagementEvent, RecordModificationEvent):
    case_id: str = None
    element: Subject = None
    audit_description = "User added a case subject"

    def new_audit_record(self, **kwargs) -> UserAudit:
        record = super().new_audit_record(**kwargs)
        record.eventDetails.description = (
            f"User added a case subject for case '{self.case_id}': {self.element.to_dict()}"
        )
        return record


@dataclass
class CaseSubjectDeleted(CaseManagementEvent, RecordModificationEvent):
    case_id: str = None
    element: Subject = None
    audit_description = "User deleted a case subject"

    def new_audit_record(self, **kwargs) -> UserAudit:
        record = super().new_audit_record(**kwargs)
        record.eventDetails.description = (
            f"User deleted a case subject for case '{self.case_id}': {self.element.to_dict()}"
        )
        return record


@dataclass
class CaseRoleAdded(CaseManagementEvent, RecordModificationEvent):
    case_id: str = None
    element: OtherRole = None
    audit_description = "User added a case role"

    def new_audit_record(self, **kwargs) -> UserAudit:
        record = super().new_audit_record(**kwargs)
        record.eventDetails.description = (
            f"User added a case role for case '{self.case_id}': {self.element.to_dict()}"
        )
        return record


@dataclass
class CaseRoleDeleted(CaseManagementEvent, RecordModificationEvent):
    case_id: str = None
    element: OtherRole = None
    audit_description = "User deleted a case subject"

    def new_audit_record(self, **kwargs) -> UserAudit:
        record = super().new_audit_record(**kwargs)
        record.eventDetails.description = (
            f"User deleted a case role for case '{self.case_id}': {self.element.to_dict()}"
        )
        return record


@dataclass
class CaseOpened(CaseManagementEvent, RecordCreationEvent):
    case_slug: Optional[str] = None

    @property
    def audit_description(self):
        return f"User opened a case '{self.case_slug}'"


@dataclass
class CaseReopened(CaseManagementEvent, RecordModificationEvent):
    case_slug: Optional[str] = None

    @property
    def audit_description(self):
        return f"User reopened the case '{self.case_slug}'"


@dataclass
class CaseResolved(CaseManagementEvent, RecordModificationEvent):
    caseSlug: Optional[str] = None
    closedComment: Optional[str] = None
    closedResolutionCategory: Optional[str] = None
    event_description = (
        "User resolved the {caseSlug} with comment "
        '"{closedComment}" and resolution category '
        '"{closedResolutionCategory}"'
    )

    @property
    def audit_description(self):
        return self.event_description.format(
            caseSlug=self.caseSlug,
            closedComment=self.closedComment,
            closedResolutionCategory=self.closedResolutionCategory,
        )


@dataclass
class CaseReopenedNotification(CaseManagementEvent, EmailNotificationEvent):
    pass


@dataclass
class CaseResolvedNotification(CaseManagementEvent, EmailNotificationEvent):
    pass


@dataclass
class CaseReassigned(CaseManagementEvent, RecordModificationEvent):
    pass


@dataclass
class CaseReassignedNotification(CaseManagementEvent, EmailNotificationEvent):
    pass


@dataclass
class CaseOwnershipTransferred(CaseManagementEvent, RecordModificationEvent):
    pass


@dataclass
class CaseOwnershipTransferedNotification(CaseManagementEvent, EmailNotificationEvent):
    pass


@dataclass
class CaseRecordsSearched(CaseManagementEvent, SearchEvent):
    pass


@dataclass
class CaseRecordCreated(CaseRecordEvent, RecordCreationEvent):
    record_description = (
        "User attached record {record_id}({record_type}) to case {case_id}({case_slug})"
    )


@dataclass
class CaseRecordsCreated(CaseRecordsEvent, RecordCreationEvent):
    pass


@dataclass
class CaseRecordDeleted(CaseRecordEvent, RecordDeletionEvent):
    record_description = (
        "User deleted record {record_id}({record_type}) from case {case_id}({case_slug})"
    )


# Postgres events


class RetentionTableEvent(SearchEvent):
    audit_description = "User searched retention table records"
    audit_module = ModuleTitle.CASE_MANAGER


# Legal hold events
@dataclass
class LegalHoldApplied(CaseManagementEvent, RecordModificationEvent):
    case_id: str = None
    objectUri: str = None
    audit_description = None

    def new_audit_record(self, **kwargs) -> UserAudit:
        record = super().new_audit_record(**kwargs)
        record.eventDetails.description = (
            f"Legal hold applied on {self.case_id}, retained file {self.objectUri}"
        )
        return record


@dataclass
class LegalHoldReleased(CaseManagementEvent, RecordModificationEvent):
    case_id: str = None
    objectUri: str = None
    audit_description = None

    def new_audit_record(self, **kwargs) -> UserAudit:
        record = super().new_audit_record(**kwargs)
        record.eventDetails.description = (
            f"Legal hold released on {self.case_id}, releasing file {self.objectUri}"
        )
        return record


# Case events (stored in PG)


@dataclass
class CaseEventManagementEvent(DomainEvent):
    audit_module = ModuleTitle.CASE_MANAGER

    event: Optional[Event] = None

    @property
    def record(self):
        return self.event


@dataclass
class CaseEventCreated(CaseEventManagementEvent, RecordCreationEvent):
    event_id: str = None
    case: Any = None

    @property
    def record(self):
        return self.case

    def new_audit_record(self, **kwargs) -> UserAudit:
        record = super().new_audit_record(**kwargs)
        record.eventDetails.description = (
            f"Event '{self.event.name}' is created for case '{self.case.slug or self.case.id_}'"
        )
        return record


@dataclass
class CaseEventUpdated(CaseEventManagementEvent, RecordModificationEvent):
    event_id: str = None
    audit_description: str = None


@dataclass
class RefinitivNewsSearched(CaseManagementEvent, SearchEvent):
    case_slug: Optional[str] = None

    @property
    def audit_description(self):
        return f"User fetched Refinitiv news stories for case '{self.case_slug}'"

# type: ignore
import dataclasses
from api_sdk.messages.base import DomainCommand, DomainEvent
from api_sdk.schemas.surveillance.watches import PendingChange
from api_sdk.utils.utils import StringEnum, nested_get
from dataclasses import dataclass
from se_api_svc.messages.audit.events import (
    AuditedEvent,
    RecordCreationEvent,
    RecordDeletionEvent,
    RecordModificationEvent,
    RecordViewEvent,
    SearchEvent,
)
from se_api_svc.messages.email_notification.events import EmailNotificationEvent
from se_api_svc.schemas.surveillance.alerts import SurveillanceAlert
from se_api_svc.schemas.surveillance.notification_settings import NotificationSetting
from se_api_svc.schemas.surveillance.watches import Watch
from se_api_svc.schemas.surveillance.workflow_settings import WorkflowSetting
from se_api_svc.schemas.track import (
    CategoryTitle,
    ModuleTitle,
    RecordDetails,
    UserAudit,
)
from se_elastic_schema.models.tenant.mar.mar_audit import MARAudit
from se_elastic_schema.models.tenant.mifid2.order import Order
from se_elastic_schema.models.tenant.user.user_comment import UserComment
from se_elastic_schema.static.surveillance import WatchStatusType
from typing import Dict, List, NamedTuple, Optional


@dataclass
class PendingChangeEvent(DomainEvent):
    audit_module = ModuleTitle.SURVEILLANCE

    pending_change: PendingChange = None

    @property
    def record(self):
        return self.pending_change


@dataclass
class NewWatchProposed(PendingChangeEvent, RecordCreationEvent):
    pass


@dataclass
class AlertEvent(AuditedEvent):
    audit_module = ModuleTitle.SURVEILLANCE

    alert: SurveillanceAlert = None

    @property
    def record(self):
        return self.alert


@dataclass
class OrderEvent(AuditedEvent):
    audit_module = ModuleTitle.ORDERS

    order: Order = None

    @property
    def record(self):
        return self.order


@dataclass
class WatchEvent(DomainEvent):
    audit_module = ModuleTitle.SURVEILLANCE

    watch: Watch = None

    @property
    def record(self):
        return self.watch


@dataclass
class UserCommentEvent(AuditedEvent):
    audit_module = ModuleTitle.SURVEILLANCE

    comment: UserComment = None

    @property
    def record(self):
        return self.comment


@dataclass
class MarketAbuseAuditEvent(AuditedEvent):
    audit_module = ModuleTitle.SURVEILLANCE

    mar_audit: MARAudit = None

    @property
    def record(self):
        return self.mar_audit


@dataclass
class AlertsSearched(AlertEvent, SearchEvent):
    pass


@dataclass
class MarketAbuseAuditSearched(MarketAbuseAuditEvent, SearchEvent):
    pass


@dataclass
class AlertViewed(AlertEvent, RecordViewEvent):
    alerts: List[SurveillanceAlert] = None
    audit_description = "User viewed alert details"


@dataclass
class WatchCreated(WatchEvent, RecordCreationEvent):
    schedule_created: bool = None
    event_description = "User created a {schedule_status} watch named {watch_name}"

    @property
    def audit_description(self):
        is_active_watch = nested_get(self.watch, "status") == WatchStatusType.ACTIVE
        return self.event_description.format(
            schedule_status=("scheduled" if is_active_watch else "unscheduled"),
            watch_name=nested_get(self.record, "name"),
        )


class WatchChange(NamedTuple):
    category: str
    event: str
    description: str


class WatchChangeCategory(StringEnum):
    SCHEDULE = "Schedule"
    SETTINGS = "Settings"


class WatchChangeEvent:
    SCHEDULE_EDITED = "Schedule Edited"
    SCHEDULE_CREATED = "Schedule Created"  # Not surfaced, used internally
    SCHEDULE_DELETED = "Schedule Deleted"
    RULE_CHANGES = "Rule Changes"
    FILTER_CHANGES = "Filter Changes"


class WatchChangeList(list):
    @property
    def schedule_created(self):
        return any(ch.event == WatchChangeEvent.SCHEDULE_CREATED for ch in self)

    @property
    def schedule_updated(self):
        return any(ch.event == WatchChangeEvent.SCHEDULE_EDITED for ch in self)


@dataclass
class WatchEdited(WatchEvent, RecordModificationEvent):
    changes: WatchChangeList = None
    audit_description = "User edited watch"

    def generate_audit_records(self, **kwargs):
        main_record = super().new_audit_record(**kwargs)
        yield main_record

        change: WatchChange
        for change in self.changes or ():
            # Do not surface the api-svc internal SCHEDULE_CREATED event.
            if change.event != WatchChangeEvent.SCHEDULE_CREATED:
                yield super().new_audit_record(**kwargs, **change._asdict())

    @property
    def schedule_created(self):
        if isinstance(self.changes, WatchChangeList):
            return self.changes and self.changes.schedule_created
        return False

    @property
    def schedule_updated(self):
        if isinstance(self.changes, WatchChangeList):
            return self.changes and self.changes.schedule_updated
        return False


@dataclass
class WatchDeleted(WatchEvent, RecordDeletionEvent):
    pass


@dataclass
class WatchesSearched(WatchEvent, SearchEvent):
    pass


@dataclass
class WatchViewed(WatchEvent, RecordViewEvent):
    pass


@dataclass
class SelerityNewsSearched(SearchEvent):
    audit_module = ModuleTitle.SURVEILLANCE
    audit_description = "User searched Selerity news"
    pass


@dataclass
class OrdersByScenarioSearched(OrderEvent, SearchEvent):
    audit_description = "User searched scenario orders"
    pass


@dataclass
class UserCommentsSearched(UserCommentEvent, SearchEvent):
    audit_description = "User searched alert comments"
    pass


@dataclass
class UserCommentsCreated(UserCommentEvent, RecordCreationEvent):
    audit_description = "User created an alert comment"
    pass


@dataclass
class WatchAuditSearched(SearchEvent):
    audit_module = ModuleTitle.SURVEILLANCE
    audit_description = "User searched watch audit"
    pass


@dataclass
class WatchRestrictionsExported(SearchEvent):
    audit_description = "User exported watch restrictions"


@dataclass
class WatchRestrictionsUploaded(AuditedEvent):
    audit_description = "User uploaded watch restrictions"


@dataclass
class AlertsWorkflowUpdatedIn(DomainCommand):
    watch_id: str = None
    alert_ids: Optional[List[str]] = dataclasses.field(default_factory=list)
    scenario_ids: Optional[List[str]] = dataclasses.field(default_factory=list)


@dataclass
class AlertsWorkflowUpdated(WatchEvent, AuditedEvent):
    audit_category = CategoryTitle.WORKFLOW

    watches: List[AlertsWorkflowUpdatedIn] = dataclasses.field(default_factory=list)
    message: Optional[Dict] = None

    alert_audit_module = ModuleTitle.SURVEILLANCE
    alert_audit_description = "{audit_label}: Alert '{slug}' updated {message}"

    def generate_audit_records(self, **kwargs):
        for watch in self.watches:
            for alert_id in watch.alert_ids:
                yield self.new_audit_record(
                    recordId=alert_id,
                    module=self.alert_audit_module,
                    description=self.alert_audit_description.format(
                        audit_label="Alerts",
                        slug=self.message.get(f"{alert_id}")[1],
                        message=self.message.get(f"{alert_id}")[0],
                    ),
                )

                yield self.new_audit_record(
                    recordId=watch.watch_id,
                    module=self.alert_audit_module,
                    description=self.alert_audit_description.format(
                        audit_label="Watches",
                        slug=self.message.get(f"{alert_id}")[1],
                        message=self.message.get(f"{alert_id}")[0],
                    ),
                )


@dataclass
class AlertsLabelUpdated(AlertsWorkflowUpdated):
    audit_category = CategoryTitle.RECORD_MODIFICATION


@dataclass
class WatchCommand(DomainCommand):
    watch: Watch = None


@dataclass
class CreateWatchCommand(WatchCommand):
    watch: Watch = None


@dataclass
class WatchSearched(WatchEvent, SearchEvent):
    def new_audit_record(self, **kwargs) -> UserAudit:
        audit = super().new_audit_record(**kwargs)
        audit.recordId = self.record.id_
        audit.recordDetails = RecordDetails.for_record(self.record)
        return audit


@dataclass
class LexicaSuggestionsViewed(WatchEvent, RecordViewEvent):
    audit_description = "User viewed lexica suggestions of the watch"


@dataclass
class NotificationSettingEvent(DomainEvent):
    audit_module = ModuleTitle.SURVEILLANCE

    setting: NotificationSetting = None

    @property
    def record(self):
        return self.setting


@dataclass
class NotificationSettingCreated(NotificationSettingEvent, RecordCreationEvent):
    pass


@dataclass
class NotificationSettingUpdated(NotificationSettingEvent, RecordModificationEvent):
    pass


@dataclass
class NotificationSettingDeleted(NotificationSettingEvent, RecordDeletionEvent):
    pass


@dataclass
class WorkflowSettingEvent(DomainEvent):
    audit_module = ModuleTitle.SURVEILLANCE

    setting: WorkflowSetting = None

    @property
    def record(self):
        return self.setting


@dataclass
class WorkflowSettingCreated(WorkflowSettingEvent, RecordCreationEvent):
    pass


@dataclass
class WorkflowSettingUpdated(WorkflowSettingEvent, RecordModificationEvent):
    pass


@dataclass
class WorkflowSettingDeleted(WorkflowSettingEvent, RecordDeletionEvent):
    pass


@dataclass
class ScenariosSearched(WatchEvent, SearchEvent):
    pass


@dataclass
class AlertsAssigneeEmailNotification(EmailNotificationEvent):
    pass


@dataclass
class WatchAlertsResolutionEmailNotification(EmailNotificationEvent):
    pass


@dataclass
class AlertsWorkflowUpdatedIn(DomainCommand):
    watch_id: str = None
    alert_ids: Optional[List[str]] = dataclasses.field(default_factory=list)
    scenario_ids: Optional[List[str]] = dataclasses.field(default_factory=list)


@dataclass
class PendingChangeResolved(PendingChangeEvent, RecordModificationEvent):
    pass

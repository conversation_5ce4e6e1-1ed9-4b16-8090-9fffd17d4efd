# ruff: noqa: E501
import copy
import datetime as dt
import logging
import re
from api_sdk.di.request import ReqDep
from api_sdk.es_dsl.utils import json_dumps
from api_sdk.exceptions import BadInput, EditConflict, NotFound, WorkflowPermissionsException
from api_sdk.messages.base import MessageBus
from api_sdk.repository.asyncronous.request_bound import has_feature_flag
from api_sdk.schemas.surveillance.watches import Pending<PERSON>hange, PendingChangeStatus
from api_sdk.utils.utils import nested_dict_get
from enum import Enum
from fastapi import Request
from se_api_svc.core.config import ApiServiceConfig
from se_api_svc.core.constants import ES_MAX_AGG_SIZE, MAR_ALGO_WORKFLOW
from se_api_svc.messages.common.command_handlers import get_surveillance_record_models
from se_api_svc.messages.registry import registry
from se_api_svc.messages.surveillance.commands import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>lsUpdateCommand,
    AlertsWorkflowUpdateCommand,
    CreateNotificationSettingCommand,
    CreateWatchCommand,
    CreateWorkflowSettingCommand,
    DeleteNotificationSettingCommand,
    DeleteWatchCommand,
    DeleteWorkflowSettingCommand,
    EditWatchCommand,
    EditWatchWorkflowCommand,
    ProposeNewWatchCommand,
    ResolvePendingChangeCommand,
    UpdateNotificationSettingCommand,
    UpdateWorkflowSettingCommand,
    WatchAlertsWorkflowUpdateCommand,
)
from se_api_svc.messages.surveillance.email_generator import (
    generate_alert_assignee_message,
    generate_watch_resolution_updated_message,
)
from se_api_svc.messages.surveillance.events import (
    AlertsAssigneeEmailNotification,
    AlertsLabelUpdated,
    AlertsWorkflowUpdated,
    AlertsWorkflowUpdatedIn,
    NewWatchProposed,
    NotificationSettingCreated,
    NotificationSettingDeleted,
    NotificationSettingUpdated,
    PendingChangeResolved,
    WatchAlertsResolutionEmailNotification,
    WatchChange,
    WatchChangeCategory,
    WatchChangeEvent,
    WatchCreated,
    WatchDeleted,
    WatchEdited,
    WorkflowSettingCreated,
    WorkflowSettingDeleted,
    WorkflowSettingUpdated,
)
from se_api_svc.messages.surveillance.field_changes import ChangeField, get_changes
from se_api_svc.messages.surveillance.watch_changes import (
    apply_lexica_preprocessing_feature_on_query,
    get_watch_changes,
)
from se_api_svc.permissions import get_module_admin_permission
from se_api_svc.repository.surveillance.alerts import (
    AlertsRepository,
    AlertsSearchBaseWithCommentSearch,
)
from se_api_svc.repository.surveillance.settings import (
    NotificationSettingsRepository,
    WorkflowSettingsRepository,
)
from se_api_svc.repository.surveillance.watches import WatchesRepository, is_comms_watch
from se_api_svc.repository.tenancy import TenantConfigurationRepository
from se_api_svc.repository.user_comments.user_comments import (
    UserCommentsRepository,
    generate_alert_comment,
)
from se_api_svc.schemas.settings import SettingsModule, get_settings_module_title
from se_api_svc.schemas.surveillance.alerts import CommunicationAlert, OrderAlert, SurveillanceAlert
from se_api_svc.schemas.surveillance.market_abuse import MarketAbuseAlert, MarketAbuseScenarioTag
from se_api_svc.schemas.surveillance.watches import (
    PendingChangeResolutionStatus,
    Watch,
    WatchBase,
)
from se_api_svc.schemas.surveillance.workflow import WorkflowStatus
from se_api_svc.schemas.surveillance.workflow_settings import WorkflowSetting
from se_api_svc.schemas.surveillance.workflow_settings import WorkflowSetting as WS
from se_api_svc.schemas.tenancy import TenantFeatureFlags
from se_api_svc.schemas.track import ModuleTitle
from se_api_svc.schemas.user_comments import UserCommentIn
from se_api_svc.services.surveillance.watch_service import WatchService
from se_api_svc.services.surveillance.workflow_engine import WorkflowEngine
from se_elastic_schema.static.surveillance import WatchQueryType, WatchStatusType, WatchType
from tenant_db.models.workflow.constant import Module
from typing import Dict, List, Tuple

log = logging.getLogger(__name__)


@registry.command_handler
async def create_watch(
    command: CreateWatchCommand,
    mb: MessageBus,
    repo: WatchesRepository,
    watch_service: WatchService,
    workflow: WorkflowEngine,
):
    # TODO: Change the below logic based on new spec
    # populate author's jurisdiction to watch
    command.watch = await repo.update_watch_with_jurisdiction(command.watch)

    watch = Watch.create_from(
        command.watch,
        createdBy=command.tenancy.userId,
        createdByAdmin=workflow.has_admin_permission_for(command.watch),
    )

    if watch.queryType == Watch.QueryType.COMMUNICATIONS:
        await apply_lexica_preprocessing_feature_on_query(watch.query, repo=repo)

    create_policy = await workflow.get_create_watch_policy(watch)
    create_policy.raise_on_prevent()

    schedule_created = Watch.has_schedule(watch)
    schedule_policy = await workflow.get_edit_schedule_policy(watch)
    if schedule_created:
        schedule_policy.raise_on_prevent()

    if create_policy.effect == WS.Effect.REVIEW or (
        schedule_created and schedule_policy.effect == WS.Effect.REVIEW
    ):
        await repo.create_pending_change(watch=None, proposed_changes=command.watch)
        (
            create_policy.raise_on_not_allow()
            if create_policy.effect == WS.Effect.REVIEW
            else schedule_policy.raise_on_not_allow()
        )

    # Put watch to PAUSED status
    # Don't create backtest if workflow is not deployed
    # In case of watch it's not needed as watch should be created
    # And we can add workflow/scheduling cron later
    if watch.type == WatchType.ON_DEMAND:
        watch.status = WatchStatusType.PAUSED
        await watch_service.ping_workflow(workflow_name=MAR_ALGO_WORKFLOW)

    await repo.save_new(watch)

    # In case of ON_DEMAND mar backtest
    # Send kafka event to trigger
    # run_mar_watch workflow
    # TODO: Move this logic to se-mono
    if watch.type == WatchType.ON_DEMAND.value and watch.queryType in (
        WatchQueryType.MARKET_ABUSE.value,
        WatchQueryType.RESTRICTED_LIST.value,
    ):
        log.info("Attempting to send event for mar backtest")
        watch_service.send_backtest_message(watch=watch)

    # In case of Trade Watch
    # Create schedule in scheduler DB
    # TODO: Move this logic to se-mono
    if (
        watch.queryType == WatchQueryType.TRADES.value
        and watch.status == WatchStatusType.ACTIVE.value
    ):
        await watch_service.upsert_watch_schedule(watch, watch.scheduleDetails)

    await mb.publish(
        WatchCreated(
            watch=watch,
            schedule_created=schedule_created,
            in_reply_to=command,
        )
    )


def _update_watch_alerts_priority(command: EditWatchCommand, repo: WatchesRepository):
    script = """if(ctx._source.detail.watchPriority == null) {\n ctx._source.detail.put('watchPriority' , params['priority']);}
                else {\n ctx._source.detail.watchPriority = params['priority'];}"""

    params = {"priority": command.watch.priority}

    body = {
        "script": {"source": script, "params": params, "lang": "painless"},
        "query": {
            "bool": {
                "must_not": [{"exists": {"field": "&expiry"}}],
                "filter": [{"term": {"detail.watchId": command.watch.id_}}],
            }
        },
    }

    index = repo.index_for_record_model(SurveillanceAlert)

    repo.es_client.update_by_query(
        index=index,
        body=body,
        refresh=True,
        wait_for_completion=False,
    )


@registry.command_handler
async def edit_watch(
    command: EditWatchCommand,
    mb: MessageBus,
    repo: WatchesRepository,
    workflow: WorkflowEngine,
    watch_service: WatchService,
):
    existing = await repo.get_editable_watch(command.watch_id, timestamp=command.timestamp)
    existing_copy = copy.deepcopy(existing)

    change_policy = await workflow.get_edit_watch_policy(existing)
    change_policy.raise_on_prevent()
    schedule_policy = await workflow.get_edit_schedule_policy(existing)

    # TODO: Change the below logic based on new spec
    # Do not change the jurisdiction, as the user can be other than watch author
    command.watch.jurisdictions = getattr(existing, "jurisdictions", None)

    key_before = existing.key_
    changes = get_watch_changes(old=existing, new=command.watch)

    if changes.schedule_updated:
        schedule_policy.raise_on_prevent()

    priority_changed = existing.priority != command.watch.priority
    # Updating name in the query part of watch if watch_name is changed
    if existing.name != command.watch.name:
        if isinstance(command.watch.query, dict):
            command.watch.query["name"] = command.watch.name
        else:
            command.watch.query.name = command.watch.name

    existing.update_from(command.watch)

    if existing.queryType == Watch.QueryType.COMMUNICATIONS:
        await apply_lexica_preprocessing_feature_on_query(existing.query, repo=repo)

    if change_policy.effect == WS.Effect.REVIEW or (
        changes.schedule_updated and schedule_policy.effect == WS.Effect.REVIEW
    ):
        await repo.create_pending_change(watch=existing_copy, proposed_changes=existing)
        change_policy.raise_on_not_allow() if change_policy.effect == WS.Effect.REVIEW else schedule_policy.raise_on_not_allow()  # noqa

    await repo.save_existing(existing)

    if existing.queryType == WatchQueryType.TRADES.value:
        await watch_service.upsert_watch_schedule(existing, existing.scheduleDetails)

    await mb.publish(
        WatchEdited(
            watch=existing,
            key_before=key_before,
            in_reply_to=command,
            changes=changes,
        )
    )

    if priority_changed:
        _update_watch_alerts_priority(command, repo)


@registry.command_handler
async def edit_watch_workflow(
    command: EditWatchWorkflowCommand,
    mb: MessageBus,
    repo: WatchesRepository,
    workflow: WorkflowEngine,
    watch_service: WatchService,
):
    existing: Watch = await repo.get_one(Watch, id=command.watch_id, timestamp=command.timestamp)

    (await workflow.get_edit_watch_policy(existing)).raise_on_not_allow()

    key_before = existing.key_

    old_status = existing.status
    existing.status = command.status

    await repo.save_existing(existing)

    if existing.queryType == WatchQueryType.TRADES.value:
        await watch_service.upsert_watch_schedule(existing, existing.scheduleDetails)

    await mb.publish(
        WatchEdited(
            watch=existing,
            key_before=key_before,
            in_reply_to=command,
            changes=[
                WatchChange(
                    category=WatchChangeCategory.SCHEDULE,
                    event=WatchChangeEvent.SCHEDULE_EDITED,
                    description=f"User changed watch status from {old_status.value} to {command.status.value}",  # noqa
                )
            ],
        )
    )


@registry.command_handler
async def delete_watch(
    command: DeleteWatchCommand,
    mb: MessageBus,
    repo: WatchesRepository,
    workflow: WorkflowEngine,
    watch_service: WatchService,
):
    if command.watch:
        watch = command.watch
    else:
        watch = repo.get_one(Watch, command.watch_id, timestamp=command.timestamp)

    (await workflow.get_delete_watch_policy(watch)).raise_on_not_allow()

    if watch.queryType == WatchQueryType.TRADES.value:
        await watch_service.upsert_watch_schedule(watch, watch.scheduleDetails)

    await repo.delete_existing(watch)

    await mb.publish(WatchDeleted(watch=watch, in_reply_to=command))


@registry.command_handler
async def propose_new_watch(
    command: ProposeNewWatchCommand,
    mb: MessageBus,
    repo: WatchesRepository,
    workflow: WorkflowEngine,
):
    policy = await workflow.get_create_watch_policy(watch=command.watch)
    policy.raise_on_prevent()
    policy.raise_on_not_review(
        comment="Should create watches without going through the review workflow"
    )

    command.watch.createdBy = workflow.tenancy.userId
    command.watch.createdByAdmin = workflow.has_admin_permission_for(command.watch)

    pending_change = await repo.create_pending_change(watch=None, proposed_changes=command.watch)

    await mb.publish(NewWatchProposed(pending_change=pending_change, in_reply_to=command))


@registry.command_handler
async def resolve_pending_change(
    command: ResolvePendingChangeCommand,
    mb: MessageBus,
    repo: WatchesRepository,
    workflow: WorkflowEngine,
    watch_service: WatchService,
):
    resolution = command.resolution

    p_change = await repo.get_pending_change(model_id=command.change_id)
    if (
        p_change.status == PendingChangeStatus.APPROVED
        or p_change.status == PendingChangeStatus.REJECTED
    ):
        raise EditConflict(
            model=PendingChange, id=command.change_id, message="Pending change is already resolved"
        )

    if p_change.createdBy == repo.tenancy.userId:
        raise WorkflowPermissionsException(
            policy=None, comment="User cannot review their own changes"
        )

    watch = None
    key_before = None
    is_new_watch_proposal = False
    if p_change.watchId:
        try:
            watch = repo.get_editable_watch(p_change.watchId)
        except NotFound:
            is_new_watch_proposal = True
        else:
            key_before = watch.key_

    if is_new_watch_proposal:
        workflow.get_create_watch_policy(p_change.proposedChanges).raise_on_not_allow()
    else:
        workflow.get_edit_watch_policy(watch).raise_on_not_allow()

    p_change, watch, change_list = await repo.resolve_pending_change(
        watch=watch, change=p_change, resolution=resolution, watch_service=watch_service
    )

    if watch and resolution.status == PendingChangeResolutionStatus.APPROVED:
        if is_new_watch_proposal:
            await mb.publish(WatchCreated(watch=watch))
        else:
            await mb.publish(
                WatchEdited(
                    watch=watch,
                    key_before=key_before,
                    changes=change_list,
                )
            )

    await mb.publish(PendingChangeResolved(pending_change=p_change, in_reply_to=command))


@registry.command_handler
async def create_workflow_setting(
    command: CreateWorkflowSettingCommand,
    mb: MessageBus,
    repo: WorkflowSettingsRepository,
):
    repo.tenancy.require_permissions(get_module_admin_permission(command.module))

    ws = await repo.create_module_setting(
        module=command.module,
        settingType=command.setting.settingType,
        subSetting=command.setting.subSetting,
        effect=command.setting.effect,
        enabled=command.setting.enabled,
    )

    await mb.publish(
        WorkflowSettingCreated(
            setting=ws,
            in_reply_to=command,
            audit=dict(module=get_settings_module_title(command.module)),
        )
    )


@registry.command_handler
async def update_workflow_setting(
    command: UpdateWorkflowSettingCommand,
    mb: MessageBus,
    repo: WorkflowSettingsRepository,
):
    repo.tenancy.require_permissions(get_module_admin_permission(command.module))

    ws: WorkflowSetting = await repo.get_module_setting(
        module=command.module, id=command.setting_id, timestamp=command.timestamp
    )
    key_before = ws.key_
    await repo.update_module_setting(
        ws, enabled=command.setting.enabled, effect=command.setting.effect
    )
    await mb.publish(
        WorkflowSettingUpdated(
            setting=ws,
            in_reply_to=command,
            audit=dict(module=get_settings_module_title(command.module)),
            key_before=key_before,
        )
    )


@registry.command_handler
async def delete_workflow_setting(
    command: DeleteWorkflowSettingCommand,
    mb: MessageBus,
    repo: WorkflowSettingsRepository,
):
    repo.tenancy.require_permissions(get_module_admin_permission(command.module))

    ws = repo.get_module_setting(module=command.module, id=command.setting_id)
    await repo.delete_existing(ws)
    await mb.publish(WorkflowSettingDeleted(setting=ws, in_reply_to=command))


@registry.command_handler
async def create_notification_setting(
    command: CreateNotificationSettingCommand,
    mb: MessageBus,
    repo: NotificationSettingsRepository,
):
    repo.tenancy.require_permissions(get_module_admin_permission(command.module))

    ns = await repo.create_module_setting(
        module=command.module,
        settingType=command.setting.settingType,
        option=command.setting.option,
        notificationEmails=command.setting.notificationEmails,
        enabled=command.setting.enabled,
    )

    await mb.publish(
        NotificationSettingCreated(
            setting=ns,
            in_reply_to=command,
            audit=dict(module=get_settings_module_title(command.module)),
        )
    )


@registry.command_handler
async def update_notification_setting(
    command: UpdateNotificationSettingCommand,
    mb: MessageBus,
    repo: NotificationSettingsRepository,
):
    repo.tenancy.require_permissions(get_module_admin_permission(command.module))

    ns = await repo.get_module_setting(
        module=command.module, id=command.setting_id, timestamp=command.timestamp
    )
    key_before = ns.key_
    await repo.update_module_setting(
        ns, enabled=command.setting.enabled, notificationEmails=command.setting.notificationEmails
    )

    await mb.publish(
        NotificationSettingUpdated(
            setting=ns,
            in_reply_to=command,
            audit=dict(module=get_settings_module_title(command.module)),
            key_before=key_before,
        ),
    )


@registry.command_handler
async def delete_notification_setting(
    command: DeleteNotificationSettingCommand,
    mb: MessageBus,
    repo: NotificationSettingsRepository,
):
    repo.tenancy.require_permissions(get_module_admin_permission(command.module))

    ns = await repo.get_module_setting(module=command.module, id=command.setting_id)
    await repo.delete_existing(ns)
    await mb.publish(
        NotificationSettingDeleted(
            setting=ns,
            in_reply_to=command,
            audit=dict(module=get_settings_module_title(command.module)),
        )
    )


def get_alert_update_body(body: Tuple, script: str = "") -> Tuple[Dict, str]:
    # remove None fields from the update dictionary
    update_body = {k: v for k, v in body if v is not None}

    if update_body.get("status") == WorkflowStatus.ESCALATED:
        update_body["wasEscalated"] = True
        script += """        if (ctx._source.workflow.escalatedCount == null) {
            ctx._source.workflow.escalatedCount = 1;
        } else {
            ctx._source.workflow.escalatedCount += 1;
        }"""

    if update_body.get("status") == WorkflowStatus.IN_REVIEW:
        update_body["wasReviewed"] = True
        script += """        if (ctx._source.workflow.reviewCount == null) {
                    ctx._source.workflow.reviewCount = 1;
                } else {
                    ctx._source.workflow.reviewCount += 1;
                }"""

    if "status" in update_body and update_body["status"] in [
        WorkflowStatus.RESOLVED,
        WorkflowStatus.RESOLVED_WITH_BREACH,
        WorkflowStatus.RESOLVED_WITH_DISMISSAL,
        WorkflowStatus.RESOLVED_WITH_INVESTIGATION,
        WorkflowStatus.RESOLVED_WITH_INVESTIGATION_WITH_BREACH,
    ]:
        return {**update_body, "assigneeId": None, "assigneeName": None}, script
    else:
        return update_body, script


def get_workflow_update_message(old: List[Dict], new: Dict, unique_field: str) -> Dict:
    workflow_editable_fields = [
        ChangeField(path="workflow.resolutionSubCategories", type_=list, of=list),
        ChangeField(path="workflow.resolutionCategory", type_=str, of=str),
        ChangeField(path="workflow.status", type_=str, of=str),
        ChangeField(path="workflow.resolvedComment", type_=str, of=str),
        ChangeField(path="workflow.assigneeName", label="AssigneeName", type_=str, of=str),
    ]

    workflow_audit_message = {}
    for old_workflow in old:
        field_changes = [
            change.get_workflow_change_message()
            for change in get_changes(old=old_workflow, new=new, fields=workflow_editable_fields)
        ]

        current_audit_message = ""

        for iteration, change in enumerate(field_changes):
            current_audit_message += (
                f"{change}, " if iteration < len(field_changes) - 1 else f"{change}"
            )

        if len(field_changes) == 1:
            current_audit_message = field_changes[0]

        workflow_audit_message[f"{old_workflow[unique_field]}"] = (
            current_audit_message,
            old_workflow["slug"],
        )

    return workflow_audit_message


def get_labels_update_message(old: List[Dict], new: Dict, unique_field: str) -> Dict:
    editable_fields = [
        ChangeField(path="labels", type_=list, of=list),
        ChangeField(path="labelsCustom", type_=list, of=list),
    ]
    audit_message = {}
    for old_labels in old:
        field_changes = [
            change.get_workflow_change_message()
            for change in get_changes(
                old=old_labels.get("labels", {}), new=new, fields=editable_fields
            )
        ]

        if not field_changes:
            continue

        current_audit_message = ""
        for iteration, change in enumerate(field_changes):
            current_audit_message += f"{change}, "

        current_audit_message.rstrip(", ")
        audit_message[f"{old_labels[unique_field]}"] = (
            current_audit_message,
            old_labels["slug"],
        )

    return audit_message


# flake8: noqa: C901
@registry.command_handler
async def alerts_workflow_update(
    request: Request,
    command: AlertsWorkflowUpdateCommand,
    mb: MessageBus,
    repo: AlertsRepository,
    user_comment_repo: UserCommentsRepository = ReqDep(UserCommentsRepository),
    tenant_repo: TenantConfigurationRepository = ReqDep(TenantConfigurationRepository),
    config: ApiServiceConfig = ReqDep(ApiServiceConfig),
):
    timestamp = dt.datetime.utcnow().replace(tzinfo=dt.timezone.utc)
    timestamp_ms = int(timestamp.timestamp() * 1000)
    tenant_configuration = await tenant_repo.get()

    script = f"""
        if (ctx._source.workflow != null) {{
            HashMap w = ctx._source.workflow;
            w.putAll(params.update);
        }}
        else {{
            ctx._source.workflow = params.update
        }}
        if (ctx._source.detail == null) {{
            Map m = new HashMap();
            m['createdOn'] = ctx._source['&timestamp'];
            ctx._source.detail = m
        }}
        else if (ctx._source.detail.createdOn == null) {{
            ctx._source.detail.createdOn = ctx._source['&timestamp'];
        }}
        ctx._source['&timestamp'] = {timestamp_ms}L;
        if (params.update.resolutionCategory != null) {{
            ctx._source.resolved = {timestamp_ms}L;
        }}
        else {{
            ctx._source.workflow.resolutionCategory = null;
            ctx._source.workflow.resolvedComment = null;
            ctx._source.workflow.resolvedViaCase = null;
            ctx._source.workflow.resolvedByName = null;
            ctx._source.workflow.resolutionSubCategories = null;
            ctx._source.workflow.resolutionSubCategoryCustom = null;
            ctx._source.workflow.resolutionCategoryCustom = null;
            ctx._source['resolved'] = null;
        }}
    """

    update_body, script = get_alert_update_body(
        body=command.update.to_dict().items(), script=script
    )

    body = {
        "script": {
            "source": script,
            "params": {"update": update_body},
        },
        "query": {
            "bool": {
                "must_not": [{"exists": {"field": "&expiry"}}],
            }
        },
    }
    if command.scenario_ids and command.alert_ids:
        body["query"]["bool"]["filter"] = [
            {
                "bool": {
                    "should": [
                        {"terms": {"scenarioId": command.scenario_ids}},
                        {"terms": {"&id": command.alert_ids}},
                    ]
                }
            }
        ]
    elif command.scenario_ids:
        body["query"]["bool"]["filter"] = [{"terms": {"scenarioId": command.scenario_ids}}]
    elif command.alert_ids:
        body["query"]["bool"]["filter"] = [{"terms": {"&id": command.alert_ids}}]
    else:
        raise BadInput(msg="alertIds and scenarioIds empty", loc=["body", "ids"])

    index = repo.index_for_record_model(
        get_surveillance_record_models(tenant_config=tenant_configuration)
    )

    existing_workflow = []
    has_resolved_status = False

    unique_field = "&id"

    alert_workflows = repo.es_client.search(
        index=index,
        body={
            "query": body["query"],
            "_source": [f"{unique_field}", "workflow", "&model", "&key", "slug"],
            "size": 10000,
        },
    )

    for alert in nested_dict_get(alert_workflows.raw, "hits.hits"):
        existing_workflow.append(nested_dict_get(alert, "_source"))
        if alert["_source"]["workflow"]["status"].startswith("RESOLVED"):
            has_resolved_status = True

    if config.DEBUG:
        log.debug(f"Against {index}, executing query: {json_dumps(body)}")

    if has_resolved_status and not command.update.status.startswith("RESOLVED"):
        script += "ctx._source.workflow.wasReopened = true;"

    # Update records
    repo.es_client.update_by_query(index=index, body=body, refresh=True)

    # Add user audit event
    watches_query_body = {
        "size": 0,
        "query": body["query"],
        "aggs": {
            "WATCHES": {
                "terms": {"field": "detail.watchId", "size": ES_MAX_AGG_SIZE},
                "aggs": {
                    "IDS": {
                        "terms": {
                            "field": "&id" if command.alert_ids else "scenarioId",
                            "size": ES_MAX_AGG_SIZE,
                        }
                    }
                },
            }
        },
    }
    watches_result = repo.es_client.search(index=index, body=watches_query_body)
    watches = []
    for watch in watches_result["aggregations"]["WATCHES"]["buckets"]:
        ids = [i["key"] for i in watch["IDS"]["buckets"]]
        watch_body = AlertsWorkflowUpdatedIn(
            watch_id=watch["key"],
            alert_ids=ids if command.alert_ids else [],
            scenario_ids=ids if command.scenario_ids else [],
        )
        watches.append(watch_body)

    # Flatten the Enum values
    flattened_update_body = {"workflow": {}}
    for key, value in update_body.items():
        if isinstance(value, Enum):
            flattened_update_body.get("workflow")[key] = value.value
        else:
            flattened_update_body.get("workflow")[key] = value

    workflow_audit_message = get_workflow_update_message(
        old=existing_workflow, new=flattened_update_body, unique_field=unique_field
    )

    # In case of bulk-scenario update
    # Audit record should be generated using &id of scenarios
    if command.scenario_ids:
        response = repo.es_client.search(
            index=repo.index_for_record_model(MarketAbuseScenarioTag),
            body={
                "query": {
                    "bool": {
                        "filter": [{"terms": {"scenarioId": command.scenario_ids}}],
                        "must_not": [{"exists": {"field": "&expiry"}}],
                    }
                },
                "_source": [f"{unique_field}"],
            },
        )

        alert_ids = [alert.get("_id") for alert in nested_dict_get(response.raw, "hits.hits")]

        [setattr(watch, "alert_ids", alert_ids) for watch in watches]

    await mb.publish(
        AlertsWorkflowUpdated(watches=watches, message=workflow_audit_message, request=request)
    )

    alert_info = []

    status = command.update.status

    if status:
        status = re.sub(
            r"[A-Za-z]+('[A-Za-z]+)?", lambda word: word.group(0).capitalize(), status
        ).replace("_", " ")

    # Iterate through alerts and save comment for each alert resolved
    try:
        if not command.assigner_comment:
            message = generate_alert_comment(update=command.update)
            command.assigner_comment = [
                UserCommentIn.from_dict(
                    {
                        "comment": message,
                        "linkModel": alert["_source"]["&model"],
                        "linkId": alert["_source"]["slug"],
                        "linkKey": alert["_source"]["&key"],
                    }
                )
                for alert in nested_dict_get(alert_workflows.raw, "hits.hits")
            ]

        for comment_object in command.assigner_comment:
            await user_comment_repo.save_comment(comment_body=comment_object)
    except Exception as e:
        log.warning(f"Skipped saving comment : {e}")

    if command.alert_details and command.email:
        for alert_detail in command.alert_details:
            alert_id = str(alert_detail["alert_id"])

            alert_response = await repo.get_alert(
                alert_id=alert_id,
                search_mar_and_non_mar=bool(command.module == Module.TRADE_SURVEILLANCE),
            )

            alert_info.append(
                {
                    "alert_name": f"{alert_response['slug']}" if alert_response["slug"] else "",
                    "watch_name": f"{alert_response['detail.watchName']}",
                    "alert_link": alert_detail["alert_link"],
                }
            )

        alert_assignee_email_message = generate_alert_assignee_message(
            realm=repo.realm,
            subject="Alert Assigned",
            header="Alerts Assigned"
            if len(alert_info) > 1
            else f"{alert_info[0].get('watch_name', '')} Alert Assigned",
            assigner=str(command.update.updatedBy),
            date=str(timestamp),
            reason="no reason specified" if not status else f"the reason being {status}",
            alert_info=alert_info,
            # here we are assuming, there is only one comment for multiple alerts:
            assigner_comment="Comment by assigner: " + command.assigner_comment[0].comment
            if command.assigner_comment
            else "",
        )

        await mb.publish(
            AlertsAssigneeEmailNotification(
                message=alert_assignee_email_message, recipients=command.email
            )
        )


@registry.command_handler
async def alerts_labels_update(
    request: Request,
    command: AlertsLabelsUpdateCommand,
    mb: MessageBus,
    repo: AlertsRepository,
    tenant_repo: TenantConfigurationRepository = ReqDep(TenantConfigurationRepository),
):
    timestamp = dt.datetime.utcnow().replace(tzinfo=dt.timezone.utc)
    timestamp_ms = int(timestamp.timestamp() * 1000)
    tenant_configuration = await tenant_repo.get()

    script = f"""
    if (ctx._source.labels != null) {{
        if (ctx._source.labels.labels != null) {{
            for (label in params.update.labels) {{
                if (!ctx._source.labels.labels.contains(label)) {{
                    ctx._source.labels.labels.add(label);
                }}
            }}
        }} else {{
            ctx._source.labels.labels = params.update.labels;
        }}
        if (ctx._source.labels.labelsCustom != null) {{
            for (label in params.update.labelsCustom) {{
                if (!ctx._source.labels.labelsCustom.contains(label)) {{
                    ctx._source.labels.labelsCustom.add(label);
                }}
            }}
        }} else {{
            ctx._source.labels.labelsCustom = params.update.labelsCustom;
        }}
    }} else {{
        ctx._source.labels = new HashMap();
        ctx._source.labels.labels = params.update.labels;
        ctx._source.labels.labelsCustom = params.update.labelsCustom;
    }}
    ctx._source['&timestamp'] = {timestamp_ms}L;
    """

    update_body = command.update.as_dict()

    body = {
        "script": {
            "source": script,
            "params": {"update": update_body},
        },
        "query": {
            "bool": {
                "must_not": [{"exists": {"field": "&expiry"}}],
            }
        },
    }
    if command.scenario_ids and command.alert_ids:
        body["query"]["bool"]["filter"] = [
            {
                "bool": {
                    "should": [
                        {"terms": {"scenarioId": command.scenario_ids}},
                        {"terms": {"&id": command.alert_ids}},
                    ]
                }
            }
        ]
    elif command.scenario_ids:
        body["query"]["bool"]["filter"] = [{"terms": {"scenarioId": command.scenario_ids}}]
    elif command.alert_ids:
        body["query"]["bool"]["filter"] = [{"terms": {"&id": command.alert_ids}}]
    else:
        raise BadInput(msg="alertIds and scenarioIds empty", loc=["body", "ids"])

    index = repo.index_for_record_model(
        get_surveillance_record_models(tenant_config=tenant_configuration)
    )

    unique_field = "&id"
    existing_labels = []
    alert_labels = repo.es_client.search(
        index=index,
        body={
            "query": body["query"],
            "_source": [f"{unique_field}", "labels", "&model", "&key", "slug"],
            "size": 10000,
        },
    )

    for alert in nested_dict_get(alert_labels.raw, "hits.hits"):
        existing_labels.append(nested_dict_get(alert, "_source"))

    workflow_audit_message = get_labels_update_message(
        old=existing_labels, new=update_body, unique_field=unique_field
    )

    if not workflow_audit_message:
        return

    # Update records
    repo.es_client.update_by_query(index=index, body=body, refresh=True)

    # Add user audit event
    watches_query_body = {
        "size": 0,
        "query": body["query"],
        "aggs": {
            "WATCHES": {
                "terms": {"field": "detail.watchId", "size": ES_MAX_AGG_SIZE},
                "aggs": {
                    "IDS": {
                        "terms": {
                            "field": "&id",
                            "size": ES_MAX_AGG_SIZE,
                        }
                    }
                },
            }
        },
    }
    watches_result = repo.es_client.search(index=index, body=watches_query_body)
    watches = []
    for watch in watches_result["aggregations"]["WATCHES"]["buckets"]:
        ids = [i["key"] for i in watch["IDS"]["buckets"]]
        watch_body = AlertsWorkflowUpdatedIn(
            watch_id=watch["key"],
            alert_ids=ids,
        )
        watches.append(watch_body)

    await mb.publish(
        AlertsLabelUpdated(watches=watches, message=workflow_audit_message, request=request)
    )


@registry.command_handler
async def watch_alerts_workflow_update(
    command: WatchAlertsWorkflowUpdateCommand,
    mb: MessageBus,
    repo: AlertsRepository,
    watch_repo: WatchesRepository,
    config: ApiServiceConfig = ReqDep(ApiServiceConfig),
):
    watch = await watch_repo.get_watch(watch_id=command.watch_id)

    if watch is None:
        return

    timestamp = dt.datetime.utcnow().replace(tzinfo=dt.timezone.utc)
    timestamp_ms = int(timestamp.timestamp() * 1000)

    alerts = await repo.get_aggs(
        search_model_cls=AlertsSearchBaseWithCommentSearch,
        record_model=[CommunicationAlert, OrderAlert, MarketAbuseAlert, MarketAbuseScenarioTag],
        aggs={
            "ALERT_IDS": {"terms": {"field": "&id", "size": ES_MAX_AGG_SIZE}},
            "SCENARIO_IDS": {"terms": {"field": "scenarioId", "size": ES_MAX_AGG_SIZE}},
        },
        detail_watch_id=command.watch_id,
        workflow_status="UNRESOLVED",
        module=ModuleTitle.COMMS_SURVEILLANCE
        if is_comms_watch(query_type=watch.queryType)
        else ModuleTitle.TRADE_SURVEILLANCE,
        **command.as_search_kwargs(),
    )

    tenant_config = command.tenancy.tenant_config
    index = repo.index_for_record_model(get_surveillance_record_models(tenant_config=tenant_config))

    # Add user audit event
    watch_ids_type = (
        "SCENARIO_IDS" if watch.queryType == WatchBase.QueryType.MARKET_ABUSE else "ALERT_IDS"
    )
    terms_field_for_update = (
        "scenarioId" if watch.queryType == WatchBase.QueryType.MARKET_ABUSE else "&id"
    )
    ids = [i["key"] for i in alerts.aggregations[watch_ids_type]["buckets"]]

    if len(ids) == 0:
        return

    script = f"""
        if (ctx._source.workflow != null) {{
            HashMap w = ctx._source.workflow;
            w.putAll(params.update);
        }}
        else {{
            ctx._source.workflow = params.update
        }}
        if (ctx._source.detail == null) {{
            Map m = new HashMap();
            m['createdOn'] = ctx._source['&timestamp'];
            ctx._source.detail = m
        }}
        else if (ctx._source.detail.createdOn == null) {{
            ctx._source.detail.createdOn = ctx._source['&timestamp'];
        }}
        ctx._source['&timestamp'] = {timestamp_ms}L;
        if (params.update.resolutionCategory != null) {{
            ctx._source.resolved = {timestamp_ms}L;
        }}
    """

    # remove None fields from the update dictionary
    update_body = {k: v for k, v in command.update.to_dict().items() if v is not None}

    body = {
        "script": {
            "source": script,
            "params": {"update": update_body},
        },
        "query": {
            "bool": {
                "must_not": [{"exists": {"field": "&expiry"}}],
                "filter": [{"terms": {terms_field_for_update: ids}}],
            }
        },
    }

    if config.DEBUG:
        log.debug(f"Against {index}, executing query: {json_dumps(body)}")
    # Update records
    repo.es_client.update_by_query(index=index, body=body, refresh=True)
    watch_body = AlertsWorkflowUpdatedIn(
        watch_id=watch["key"],
        alert_ids=ids if watch_ids_type == "ALERT_IDS" else [],
        scenario_ids=ids if watch_ids_type == "SCENARIO_IDS" else [],
    )

    await mb.publish(AlertsWorkflowUpdated(watches=[watch_body]))
    module = watch.get_settings_module()
    if module is None:
        module = SettingsModule.TRADES_SURVEILLANCE

    if not has_feature_flag(tenant_config, TenantFeatureFlags.DISABLE_BULK_RESOLVE_ALERT_EMAILS):
        watch_resolution_email_message = generate_watch_resolution_updated_message(
            realm=repo.realm,
            subject=f"Watch {watch.name} Alerts has been marked {command.update.resolutionCategory}",
            header=f"{watch.name} Alerts",
            watch_name=f"{watch.name}",
            alert_number=len(ids),
            resolution_state=command.update.resolutionCategory,
            module=module.value.lower().replace("_", "-"),
            watch_id=watch.id_,
        )

        # As per requirement email should trigger to action user
        # one could have used repo.user.email rather than taking
        # email list as part of command
        # problem faced is CommandHandler may not resolve elastic_repo
        # as RequestBoundRepository, it could resolve it as ServiceBoundRepository
        # In such case repo.user will be None
        # same problem faced in writing test conditions
        if command.recipients_email and len(command.recipients_email) > 0:
            await mb.publish(
                WatchAlertsResolutionEmailNotification(
                    message=watch_resolution_email_message, recipients=command.recipients_email
                )
            )

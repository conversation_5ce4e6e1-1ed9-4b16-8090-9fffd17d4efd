# type: ignore
import datetime as dt
import pydantic
import re
from api_sdk.es_dsl.base import <PERSON><PERSON><PERSON><PERSON>, NotExpired, SearchModel
from api_sdk.exceptions import BadInput
from api_sdk.models.search import SearchResult
from api_sdk.schemas.base import APIModel, Field, RecordModel
from api_sdk.schemas.static import Module
from api_sdk.utils.utils import StringEnum
from datetime import datetime
from enum import auto
from se_api_svc.schemas.firm import Firm
from se_api_svc.schemas.person import Person
from se_elastic_schema.components.mifid2.order.jurisdiction import Jurisdiction
from se_elastic_schema.static.reference import Permission
from se_elastic_schema.static.security import AccessRole
from typing import Any, Dict, List, Optional

ACCOUNT_FIRM_TEXT_SEARCH_FIELDS = (
    "personalDetails.lastName.text",
    "sinkIdentifiers.tradeFileIdentifiers.id.text",
    "sinkIdentifiers.tradeFileIdentifiers.label.text",
)


class WorkflowDetails(APIModel):
    id: str
    name: Optional[str]
    appliedBy: Optional[str]
    timestamp: datetime = Field(datetime.utcnow())


class PolicyDetails(APIModel):
    id: str
    name: Optional[str]
    appliedBy: Optional[str]
    timestamp: datetime = Field(datetime.utcnow())


class UserDataAccessPolicy(APIModel):
    caseManager: Optional[PolicyDetails]
    communication: Optional[PolicyDetails]
    cSurv: Optional[PolicyDetails]
    mifir: Optional[PolicyDetails]
    order: Optional[PolicyDetails]
    tSurv: Optional[PolicyDetails]
    market: Optional[PolicyDetails]
    dataProvenance: Optional[PolicyDetails]


class UserSurveillanceWorkflow(APIModel):
    cSurv: Optional[WorkflowDetails]
    tSurv: Optional[WorkflowDetails]


class AccountUser(RecordModel):
    class Config:
        model_name = "AccountUser"
        index_suffix = "account_user"
        extra = pydantic.Extra.ignore

    class MfaStatus(StringEnum):
        NOT_SET = auto()  # MFA not setup yet
        ACTIVATED = auto()  # MFA is set up by user in MFA app.
        DISABLED = auto()  # No MFA required for log in.

    class ProfileLanguage(StringEnum):
        English = auto()
        French = auto()
        Spanish = auto()
        German = auto()
        Dutch = auto()

    uniqueProps_: Optional[List[str]] = Field(alias="&uniqueProps")
    userId: Optional[str]
    name: Optional[str]
    mobileNumber: Optional[str]
    countryCode: Optional[str]
    messenger: Optional[str]
    activated: Optional[bool]
    mfaStatus: Optional[MfaStatus]
    mfaProvider: Optional[str]
    mfaId: Optional[str]
    mfaSecret: Optional[str]
    tags: Optional[Dict[str, Any]]
    email: Optional[str]
    jobTitle: Optional[str]
    registered: Optional[dt.datetime]
    timeZone: Optional[str]
    language: Optional[ProfileLanguage]
    permissions: Optional[List[Module | Permission]]
    roles: Optional[List[AccessRole]]
    invalidLoginAttempts: Optional[int]
    locked: Optional[bool] = False
    jurisdiction: Optional[List[Jurisdiction]]
    authLocation: Optional[str]
    authChannel: Optional[str]
    surveillanceWorkflow: Optional[UserSurveillanceWorkflow]
    appliedPolicy: Optional[UserDataAccessPolicy]

    @staticmethod
    def sanitize_account_user(account_user):
        # remove all invalid meta data
        meta_to_remove = (
            "cascadeId_",
            "hash_",
            "link_",
            "model_",
            "traitFqn_",
            "uniqueProps_",
            "user_",
            "validationErrors_",
            "version_",
        )

        for key in meta_to_remove:
            if hasattr(account_user, key):
                setattr(account_user, key, None)

        return account_user

    def sanitize_data(self):
        """Cleans up the user's data (especially email). Call this when
        creating or updating a user's email address.

        This could be made automatic as a validator, but then would mess
        up older data.
        """
        if self.email is not None:
            self.email = self.email.lower().strip()
            # validation
            if not re.match(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", self.email):
                raise BadInput("Invalid email address")
        else:
            raise BadInput("Email address is required")


class UpdateUserIn(AccountUser):
    permissions: Optional[List[Module]]


class AccountUserWithRoles(AccountUser):
    permissions: Optional[List[Module]]
    user_roles: Optional[List[str]] = Field(None, alias="userRoles")


class AccountUserWithAuthType(AccountUser):
    class AuthType(StringEnum):
        UNKNOWN = auto()
        SSO = auto()
        EMAIL_PASS = auto()
        MFA = auto()

    auth_type = AuthType.UNKNOWN


class AccountUserSearch(SearchModel):
    features = [
        ModelFilter(model=AccountUser),
        NotExpired,
    ]


class AccountPerson(Person, RecordModel):
    class Config:
        model_name = "AccountPerson"
        index_suffix = "account_person"
        trait_fqn = "account/person"

        extra = pydantic.Extra.allow


class AccountFirm(Firm, RecordModel):
    class Config:
        model_name = "AccountFirm"
        index_suffix = "account_firm"
        trait_fqn = "account/account_firm"

        extra = pydantic.Extra.allow


class UserOut(APIModel):
    userId: str
    name: Optional[str] = None
    email: str


class ListUsersResponse(SearchResult):
    results: List[UserOut]


class AccountUserLockUnlockParam(StringEnum):
    LOCK = "lock"
    UNLOCK = "unlock"


class AccountUserNotifyParam(StringEnum):
    YES = "yes"
    NO = "no"


class AccountUserAction(StringEnum):
    CREATED = "created"
    DELETED = "deleted"


class AccountUserEmailPayload(APIModel):
    email: str


class AccountUserAdminTypes(StringEnum):
    TRANSACTION_REPORTING_ADMIN = "TRANSACTION_REPORTING_ADMIN"
    COMMS_SURVEILLANCE_ADMIN = "COMMS_SURVEILLANCE_ADMIN"
    TRADE_SURVEILLANCE_ADMIN = "TRADE_SURVEILLANCE_ADMIN"


class UserAttributes(StringEnum):
    WORKFLOW = "WORKFLOW"
    DATA_ACCESS_POLICY = "DATA_ACCESS_POLICY"

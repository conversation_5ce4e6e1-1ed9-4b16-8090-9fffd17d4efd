# type: ignore
from api_sdk.schemas.base import APIModel, Field
from api_sdk.utils.utils import StringEnum
from enum import auto
from pydantic import validator
from se_api_svc.core.constants import SE_GLOBAL_WORKFLOW
from se_api_svc.schemas.user_comments import UserCommentIn
from se_elastic_schema.static.case import (
    ResolutionCategoryCommsEnum,
    ResolutionCategoryMarEnum,
    ResolutionSubCategoryCommsEnum,
    ResolutionSubCategoryMarEnum,
)
from se_elastic_schema.static.reference import LabelsEnum, TradeSurveillanceAlertLabelsEnum
from se_elastic_schema.static.surveillance import AssignReviewEscalateReasonCategory
from tenant_db.models.workflow.constant import Module, TransitionRestrictionType
from typing import Any, Dict, List, Literal, Optional, Union


class WorkflowStatus(StringEnum):
    ESCALATED = auto()
    IN_PROGRESS = auto()
    IN_REVIEW = auto()
    RESOLVED = auto()
    RESOLVED_WITH_BREACH = auto()
    RESOLVED_WITH_DISMISSAL = auto()
    RESOLVED_WITH_INVESTIGATION = auto()
    RESOLVED_WITH_INVESTIGATION_WITH_BREACH = auto()
    UNDER_INVESTIGATION = auto()
    UNRESOLVED = auto()
    ASSIGNED_OTHER = auto()


class WorkflowAssigneeFilterOptions(StringEnum):
    ASSIGNED = auto()
    UNASSIGNED = auto()


class Workflow(APIModel):
    assignedOtherReasonCategoryCustom: Optional[str]
    assigneeId: Optional[str]
    assigneeName: Optional[str]
    assigneeOtherReasonCategories: Optional[List[AssignReviewEscalateReasonCategory]]
    caseId: Optional[str]
    caseSlug: Optional[str]
    escalatedCount: Optional[int]
    escalatedReasonCategories: Optional[List[AssignReviewEscalateReasonCategory]]
    escalatedReasonCategoryCustom: Optional[str]
    read: Optional[bool]
    reopenedReason: Optional[str]
    resolutionCategory: Optional[ResolutionCategoryCommsEnum | ResolutionCategoryMarEnum]
    resolutionCategoryCustom: Optional[str]
    resolutionSubCategories: Optional[
        List[ResolutionSubCategoryCommsEnum | ResolutionSubCategoryMarEnum]
    ] = None
    resolutionSubCategoryCustom: Optional[str]
    resolvedById: Optional[str]
    resolvedAfterReview: Optional[bool]
    resolvedAfterEscalation: Optional[bool]
    resolvedWithCopilotSuggestions: Optional[bool]
    resolvedWithCopilotSuggestionsWithEdits: Optional[bool]
    resolvedByName: Optional[str]
    resolvedComment: Optional[str]
    resolvedViaCase: Optional[bool]
    reviewCount: Optional[int]
    reviewReasonCategories: Optional[List[AssignReviewEscalateReasonCategory]]
    reviewReasonCategoryCustom: Optional[str]
    status: Optional[WorkflowStatus]
    updatedBy: Optional[str]
    wasEscalated: Optional[bool]
    wasReopened: Optional[bool]
    wasReviewed: Optional[bool]


class WorkflowUpdate(APIModel):
    assignedOtherReasonCategoryCustom: Optional[str]
    assigneeId: Optional[str]
    assigneeName: Optional[str]
    assigneeOtherReasonCategories: Optional[List[AssignReviewEscalateReasonCategory]]
    caseId: Optional[str]
    caseSlug: Optional[str]
    escalatedReasonCategories: Optional[List[AssignReviewEscalateReasonCategory]]
    escalatedReasonCategoryCustom: Optional[str]
    read: Optional[bool]
    reopenedReason: Optional[str]
    resolutionCategory: Optional[ResolutionCategoryCommsEnum | ResolutionCategoryMarEnum]
    resolutionCategoryCustom: Optional[str]
    resolutionSubCategories: Optional[
        List[Union[ResolutionSubCategoryCommsEnum, ResolutionSubCategoryMarEnum]]
    ]
    resolutionSubCategoryCustom: Optional[str]
    resolvedById: Optional[str]
    resolvedAfterReview: Optional[bool]
    resolvedAfterEscalation: Optional[bool]
    resolvedWithCopilotSuggestions: Optional[bool]
    resolvedWithCopilotSuggestionsWithEdits: Optional[bool]
    resolvedByName: Optional[str]
    resolvedComment: Optional[str]
    resolvedViaCase: Optional[bool]
    reviewReasonCategories: Optional[List[AssignReviewEscalateReasonCategory]]
    reviewReasonCategoryCustom: Optional[str]
    status: Optional[WorkflowStatus]
    updatedBy: Optional[str]
    wasEscalated: Optional[bool]
    wasReopened: Optional[bool]
    wasReviewed: Optional[bool]

    @validator("resolutionSubCategoryCustom")
    def check_resolution_sub_category_custom_is_not_empty(cls, resolution_sub_category_custom):
        if resolution_sub_category_custom is not None and (
            not resolution_sub_category_custom or not resolution_sub_category_custom.strip()
        ):
            raise ValueError("resolutionSubCategoryCustom can not be an empty string")

        return resolution_sub_category_custom


class AlertIdLinkMapping(APIModel):
    alert_id: str = Field(None, alias="alertId")
    alert_link: str = Field(None, alias="alertLink")


class WorkflowFromStatus(APIModel):
    __root__: Dict[str, WorkflowStatus]

    def to_dict(self, **kwargs) -> Dict[str, str]:
        return {key: value.value for key, value in self.__root__.items()}


class AlertsWorkflowBulkUpdate(APIModel):
    alert_ids: Optional[List[str]] = Field(None, alias="alertIds")
    scenario_ids: Optional[List[str]] = Field(None, alias="scenarioIds")
    update: WorkflowUpdate = Field(None, alias="workflowUpdate")
    email: Optional[List[str]] = Field(None, alias="assigneeEmail")
    alert_details: Optional[List[AlertIdLinkMapping]] = Field(None, alias="alertLinks")
    # ToDo: `user_comment` and  `from_status` should not be optional
    user_comment: Optional[List[UserCommentIn]] = Field(None, alias="userComment")
    from_status: Optional[WorkflowFromStatus] = Field(None, alias="fromStatus")
    module: Literal["cSurv", "tSurv"] = Field()
    validate_only_workflow: Optional[bool] = Field(False, alias="validateOnlyWorkflow")


class AlertsWorkflowBulkUpdateIn(APIModel):
    alert_ids: Optional[List[str]] = Field(None, alias="alertIds")
    update: WorkflowUpdate = Field(None, alias="workflowUpdate")


class ScenariosWorkflowBulkUpdateIn(APIModel):
    scenario_ids: Optional[List[str]] = Field(None, alias="scenarioIds")
    update: WorkflowUpdate = Field(None, alias="workflowUpdate")


class LabelsUpdate(APIModel):
    label_enum: Optional[List[Union[TradeSurveillanceAlertLabelsEnum, LabelsEnum]]] = Field(
        None, alias="labels"
    )
    label_custom: Optional[List[str]] = Field(None, alias="labelsCustom")

    def as_dict(self):
        res = {"labels": [], "labelsCustom": []}
        for item in self.label_enum or []:
            res["labels"].append(item.value)

        for item in self.label_custom or []:
            res["labelsCustom"].append(item)

        return res


class AlertsLabelsBulkUpdate(APIModel):
    alert_ids: Optional[List[str]] = Field(None, alias="alertIds")
    scenario_ids: Optional[List[str]] = Field(None, alias="scenarioIds")
    update: LabelsUpdate = Field(None, alias="labelsUpdate")


class WatchAlertsWorkflowUpdate(APIModel):
    watch_id: str = Field(None, alias="watchId")
    update: WorkflowUpdate = Field(None, alias="workflowUpdate")


class SurveillanceTrendChart(StringEnum):
    ASSET_CLASS = auto()
    ASSET_CLASS_SUB = auto()
    COUNTERPARTY = auto()
    INSTRUMENT_FULL_NAME = auto()
    INVESTMENT_DECISION_MAKER = auto()
    PAD = auto()
    TRADER = auto()


class AlertWorkflowTransition(APIModel):
    transitionName: Optional[str]
    fromStatus: WorkflowStatus
    toStatus: WorkflowStatus
    transitionRestrictionType: Optional[TransitionRestrictionType]
    transitionCondition: Optional[Dict]

    # @validator("transitionCondition")
    # def validate_transition_condition(cls, val: Any):
    #     # Todo add validation to:
    #     #  - check condition is on allowed field
    #     try:
    #         if not isinstance(val, Dict):
    #             raise ValueError("Invalid JSON format")
    #     except json.JSONDecodeError:
    #         raise ValueError("Invalid JSON format")
    #     return val

    @validator("transitionRestrictionType")
    def validate_transition_restriction_type(cls, val: Any):
        if not TransitionRestrictionType.has_value(val):
            raise ValueError(f"{val} is not a valid Module")
        return val


class AlertWorkflowIn(APIModel):
    description: Optional[str] = Field(None)
    module: Optional[str] = Field(None)
    workflowName: str
    templateId: str
    templateName: str = Field(None)
    isDefault: bool = Field(False)
    transitions: Optional[List[AlertWorkflowTransition]]
    module: str
    assign_user_ids: Optional[List[str]] = Field(None, alias="assignUserIds")

    @validator("module")
    def validate_modules(cls, val: Any):
        if not Module.has_value(val):
            raise ValueError(f"{val} is not a valid Module")
        return val

    @validator("workflowName")
    def validate_name(cls, val: Any):
        if str(val).lower() == SE_GLOBAL_WORKFLOW.lower():
            raise ValueError(f"Workflow name {val} is reserved for global workflow")
        return val


class AlertWorkflowUpdateIn(APIModel):
    description: Optional[str] = Field(None)
    workflowName: Optional[str]
    isDefault: Optional[bool] = Field(False)
    transitions: Optional[List[AlertWorkflowTransition]]

    @validator("workflowName")
    def validate_name(cls, val: Any):
        if str(val).lower() == SE_GLOBAL_WORKFLOW.lower():
            raise ValueError(f"Workflow name {val} is reserved for global workflow")
        return val

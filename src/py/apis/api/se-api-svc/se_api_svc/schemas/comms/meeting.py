# type: ignore
import pydantic
from api_sdk.schemas.base import Field, RecordModel
from datetime import datetime, time
from pydantic import BaseModel
from se_api_svc.schemas.comms.call import MISSING_TRANSCRIPT_TXT
from se_api_svc.schemas.comms.common import Body
from se_api_svc.utils.s3 import get_s3_bucket_from_url
from se_elastic_schema.components.communication.identifiers import (
    Identifiers as ParticipantsIdentifiers,
)
from se_elastic_schema.components.communication.labels import Labels
from se_elastic_schema.components.communication.meeting.meeting_details import MeetingDetails
from se_elastic_schema.components.communication.participant import Participant
from se_elastic_schema.models.tenant.communication.attachment import Attachment
from se_elastic_schema.models.tenant.communication.transcript import Transcript as FullTranscript
from se_elastic_schema.static.comms.meeting import RecordingTypeEnum
from typing import Any, List, Optional


class TranscriptModel(pydantic.BaseModel):
    id: Optional[str]
    language: Optional[str]
    vendor: str


class Transcript(pydantic.BaseModel):
    id: str
    model: TranscriptModel

    class Config:
        extra = pydantic.Extra.allow


class File(BaseModel):
    sizeInBytes: Optional[float]
    type: Optional[str]
    id: Optional[str]

    class Config:
        extra = pydantic.Extra.allow


class Recording(BaseModel):
    file: Optional[File]
    hasTranscript: bool
    sourceKey: Optional[str]
    timeDuration: Optional[time]
    timeEnd: Optional[datetime]
    timeEndLocal: Optional[datetime]
    timeEndTimeZone: Optional[str]
    timeStart: Optional[datetime]
    timeStartLocal: Optional[datetime]
    timeStartTimeZone: Optional[str]
    type: Optional[RecordingTypeEnum]
    url: Optional[Any]

    class Config:
        extra = pydantic.Extra.allow


class Meeting(RecordModel):
    recordings: Optional[List[Recording]]
    labels: Optional[Labels] = Field(None)
    transcripts: Optional[List[Transcript]] = None
    body: Optional[Body]
    meetingDetails: Optional[MeetingDetails]
    sourceKey: Optional[str]
    meetingId: Optional[str]
    comm_type: Optional[str] = None
    participants: Optional[List[Participant]]
    identifiers: Optional[ParticipantsIdentifiers]
    attachments: Optional[List[Attachment]]

    class Config:
        model_name = "Meeting"
        index_suffix = "meeting"

        extra = pydantic.Extra.allow

    def has_transcript(self, id: str) -> bool:
        if not self.transcripts:
            return False
        return len(list(filter(lambda x: x.id == id, self.transcripts))) > 0

    def shim_transcript(self) -> Optional[FullTranscript]:
        """Shims a Transcript from the information available within the Call.

        Use only for calls that don't have a real Transcript attached to
        them.
        """
        if not self.transcripts:
            return None

        last_transcript = self.transcripts[-1]
        if not last_transcript or not self.body or self.body.text == MISSING_TRANSCRIPT_TXT:
            return None

        return FullTranscript(
            id=last_transcript.id,
            isTranscribedBySteelEye=False,
            model=last_transcript.model.dict(by_alias=True),
            isAnnotated=False,
            transcriptSourceKey="Legacy",
            recordingSourceKey="Legacy",
            text=self.body.text,
        )

    def validate_has_attachment_key(self, attachment_key):
        if any(
            [True if record.sourceKey == attachment_key else False for record in self.recordings]
        ):
            return True

        return False

    def get_key_as_attachment(self, attachment_key) -> Optional[Attachment]:
        if not self.validate_has_attachment_key(attachment_key):
            return None

        s3_bucket, s3_key, file_name = get_s3_bucket_from_url(attachment_key)
        attachment = {
            "fileName": file_name,
            "fileInfo": {"location": {"key": s3_key, "bucket": s3_bucket}},
        }

        return Attachment(**attachment)

    def label_comm_type(self, domains):
        is_internal = [True]
        participants = self.participants or []
        for p in participants:
            key = p.value.key__
            if key.find("MarketPerson") > -1:
                is_internal.append(False)

        emails = (self.identifiers and self.identifiers.allIds) or []
        email_internal_check = [True]

        for email in emails:
            email_domains = [False]

            for domain in domains:
                if email.find(domain) > -1:
                    email_domains.append(True)
                else:
                    email_domains.append(False)

            email_internal_check.append(any(email_domains))

        is_internal.append(all(email_internal_check))

        self.comm_type = "INTERNAL" if all(is_internal) else "EXTERNAL"

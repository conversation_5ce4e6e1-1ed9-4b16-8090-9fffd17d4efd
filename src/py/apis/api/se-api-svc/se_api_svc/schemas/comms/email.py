# type: ignore
import pydantic
from api_sdk.schemas.base import Field, RecordModel
from se_elastic_schema.components.communication.labels import Labels
from se_elastic_schema.models.tenant.communication.attachment import Attachment
from typing import List, Optional


class Email(RecordModel):
    attachments: Optional[List[Attachment]]

    class Config:
        model_name = "Email"
        index_suffix = "email"

        extra = pydantic.Extra.allow

    labels: Optional[Labels] = Field(None)

# type: ignore
import datetime as dt
import pydantic
from api_sdk.models.search import SearchResult
from api_sdk.schemas.base import APIModel, RecordModel
from pydantic import Field
from se_elastic_schema.components.communication.identifiers import Identifiers
from se_elastic_schema.components.communication.metadata import Metadata
from se_elastic_schema.components.communication.timestamp import Timestamp
from se_elastic_schema.models.tenant.communication.attachment import Attachment
from typing import List, Optional, Union


class ChatRoom(APIModel):
    roomId: str
    roomName: Optional[str]
    count: int


class ChatSummaryByPeopleItem(APIModel):
    email: str
    count: int
    name: Optional[str] = None
    company: Optional[str] = None
    id: Optional[str] = None
    key: Optional[str] = None


class ChatSummaryByCompanyItem(APIModel):
    count: int
    company: str


class ChatSummaryBySenderRecipient(APIModel):
    sender: List[ChatSummaryByPeopleItem]
    recipient: List[ChatSummaryByPeopleItem]


class ChatSummaryByTimeItem(APIModel):
    datetime: dt.datetime
    count: int


class ChatEvent(RecordModel):
    class Config:
        model_name = "ChatEvent"
        index_suffix = "chat_event"

        extra = pydantic.Extra.allow

    group_id: Optional[str] = None
    timestamps: Optional[Timestamp] = pydantic.Field(None)


class Message(RecordModel):
    attachments: Optional[List[Attachment]]
    chatType: Optional[str] = Field(None)
    metadata: Optional[Metadata] = Field(None, description="metadata")
    roomName: Optional[str] = Field(None)
    identifiers: Optional[Identifiers] = Field(None)
    timestamps: Optional[Timestamp] = pydantic.Field(None)

    class Config:
        model_name = "Message"
        index_suffix = "message"

        extra = pydantic.Extra.allow


class Text(RecordModel):
    class Config:
        model_name = "Text"
        index_suffix = "text"

        extra = pydantic.Extra.allow


class MessageRoomResponse(APIModel):
    roomId: str
    roomName: Optional[str]
    roomSource: Optional[str]
    chatType: Optional[str]
    count: int
    createdBy: Optional[str]
    createdOn: dt.datetime


class MessagesResponse(SearchResult):
    results: List[Union[Message, ChatEvent]]


class MessageId(APIModel):
    id: str


# For getting message list for /download/comms/chat-room/messages
class MessageList(APIModel):
    messages: Optional[List[MessageId]]

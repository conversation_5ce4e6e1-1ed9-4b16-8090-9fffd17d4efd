from datetime import datetime
from pydantic import BaseModel


class RiskFields(BaseModel):
    riskScore: int
    riskExplanation: str


class NeuralReviewResponse(RiskFields):
    riskCategory: str
    l1: RiskFields
    l2: RiskFields
    l3: RiskFields


class NeuralReviewsOut(NeuralReviewResponse):
    id: str
    text: str
    timestamp: datetime


class NeuralReviewInput(BaseModel):
    reviewText: str

# type: ignore
from api_sdk.schemas.base import APIModel, Field
from api_sdk.schemas.orders.common import OrdersTrendChart
from api_sdk.utils.utils import StringEnum
from enum import Enum, auto
from pydantic import validator
from se_elastic_schema.components.fx.ecb_ref_rate import EcbRefRate
from typing import Dict, List, Optional


class OrderTypeChart(StringEnum):
    ORDER_QUANTITY = auto()
    EXECUTED_QUANTITY = auto()
    EXECUTED_NOTIONAL = auto()
    COUNT_OF_ORDER = auto()
    COUNT_OF_EXECUTION = auto()
    COUNT_OF_ORDER_EVENTS = auto()


class ExecutionsTrendChart(StringEnum):
    COUNTERPARTY = auto()
    TRADER = auto()


class OrdersTrendChartMetric(StringEnum):
    BUYSELL_RATIO = auto()
    VOLUME = auto()


class OrdersPricingMetric(StringEnum):
    PERCENT_BID_ASK_SPREAD = "percentVsBidAskSpread"
    PERCENT_NEAREST_QUOTE = "percentVsNearestQuote"
    PERCENT_VS_ARRIVAL = "percentVsArrival"
    PERCENT_VS_ASK_PRICE = "percentVsAskPrice"
    PERCENT_VS_BEST_ASK = "percentVsBestAsk"
    PERCENT_VS_BEST_BID = "percentVsBestBid"
    PERCENT_VS_BID_PRICE = "percentVsBidPrice"
    PERCENT_VS_CLOSE = "percentVsClose"
    PERCENT_VS_INTERNAL_TWAP = "percentVsInternalTwap"
    PERCENT_VS_INTERNAL_VWAP = "percentVsInternalVwap"
    PERCENT_VS_MARKET = "percentVsMarket"
    PERCENT_VS_MIDPOINT_PRICE = "percentVsMidPointPrice"
    PERCENT_VS_MOVING_VWAP = "percentVsMovingVwap"
    PERCENT_VS_OPEN = "percentVsOpen"
    PERCENT_VS_PREVIOUS_DAY_CLOSE = "percentVsPreviousDayClose"
    PERCENT_VS_PREVIOUS_DAY_OPEN = "percentVsPreviousDayOpen"
    PERCENT_VS_SLIPPAGE = "percentVsSlippage"
    PERCENT_VS_T_PLUS_ONE = "percentVsTPlusOne"
    PERCENT_VS_T_PLUS_TEN = "percentVsTPlusTen"
    PERCENT_VS_VENUE_VWAP = "percentVsVenueVwap"
    PERCENT_VS_VWAP = "percentVsVwap"
    PERCENT_VS_ARRIVAL_PRICE_MID = "percentVsArrivalPriceMid"
    PERCENT_VS_ARRIVAL_PRICE_FIRST_FILL = "percentVsArrivalPriceFirstFill"
    PERCENT_VS_ARRIVAL_PRICE_MARKET_SUBMISSION = "percentVsArrivalPriceMarketSubmission"
    PERCENT_VS_LIMIT_VS_EXECUTION = "percentVsLimitVsExecution"
    PERCENT_VS_NEAR_TOUCH = "percentVsNearTouch"
    PERCENT_VS_PREVAL_1M = "percentVsPreval1Minute"
    PERCENT_VS_PREVAL_5M = "percentVsPreval5Minutes"
    PERCENT_VS_PREVAL_10M = "percentVsPreval10Minutes"
    PERCENT_VS_PREVAL_1H = "percentVsPreval1Hour"
    PERCENT_VS_PREVAL_2H = "percentVsPreval2Hours"
    PERCENT_VS_PREVAL_1D = "percentVsPreval1Day"
    PERCENT_VS_PREVAL_5D = "percentVsPreval5Days"
    PERCENT_VS_PREVAL_10D = "percentVsPreval10Days"
    PERCENT_VS_PREVAL_20D = "percentVsPreval20Days"
    PERCENT_VS_VWAP_ALL_DAY_LIMIT_ADJUSTED = "percentVsVwapAllDayLimitAdjusted"
    PERCENT_VS_VWAP_START_TO_CLOSE = "percentVsVwapStartToClose"
    PERCENT_VS_VWAP_START_TO_CLOSE_LIMIT_ADJUSTED = "percentVsVwapStartToCloseLimitAdjusted"
    PERCENT_VS_VWAP_START_TO_EXECUTION = "percentVsVwapStartToExecution"
    PERFORMANCE_VS_ARRIVAL_PRICE_MID = "performanceVsArrivalPriceMid"
    PERFORMANCE_VS_ARRIVAL_PRICE_FIRST_FILL = "performanceVsArrivalPriceFirstFill"
    PERFORMANCE_VS_ARRIVAL_PRICE_MARKET_SUBMISSION = "performanceVsArrivalPriceMarketSubmission"
    PERFORMANCE_VS_LIMIT_VS_EXECUTION = "performanceVsLimitVsExecution"
    PERFORMANCE_VS_NEAR_TOUCH = "performanceVsNearTouch"
    PERFORMANCE_VS_PREVAL_1_MINUTE = "performanceVsPreval1Minute"
    PERFORMANCE_VS_PREVAL_5_MINUTES = "performanceVsPreval5Minutes"
    PERFORMANCE_VS_PREVAL_10_MINUTES = "performanceVsPreval10Minutes"
    PERFORMANCE_VS_PREVAL_1_HOUR = "performanceVsPreval1Hour"
    PERFORMANCE_VS_PREVAL_2_HOURS = "performanceVsPreval2Hours"
    PERFORMANCE_VS_PREVAL_1_DAY = "performanceVsPreval1Day"
    PERFORMANCE_VS_PREVAL_5_DAYS = "performanceVsPreval5Days"
    PERFORMANCE_VS_PREVAL_10_DAYS = "performanceVsPreval10Days"
    PERFORMANCE_VS_PREVAL_20_DAYS = "performanceVsPreval20Days"
    PERFORMANCE_VS_VWAP_ALL_DAY_LIMIT_ADJUSTED = "performanceVsVwapAllDayLimitAdjusted"
    PERFORMANCE_VS_VWAP_START_TO_CLOSE = "performanceVsVwapStartToClose"
    PERFORMANCE_VS_VWAP_START_TO_CLOSE_LIMIT_ADJUSTED = "performanceVsVwapStartToCloseLimitAdjusted"
    PERFORMANCE_VS_VWAP_START_TO_EXECUTION = "performanceVsVwapStartToExecution"
    PERFORMANCE_VS_VWAP_SUBMIT_TO_EXECUTION = "performanceVsVwapSubmitToExecution"
    PERFORMANCE_VS_ARRIVAL = "performanceVsArrival"
    PERFORMANCE_VS_ASK_PRICE = "performanceVsAskPrice"
    PERFORMANCE_VS_BEST_ASK = "performanceVsBestAsk"
    PERFORMANCE_VS_BEST_BID = "performanceVsBestBid"
    PERFORMANCE_VS_BID_ASK_SPREAD = "performanceVsBidAskSpread"
    PERFORMANCE_VS_BID_PRICE = "performanceVsBidPrice"
    PERFORMANCE_VS_CLOSE = "performanceVsClose"
    PERFORMANCE_VS_INTERNAL_TWAP = "performanceVsInternalTwap"
    PERFORMANCE_VS_INTERNAL_VWAP = "performanceVsInternalVwap"
    PERFORMANCE_VS_INTERVAL_TWAP = "performanceVsIntervalTwap"
    PERFORMANCE_VS_INTERVAL_VWAP = "performanceVsIntervalVwap"
    PERFORMANCE_VS_MARKET = "performanceVsMarket"
    PERFORMANCE_VS_MID_POINT_PRICE = "performanceVsMidPointPrice"
    PERFORMANCE_VS_MOVING = "performanceVsMoving"
    PERFORMANCE_VS_MOVING_VWAP = "performanceVsMovingVwap"
    PERFORMANCE_VS_NEAREST_QUOTE = "performanceVsNearestQuote"
    PERFORMANCE_VS_OPEN = "performanceVsOpen"
    PERFORMANCE_VS_PREVIOUS_DAY = "performanceVsPreviousDay"
    PERFORMANCE_VS_PREVIOUS_DAY_CLOSE = "performanceVsPreviousDayClose"
    PERFORMANCE_VS_PREVIOUS_DAY_OPEN = "performanceVsPreviousDayOpen"
    PERFORMANCE_VS_SLIPPAGE = "performanceVsSlippage"
    PERFORMANCE_VS_T_PLUS_ONE = "performanceVsTPlusOne"
    PERFORMANCE_VS_T_PLUS_TEN = "performanceVsTPlusTen"
    PERFORMANCE_VS_VENUE_VWAP = "performanceVsVenueVwap"
    PERFORMANCE_VS_VWAP = "performanceVsVwap"


class OrdersSpeedMetric(StringEnum):
    TIME_TO_FILL = "timeToFill"
    EXTERNAL_TIME_TO_FILL = "externalTimeToFill"
    INTERNAL_TIME_TO_FILL = "internalTimeToFill"


ecbRefRateKeys = list(EcbRefRate.__fields__.keys())
currency = [(x, x) for x in ecbRefRateKeys if len(x) == 3]
volume_other_metric = [
    ("INITIAL_QUANTITY", "initialQuantity"),
    ("TRADED_QUANTITY", "tradedQuantity"),
]
volume_metrics = [*currency, *volume_other_metric]
OrdersVolumeMetric = Enum("OrdersVolumeMetric", volume_metrics, module=__name__)


class OrdersTradingChart(StringEnum):
    ASSET_CLASS = auto()
    CFI_ATTRIBUTE_1 = auto()
    CFI_ATTRIBUTE_2 = auto()
    CFI_ATTRIBUTE_3 = auto()
    CFI_ATTRIBUTE_4 = auto()
    CFI_CATEGORY = auto()
    CFI_GROUP = auto()


class OrdersTradingMetric(StringEnum):
    ORDER_QUANTITY = auto()
    TRADED_NOTIONAL = auto()
    TRADING_QUANTITY = auto()
    COUNT_OF_ORDER = auto()
    COUNT_OF_EXECUTION = auto()


class OrdersTradeParticipantTrend(StringEnum):
    NAME = auto()
    NATIONALITY = auto()
    HOME_ADDRESS_COUNTRY = auto()
    HOME_ADDRESS_CITY = auto()
    OFFICE_ADDRESS_COUNTRY = auto()
    OFFICE_ADDRESS_CITY = auto()
    CLIENT_MANDATE = auto()
    EMPLOYEE_ID = auto()
    TRADER_ID = auto()
    DECISION_MAKER = auto()
    INSTRUMENT = auto()
    ROLE = auto()
    SMCR_FUNCTION = auto()
    SMCR_FUNCTION_CATEGORY = auto()
    SMCR_REVIEW_DATE = auto()
    TYPE = auto()
    PARTICIPANT_BRANCH_COUNTRY = auto()
    DEPARTMENT = auto()
    ACCOUNT_CURRENCY = auto()
    ACCOUNT_CLOSE_DATE = auto()
    ACCOUNT_OPENED_DATE = auto()
    ASSETS_CATEGORY = auto()
    RISK_SCORE = auto()
    STATUS = auto()


class GetOrdersPricingSpreadIn(APIModel):
    field: OrdersPricingMetric = Field(None)
    max: float = Field(None, le=1, ge=-1)
    min: float = Field(None, le=1, ge=-1)

    @validator("min")
    def min_greater_than_max(cls, v, values: Dict):
        max_val = values.get("max")
        if isinstance(v, float) and isinstance(max_val, float) and v >= max_val:
            raise ValueError(f"min value: {v} must be less than max value: {max_val}")
        return v


class GetOrdersSpeedIn(APIModel):
    max: float = Field(None, le=1200000, ge=0)
    min: float = Field(None, le=1200000, ge=0)

    @validator("min")
    def min_greater_than_max(cls, v, values: Dict):
        max_val = values.get("max")
        if isinstance(v, float) and isinstance(max_val, float) and v >= max_val:
            raise ValueError(f"min value: {v} must be less than max value: {max_val}")
        return v


class GetOrdersTrendsIn(APIModel):
    field: OrdersTrendChart = Field(None)
    values: List[str] = Field(None)


class GetOrdersIn(APIModel):
    asset_classes: Optional[List[str]] = Field(None, alias="assetClasses")
    instrument_ids: Optional[List[str]] = Field(None, alias="instrumentIds")
    filter_execution_keys: Optional[List[str]] = Field(None, alias="executionKeys")
    time_to_fill: Optional[int] = Field(None, alias="timeToFill")
    internal_time_to_fill: Optional[int] = Field(None, alias="internalTimeToFill")
    external_time_to_fill: Optional[int] = Field(None, alias="externalTimeToFill")
    ecb_ref_rate: Optional[int] = Field(None, alias="ecbRefRate")
    order_volume_native: Optional[int] = Field(None, alias="orderVolume")
    keys: Optional[List[str]]
    price_spreads: Optional[List[GetOrdersPricingSpreadIn]] = Field(None, alias="priceSpreads")
    speed: Optional[GetOrdersSpeedIn] = Field(None)
    trends: Optional[List[GetOrdersTrendsIn]] = Field(None)


class OrdersTradeFirmsTrends(StringEnum):
    NAME = auto()
    KYC_APPROVED = auto()
    RETAIL_OR_PROFESSIONAL = auto()
    FIRM_TYPE = auto()
    CLIENT_MANDATE = auto()
    LEI_REGISTRATION_STATUS = auto()
    BRANCH_COUNTRY = auto()
    REGISTERED_ADDRESS = auto()
    REGISTERED_ADDRESS_CITY = auto()
    REGISTERED_ADDRESS_COUNTRY = auto()
    REGISTERED_ADDRESS_POSTAL_CODE = auto()
    TRADING_ADDRESS = auto()
    TRADING_ADDRESS_CITY = auto()
    TRADING_ADDRESS_COUNTRY = auto()
    TRADING_ADDRESS_POSTAL_CODE = auto()


class TimeOperator(StringEnum):
    OUTSIDE = auto()
    BETWEEN = auto()


class TimeType(StringEnum):
    ORDER = auto()
    TRADE = auto()


class GetOrdersAndExecutionsTimeIn(APIModel):
    end: Optional[int] = None
    operator: Optional[TimeOperator] = None
    start: Optional[int] = None
    type: Optional[TimeType] = None

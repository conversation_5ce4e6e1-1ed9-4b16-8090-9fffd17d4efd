import enum
import uuid
from api_sdk.schemas.static import <PERSON>du<PERSON>
from sqlalchemy.dialects.postgresql import UUID


class APIWorkflows:
    CASE_BULK_WORKFLOW = "case_bulk"
    ON_DEMAND_API_TRANSLATION = "on_demand_api_translation"


class StatusEnum(enum.Enum):
    PROCESSING = "PROCESSING"
    READY = "READY"
    FAILED = "FAILED"


# These are the permissions which can have WITH_APPROVAL status
pending_allowed_permissions = [
    # Case manager
    ("LEGAL_HOLD", "CASE_MANAGER", "WORKFLOW"),
    # CSurv
    ("CREATION", Module.COMMS_SURVEILLANCE.name, "LEXICA"),
    ("DELETION_STEELEYE_LEXICA", Module.COMMS_SURVEILLANCE.name, "LEXICA"),
    ("DELETION_CUSTOM_LEXICA", Module.COMMS_SURVEILLANCE.name, "LEXIC<PERSON>"),
    ("EDIT_CUSTOM_LEXICA", Module.COMMS_SURVEILLANCE.name, "LEXICA"),
    ("EDIT_STEELEYE_LEXICA", Module.COMMS_SURVEILLANCE.name, "LEXICA"),
    ("UPGRADE", Module.COMMS_SURVEILLANCE.name, "LEXICA"),
    ("SCHEDULE_CREATION", Module.COMMS_SURVEILLANCE.name, "WATCH_AND_SCHEDULE"),
    ("SCHEDULE_EDIT", Module.COMMS_SURVEILLANCE.name, "WATCH_AND_SCHEDULE"),
    ("WATCH_CREATION", Module.COMMS_SURVEILLANCE.name, "WATCH_AND_SCHEDULE"),
    ("WATCH_EDIT", Module.COMMS_SURVEILLANCE.name, "WATCH_AND_SCHEDULE"),
    ("EDIT_WORKFLOW", Module.COMMS_SURVEILLANCE.name, "WORKFLOW"),
    # TSurv
    ("CREATE", Module.TRADE_SURVEILLANCE.name, "WATCH_AND_RL"),
    ("DELETE", Module.TRADE_SURVEILLANCE.name, "WATCH_AND_RL"),
    ("EDIT", Module.TRADE_SURVEILLANCE.name, "WATCH_AND_RL"),
    ("LIST_CREATION", Module.TRADE_SURVEILLANCE.name, "WATCH_AND_RL"),
    ("LIST_EDIT", Module.TRADE_SURVEILLANCE.name, "WATCH_AND_RL"),
    ("LIST_DELETE", Module.TRADE_SURVEILLANCE.name, "WATCH_AND_RL"),
    ("BACKTEST", Module.TRADE_SURVEILLANCE.name, "WATCH_AND_SCHEDULE"),
    ("SCHEDULE_CREATION", Module.TRADE_SURVEILLANCE.name, "WATCH_AND_SCHEDULE"),
    ("SCHEDULE_EDIT", Module.TRADE_SURVEILLANCE.name, "WATCH_AND_SCHEDULE"),
    ("WATCH_CREATION", Module.TRADE_SURVEILLANCE.name, "WATCH_AND_SCHEDULE"),
    ("WATCH_EDIT", Module.TRADE_SURVEILLANCE.name, "WATCH_AND_SCHEDULE"),
    ("EDIT_WORKFLOW", Module.TRADE_SURVEILLANCE.name, "WORKFLOW"),
]


class PyUUID(UUID):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        try:
            uuid.UUID(str(v))
            return v
        except ValueError:
            return None

    @classmethod
    def __modify_schema__(cls, field_schema):
        field_schema.update(type="string")

import random
from api_sdk.schemas.base import APIModel
from api_sdk.utils.utils import StringEnum
from enum import auto
from typing import Optional

DEFAULT_STREAM_RETRY_TIMEOUT_RANGE = (30000, 80000)  # in millisecond


class StreamEvents(StringEnum):
    COMPLETED = auto()
    ERROR = auto()
    UPDATED = auto()
    QUEUED = auto()

    def __str__(self):
        return self.value


class StreamOut(APIModel):
    event: StreamEvents
    data: Optional[str] = None
    retry: Optional[int] = random.randint(*DEFAULT_STREAM_RETRY_TIMEOUT_RANGE)

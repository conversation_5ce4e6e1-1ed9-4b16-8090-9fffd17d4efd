# type: ignore
import base64
import datetime as dt
import logging
import string
import sys
import uuid
from ...schemas.attachment import FileInfo
from ...schemas.comms import Call, Email, Meeting, Message, Text
from ...schemas.order import Order
from ...schemas.surveillance.alerts import CommunicationAlert
from ...schemas.surveillance.market_abuse import MarketAbuseScenarioTag
from ...services.attachments.attachments import AbstractAttachmentsService
from .records import CaseRecordRepository
from api_sdk.auth import Tenancy
from api_sdk.config_base import ApiConfig
from api_sdk.exceptions import BadRequest, NotFound
from api_sdk.repository.asyncronous.request_bound import RequestBoundRepository
from api_sdk.repository.elastic import AbstractEsRepository
from api_sdk.utils.utils import nested_dict_get, nested_get
from fastapi import Request
from pathlib import Path
from se_api_svc.schemas.cases.cases import (
    <PERSON>ta<PERSON><PERSON>,
    <PERSON>,
    CaseByIdOrSlugSearch,
    CaseComment,
    Case<PERSON><PERSON>in,
    CaseOriginSearch,
    CasesByStatusAgg,
    CasesGroupedBy,
    CasesSearch,
    EventAttachmentIn,
)
from se_db_utils.database import Database
from se_enums.cloud import CloudProviderEnum
from tenant_db.models.case_manager.attachment import CaseAttachment as AttachmentModel
from tenant_db.models.case_manager.events import Event  # noqa: F401
from typing import Optional

log = logging.getLogger(__name__)


class CasesRepository(RequestBoundRepository):
    def __init__(
        self,
        tenancy: Tenancy,
        es_repo: AbstractEsRepository,
        case_record_repo: CaseRecordRepository,
        attachments: AbstractAttachmentsService,
        db: Database,
        api_config: ApiConfig,
        owner: Request,
    ):
        super().__init__(tenancy=tenancy, es_repo=es_repo, owner=owner, db=db)
        self.attachments = attachments
        self.storage_client = attachments.storage_client
        self.case_record_repo = case_record_repo
        self.api_config = api_config

    @property
    def upload_bucket(self):
        if self.api_config.CLOUD_PROVIDER.lower() == CloudProviderEnum.AWS:
            return self.tenancy.realm

        return self.tenancy.tenant

    async def get_one_by_id_or_slug(self, id_or_slug):
        return await self.get_one(Case, search_model=CaseByIdOrSlugSearch(identifier=id_or_slug))

    async def get_case_by_identifier(self, identifier: str) -> Case:
        """If the provided identifier doesn't exist, this raises a `BadRequest`
        error which will result in a 400 error for the client."""
        try:
            return await self.get_one_by_id_or_slug(identifier)
        except NotFound:
            raise BadRequest(f"Case '{identifier}' doesn't exist")

    async def get_many_cases(self, **kwargs):
        """Returns many records of Case models (cases are mixed in their index
        with their children)"""
        kwargs["search"] = kwargs.pop("model_qs", None)
        return await self.get_many(
            Case,
            search_model=CasesSearch(
                model=Case,
                **kwargs,
            ),
        )

    async def get_cases_summary_by_status(self, **params):
        search_model = CasesByStatusAgg(**params)

        agg_items = await self.get_aggs(search_model=search_model)

        agg_items = nested_dict_get(agg_items.aggregations, "STATUSES.buckets")

        return {key: item["doc_count"] for (key, item) in agg_items.items()}

    @staticmethod
    def process_group_aggs(aggs):
        return {
            "groups": [
                {
                    "key": a.get("key"),
                    "doc_count": a.get("doc_count"),
                    "source_info": (
                        a["HIT_INFO"]["hits"]["hits"][0]["_source"] if a.get("HIT_INFO") else None
                    ),
                }
                for a in aggs["GROUPED_BY"]["buckets"]
            ]
        }

    async def get_cases_grouped_by_count(self, group_by, **params):
        source_include_fields = (
            ["assignee.name"]
            if group_by == "assignee.userId"
            else (["owner.name"] if group_by == "owner.userId" else None)
        )
        params["search"] = params.pop("search", None) or params.pop("model_qs", None)
        search_model = CasesGroupedBy(group_by, source_include_fields, **params)

        agg_items = await self.get_aggs(search_model=search_model)

        return self.process_group_aggs(agg_items.aggregations)

    @property
    def s3_bucket(self):
        return self.tenancy.realm

    async def delete_comment_attachment(self, case_id: str, attachment_id: str) -> CaseComment:
        case = await self.get_one(Case, case_id)

        comment, attachment = case.find_comment_attachment(attachment_id)
        comment.attachments.remove(attachment)

        await self.save_existing(case)
        await self.delete_existing(attachment)  # Attachment is stored as a separate document too

        try:
            location = attachment.fileInfo.location
            self.storage_client.delete_object(bucket=location["bucket"], key=location["key"])
        except Exception as e:
            log.exception(e)
            log.warning(
                "Failed to delete case comment attachment from S3. Carrying on. "
                f"Attachment: {attachment}"
            )

        log.info(f"Deleted case {case_id!r} comment attachment {attachment_id!r}")
        return comment

    async def process_new_event_attachment(
        self,
        case: Case,
        event_id: str,
        attachment: EventAttachmentIn,
        attachment_repo,
    ):
        now = dt.datetime.utcnow()
        original_name = Path(attachment.name)
        stored_name = original_name.stem + f".{now.strftime('%Y%m%d-%H%M%S')}{original_name.suffix}"
        file_key = f"cases/{case.id_}/events/{event_id}/attachments/{stored_name}"

        log.debug(
            f"Uploading case {case.id_!r} new event {event_id} "
            f"attachment {str(original_name)!r} to bucket={self.s3_bucket!r}, key={file_key!r}"
        )

        try:
            content_to_upload = base64.b64decode(attachment.content)
        except Exception:
            content_to_upload = attachment.content

        self.storage_client.upload_object(
            content_to_upload,
            bucket=self.s3_bucket,
            key=file_key,
        )

        size = sys.getsizeof(attachment.content)

        # Clear content as we have stored it in S3
        attachment.content = None

        # Add attachment to postgres
        attachment_repo.add_one(
            AttachmentModel(
                name=attachment.name,
                eventId=event_id,
                objectUri=self.storage_client.generate_storage_url(
                    bucket=self.tenancy.realm, key=file_key
                ),
                size=size,
                mimeTag=attachment.mimeTag,
                createdBy=self.tenancy.tenant,
            )
        )

    async def process_new_case_comment_attachment(
        self,
        case: Case,
        attachment: Attachment,
    ):
        now = dt.datetime.utcnow()
        original_name = Path(attachment.fileName)
        stored_name = original_name.stem + f".{now.strftime('%Y%m%d-%H%M%S')}{original_name.suffix}"
        prefix = f"cases/{case.id_}/comments/attachments/"
        key = f"{prefix}{stored_name}"
        log.debug(
            f"Uploading case {case.id_!r} new comment "
            f"attachment {str(original_name)!r} to bucket={self.upload_bucket!r}, key={key!r}"
        )
        self.storage_client.upload_object(
            base64.b64decode(attachment.content), key=key, bucket=self.upload_bucket
        )

        attachment.fileInfo = FileInfo(
            location={"bucket": self.upload_bucket, "key": key},
            contentLength=attachment.sizeInBytes,
            processed=now.isoformat(),
        )

        # Clear content as we have stored it in cloud
        attachment.content = None

        # Previously we used to create attachment
        # doc and store them in attachment index
        # but since in future attachment index would be gone,
        # so we are not storing the doc instead creating it in memory
        # and adding it to case only
        # so we need to populate `&id` manually
        attachment.id_ = str(uuid.uuid4())

        return attachment

    async def add_case_origin(self, case):
        """
        Builds and sets a human-readable `displayOrigin` string for the case.

        Extracts origin reference IDs, fetches related aggregation data, formats
        keys into Title Case, and combines them into a descriptive message based
        on the case's origin. Returns the updated case.
        """

        module_map = {
            CaseOrigin.COMMS_SURVEILLANCE.value: "cSurv",
            CaseOrigin.TRADE_SURVEILLANCE.value: "tSurv",
            CaseOrigin.BEST_EX.value: "BestEx",
            CaseOrigin.COMMUNICATIONS.value: "Comms",
            CaseOrigin.ORDERS_AND_TRADES.value: "Orders & Trades",
        }

        origin_record_model_map = {
            CaseOrigin.COMMS_SURVEILLANCE.value: [CommunicationAlert],
            CaseOrigin.TRADE_SURVEILLANCE.value: [MarketAbuseScenarioTag],
            CaseOrigin.BEST_EX.value: [Order],
            CaseOrigin.COMMUNICATIONS.value: [Call, Email, Text, Message, Meeting],
            CaseOrigin.ORDERS_AND_TRADES.value: [Order],
        }

        def to_title_case(value: str) -> str:
            """Convert string to Title Case, replacing underscores with spaces."""
            if not value:
                return value
            return string.capwords(value.replace("_", " ").lower())

        if case.origin not in module_map.keys():
            return

        case_origin_ids = [
            parts[1]
            for ref_id in getattr(case, "origin_reference_ids") or []
            if (parts := ref_id.split("@")) and len(parts) > 1
        ]

        models = origin_record_model_map.get(case.origin, [])

        agg_items = await self.get_aggs(
            record_model=models,
            search_model_cls=CaseOriginSearch,
            filter_ids=case_origin_ids,
        )

        message_sub_part_1_parts, message_sub_part_2_parts = [], []

        for bucket in agg_items.iter_raw_bucket_agg(case.origin):
            if key := bucket.get("key"):
                message_sub_part_1_parts.append(str(key))

            behaviour = nested_get(bucket, "INNER_AGGS.buckets", default=[])

            for item in behaviour:
                message_sub_part_2_parts.append(nested_get(item, "key", default=""))

        message_sub_part_1 = ", ".join(message_sub_part_1_parts)
        message_sub_part_2 = ", ".join(message_sub_part_2_parts)
        case_origin_message = (
            f"{module_map.get(case.origin, case.origin)} - {to_title_case(message_sub_part_1)}"
            + (f" - {to_title_case(message_sub_part_2)}" if message_sub_part_2 else "")
        )

        setattr(case, "displayOrigin", case_origin_message)


class CaseResolutionRepository(RequestBoundRepository):
    model = Case

    async def get_case_resolutions(self, search: Optional[str]):
        if search:
            search = search.lower()

        data = await self.get_aggs(
            self.model,
            aggs={
                "custom_reason": {
                    "filter": {"wildcard": {"closedResolutionCategoryCustom.text": f"*{search}*"}},
                    "aggs": {
                        "reasons": {
                            "terms": {"field": "closedResolutionCategoryCustom"},
                        }
                    },
                }
            },
        )
        return [r["key"] for r in data.aggregations["custom_reason"]["reasons"]["buckets"]]

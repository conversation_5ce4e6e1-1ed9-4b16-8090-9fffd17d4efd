import datetime
from api_sdk.auth import Tenancy
from api_sdk.cloud.abstractions_sync.abstract_storage_client import AbstractStorageClient
from se_api_svc.repository.download.esceptions import AlreadyCreated
from se_api_svc.schemas.static import StatusEnum
from sql_query_builder.utils.client import SQLClient
from sqlalchemy import insert, update
from starlette.requests import Request
from tenant_db.models.download.download import Download  # type: ignore
from typing import Any, BinaryIO, Optional

DOWNLOAD_BUCKET_PATH = "downloads/"


class CreateFile:
    download_id: Optional[Any]
    directory: Optional[str]  # Key without filename, should end with "/"
    filename: Optional[str]

    def __init__(
        self,
        request: Request,
        sql_client: SQLClient,
        tenancy: Tenancy,
        storage_client: AbstractStorageClient,
    ):
        """This is download file found class.

        ToDo: Need to handle (check boxes once implemented)
        [ ] Enc/Dec
        [ ] Extension guessing
        """
        self.sql_client = sql_client
        self.tenancy = tenancy
        self.storage_client = storage_client
        self.download_id = None
        self.directory = DOWNLOAD_BUCKET_PATH  # Key without filename, should end with "/"
        self.filename = None

    @property
    def key(self) -> str:
        return f"{self.directory}{self.filename}"

    @staticmethod
    def suffix() -> str:
        """Whatever you want to add as a suffix for filename, return."""
        return datetime.datetime.utcnow().strftime("%Y-%m-%d_%H-%M-%S")

    def _done(self, status: str, msg: Optional[str] = None) -> None:
        # Success or fail
        with self.sql_client.db.session(tenant_schema=self.tenancy.tenant) as session:
            values = {
                "status": status,
                "updatedBy": self.tenancy.principal,
                "updatedDateTime": datetime.datetime.utcnow(),
                "generatedLink": self.storage_client.generate_storage_url(
                    bucket=self.tenancy.realm, key=self.key
                ),
            }
            values.update({Download.description.name: msg}) if msg else None
            stmt = update(Download).where(Download.id == self.download_id).values(**values)
            session.execute(stmt)
            session.commit()

    def draft(self, directory: str, filename: str) -> None:
        """Create a draft download file.

        @:raises AlreadyCreated: if a download file has already been created
        """
        if self.download_id:
            raise AlreadyCreated()

        self.directory += directory + "/" if not directory.endswith("/") else directory  # type: ignore
        self.filename = filename

        with self.sql_client.db.session(tenant_schema=self.tenancy.tenant) as session:
            stmt = insert(Download).values(
                status=StatusEnum.PROCESSING.value, createdBy=self.tenancy.principal
            )
            result = session.execute(stmt)
            self.download_id = result.inserted_primary_key.id
            session.commit()

    def fail(self, msg: str) -> None:
        self._done(status=StatusEnum.FAILED.value, msg=msg)

    def save_obj(self, io_obj: BinaryIO):
        """Upload file to cloud.

        Before this, all the validation, updating content should happen, like:
        - Extension guessing
        - Filename
        - UTF-8 encoding
        - Excel BOM
        """
        self.storage_client.upload_object(data_obj=io_obj, bucket=self.tenancy.realm, key=self.key)
        self._done(status=StatusEnum.READY.value, msg="File uploaded successfully.")

# type: ignore
# flake8: noqa
import collections
import datetime as dt
import logging
from dataclasses import dataclass
from enum import auto
from functools import cached_property
from typing import Dict, List, Optional, Union
from api_sdk.full_text_search import (
    get_free_text_search_filters,
    CSURV_ALERT_TEXT_SEARCH_FIELDS,
    prefix_hit,
    COMMS_TEXT_ANALYTICS_SEARCH_FIELDS,
    COMMS_TEXT_PARTICIPANTS_SEARCH_FIELDS,
    COMMS_TEXT_NON_NESTED_SEARCH_FIELDS,
    TSURV_ALERT_TEXT_SEARCH_FIELDS,
)
from api_sdk.es_dsl.base import (
    And,
    Exists,
    ModelFilter,
    Not,
    NotExpired,
    NotExpiredFilter,
    Or,
    SearchFeature,
    SearchFeatureConfig,
    SearchModel,
    TermFilter,
    QueryString,
)
from api_sdk.es_dsl.features import Nested, RangeFilter
from api_sdk.es_dsl.flang import FlangFilter
from api_sdk.es_dsl.params import SearchModelParams
from api_sdk.es_dsl.utils import json_dumps
from api_sdk.models.elasticsearch import RawResult
from api_sdk.models.search import Sort
from api_sdk.repository.asyncronous.request_bound import RepoHelpersMixin
from api_sdk.utils.intervals import get_interval_in_millis_from_params
from api_sdk.utils.utils import StringEnum, nested_dict_get
from schema_sdk.steeleye_model.gamma import GammaMetaFields
from se_api_svc.core.config import ApiServiceConfig
from se_api_svc.core.constants import DATE_TIME_ISO_FORMAT, ES_MAX_AGG_SIZE
from se_api_svc.repository.common import get_alert_record_models, CustomClassProperty
from se_api_svc.schemas.surveillance.alerts import (
    AlertByIdOrSlugSearch,
    CommunicationAlert,
    OrderAlert,
    SurveillanceAlert,
    SlugOut,
)
from se_api_svc.schemas.surveillance.market_abuse import (
    MarketAbuseAlert,
    MarketAbuseScenarioTag,
)
from se_api_svc.schemas.surveillance.watches import (
    TradeSurveillanceTrendChart,
    WatchBase,
    CommsSurveillanceTrendChart,
)
from se_api_svc.schemas.surveillance.workflow import (
    WorkflowAssigneeFilterOptions,
    WorkflowStatus,
)
from se_api_svc.schemas.track import ModuleTitle
from se_elastic_schema.components.surveillance.alert_workflow_base import (
    AlertWorkflowBase,
)
from se_elastic_schema.static.surveillance import (
    AlertHitStatus,
    WatchQueryType,
    WatchType,
)
from api_sdk.static import FieldType

log = logging.getLogger(__name__)


PERSON_TYPES = [
    "buyer",
    "seller",
    "buyerDecisionMaker",
    "sellerDecisionMaker",
    "trader",
]

NEEDS_POLICY = [{"tenant": "cheynecapital", "email": "<EMAIL>"}]

TRADE_SURVEILLANCE_TREND_CHART_SPEC = {
    TradeSurveillanceTrendChart.ASSET_CLASS: (
        "instrumentDetails.instrument.ext.bestExAssetClassMain",
        False,
    ),
    TradeSurveillanceTrendChart.ASSET_CLASS_SUB: (
        "instrumentDetails.instrument.ext.bestExAssetClassSub",
        False,
    ),
    TradeSurveillanceTrendChart.INVESTMENT_DECISION_MAKER: (
        "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.name",
        False,
    ),
    TradeSurveillanceTrendChart.INSTRUMENT_FULL_NAME: (
        "instrumentDetails.instrument.instrumentFullName",
        False,
    ),
    TradeSurveillanceTrendChart.PAD: ("marDetails.isPersonalAccountDealing", False),
    TradeSurveillanceTrendChart.PEOPLE: (PERSON_TYPES, False),
    TradeSurveillanceTrendChart.STATUS: ("workflow.status", False),
    TradeSurveillanceTrendChart.ASSIGNEE: ("workflow.assigneeName", False),
    TradeSurveillanceTrendChart.RESOLUTION_CATEGORY: (
        "workflow.resolutionCategory",
        False,
    ),
    TradeSurveillanceTrendChart.RESOLUTION_SUB_CATEGORY: (
        "workflow.resolutionSubCategories",
        False,
    ),
}


COMMS_SURVEILLANCE_TREND_CHART_SPEC = {
    CommsSurveillanceTrendChart.PARTICIPANTS: ("participants", False),
    CommsSurveillanceTrendChart.ASSIGNEE: ("workflow.assigneeName", False),
    CommsSurveillanceTrendChart.STATUS: ("workflow.status", False),
    CommsSurveillanceTrendChart.RESOLUTION_CATEGORY: (
        "workflow.resolutionCategory",
        False,
    ),
    CommsSurveillanceTrendChart.RESOLUTION_SUB_CATEGORY: (
        "workflow.resolutionSubCategories",
        False,
    ),
    CommsSurveillanceTrendChart.RESOLUTION_SUB_CATEGORY_CUSTOM: (
        "workflow.resolutionSubCategoryCustom",
        False,
    ),
    CommsSurveillanceTrendChart.SENDER_ID: ("hit.identifiers.fromId", False),
}

PARTICIPANTS_FROM_TYPE_FILTER = {
    "bool": {
        "must": TermFilter(name="hit.participants.types", value=["FROM"]).to_dict(
            meta_fields=GammaMetaFields
        )
    }
}

IDENTIFIERS_FROM_TYPE_FILTER = {
    "bool": {
        "must": TermFilter(name="hit.identifiers.domains.types", value=["FROM"]).to_dict(
            meta_fields=GammaMetaFields
        )
    }
}

COMMS_SURVEILLANCE_TREND_CHART_PARTICIPANTS_FIELDS_SPEC = {
    CommsSurveillanceTrendChart.PARTICIPANTS: (
        "hit.participants.value.name",
        "hit.participants",
        {
            "bool": {
                "should": [
                    {"prefix": {"hit.participants.value.&key": "MarketPerson"}},
                    {"prefix": {"hit.participants.value.&key": "AccountPerson"}},
                    Not(Exists(field="hit.participants.value.&key")).to_dict(
                        meta_fields=GammaMetaFields
                    ),
                ],
                "minimum_should_match": 1,
            }
        },
    ),
    CommsSurveillanceTrendChart.SENDER: (
        "hit.participants.value.name",
        "hit.participants",
        PARTICIPANTS_FROM_TYPE_FILTER,
    ),
    CommsSurveillanceTrendChart.SENDER_ID_DOMAIN: (
        "hit.identifiers.domains.value",
        "hit.identifiers.domains",
        IDENTIFIERS_FROM_TYPE_FILTER,
    ),
    CommsSurveillanceTrendChart.SENDER_COUNTRY: (
        "hit.participants.value.location.homeAddress.country",
        "hit.participants",
        PARTICIPANTS_FROM_TYPE_FILTER,
    ),
    CommsSurveillanceTrendChart.SENDER_DEPARTMENT: (
        "hit.participants.value.structure.department",
        "hit.participants",
        PARTICIPANTS_FROM_TYPE_FILTER,
    ),
    CommsSurveillanceTrendChart.SENDER_DESK: (
        "hit.participants.value.structure.desks.name",
        ["hit.participants.value.structure.desks", "hit.participants"],
        PARTICIPANTS_FROM_TYPE_FILTER,
    ),
    CommsSurveillanceTrendChart.SENDER_DESK_ID: (
        "hit.participants.value.structure.desks.id",
        ["hit.participants.value.structure.desks", "hit.participants"],
        PARTICIPANTS_FROM_TYPE_FILTER,
    ),
    CommsSurveillanceTrendChart.SENDER_ROLE: (
        "hit.participants.value.structure.role",
        "hit.participants",
        PARTICIPANTS_FROM_TYPE_FILTER,
    ),
}


class AlertsWatchIdFilter(SearchFeature):
    @dataclass
    class Config(SearchFeatureConfig):
        watch_id: Optional[str] = None
        execution_id: Optional[str] = None

    config: Config

    def build_q(self, params=None, *, meta_fields):
        if params.watch_id:
            return TermFilter(name="detail.watchId", param="watch_id").build_q(
                params=params, meta_fields=meta_fields
            )


class AlertsModuleFilter(SearchFeature):
    @dataclass
    class Config(SearchFeatureConfig):
        module: Optional[ModuleTitle] = None

    config: Config

    def build_q(self, params=None, *, meta_fields):
        module = getattr(params, "module", None) or self.config.module
        if not module:
            return

        models = {
            ModuleTitle.TRADE_SURVEILLANCE: [
                OrderAlert,
                MarketAbuseAlert,
                MarketAbuseScenarioTag,
            ],
            ModuleTitle.COMMS_SURVEILLANCE: [CommunicationAlert],
        }
        if module in models:
            return TermFilter(
                name="&model",
                value=[m.__config__.model_name for m in models[module]],
            ).build_q(meta_fields=meta_fields)


class PersonFilter(SearchFeature):
    @dataclass
    class Config(SearchFeatureConfig):
        person: Optional[str] = None
        person_param: str = "person"
        module: str = ModuleTitle.COMMUNICATIONS

    config: Config

    def build_q(self, params=None, *, meta_fields):
        person = getattr(params, self.config.person_param, None) or self.config.person
        if not person:
            return

        if self.config.module in [ModuleTitle.COMMUNICATIONS, ModuleTitle.COMMS_SURVEILLANCE]:
            return Or(
                *(
                    Nested(TermFilter(name=f"hit.{pt}.name", value=person), path=f"hit.{pt}")
                    for pt in PERSON_TYPES
                ),
                Nested(
                    TermFilter(name="hit.participants.value.name", value=person),
                    path="hit.participants",
                ),
                Nested(
                    TermFilter(name="hit.participants.value.counterparty.name", value=person),
                    path="hit.participants",
                ),
            ).build_q(meta_fields=meta_fields)
        else:
            return Or(
                *(TermFilter(name=f"hit.{pt}.name", value=person) for pt in PERSON_TYPES),
                TermFilter(name="hit.participants.value.name", value=person),
                TermFilter(name="hit.participants.value.counterparty.name", value=person),
            ).build_q(meta_fields=meta_fields)


class UnresolvedAlertsAssigneeStatusFilter(SearchFeature):
    @dataclass
    class Config(SearchFeatureConfig):
        unresolved_alerts_assignee_status_param: Optional[List[str]] = (
            "unresolved_alerts_assignee_status"
        )

    config: Config

    def build_q(self, params=None, *, meta_fields):
        unresolved_alerts_assignee_status_param = getattr(
            params, self.config.unresolved_alerts_assignee_status_param, []
        )

        if unresolved_alerts_assignee_status_param is None or set(
            [e.value for e in WorkflowAssigneeFilterOptions]
        ) <= set(unresolved_alerts_assignee_status_param):
            pass
        else:
            unresolved_alerts_assignee_status_param[0]
            filter_options = {
                WorkflowAssigneeFilterOptions.ASSIGNED.value: And(
                    Exists(field="workflow.assigneeId"),
                    Not(TermFilter(name="workflow.assigneeId", value="")),
                    TermFilter(name="workflow.status", value=WorkflowStatus.UNRESOLVED.value),
                ),
                WorkflowAssigneeFilterOptions.UNASSIGNED.value: And(
                    Or(
                        Not(Exists(field="workflow.assigneeId")),
                        TermFilter(name="workflow.assigneeId", value=""),
                    ),
                    TermFilter(name="workflow.status", value=WorkflowStatus.UNRESOLVED.value),
                ),
            }

            return filter_options[unresolved_alerts_assignee_status_param[0]].build_q(
                meta_fields=meta_fields
            )


class AlertsSearchBase(SearchModel):
    class Params(SearchModelParams):
        alert_status: Optional[List[AlertHitStatus]]
        alert_resolution_category: Optional[List[str]] = None
        alert_resolution_sub_category: Optional[List[str]] = None
        asset_class: Optional[List[str]] = None
        asset_class_sub: Optional[List[str]] = None
        counterparties: Optional[List[str]] = None
        unresolved_alerts_assignee_status: Optional[List[str]] = None
        detail_watch_id: Optional[str] = None
        end: Optional[Union[dt.datetime, dt.date]]
        execution_id: Optional[str] = None
        hit_id: Optional[str] = None
        instrument_full_name: Optional[List[str]] = None
        investment_decision_maker: Optional[List[str]] = None
        matched_lexica: Optional[List[str]] = None
        module: Optional[ModuleTitle]
        needs_policy: Optional[bool] = False
        pad: Optional[List[str]] = None
        person: Optional[List[str]]
        start: Optional[Union[dt.datetime, dt.date]]
        search: Optional[str] = None
        traders: Optional[List[str]] = None
        watch_id: Optional[str] = None
        watch_ids: Optional[List[str]] = None
        watch_name: Optional[List[str]]
        workflow_case: Optional[str]
        workflow_status: Optional[str]
        workflow_statuses: Optional[List[str]]
        f: Optional[str]
        not_watch_type: Optional[str] = "ON_DEMAND"
        with_out_participants: Optional[bool] = False
        watch_query_types: Optional[List[WatchQueryType]] = None
        watch_types: Optional[List[WatchType]] = None
        scenario_ids: Optional[List[str]] = None

    params: Params

    @staticmethod
    def get_nested(module: ModuleTitle) -> List[str]:
        if module == ModuleTitle.COMMS_SURVEILLANCE:
            return [
                "hit.participants",
                "hit.analytics.lexica",
            ]
        return []

    @CustomClassProperty
    def features(cls, instance):
        model = get_alert_record_models(module=instance.params.module if instance else None)
        return [
            # ModelFilter has to be at index 0,
            # CommsAlertsSearchBase depends on it.
            ModelFilter(
                model=[
                    CommunicationAlert,
                    OrderAlert,
                    MarketAbuseAlert,
                    MarketAbuseScenarioTag,
                ]
            ),
            AlertsModuleFilter,
            Not(TermFilter(name="detail.watchType", param="not_watch_type")),
            FlangFilter.simple(
                nested_paths=[f"hit.{pt}" for pt in PERSON_TYPES] + ["hit.participants"]
                if instance and instance.params.module == ModuleTitle.COMMS_SURVEILLANCE
                else None,
            ),
            TermFilter(name="detail.watchType", param="watch_types"),
            NotExpired,
            TermFilter(name="watchDetails.queryType", param="watch_query_types"),
            RangeFilter(field="detected"),
            PersonFilter(
                config=PersonFilter.Config(module=instance.params.module if instance else None)
            ),
            TermFilter(name="workflow.status", param="alert_status"),
            TermFilter(name="workflow.resolutionCategory", param="alert_resolution_category"),
            TermFilter(
                name="workflow.resolutionSubCategories",
                param="alert_resolution_sub_category",
            ),
            TermFilter(name="orderDetails.parties.trader.name", param="traders"),
            TermFilter(name="workflow.status", param="workflow_status"),
            TermFilter(name="workflow.status", param="workflow_statuses"),
            TermFilter(name="workflow.caseId", param="workflow_case"),
            TermFilter(name="detail.watchName", param="watch_name"),
            TermFilter(name="detail.watchId", param="detail_watch_id"),
            TermFilter(name="detail.watchId", param="watch_ids"),
            TermFilter(name="matchedLexica", param="matched_lexica"),
            TermFilter(name="hit.&id", param="hit_id"),
            TermFilter(
                name="hit.instrumentDetails.instrument.ext.bestExAssetClassMain",
                param="asset_class",
            ),
            TermFilter(
                name="hit.instrumentDetails.instrument.ext.bestExAssetClassSub",
                param="asset_class_sub",
            ),
            TermFilter(
                name="hit.tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.name",
                param="investment_decision_maker",
            ),
            TermFilter(
                name="hit.instrumentDetails.instrument.instrumentFullName",
                param="instrument_full_name",
            ),
            TermFilter(name="orderDetails.parties.counterparty.name", param="counterparties"),
            TermFilter(name="hit.marDetails.isPersonalAccountDealing", param="pad"),
            TermFilter(name="scenarioId", param="scenario_ids"),
            AlertsWatchIdFilter,
            UnresolvedAlertsAssigneeStatusFilter,
            Or(
                *[
                    QueryString(
                        param="search",
                        fields=[
                            *CSURV_ALERT_TEXT_SEARCH_FIELDS,
                        ],
                    ),
                    *get_free_text_search_filters(
                        {
                            FieldType.NESTED.value: {
                                "hit.analytics.lexica": prefix_hit(
                                    COMMS_TEXT_ANALYTICS_SEARCH_FIELDS
                                ),
                                "hit.participants": prefix_hit(
                                    COMMS_TEXT_PARTICIPANTS_SEARCH_FIELDS
                                ),
                            },
                            FieldType.NON_NESTED.value: prefix_hit(
                                COMMS_TEXT_NON_NESTED_SEARCH_FIELDS
                            ),
                        },
                        qs_param="search",
                    ),
                ]
                if instance and instance.params.module == ModuleTitle.COMMS_SURVEILLANCE
                else [
                    QueryString(
                        param="search",
                        fields=[
                            *TSURV_ALERT_TEXT_SEARCH_FIELDS,
                        ],
                    )
                ],
            ),
        ]

    MAR_VS_OTHER_AGGS = {
        "SCENARIO_COUNT": {"cardinality": {"field": "scenarioId"}},
        "MAR": {
            "filter": {
                "term": {"&model": MarketAbuseScenarioTag.__config__.model_name},
                "aggs": {
                    "STATUS": {
                        "terms": {"field": "workflow.status"},
                        "aggs": {
                            "ASSIGNEE_AGG": {
                                "terms": {"field": "workflow.assigneeId", "exclude": ""}
                            }
                        },
                    },
                },
            }
        },
        "MAR_ALERT": {
            "filter": {
                "term": {"&model": MarketAbuseAlert.__config__.model_name},
                "aggs": {
                    "STATUS": {
                        "terms": {"field": "workflow.status"},
                        "aggs": {
                            "SCENARIO_COUNT": {
                                "cardinality": {"field": "scenarioId"},
                            },
                            "ASSIGNEE_AGG": {
                                "terms": {"field": "workflow.assigneeId", "exclude": ""}
                            },
                        },
                    }
                },
            }
        },
        "OTHER": {
            "filter": {
                "terms": {
                    "&model": [
                        OrderAlert.__config__.model_name,
                        CommunicationAlert.__config__.model_name,
                    ]
                },
                "aggs": {
                    "STATUS": {
                        "terms": {"field": "workflow.status"},
                        "aggs": {
                            "ASSIGNEE_AGG": {
                                "terms": {"field": "workflow.assigneeId", "exclude": ""}
                            }
                        },
                    },
                },
            }
        },
    }

    COMMS_AGG = {
        "COMMS": {
            "filter": {
                "terms": {
                    "&model": [
                        CommunicationAlert.__config__.model_name,
                    ]
                },
                "aggs": {
                    "STATUS": {
                        "terms": {"field": "workflow.status"},
                        "aggs": {"ASSIGNEE_AGG": {"terms": {"field": "workflow.assigneeId"}}},
                    },
                },
            }
        },
    }

    MODELS_AGG = {
        "terms": {
            "script": {
                "inline": (
                    "if (!doc.containsKey('hit.&key') || doc['hit.&key'].size() == 0) { return 'NO KEY'; }"
                    "String key = doc['hit.&key'].value; "
                    "StringTokenizer st = new StringTokenizer(key, ':'); "
                    "if (st.hasMoreTokens()) { return st.nextToken(); } return 'UNEXPECTED';"
                ),
            },
        },
    }

    STATUS_AGGS = {
        "STATUS": {"terms": {"field": "workflow.status", "size": ES_MAX_AGG_SIZE}},
    }


class AlertsAggByLexicaTriggered(AlertsSearchBase):
    class Params(AlertsSearchBase.Params):
        f: str = None
        search: str = None
        languages: Optional[Union[str, List[str]]] = None
        sub_behaviour_id: Optional[List[str]] = None

    params: Params

    features = AlertsSearchBase.features[2:-1] + [
        ModelFilter(model=CommunicationAlert),
        Nested(
            QueryString(default_field="hit.analytics.lexica.term.text", param="search"),
            TermFilter(param="languages", name="hit.analytics.lexica.termLanguage"),
            TermFilter(param="sub_behaviour_id", name="hit.analytics.lexica.subBehaviourId"),
            path="hit.analytics.lexica",
        ),
    ]

    default_sort_order = ["detected:desc"]

    def build_aggs(self) -> Optional[Dict]:
        filters = []
        if self.params.languages:
            filters.append(
                TermFilter(
                    value=self.params.languages,
                    name="hit.analytics.lexica.termLanguage",
                ).to_dict(meta_fields=GammaMetaFields)
            )

        if self.params.search:
            filters.append(
                QueryString(
                    default_field="hit.analytics.lexica.term.text",
                    value=self.params.search,
                ).to_dict(meta_fields=GammaMetaFields)
            )

        filters = filters or [{"match_all": {}}]

        return {
            "MATCHED_LEXICA": {
                "nested": {"path": "hit.analytics.lexica"},
                "aggs": {
                    "FILTERED_LEXICA": {
                        "filter": {"bool": {"must": filters}},
                        "aggs": {
                            "TERM_ID": {
                                "terms": {
                                    "field": "hit.analytics.lexica.termId",
                                    "size": 50,
                                },
                                "aggs": {
                                    "TERM": {
                                        "terms": {"field": "hit.analytics.lexica.term"},
                                        "aggs": {
                                            "TERM_LANGUAGE": {
                                                "terms": {
                                                    "field": "hit.analytics.lexica.termLanguage"
                                                },
                                                "aggs": {
                                                    "REVERSE": {
                                                        "reverse_nested": {},
                                                        "aggs": {
                                                            "RESOLUTION_CATEGORY": {
                                                                "terms": {
                                                                    "field": "workflow.resolutionCategory",
                                                                    "missing": "-",
                                                                }
                                                            }
                                                        },
                                                    }
                                                },
                                            }
                                        },
                                    }
                                },
                            }
                        },
                    }
                },
            }
        }


class CommsAlertsSearchBase(AlertsSearchBase):
    features = [
        ModelFilter(
            model=[
                CommunicationAlert,
            ]
        ),
        *AlertsSearchBase.features[1:],
    ]


class AlertsAggByWatchId(AlertsSearchBase):
    def build_aggs(self) -> Optional[Dict]:
        return {
            "WATCHES": {
                "terms": {"field": "detail.watchId", "size": ES_MAX_AGG_SIZE},
                "aggs": self.STATUS_AGGS,
            }
        }


class CommsAlertsAggByWatchId(CommsAlertsSearchBase):
    def build_aggs(self) -> Optional[Dict]:
        return {
            "WATCHES": {
                "terms": {"field": "detail.watchId", "size": ES_MAX_AGG_SIZE},
                "aggs": self.STATUS_AGGS,
            }
        }


class UnresolvedAlertsSearchWithIds(SearchModel):
    class Params(SearchModelParams):
        alert_ids: Union[str, List[str]]
        workflow_statuses: Optional[List[str]]

    params: Params

    features = [
        ModelFilter(
            model=[
                CommunicationAlert,
                OrderAlert,
                MarketAbuseAlert,
                MarketAbuseScenarioTag,
            ]
        ),
        AlertsModuleFilter,
        Or(
            TermFilter(name="&id", param="alert_ids"),
            TermFilter(name="slug", param="alert_ids"),
        ),
        NotExpired,
        TermFilter(name="workflow.status", value=WorkflowStatus.UNRESOLVED),
    ]


class CaseAlertModelAggs(SearchModel):
    class Params(SearchModelParams):
        alert_status: Optional[List[AlertHitStatus]]
        alert_resolution_category: Optional[List[str]] = None
        alert_resolution_sub_category: Optional[List[str]] = None
        asset_class: Optional[List[str]] = None
        asset_class_sub: Optional[List[str]] = None
        unresolved_alerts_assignee_status: Optional[List[str]] = None
        detail_watch_id: Optional[str] = None
        end: Optional[Union[dt.datetime, dt.date]]
        execution_id: Optional[str] = None
        hit_id: Optional[str] = None
        instrument_full_name: Optional[List[str]] = None
        investment_decision_maker: Optional[List[str]] = None
        matched_lexica: Optional[List[str]] = None
        module: Optional[ModuleTitle]
        needs_policy: Optional[bool] = False
        pad: Optional[List[str]] = None
        person: Optional[List[str]]
        start: Optional[Union[dt.datetime, dt.date]]
        watch_id: Optional[str] = None
        watch_name: Optional[List[str]]
        workflow_case: Optional[str]
        workflow_status: Optional[str]
        workflow_statuses: Optional[List[str]]
        f: Optional[str]

    params: Params

    features = [
        ModelFilter(
            model=[
                CommunicationAlert,
                OrderAlert,
                MarketAbuseAlert,
                MarketAbuseScenarioTag,
            ]
        ),
        AlertsModuleFilter,
        FlangFilter.simple(
            nested_paths=[f"hit.{pt}" for pt in PERSON_TYPES] + ["hit.participants"],
        ),
        NotExpired,
        Or(RangeFilter(field="detected"), RangeFilter(field="detail.createdOn")),
        PersonFilter,
        TermFilter(name="workflow.status", param="alert_status"),
        TermFilter(name="workflow.resolutionCategory", param="alert_resolution_category"),
        TermFilter(
            name="workflow.resolutionSubCategories",
            param="alert_resolution_sub_category",
        ),
        TermFilter(name="workflow.status", param="workflow_status"),
        TermFilter(name="workflow.status", param="workflow_statuses"),
        TermFilter(name="workflow.caseId", param="workflow_case"),
        TermFilter(name="detail.watchName", param="watch_name"),
        TermFilter(name="detail.watchId", param="detail_watch_id"),
        TermFilter(name="matchedLexica", param="matched_lexica"),
        TermFilter(name="hit.&id", param="hit_id"),
        TermFilter(
            name="hit.instrumentDetails.instrument.ext.bestExAssetClassMain",
            param="asset_class",
        ),
        TermFilter(
            name="hit.instrumentDetails.instrument.ext.bestExAssetClassSub",
            param="asset_class_sub",
        ),
        TermFilter(
            name="hit.tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.name",
            param="investment_decision_maker",
        ),
        TermFilter(
            name="hit.instrumentDetails.instrument.instrumentFullName",
            param="instrument_full_name",
        ),
        TermFilter(name="hit.marDetails.isPersonalAccountDealing", param="pad"),
        AlertsWatchIdFilter,
        UnresolvedAlertsAssigneeStatusFilter,
    ]

    def build_aggs(self) -> Optional[Dict]:
        aggs_query = {"ALERTS": {"terms": {"field": "&model"}}}
        return aggs_query


class AlertAggByTradesTrendChart(AlertsSearchBase):
    class Params(AlertsSearchBase.Params):
        trend_chart: TradeSurveillanceTrendChart
        include_custom: bool = False
        search: str = None
        only_scenario_ids: bool = False

    params: Params

    def build_aggs(self) -> Optional[Dict]:
        if self.params.only_scenario_ids:
            return {
                "SCENARIO_IDS": {
                    "terms": {
                        "field": "scenarioId",
                        "size": 10000,
                    }
                }
            }

        field, nested = TRADE_SURVEILLANCE_TREND_CHART_SPEC[self.params.trend_chart]
        if self.params.trend_chart != TradeSurveillanceTrendChart.PEOPLE:
            agg = {
                f"{self.params.trend_chart.value}": {
                    "terms": {
                        "field": field
                        if (
                            self.params.trend_chart
                            in [
                                TradeSurveillanceTrendChart.ASSIGNEE,
                                TradeSurveillanceTrendChart.STATUS,
                                TradeSurveillanceTrendChart.RESOLUTION_CATEGORY,
                                TradeSurveillanceTrendChart.RESOLUTION_SUB_CATEGORY,
                            ]
                        )
                        else f"hit.{field}",
                        "size": ES_MAX_AGG_SIZE,
                        **(
                            {"exclude": "OTHER"}
                            if (
                                self.params.include_custom
                                and self.params.trend_chart
                                == TradeSurveillanceTrendChart.RESOLUTION_CATEGORY
                            )
                            else {}
                        ),  # if Customs categories are populated then OTHERS should be ignored
                    },
                    "aggs": self.MAR_VS_OTHER_AGGS,
                }
            }
            if (
                self.params.include_custom
                and self.params.trend_chart == TradeSurveillanceTrendChart.RESOLUTION_CATEGORY
            ):
                agg["CUSTOM_RESOLUTION_CATEGORY"] = {
                    "terms": {
                        "field": "workflow.resolutionCategoryCustom",
                        "size": ES_MAX_AGG_SIZE,
                    },
                    "aggs": self.MAR_VS_OTHER_AGGS,
                }
            return agg
        else:
            return {
                pt.upper(): {
                    "filter": {
                        "bool": {
                            "should": [
                                {"prefix": {f"hit.{pt}.&key": "MarketPerson"}},
                                {"prefix": {f"hit.{pt}.&key": "AccountPerson"}},
                                Not(Exists(field=f"hit.{pt}.&key")).to_dict(
                                    meta_fields=GammaMetaFields
                                ),
                            ],
                            "minimum_should_match": 1,
                        }
                    },
                    "aggs": {
                        "NAME": {
                            "terms": {
                                "field": f"hit.{pt}.name",
                                "size": ES_MAX_AGG_SIZE,
                            },
                            **(
                                {
                                    "aggs": {
                                        "REVERSE": {
                                            "reverse_nested": {},
                                            "aggs": self.MAR_VS_OTHER_AGGS,
                                        }
                                    }
                                }
                                if self.params.module == ModuleTitle.COMMS_SURVEILLANCE
                                else {}
                            ),
                        }
                    },
                }
                for pt in PERSON_TYPES
            }


class CommunicationAlertSearch(SearchModel):
    class Params(SearchModelParams):
        f: Optional[str]
        unresolved_alerts_assignee_status: Optional[List[str]] = None
        workflow_status: Optional[str]
        workflow_statuses: Optional[List[str]]
        watch_id: Optional[str]
        thread_id: Optional[str]
        matched_lexica: Optional[List[str]]
        person: Optional[List[str]]
        start: Optional[Union[dt.datetime, dt.date]] = None
        end: Optional[Union[dt.datetime, dt.date]] = None

    features = [
        ModelFilter(model=CommunicationAlert),
        NotExpiredFilter,
        FlangFilter.simple(
            nested_paths=[f"hit.{pt}" for pt in PERSON_TYPES],
        ),
        TermFilter(name="workflow.status", param="workflow_status"),
        TermFilter(name="workflow.status", param="workflow_statuses"),
        TermFilter(name="detail.watchId", param="watch_id"),
        TermFilter(name="hit.metadata.threadId", param="thread_id"),
        TermFilter(name="matchedLexica", param="matched_lexica"),
        RangeFilter(field="detected"),
        PersonFilter,
        UnresolvedAlertsAssigneeStatusFilter,
    ]

    default_sort_order = [Sort(field="detected", order=Sort.Order.desc)]

    extras = [
        {
            "_source": {
                "includes": [
                    "&*",
                    "hit.&*",
                    "detected",
                    "hit.subject",
                    "hit.body.text",
                    "hit.participants",
                    "hit.metadata.threadId",
                    "highlights",
                    "hitModel",
                    "matchedLexica",
                    "hit.timestamps",
                    "detail.*",
                    "workflow",
                    "hit.voiceFile.&*",
                    "hit.voiceFile.fileName",
                    "hit.voiceFile.fileType",
                ]
            }
        }
    ]


class TradeSurveillanceAlertSearch(SearchModel):
    class Params(SearchModelParams):
        f: Optional[str]
        alert_status: Optional[List[str]]
        alert_resolution_category: Optional[List[str]] = None
        alert_resolution_sub_category: Optional[List[str]] = None
        unresolved_alerts_assignee_status: Optional[List[str]] = None
        watch_id: Optional[str]
        person: Optional[List[str]]
        start: Optional[Union[dt.datetime, dt.date]] = None
        end: Optional[Union[dt.datetime, dt.date]] = None
        asset_class: Optional[List[str]] = None
        asset_class_sub: Optional[List[str]] = None
        investment_decision_maker: Optional[List[str]] = None
        instrument_full_name: Optional[List[str]] = None
        pad: Optional[List[str]] = None
        search: Optional[str] = None
        not_watch_type: Optional[str] = "ON_DEMAND"
        include_scenario_ids: Optional[List[str]] = None

    _base_features_ = [
        ModelFilter(model=[OrderAlert, MarketAbuseScenarioTag]),
        NotExpired,
        FlangFilter.simple(param="f"),
        TermFilter(name="workflow.resolutionCategory", param="alert_resolution_category"),
        TermFilter(name="workflow.resolutionSubCategories", param="alert_resolution_sub_category"),
        TermFilter(name="workflow.status", param="alert_status"),
        TermFilter(name="detail.watchId", param="watch_id"),
        RangeFilter(field="detected"),
        Not(TermFilter(name="detail.watchType", param="not_watch_type")),
        PersonFilter(config=PersonFilter.Config(module=ModuleTitle.TRADE_SURVEILLANCE)),
        UnresolvedAlertsAssigneeStatusFilter,
        QueryString(param="search", fields=TSURV_ALERT_TEXT_SEARCH_FIELDS),
    ]

    @CustomClassProperty
    def features(cls, instance):
        return [
            Or(
                And(*cls._base_features_),
                TermFilter(name="scenarioId", param="include_scenario_ids"),
            )
        ]

    default_sort_order = [Sort(field="detected", order=Sort.Order.desc)]


class AggScenarioIds(TradeSurveillanceAlertSearch):
    params: TradeSurveillanceAlertSearch.Params

    features = [
        ModelFilter(model=[MarketAbuseAlert])
    ] + TradeSurveillanceAlertSearch._base_features_[1:]

    def build_aggs(self) -> Optional[Dict]:
        return {"SCENARIO_IDS": {"terms": {"field": "scenarioId", "size": ES_MAX_AGG_SIZE}}}


class OrderAlertSearch(SearchModel):
    class Params(SearchModelParams):
        f: Optional[str]
        alert_status: Optional[List[str]]
        alert_resolution_category: Optional[List[str]] = None
        alert_resolution_sub_category: Optional[List[str]] = None
        unresolved_alerts_assignee_status: Optional[List[str]] = None
        watch_id: Optional[str]
        person: Optional[List[str]]
        start: Optional[Union[dt.datetime, dt.date]] = None
        end: Optional[Union[dt.datetime, dt.date]] = None
        asset_class: Optional[List[str]] = None
        asset_class_sub: Optional[List[str]] = None
        investment_decision_maker: Optional[List[str]] = None
        instrument_full_name: Optional[List[str]] = None
        pad: Optional[List[str]] = None
        search: Optional[str] = None

    features = [
        ModelFilter(model=OrderAlert),
        NotExpiredFilter,
        FlangFilter.simple(),
        TermFilter(name="workflow.resolutionCategory", param="alert_resolution_category"),
        TermFilter(
            name="workflow.resolutionSubCategories",
            param="alert_resolution_sub_category",
        ),
        TermFilter(name="workflow.status", param="alert_status"),
        TermFilter(name="detail.watchId", param="watch_id"),
        TermFilter(
            name="hit.instrumentDetails.instrument.ext.bestExAssetClassMain",
            param="asset_class",
        ),
        TermFilter(
            name="hit.instrumentDetails.instrument.ext.bestExAssetClassSub",
            param="asset_class_sub",
        ),
        TermFilter(
            name="hit.tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.name",
            param="investment_decision_maker",
        ),
        TermFilter(
            name="hit.instrumentDetails.instrument.instrumentFullName",
            param="instrument_full_name",
        ),
        TermFilter(name="hit.marDetails.isPersonalAccountDealing", param="pad"),
        Or(RangeFilter(field="detected"), RangeFilter(field="detail.createdOn")),
        PersonFilter(config=PersonFilter.Config(module=ModuleTitle.TRADE_SURVEILLANCE)),
        UnresolvedAlertsAssigneeStatusFilter,
    ]

    default_sort_order = [Sort(field="detected", order=Sort.Order.desc)]


class UnresolvedAlertsAssigneeStatusFilter(SearchFeature):
    @dataclass
    class Config(SearchFeatureConfig):
        unresolved_alerts_assignee_status_param: Optional[List[str]] = (
            "unresolved_alerts_assignee_status"
        )

    config: Config

    def build_q(self, params=None, *, meta_fields):
        unresolved_alerts_assignee_status_param = getattr(
            params, self.config.unresolved_alerts_assignee_status_param, []
        )

        if unresolved_alerts_assignee_status_param is None or set(
            [e.value for e in WorkflowAssigneeFilterOptions]
        ) <= set(unresolved_alerts_assignee_status_param):
            pass
        else:
            unresolved_alerts_assignee_status_param[0]
            filter_options = {
                WorkflowAssigneeFilterOptions.ASSIGNED.value: And(
                    Exists(field="workflow.assigneeId"),
                    Not(TermFilter(name="workflow.assigneeId", value="")),
                    TermFilter(name="workflow.status", value=WorkflowStatus.UNRESOLVED.value),
                ),
                WorkflowAssigneeFilterOptions.UNASSIGNED.value: And(
                    Or(
                        Not(Exists(field="workflow.assigneeId")),
                        TermFilter(name="workflow.assigneeId", value=""),
                    ),
                    TermFilter(name="workflow.status", value=WorkflowStatus.UNRESOLVED.value),
                ),
            }

            return filter_options[unresolved_alerts_assignee_status_param[0]].build_q(
                meta_fields=meta_fields
            )


class AlertsSearchBaseWithCommentSearch(SearchModel):
    class Params(SearchModelParams):
        alert_status: Optional[List[AlertHitStatus]]
        alert_resolution_category: Optional[List[str]] = None
        alert_resolution_sub_category: Optional[List[str]] = None
        asset_class: Optional[List[str]] = None
        asset_class_sub: Optional[List[str]] = None
        unresolved_alerts_assignee_status: Optional[List[str]] = None
        detail_watch_id: Optional[str] = None
        end: Optional[Union[dt.datetime, dt.date]]
        execution_id: Optional[str] = None
        hit_id: Optional[str] = None
        instrument_full_name: Optional[List[str]] = None
        investment_decision_maker: Optional[List[str]] = None
        matched_lexica: Optional[List[str]] = None
        module: Optional[ModuleTitle]
        needs_policy: Optional[bool] = False
        pad: Optional[List[str]] = None
        person: Optional[List[str]]
        start: Optional[Union[dt.datetime, dt.date]]
        watch_id: Optional[str] = None
        watch_name: Optional[List[str]]
        workflow_case: Optional[str]
        workflow_status: Optional[str]
        workflow_statuses: Optional[List[str]]
        f: Optional[str]
        not_watch_type: Optional[str] = "ON_DEMAND"
        with_out_participants: Optional[bool] = False
        comment_link_ids: Optional[List[str]] = None
        languages: Optional[List[str]] = None
        sub_behaviour_id: Optional[List[str]] = None
        search: Optional[str] = None
        scenario_ids: Optional[List[str]] = None
        resolved_category_only: bool = False

    params: Params

    @staticmethod
    def get_nested(module: ModuleTitle) -> List[str]:
        if module == ModuleTitle.COMMS_SURVEILLANCE:
            return [
                "hit.participants",
                "hit.analytics.lexica",
            ]
        return []

    @CustomClassProperty
    def features(cls, instance):
        return [
            NotExpired,
            Or(
                TermFilter(name="&id", param="comment_link_ids"),
                TermFilter(name="slug", param="comment_link_ids"),
                And(
                    ModelFilter(
                        model=[
                            CommunicationAlert,
                            OrderAlert,
                            MarketAbuseAlert,
                            MarketAbuseScenarioTag,
                        ]
                    ),
                    AlertsModuleFilter,
                    Not(TermFilter(name="detail.watchType", param="not_watch_type")),
                    FlangFilter.simple(
                        nested_paths=instance.get_nested(instance.params.module) if instance else []
                    ),
                    RangeFilter(field="detected"),
                    PersonFilter(
                        config=PersonFilter.Config(
                            module=instance.params.module if instance else None
                        )
                    ),
                    TermFilter(name="&parent", param="execution_id"),
                    TermFilter(name="workflow.status", param="alert_status"),
                    TermFilter(
                        name="workflow.resolutionCategory",
                        param="alert_resolution_category",
                    ),
                    TermFilter(
                        name="workflow.resolutionSubCategories",
                        param="alert_resolution_sub_category",
                    ),
                    TermFilter(name="workflow.status", param="workflow_status"),
                    TermFilter(name="workflow.status", param="workflow_statuses"),
                    TermFilter(name="workflow.caseId", param="workflow_case"),
                    TermFilter(name="detail.watchName", param="watch_name"),
                    TermFilter(name="detail.watchId", param="detail_watch_id"),
                    TermFilter(name="matchedLexica", param="matched_lexica"),
                    TermFilter(name="hit.&id", param="hit_id"),
                    TermFilter(
                        name="hit.instrumentDetails.instrument.ext.bestExAssetClassMain",
                        param="asset_class",
                    ),
                    TermFilter(
                        name="hit.instrumentDetails.instrument.ext.bestExAssetClassSub",
                        param="asset_class_sub",
                    ),
                    TermFilter(
                        name="hit.tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.name",
                        param="investment_decision_maker",
                    ),
                    Nested(
                        TermFilter(
                            param="sub_behaviour_id",
                            name="hit.analytics.lexica.subBehaviourId",
                        ),
                        TermFilter(param="languages", name="hit.analytics.lexica.termLanguage"),
                        path="hit.analytics.lexica",
                    ),
                    TermFilter(
                        name="hit.instrumentDetails.instrument.instrumentFullName",
                        param="instrument_full_name",
                    ),
                    TermFilter(name="hit.marDetails.isPersonalAccountDealing", param="pad"),
                    TermFilter(name="scenarioId", param="scenario_ids"),
                    AlertsWatchIdFilter,
                    UnresolvedAlertsAssigneeStatusFilter,
                    Or(
                        *[
                            QueryString(
                                param="search",
                                fields=[
                                    *CSURV_ALERT_TEXT_SEARCH_FIELDS,
                                ],
                            ),
                            *get_free_text_search_filters(
                                {
                                    FieldType.NESTED.value: {
                                        "hit.analytics.lexica": prefix_hit(
                                            COMMS_TEXT_ANALYTICS_SEARCH_FIELDS
                                        ),
                                        "hit.participants": prefix_hit(
                                            COMMS_TEXT_PARTICIPANTS_SEARCH_FIELDS
                                        ),
                                    },
                                    FieldType.NON_NESTED.value: prefix_hit(
                                        COMMS_TEXT_NON_NESTED_SEARCH_FIELDS
                                    ),
                                },
                                qs_param="search",
                            ),
                        ]
                        if instance and instance.params.module == ModuleTitle.COMMS_SURVEILLANCE
                        else [
                            QueryString(
                                param="search",
                                fields=[
                                    *TSURV_ALERT_TEXT_SEARCH_FIELDS,
                                ],
                            )
                        ],
                    ),
                ),
            ),
        ]

    MAR_VS_OTHER_AGGS = {
        "SCENARIO_COUNT": {"cardinality": {"field": "scenarioId"}},
        "MAR": {
            "filter": {
                "term": {"&model": MarketAbuseScenarioTag.__config__.model_name},
                "aggs": {
                    "STATUS": {
                        "terms": {"field": "workflow.status"},
                        "aggs": {
                            "ASSIGNEE_AGG": {
                                "terms": {"field": "workflow.assigneeId", "exclude": ""}
                            }
                        },
                    },
                },
            }
        },
        "MAR_ALERT": {
            "filter": {
                "term": {"&model": MarketAbuseAlert.__config__.model_name},
                "aggs": {
                    "STATUS": {
                        "terms": {"field": "workflow.status"},
                        "aggs": {
                            "SCENARIO_COUNT": {
                                "cardinality": {"field": "scenarioId"},
                            },
                            "ASSIGNEE_AGG": {
                                "terms": {"field": "workflow.assigneeId", "exclude": ""}
                            },
                        },
                    }
                },
            }
        },
        "OTHER": {
            "filter": {
                "terms": {
                    "&model": [
                        OrderAlert.__config__.model_name,
                        CommunicationAlert.__config__.model_name,
                    ]
                },
                "aggs": {
                    "STATUS": {
                        "terms": {"field": "workflow.status"},
                        "aggs": {
                            "ASSIGNEE_AGG": {
                                "terms": {"field": "workflow.assigneeId", "exclude": ""}
                            }
                        },
                    },
                },
            }
        },
    }

    COMMS_AGG = {
        "COMMS": {
            "filter": {
                "terms": {
                    "&model": [
                        CommunicationAlert.__config__.model_name,
                    ]
                },
                "aggs": {
                    "STATUS": {
                        "terms": {"field": "workflow.status"},
                        "aggs": {"ASSIGNEE_AGG": {"terms": {"field": "workflow.assigneeId"}}},
                    },
                },
            }
        },
    }

    MODELS_AGG = {
        "terms": {
            "script": {
                "source": (
                    "if (!doc.containsKey('hit.&key') || doc['hit.&key'].size() == 0) {     return 'NO KEY'; } else {     String key = doc['hit.&key'].value;     if (key == null || key.isEmpty()) {         return 'NO KEY';     }     StringTokenizer st = new StringTokenizer(key, ':');     if (st.hasMoreTokens()) {         return st.nextToken();     }     return 'UNEXPECTED'; }"
                ),
            },
        },
    }


class AlertsAggByWatch(AlertsSearchBaseWithCommentSearch):
    def build_aggs(self) -> Optional[Dict]:
        return {
            "WATCH": {
                "terms": {
                    "field": "detail.watchName",
                    "size": ES_MAX_AGG_SIZE,
                },
                "aggs": self.MAR_VS_OTHER_AGGS,
            }
        }


class AlertsAggByTime(AlertsSearchBaseWithCommentSearch):
    class Params(AlertsSearchBaseWithCommentSearch.Params):
        interval: Optional[str]
        buckets: Optional[int]

    params: Params

    @cached_property
    def interval_in_millis(self) -> int:
        return get_interval_in_millis_from_params(self.params, min_interval=5 * 60_000)

    def build_aggs(self) -> Optional[Dict]:
        return {
            "DATES": {
                "date_histogram": {
                    "field": "detected",
                    "fixed_interval": f"{self.interval_in_millis}ms",
                    "format": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
                    "min_doc_count": 1,
                },
                "aggs": self.MAR_VS_OTHER_AGGS,
            }
        }


class AlertAggByCommsTrendChart(AlertsSearchBaseWithCommentSearch):
    class Params(AlertsSearchBaseWithCommentSearch.Params):
        trend_chart: CommsSurveillanceTrendChart

    params: Params

    def build_nested_aggs(self, field, nested_path, filter) -> Optional[Dict]:
        agg = {
            "NAME": {
                "terms": {"field": field, "size": ES_MAX_AGG_SIZE},
                "aggs": {
                    "REVERSE": {
                        "reverse_nested": {},
                        "aggs": {"STATUS": {"terms": {"field": "workflow.status"}}},
                    }
                },
            }
        }

        for path in reversed(nested_path[:-1]):
            agg = {path.split(".")[-1].upper(): {"nested": {"path": path}, "aggs": agg}}
        return {
            "PARTICIPANTS": {
                "nested": {"path": nested_path[-1]},
                "aggs": {"PEOPLE": {"filter": filter, "aggs": agg}},
            }
        }

    def build_aggs(self) -> Optional[Dict]:
        if self.params.trend_chart in COMMS_SURVEILLANCE_TREND_CHART_PARTICIPANTS_FIELDS_SPEC:
            field, nested_path, filter = COMMS_SURVEILLANCE_TREND_CHART_PARTICIPANTS_FIELDS_SPEC[
                self.params.trend_chart
            ]
            if isinstance(nested_path, list):
                return self.build_nested_aggs(field, nested_path, filter)
            return {
                "PARTICIPANTS": {
                    "nested": {
                        "path": nested_path,
                    },
                    "aggs": {
                        "PEOPLE": {
                            "filter": filter,
                            "aggs": {
                                "NAME": {
                                    "terms": {
                                        "field": field,
                                        "size": ES_MAX_AGG_SIZE,
                                    },
                                    "aggs": {
                                        "REVERSE": {
                                            "reverse_nested": {},
                                            "aggs": {
                                                "STATUS": {"terms": {"field": "workflow.status"}}
                                            },
                                        }
                                    },
                                }
                            },
                        },
                    },
                }
            }
        else:
            field, nested = COMMS_SURVEILLANCE_TREND_CHART_SPEC[self.params.trend_chart]
            return {
                self.params.trend_chart.value: {
                    "terms": {
                        "field": field,
                        "size": ES_MAX_AGG_SIZE,
                    },
                    "aggs": self.COMMS_AGG,
                }
            }


class AlertsAggByStatus(AlertsSearchBaseWithCommentSearch):
    class Params(AlertsSearchBaseWithCommentSearch.Params):
        resolved_category_only: bool = False

    # to fetch the alerts corresponding only to the given execution_id:
    @CustomClassProperty
    def features(cls, instance):
        params = instance.params.dict() if instance is not None else {}
        return AlertsSearchBaseWithCommentSearch(**params).features + [
            TermFilter(name="&parent", param="execution_id"),
        ]

    def build_aggs(self) -> Optional[Dict]:
        SUB_AGGS = {
            "ASSIGNEE_AGG": {
                "filter": {
                    "bool": {
                        "must": {"exists": {"field": "workflow.assigneeId"}},
                        "must_not": {"term": {"workflow.assigneeId": ""}},
                    }
                },
                "aggs": {"ASSIGNEE_AGG_SCENARIO_COUNT": {"cardinality": {"field": "scenarioId"}}},
            }
        }
        if self.params.resolved_category_only:
            SUB_AGGS.update(
                {
                    "RESOLUTION_CATEGORY_AGG": {
                        "terms": {
                            "field": "workflow.resolutionCategory",
                            "exclude": "OTHER",
                        }
                    },
                    "RESOLUTION_CATEGORY_CUSTOM_AGG": {
                        "terms": {"field": "workflow.resolutionCategoryCustom"}
                    },
                }
            )
        return {
            "SCENARIO_COUNT": {"cardinality": {"field": "scenarioId"}},
            "MAR": {
                "filter": {
                    "term": {"&model": MarketAbuseScenarioTag.__config__.model_name},
                    "aggs": {
                        "STATUS": {
                            "terms": {"field": "workflow.status"},
                            "aggs": SUB_AGGS,
                        },
                    },
                }
            },
            "MAR_ALERT": {
                "filter": {
                    "term": {"&model": MarketAbuseAlert.__config__.model_name},
                    "aggs": {
                        "STATUS": {
                            "terms": {"field": "workflow.status"},
                            "aggs": {
                                "SCENARIO_COUNT": {
                                    "cardinality": {"field": "scenarioId"},
                                },
                                **SUB_AGGS,
                            },
                        }
                    },
                }
            },
            "OTHER": {
                "filter": {
                    "terms": {
                        "&model": [
                            OrderAlert.__config__.model_name,
                            CommunicationAlert.__config__.model_name,
                        ]
                    },
                    "aggs": {
                        "STATUS": {
                            "terms": {"field": "workflow.status"},
                            "aggs": SUB_AGGS,
                        },
                    },
                }
            },
        }


class AlertsAggByTradesPeople(AlertsSearchBase):
    def build_aggs(self) -> Optional[Dict]:
        return self._aggs_cached

    @cached_property
    def _aggs_cached(self):
        # Because there's nothing dynamic here we can cache the result.
        return {
            pt.upper(): {
                "filter": {
                    "bool": {
                        "should": [
                            {"prefix": {f"hit.{pt}.&key": "MarketPerson"}},
                            {"prefix": {f"hit.{pt}.&key": "AccountPerson"}},
                            Not(Exists(field=f"hit.{pt}.&key")).to_dict(
                                meta_fields=GammaMetaFields
                            ),
                        ],
                        "minimum_should_match": 1,
                    }
                },
                "aggs": {
                    "NAME": {
                        "terms": {
                            "field": f"hit.{pt}.name",
                            "size": ES_MAX_AGG_SIZE,
                        },
                        "aggs": self.MAR_VS_OTHER_AGGS,
                    }
                },
            }
            for pt in PERSON_TYPES
        }


class AlertsAggByCommsPeople(AlertsSearchBaseWithCommentSearch):
    def build_aggs(self) -> Optional[Dict]:
        return {
            "PARTICIPANTS": {
                "nested": {
                    "path": "hit.participants",
                },
                "aggs": {
                    "PEOPLE": {
                        "filter": {
                            "bool": {
                                "should": [
                                    {"prefix": {"hit.participants.value.&key": "MarketPerson"}},
                                    {"prefix": {"hit.participants.value.&key": "AccountPerson"}},
                                    Not(Exists(field="hit.participants.value.&key")).to_dict(
                                        meta_fields=GammaMetaFields
                                    ),
                                ],
                                "minimum_should_match": 1,
                            }
                        },
                        "aggs": {
                            "NAME": {
                                "terms": {
                                    "field": "hit.participants.value.name",
                                    "size": ES_MAX_AGG_SIZE,
                                },
                                "aggs": {
                                    "REVERSE": {
                                        "reverse_nested": {},
                                        "aggs": {"STATUS": {"terms": {"field": "workflow.status"}}},
                                    }
                                },
                            }
                        },
                    },
                },
            }
        }


class AlertsAggByWorkStatus(AlertsSearchBase):
    """Aggregation class to fetch the counts for:

    MY_WORK: Alerts that are either assigned to or resolved by the specific user
    OTHER_PEOPLE_WORK: Alerts that are neither assigned to nor resolved by the specific user
    UNASSIGNED: Alerts which are not assigned to anyone
    """

    class Params(AlertsSearchBase.Params):
        user_name: Optional[str]
        user_id: Optional[str]

    params: Params

    def build_aggs(self) -> Optional[Dict]:
        return {
            "MY_WORK": {
                "filter": {
                    "bool": {
                        "should": [
                            {"term": {"workflow.assigneeId": self.params.user_id}},
                            {"term": {"workflow.resolvedByName": self.params.user_name}},
                        ]
                    }
                }
            },
            "OTHER_PEOPLE_WORK": {
                "filter": {
                    "bool": {
                        "must_not": [
                            {"terms": {"workflow.assigneeId": [self.params.user_id, ""]}},
                            {"term": {"workflow.resolvedByName": self.params.user_name}},
                        ],
                        "must": [{"exists": {"field": "workflow.assigneeId"}}],
                    }
                }
            },
            "UNASSIGNED": {
                "filter": {"bool": {"must_not": {"exists": {"field": "workflow.assigneeName"}}}}
            },
        }


class AlertsAggByThread(AlertsSearchBase):
    def build_aggs(self) -> Optional[Dict]:
        return {
            "AGG_RESULTS_BY_THREAD_ID": {
                "terms": {"field": "hit.metadata.threadId", "size": ES_MAX_AGG_SIZE},
                "aggs": {
                    "thread_bucket_filter": {
                        "bucket_selector": {
                            "buckets_path": {"threadCount": "_count"},
                            "script": "params.threadCount >= 1",
                        }
                    },
                    "AGGS_BY_STATUS": {
                        "terms": {"field": "workflow.status", "size": ES_MAX_AGG_SIZE}
                    },
                    **(
                        {
                            "TOP_HITS_BY_THREAD": {
                                "top_hits": {
                                    "_source": {
                                        "includes": [
                                            "hit.participants.value.name",
                                            "hit.participants.value.counterparty.name",
                                            "hit.identifiers*",
                                        ]
                                    },
                                    "size": 10,
                                }
                            }
                        }
                        if not self.params.with_out_participants
                        else {}
                    ),
                    "TOTAL_WATCHES_DETECTED": {"cardinality": {"field": "detail.watchId"}},
                    "RECENT_DATE": {"max": {"field": "detected"}},
                },
            }
        }


class AlertsWithoutThread(AlertsSearchBase):
    features = AlertsSearchBase.features + [
        ModelFilter(model=[CommunicationAlert]),
        Not(Exists(field="hit.metadata.threadId")),
    ]


class AlertsAggByMatchedLexica(AlertsSearchBase):
    def build_aggs(self) -> Optional[Dict]:
        return {
            "MATCHED_LEXICA": {
                "terms": {
                    "field": "matchedLexica",
                }
            }
        }


class AlertsRepository(RepoHelpersMixin):
    _NON_CATEGORY_VALUES_ = AlertHitStatus.list() + WorkflowAssigneeFilterOptions.as_list()

    async def get_alerts_summary_by_matched_lexica(self, **params):
        results = await self.get_aggs(
            search_model_cls=AlertsAggByMatchedLexica,
            **params,
        )
        return results.iter_raw_bucket_agg("MATCHED_LEXICA")

    async def get_alerts_summary_by_lexica_triggered(self, **params):
        pagination = params.pop("pagination")
        result = await self.get_aggs(
            search_model_cls=AlertsAggByLexicaTriggered,
            module=ModuleTitle.COMMS_SURVEILLANCE,
            **params,
        )
        filtered_data = []

        total = result.aggregations["MATCHED_LEXICA"]["FILTERED_LEXICA"]["doc_count"]
        if not total:
            return []
        for term_id_bucket in result.iter_raw_bucket_agg("MATCHED_LEXICA.FILTERED_LEXICA.TERM_ID"):
            term_id = term_id_bucket["key"]

            term_bucket = (
                term_id_bucket["TERM"]["buckets"][0] if term_id_bucket["TERM"]["buckets"] else {}
            )
            term_key = term_bucket.get("key")
            term_count = term_bucket.get("doc_count", 0)

            term_lang_bucket = term_bucket.get("TERM_LANGUAGE", {}).get("buckets", [{}])[0]
            term_lang = term_lang_bucket.get("key")

            # Extract the resolution categories
            resolution_data = (
                {
                    rc["key"]: rc["doc_count"]
                    for rc in term_lang_bucket["REVERSE"]["RESOLUTION_CATEGORY"]["buckets"]
                }
                if term_lang_bucket
                else {}
            )

            filtered_data.append(
                {
                    "term": term_key,
                    "termId": term_id,
                    "termLanguage": term_lang,
                    "alertCount": term_count,
                    "PercentageOfAlerts": (term_count / total) * 100,
                    "falsePositives": resolution_data.get("FALSE_POSITIVE", 0),
                    "percentageOfFalsePositives": (
                        resolution_data.get("FALSE_POSITIVE", 0) / term_count
                    )
                    * 100,
                    "nearMisses": resolution_data.get("NEAR_MISS", 0),
                    "PercentageOfNearMisses": (resolution_data.get("NEAR_MISS", 0) / term_count)
                    * 100,
                    "breaches": resolution_data.get("POLICY_BREACH", 0),
                    "PercentageOfBreaches": (resolution_data.get("POLICY_BREACH", 0) / term_count)
                    * 100,
                }
            )

        return filtered_data[pagination.skip : pagination.take]

    def _needs_policy(self) -> bool:
        for policy in NEEDS_POLICY:
            if (
                self.tenancy.realm.startswith(policy["tenant"])
                and self.tenancy.principal != policy["email"]
            ):
                return True

        return False

    def bulk_attach_case(self, alert_ids: List[str], case_id, case_slug):
        """Attach a case to an alert.

        Will update all Communications or Order alerts linked to the
        ``alert_ids`` with the ``case_id``.
        """
        query = {
            "bool": {
                "must": [
                    {
                        "terms": {
                            "&model": [
                                CommunicationAlert.__config__.model_name,
                                OrderAlert.__config__.model_name,
                            ]
                        }
                    },
                    {"terms": {"&id": alert_ids}},
                ]
            }
        }

        script = {
            "lang": "painless",
            "source": f"""
            ctx._source.workflow.status = "{AlertHitStatus.UNDER_INVESTIGATION.value}";
            ctx._source.workflow.caseSlug = "{case_slug}";
            ctx._source.workflow.caseId = "{case_id}";
            ctx._source.workflow.updatedBy = "{self.repo.tenancy.session.principal}";
            """,
        }

        return self.es_client.update_by_query(
            body={
                "query": query,
                "script": script,
            },
            index=self.index_for_record_model(CommunicationAlert),
            refresh=True,
            conflicts="proceed",
        )

    async def attach_case(self, alert_id, case_id, case_slug):
        alert = await self.get_alert(alert_id)
        # add workflow record with case ID to alert
        alert.workflow.status = WorkflowStatus.UNDER_INVESTIGATION
        alert.workflow.caseSlug = case_slug
        alert.workflow.caseId = case_id
        alert.workflow.updatedBy = self.repo.tenancy.session.principal
        return await self.save_existing(alert)

    async def remove_case(self, alert: SurveillanceAlert):
        alert.workflow.status = WorkflowStatus.UNRESOLVED
        alert.workflow.updatedBy = self.repo.tenancy.session.principal
        alert.workflow.caseId = None
        alert.workflow.caseSlug = None
        return await self.save_existing(alert)

    async def get_alerts_by_case_and_hit(self, case_id: str, hit_id: str):
        return await self.get_many(
            search_model=AlertsSearchBase(workflow_case=case_id, hit_id=hit_id)
        )

    def update_alerts_workflow(
        self,
        alert_ids: List[str],
        update: AlertWorkflowBase,
        config: ApiServiceConfig,
    ):
        timestamp = dt.datetime.utcnow().replace(tzinfo=dt.timezone.utc)
        timestamp_ms = int(timestamp.timestamp() * 1000)
        script = f"""
            if (ctx._source.workflowDetails != null) {{
                HashMap w = ctx._source.workflowDetails;
                w.putAll(params.update);
            }}
            else {{
                ctx._source.workflowDetails = params.update
            }}
            ctx._source['_meta']['timestamp'] = {timestamp_ms}L;
            if (params.update['status'].indexOf('{AlertHitStatus.RESOLVED.value}') > -1) {{
                ctx._source['timestamps']['resolved'] = "{timestamp.strftime(DATE_TIME_ISO_FORMAT)}";
            }}
        """

        # remove None fields from the update dictionary
        update_body = {k: v for k, v in update.dict(by_alias=True).items() if v is not None}

        body = {
            "script": {
                "source": script,
                "params": {"update": update_body},
            },
            "query": {
                "bool": {
                    "must_not": [{"exists": {"field": "_meta.expiry"}}],
                    "filter": [{"terms": {"&id": alert_ids}}],
                },
            },
        }

        index = self._repo.alias_for(record_model=SurveillanceAlert)

        if config.DEBUG:
            log.debug(f"Against {index}, executing query: {json_dumps(body)}")

        return self._repo.es_client.update_by_query(index=index, body=body, refresh=True)

    async def get_alerts_by_case(self, case_id: str):
        return await self.get_aggs(search_model=CaseAlertModelAggs(workflow_case=case_id))

    async def get_alerts_records_by_case(self, case_id: str):
        return await self.get_many(search_model=CaseAlertModelAggs(workflow_case=case_id))

    async def get_unresolved_alerts_records_ids(self, alert_ids: Union[str, List[str]]):
        return await self.get_many(
            search_model=UnresolvedAlertsSearchWithIds(
                alert_ids=alert_ids,
            )
        )

    async def get_alert(self, alert_id, search_mar_and_non_mar: bool = False) -> SurveillanceAlert:
        if search_mar_and_non_mar:
            return await self.get_one([MarketAbuseScenarioTag, OrderAlert], id=alert_id)

        alert = await self.get_one(
            SurveillanceAlert, search_model=AlertByIdOrSlugSearch(alert_id=alert_id)
        )
        return alert.into()

    async def get_alerts(self, search_mar_and_non_mar: bool = False, **params) -> RawResult:
        module = params.pop("module", ModuleTitle.COMMS_SURVEILLANCE)

        if search_mar_and_non_mar:
            if params.get("search") or params.get("f"):
                pagination = params.pop("pagination", None)

                result = await self.get_aggs(
                    search_model_cls=AggScenarioIds, record_model=MarketAbuseAlert, **params
                )

                scenario_ids = [item["key"] for item in result.iter_raw_bucket_agg("SCENARIO_IDS")]

                params.update({"pagination": pagination, "include_scenario_ids": scenario_ids})
            return await self.get_many(
                search_model_cls=TradeSurveillanceAlertSearch,
                record_model=[MarketAbuseScenarioTag, OrderAlert],
                **params,
            )

        return await self.get_many(
            search_model_cls=(
                CommunicationAlertSearch
                if module == ModuleTitle.COMMS_SURVEILLANCE
                else OrderAlertSearch
            ),
            **params,
        )

    async def get_alerts_summary_by_watch(self, **params):
        result = await self.get_aggs(
            search_model_cls=AlertsAggByWatch,
            record_model=[
                CommunicationAlert,
                OrderAlert,
                MarketAbuseAlert,
                MarketAbuseScenarioTag,
            ],
            **params,
        )

        counters = collections.defaultdict(lambda: collections.defaultdict(int))
        for watch_item in result.iter_raw_bucket_agg("WATCH"):
            for k in ("MAR", "OTHER"):
                for status_item in watch_item[k]["STATUS"]["buckets"]:
                    counters[watch_item["key"]][status_item["key"]] += status_item["doc_count"]
                # If some filter (related to order) is applied, then the count will be 0.
                # Then we need to check for MAR_ALERT.
                if len(collections.Counter(list(counters[watch_item["key"]]))) == 0:
                    for status_item in watch_item["MAR_ALERT"]["STATUS"]["buckets"]:
                        counters[watch_item["key"]][status_item["key"]] += status_item[
                            "SCENARIO_COUNT"
                        ]["value"]

        return [
            {
                "name": watch_name,
                **self.aggregate_status_counts(counts),
                "total": sum(counts.values()),
            }
            for watch_name, counts in counters.items()
        ]

    async def get_alerts_summary_by_trend(
        self, trend_chart, watch_repo=None, include_custom=False, **params
    ):
        counters = collections.defaultdict(lambda: collections.defaultdict(int))
        module = params.get("module", None)

        if module == ModuleTitle.TRADE_SURVEILLANCE:
            is_on_demand = (
                await watch_repo.get_watch_execution_type(params.get("watch_id"), True)
                if watch_repo
                else False
            )
            not_watch_type = None if is_on_demand else "ON_DEMAND"

            search = params.pop("search", None)
            scenario_ids = (
                [
                    x.get("key")
                    for x in (
                        await self.get_aggs(
                            search_model_cls=AlertAggByTradesTrendChart,
                            trend_chart=trend_chart,
                            not_watch_type=not_watch_type,
                            include_custom=include_custom,
                            only_scenario_ids=True,
                            **params,
                            search=search,
                        )
                    ).iter_raw_bucket_agg("SCENARIO_IDS")
                ]
                if search
                else None
            )

            result = await self.get_aggs(
                search_model_cls=AlertAggByTradesTrendChart,
                trend_chart=trend_chart,
                not_watch_type=not_watch_type,
                include_custom=include_custom,
                scenario_ids=scenario_ids,
                **params,
            )
            if trend_chart != TradeSurveillanceTrendChart.PEOPLE:
                for chart in (
                    [trend_chart, "CUSTOM_RESOLUTION_CATEGORY"] if include_custom else [trend_chart]
                ):
                    for item in result.iter_raw_bucket_agg(chart):
                        types = ["MAR", "OTHER"]
                        for k in types:
                            for status_item in item[k]["STATUS"]["buckets"]:
                                if k == "MAR_ALERT":
                                    counters[item["key"]][status_item["key"]] += status_item[
                                        "SCENARIO_COUNT"
                                    ]["value"]
                                else:
                                    counters[item["key"]][status_item["key"]] += status_item[
                                        "doc_count"
                                    ]

                            types.insert(types.index(k) + 1, "MAR_ALERT") if len(
                                collections.Counter(list(counters[item["key"]]))
                            ) == 0 and k != "MAR_ALERT" else None
            else:
                people_names = collections.defaultdict(int)
                fstring = ""
                for pt in PERSON_TYPES:
                    for person_item in result.iter_raw_bucket_agg(f"{pt.upper()}.NAME"):
                        people_names[person_item["key"]] += person_item["doc_count"]

                    fstring += f"hit.{pt}.name in ['{{name}}']" + (" or " if pt != "trader" else "")

                people_names = sorted(people_names.items(), key=lambda x: x[1], reverse=True)[:10]

                for person_name, _ in people_names:
                    params_copy = params.copy()
                    params_copy["f"] = f"({fstring.format(name=person_name)}) and " + (
                        params_copy.get("f") or ""
                    )

                    result = await self.get_alerts_summary_by_trend(
                        trend_chart=TradeSurveillanceTrendChart.STATUS,
                        **params_copy,
                    )
                    for status_item in result:
                        counters[person_name][status_item["name"]] += status_item["total"]

        if module == ModuleTitle.COMMS_SURVEILLANCE:
            result = await self.get_aggs(
                search_model_cls=AlertAggByCommsTrendChart,
                trend_chart=trend_chart,
                record_model=[CommunicationAlert],
                **params,
            )
            if trend_chart not in COMMS_SURVEILLANCE_TREND_CHART_PARTICIPANTS_FIELDS_SPEC:
                for item in result.iter_raw_bucket_agg(trend_chart):
                    for status_item in item["COMMS"]["STATUS"]["buckets"]:
                        counters[item["key"]][status_item["key"]] += status_item["doc_count"]
            else:
                for person_item in list(
                    result.iter_raw_bucket_agg("PARTICIPANTS.PEOPLE.NAME")
                ) or list(result.iter_raw_bucket_agg("PARTICIPANTS.PEOPLE.DESKS.NAME")):
                    for status_item in person_item["REVERSE"]["STATUS"]["buckets"]:
                        counters[person_item["key"]][status_item["key"]] += status_item["doc_count"]

        return [
            {
                "name": name,
                **self.aggregate_status_counts(counts),
                "total": sum(counts.values()),
            }
            for name, counts in counters.items()
        ]

    async def save_existing(self, alert: SurveillanceAlert, **kwargs):
        if alert.detail.queryId is None:
            alert.detail.queryId = alert.detail.queryName
        return await self.repo.save_existing(alert, **kwargs)

    async def get_alerts_timeline(self, **params):
        if not params.get("interval"):
            params["interval"] = "14d"

        agg = AlertsAggByTime(**params)
        result = await self.get_aggs(
            search_model=agg,
            record_model=[
                CommunicationAlert,
                OrderAlert,
                MarketAbuseAlert,
                MarketAbuseScenarioTag,
            ],
        )

        counters = collections.defaultdict(lambda: collections.defaultdict(int))
        for dt_item in result.iter_raw_bucket_agg("DATES"):
            for k in ("MAR", "MAR_ALERT", "OTHER"):
                for status_item in dt_item[k]["STATUS"]["buckets"]:
                    if k == "MAR_ALERT":
                        counters[dt_item["key_as_string"], dt_item["key"]][status_item["key"]] += (
                            status_item.get("SCENARIO_COUNT", {}).get("value", 0)
                        )
                    else:
                        counters[dt_item["key_as_string"], dt_item["key"]][status_item["key"]] += (
                            status_item["doc_count"]
                        )

        return {
            "intervalInMillis": agg.interval_in_millis,
            "buckets": [
                {
                    "datetime": dt,
                    "timestamp": timestamp,
                    **self.aggregate_status_counts(counts),
                    "total": sum(counts.values()),
                }
                for (dt, timestamp), counts in counters.items()
            ],
        }

    async def get_alert_counts_by_watches(self, **params):
        if self._needs_policy():
            params.setdefault("needs_policy", True)

        result = await self.get_aggs(search_model_cls=CommsAlertsAggByWatchId, **params)

        counters = collections.defaultdict(lambda: collections.defaultdict(int))
        for watch_item in result.iter_raw_bucket_agg("WATCHES"):
            for status_item in watch_item["STATUS"]["buckets"]:
                counters[watch_item["key"]][status_item["key"]] += status_item["doc_count"]

        return [
            {
                "id": watch_id,
                **self.aggregate_status_counts(counts),
                "total": sum(counts.values()),
            }
            for watch_id, counts in counters.items()
        ]

    async def get_comms_alert_counts_by_watches(self, **params):
        if self._needs_policy():
            params.setdefault("needs_policy", True)

        result = await self.get_aggs(search_model_cls=CommsAlertsAggByWatchId, **params)

        counters = collections.defaultdict(lambda: collections.defaultdict(int))
        for watch_item in result.iter_raw_bucket_agg("WATCHES"):
            for status_item in watch_item["STATUS"]["buckets"]:
                counters[watch_item["key"]][status_item["key"]] += status_item["doc_count"]

        return [
            {
                "id": watch_id,
                **self.aggregate_status_counts(counts),
                "total": sum(counts.values()),
            }
            for watch_id, counts in counters.items()
        ]

    async def get_alerts_summary_by_status(
        self, with_sub_statuses, resolved_category_only: bool = False, **params
    ):
        watch_type = params.pop("watch_type", None)
        # If watch_type is None, returns the sum of MAR and non-MAR status counts.
        module = params.get("module", None)
        record_model = get_alert_record_models(module)

        if "model_qs" in params:
            params["search"] = params.pop("model_qs")

        result = await self.get_aggs(
            search_model_cls=AlertsAggByStatus,
            record_model=record_model,
            resolved_category_only=resolved_category_only,
            **params,
        )

        mar_counts = {status: 0 for status in AlertHitStatus.__members__}
        counts = {status: 0 for status in AlertHitStatus.__members__}

        # MarketScenarioTags can have many MarketAbuseAlerts(Market Absuse Orders)
        # MarketScenarioTags count or cardinality count of scenarioId in MarketAbuse
        # should be same. There are few cases where MarketScenarioTags doesn't have
        # MarketAbuseAlerts result in different count of "MAR.STATUS" and "MAR_ALERT.STATUS"
        #
        # Since MarketAbuseAlert contain orders, applying search filter will only return MarketAbuseAlerts
        # In this case MarketScenarioTags number would be zero.
        #
        # In Frontend MarketAbuse lists MarketAbuseScenarios. Since there could be disparity between
        # frontend marketAbuseScenarios list and marketAbuseAlerts number, we should preferably use
        # "MAR.STATUS" to build the number. In case search filter is applied use "MAR_ALERT.STATUS" numbers

        if not watch_type or watch_type == WatchBase.QueryType.MARKET_ABUSE:
            for item in result.iter_raw_bucket_agg("MAR.STATUS"):
                if item["key"] == "UNRESOLVED":
                    mar_counts["ASSIGNED"] = item["ASSIGNEE_AGG"]["doc_count"]
                    mar_counts["UNASSIGNED"] = item["doc_count"] - mar_counts["ASSIGNED"]
                if resolved_category_only and item["key"].startswith("RESOLVED"):
                    mar_counts.update(
                        {
                            i["key"]: mar_counts.get(i["key"], 0) + i["doc_count"]
                            for i in item["RESOLUTION_CATEGORY_AGG"]["buckets"]
                            + item["RESOLUTION_CATEGORY_CUSTOM_AGG"]["buckets"]
                        }
                    )
                mar_counts[item["key"]] += item["doc_count"]

            for item in result.iter_raw_bucket_agg("MAR_ALERT.STATUS"):
                if item["key"] == "UNRESOLVED":
                    counts["ASSIGNED"] = item["ASSIGNEE_AGG"]["ASSIGNEE_AGG_SCENARIO_COUNT"][
                        "value"
                    ]
                    counts["UNASSIGNED"] = item["SCENARIO_COUNT"]["value"] - counts["ASSIGNED"]
                if resolved_category_only and item["key"].startswith("RESOLVED"):
                    counts.update(
                        {
                            i["key"]: counts.get(i["key"], 0) + i["doc_count"]
                            for i in item["RESOLUTION_CATEGORY_AGG"]["buckets"]
                            + item["RESOLUTION_CATEGORY_CUSTOM_AGG"]["buckets"]
                        }
                    )
                counts[item["key"]] += item["SCENARIO_COUNT"]["value"]
            # Considering "MAR.STATUS" alert numbers first
            for key, value in mar_counts.items():
                if value:
                    counts[key] = value

        if not watch_type or watch_type == WatchBase.QueryType.TRADES:
            for item in result.iter_raw_bucket_agg("OTHER.STATUS"):
                if item["key"] in counts:
                    if item["key"] == "UNRESOLVED":
                        counts["ASSIGNED"] = (
                            counts.get("ASSIGNED", 0) + item["ASSIGNEE_AGG"]["doc_count"]
                        )
                        counts["UNASSIGNED"] = (
                            counts.get("UNASSIGNED", 0)
                            + item["doc_count"]
                            - item["ASSIGNEE_AGG"]["doc_count"]
                        )

                    if resolved_category_only and item["key"].startswith("RESOLVED"):
                        counts.update(
                            {
                                i["key"]: counts.get(i["key"], 0) + i["doc_count"]
                                for i in item["RESOLUTION_CATEGORY_AGG"]["buckets"]
                                + item["RESOLUTION_CATEGORY_CUSTOM_AGG"]["buckets"]
                            }
                        )
                    counts[item["key"]] += item["doc_count"]

        return (
            self.aggregate_status_counts_with_sub_statuses(counts, resolved_category_only)
            if with_sub_statuses
            else self.aggregate_status_counts(counts)
        )

    @classmethod
    def aggregate_status_counts_with_sub_statuses(
        cls, counts, resolved_category_only: bool = False
    ):
        return {
            "unresolved": {
                "total": counts.get(WorkflowAssigneeFilterOptions.UNASSIGNED.value, 0)
                + counts.get(WorkflowAssigneeFilterOptions.ASSIGNED.value, 0),
                WorkflowAssigneeFilterOptions.UNASSIGNED.value: counts.get(
                    WorkflowAssigneeFilterOptions.UNASSIGNED.value, 0
                ),
                WorkflowAssigneeFilterOptions.ASSIGNED.value: counts.get(
                    WorkflowAssigneeFilterOptions.ASSIGNED.value, 0
                ),
            },
            "underInvestigation": {
                "total": (
                    counts.get(AlertHitStatus.UNDER_INVESTIGATION.value, 0)
                    + counts.get(AlertHitStatus.IN_PROGRESS.value, 0)
                    + counts.get(AlertHitStatus.IN_REVIEW.value, 0)
                    + counts.get(AlertHitStatus.ESCALATED.value, 0)
                    + counts.get(AlertHitStatus.ASSIGNED_OTHER.value, 0)
                ),
                AlertHitStatus.UNDER_INVESTIGATION.value: counts.get(
                    AlertHitStatus.UNDER_INVESTIGATION.value, 0
                ),
                AlertHitStatus.IN_PROGRESS.value: counts.get(AlertHitStatus.IN_PROGRESS.value, 0),
                AlertHitStatus.IN_REVIEW.value: counts.get(AlertHitStatus.IN_REVIEW.value, 0),
                AlertHitStatus.ESCALATED.value: counts.get(AlertHitStatus.ESCALATED.value, 0),
                AlertHitStatus.ASSIGNED_OTHER.value: counts.get(
                    AlertHitStatus.ASSIGNED_OTHER.value, 0
                ),
            },
            "resolved": {
                **(
                    {
                        "total": sum(
                            [
                                counts[item]
                                for item in counts
                                if item not in cls._NON_CATEGORY_VALUES_
                            ]
                        ),
                        **{
                            item: counts[item]
                            for item in counts
                            if item not in cls._NON_CATEGORY_VALUES_
                        },
                    }
                    if resolved_category_only
                    else {
                        "total": (
                            counts.get(AlertHitStatus.RESOLVED.value, 0)
                            + counts.get(AlertHitStatus.RESOLVED_WITH_INVESTIGATION.value, 0)
                            + counts.get(AlertHitStatus.RESOLVED_WITH_DISMISSAL.value, 0)
                            + counts.get(AlertHitStatus.RESOLVED_WITH_BREACH.value, 0)
                            + counts.get(
                                AlertHitStatus.RESOLVED_WITH_INVESTIGATION_WITH_BREACH.value, 0
                            )
                        ),
                        AlertHitStatus.RESOLVED.value: counts.get(AlertHitStatus.RESOLVED.value, 0),
                        AlertHitStatus.RESOLVED_WITH_INVESTIGATION.value: counts.get(
                            AlertHitStatus.RESOLVED_WITH_INVESTIGATION.value, 0
                        ),
                        AlertHitStatus.RESOLVED_WITH_DISMISSAL.value: counts.get(
                            AlertHitStatus.RESOLVED_WITH_DISMISSAL.value, 0
                        ),
                        AlertHitStatus.RESOLVED_WITH_BREACH.value: counts.get(
                            AlertHitStatus.RESOLVED_WITH_BREACH.value, 0
                        ),
                        AlertHitStatus.RESOLVED_WITH_INVESTIGATION_WITH_BREACH.value: counts.get(
                            AlertHitStatus.RESOLVED_WITH_INVESTIGATION_WITH_BREACH.value, 0
                        ),
                    }
                ),
            },
        }

    @classmethod
    def aggregate_status_counts(cls, counts):
        return {
            "unresolved": counts.get(AlertHitStatus.UNRESOLVED.value, 0),
            "underInvestigation": (
                counts.get(AlertHitStatus.UNDER_INVESTIGATION.value, 0)
                + counts.get(AlertHitStatus.IN_PROGRESS.value, 0)
                + counts.get(AlertHitStatus.IN_REVIEW.value, 0)
                + counts.get(AlertHitStatus.ESCALATED.value, 0)
                + counts.get(AlertHitStatus.ASSIGNED_OTHER.value, 0)
            ),
            "resolved": (
                counts.get(AlertHitStatus.RESOLVED.value, 0)
                + counts.get(AlertHitStatus.RESOLVED_WITH_INVESTIGATION.value, 0)
                + counts.get(AlertHitStatus.RESOLVED_WITH_DISMISSAL.value, 0)
                + counts.get(AlertHitStatus.RESOLVED_WITH_BREACH.value, 0)
                + counts.get(AlertHitStatus.RESOLVED_WITH_INVESTIGATION_WITH_BREACH.value, 0)
            ),
        }

    async def get_alerts_summary_by_people(self, **params):
        counters = collections.defaultdict(lambda: collections.defaultdict(int))

        module = params.get("module", None)
        record_model = get_alert_record_models(module=module)
        if module == ModuleTitle.TRADE_SURVEILLANCE:
            result = await self.get_aggs(
                search_model_cls=AlertsAggByTradesPeople,
                record_model=record_model,
                **params,
            )
            for pt in PERSON_TYPES:
                for person_item in result.iter_raw_bucket_agg(f"{pt.upper()}.NAME"):
                    name = person_item["key"]
                    for k in ("MAR", "MAR_ALERT", "OTHER"):
                        for status_item in person_item[k]["STATUS"]["buckets"]:
                            if k == "MAR_ALERT":
                                counters[name][status_item["key"]] += status_item.get(
                                    "SCENARIO_COUNT", {}
                                ).get("value", 0)
                            else:
                                counters[name][status_item["key"]] += status_item["doc_count"]

        elif module == ModuleTitle.COMMS_SURVEILLANCE:
            result = await self.get_aggs(
                search_model_cls=AlertsAggByCommsPeople,
                record_model=record_model,
                **params,
            )
            for person_item in result.iter_raw_bucket_agg("PARTICIPANTS.PEOPLE.NAME"):
                for status_item in person_item["REVERSE"]["STATUS"]["buckets"]:
                    counters[person_item["key"]][status_item["key"]] += status_item["doc_count"]

        else:
            raise ValueError(f"Unsupported module: {module!r}")

        return [
            {
                "name": person,
                **self.aggregate_status_counts(counts),
                "total": sum(counts.values()),
            }
            for person, counts in counters.items()
        ]

    async def get_alerts_summary_by_work_status(self, **params):
        """
        Retrieves the alert counts.
        Returns:
            dict: A dictionary containing the alert counts in the following format:
                {
                    "myWork": count_assigned_or_resolved,
                    "otherWork": count_unassigned,
                    "unassigned": count_not_assigned
                }
            where:
                - "myWork" represents the count of alerts assigned to or resolved by the user.
                - "otherWork" represents the count of alerts not assigned to the user or resolved by him/her
                - "unassigned" represents the count of alerts with no assigned user.
        """

        result = (
            await self.get_aggs(
                search_model_cls=AlertsAggByWorkStatus,
                record_model=[
                    CommunicationAlert,
                    OrderAlert,
                    MarketAbuseAlert,
                    MarketAbuseScenarioTag,
                ],
                **params,
            )
        ).dict()

        return {
            "myWork": nested_dict_get(result, "aggregations.MY_WORK.doc_count") or 0,
            "otherWork": nested_dict_get(result, "aggregations.OTHER_PEOPLE_WORK.doc_count") or 0,
            "unassigned": nested_dict_get(result, "aggregations.UNASSIGNED.doc_count") or 0,
        }

    async def get_watch_alerts_grouped_by_thread(self, module: ModuleTitle = None, **params):
        params = {k: v for k, v in params.items() if k != "pagination"}
        record_model = get_alert_record_models(module=module)
        result = await self.get_aggs(
            search_model_cls=AlertsAggByThread, record_model=record_model, **params
        )

        agg_result = []

        for thread in result.iter_raw_bucket_agg("AGG_RESULTS_BY_THREAD_ID"):
            recent_date = thread.get("RECENT_DATE", {}).get("value")
            thread_info = {
                "&id": thread.get("key"),
                "threadId": thread.get("key"),
                "dateOfLastAlert": int(recent_date) if recent_date is not None else None,
                "watchesDetected": thread.get("TOTAL_WATCHES_DETECTED", {}).get("value"),
            }

            status_counts = collections.defaultdict(int)
            for status in thread.get("AGGS_BY_STATUS", {}).get("buckets", []):
                status_counts[status.get("key")] = status.get("doc_count")

            status_counts = AlertsRepository.aggregate_status_counts(status_counts)
            status_response = {
                "unresolved": AlertHitStatus.UNRESOLVED.value,
                "underInvestigation": AlertHitStatus.UNDER_INVESTIGATION.value,
                "resolved": AlertHitStatus.RESOLVED.value,
            }
            for status, count in status_counts.items():
                if count:
                    thread_info[status_response[status]] = count

            participant_info = []

            for hit in thread.get("TOP_HITS_BY_THREAD", {}).get("hits", {}).get("hits", []):
                inner_hit = hit.get("_source").get("hit")

                if inner_hit is None:
                    continue

                participant_info.extend(
                    participant.get("value", {}).get("name")
                    for participant in inner_hit.get("participants", [])
                    if participant is not None and participant.get("value", {}) is not None
                )
                identifiers = inner_hit.get("identifiers")
                if identifiers and identifiers.get("toIds"):
                    participant_info.extend(identifiers.get("toIds"))
                if identifiers and identifiers.get("fromId"):
                    participant_info.append(identifiers.get("fromId"))

            thread_info["participantInfo"] = ",".join(
                sorted([id for id in set(participant_info) if id])
            )

            agg_result.append(thread_info)

        return agg_result

    async def get_alerts_without_thread(self, **params) -> RawResult:
        return await self.get_many(
            record_model=CommunicationAlert,
            search_model_cls=AlertsWithoutThread,
            **params,
        )

    async def get_alert_slug_info(self, alert_ids: List[str]) -> SlugOut:
        raw_result = await self.get_many(
            SurveillanceAlert,
            search_model_cls=AlertByIdOrSlugSearch,
            alert_ids=alert_ids,
        )

        return SlugOut.output(raw_result)

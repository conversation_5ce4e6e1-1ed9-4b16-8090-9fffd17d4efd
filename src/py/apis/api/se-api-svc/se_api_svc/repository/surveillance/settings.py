# type: ignore
import datetime as dt
import logging
from api_sdk.repository.asyncronous.request_bound import RepoHelpersMixin
from api_sdk.schemas.base import RecordModel
from se_api_svc.permissions import get_module_admin_permission
from se_api_svc.schemas.settings import SettingsModule
from se_api_svc.schemas.surveillance.notification_settings import NotificationSetting
from se_api_svc.schemas.surveillance.workflow_settings import WorkflowSetting
from typing import ClassVar, List, Type, TypeVar, Union

log = logging.getLogger(__name__)


ST = TypeVar("ST", bound=RecordModel)


class SettingsRepository(RepoHelpersMixin):
    setting_cls: ClassVar[Type[ST]]

    async def get_module_settings(self, module: SettingsModule):
        return await self.get_many(self.setting_cls, terms={"module": module})

    async def create_module_setting(self, module: SettingsModule, **kwargs) -> ST:
        raise NotImplementedError()

    async def get_module_setting(
        self, module: SettingsModule, id: str, timestamp: int = None
    ) -> ST:
        return await self.get_one(
            self.setting_cls, id, terms={"module": module}, timestamp=timestamp
        )

    async def update_module_setting(self, ms: ST, **updates):
        for k, v in updates.items():
            setattr(ms, k, v)  # TODO validate the model after this!
        ms.updated = dt.datetime.utcnow()
        ms.updatedBy = self.userId
        await self.save_existing(ms)

    async def delete_module_setting(self, id: str) -> ST:
        ms = await self.get_one(self.setting_cls, id)
        await self.delete_existing(ms)
        return ms


class WorkflowSettingsRepository(SettingsRepository):
    setting_cls = WorkflowSetting

    async def get_my_module_settings(self, module: SettingsModule) -> List[Union[WorkflowSetting]]:
        is_module_admin = self.tenancy.has_permissions(get_module_admin_permission(module))

        setting: WorkflowSetting
        user_settings = []
        settings = await self.get_module_settings(module=module)
        for setting in settings.hits.hits:
            if not setting.enabled:
                continue
            if not is_module_admin and setting.is_admin_setting:
                continue
            user_settings.append(setting)

        return user_settings

    async def create_module_setting(
        self,
        module: SettingsModule,
        *,
        settingType: WorkflowSetting.SettingType,
        subSetting: WorkflowSetting.SubSetting,
        **kwargs,
    ) -> ST:
        id = f"{module}:{settingType}:{subSetting}"
        ms = self.setting_cls(
            id_=id,
            module=module,
            settingType=settingType,
            subSetting=subSetting,
            **kwargs,
            created=dt.datetime.utcnow(),
            createdBy=self.userId,
            updated=dt.datetime.utcnow(),
            updatedBy=self.userId,
        )
        await self.save_new(ms)
        return ms

    @staticmethod
    def get_crc_settings(settings, module):
        return [
            {
                "module": module,
                "category_visibility": settings.cSurvVisibility
                if module == SettingsModule.COMMS_SURVEILLANCE
                else settings.tSurvVisibility,
                "custom_categories": settings.cSurv
                if module == SettingsModule.COMMS_SURVEILLANCE
                else settings.tSurv,
            }
        ]


class NotificationSettingsRepository(SettingsRepository):
    setting_cls = NotificationSetting

    @staticmethod
    def id_for(
        module: SettingsModule,
        settingType: NotificationSetting.SettingType,
        option: NotificationSetting.Option,
    ):
        return f"{module.value}:{settingType.value}:{option.value}"

    async def create_module_setting(
        self,
        module: SettingsModule,
        *,
        settingType: NotificationSetting.SettingType,
        option: NotificationSetting.Option,
        **kwargs,
    ) -> ST:
        id = self.id_for(module, settingType, option)
        ms = self.setting_cls(
            id_=id,
            module=module,
            settingType=settingType,
            option=option,
            **kwargs,
            created=dt.datetime.utcnow(),
            createdBy=self.userId,
            updated=dt.datetime.utcnow(),
            updatedBy=self.userId,
        )
        await self.save_new(ms)
        return ms

import datetime as dt
import logging
from api_sdk.es_dsl.base import (
    And,
    ModelFilter,
    Not,
    NotExpiredFilter,
    Or,
    QueryString,
    SearchFeature,
    SearchModel,
    TermFilter,
)
from api_sdk.es_dsl.features import Nested, RangeFilter, SearchAfter
from api_sdk.es_dsl.flang import FlangFilter
from api_sdk.es_dsl.params import SearchModelParams
from api_sdk.full_text_search import (
    COMMS_TEXT_ANALYTICS_SEARCH_FIELDS,
    COMMS_TEXT_NON_NESTED_SEARCH_FIELDS,
    COMMS_TEXT_PARTICIPANTS_SEARCH_FIELDS,
    CSURV_ALERT_TEXT_SEARCH_FIELDS,
    get_free_text_search_filters,
    prefix_hit,
)
from api_sdk.models.elasticsearch import RawResult
from api_sdk.models.search import Sort
from api_sdk.repository.asyncronous.request_bound import RequestBoundRepository
from api_sdk.static import FieldType
from api_sdk.utils.intervals import get_interval_in_millis_from_params
from api_sdk.utils.utils import StringEnum
from elasticsearch_dsl import Q
from functools import cached_property
from se_api_svc.core.constants import ES_MAX_AGG_SIZE
from se_api_svc.repository.common import CustomClassProperty
from se_api_svc.repository.surveillance.alerts import (
    PersonFilter,
    UnresolvedAlertsAssigneeStatusFilter,
)
from se_api_svc.repository.surveillance.watches import customQueryString
from se_api_svc.schemas.surveillance.alerts import CommunicationAlert
from se_api_svc.schemas.surveillance.comms_surveillance.comm_surveillance.commsurveillance import (
    TrendIn,
)
from se_api_svc.schemas.surveillance.watches import Watch
from se_elastic_schema.components.communication.message_group import MessageGroup
from se_elastic_schema.static.surveillance import AlertHitStatus
from typing import Any, Dict, List, Optional, Union

log = logging.getLogger(__name__)

PERSON_TYPES = [
    "buyer",
    "seller",
    "buyerDecisionMaker",
    "sellerDecisionMaker",
    "trader",
    "participants",
]


class StatusResponseAdatptor(StringEnum):
    UNRESOLVED = "unresolved"
    UNDER_INVESTIGATION = "underInvestigation"
    RESOLVED = "resolved"


class CommunicationSurveillanceWatchSearch(SearchModel):
    class Params(SearchModelParams):
        watch_ids: Optional[List[str]]

    features = [
        ModelFilter(model=Watch),
        NotExpiredFilter,
        TermFilter(name="&id", param="watch_ids"),
    ]


@SearchFeature.register_q_builder
def BeforeMessageFilter(params):
    if params.before_message_timestamp:
        return Q(
            "range", **{"hit.timestamps.timestampStart": {"lt": params.before_message_timestamp}}
        )


@SearchFeature.register_q_builder
def SubjectFilter(params):
    if params.subject == dict():
        return ~Q("exists", **{"field": "hit.subject"})
    elif params.subject:
        return Q("term", **{"hit.subject": params.subject})


class CommunicationAlertSearch(SearchModel):
    class Params(SearchModelParams):
        f: Optional[str]
        workflow_status: Optional[str]
        unresolved_alerts_assignee_status: Optional[List[str]]
        workflow_statuses: Optional[List[str]]
        alert_resolution_category: Optional[List[str]] = None
        alert_resolution_sub_category: Optional[List[str]] = None
        watch_id: Optional[str]
        execution_id: Optional[str]
        thread_id: Optional[str]
        matched_lexica: Optional[List[str]]
        person: Optional[List[str]]
        start: Union[dt.datetime, dt.date] = None
        end: Union[dt.datetime, dt.date] = None
        record_id: Optional[Union[str, List[str]]] = None
        search_after: List = None
        alert_id: Optional[str] = None
        query_kind: Optional[str] = None
        comment_link_ids: Optional[List[str]] = None
        subject: Optional[Union[str, dict]] = None
        before_message_timestamp: Optional[Any] = None
        search: Optional[str] = None
        term_type: Optional[str] = None
        term: Optional[List[str]] = None
        term_base: Optional[List[str]] = None
        sub_behaviour_id: Optional[List[str]] = None
        behaviour_id: Optional[str] = None
        term_id: Optional[List[str]] = None
        local_parts_exclude: Optional[List[str]] = None
        local_parts_include: Optional[List[str]] = None
        domains_include: Optional[List[str]] = None
        domains_exclude: Optional[List[str]] = None
        languages: Optional[List[str]] = None
        backtest_period: Optional[dt.datetime] = None

    params: Params

    features = [
        NotExpiredFilter,
        SearchAfter,
        Or(
            TermFilter(name="&id", param="comment_link_ids"),
            TermFilter(name="slug", param="comment_link_ids"),
            And(
                ModelFilter(model=CommunicationAlert),
                FlangFilter.simple(
                    nested_paths=[
                        "hit.participants",
                        "hit.analytics.lexica",
                    ],
                ),
                TermFilter(name="workflow.status", param="workflow_status"),
                TermFilter(name="workflow.status", param="workflow_statuses"),
                TermFilter(name="workflow.resolutionCategory", param="alert_resolution_category"),
                TermFilter(
                    name="workflow.resolutionSubCategories", param="alert_resolution_sub_category"
                ),
                TermFilter(name="detail.watchId", param="watch_id"),
                TermFilter(name="&parent", param="execution_id"),
                TermFilter(name="hit.metadata.threadId", param="thread_id"),
                TermFilter(name="matchedLexica", param="matched_lexica"),
                TermFilter(name="hit.&id", param="record_id"),
                TermFilter(name="detail.queryKind", param="query_kind"),
                Or(
                    TermFilter(name="&id", param="alert_id"),
                    TermFilter(name="slug", param="alert_id"),
                ),
                RangeFilter(field="detected", start_param="backtest_period"),
                Nested(
                    TermFilter(name="hit.analytics.lexica.term", param="term"),
                    TermFilter(name="hit.analytics.lexica.behaviourId", param="behaviour_id"),
                    TermFilter(name="hit.analytics.lexica.termId", param="term_id"),
                    TermFilter(name="hit.analytics.lexica.termBase", param="term_base"),
                    TermFilter(name="hit.analytics.lexica.termType", param="term_type"),
                    TermFilter(
                        name="hit.analytics.lexica.subBehaviourId", param="sub_behaviour_id"
                    ),
                    TermFilter(name="hit.analytics.lexica.termLanguage", param="languages"),
                    path="hit.analytics.lexica",
                    ignore_unmapped=True,
                ),
                Nested(
                    TermFilter(
                        name="hit.identifiers.localParts.value", param="local_parts_include"
                    ),
                    ignore_unmapped=True,
                    path="hit.identifiers.localParts",
                ),
                Nested(
                    TermFilter(name="hit.identifiers.domains.value", param="domains_include"),
                    ignore_unmapped=True,
                    path="hit.identifiers.domains",
                ),
                Not(
                    Nested(
                        TermFilter(
                            name="hit.identifiers.localParts.value", param="local_parts_exclude"
                        ),
                        path="hit.identifiers.localParts",
                        ignore_unmapped=True,
                    ),
                ),
                Not(
                    Nested(
                        TermFilter(name="hit.identifiers.domains.value", param="domains_exclude"),
                        path="hit.identifiers.domains",
                        ignore_unmapped=True,
                    )
                ),
                SubjectFilter,
                RangeFilter(field="detected"),
                PersonFilter,
                BeforeMessageFilter,
                UnresolvedAlertsAssigneeStatusFilter,
                Or(
                    QueryString(
                        param="search",
                        fields=[
                            *CSURV_ALERT_TEXT_SEARCH_FIELDS,
                        ],
                    ),
                    *get_free_text_search_filters(
                        {
                            FieldType.NESTED.value: {
                                "hit.analytics.lexica": prefix_hit(
                                    COMMS_TEXT_ANALYTICS_SEARCH_FIELDS
                                ),
                                "hit.participants": prefix_hit(
                                    COMMS_TEXT_PARTICIPANTS_SEARCH_FIELDS
                                ),
                            },
                            FieldType.NON_NESTED.value: prefix_hit(
                                COMMS_TEXT_NON_NESTED_SEARCH_FIELDS
                            ),
                        },
                        qs_param="search",
                    ),
                ),
            ),
        ),
    ]

    default_sort_order = [Sort(field="detected", order=Sort.Order.desc)]

    extras = [
        {
            "_source": {
                "includes": [
                    "&*",
                    "hit.&*",
                    "detected",
                    "copilotAnalysis",
                    "hit.subject",
                    "hit.analytics",
                    "hit.callDuration",
                    "hit.callDurationSpeaking",
                    "hit.callType",
                    "hit.transcripts",
                    "hit.semantic",
                    "hit.transcribed",
                    "hit.voiceFile.analytics",
                    "hit.voiceTranscript",
                    "hit.transcriptionStatus",
                    "hit.waveform",
                    "hit.participants",
                    "highlights",
                    "hitModel",
                    "matchedLexica",
                    "matchedLexicaCategories",
                    "hit.timestamps",
                    "detail.*",
                    "workflow",
                    "hit.voiceFile.&*",
                    "hit.voiceFile.fileInfo.*",
                    "hit.voiceFile.fileName",
                    "hit.voiceFile.fileType",
                    "hit.attachments",
                    "hit.body",
                    "hit.identifiers",
                    "hit.metadata.threadId",
                    "hit.metadata.sizeInBytes",
                    "hit.metadata.messageId",
                    "hit.metadata.source",
                    *[f"hit.{field}" for field, _ in MessageGroup.__fields__.items()],
                    "slug",
                    "resolved",
                    "underlyingAlertId",
                    "neuralAssessments",
                    "hitModel",
                    "neuralLevelsReviewedBy",
                    "neuralLevelClosedAt",
                    "labels",
                ]
            }
        }
    ]


class CommunicationAlertsSummaryBySourceAggs(CommunicationAlertSearch):
    class Params(CommunicationAlertSearch.Params):
        trend: TrendIn
        interval: Optional[str]
        buckets: Optional[int]

    params: Params

    @cached_property
    def interval_in_millis(self) -> int:
        return get_interval_in_millis_from_params(self.params)

    def build_aggs(self) -> Optional[dict]:
        field = "hit.metadata.source.client" if self.params.trend == TrendIn.SOURCE else "hitModel"
        return {
            "TIMELINE": {
                "date_histogram": {
                    "field": "detected",
                    "fixed_interval": f"{self.interval_in_millis}ms",
                    "format": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
                    "min_doc_count": 1,
                },
                "aggs": {"SOURCE": {"terms": {"field": field}}},
            }
        }


class CommunicationAlertsAggFilter(SearchModel):
    class Params(SearchModelParams):
        term_type: Optional[str] = None
        term_base: Optional[List[str]] = None
        sub_behaviour_id: Optional[List[str]] = None
        behaviour_id: Optional[str] = None
        sub_behaviour_idb: Optional[List[str]] = None
        term_id: Optional[List[str]] = None
        f: Optional[str] = None

        languages: Optional[List[str]] = None

    params: Params

    features = [
        TermFilter(name="hit.analytics.lexica.termBase", param="term_base"),
        TermFilter(name="hit.analytics.lexica.termType", param="term_type"),
        TermFilter(name="hit.analytics.lexica.subBehaviourId", param="sub_behaviour_id"),
        TermFilter(name="hit.analytics.lexica.behaviourId", param="behaviour_id"),
        TermFilter(name="hit.analytics.lexica.termId", param="term_id"),
        TermFilter(name="hit.analytics.lexica.termLanguage", param="languages"),
        FlangFilter.simple(
            nested_paths=[
                "hit.participants",
                "hit.analytics.lexica",
            ],
        ),
    ]

    extras = []


class CommunicationAlertsHitByTermAggs(CommunicationAlertSearch):
    def build_aggs(self) -> Optional[dict]:
        aggs_query = CommunicationAlertsAggFilter(
            term_type=self.params.term_type,
            term_id=self.params.term_id,
            term_base=self.params.term_base,
            behaviour_id=self.params.behaviour_id,
            sub_behaviour_id=self.params.sub_behaviour_id,
            languages=self.params.languages,
        ).to_dict()

        return {
            "NESTED": {
                "nested": {"path": "hit.analytics.lexica"},
                "aggs": {
                    "FILTER": {
                        "filter": aggs_query.get("query", {"match_all": {}}),
                        "aggs": {
                            "TERM": {
                                "terms": {
                                    "field": "hit.analytics.lexica.termId",
                                    "size": ES_MAX_AGG_SIZE,
                                },
                                "aggs": {
                                    "total_doc_count": {
                                        "reverse_nested": {},
                                        "aggs": {"total": {"cardinality": {"field": "hit.&id"}}},
                                    },
                                    "TERM_BASE": {
                                        "terms": {
                                            "field": "hit.analytics.lexica.termBase",
                                            "size": ES_MAX_AGG_SIZE,
                                        }
                                    },
                                    "TERM": {
                                        "terms": {
                                            "field": "hit.analytics.lexica.term",
                                            "size": ES_MAX_AGG_SIZE,
                                        }
                                    },
                                    "TERM_PAIRED": {
                                        "terms": {
                                            "field": "hit.analytics.lexica.termPaired",
                                            "size": ES_MAX_AGG_SIZE,
                                        }
                                    },
                                    "LANGUAGE": {
                                        "terms": {
                                            "field": "hit.analytics.lexica.termLanguage",
                                            "size": ES_MAX_AGG_SIZE,
                                        }
                                    },
                                },
                            }
                        },
                    }
                },
            },
            "start_date": {"min": {"field": "detected"}},
            "end_date": {"max": {"field": "detected"}},
        }

    extras = []


class CommunicationAlertsHitByBaseTermAggs(CommunicationAlertSearch):
    def build_aggs(self) -> Optional[dict]:
        aggs_query = CommunicationAlertsAggFilter(
            term_type=self.params.term_type,
            term_id=self.params.term_id,
            behaviour_id=self.params.behaviour_id,
            sub_behaviour_id=self.params.sub_behaviour_id,
            term_base=self.params.term_base,
            languages=self.params.languages,
        ).to_dict()
        return {
            "NESTED": {
                "nested": {"path": "hit.analytics.lexica"},
                "aggs": {
                    "FILTER": {
                        "filter": aggs_query.get("query", {"match_all": {}}),
                        "aggs": {
                            "TERM": {
                                "terms": {
                                    "field": "hit.analytics.lexica.termBase",
                                    "size": ES_MAX_AGG_SIZE,
                                },
                                "aggs": {
                                    "total_doc_count": {
                                        "reverse_nested": {},
                                        "aggs": {"total": {"cardinality": {"field": "hit.&id"}}},
                                    },
                                    "total_terms": {
                                        "value_count": {
                                            "field": "hit.analytics.lexica.termBase",
                                        }
                                    },
                                    "distinct_terms": {
                                        "cardinality": {
                                            "field": "hit.analytics.lexica.termId",
                                        }
                                    },
                                    "TERM_PAIRED": {
                                        "terms": {
                                            "field": "hit.analytics.lexica.termPaired",
                                            "size": ES_MAX_AGG_SIZE,
                                        }
                                    },
                                },
                            }
                        },
                    }
                },
            },
            "start_date": {"min": {"field": "detected"}},
            "end_date": {"max": {"field": "detected"}},
        }

    extras = []


class CommunicationAlertsBackTestSummaryAggs(CommunicationAlertSearch):
    def build_aggs(self) -> Optional[dict]:
        query = CommunicationAlertsAggFilter(
            term_type=self.params.term_type,
            term_id=self.params.term_id,
            term_base=self.params.term_base,
            sub_behaviour_id=self.params.sub_behaviour_id,
            behaviour_id=self.params.behaviour_id,
        ).to_dict()
        return {
            "NESTED": {
                "nested": {"path": "hit.analytics.lexica"},
                "aggs": {
                    "FILTER": {
                        "filter": query.get("query", {"match_all": {}}),
                        "aggs": {
                            "SUB_BEHAVIOURS": {
                                "terms": {"field": "hit.analytics.lexica.subBehaviourId"},
                                "aggs": {
                                    "SUB_BEHAVIOUR": {
                                        "terms": {"field": "hit.analytics.lexica.subBehaviour"}
                                    },
                                    "HIT_COUNT": {
                                        "reverse_nested": {},
                                        "aggs": {
                                            "HIT_COUNT": {"cardinality": {"field": "hit.&id"}}
                                        },
                                    },
                                },
                            }
                        },
                    },
                },
            },
            "TOTAL": {"cardinality": {"field": "hit.&id"}},
            "start_date": {"min": {"field": "detected"}},
            "end_date": {"max": {"field": "detected"}},
        }

    extras = []


class CommunicationAlertLessFieldsSearch(CommunicationAlertSearch):
    class Params(CommunicationAlertSearch.Params):
        only_ids: bool = False
        without_content: bool = False

    params: Params

    @property
    def extras(self):
        if self.params.only_ids:
            return [
                {
                    "_source": {
                        "includes": [
                            "&id",
                            "slug",
                            "hit.&id",
                            "workflow",
                            "hitModel",
                            "hit.analytics",
                        ]
                    }
                }
            ]

        return [
            {
                "_source": {
                    "includes": [
                        "&*",
                        "hit.&*",
                        "detected",
                        "copilotAnalysis",
                        "hit.subject",
                        "hit.analytics",
                        "hit.callDuration",
                        "hit.callDurationSpeaking",
                        "hit.callType",
                        "hit.transcripts",
                        "hit.transcribed",
                        "hit.voiceFile.analytics",
                        "hit.voiceTranscript",
                        "hit.transcriptionStatus",
                        "hit.waveform",
                        "hit.participants",
                        "highlights",
                        "hitModel",
                        "matchedLexica",
                        "matchedLexicaCategories",
                        "hit.timestamps",
                        "detail.*",
                        "workflow",
                        "hit.voiceFile.&*",
                        "hit.voiceFile.fileInfo.*",
                        "hit.voiceFile.fileName",
                        "hit.voiceFile.fileType",
                        "hit.attachments",
                        "hit.body",
                        "hit.identifiers",
                        *[f"hit.{field}" for field, _ in MessageGroup.__fields__.items()],
                        "slug",
                        "resolved",
                        "underlyingAlertId",
                        "neuralAssessments",
                        "hitModel",
                        "neuralLevelsReviewedBy",
                        "neuralLevelClosedAt",
                    ],
                    "excludes": [
                        "hit.analytics.lexica.subBehaviour",
                        "hit.analytics.lexica.subBehaviourId",
                        "hit.analytics.lexica.termBase",
                        "hit.analytics.topic",
                        "hit.analytics.lexicaTopics",
                        "hit.body.displayText",
                    ]
                    if not self.params.without_content
                    else [
                        "hit.body",
                        "hit.analytics",
                        "hit.attachments",
                    ],
                }
            }
        ]


class CommunicationAlertThreadSummaryAgg(CommunicationAlertSearch):
    def build_aggs(self) -> Dict:
        return {
            "MIN_THREAD_TIMESTAMP": {"min": {"field": "hit.timestamps.timestampStart"}},
            "MAX_THREAD_TIMESTAMP": {"max": {"field": "hit.timestamps.timestampStart"}},
            "STATUS": {"terms": {"field": "workflow.status"}},
        }


class CommunicationAlertSummaryByCustomField(CommunicationAlertSearch):
    class Params(CommunicationAlertSearch.Params):
        agg_field: str
        take: Optional[int] = 50
        search: Optional[str] = None
        analyzer: Optional[str] = ".text"

    params: Params

    @CustomClassProperty
    def features(cls, instance):
        return CommunicationAlertSearch.features + [
            Or(
                And(
                    customQueryString(
                        default_field=instance.params.agg_field,
                        analyzer=".text",
                        search=instance.params.search,
                    )
                )
            )
        ]

    def build_aggs(self) -> Dict:
        nested_paths = ["hit.analytics.lexica"]

        nested_path, parts = None, self.params.agg_field.split(".")
        for i in range(len(parts) - 1, 0, -1):
            prefix = ".".join(parts[0:i])
            if prefix in nested_paths:
                nested_path = prefix
                break

        if nested_path:
            return {
                "aggs": {
                    "nested": {"path": nested_path},
                    "aggs": {
                        "BY_FIELD": {
                            "terms": {"field": self.params.agg_field, "size": self.params.take}
                        },
                    },
                }
            }

        return {
            "BY_FIELD": {"terms": {"field": self.params.agg_field, "size": self.params.take}},
        }


def source_deserializer(hit: Dict) -> Dict:
    return hit.get("_source")


class CommunicationAlertRepository(RequestBoundRepository):
    async def scroll_alerts(self, deserializer=source_deserializer, **params) -> List:
        search_model = CommunicationAlertSearch(**params)
        index = self.index_for_record_model(CommunicationAlert)

        return [
            record
            async for record in self.execute_scan(
                search_model,
                hit_deserializer=deserializer,
                index=index,
            )
        ]

    async def get_alerts(self, count=False, only_ids=False, **params) -> Union[RawResult, int]:
        search_model = CommunicationAlertSearch(**params)
        # ToDo: Remove this condition.
        if only_ids:
            search_model.extras = [
                {
                    "_source": {
                        "includes": [
                            "&id",
                            "slug",
                            "hit.&id",
                            "workflow",
                            "hitModel",
                            "detail",
                        ]
                    }
                }
            ]
        result = await self.execute_search(
            search_model,
            hit_deserializer=source_deserializer,
            index=self.index_for_record_model(CommunicationAlert),
            count=count,
        )
        return result

    async def get_alerts_with_less_fields(self, count=False, **params) -> Union[RawResult, int]:
        if "model_qs" in params:
            params["search"] = params.pop("model_qs")

        search_model = CommunicationAlertLessFieldsSearch(**params)

        result = await self.execute_search(
            search_model,
            hit_deserializer=source_deserializer,
            index=self.index_for_record_model(CommunicationAlert),
            count=count,
        )
        return result

    async def get_alert(self, alert_id) -> CommunicationAlert:
        return await self.get_one(
            CommunicationAlert, search_model=CommunicationAlertSearch(alert_id=alert_id)
        )

    @staticmethod
    def aggregate_status_counts(counts):
        return {
            StatusResponseAdatptor[AlertHitStatus.UNRESOLVED.value].value: counts.get(
                AlertHitStatus.UNRESOLVED.value, 0
            ),
            StatusResponseAdatptor[AlertHitStatus.UNDER_INVESTIGATION.value].value: (
                counts.get(AlertHitStatus.UNDER_INVESTIGATION.value, 0)
                + counts.get(AlertHitStatus.IN_PROGRESS.value, 0)
                + counts.get(AlertHitStatus.IN_REVIEW.value, 0)
                + counts.get(AlertHitStatus.ESCALATED.value, 0)
            ),
            StatusResponseAdatptor[AlertHitStatus.RESOLVED.value].value: (
                counts.get(AlertHitStatus.RESOLVED.value, 0)
                + counts.get(AlertHitStatus.RESOLVED_WITH_INVESTIGATION.value, 0)
                + counts.get(AlertHitStatus.RESOLVED_WITH_DISMISSAL.value, 0)
                + counts.get(AlertHitStatus.RESOLVED_WITH_BREACH.value, 0)
                + counts.get(AlertHitStatus.RESOLVED_WITH_INVESTIGATION_WITH_BREACH.value, 0)
            ),
        }

    async def get_alerts_thread_summary(self, thread_id: str, **search_params) -> Dict:
        aggs_result = await self.get_aggs(
            search_model_cls=CommunicationAlertSearch,
            record_model=CommunicationAlert,
            aggs={"STATUS": {"terms": {"field": "workflow.status"}}},
            thread_id=thread_id,
            **search_params,
        )

        counts = {status: 0 for status in AlertHitStatus.__members__}

        for item in aggs_result.iter_raw_bucket_agg("STATUS"):
            counts[item["key"]] += item["doc_count"]

        return self.aggregate_status_counts(counts)

    async def get_alerts_with_watches(self, **params) -> RawResult:
        alerts = await self.get_alerts(**params)

        watch_ids = {alert.get("detail").get("watchId") for alert in alerts.hits.hits}

        search_model = CommunicationSurveillanceWatchSearch(watch_ids=list(watch_ids))

        watches = await self.execute_search(
            search_model=search_model,
            hit_deserializer=source_deserializer,
            index=self.index_for_record_model(Watch),
        )

        watch_by_watch_id = {watch.get("&id"): watch for watch in watches.hits.hits}

        for alert in alerts.hits.hits:
            alert["watch"] = watch_by_watch_id[alert.get("detail").get("watchId")]

        return alerts

    async def get_alerts_summary_of_resolution_category_custom(self, **params):
        result = await self.get_aggs(
            search_model_cls=CommunicationAlertSearch,
            record_model=CommunicationAlert,
            aggs={
                "RESOLUTION_CATEGORY_CUSTOM": {
                    "terms": {"field": "workflow.resolutionSubCategoryCustom"}
                },
            },
            **params,
        )
        return list(result.iter_bucket_agg("RESOLUTION_CATEGORY_CUSTOM"))

    async def get_alerts_summary_by_field(self, **params):
        result = await self.get_aggs(
            search_model_cls=CommunicationAlertSummaryByCustomField,
            record_model=CommunicationAlert,
            **params,
        )
        return list(result.iter_raw_bucket_agg("BY_FIELD")) or list(
            result.iter_raw_bucket_agg("aggs.BY_FIELD")
        )

# ruff: noqa: E501
import datetime as dt
import json
from api_sdk.es_dsl.base import (
    ModelFilter,
    Not,
    NotExpiredFilter,
    Or,
    QueryString,
    SearchModel,
    TermFilter,
)
from api_sdk.es_dsl.features import RangeFilter
from api_sdk.es_dsl.flang import FlangFilter
from api_sdk.es_dsl.params import SearchModelParams
from api_sdk.exceptions import NotFound
from api_sdk.full_text_search import TSURV_ALERT_TEXT_SEARCH_FIELDS
from api_sdk.models.elasticsearch import RawResult
from api_sdk.models.search import CustomPagination, Pagination, Sort
from api_sdk.repository.asyncronous.request_bound import RepoHelpersMixin
from api_sdk.utils.utils import nested_dict_get
from dictbelt import dic_get
from se_api_svc.cloud.abstractions.abstract_cases_repository import AbstractCasesRepository
from se_api_svc.core.constants import ES_MAX_AGG_SIZE
from se_api_svc.repository.order.orders import OrdersAndExecutionsSearch, OrdersRepository
from se_api_svc.repository.surveillance.alerts import UnresolvedAlertsAssigneeStatusFilter
from se_api_svc.repository.surveillance.watches import WatchesRepository
from se_api_svc.schemas.cases.cases import Case
from se_api_svc.schemas.mar.scenarios import MarketAbuseScenario
from se_api_svc.schemas.order import Order
from se_api_svc.schemas.surveillance.market_abuse import (
    MarketAbuseAlert,
    MarketAbuseAlertOrScenarioTag,
    MarketAbuseScenarioTag,
)
from se_api_svc.schemas.surveillance.watches import Watch
from se_api_svc.schemas.surveillance.workflow import WorkflowStatus
from se_elastic_schema.static.surveillance import AlertHitStatus, MarketAbuseReportType
from typing import Any, Dict, List, Optional, Union

PERSON_TYPES = [
    "buyer",
    "seller",
    "buyerDecisionMaker",
    "sellerDecisionMaker",
    "trader",
    "participants",
]

SINGLE_HIT_MARKET_ABUSE_ALERT_LABELS = ["order", "restrictedListHit"]

SCENARIO_CASE_UPDT_SCRIPT = """
if (ctx._source.workflow != null) {
    HashMap w = ctx._source.workflow;
    w.putAll(params.update);
} else {
    ctx._source.workflow = params.update
}
if (ctx._source.detail == null) {
    Map m = new HashMap();
    m['createdOn'] = ctx._source['&timestamp'];
    ctx._source.detail = m
} else if (ctx._source.detail.createdOn == null) {
    ctx._source.detail.createdOn = ctx._source['&timestamp'];
}
"""
# EU-11553: The record keys in MarketAbuseScenarioTag is not uniform for all algos
# currently API supports the below keys only
EXECUTION_RECORD_KEYS_TO_CHECK = ["executions", "orderStates"]

# An algo to record key map, that checks which record keys to extract order ids from, this is used to enrich scenarios
# with isin and ric values.
ALGO_RECORD_KEY_MAP = {
    MarketAbuseReportType.MARKING_OPEN_CLOSE_INTRADAY_V2.value: ["orders", "executions"],
    MarketAbuseReportType.PAINTING_THE_TAPE_V2.value: ["orders", "executions"],
    MarketAbuseReportType.INSIDER_TRADING_V3_REFINITIV: [
        "observationPeriodExecutions",
        "behaviourPeriodExecutions",
    ],
    MarketAbuseReportType.FRONT_RUNNING_V2: [
        "frontRunningOrder",
        "frontRunOrders",
        "executionEntry",
        "executionExit",
    ],
    MarketAbuseReportType.FRONT_RUNNING_CLIENT_VS_CLIENT: [
        "frontRunningOrder",
        "frontRunOrders",
        "executionEntry",
        "executionExit",
    ],
    MarketAbuseReportType.FRONT_RUNNING: ["order", "newOrders", "executionEntry", "executionExit"],
    MarketAbuseReportType.SUSPICIOUS_LARGE_ORDER_VOLUME_V2: ["orderStates", "orders"],
    MarketAbuseReportType.SPOOFING_V2: ["executions", "fakeOrders", "realOrders"],
    MarketAbuseReportType.SPOOFING: ["orders", "executions"],
    MarketAbuseReportType.LAYERING_V2: ["executions", "orders"],
}


def sort_with_none(dictionary: dict, key: str, reverse: bool = False, float_type: bool = False):
    value = nested_dict_get(dictionary=dictionary, dotted_key=key)
    # The value MUST be comparable with its own type for sorting operations.
    if isinstance(value, list):
        # for lists, concat the values for sorting
        if not any(value):
            value = None
        else:
            value = max(value) if float_type else ",".join(str(v) for v in value if v is not None)
    try:
        value < value
    except TypeError:
        value = None

    sort_key = value is None
    if reverse:
        sort_key = value is not None

    return sort_key, value


class BaseScenarioSearch(SearchModel):
    class Params(SearchModelParams):
        alert_status: Optional[Union[str, List[str]]]
        alert_resolution_category: Optional[List[str]] = None
        alert_resolution_sub_category: Optional[List[str]] = None
        unresolved_alerts_assignee_status: Optional[List[str]] = None
        start: Optional[Union[dt.datetime, dt.date]]
        end: Optional[Union[dt.datetime, dt.date]]
        f: Any = None
        hit_keys: Optional[List[str]] = None
        people: Optional[Any] = None
        scenario_ids: Optional[Union[str, List[str]]]
        watch_id: Optional[Union[str, List[str]]]
        asset_class: Optional[List[str]] = None
        asset_class_sub: Optional[List[str]] = None
        investment_decision_maker: Optional[List[str]] = None
        instrument_full_name: Optional[List[str]] = None
        pad: Optional[List[str]] = None
        not_watch_type: Optional[str] = "ON_DEMAND"
        search: Optional[str] = None

    params: Params

    features = [
        NotExpiredFilter,
        TermFilter(name="workflow.status", param="alert_status"),
        Not(TermFilter(name="type", param="not_watch_type")),
        TermFilter(name="workflow.resolutionCategory", param="alert_resolution_category"),
        TermFilter(name="workflow.resolutionSubCategories", param="alert_resolution_sub_category"),
        RangeFilter(field="detected"),
        # TODO: Do we want to introduce a range filter here for hit.timestamps.*?
        Or(
            TermFilter(name="scenarioId", param="scenario_ids"),
            TermFilter(name="slug", param="scenario_ids"),
        ),
        TermFilter(name="detail.watchId", param="watch_id"),
        TermFilter(name="hit.&key", param="hit_keys"),
        TermFilter(
            name="hit.instrumentDetails.instrument.ext.bestExAssetClassMain", param="asset_class"
        ),
        TermFilter(
            name="hit.instrumentDetails.instrument.ext.bestExAssetClassSub", param="asset_class_sub"
        ),
        TermFilter(
            name="hit.tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.name",
            param="investment_decision_maker",
        ),
        TermFilter(
            name="hit.instrumentDetails.instrument.instrumentFullName", param="instrument_full_name"
        ),
        TermFilter(name="hit.marDetails.isPersonalAccountDealing", param="pad"),
        FlangFilter.simple(param="f"),
        QueryString(param="search", fields=[*TSURV_ALERT_TEXT_SEARCH_FIELDS]),
        UnresolvedAlertsAssigneeStatusFilter,
    ]


class ScenarioTagAndAlertsSearch(BaseScenarioSearch):
    class Params(BaseScenarioSearch.Params):
        case_id: Optional[str]
        hit_id: Optional[str]

    params: Params

    features = BaseScenarioSearch.features + [
        TermFilter(name="workflow.caseId", param="case_id"),
        TermFilter(name="hit.&id", param="hit_id"),
        ModelFilter(model=[MarketAbuseScenarioTag, MarketAbuseAlert]),
    ]


class MarketAbuseScenarioTagsSearch(BaseScenarioSearch):
    params: BaseScenarioSearch.Params

    features = BaseScenarioSearch.features + [ModelFilter(model=MarketAbuseScenarioTag)]


class MarketAbuseAlertsSearch(BaseScenarioSearch):
    class Params(BaseScenarioSearch.Params):
        label: Optional[Union[str, List[str]]]

    params: Params

    features = BaseScenarioSearch.features + [
        ModelFilter(model=MarketAbuseAlert),
        TermFilter(name="label"),
    ]


class ScenariosRepository(RepoHelpersMixin):
    def __init__(
        self,
        watch_repo: WatchesRepository,
        repo: AbstractCasesRepository,
        orders_repo: OrdersRepository,
    ):
        super().__init__(repo=repo)
        self.orders_repo = orders_repo
        self.watch_repo = watch_repo

    model = MarketAbuseAlertOrScenarioTag

    async def _add_computed_order_fields(self, orders: List):
        order_id_codes = [order.get("&id") for order in orders]

        aggs_by_order = await self.orders_repo.get_aggregated_order_data(
            order_id_codes=order_id_codes
        )

        if not any(aggs_by_order):
            return

        for order in orders:
            order_id_code = order.get("&id")
            order_stats = aggs_by_order.get(order_id_code)

            if not order_stats:
                continue

            order.update(order_stats)

            # Some (older?) data has quantities recorded as float, double-converting is needed
            initial_qty = int(float(order.get("priceFormingData", {}).get("initialQuantity", 0)))
            modified_qty = int(float(order.get("priceFormingData", {}).get("modifiedQuantity", 0)))
            traded_qty = int(float(order["tradedQuantity"]))
            # [ENG-10370] - Use the modified quantity
            # if it exists; otherwise, use the initial quantity.
            order["remainingQuantity"] = (modified_qty or initial_qty) - traded_qty

    async def _get_case(self, case_id: str):
        return await self.repo.get_one(record_model=Case, id=case_id)

    async def _optimized_get_orders_and_executions_by_keys(self, keys: List[str]) -> List:
        if len(keys) < 2000:
            orders_and_ex = await self._get_orders_and_executions_by_keys(keys=keys)
            return [o.to_dict() for o in orders_and_ex]

        orders_and_ex = await self._get_orders_and_executions_by_keys_with_scroll(keys=keys)
        return [o.get("_source", o) for o in orders_and_ex]

    async def _get_orders_and_executions_by_keys(self, keys: List[str]) -> List:
        result = await self.repo.get_many(
            search_model_cls=OrdersAndExecutionsSearch,
            pagination=CustomPagination(take=len(keys)),
            keys=keys,
        )
        return result.as_list()

    async def _get_orders_and_executions_by_keys_with_scroll(self, keys: List[str]) -> List:
        return [
            item
            async for item in self.repo.execute_scan(
                index=self.index_for_record_model(Order),
                search_model=OrdersAndExecutionsSearch(keys=keys),
            )
        ]

    async def _get_scenario_tag_by_scenario_id(self, scenario_id: str):
        result = await self.repo.get_many(
            search_model=MarketAbuseScenarioTagsSearch(scenario_ids=scenario_id)
        )

        if result.hits.total == 0:
            return

        return result.as_list()[0]

    async def _get_scenario_tags(self, **params) -> List:
        return [
            item
            async for item in self.repo.execute_scan(
                index=self.index_for_record_model(MarketAbuseScenarioTag),
                search_model=MarketAbuseScenarioTagsSearch(**params),
            )
        ]

    async def _get_scenario_alerts(self, **params):
        return [
            item
            async for item in self.repo.execute_scan(
                index=self.index_for_record_model(MarketAbuseAlert),
                search_model=MarketAbuseAlertsSearch(**params),
            )
        ]

    async def _get_scenario_alert_scenario_ids(self, **params) -> Optional[List]:
        result = await self.repo.get_aggs(
            search_model=MarketAbuseAlertsSearch(**params),
            aggs={
                "SCENARIO_IDS": {
                    "terms": {
                        "field": "scenarioId",
                        "size": ES_MAX_AGG_SIZE,
                    }
                }
            },
        )

        return list(set([bucket["key"] for bucket in result.iter_raw_bucket_agg("SCENARIO_IDS")]))

    async def get_scenario_by_id(
        self, identifier: str, rehydrate_scenario: bool = True
    ) -> Optional[MarketAbuseScenario]:
        scenario_tag = await self._get_scenario_tag_by_scenario_id(identifier)
        # if the identifier is a slug id, scenario id should come from the record itself

        if not scenario_tag:
            raise NotFound(f"Scenario {identifier} not found.")

        scenario_id = scenario_tag.scenarioId

        scenario = {}

        tag = scenario_tag.dict()

        if tag:
            scenario = tag.copy()

            if tag.get("tagText"):
                scenario.update(json.loads(tag.get("tagText")))
            elif tag.get("tag"):
                scenario.update(json.loads(tag.get("tag")))

        if not rehydrate_scenario:
            return MarketAbuseScenario.from_dict(scenario)

        # check for any case information
        if tag.get("workflow", {}).get("caseId"):
            case_response = await self._get_case(case_id=tag.get("workflow").get("caseId"))
            case = case_response.dict()
            tag["workflow"]["caseClosed"] = case.get("closed")
            tag["workflow"]["caseClosedResolutionCategory"] = case.get("closedResolutionCategory")
            tag["workflow"]["caseClosedComment"] = case.get("closedComment")

        scenario_alerts = await self._get_scenario_alerts(scenario_ids=scenario_id)

        alerts = {}

        for alert in scenario_alerts:
            label = alert.get("label")
            hit = alert.get("hit")

            if label not in alerts:
                alerts[label] = []

            alerts[label].append(hit)

        # treat the Order label as a single record instead of a list
        if "order" in alerts:
            alerts["order"] = alerts["order"][0]
            await self._add_computed_order_fields(orders=[alerts["order"]])

        if "records" not in scenario:
            scenario["records"] = alerts.copy()
        else:
            scenario["records"].update(alerts)

        # TODO - this is a bit of a HACK to make sure traderOrders is populated,
        #  as the scenario alerts do not seem to contain anything
        #  with the `traderOrders` label...
        if (
            "records" in scenario
            and "traderOrders" in scenario["records"]
            and any(
                scenario.get("records", {}).get("traderOrders", [])
                if hasattr(scenario.get("records", {}).get("traderOrders", []), "__iter__")
                else []
            )
        ):
            # collect the traderOrders IDs and populate them
            trader_orders_keys = scenario.get("records").get("traderOrders")
            trader_orders = await self._optimized_get_orders_and_executions_by_keys(
                keys=trader_orders_keys
            )

            scenario["records"]["traderOrders"] = list(trader_orders)
        report_type = dic_get(scenario, ["detail", "marketAbuseReportType"], default=[])
        if any(
            scenario.get("records", {}).get("orders", [])
            if hasattr(scenario.get("records", {}).get("orders", []), "__iter__")
            else []
        ):
            orders = nested_dict_get(scenario, "records.orders")
            record_order_keys = [orders] if isinstance(orders, str) else orders

            if isinstance(record_order_keys, list) and isinstance(record_order_keys[0], str):
                record_orders = await self._optimized_get_orders_and_executions_by_keys(
                    keys=record_order_keys
                )
            else:
                record_orders = record_order_keys

            await self._add_computed_order_fields(orders=record_orders)

            scenario["records"]["orders"] = record_orders
        for record_key in ALGO_RECORD_KEY_MAP.get(report_type, []):
            orders = nested_dict_get(scenario, f"records.{record_key}")
            record_order_keys = [orders] if isinstance(orders, str) else orders

            if (
                isinstance(record_order_keys, list)
                and record_order_keys
                and isinstance(record_order_keys[0], str)
            ):
                record_orders = await self._optimized_get_orders_and_executions_by_keys(
                    keys=record_order_keys
                )
                await self._add_computed_order_fields(orders=record_orders)
                scenario["records"][record_key] = record_orders

        # manual code for the case if resp is in string format
        # see EU-4625
        await self.executions(scenario=scenario)

        scenario.pop("tag", None)
        scenario.pop("tagText", None)

        return MarketAbuseScenario.from_dict(scenario)

    async def _enrich_scenario_with_record_orders(
        self, watch_id: str, scenarios: List[Dict], keys_to_retrieve: List[str]
    ):
        if not any(keys_to_retrieve):
            return

        order_keys = list(set(keys_to_retrieve))
        order_alerts = await self._get_scenario_alerts(
            watch_id=watch_id,
            hit_keys=order_keys,
        )

        orders_by_keys = {}

        for alert_hit in order_alerts:
            hit = alert_hit.get("hit")
            if hit:
                orders_by_keys[hit.get("&key")] = hit

        if any(orders_by_keys):
            for scenario in scenarios:
                if isinstance(scenario.get("records", {}).get("order", {}), dict):
                    continue

                record_order = orders_by_keys.get(scenario.get("records").get("order"))

                if record_order:
                    scenario["order"] = record_order.copy()

    async def _enrich_with_isin_name(self, scenarios: List[Dict], record_order_ids: List[str]):
        if not any(record_order_ids):
            return

        order_keys = list(set(record_order_ids))
        order_alerts = await self._get_orders_and_executions_by_keys(keys=order_keys)

        orders_by_keys = {}

        for order in order_alerts:
            orders_by_keys[order.key_] = (
                order.instrumentDetails.instrument.instrumentFullName,
                order.instrumentDetails.instrument.instrumentIdCode,
                order.instrumentDetails.instrument.ext.pricingReferences.RIC,
                order.instrumentDetails.instrument.ext.exchangeSymbol,
            )

        for scenario in scenarios:
            report_type = dic_get(scenario, ["detail", "marketAbuseReportType"], default=[])
            keys = [
                key
                for record_key in ALGO_RECORD_KEY_MAP.get(report_type, [])
                for key in (
                    scenario.get("records", {}).get(record_key, [])
                    if isinstance(scenario.get("records", {}).get(record_key, []), list)
                    else [scenario.get("records", {}).get(record_key)]
                )
            ]
            key = keys[0] if keys else None
            order = orders_by_keys.get(key)
            if scenario.get("additionalFields") and scenario.get("additionalFields").get(
                "topLevel"
            ):
                scenario["additionalFields"]["topLevel"]["instrumentFullName"] = (
                    order[0] if order and len(order) > 0 and order[0] else None
                )
                scenario["additionalFields"]["topLevel"]["instrumentIdCode"] = (
                    order[1] if order and len(order) > 1 and order[1] else None
                )
                # APPCIP-522/APPCIP-550 - RIC is not always present under
                #  scenario["additionalFields"]["topLevel"]["ric"]
                # fallsback to order.instrumentDetails.instrument.ext.pricingReferences.RIC or
                # order.instrumentDetails.instrument.ext.exchangeSymbol
                scenario["additionalFields"]["topLevel"]["ric"] = scenario.get(
                    "additionalFields"
                ).get("topLevel").get("ric") or (
                    order[2]
                    if order and len(order) > 2 and order[2] not in (None, "", {})
                    else order[3]
                    if order and len(order) > 3 and order[3] not in (None, "", {})
                    else None
                )

    @staticmethod
    def _populate_alerts(scenario_alerts: List[Dict]) -> Dict:
        alerts = {}

        for alert_hit in scenario_alerts:
            alert = alert_hit.get("_source")
            label = alert.get("label")
            hit = alert.get("hit")
            scenario_id = alert.get("scenarioId")

            if scenario_id not in alerts:
                alerts[scenario_id] = {}

            if label in SINGLE_HIT_MARKET_ABUSE_ALERT_LABELS:
                # the labels in this list indicate there should only be ONE hit
                alerts[scenario_id][label] = hit
                continue
            elif label not in alerts[scenario_id]:
                alerts[scenario_id][label] = []

            alerts[scenario_id][label].append(hit)

        return alerts

    async def get_scenarios_by_watch_id(
        self,
        watch_id: str,
        alert_status: Optional[List[AlertHitStatus]] = None,
        unresolved_alerts_assignee_status: Optional[List[str]] = None,
        people: Optional[List[str]] = None,
        rehydrate_scenarios: Optional[bool] = False,
        **params,
    ) -> RawResult:
        # remove these here, because we do NOT want pagination
        # applied to the Scenario Tags/Alerts queries...
        default_pagination = Pagination(
            take=10, sorts=[Sort(field="order.timestamps.orderSubmitted", order=Sort.Order.desc)]
        )
        page_params: Pagination = params.pop("pagination", default_pagination)

        is_on_demand = await self.watch_repo.get_watch_execution_type(watch_id, True)
        not_watch_type = None if is_on_demand else "ON_DEMAND"

        scenario_tags = list(
            await self._get_scenario_tags(
                watch_id=watch_id,
                alert_status=alert_status,
                unresolved_alerts_assignee_status=unresolved_alerts_assignee_status,
                people=people,
                not_watch_type=not_watch_type,
                **params,
            )
        )

        scenario_alerts = list()
        scenario_ids = list()

        if rehydrate_scenarios:
            scenario_alerts = list(
                await self._get_scenario_alerts(
                    watch_id=watch_id,
                    alert_status=alert_status,
                    unresolved_alerts_assignee_status=unresolved_alerts_assignee_status,
                    people=people,
                    not_watch_type=not_watch_type,
                    **params,
                )
            )
        else:
            scenario_ids = await self._get_scenario_alert_scenario_ids(
                watch_id=watch_id,
                alert_status=alert_status,
                unresolved_alerts_assignee_status=unresolved_alerts_assignee_status,
                people=people,
                not_watch_type=not_watch_type,
                **params,
            )

        # if, somehow we have alerts but no tags (usually because of a search filter)
        # collect all of the scenario ids and re-query without the search/f term
        if not any(scenario_tags) and (any(scenario_alerts) or any(scenario_ids)):
            if any(scenario_alerts) and not any(scenario_ids):
                scenario_ids = set(
                    [a.get("_source", {}).get("scenarioId") for a in scenario_alerts]
                )

            scenario_tags = await self._get_scenario_tags(
                watch_id=watch_id,
                alert_status=alert_status,
                unresolved_alerts_assignee_status=unresolved_alerts_assignee_status,
                start=params.get("start"),
                end=params.get("end"),
                not_watch_type=not_watch_type,
                scenario_ids=list(scenario_ids),
            )

        alerts = self._populate_alerts(scenario_alerts=scenario_alerts)

        scenarios = []

        record_order_keys_to_retrieve = []
        record_order_ids = []

        for tag in scenario_tags:
            scenario_id = tag.get("scenarioId")
            scenario = tag.copy()
            scenario_alerts = alerts.get(scenario_id)

            if tag.get("tagText"):
                scenario.update(json.loads(tag.get("tagText")))
                scenario.pop("tagText")
            elif tag.get("tag"):
                scenario.update(json.loads(tag.get("tag")))
                scenario.pop("tag")

            if scenario_alerts:
                if "records" not in scenario:
                    scenario["records"] = scenario_alerts.copy()
                else:
                    scenario["records"].update(scenario_alerts)

            if scenario.get("records", {}).get("order"):
                order = scenario.get("records").get("order")
                if isinstance(order, dict):
                    scenario["order"] = scenario.get("records").get("order").copy()
                elif isinstance(order, str):
                    # retrieve the record and populate
                    record_order_keys_to_retrieve.append(order)

            # This is a manual intervention to change field name
            # Since It has an extra space it needs to be changed in response.

            additional_fields = scenario.get("additionalFields").get("topLevel")

            if additional_fields.get("benchmarkTwoDateTime "):
                additional_fields["benchmarkTwoDateTime"] = additional_fields.pop(
                    "benchmarkTwoDateTime "
                )

            report_type = dic_get(scenario, ["detail", "marketAbuseReportType"], default=[])
            if report_type in list(ALGO_RECORD_KEY_MAP.keys()) and any(
                scenario.get("records", {}).get(record_key)
                for record_key in ALGO_RECORD_KEY_MAP.get(report_type, [])
            ):
                keys = [
                    key
                    for record_key in ALGO_RECORD_KEY_MAP.get(report_type, [])
                    for key in (
                        scenario.get("records", {}).get(record_key, [])
                        if isinstance(scenario.get("records", {}).get(record_key, []), list)
                        else [scenario.get("records", {}).get(record_key)]
                    )
                ]
                record_order_ids.append(keys[0])

            scenarios.append(scenario)

        await self._enrich_scenario_with_record_orders(
            watch_id=watch_id, scenarios=scenarios, keys_to_retrieve=record_order_keys_to_retrieve
        )

        await self._enrich_with_isin_name(scenarios=scenarios, record_order_ids=record_order_ids)

        total_scenarios = len(scenarios)
        scenarios = self._apply_sorting(scenarios=scenarios, page_params=page_params) or []

        return RawResult(
            **{"took": 0, "timed_out": False, "hits": {"hits": scenarios, "total": total_scenarios}}
        )

    @staticmethod
    def _apply_sorting(scenarios: List[Dict], page_params: Pagination):
        float_type = False
        float_list = [
            "additionalFields.topLevel.advPercentage",
            "additionalFields.topLevel.adv",
            "additionalFields.topLevel.totalOrderQuantity",
        ]
        if page_params.sorts:
            sort = page_params.sorts[0]
            reverse = sort.order == Sort.Order.desc
            if sort.field in float_list:
                float_type = True
            scenarios = sorted(
                scenarios,
                key=lambda scenario: sort_with_none(
                    dictionary=scenario, key=sort.field, reverse=reverse, float_type=float_type
                ),
                reverse=reverse,
            )

        if page_params.skip < len(scenarios):
            return scenarios[page_params.skip : page_params.skip + page_params.take]
        elif page_params.take < len(scenarios):
            return scenarios[0 : page_params.take]

    async def get_orders_by_watch_id(
        self,
        watch_id: str,
        alert_status: Optional[List[AlertHitStatus]] = None,
        unresolved_alerts_assignee_status: Optional[List[str]] = None,
        people: Optional[List[str]] = None,
        rehydrate_scenarios: Optional[bool] = False,
        **params,
    ) -> List:
        scenario_tags = list(
            await self._get_scenario_tags(
                watch_id=watch_id,
                alert_status=alert_status,
                unresolved_alerts_assignee_status=unresolved_alerts_assignee_status,
                people=people,
                **params,
            )
        )

        scenario_alerts = list()
        scenario_ids = list()

        if rehydrate_scenarios:
            scenario_alerts = list(
                await self._get_scenario_alerts(
                    watch_id=watch_id,
                    alert_status=alert_status,
                    unresolved_alerts_assignee_status=unresolved_alerts_assignee_status,
                    people=people,
                    **params,
                )
            )
        else:
            scenario_ids = await self._get_scenario_alert_scenario_ids(
                watch_id=watch_id,
                alert_status=alert_status,
                unresolved_alerts_assignee_status=unresolved_alerts_assignee_status,
                people=people,
                **params,
            )

        # if, somehow we have alerts but no tags (usually because of a search filter)
        # collect all of the scenario ids and re-query without the search/f term
        if not any(scenario_tags) and (any(scenario_alerts) or any(scenario_ids)):
            if any(scenario_alerts) and not any(scenario_ids):
                scenario_ids = set(
                    [a.get("_source", {}).get("scenarioId") for a in scenario_alerts]
                )

            scenario_tags = await self._get_scenario_tags(
                watch_id=watch_id,
                alert_status=alert_status,
                unresolved_alerts_assignee_status=unresolved_alerts_assignee_status,
                start=params.get("start"),
                end=params.get("end"),
                scenario_ids=list(scenario_ids),
            )

        alerts = self._populate_alerts(scenario_alerts=scenario_alerts)

        record_order_keys_to_retrieve = []

        for tag_hit in scenario_tags:
            tag = tag_hit.get("_source")
            scenario_id = tag.get("scenarioId")
            scenario = tag.copy()
            scenario_alerts = alerts.get(scenario_id)

            if tag.get("tagText"):
                scenario.update(json.loads(tag.get("tagText")))
                scenario.pop("tagText")
            elif tag.get("tag"):
                scenario.update(json.loads(tag.get("tag")))
                scenario.pop("tag")

            if scenario_alerts:
                if "records" not in scenario:
                    scenario["records"] = scenario_alerts.copy()
                else:
                    scenario["records"].update(scenario_alerts)

            if scenario.get("records", {}).get("order"):
                order = scenario.get("records").get("order")
                if isinstance(order, dict):
                    scenario["order"] = scenario.get("records").get("order").copy()
                elif isinstance(order, str):
                    # retrieve the record and populate
                    record_order_keys_to_retrieve.append(order)
        return record_order_keys_to_retrieve

    def attach_case(self, scenario_id: Union[str, List[str]], case_id, case_slug):
        """Attach a case to a scenario.

        Will update all MarketAbuseAlerts and MarketAbuseScenarioTag
        linked to ``scenario_id`` with the ``case_id``.
        """
        query = {
            "bool": {
                "must": [
                    {"terms": {"&model": ["MarketAbuseAlert", "MarketAbuseScenarioTag"]}},
                    {
                        "terms": {
                            "scenarioId": scenario_id
                            if isinstance(scenario_id, list)
                            else [scenario_id]
                        }
                    },
                ]
            }
        }

        script = {
            "source": SCENARIO_CASE_UPDT_SCRIPT,
            "params": {
                "update": {
                    "caseId": case_id,
                    "caseSlug": case_slug,
                    "status": AlertHitStatus.UNDER_INVESTIGATION.value,
                }
            },
        }

        return self.es_client.update_by_query(
            body={
                "query": query,
                "script": script,
            },
            index=self.index_for_record_model(self.model),
            refresh=True,
            conflicts="proceed",
        )

    async def remove_case(self, scenario):
        scenario.workflow.status = WorkflowStatus.UNRESOLVED
        scenario.workflow.updatedBy = self.repo.tenancy.session.principal
        scenario.workflow.caseId = None
        scenario.workflow.caseSlug = None
        return await self.save_existing(scenario)

    async def get_scenarios_by_case_and_hit(self, case_id: str, hit_id: str):
        return await self.get_many(
            search_model=ScenarioTagAndAlertsSearch(case_id=case_id, hit_id=hit_id)
        )

    async def get_watch_detail(self, watch_id: str):
        return await self.get_one(record_model=Watch, id=watch_id)

    async def executions(self, scenario: dict):
        # this is a manual "hack" for https://steeleye.atlassian.net/browse/PR-2128
        # the front-running algo should have a "frontRunExecutions"
        if dic_get(
            scenario, ["detail", "marketAbuseReportType"], default=[]
        ) == MarketAbuseReportType.FRONT_RUNNING.value and scenario.get("records", {}).get("order"):
            if isinstance(scenario.get("records", {}).get("order"), dict):
                keys = [scenario.get("records", {}).get("order", {}).get("&id")]
            else:
                keys = [scenario.get("records", {}).get("order")]
            executions_raw = await self.orders_repo.get_executions(keys=keys)
            executions = [e.to_dict(exclude_none=True) for e in executions_raw.as_list()]

            scenario["records"]["frontRunningExecutions"] = executions

        if self.execution_has_iterable_key(scenario=scenario):
            execution_keys = []
            records = scenario.get("records", {})
            for key in EXECUTION_RECORD_KEYS_TO_CHECK:
                if key in records:
                    for record in records.get(key):
                        if isinstance(record, str):
                            execution_keys.append(record)
                            scenario["records"][key] = (
                                await self._optimized_get_orders_and_executions_by_keys(
                                    keys=execution_keys
                                )
                                if execution_keys
                                else None
                            )

    @staticmethod
    def execution_has_iterable_key(scenario):
        """Checks if any of the specified keys in `keys` exist in
        `scenario['records']` and if their corresponding values are iterable.

        :param scenario: The dictionary to check.
        :param keys: A list of keys to check for.
        :return: True if any key exists and is iterable, False otherwise.
        """
        records = scenario.get("records", {})
        for key in EXECUTION_RECORD_KEYS_TO_CHECK:
            value = records.get(key, [])
            if hasattr(value, "__iter__") and value:
                return True
        return False

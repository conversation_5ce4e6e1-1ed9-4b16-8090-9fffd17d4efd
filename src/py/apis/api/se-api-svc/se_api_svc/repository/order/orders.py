# type: ignore
# noqa: E501
import datetime as dt
import logging
from api_sdk.es_dsl.base import (
    And,
    ModelFilter,
    NoExpirySearchModel,
    Not,
    NotExpired,
    Or,
    SearchFeature,
    SearchFeatureConfig,
    SearchModel,
    TermFilter,
)
from api_sdk.es_dsl.features import Nested, RangeFilter
from api_sdk.es_dsl.flang import FlangFilter
from api_sdk.es_dsl.params import SearchModelParams
from api_sdk.models.elasticsearch import RawResult
from api_sdk.repository.asyncronous.request_bound import RepoHelpersMixin
from api_sdk.schemas.orders.common import OrdersTrendChart
from api_sdk.search_models.orders import (
    ORDER_STAT_DETAILS_SUMMARY_FILEDS,
    ExecutionsSearch,
    OrdersAndExecutionsByStatusAgg,
    OrdersAndExecutionsSearch,
    OrdersAndExecutionsSearchByKeys,
    OrdersAndExecutionsStatistics<PERSON>ggs,
    OrdersAndExecutionsSummaryByField,
    OrdersAndExecutionsTimelineAgg,
    OrdersSearch,
    OrderTcaMetricsSummary,
)
from api_sdk.utils.intervals import get_interval_in_millis_from_params
from api_sdk.utils.utils import (
    nested_dict_get,
    nested_get,
    parse_datetime,
)
from dataclasses import dataclass
from functools import cached_property
from pathlib import Path
from se_api_svc.repository.order.common import (
    ORDERS_TREND_CHART_SPEC,
)
from se_api_svc.schemas.order import Order
from se_api_svc.schemas.orders.common import (
    ExecutionsTrendChart,
    GetOrdersPricingSpreadIn,
    GetOrdersSpeedIn,
    GetOrdersTrendsIn,
)
from se_api_svc.utils.math import weighted_average
from se_elastic_schema.static.market import ParticipationType as MarketParticipationType
from se_elastic_schema.static.mifid2 import OrderStatus
from typing import Dict, List, Optional, Tuple, Union

IS_NEWO_FILTER = TermFilter(
    name="executionDetails.orderStatus", value=OrderStatus.NEWO
)  # Is Order Record
NOT_NEWO_FILTER = Not(IS_NEWO_FILTER)  # Is Order Execution Record

log = logging.getLogger(__name__)

BEST_EX_OUTLIER_FIELD_MASK = {
    "above": "above touch",
    "aboveHalfPercent": "above (0.5%) touch",
    "aboveOnePercent": "above (1.0%) touch",
    "aboveFivePercent": "above (5.0%) touch",
    "below": "below touch",
    "belowFivePercent": "below (-5.0%) touch",
    "belowOnePercent": "below (-1.0%) touch",
    "belowHalfPercent": "below (-0.5%) touch",
    "total": "count of executions",
}

BUY_SELL_VALUE_MASK = {"BUYI": "buy", "SELL": "sell"}

BEST_EX_OUTLIER_FIELDS = [
    ("above", 0, 1),
    ("aboveHalfPercent", 0.005, 1),
    ("aboveOnePercent", 0.01, 1),
    ("aboveFivePercent", 0.05, 1),
    ("below", -1, 0),
    ("belowFivePercent", -1, -0.05),
    ("belowOnePercent", -1, -0.01),
    ("belowHalfPercent", -1, -0.005),
]

EXECUTIONS_TREND_CHART_SPEC = {
    ExecutionsTrendChart.COUNTERPARTY: ("counterparty.name", False, False),
    ExecutionsTrendChart.TRADER: ("trader.name", False, False),
}

ORDER_AND_FILL_EXPORT_FIELDS_CSV_PATH = (
    Path(__file__).parent.parent / "cases/export_fields/order_and_fill_export_fields.csv"
)


def none_as_zero(value: Optional[Union[int, float]]):
    return 0 if value is None else value


class PriceSpreadsFilter(SearchFeature):
    """Filter to create RangeFilters for the given spread filters.

    We receive an array of pricing spreads for eg: [{ field:
    'priceVsMarket', min: 0.5, max: 0.5 }] For these values generate an
    appropriate range queries.
    """

    @dataclass
    class Config(SearchFeatureConfig):
        field_prefix: str
        param: Optional[str] = "spreads"
        spreads: Optional[List[GetOrdersPricingSpreadIn]] = None

    config: Config

    def build_q(self, params=None, *, meta_fields):
        spreads = getattr(params, self.config.param, None) or self.config.spreads
        if not spreads:
            return

        return And(
            *[
                RangeFilter(
                    field=f"{self.config.field_prefix}.{spread.field}",
                    start=spread.min,
                    end=spread.max,
                )
                for spread in spreads
            ]
        ).build_q(meta_fields=meta_fields)


class SpeedFilter(SearchFeature):
    """Filter to create RangeFilters for the given speed spread.

    We receive an dict of min and max speed of execution in ms. For eg:
    { min: 100, max: 120000 } For these values generate an appropriate
    range query.
    """

    @dataclass
    class Config(SearchFeatureConfig):
        param: Optional[str] = "speed"
        speed: Optional[GetOrdersSpeedIn] = None

    config: Config

    def build_q(self, params=None, *, meta_fields):
        speed = getattr(params, self.config.param, None) or self.config.speed
        if not speed:
            return

        return RangeFilter(
            field="bestExecutionData.timeToFill", start=speed.min, end=speed.max
        ).build_q(meta_fields=meta_fields)


class TrendsFilter(SearchFeature):
    """Filter to create TermFilters for the given trends.

    We receive an array of trends for eg: [{ field: 'ASSET_CLASS',
    values: ['Debt Instruments'] }] For these values generate an
    appropriate Term queries. The field, nested_path is fetched from the
    ORDERS_TREND_CHART_SPEC config defined above.
    """

    @dataclass
    class Config(SearchFeatureConfig):
        param: Optional[str] = "trends"
        trends: Optional[List[GetOrdersTrendsIn]] = None

    config: Config

    def build_q(self, params=None, *, meta_fields):
        trends = getattr(params, self.config.param, None) or self.config.trends
        if not trends:
            return

        filters = []
        for trend in trends:
            field, nested_path, script = ORDERS_TREND_CHART_SPEC[trend.field]
            term_filter = TermFilter(name=field, value=trend.values)
            if nested_path:
                filters.append(Nested(term_filter, path=nested_path))
            else:
                filters.append(term_filter)

        return And(*filters).build_q(meta_fields=meta_fields)


class PeopleFilter(SearchFeature):
    @dataclass
    class Config(SearchFeatureConfig):
        people: Optional[List[str]] = None
        param: str = "people"

    config: Config

    def build_q(self, params=None, *, meta_fields):
        people = getattr(params, self.config.param, None) or self.config.people
        if not people:
            return

        return Nested(
            TermFilter(name="participants.types", value=MarketParticipationType.TRADER),
            TermFilter(name="participants.value.name", value=people),
            path="participants",
            ignore_unmapped=True,
        ).build_q(meta_fields=meta_fields)


TIME_SCRIPT = """
    def time = LocalDateTime.ofInstant(Instant.ofEpochMilli(doc['{field}'].value), ZoneId.of('Z'));
    def hour = time.getHour();
    def minute = time.getMinute();
    def second = time.getSecond();
    def timeInMillis = ((hour * 60 * 60) + (minute * 60) + (second)) * 1000;
    if (params.inside) {{
        return params.timeStart <= timeInMillis && timeInMillis <= params.timeEnd;
    }} else {{
        return params.timeStart > timeInMillis || timeInMillis > params.timeEnd;
    }}
"""


class OrdersAndExecutionsWithRawFileAggsSearch(OrdersSearch):
    """Get counts of comms with given s3 key."""

    class Params(SearchModelParams):
        start: Union[dt.datetime, dt.date] = None
        end: Union[dt.datetime, dt.date] = None
        raw_files: Union[str, List[str]] = None

    params: Params
    features = [
        TermFilter(name="sourceKey", param="raw_files"),
        ModelFilter(model=Order),
    ]

    def build_aggs(self) -> Optional[Dict]:
        aggs_query = {
            "RECORDS": {
                "terms": {"field": "sourceKey"},
            }
        }
        return aggs_query


class OrdersWithoutStatesFiltersSearch(SearchModel):
    """This will search will only apply filters on orders and not on
    OrderState."""

    class Params(SearchModelParams):
        f: Optional[str] = None
        ids: Optional[List[str]]
        start: Optional[Union[dt.datetime, dt.date]] = None
        end: Optional[Union[dt.datetime, dt.date]] = None

    features = [
        ModelFilter(model=Order),
        NotExpired,
        IS_NEWO_FILTER,
        TermFilter(param="ids", name="&id"),
        FlangFilter.simple(
            param="f",
        ),
    ]


class OrdersWithExecutionsSearchCountAggs(OrdersAndExecutionsSearch):
    features = [
        ModelFilter(model=Order),
        Or(
            And(*OrdersAndExecutionsSearch.features),
            TermFilter(name="&id", param="order_ids"),
        ),
    ]

    def build_aggs(self) -> Optional[Dict]:
        return {
            "COUNTS": {
                "filters": {
                    "filters": {
                        "ORDERS_COUNT": {
                            "bool": {
                                "must": [
                                    {"terms": {"executionDetails.orderStatus": [OrderStatus.NEWO]}},
                                ]
                            }
                        },
                        "ORDER_EVENTS_COUNT": {
                            "bool": {
                                "must_not": [
                                    {"terms": {"executionDetails.orderStatus": [OrderStatus.NEWO]}}
                                ],
                            }
                        },
                    }
                }
            }
        }


class OrdersInstrumentsWithoutStatesFiltersSearch(SearchModel):
    """This will search will only apply filters on orders and not on
    OrderState."""

    class Params(SearchModelParams):
        f: Optional[str] = None
        ids: Optional[List[str]]
        start: Optional[Union[dt.datetime, dt.date]] = None
        end: Optional[Union[dt.datetime, dt.date]] = None

    features = [
        ModelFilter(model=Order),
        NotExpired,
        IS_NEWO_FILTER,
        TermFilter(param="ids", name="&id"),
        FlangFilter.simple(
            param="f",
        ),
    ]

    extras = [
        {"_source": {"includes": ["&*", "instrumentDetails", "executionDetails", "timestamps"]}}
    ]


class ExecutionsTimelineAgg(ExecutionsSearch):
    class Params(ExecutionsSearch.Params):
        interval: Optional[str]
        buckets: Optional[int] = None

    params: Params
    granular_interval = 15 * 1000  # 15 seconds
    min_interval = 15 * 60 * 1000  # 15 minutes

    @cached_property
    def interval_in_millis(self) -> int:
        start = parse_datetime(self.params.start)
        end = parse_datetime(self.params.end)
        interval = self.params.interval

        if start is not None and end is not None and interval is None:
            # if the Start/End are within 30 minutes of each other,
            # we want to increase the granularity of the timeline to 15 seconds

            # Normalize the timezones before working out the time span.
            if start.tzinfo is None:
                start = start.replace(tzinfo=dt.timezone.utc)
            if end.tzinfo is None:
                end = end.replace(tzinfo=dt.timezone.utc)

            total_seconds = (end - start).total_seconds()
            if total_seconds <= 1800:
                return self.granular_interval

        return get_interval_in_millis_from_params(self.params, min_interval=self.min_interval)

    def build_aggs(self) -> Optional[Dict]:
        return {
            "TIMES": {
                "date_histogram": {
                    "field": "timestamps.tradingDateTime",
                    "fixed_interval": f"{self.interval_in_millis}ms",
                    "format": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
                    "min_doc_count": 0,
                }
            }
        }


class OrdersRepository(RepoHelpersMixin):
    async def _get_order_ids_from_aggregated_executions(self, **params):
        executions_search_model = ExecutionsSearch(**params)
        executions = await self.get_aggs(
            record_model=[Order],
            search_model=executions_search_model,
            aggs={
                "ORDER_IDS": {
                    "terms": {
                        "field": "&parent",
                        "size": 250,
                    }
                }
            },
        )

        order_ids = [execution["key"] for execution in executions.iter_raw_bucket_agg("ORDER_IDS")]
        return order_ids

    async def get_executions_timeline(
        self,
        start: dt.datetime = None,
        end: dt.datetime = None,
        buckets=None,
        interval=None,
        **params,
    ):
        if not start:
            start = await self.get_min_value(
                search_model_cls=OrdersAndExecutionsSearchByKeys,
                field="timestamps.orderSubmitted",
                as_string=True,
                end=end,
                **params,
            )
        if not end:
            end = dt.datetime.utcnow().replace(tzinfo=dt.timezone.utc)

        search_model = ExecutionsTimelineAgg(
            start=start, end=end, buckets=buckets, interval=interval, **params
        )

        agg_items = await self.get_aggs(search_model=search_model)

        return {
            "start": parse_datetime(start).isoformat() if start else None,
            "end": parse_datetime(end).isoformat() if end else None,
            "intervalInMillis": search_model.interval_in_millis,
            "buckets": [
                {
                    "timestamp": bucket["key"],
                    "datetime": bucket["key_as_string"],
                    "count": bucket["doc_count"],
                }
                for bucket in agg_items.iter_raw_bucket_agg("TIMES")
            ],
        }

    async def get_executions_trends_summary_by_type(
        self, trend_chart: ExecutionsTrendChart, **search_params
    ):
        field, nested_path, script = EXECUTIONS_TREND_CHART_SPEC[trend_chart]

        if nested_path:
            aggs = {
                "NESTED": {
                    "nested": {"path": nested_path},
                    "aggs": {"SUBJECTS": {"terms": {"field": field, "size": 5}}},
                }
            }
        else:
            # default aggregation structure
            agg = {"terms": {"field": field, "size": 5}}

            if script:
                agg = {"terms": {"script": {"source": script}}}

            aggs = {"SUBJECTS": agg}

        result = await self.get_aggs(
            search_model_cls=ExecutionsSearch,
            aggs=aggs,
            **search_params,
        )

        return {
            item["key"]: {"tradeCount": item["count"]}
            for item in result.iter_bucket_agg("NESTED.SUBJECTS" if nested_path else "SUBJECTS")
        }

    async def get_orders_stats(self, **params):
        order_count = await self.get_count(
            search_model=OrdersSearch(**params, order_status=[OrderStatus.NEWO]),
            index_suffix=Order.Config.index_suffix,
        )

        trade_count = await self.get_count(
            search_model=ExecutionsSearch(
                **params,
            ),
            index_suffix=Order.Config.index_suffix,
        )

        results = {"currentPeriodOrderCount": order_count, "currentPeriodTradeCount": trade_count}

        # if start AND end are populated
        # build previous period date range:
        # 1. Calculate Days Diff between Start/End
        # 2. Previous Period = [Start - DaysDiff, End = Start]
        start: Optional[Union[dt.date, dt.datetime]] = params.get("start")
        end: Optional[Union[dt.date, dt.datetime]] = params.get("end")

        if start and end:
            delta = end - start
            previous_period_start = start - dt.timedelta(days=delta.days)

            for key, statuses in [
                ("previousPeriodOrderCount", [OrderStatus.NEWO]),
                ("previousPeriodTradeCount", [OrderStatus.FILL, OrderStatus.PARF]),
            ]:
                count = await self.get_count(
                    search_model=OrdersSearch(
                        order_status=statuses,
                        start=previous_period_start,
                        end=start,
                    ),
                    index_suffix=Order.Config.index_suffix,
                )
                results[key] = count

        aggs_result = await self.get_aggs(
            search_model_cls=OrdersAndExecutionsStatisticsAggs,
            **params,
        )
        aggregations = getattr(aggs_result, "aggregations", {}) or {}
        stats = aggregations.get("STATS", {}).get("buckets", {})
        current_period = stats.get("CURRENT_PERIOD")
        previous_period = stats.get("PREVIOUS_PERIOD")

        results["currentPeriodTradersTraded"] = nested_dict_get(
            current_period, "TRADERS_TRADED.value"
        )
        results["currentPeriodCounterpartiesTraded"] = nested_dict_get(
            current_period, "COUNTERPARTIES_TRADED.value"
        )

        if previous_period:
            results["previousPeriodTradersTraded"] = nested_dict_get(
                previous_period, "TRADERS_TRADED.value"
            )
            results["previousPeriodCounterpartiesTraded"] = nested_dict_get(
                previous_period, "COUNTERPARTIES_TRADED.value"
            )

        return results

    async def get_orders_timeline(
        self,
        start: dt.datetime = None,
        end: dt.datetime = None,
        buckets=None,
        interval=None,
        **params,
    ):
        if not start:
            start = await self.get_min_value(
                search_model_cls=OrdersAndExecutionsSearchByKeys,
                field="timestamps.orderSubmitted",
                as_string=True,
                end=end,
                **params,
            )
        if not end:
            end = dt.datetime.utcnow().replace(tzinfo=dt.timezone.utc)

        search_model = OrdersAndExecutionsTimelineAgg(
            start=start, end=end, buckets=buckets, interval=interval, **params
        )

        res = await self.get_aggs(search_model=search_model)
        agg_items = nested_get(res.aggregations, "TIMES.buckets", default={})
        return {
            "start": parse_datetime(start).isoformat() if start else None,
            "end": parse_datetime(end).isoformat() if end else None,
            "intervalInMillis": search_model.interval_in_millis,
            "buckets": [
                {
                    "key": key,
                    "count": item["doc_count"],
                    "buckets": [
                        {
                            "timestamp": bucket["key"],
                            "datetime": bucket["key_as_string"],
                            "count": bucket["doc_count"],
                        }
                        for bucket in item["aggs"]["buckets"]
                    ],
                }
                for (key, item) in agg_items.items()
            ],
        }

    async def get_orders_trends_summary_by_type(
        self, trend_chart: OrdersTrendChart, **search_params
    ):
        field, nested_path, script = ORDERS_TREND_CHART_SPEC[trend_chart]

        if nested_path:
            aggs = {
                "NESTED": {
                    "nested": {"path": nested_path},
                    "aggs": {"SUBJECTS": {"terms": {"field": field, "size": 5}}},
                }
            }
        else:
            # default aggregation structure
            agg = {"terms": {"field": field, "size": 5}}

            if script:
                agg = {"terms": {"script": {"source": script}}}

            aggs = {"SUBJECTS": agg}

        result = await self.get_aggs(
            search_model_cls=OrdersAndExecutionsSearch,
            aggs=aggs,
            **search_params,
        )
        return list(result.iter_bucket_agg("NESTED.SUBJECTS" if nested_path else "SUBJECTS"))

    async def get_tca_metrics_summary(self, **params):
        search_model = OrderTcaMetricsSummary(
            **params,
        )
        aggs_result = await self.get_aggs(search_model=search_model)

        stat_weight_buckets = aggs_result.aggregations.get("STAT_WEIGHTS_AGG", {}).get(
            "buckets", []
        )
        # TODO: when we upgrade elasticsearch to vv6 or
        #  higher we can use weighted average instead of making this calculation
        # script aggs also cannot be used because
        # of creates max_compilations_per_minute has been excessed # noqa: E501
        tca_metrics_summary = {
            k: weighted_average(
                value_and_weights=[
                    {
                        "value": none_as_zero(item.get(k, {}).get("value")),
                        "weight": none_as_zero(item.get("key"))
                        * none_as_zero(item.get("doc_count")),
                    }
                    for item in stat_weight_buckets
                ]
            )
            for k in ORDER_STAT_DETAILS_SUMMARY_FILEDS.keys()
        }

        return tca_metrics_summary

    async def get_order_or_execution(self, id_):
        return await self.get_one(record_model=Order, id=id_)

    async def get_executions(self, record_model=Order, **params) -> RawResult:
        return await self.get_many(record_model, search_model_cls=ExecutionsSearch, **params)

    async def get_orders_and_executions_by_keys(self, keys: List[str], **params) -> RawResult:
        return await self.get_many(
            record_model=Order,
            search_model_cls=OrdersAndExecutionsSearchByKeys,
            keys=keys,
            **params,
        )

    async def get_one_by_key(self, key_):
        return await self.get_one(
            record_model=Order,
            search_model_cls=NoExpirySearchModel,
            terms={"&key": key_},
        )

    async def get_records_of_raw_files(self, raw_files: Union[str, List[str], Tuple[str]] = None):
        return await self.get_aggs(
            search_model=OrdersAndExecutionsWithRawFileAggsSearch(raw_files=raw_files),
        )

    async def get_orders_records(self, hit_deserializer=None, **params):
        """This function returns order records that satisfy the specified
        filters or the orders whose corresponding executions satisfy the
        filters."""
        order_ids = await self._get_order_ids_from_aggregated_executions(**params)

        return await self.get_many(
            record_model=[Order],
            search_model_cls=OrdersSearch,
            order_ids=order_ids,
            hit_deserializer=hit_deserializer,
            **params,
        )

    @staticmethod
    def get_exclusive_filter_params(
        ecb_ref_rate,
        order_volume_native,
        time_to_fill,
        external_time_to_fill,
        internal_time_to_fill,
        f_string,
    ):
        """OrdersSearch model queries both Order and OrderState model, so when
        only either of a filter value is changed/applied then we need to
        restrict the query to a single model, respective to the filter
        changed/applied.

        Args:
            ecb_ref_rate, order_volume_native - params that gets
            populated only in Order model
            time_to_fill, external_time_to_fill, internal_time_to_fill
            - params that gets populated only in OrderState model
            f-string is a combined filter applies to both models

        Returns:
            exclusive_ecb_ref_rate - value of Order model param
            and will be applied to OrderState model,
            so we will get 0 results from child OrderState
            query and similarly exclusive_time_to_fill is vice versa
        """

        exclusive_ecb_ref_rate = None
        if (order_volume_native is not None or ecb_ref_rate is not None) and not (
            time_to_fill is not None
            or internal_time_to_fill is not None
            or external_time_to_fill is not None
            or f_string
        ):
            exclusive_ecb_ref_rate = (ecb_ref_rate or 0) or (order_volume_native or 0)

        exclusive_time_to_fill = None
        if (
            time_to_fill is not None
            or internal_time_to_fill is not None
            or external_time_to_fill is not None
        ) and not (order_volume_native is not None or ecb_ref_rate is not None or f_string):
            exclusive_time_to_fill = (
                (time_to_fill or 0) or (internal_time_to_fill or 0) or (external_time_to_fill or 0)
            )

        return exclusive_ecb_ref_rate, exclusive_time_to_fill

    async def get_orders_and_executions(self, **params) -> RawResult:
        return await self.get_many(
            record_model=Order, search_model_cls=OrdersAndExecutionsSearch, **params
        )

    async def get_aggregated_order_data(self, order_id_codes: List[str], **param) -> Optional[Dict]:
        order_id_codes_size = len(order_id_codes) or 10

        aggs_result = await self.get_aggs(
            search_model_cls=ExecutionsSearch,
            aggs={
                "ORDER_IDS": {
                    "terms": {
                        "field": "&parent",
                        "size": order_id_codes_size,
                    },
                    "aggs": {
                        "STATUSES": {
                            "filters": {
                                "filters": {
                                    "AMENDS": {
                                        "terms": {
                                            "executionDetails.orderStatus": [
                                                OrderStatus.REMA,
                                                OrderStatus.REME,
                                                OrderStatus.REMH,
                                            ]
                                        }
                                    },
                                    "FILLS": {
                                        "terms": {
                                            "executionDetails.orderStatus": [
                                                OrderStatus.FILL,
                                                OrderStatus.PARF,
                                            ]
                                        }
                                    },
                                }
                            }
                        },
                        "FILL_ORDERS": {
                            "filter": {
                                "bool": {
                                    "must": {
                                        "terms": {
                                            "executionDetails.orderStatus": [
                                                OrderStatus.FILL,
                                                OrderStatus.PARF,
                                            ]
                                        }
                                    }
                                }
                            },
                            "aggs": {
                                "TOTAL_TRADED_QUANTITY": {
                                    "sum": {
                                        "field": "priceFormingData.tradedQuantity",
                                    },
                                },
                                "TOTAL_PRICE": {
                                    "sum": {
                                        "script": {
                                            "inline": (
                                                "def tradedQuantity = doc['priceFormingData.tradedQuantity'].size() > "  # noqa
                                                "0 ? doc['priceFormingData.tradedQuantity'].value : 0; def price = "  # noqa: E501
                                                "doc['priceFormingData.price'].size() > 0 ? doc["
                                                "'priceFormingData.price'].value : 0; tradedQuantity * price"  # noqa
                                            ),
                                        },
                                    },
                                },
                                "STAT_WEIGHTS_AGG": {
                                    "terms": {"field": "priceFormingData.tradedQuantity"},
                                    "aggs": {
                                        k: {"avg": {"field": v}}
                                        for k, v in ORDER_STAT_DETAILS_SUMMARY_FILEDS.items()
                                    },
                                },
                                "LAST_FILL_TIME": {"max": {"field": "timestamps.tradingDateTime"}},
                            },
                        },
                    },
                },
            },
            terms={"&parent": order_id_codes},
            **param,
        )

        aggs_by_order = {
            item["key"]: {
                # maintaining avgPrice/tradedQuantity at the root-level for legacy usage...
                "avgPrice": none_as_zero(
                    (
                        nested_dict_get(item, "FILL_ORDERS.TOTAL_PRICE.value")
                        / nested_dict_get(item, "FILL_ORDERS.TOTAL_TRADED_QUANTITY.value")
                    )
                    if nested_dict_get(item, "FILL_ORDERS.TOTAL_TRADED_QUANTITY.value")
                    else 0
                ),
                "tradedQuantity": none_as_zero(
                    item["FILL_ORDERS"]["TOTAL_TRADED_QUANTITY"]["value"]
                ),
                # TODO: when we upgrade elasticsearch to v6 or higher we
                #  can use weighted average instead of making
                #  this calculation script aggs also cannot be used because
                #  of creates max_compilations_per_minute
                #  has been excessed
                "statsDetails": {
                    **{
                        k: weighted_average(
                            value_and_weights=[
                                {
                                    "value": none_as_zero(stat_item.get(k, {}).get("value")),
                                    "weight": none_as_zero(stat_item.get("key"))
                                    * none_as_zero(stat_item.get("doc_count")),
                                }
                                for stat_item in item["FILL_ORDERS"]["STAT_WEIGHTS_AGG"].get(
                                    "buckets", []
                                )
                            ]
                        )
                        for k in ORDER_STAT_DETAILS_SUMMARY_FILEDS.keys()
                    },
                    "avgPrice": none_as_zero(
                        (
                            nested_dict_get(item, "FILL_ORDERS.TOTAL_PRICE.value")
                            / nested_dict_get(item, "FILL_ORDERS.TOTAL_TRADED_QUANTITY.value")
                        )
                        if nested_dict_get(item, "FILL_ORDERS.TOTAL_TRADED_QUANTITY.value")
                        else 0
                    ),
                    "tradedQuantity": none_as_zero(
                        item["FILL_ORDERS"]["TOTAL_TRADED_QUANTITY"]["value"]
                    ),
                    "amends": none_as_zero(
                        item["STATUSES"].get("buckets", {}).get("AMENDS", {}).get("doc_count", 0)
                    ),
                    "fills": none_as_zero(
                        item["STATUSES"].get("buckets", {}).get("FILLS", {}).get("doc_count", 0)
                    ),
                    "lifeCycle": none_as_zero(item["doc_count"]),
                    "lastFillTime": int(
                        none_as_zero(item["FILL_ORDERS"].get("LAST_FILL_TIME", {}).get("value"))
                    ),
                },
            }
            for item in aggs_result.iter_raw_bucket_agg("ORDER_IDS")
        }

        return aggs_by_order

    async def get_orders_summary_by_status(self, **params):
        search_model = OrdersAndExecutionsByStatusAgg(**params)

        aggs_result = await self.get_aggs(search_model=search_model)
        aggregations = getattr(aggs_result, "aggregations", {}) or {}
        stats = aggregations.get("STATUSES", {}).get("buckets", {})
        return {key: item.get("doc_count") for (key, item) in stats.items()}

    async def get_orders_summary_by_field(self, **params):
        result = await self.get_aggs(
            search_model_cls=OrdersAndExecutionsSummaryByField,
            record_model=Order,
            **params,
        )
        return list(result.iter_raw_bucket_agg("BY_FIELD"))

    async def get_orders_by_order_only_filters(self, **params) -> RawResult:
        """This will fetch orders and will not apply filters on OrderState."""
        return await self.get_many(
            record_model=[Order], search_model_cls=OrdersWithoutStatesFiltersSearch, **params
        )

    async def get_orders_instruments_by_order_only_filters(self, **params) -> RawResult:
        """This will fetch orders instrument details and will not apply filters
        on OrderState."""
        return await self.get_many(
            record_model=[Order],
            search_model_cls=OrdersInstrumentsWithoutStatesFiltersSearch,
            **params,
        )

    async def count_orders_and_order_events(self, hit_deserializer=None, **params):
        params["order_ids"] = await self._get_order_ids_from_aggregated_executions(**params)
        results = (
            await self.get_aggs(
                search_model_cls=OrdersWithExecutionsSearchCountAggs,
                **params,
            )
        ).aggregations

        return {
            key: item["doc_count"]
            for (key, item) in nested_get(results, "COUNTS.buckets", default={}).items()
        }

# type: ignore
import abc
import httpx
import math
import pandas as pd
from addict import Dict as Addict
from api_sdk.es_dsl.base import ModelFilter, SearchModel, TermFilter
from api_sdk.es_dsl.features import RangeFilter
from api_sdk.es_dsl.flang import FlangFilter
from api_sdk.es_dsl.params import SearchModelParams
from api_sdk.exceptions import BadInput
from api_sdk.models.elasticsearch import RawResult
from api_sdk.models.search import Pagination, Sort
from api_sdk.repository.asyncronous.request_bound import RequestBoundRepository
from api_sdk.utils.intervals import get_histogram_interval, get_interval_in_millis_from_params
from api_sdk.utils.utils import nested_dict_get, parse_datetime
from collections import deque
from datetime import date, datetime, timezone
from deepmerge import always_merger
from elasticsearch_dsl import Search
from flang.generators import BadFilterValue, NotSupported
from functools import cached_property
from glom import GlomError, glom
from pydantic import BaseModel
from schema_sdk.steeleye_model.gamma import GammaMetaFields
from se_api_svc.core.config import ApiServiceConfig
from se_api_svc.core.constants import ES_MAX_AGG_SIZE
from se_api_svc.repository.comms_surveillance.common import (
    CustomClassProperty,
    communication_base_search_model_features,
)
from se_api_svc.repository.comms_surveillance.static import language_mappings
from se_api_svc.repository.surveillance.comms_alerts import (
    CommunicationAlertsBackTestSummaryAggs,
    CommunicationAlertSearch,
    CommunicationAlertsHitByBaseTermAggs,
    CommunicationAlertsHitByTermAggs,
    CommunicationAlertsSummaryBySourceAggs,
)
from se_api_svc.schemas.comms import Call, Email, Meeting, Message, Text
from se_api_svc.schemas.surveillance.comms_surveillance.comm_surveillance.commsurveillance import (
    TREND_CHART_SPEC,
    LexicaBacktestIn,
    LexicaBacktestTrendChart,
    TermTypeIn,
    TrendIn,
    WordCloudGroupByIn,
)
from se_api_svc.schemas.surveillance.watches import Watch
from se_api_svc.services.lexica.service import LexicaService
from se_elastic_schema.components.surveillance.behaviour_query import SurveillanceBehaviourQuery
from se_elastic_schema.components.surveillance.excluded_lexicon import ExcludedLexicon
from se_elastic_schema.components.surveillance.surveillance_comms_query import (
    SurveillanceCommsQuery,
)
from se_elastic_schema.components.surveillance.surveillance_lexicon import SurveillanceLexicon
from se_elastic_schema.components.surveillance.watch_schedule_details import WatchScheduleDetails
from se_elastic_schema.models.tenant.surveillance.communication_alert import CommunicationAlert
from se_elastic_schema.static.comms.analytics import AnalyticsTermTypeEnum
from se_elastic_schema.static.surveillance import (
    CommsQueryType,
    ParticipantQueryType,
    WatchQueryType,
)
from surveillance_query_builder.behaviour_query_builder.flang import COMMS_FUNCS, COMMS_NESTED_PATHS
from surveillance_query_builder.comms_query_builder.builder import (
    CommsQueryBuilder,
    WatchCommsQueryBuilder,
)
from typing import Any, Dict, List, Optional, Union


class BasetermHit(BaseModel):
    termId: Optional[str]
    baseTerm: str
    hits: Optional[int]


class TermHit(BaseModel):
    termId: Optional[str]
    term: Optional[str]
    behaviour: Optional[str]
    behaviourId: Optional[str]
    subBehaviour: Optional[str]
    subBehaviourId: Optional[str]
    termBase: Optional[str]
    termCount: int
    collocatedTerm: Optional[str]
    lexicaOrPhrase: Optional[str]
    proximity: Optional[int]
    stemming: Optional[bool]
    fuzziness: Optional[bool]
    language: Optional[str]
    exclusionSearch_type: Optional[str]
    exclusionTerms: Optional[str]
    exclusionTopics: Optional[str]
    seLexicaOverrideType: Optional[str]
    termPaired: Optional[str]
    termType: Optional[str]
    hits: int
    hitPerDay: float
    matchedLexica: int


class TermsHitById(BaseModel):
    termId: str
    hits: int
    hitPerDay: float


class SubBehaviourHit(BaseModel):
    sub_behaviour_id: str
    sub_behaviour: str
    hits: int
    hitPerDay: float


class TrendSummary(BaseModel):
    start: datetime
    end: datetime
    intervalInMillis: int
    buckets: List[Dict]


class BacktestSubCategory(BaseModel):
    subBehaviourId: str
    subBehaviour: str
    hits: int
    percentageOfComms: float
    hitPerDay: float


class LexicaSummary(BaseModel):
    BASE_TERM: int = 0
    FULL_TERM: int = 0
    PHRASE: int = 0


class BespokeSummary(BaseModel):
    COUNT: int = 0


class Behaviour(BaseModel):
    behaviourId: str
    behaviour: str
    subBehaviours: Optional[List[BacktestSubCategory]]


class BacktestSummary(BaseModel):
    lexicaSummary: LexicaSummary
    totalHits: Optional[int]
    percentageOfComms: Optional[float]
    hitPerDay: Optional[float]
    behaviour: Behaviour


class BespokeBacktestSummary(BaseModel):
    totalHits: Optional[int]
    percentageOfComms: Optional[float]
    hitPerDay: Optional[float]


class RandomSamplingPrediction(BaseModel):
    totalHits: Optional[int]
    averageHitPerWeek: Optional[float]
    averageHitPerMonth: Optional[float]
    averageHitPerDay: Optional[float]


class BacktestHitsSummary(BaseModel):
    hits: int
    hitsPerWeek: float
    hitsPerDay: float


class HitsByTermHeader(BaseModel):
    nextSearchAfter: Optional[str] = None
    offset: Optional[str] = None
    previousSearchBefore: Optional[str] = None
    returnedHits: int
    skippedHits: int
    totalHits: int


class HitsByTerm(BaseModel):
    header: HitsByTermHeader
    results: List[TermHit]


class CommsSurveillanceAggFilter(SearchModel):
    class Params(SearchModelParams):
        term_type: Optional[str] = None
        term_base: Optional[List[str]] = None
        sub_behaviour_id: Optional[List[str]] = None
        behaviour_id: Optional[str] = None
        sub_behaviour_idb: Optional[List[str]] = None
        term_id: Optional[List[str]] = None
        f: Optional[str] = None

        languages: Optional[List[str]] = None

    params: Params

    features = [
        TermFilter(name="analytics.lexica.termBase", param="term_base"),
        TermFilter(name="analytics.lexica.termType", param="term_type"),
        TermFilter(name="analytics.lexica.subBehaviourId", param="sub_behaviour_id"),
        TermFilter(name="analytics.lexica.behaviourId", param="behaviour_id"),
        TermFilter(name="analytics.lexica.termId", param="term_id"),
        TermFilter(name="analytics.lexica.termLanguage", param="languages"),
        FlangFilter.simple(param="f", nested_paths=COMMS_NESTED_PATHS, funcs=COMMS_FUNCS),
    ]


class CommsSurveillanceSearchBase(SearchModel):
    class Params(SearchModelParams):
        term_type: Optional[str] = None
        term: Optional[List[str]] = None
        term_base: Optional[List[str]] = None
        sub_behaviour_id: Optional[List[str]] = None
        behaviour_id: Optional[str] = None
        term_id: Optional[List[str]] = None
        start: Union[datetime, date, None] = None
        end: Union[datetime, date, None] = None
        watch: Optional[Union[Watch, Dict, Addict]] = None
        skip: Optional[int] = 0
        take: Optional[int] = 50
        f: Optional[str] = None
        local_parts_exclude: Optional[List[str]] = None
        local_parts_include: Optional[List[str]] = None
        domains_include: Optional[List[str]] = None
        domains_exclude: Optional[List[str]] = None
        languages: Optional[List[str]] = None
        is_neural: Optional[bool] = False
        backtest_period: Optional[datetime] = None

    params: Params

    @CustomClassProperty
    def features(cls, instance):
        return [
            (
                ModelFilter(model=[Call])
                if instance is not None and instance.params.is_neural
                else ModelFilter(model=[Email, Call, Text, Message, Meeting])
            ),
            RangeFilter(field="&timestamp", start_param="backtest_period"),
        ] + communication_base_search_model_features

    default_sort_order = [Sort(field="createdOn", order=Sort.Order.desc)]

    def _build_comms_query(self):
        watch = self.params.watch
        if not watch:
            return None

        if issubclass(type(watch), BaseModel):
            watch = watch.to_dict(exclude_none=True)

        watch = Addict(watch)
        if watch.queryType != WatchQueryType.COMMUNICATIONS:
            raise BadInput(msg="Unable to work out stats for non-communication watches")

        try:
            watch.data = ["Call", "Email", "Text", "Message", "Meeting"]
            return WatchCommsQueryBuilder(watch).build_query()
        except (BadFilterValue, NotSupported) as e:
            raise BadInput(
                msg=f"The following query is not supported by flang, {watch.query}"
            ) from e

    def build(self, *args, **kwargs) -> Search:
        query = super().build(*args, **kwargs)
        comms_query = self._build_comms_query()
        if not comms_query:
            return query

        def is_model_filter(q):
            return "terms" in q and "&model" in q["terms"]

        comms_query["query"]["bool"]["must"] = [
            q for q in comms_query["query"]["bool"]["must"] if not is_model_filter(q)
        ]

        return Search.from_dict(always_merger.merge(query.to_dict(), comms_query))


class CommsSurveillanceSummaryAggs(CommsSurveillanceSearchBase):
    class Params(CommsSurveillanceSearchBase.Params):
        group_by: WordCloudGroupByIn = WordCloudGroupByIn.BASE_TERM

    params: Params

    def build_aggs(self) -> Optional[dict]:
        query = CommsSurveillanceAggFilter(
            term_type=self.params.term_type,
            term_id=self.params.term_id,
            term_base=self.params.term_base,
            behaviour_id=self.params.behaviour_id,
            sub_behaviour_id=self.params.sub_behaviour_id,
            languages=self.params.languages,
        ).to_dict()
        return {
            "NESTED": {
                "nested": {"path": "analytics.lexica"},
                "aggs": {
                    "FILTER": {
                        "filter": query.get("query", {"match_all": {}}),
                        "aggs": {
                            "TERM_COUNTS": {
                                "terms": {"field": "analytics.lexica.termId"},
                                "aggs": {
                                    "BASE_TERM": {"terms": {"field": "analytics.lexica.termBase"}}
                                },
                            }
                        },
                    }
                },
            }
        }


class CommsSurveillanceHitByTermAggs(CommsSurveillanceSearchBase):
    class Params(CommsSurveillanceSearchBase.Params):
        query_in: Any = None
        use_watch_filter: bool = False
        search: Optional[str] = None

    params: Params

    def build_aggs(self) -> Optional[dict]:
        watch_query = None

        if self.params.use_watch_filter:
            watch_query = CommsSurveillanceSearchBase(
                watch=Watch.from_query(
                    self.params.query_in.query,
                    self.params.query_in.query_type,
                ),
            ).to_dict(meta_fields=GammaMetaFields)

        aggs_query = CommsSurveillanceAggFilter(
            term_type=self.params.term_type,
            term_id=self.params.term_id,
            term_base=self.params.term_base,
            behaviour_id=self.params.behaviour_id,
            sub_behaviour_id=self.params.sub_behaviour_id,
            languages=self.params.languages,
        ).to_dict()

        return {
            "WATCH_FILTER": {
                "filter": (
                    watch_query.get("query", {"match_all": {}})
                    if watch_query
                    else {"match_all": {}}
                ),
                "aggs": {
                    "NESTED": {
                        "nested": {"path": "analytics.lexica"},
                        "aggs": {
                            "FILTER": {
                                "filter": aggs_query.get("query", {"match_all": {}}),
                                "aggs": {
                                    "TERM": {
                                        "terms": {
                                            "field": "analytics.lexica.termId",
                                            "size": ES_MAX_AGG_SIZE,
                                        },
                                        "aggs": {
                                            "TERM_BASE": {
                                                "terms": {
                                                    "field": "analytics.lexica.termBase",
                                                    "size": ES_MAX_AGG_SIZE,
                                                }
                                            },
                                            "TERM": {
                                                "terms": {
                                                    "field": "analytics.lexica.term",
                                                    "size": ES_MAX_AGG_SIZE,
                                                }
                                            },
                                            "TERM_PAIRED": {
                                                "terms": {
                                                    "field": "analytics.lexica.termPaired",
                                                    "size": ES_MAX_AGG_SIZE,
                                                }
                                            },
                                            "LANGUAGE": {
                                                "terms": {
                                                    "field": "analytics.lexica.termLanguage",
                                                    "size": ES_MAX_AGG_SIZE,
                                                }
                                            },
                                        },
                                    }
                                },
                            }
                        },
                    },
                },
            },
            "start_date": {"min": {"field": "&timestamp"}},
            "end_date": {"max": {"field": "&timestamp"}},
        }


class CommsSurveillancebespokeAggs(CommsSurveillanceSearchBase):
    def build_aggs(self) -> Optional[dict]:
        query = CommsSurveillanceAggFilter().to_dict()
        return {
            "aggs": {
                "filter": {
                    "filter": query.get("query", {"match_all": {}}),
                    "aggs": {
                        "COUNT": {
                            "terms": {"field": "&id"},
                        }
                    },
                }
            },
            "start_date": {"min": {"field": "&timestamp"}},
            "end_date": {"max": {"field": "&timestamp"}},
        }


class CommsSurveillanceHitByBaseTermAggs(CommsSurveillanceSearchBase):
    class Params(CommsSurveillanceSearchBase.Params):
        query_in: Any = None
        use_watch_filter: bool = False
        search: Optional[str] = None

    params: Params

    def build_aggs(self) -> Optional[dict]:
        watch_query = None

        if self.params.use_watch_filter:
            watch_query = CommsSurveillanceSearchBase(
                watch=Watch.from_query(
                    self.params.query_in.query,
                    self.params.query_in.query_type,
                ),
            ).to_dict(meta_fields=GammaMetaFields)

        aggs_query = CommsSurveillanceAggFilter(
            term_type=self.params.term_type,
            term_id=self.params.term_id,
            behaviour_id=self.params.behaviour_id,
            sub_behaviour_id=self.params.sub_behaviour_id,
            term_base=self.params.term_base,
            languages=self.params.languages,
        ).to_dict()
        return {
            "WATCH_FILTER": {
                "filter": (
                    watch_query.get("query", {"match_all": {}})
                    if watch_query
                    else {"match_all": {}}
                ),
                "aggs": {
                    "NESTED": {
                        "nested": {"path": "analytics.lexica"},
                        "aggs": {
                            "FILTER": {
                                "filter": aggs_query.get("query", {"match_all": {}}),
                                "aggs": {
                                    "TERM": {
                                        "terms": {
                                            "field": "analytics.lexica.termBase",
                                            "size": ES_MAX_AGG_SIZE,
                                        },
                                        "aggs": {
                                            "total_terms": {
                                                "value_count": {
                                                    "field": "analytics.lexica.termBase",
                                                }
                                            },
                                            "distinct_terms": {
                                                "cardinality": {
                                                    "field": "analytics.lexica.termId",
                                                }
                                            },
                                            "TERM_ID": {
                                                "terms": {
                                                    "field": "analytics.lexica.termId",
                                                    "size": ES_MAX_AGG_SIZE,
                                                }
                                            },
                                            "TERM_PAIRED": {
                                                "terms": {
                                                    "field": "analytics.lexica.termPaired",
                                                    "size": ES_MAX_AGG_SIZE,
                                                }
                                            },
                                            "LANGUAGE": {
                                                "terms": {
                                                    "field": "analytics.lexica.termLanguage",
                                                    "size": ES_MAX_AGG_SIZE,
                                                }
                                            },
                                        },
                                    }
                                },
                            }
                        },
                    },
                },
            },
            "start_date": {"min": {"field": "&timestamp"}},
            "end_date": {"max": {"field": "&timestamp"}},
        }


class CommsSurveillanceTermsHitAggs(CommsSurveillanceSearchBase):
    def build_aggs(self) -> Optional[dict]:
        query = CommsSurveillanceAggFilter(
            term_id=self.params.term_id,
        ).to_dict()
        return {
            "NESTED": {
                "nested": {"path": "analytics.lexica"},
                "aggs": {
                    "FILTER": {
                        "filter": query.get("query", {"match_all": {}}),
                        "aggs": {
                            "TERM": {
                                "terms": {"field": "analytics.lexica.termId"},
                            }
                        },
                    }
                },
            },
            "start_date": {"min": {"field": "&timestamp"}},
            "end_date": {"max": {"field": "&timestamp"}},
        }


class CommsSurveillanceSubBehaviourAggs(CommsSurveillanceSearchBase):
    def build_aggs(self) -> Optional[dict]:
        query = CommsSurveillanceAggFilter().to_dict()
        return {
            "NESTED": {
                "nested": {"path": "analytics.lexica"},
                "aggs": {
                    "FILTER": {
                        "filter": query.get("query", {"match_all": {}}),
                        "aggs": {
                            "SUB_BEHAVIOUR": {
                                "terms": {"field": "analytics.lexica.subBehaviour"},
                                "aggs": {
                                    "ID": {"terms": {"field": "analytics.lexica.subBehaviourId"}}
                                },
                            }
                        },
                    }
                },
            },
            "start_date": {"min": {"field": "&timestamp"}},
            "end_date": {"max": {"field": "&timestamp"}},
        }


class CommsSurveillanceBreakdownAggs(CommsSurveillanceSearchBase):
    def build_aggs(self) -> Optional[dict]:
        query = CommsSurveillanceAggFilter(
            term_type=self.params.term_type,
            term_base=self.params.term_base,
            sub_behaviour=self.params.sub_behaviour,
            sub_behaviours=self.params.sub_behaviours,
            behaviour=self.params.behaviour,
        ).to_dict()

        return {
            "NESTED": {
                "nested": {"path": "analytics.lexica"},
                "aggs": {
                    "FILTER": {
                        "filter": query.get("query", {"match_all": {}}),
                        "aggs": {
                            "BEHAVIOUR": {
                                "terms": {"field": "analytics.lexica.behaviour"},
                                "aggs": {
                                    "BEHAVIOUR": {
                                        "value_count": {"field": "analytics.lexica.behaviour"}
                                    },
                                    "SUB_BEHAVIOUR": {
                                        "terms": {"field": "analytics.lexica.subBehaviour"}
                                    },
                                },
                            },
                            "TERM_BASE": {
                                "terms": {"field": "analytics.lexica.termBase"},
                                "aggs": {
                                    "TERM_COUNT": {
                                        "value_count": {"field": "analytics.lexica.termBase"}
                                    }
                                },
                            },
                            "TERM_FULL": {
                                "terms": {"field": "analytics.lexica.term"},
                                "aggs": {
                                    "TERM_COUNT": {
                                        "value_count": {"field": "analytics.lexica.term"}
                                    }
                                },
                            },
                            "PHRASE": {
                                "terms": {"field": "analytics.lexica.termType"},
                                "aggs": {
                                    "TERM_COUNT": {
                                        "value_count": {"field": "analytics.lexica.termType"}
                                    }
                                },
                            },
                        },
                    }
                },
            }
        }


class CommsSurveillanceSummaryBySourceAggs(CommsSurveillanceSearchBase):
    class Params(CommsSurveillanceSearchBase.Params):
        trend: TrendIn
        interval: Optional[str]
        buckets: Optional[int]

    params: Params

    @cached_property
    def interval_in_millis(self) -> int:
        return get_interval_in_millis_from_params(self.params)

    def build_aggs(self) -> Optional[dict]:
        field = "metadata.source.client" if self.params.trend == TrendIn.SOURCE else "&model"
        return {
            "TIMELINE": {
                "date_histogram": {
                    "field": "timestamps.timestampStart",
                    "fixed_interval": f"{self.interval_in_millis}ms",
                    "format": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
                    "min_doc_count": 1,
                },
                "aggs": {"SOURCE": {"terms": {"field": field}}},
            }
        }


class CommsSurveillanceBackTestSummaryAggs(CommsSurveillanceSearchBase):
    def build_aggs(self) -> Optional[dict]:
        query = CommsSurveillanceAggFilter(
            term_type=self.params.term_type,
            term_id=self.params.term_id,
            term_base=self.params.term_base,
            sub_behaviour_id=self.params.sub_behaviour_id,
            behaviour_id=self.params.behaviour_id,
        ).to_dict()
        return {
            "NESTED": {
                "nested": {"path": "analytics.lexica"},
                "aggs": {
                    "FILTER": {
                        "filter": query.get("query", {"match_all": {}}),
                        "aggs": {
                            "SUB_BEHAVIOURS": {
                                "terms": {"field": "analytics.lexica.subBehaviourId"},
                                "aggs": {
                                    "SUB_BEHAVIOUR": {
                                        "terms": {"field": "analytics.lexica.subBehaviour"}
                                    },
                                },
                            }
                        },
                    },
                },
            },
            "start_date": {"min": {"field": "&timestamp"}},
            "end_date": {"max": {"field": "&timestamp"}},
        }


lambda_func_map = {
    "language": lambda x: pd.Series(language_mappings).get(x, x),
}


class BaseCommsLexicaRepository(abc.ABC):
    FILTER_FIELDS = [
        "identifiers",
        "participants",
        "metadata",
        "attachments",
        "body.text",
        "subject",
        "timestamps.",
    ]
    HIGH_LIGHT_FIELDS = [
        "body.text",
        "subject",
        "body.text.english",
        "subject.english",
        "body.text.english_stemmed",
        "subject.english_stemmed",
    ]

    @staticmethod
    def add_must_query(query: dict):
        if not query.get("bool"):
            query["bool"] = {}
        if not query["bool"].get("must"):
            query["must"] = []

        if isinstance(query["bool"]["must"], dict):
            q = query["bool"]["must"]
            query["bool"]["must"] = [q]

        return query

    @staticmethod
    def add_range_query(query: dict, start, end, field: str = "timestamps.timestampStart") -> dict:
        range_query = (
            RangeFilter(field=field, start=start, end=end)
            .build_q(meta_fields=GammaMetaFields)
            .to_dict()
        )

        query = BaseCommsLexicaRepository.add_must_query(query)
        query["bool"]["must"].append(range_query)
        return query

    @staticmethod
    def add_term_filter(query: dict, filter: TermFilter):
        query = BaseCommsLexicaRepository.add_must_query(query)
        query["bool"]["must"].append(filter.to_dict(meta_fields=GammaMetaFields))

        return query

    @staticmethod
    def get_date_diff(aggs: RawResult, include_time=False, return_delta=False):
        start_date = glom(aggs.aggregations, "start_date.value", default=0) or 0
        end_date = glom(aggs.aggregations, "end_date.value", default=0) or 0

        start_date_epoch = start_date / 1000
        end_date_epoch = end_date / 1000

        start_date = date.fromtimestamp(start_date_epoch)
        end_date = date.fromtimestamp(end_date_epoch)

        if include_time:
            start_date = datetime.fromtimestamp(start_date_epoch)
            end_date = datetime.fromtimestamp(end_date_epoch)

        delta = end_date - start_date

        if return_delta:
            return delta

        # to avoid divide by zero error
        return delta.days or 1

    @staticmethod
    def build_flang_string(body: SurveillanceBehaviourQuery) -> Optional[str]:
        if not body.filter or not body.filter.chunks:
            return None

        return " and ".join([c.f for c in body.filter.chunks])

    @staticmethod
    def get_backtest_query_dict(
        include_highlights: bool = False, highlight_fields: List = None, **params
    ) -> dict:
        # map_to_surveillance
        lexica_backtests: List[LexicaBacktestIn] = params.get("lexica_backtests")
        lexical = []

        models = ["Email", "Message", "Text", "Call", "Meeting"]
        model_type = params.get("model_type")
        people = params.get("people")
        firm = params.get("firm")

        surveillance_comm_query_params = {"name": ""}

        if firm:
            surveillance_comm_query_params["firm"] = firm
        if model_type:
            models = model_type
        if people:
            people = [f"'{x}'" for x in people]
            surveillance_comm_query_params["people"] = {
                "filter": "",
                "peopleFilter": f"participants.value.name in [{','.join(people)}]",
                "filterType": CommsQueryType.FLANG,
                "participantQueryType": ParticipantQueryType.INVOLVING,
            }

        behaviour_ids = []
        for lexica_backtest in lexica_backtests:
            if lexica_backtest.exclusion_terms is None:
                lexica_backtest.exclusion_terms = []

            lexica = {
                "category": "backtest",
                "excludedTerms": [
                    ExcludedLexicon(**{"term": e}) for e in lexica_backtest.exclusion_terms
                ],
                "excludedTermsOperator": lexica_backtest.exclusion_search_type,
                "fuzziness": lexica_backtest.term_operators_fuzziness,
                "name": "",
                "searchType": None,
                "slop": lexica_backtest.term_operators_proximity,
                "stemmed": lexica_backtest.term_operators_stem,
                "subCategory": None,
                "term": lexica_backtest.term,
                "language": lexica_backtest.language,
            }
            behaviour_ids.append(lexica_backtest.behaviour_id)
            lexical.append(SurveillanceLexicon(**lexica))
        surveillance_comm_query_params["lexica"] = lexical
        surveillance_comm_query = SurveillanceCommsQuery(**surveillance_comm_query_params)

        if any(behaviour_ids):
            custom_lexical = []
            for index, lexica in enumerate(lexical):
                lexica = lexica.dict(exclude_none=True)
                if behaviour_ids[index]:
                    lexica["behaviour_id"] = behaviour_ids[index]
                custom_lexical.append(lexica)

            surveillance_comm_query.lexica = custom_lexical

        builder = CommsQueryBuilder(
            query=surveillance_comm_query.dict(exclude_none=True),
            models=models,
            as_of=params.get("backtest_period", None),
        )
        return builder.build_query(
            csurv2=True,
            include_highlights=include_highlights,
            highlight_fields=(
                (highlight_fields or BaseCommsLexicaRepository.HIGH_LIGHT_FIELDS)
                if include_highlights
                else None
            ),
        ).to_dict()

    @staticmethod
    def replacement_method(value: str, key: str = ""):
        for field in BaseCommsLexicaRepository.FILTER_FIELDS:
            if isinstance(value, str) and value.startswith(field) and key in ("path", "fields"):
                value = value.replace(field, f"hit.{field}")
                return key, value

            if key.startswith(field):
                key = key.replace(field, f"hit.{field}")
                return key, value

        # Special Replacement for timestamps field in scripts
        if isinstance(value, str) and "timestamps." in value and key == "source":
            value = value.replace("timestamps.", "hit.timestamps.")

        return key, value

    @staticmethod
    def get_comms_alerts_backtest_query_dict(include_highlights: bool = False, **params) -> dict:
        hit_models = params.pop("model_type", None)
        start = params.pop("start", None)
        end = params.pop("end", None)
        backtest_period = params.pop("backtest_period", None)
        query = BaseCommsLexicaRepository.get_backtest_query_dict(
            include_highlights=include_highlights,
            model_type=["CommunicationAlert"],
            highlight_fields=[
                f"hit.{field}" for field in BaseCommsLexicaRepository.HIGH_LIGHT_FIELDS
            ],
            **params,
        )

        # breadth-first traversal to modify query keys to align with the CommunicationAlert model
        queue = deque([("", query)])
        while queue:
            parent, node = queue.popleft()

            if isinstance(node, dict):
                new_children = {}
                for child_name, grandchild in node.items():
                    new_key, new_value = BaseCommsLexicaRepository.replacement_method(
                        grandchild, child_name
                    )
                    new_children[new_key] = new_value
                    queue.append((new_key, new_value))
                node.clear()
                node.update(new_children)

            elif isinstance(node, list):
                for idx, grandchild in enumerate(node):
                    _, new_value = BaseCommsLexicaRepository.replacement_method(grandchild, parent)
                    node[idx] = new_value
                    queue.append((parent, new_value))

        if hit_models:
            query["query"] = BaseCommsLexicaRepository.add_term_filter(
                query=query["query"], filter=TermFilter(name="hitModel", value=hit_models)
            )

        if start or end:
            query["query"] = BaseCommsLexicaRepository.add_range_query(
                query["query"], start, end, field="hit.timestamps.timestampStart"
            )

        if backtest_period:
            query["query"] = BaseCommsLexicaRepository.add_range_query(
                query["query"], start=backtest_period, end=None, field="detected"
            )

        return query

    @staticmethod
    def get_term_type(item):
        bucket = glom(item, "TERM_TYPE.buckets")

        for x in bucket:
            if x.get("key") == "COLLOCATED_TERM":
                return "COLLOCATED_TERM"

        return "PHRASE"

    @staticmethod
    def transform_to_term_base(terms):
        terms_base = {}

        for term_id in terms:
            term_base = glom(terms, f"{term_id}.term_base")

            if term_base not in terms_base:
                terms_base[term_base] = []

            terms_base[term_base].append(terms[term_id])

        return terms_base

    @staticmethod
    def transform_to_term(query_result):
        terms = {}

        for item in query_result:
            terms[item["key"]] = glom(item, "TERM_COUNT.value")

            # buckets = glom(item, "TERM_.buckets")
            # for i in buckets:
            #     terms[i["key"]] = i["doc_count"]

        return terms

    @abc.abstractmethod
    async def get_word_cloud(
        self, lexica_search: SurveillanceBehaviourQuery, **params
    ) -> List[BasetermHit]:
        raise NotImplementedError()

    @abc.abstractmethod
    async def get_hit_by_term(
        self,
        lexica_search: SurveillanceBehaviourQuery,
        lexica_service: LexicaService,
        term_type: Optional[str] = None,
        with_hits: bool = True,
        **params,
    ) -> List[TermHit]:
        raise NotImplementedError()

    @abc.abstractmethod
    async def get_summary_by_trend(
        self,
        lexica_search: SurveillanceBehaviourQuery,
        start: Optional[datetime] = None,
        end: Optional[datetime] = None,
        sub_behaviour=None,
        **params,
    ) -> TrendSummary:
        raise NotImplementedError()

    @abc.abstractmethod
    async def get_backtest_summary(self, **params):
        raise NotImplementedError()

    @abc.abstractmethod
    async def get_backtest_alerts(self, **params):
        raise NotImplementedError()

    @abc.abstractmethod
    async def get_backtest_summary_by_trend(self, **params) -> List[str]:
        raise NotImplementedError()

    @abc.abstractmethod
    async def get_backtest_summary_by_time(self, **params):
        raise NotImplementedError()

    @abc.abstractmethod
    async def get_back_test_hit_summary(self, **params):
        raise NotImplementedError()

    async def backtest(self, watch: Watch, **params):
        raise NotImplementedError()

    async def get_sub_behaviour(self, **params):
        raise NotImplementedError()

    @staticmethod
    async def subscribed_languages(tenant: str, languages: List[str], config: ApiServiceConfig):
        url = (
            f"{config.DATA_PLATFORM_CONFIG_API_URL}/api/v1.0/"
            f"data-platform-config/stacks/{config.STACK}/tenants/{tenant}/se_lexica_version"
        )
        async with httpx.AsyncClient() as client:
            resp = await client.get(url)
        subscribed_langs = resp.json().get("subscribed_translations", ["en"])
        languages = [lang for lang in languages if lang in subscribed_langs]
        return languages

    @staticmethod
    def build_term_base_list(
        translations: dict, number_of_days, total_hits_path="doc_count"
    ) -> list:
        result = []
        for translate in translations:
            term_paired = ", ".join(
                [paired["key"] for paired in translate["TERM_PAIRED"]["buckets"]]
            )
            result.append(
                TermHit(
                    termBase=translate["key"],
                    termId=(
                        translate["TERM_ID"]["buckets"][0]["key"]
                        if translate["TERM_ID"]["buckets"]
                        else None
                    ),
                    hits=nested_dict_get(translate, total_hits_path),
                    hitPerDay=math.ceil(
                        nested_dict_get(translate, total_hits_path) / number_of_days
                    ),
                    termCount=translate["distinct_terms"]["value"],
                    termPaired=term_paired,
                    matchedLexica=translate["distinct_terms"]["value"],
                    language=", ".join([lang["key"] for lang in translate["LANGUAGE"]["buckets"]]),
                ).dict()
            )

        return result


class CommsLexicaRepository(RequestBoundRepository, BaseCommsLexicaRepository):
    async def get_word_cloud(
        self,
        lexica_search: SurveillanceBehaviourQuery,
        group_by: Optional[WordCloudGroupByIn] = None,
        term_id: Optional[List[str]] = None,
        term_base: Optional[List[str]] = None,
        term_type: Optional[str] = "COLLOCATED_TERM",
        f: Optional[str] = None,
        **params,
    ) -> List[BasetermHit]:
        record_models = [Email, Message, Text, Call, Meeting]
        resp = []

        if group_by == WordCloudGroupByIn.BASE_TERM:
            base_term_aggs_result = await self.get_aggs(
                record_model=record_models,
                search_model_cls=CommsSurveillanceHitByBaseTermAggs,
                watch=Watch.from_query(lexica_search, WatchQueryType.COMMUNICATIONS),
                behaviour_id=lexica_search.lexicaBehaviour.id,
                term_id=term_id,
                term_type="COLLOCATED_TERM",
                **params,
            )
            for item in base_term_aggs_result.aggregations["WATCH_FILTER"]["NESTED"]["FILTER"][
                "TERM"
            ]["buckets"]:
                resp.append(BasetermHit(baseTerm=item["key"], hits=item["doc_count"]))

        elif group_by == WordCloudGroupByIn.TERM:
            full_term_aggs_result = await self.get_aggs(
                record_model=record_models,
                search_model_cls=CommsSurveillanceHitByTermAggs,
                watch=Watch.from_query(lexica_search, WatchQueryType.COMMUNICATIONS),
                behaviour_id=lexica_search.lexicaBehaviour.id,
                term_base=term_base,
                term_type="COLLOCATED_TERM",
                **params,
            )

            for item in full_term_aggs_result.aggregations["WATCH_FILTER"]["NESTED"]["FILTER"][
                "TERM"
            ]["buckets"]:
                resp.append(
                    BasetermHit(
                        termId=item["key"],
                        baseTerm=item["TERM"]["buckets"][0]["key"],
                        hits=item["doc_count"],
                    )
                )

        return resp

    async def get_sub_behaviour(
        self,
        sub_behaviour_id: List[str],
        **params,
    ) -> List[SubBehaviourHit]:
        record_models = [Email, Message, Text, Call, Meeting]

        aggs_result = await self.get_aggs(
            record_model=record_models,
            search_model_cls=CommsSurveillanceSubBehaviourAggs,
            pagination=Pagination(take=0),
            sub_ehaviour_id=sub_behaviour_id,
            **params,
        )

        try:
            number_of_days = self.get_date_diff(aggs_result)
        except GlomError:
            # This reall shouldn't happen, but in case it does, fake the number of days.
            number_of_days = 365

        result = []
        for item in aggs_result.iter_raw_bucket_agg("NESTED.FILTER.SUB_BEHAVIOUR"):
            result.append(
                SubBehaviourHit(
                    sub_behaviour_id=item["ID"]["buckets"][0]["key"],
                    sub_behaviour=item["key"],
                    hits=item["doc_count"],
                    hitPerDay=math.ceil(item["doc_count"] / number_of_days),
                )
            )
        return result

    async def get_hit_by_term(
        self,
        lexica_search: SurveillanceBehaviourQuery,
        lexica_service: LexicaService,
        term_type: Optional[str] = None,
        with_hits: bool = True,
        **params,
    ) -> HitsByTerm:
        record_models = [Email, Message, Text, Call, Meeting]

        if params.get("sub_behaviour") and isinstance(params["sub_behaviour"], list):
            params["sub_behaviours"] = params.pop("sub_behaviour")

        if term_type == TermTypeIn.BASE_TERM.value:
            aggs_result = await self.get_aggs(
                record_model=record_models,
                search_model_cls=CommsSurveillanceHitByBaseTermAggs,
                term_type=AnalyticsTermTypeEnum.COLLOCATED_TERM.value,
                **params,
            )

            try:
                number_of_days = self.get_date_diff(aggs_result)
            except GlomError:
                number_of_days = 365

            query_result = aggs_result.aggregations["WATCH_FILTER"]["NESTED"]["FILTER"]["TERM"][
                "buckets"
            ]

            result = self.build_term_base_list(query_result, number_of_days)

        else:
            aggs_result = await self.get_aggs(
                record_model=record_models,
                search_model_cls=CommsSurveillanceHitByTermAggs,
                term_type=(
                    term_type
                    if term_type == AnalyticsTermTypeEnum.PHRASE.value
                    else AnalyticsTermTypeEnum.COLLOCATED_TERM.value
                ),
                **params,
            )

            try:
                number_of_days = self.get_date_diff(aggs_result)
            except GlomError:
                number_of_days = 365

            result = []
            for term in aggs_result.aggregations["WATCH_FILTER"]["NESTED"]["FILTER"]["TERM"][
                "buckets"
            ]:
                try:
                    term_paired = term["TERM_PAIRED"]["buckets"][0]["key"]
                except (KeyError, IndexError):
                    term_paired = None
                result.append(
                    {
                        "hits": term["doc_count"],
                        "hitPerDay": math.ceil(term["doc_count"] / number_of_days),
                        "termCount": 1,
                        "matchedLexica": 1,
                        "term": term["TERM"]["buckets"][0]["key"],
                        "termBase": (
                            term["TERM_BASE"]["buckets"][0]["key"]
                            if term["TERM_BASE"]["buckets"]
                            else None
                        ),
                        "termPaired": term_paired,
                        # "term_paired": term["TERM_PAIRED"]["buckets"][0]["key"]
                        # if term_type == TermTypeIn.FULL_TERM.value
                        # else None,
                        "termId": term["key"],
                        "language": term["LANGUAGE"]["buckets"][0]["key"],
                    }
                )

        # Do first the sorting and then pagination.
        # We can't perform these in ES, since the result depends on iteration
        if params["pagination"].sorts and len(result) >= 1:
            df = pd.DataFrame(result)
            sorts = params["pagination"].sorts
            try:
                df = df.sort_values(
                    by=[x.field for x in params["pagination"].sorts],
                    ascending=[
                        True if x.order == "asc" else False for x in params["pagination"].sorts
                    ],
                    key=(lambda_func_map.get(sorts[0].field) if len(sorts) == 1 else None),
                )
            except KeyError:
                pass
            result = df.to_dict(orient="records")

        start = params["pagination"].skip
        result_len = len(result)
        if start >= result_len:
            start = result_len
        end = start + params["pagination"].take
        if end >= result_len:
            end = result_len
        result = result[start:end]

        return HitsByTerm(
            header=HitsByTermHeader(
                returnedHits=len(result), skippedHits=start, totalHits=result_len
            ),
            results=result,
        )

    async def get_summary_by_trend(
        self,
        lexica_search: SurveillanceBehaviourQuery,
        start: Optional[datetime] = None,
        end: Optional[datetime] = None,
        sub_behaviour_id=None,
        local_parts_exclude=None,
        local_parts_include=None,
        **params,
    ) -> TrendSummary:
        if hasattr(lexica_search.lexicaBehaviour, "id"):
            del lexica_search.lexicaBehaviour.id

        watch = Watch.from_query(lexica_search, WatchQueryType.COMMUNICATIONS)
        if not start:
            start = await self.get_min_value(
                search_model_cls=CommsSurveillanceSearchBase,
                field="timestamps.timestampStart",
                # as_string=True,
                end=end,
                watch=watch,
                sub_behaviour_id=sub_behaviour_id,
                local_parts_exclude=local_parts_exclude,
                local_parts_include=local_parts_include,
                languages=params["languages"],
                pagination=Pagination(take=0),
            )
        if not end:
            end = datetime.utcnow().replace(tzinfo=timezone.utc)
        if not start:
            start = datetime.utcnow().replace(tzinfo=timezone.utc)
        if isinstance(start, float):
            # convert start to datetime
            start = datetime.fromtimestamp(start / 1000, tz=timezone.utc)

        search_model = CommsSurveillanceSummaryBySourceAggs(
            watch=watch,
            start=start,
            end=end,
            sub_behaviour_id=sub_behaviour_id,
            local_parts_exclude=local_parts_exclude,
            local_parts_include=local_parts_include,
            **params,
        )

        aggs_result = await self.get_aggs(
            search_model=search_model,
            pagination=Pagination(take=0),
        )
        result = []

        for item in aggs_result.iter_raw_bucket_agg("TIMELINE"):
            temp = {
                "timestamp": item["key_as_string"],
            }
            for source in glom(item, "SOURCE.buckets", default=[]):
                temp[source["key"]] = source["doc_count"]
            result.append(temp)

        start_dt = parse_datetime(start) if start else None
        end_dt = parse_datetime(end) if end else None

        return TrendSummary(
            start=start_dt.isoformat() if start_dt else None,
            end=end_dt.isoformat() if end_dt else None,
            intervalInMillis=search_model.interval_in_millis,
            buckets=result,
        )

    async def get_bespoke_backtest_summary(
        self, lexica_search: SurveillanceBehaviourQuery, **params
    ) -> BespokeBacktestSummary:
        record_models = [Email, Message, Text, Call, Meeting]

        bespoke_agg_result = await self.get_aggs(
            record_model=record_models,
            search_model_cls=CommsSurveillancebespokeAggs,
            watch=Watch.from_query(lexica_search, WatchQueryType.COMMUNICATIONS),
            **params,
        )
        total_hit = bespoke_agg_result.aggregations["aggs"]["doc_count"]
        number_of_days = self.get_date_diff(bespoke_agg_result)

        total_comms_count_resp: RawResult = await self.get_many(
            search_model_cls=CommsSurveillanceSearchBase,
            pagination=Pagination(take=0),
        )

        total_comms_count: int = (
            total_comms_count_resp.hits.total if total_comms_count_resp.hits.total > 0 else 1
        )
        response = BespokeBacktestSummary(
            totalHits=total_hit,
            percentageOfComms=round(total_hit / total_comms_count, 4),
            hitPerDay=math.ceil(total_hit / number_of_days),
        )
        return response

    @staticmethod
    def compute_random_sampling_prediction(
        sample_percentage, sample_absolute, agg_result, schedule_details
    ):
        response = RandomSamplingPrediction()

        if not sample_percentage:
            return response

        max_allowed = sample_absolute or float("inf")
        total_hit = agg_result.aggregations["aggs"]["doc_count"]
        datetime_diff = CommsLexicaRepository.get_date_diff(
            agg_result, return_delta=True, include_time=True
        )
        # Round the datetime difference to the nearest day
        number_of_days = pd.Timedelta(f"{datetime_diff}").round(freq="D").days or 1
        avg_hit_per_day = min(
            math.ceil((total_hit / number_of_days) * (sample_percentage / 100)), max_allowed
        )

        if schedule_details.recurrence == "DAILY":
            total_number_of_selected_days = len(schedule_details.daysOfWeek.split(","))

            response = RandomSamplingPrediction(
                totalHits=total_hit,
                averageHitPerDay=avg_hit_per_day,
                averageHitPerWeek=avg_hit_per_day * total_number_of_selected_days,
            )

        if schedule_details.recurrence == "MONTHLY":
            response = RandomSamplingPrediction(
                totalHits=total_hit,
                averageHitPerDay=avg_hit_per_day,
                averageHitPerMonth=math.ceil(avg_hit_per_day * (365 / 12)),
            )

        return response

    async def get_random_sampling_prediction(
        self,
        lexica_search: SurveillanceBehaviourQuery,
        schedule_details: WatchScheduleDetails,
        **params,
    ):
        record_models = [Email, Message, Text, Call, Meeting]

        random_sampling_agg_result = await self.get_aggs(
            record_model=record_models,
            search_model_cls=CommsSurveillancebespokeAggs,
            watch=Watch.from_query(lexica_search, WatchQueryType.COMMUNICATIONS),
            **params,
        )

        sample_percentage = lexica_search.template.thresholds.samplePercentage
        return self.compute_random_sampling_prediction(
            sample_percentage=sample_percentage,
            sample_absolute=lexica_search.template.thresholds.sampleSize,
            agg_result=random_sampling_agg_result,
            schedule_details=schedule_details,
        )

    async def get_backtest_summary(
        self, lexica_search: SurveillanceBehaviourQuery, **params
    ) -> BacktestSummary:
        record_models = [Email, Message, Text, Call, Meeting]
        lexica_summary = LexicaSummary()

        # to avoid watch schema error
        lexica_id = lexica_search.lexicaBehaviour.id
        del lexica_search.lexicaBehaviour.id

        phrase_aggs_result = await self.get_aggs(
            record_model=record_models,
            search_model_cls=CommsSurveillanceHitByTermAggs,
            watch=Watch.from_query(lexica_search, WatchQueryType.COMMUNICATIONS),
            behaviour_id=lexica_id,
            term_type="PHRASE",
            **params,
        )
        lexica_summary.PHRASE = len(
            phrase_aggs_result.aggregations["WATCH_FILTER"]["NESTED"]["FILTER"]["TERM"]["buckets"]
        )

        full_term_aggs_result = await self.get_aggs(
            record_model=record_models,
            search_model_cls=CommsSurveillanceHitByTermAggs,
            watch=Watch.from_query(lexica_search, WatchQueryType.COMMUNICATIONS),
            behaviour_id=lexica_id,
            term_type="COLLOCATED_TERM",
            **params,
        )
        lexica_summary.FULL_TERM = len(
            full_term_aggs_result.aggregations["WATCH_FILTER"]["NESTED"]["FILTER"]["TERM"][
                "buckets"
            ]
        )

        base_term_aggs_result = await self.get_aggs(
            record_model=record_models,
            search_model_cls=CommsSurveillanceHitByBaseTermAggs,
            watch=Watch.from_query(lexica_search, WatchQueryType.COMMUNICATIONS),
            behaviour_id=lexica_id,
            term_type="COLLOCATED_TERM",
            **params,
        )
        lexica_summary.BASE_TERM = len(
            base_term_aggs_result.aggregations["WATCH_FILTER"]["NESTED"]["FILTER"]["TERM"][
                "buckets"
            ]
        )

        aggs_result = await self.get_aggs(
            search_model=CommsSurveillanceBackTestSummaryAggs(
                watch=Watch.from_query(lexica_search, WatchQueryType.COMMUNICATIONS),
                behaviour_id=lexica_id,
                **params,
            ),
            pagination=Pagination(take=0),
        )

        total_comms_count_resp: RawResult = await self.get_many(
            search_model_cls=CommsSurveillanceSearchBase,
            pagination=Pagination(take=0),
        )

        total_comms_count: int = (
            total_comms_count_resp.hits.total if total_comms_count_resp.hits.total > 0 else 1
        )
        number_of_days = self.get_date_diff(aggs_result)
        total_hit: int = glom(aggs_result.aggregations, "NESTED.FILTER.doc_count", default=0)

        sub_behaviours = []
        for item in aggs_result.iter_raw_bucket_agg("NESTED.FILTER.SUB_BEHAVIOURS"):
            sub_behaviours.append(
                BacktestSubCategory(
                    subBehaviourId=item["key"],
                    subBehaviour=item["SUB_BEHAVIOUR"]["buckets"][0]["key"],
                    hits=item["doc_count"],
                    percentageOfComms=round(item["doc_count"] / total_comms_count, 4),
                    hitPerDay=math.ceil(item["doc_count"] / number_of_days),
                )
            )
        response = BacktestSummary(
            lexicaSummary=lexica_summary,
            behaviour=Behaviour(
                behaviourId=lexica_id,
                behaviour=lexica_search.lexicaBehaviour.name,
                subBehaviours=sub_behaviours,
            ),
            totalHits=total_hit,
            percentageOfComms=round(total_hit / total_comms_count, 4),
            hitPerDay=math.ceil(total_hit / number_of_days),
        )
        return response

    async def get_backtest_alerts(self, **params):
        query = self.get_backtest_query_dict(include_highlights=True, **params)
        pagination: Pagination = params.get("pagination")

        query["from"] = pagination.skip
        query["size"] = pagination.take
        query["sort"] = (
            [sort.to_dict() for sort in pagination.sorts]
            if pagination.sorts
            else Sort(field="&timestamp").to_dict()
        )

        start = params.get("start")
        end = params.get("end")

        if start or end:
            query["query"] = self.add_range_query(query["query"], start, end)

        index = ",".join(self.index_for_record_model([Call, Email, Message, Text, Meeting]))
        result = await self.es_repo.search(query, index=index)
        raw_result = RawResult(**result)

        raw_result.hits.hits = [
            {
                **hit["_source"],
                "&highlight": hit.get("highlight"),
                "matchedLexicaCategories": hit.get("matched_queries") or [],
            }
            for hit in raw_result.hits.hits
        ]

        return raw_result

    async def get_backtest_summary_by_time(self, **params) -> TrendSummary:
        query = self.get_backtest_query_dict(**params)
        index = ",".join(self.index_for_record_model([Call, Email, Message, Text, Meeting]))

        query["size"] = 0

        start = params.get("start")
        end = params.get("end")

        if not start:
            start = await self.get_min_value(
                field="timestamps.timestampStart",
                as_string=True,
                where=[ModelFilter(model=[Call, Email, Message, Text, Meeting])],
                **params,
            )
        if not end:
            end = datetime.utcnow().replace(tzinfo=timezone.utc)

        interval_in_millis = get_histogram_interval(
            start=parse_datetime(start),
            end=parse_datetime(end),
            max_buckets=params.get("buckets"),
            min_interval=15 * 60 * 1000,
        )

        query["query"] = self.add_range_query(query["query"], start, end)

        query["aggs"] = {
            "TIMES": {
                "date_histogram": {
                    "field": "timestamps.timestampStart",
                    "fixed_interval": f"{interval_in_millis}ms",
                    "format": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
                },
            }
        }
        result = await self.es_repo.search(query, index=index)
        raw_result = RawResult(**result)

        start_dt = parse_datetime(start) if start else None
        end_dt = parse_datetime(end) if end else None

        return TrendSummary(
            start=start_dt.isoformat() if start_dt else None,
            end=end_dt.isoformat() if end_dt else None,
            intervalInMillis=interval_in_millis,
            buckets=[
                {
                    "timestamp": bucket["key"],
                    "datetime": bucket["key_as_string"],
                    "count": bucket["doc_count"],
                }
                for bucket in raw_result.iter_raw_bucket_agg("TIMES")
            ],
        )

    async def get_backtest_summary_by_trend(self, **params) -> List[dict[str, Any]]:
        query = self.get_backtest_query_dict(**params)
        index = ",".join(self.index_for_record_model([Call, Email, Message, Text, Meeting]))

        query["size"] = 0
        trend_chart = params.pop("trend_chart")

        field, nested_path = TREND_CHART_SPEC[trend_chart]
        if nested_path:
            aggs = {
                "NESTED": {
                    "nested": {"path": nested_path},
                    "aggs": {
                        "SUBJECTS": {
                            "terms": {"field": field, "size": ES_MAX_AGG_SIZE},
                            "aggs": {"COMMS_COUNT": {"reverse_nested": {}}},
                        }
                    },
                }
            }
        else:
            aggs = {"SUBJECTS": {"terms": {"field": field, "size": ES_MAX_AGG_SIZE}}}

        query["aggs"] = aggs

        start = params.get("start")
        end = params.get("end")

        if start or end:
            query["query"] = self.add_range_query(query["query"], start, end)

        result = await self.es_repo.search(query, index=index)
        aggs_result = RawResult(**result)

        return [
            {
                "key": item["key"],
                "count": item["COMMS_COUNT"]["doc_count"] if nested_path else item["doc_count"],
            }
            for item in aggs_result.iter_raw_bucket_agg(
                "NESTED.SUBJECTS" if nested_path else "SUBJECTS"
            )
        ][:5]  # to send aggregation for only 5 rows

    async def get_back_test_hit_summary(self, **params) -> BacktestHitsSummary:
        query = self.get_backtest_query_dict(**params)
        index = ",".join(self.index_for_record_model([Call, Email, Message, Text, Meeting]))

        query["size"] = 0
        query["aggs"] = {
            "start_date": {"min": {"field": "&timestamp"}},
            "end_date": {"max": {"field": "&timestamp"}},
        }

        start = params.get("start")
        end = params.get("end")

        if start or end:
            query["query"] = self.add_range_query(query["query"], start, end)

        result = await self.es_repo.search(query, index=index, track_total_hits=10_00_000)
        aggs_result = RawResult(**result)
        number_of_days = self.get_date_diff(aggs_result)
        number_of_weeks = number_of_days // 7 or 1
        total_hit = aggs_result.hits.total

        return BacktestHitsSummary(
            hits=total_hit,
            hitsPerWeek=math.ceil(total_hit / number_of_weeks),
            hitsPerDay=math.ceil(total_hit / number_of_days),
        )

    async def backtest(self, watch, **params) -> RawResult:
        return await self.get_many(
            search_model=CommsSurveillanceSearchBase(
                watch=watch,
                **params,
            ),
            hit_deserializer=lambda x: x["_source"],
        )

    async def get_terms_hit(
        self,
        term_id: List[str] = None,
    ) -> List[TermsHitById]:
        record_models = [Email, Message, Text, Call, Meeting]

        aggs_result = await self.get_aggs(
            record_model=record_models,
            search_model_cls=CommsSurveillanceTermsHitAggs,
            term_id=term_id,
        )

        try:
            number_of_days = self.get_date_diff(aggs_result)
        except GlomError:
            number_of_days = 365

        return [
            {
                "termId": term["key"],
                "hits": term["doc_count"],
                "hitPerDay": math.ceil(term["doc_count"] / number_of_days),
            }
            for term in aggs_result.aggregations["NESTED"]["FILTER"]["TERM"]["buckets"]
        ]


class CommsAlertsLexicaRepository(BaseCommsLexicaRepository, RequestBoundRepository):
    async def get_word_cloud(
        self,
        lexica_search: SurveillanceBehaviourQuery,
        group_by: Optional[WordCloudGroupByIn] = None,
        term_id: Optional[List[str]] = None,
        term_base: Optional[List[str]] = None,
        **params,
    ) -> List[BasetermHit]:
        record_models = [CommunicationAlert]
        resp = []

        if group_by == WordCloudGroupByIn.BASE_TERM:
            base_term_aggs_result = await self.get_aggs(
                record_model=record_models,
                search_model_cls=CommunicationAlertsHitByBaseTermAggs,
                behaviour_id=lexica_search.lexicaBehaviour.id,
                term_id=term_id,
                term_type="COLLOCATED_TERM",
                **params,
            )
            for item in base_term_aggs_result.aggregations["NESTED"]["FILTER"]["TERM"]["buckets"]:
                resp.append(
                    BasetermHit(
                        baseTerm=item["key"], hits=item["total_doc_count"]["total"]["value"]
                    )
                )

        elif group_by == WordCloudGroupByIn.TERM:
            full_term_aggs_result = await self.get_aggs(
                record_model=record_models,
                search_model_cls=CommunicationAlertsHitByTermAggs,
                behaviour_id=lexica_search.lexicaBehaviour.id,
                term_base=term_base,
                term_type="COLLOCATED_TERM",
                **params,
            )

            for item in full_term_aggs_result.aggregations["NESTED"]["FILTER"]["TERM"]["buckets"]:
                resp.append(
                    BasetermHit(
                        termId=item["key"],
                        baseTerm=item["TERM"]["buckets"][0]["key"],
                        hits=item["total_doc_count"]["total"]["value"],
                    )
                )

        return resp

    async def get_summary_by_trend(
        self,
        lexica_search: SurveillanceBehaviourQuery,
        start: Optional[datetime] = None,
        end: Optional[datetime] = None,
        sub_behaviour_id=None,
        local_parts_exclude=None,
        local_parts_include=None,
        **params,
    ) -> TrendSummary:
        record_models = [CommunicationAlert]
        if hasattr(lexica_search.lexicaBehaviour, "id"):
            del lexica_search.lexicaBehaviour.id

        if not start:
            start = await self.get_min_value(
                record_model=record_models,
                search_model_cls=CommunicationAlertSearch,
                field="hit.timestamps.timestampStart",
                end=end,
                sub_behaviour_id=sub_behaviour_id,
                local_parts_exclude=local_parts_exclude,
                local_parts_include=local_parts_include,
                languages=params["languages"],
                pagination=Pagination(take=0),
            )
        if not end:
            end = datetime.utcnow().replace(tzinfo=timezone.utc)
        if not start:
            start = datetime.utcnow().replace(tzinfo=timezone.utc)
        if isinstance(start, float):
            # convert start to datetime
            start = datetime.fromtimestamp(start / 1000, tz=timezone.utc)

        search_model = CommunicationAlertsSummaryBySourceAggs(
            start=start,
            end=end,
            sub_behaviour_id=sub_behaviour_id,
            local_parts_exclude=local_parts_exclude,
            local_parts_include=local_parts_include,
            **params,
        )

        aggs_result = await self.get_aggs(
            record_model=record_models,
            search_model=search_model,
            pagination=Pagination(take=0),
        )
        result = []

        for item in aggs_result.iter_raw_bucket_agg("TIMELINE"):
            temp = {
                "timestamp": item["key_as_string"],
            }
            for source in glom(item, "SOURCE.buckets", default=[]):
                temp[source["key"]] = source["doc_count"]
            result.append(temp)

        start_dt = parse_datetime(start) if start else None
        end_dt = parse_datetime(end) if end else None

        return TrendSummary(
            start=start_dt.isoformat() if start_dt else None,
            end=end_dt.isoformat() if end_dt else None,
            intervalInMillis=search_model.interval_in_millis,
            buckets=result,
        )

    async def get_backtest_summary(
        self, lexica_search: SurveillanceBehaviourQuery, **params
    ) -> BacktestSummary:
        record_models = [CommunicationAlert]
        lexica_summary = LexicaSummary()

        # to avoid watch schema error
        lexica_id = lexica_search.lexicaBehaviour.id
        del lexica_search.lexicaBehaviour.id

        phrase_aggs_result = await self.get_aggs(
            record_model=record_models,
            search_model_cls=CommunicationAlertsHitByTermAggs,
            behaviour_id=lexica_id,
            term_type="PHRASE",
            **params,
        )
        lexica_summary.PHRASE = len(
            phrase_aggs_result.aggregations["NESTED"]["FILTER"]["TERM"]["buckets"]
        )

        full_term_aggs_result = await self.get_aggs(
            record_model=record_models,
            search_model_cls=CommunicationAlertsHitByTermAggs,
            behaviour_id=lexica_id,
            term_type="COLLOCATED_TERM",
            **params,
        )
        lexica_summary.FULL_TERM = len(
            full_term_aggs_result.aggregations["NESTED"]["FILTER"]["TERM"]["buckets"]
        )

        base_term_aggs_result = await self.get_aggs(
            record_model=record_models,
            search_model_cls=CommunicationAlertsHitByBaseTermAggs,
            behaviour_id=lexica_id,
            term_type="COLLOCATED_TERM",
            **params,
        )
        lexica_summary.BASE_TERM = len(
            base_term_aggs_result.aggregations["NESTED"]["FILTER"]["TERM"]["buckets"]
        )

        aggs_result = await self.get_aggs(
            record_model=record_models,
            search_model=CommunicationAlertsBackTestSummaryAggs(
                behaviour_id=lexica_id,
                **params,
            ),
            pagination=Pagination(take=0),
        )

        alerted_comms_count_resp: RawResult = await self.get_aggs(
            record_model=record_models,
            search_model_cls=CommunicationAlertSearch,
            aggs={"ALERTED_COMMS_COUNT": {"cardinality": {"field": "hit.&id"}}},
        )

        total_alerted_comms_count: int = (
            alerted_comms_count_resp.aggregations["ALERTED_COMMS_COUNT"]["value"] or 1
        )
        number_of_days = self.get_date_diff(aggs_result)
        total_hit: int = glom(aggs_result.aggregations, "TOTAL.value", default=0)

        sub_behaviours = []
        for item in aggs_result.iter_raw_bucket_agg("NESTED.FILTER.SUB_BEHAVIOURS"):
            hits_count = item["HIT_COUNT"]["HIT_COUNT"]["value"]
            sub_behaviours.append(
                BacktestSubCategory(
                    subBehaviourId=item["key"],
                    subBehaviour=item["SUB_BEHAVIOUR"]["buckets"][0]["key"],
                    hits=hits_count,
                    percentageOfComms=round(hits_count / total_alerted_comms_count, 4),
                    hitPerDay=math.ceil(hits_count / number_of_days),
                )
            )
        response = BacktestSummary(
            lexicaSummary=lexica_summary,
            behaviour=Behaviour(
                behaviourId=lexica_id,
                behaviour=lexica_search.lexicaBehaviour.name,
                subBehaviours=sub_behaviours,
            ),
            totalHits=total_hit,
            percentageOfComms=round(total_hit / total_alerted_comms_count, 4),
            hitPerDay=math.ceil(total_hit / number_of_days),
        )
        return response

    async def get_hit_by_term(
        self,
        lexica_search: SurveillanceBehaviourQuery,
        lexica_service: LexicaService,
        term_type: Optional[str] = None,
        with_hits: bool = True,
        use_watch_filter: bool = False,
        **params,
    ) -> HitsByTerm:
        record_models = [CommunicationAlert]

        if params.get("sub_behaviour") and isinstance(params["sub_behaviour"], list):
            params["sub_behaviours"] = params.pop("sub_behaviour")

        params.pop("query_in", None)

        if term_type == TermTypeIn.BASE_TERM.value:
            aggs_result = await self.get_aggs(
                record_model=record_models,
                search_model_cls=CommunicationAlertsHitByBaseTermAggs,
                term_type=AnalyticsTermTypeEnum.COLLOCATED_TERM.value,
                **params,
            )

            try:
                number_of_days = self.get_date_diff(aggs_result)
            except GlomError:
                number_of_days = 365

            query_result = aggs_result.aggregations["NESTED"]["FILTER"]["TERM"]["buckets"]

            result = self.build_term_base_list(
                query_result, number_of_days, total_hits_path="total_doc_count.total.value"
            )

        else:
            aggs_result = await self.get_aggs(
                record_model=record_models,
                search_model_cls=CommunicationAlertsHitByTermAggs,
                term_type=(
                    term_type
                    if term_type == AnalyticsTermTypeEnum.PHRASE.value
                    else AnalyticsTermTypeEnum.COLLOCATED_TERM.value
                ),
                **params,
            )

            try:
                number_of_days = self.get_date_diff(aggs_result)
            except GlomError:
                number_of_days = 365

            result = []
            for term in aggs_result.aggregations["NESTED"]["FILTER"]["TERM"]["buckets"]:
                try:
                    term_paired = term["TERM_PAIRED"]["buckets"][0]["key"]
                except (KeyError, IndexError):
                    term_paired = None
                result.append(
                    {
                        "hits": term["total_doc_count"]["total"]["value"],
                        "hitPerDay": math.ceil(
                            term["total_doc_count"]["total"]["value"] / number_of_days
                        ),
                        "termCount": 1,
                        "matchedLexica": 1,
                        "term": term["TERM"]["buckets"][0]["key"],
                        "termBase": (
                            term["TERM_BASE"]["buckets"][0]["key"]
                            if term["TERM_BASE"]["buckets"]
                            else None
                        ),
                        "termPaired": term_paired,
                        "termId": term["key"],
                        "language": term["LANGUAGE"]["buckets"][0]["key"],
                    }
                )

        # Do first the sorting and then pagination.
        # We can't perform these in ES, since the result depends on iteration
        if params["pagination"].sorts and len(result) >= 1:
            df = pd.DataFrame(result)
            sorts = params["pagination"].sorts
            try:
                df = df.sort_values(
                    by=[x.field for x in params["pagination"].sorts],
                    ascending=[
                        True if x.order == "asc" else False for x in params["pagination"].sorts
                    ],
                    key=(lambda_func_map.get(sorts[0].field) if len(sorts) == 1 else None),
                )
            except KeyError:
                pass
            result = df.to_dict(orient="records")

        start = params["pagination"].skip
        result_len = len(result)
        if start >= result_len:
            start = result_len
        end = start + params["pagination"].take
        if end >= result_len:
            end = result_len
        result = result[start:end]

        return HitsByTerm(
            header=HitsByTermHeader(
                returnedHits=len(result), skippedHits=start, totalHits=result_len
            ),
            results=result,
        )

    async def get_backtest_alerts(self, **params):
        pagination: Pagination = params.get("pagination")
        backtest_period = params.pop("backtest_period", None)
        # get comms ids from the alerts
        query = self.get_comms_alerts_backtest_query_dict(**params, backtest_period=backtest_period)
        query["size"] = 0
        query["aggs"] = {
            "COMMS_IDS": {
                "terms": {
                    "field": "hit.&id",
                    "size": ES_MAX_AGG_SIZE,
                }
            }
        }

        index = self.index_for_record_model([CommunicationAlert])
        result = await self.es_repo.search(query, index=index)
        raw_result = RawResult(**result)

        agg_buckets = list(raw_result.iter_raw_bucket_agg("COMMS_IDS"))[
            pagination.skip : pagination.take
        ]
        comms_ids = [item["key"] for item in agg_buckets]

        # run query on comms
        comms_query = self.get_backtest_query_dict(include_highlights=True, **params)
        comms_query["query"] = self.add_term_filter(
            query=comms_query["query"], filter=TermFilter(name="&id", value=comms_ids)
        )

        comms_indices = ",".join(self.index_for_record_model([Call, Email, Message, Text, Meeting]))
        result = await self.es_repo.search(comms_query, index=comms_indices)
        comms_raw_result = RawResult(**result)

        comms_raw_result.hits.total = len(raw_result.aggregations["COMMS_IDS"]["buckets"])
        comms_raw_result.hits.hits = [
            {
                **hit["_source"],
                "&highlight": hit.get("highlight"),
                "matchedLexicaCategories": hit.get("matched_queries") or [],
            }
            for hit in comms_raw_result.hits.hits
        ]

        return comms_raw_result

    async def get_backtest_summary_by_time(self, **params) -> TrendSummary:
        start = params.pop("start", None)
        end = params.pop("end", None)

        query = self.get_comms_alerts_backtest_query_dict(**params)
        index = self.index_for_record_model([CommunicationAlert])
        query["size"] = 0

        if not start:
            start = await self.get_min_value(
                field="hit.timestamps.timestampStart",
                as_string=True,
                where=[ModelFilter(model=[CommunicationAlert])],
                **params,
            )
        if not end:
            end = datetime.utcnow().replace(tzinfo=timezone.utc)

        interval_in_millis = get_histogram_interval(
            start=parse_datetime(start),
            end=parse_datetime(end),
            max_buckets=params.get("buckets"),
            min_interval=15 * 60 * 1000,
        )

        query["query"] = self.add_range_query(
            query["query"], start, end, field="hit.timestamps.timestampStart"
        )

        query["aggs"] = {
            "TIMES": {
                "date_histogram": {
                    "field": "hit.timestamps.timestampStart",
                    "fixed_interval": f"{interval_in_millis}ms",
                    "format": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
                },
                "aggs": {"COMMS_COUNT": {"cardinality": {"field": "hit.&id"}}},
            }
        }
        result = await self.es_repo.search(query, index=index)
        raw_result = RawResult(**result)

        start_dt = parse_datetime(start) if start else None
        end_dt = parse_datetime(end) if end else None

        return TrendSummary(
            start=start_dt.isoformat() if start_dt else None,
            end=end_dt.isoformat() if end_dt else None,
            intervalInMillis=interval_in_millis,
            buckets=[
                {
                    "timestamp": bucket["key"],
                    "datetime": bucket["key_as_string"],
                    "count": bucket["COMMS_COUNT"]["value"],
                }
                for bucket in raw_result.iter_raw_bucket_agg("TIMES")
            ],
        )

    async def get_backtest_summary_by_trend(self, **params) -> List[dict[str, Any]]:
        query = self.get_comms_alerts_backtest_query_dict(**params)
        index = ",".join(self.index_for_record_model([CommunicationAlert]))

        query["size"] = 0
        trend_chart = params.pop("trend_chart")

        if trend_chart == LexicaBacktestTrendChart.TYPE:
            field, nested_path = "hitModel", None
        else:
            field, nested_path = TREND_CHART_SPEC[trend_chart]
            field, nested_path = f"hit.{field}", f"hit.{nested_path}"

        if nested_path:
            aggs = {
                "NESTED": {
                    "nested": {"path": nested_path},
                    "aggs": {
                        "SUBJECTS": {
                            "terms": {"field": field, "size": ES_MAX_AGG_SIZE},
                            "aggs": {
                                "COMMS_COUNT": {
                                    "reverse_nested": {},
                                    "aggs": {"COMMS_COUNT": {"cardinality": {"field": "hit.&id"}}},
                                }
                            },
                        }
                    },
                }
            }
        else:
            aggs = {
                "SUBJECTS": {
                    "terms": {"field": field, "size": ES_MAX_AGG_SIZE},
                    "aggs": {"COMMS_COUNT": {"cardinality": {"field": "hit.&id"}}},
                }
            }

        query["aggs"] = aggs

        result = await self.es_repo.search(query, index=index)
        aggs_result = RawResult(**result)

        return [
            {
                "key": item["key"],
                "count": item["COMMS_COUNT"]["COMMS_COUNT"]["value"]
                if nested_path
                else item["COMMS_COUNT"]["value"],
            }
            for item in aggs_result.iter_raw_bucket_agg(
                "NESTED.SUBJECTS" if nested_path else "SUBJECTS"
            )
        ][:5]  # to send aggregation for only 5 rows

    async def get_back_test_hit_summary(self, **params) -> BacktestHitsSummary:
        query = self.get_comms_alerts_backtest_query_dict(**params)
        index = ",".join(self.index_for_record_model([CommunicationAlert]))

        query["size"] = 0
        query["aggs"] = {
            "start_date": {"min": {"field": "detected"}},
            "end_date": {"max": {"field": "detected"}},
            "total_comms": {"cardinality": {"field": "hit.&id"}},
        }

        result = await self.es_repo.search(query, index=index)
        aggs_result = RawResult(**result)
        number_of_days = self.get_date_diff(aggs_result)
        number_of_weeks = number_of_days // 7 or 1
        total_hit = glom(aggs_result.aggregations, "total_comms.value", default=0)

        return BacktestHitsSummary(
            hits=total_hit,
            hitsPerWeek=math.ceil(total_hit / number_of_weeks),
            hitsPerDay=math.ceil(total_hit / number_of_days),
        )

# type: ignore
# flake8: noqa
from api_sdk.es_dsl.lexicon_features import LexiconSearchFilter
from api_sdk.es_dsl.base import (
    ModelFilter,
    NotExpired,
    Or,
    QueryString,
    SearchBase,
    SearchFeature,
    SearchFeatureConfig,
    SearchModel,
    Sort,
    TermFilter,
)
from se_api_svc.models.results import AggregationResult

import math
import datetime as dt
import time
from collections import defaultdict
from dataclasses import dataclass
from functools import cached_property
from typing import Any, Dict, Generator, List, Optional, Set, Tuple, Type, Union
from api_sdk.models.search import Pagination, SearchResult, SearchResultHeader
from api_sdk.utils.utils import (
    StringEnum,
    decode_search_after,
    encode_search_after,
    nested_get,
    parse_datetime,
)
from se_api_svc.schemas.comms import Call, ChatEvent, Email, Message, Text, TranscriptRecordModel

from se_elastic_schema.models.tenant.communication.transcript import Transcript
from se_api_svc.repository.market.market import MarketCounterparty, MarketCounterpartySearchName
from api_sdk.schemas.base import APIModel, Field, RecordModel

from se_elastic_schema.models.tenant.communication.call import Call as CallSchema
from se_elastic_schema.models.tenant.communication.email import Email as EmailSchema
from se_elastic_schema.models.tenant.communication.message import Message as MessageSchema
from se_elastic_schema.models.tenant.communication.text import Text as TextSchema
from se_elastic_schema.models.tenant.communication.meeting import Meeting as MeetingSchema

from se_api_svc.core.constants import ES_MAX_AGG_SIZE
from se_api_svc.schemas.comms.common import (
    CommsParticipantType,
    CommsTrendChart,
    CommsTrends,
    DetailResponseSizing,
    GetCommsTimeIn,
    GroupDetails,
    TimeOperator,
)
from enum import auto
from api_sdk.es_dsl.features import Nested, RangeFilter, SearchAfter
from api_sdk.es_dsl.flang import FlangFilter
from api_sdk.es_dsl.params import SearchModelParams
from api_sdk.full_text_search import (
    COMMS_TEXT_NESTED_NON_NESTED_MAPPING,
    get_free_text_search_filters,
)
from api_sdk.models.elasticsearch import RawResult
from api_sdk.repository.asyncronous.request_bound import RepoHelpersMixin
from elasticsearch_dsl import Q
from se_api_svc.repository.comms.participants import ParticipantsSearchBase
from se_api_svc.repository.comms_surveillance.common import CustomClassProperty
from se_api_svc.schemas.comms import Call, Email, Message, Text
from se_api_svc.schemas.comms.common import DetailResponseSizing, GetCommsTimeIn, TimeOperator
from se_api_svc.schemas.comms.meeting import Meeting
from se_api_svc.schemas.order import Order
from api_sdk.es_dsl.utils import sanitize_es_reserved_characters
from datetime import timezone
from api_sdk.utils.intervals import get_interval_in_millis_from_params
from api_sdk.schemas.surveillance.lexica import LexiconSearchFilterIn

_MESSAGE_DAY_SCRIPT = """
    def ts = doc['timestamps.timestampStart'].value;
    def localTs = LocalDateTime.ofInstant(Instant.ofEpochMilli(ts), ZoneId.of('Z'));
    return localTs.format(DateTimeFormatter.ISO_DATE);
"""
_NUM_OF_PARTICIPANTS_BASE_SCRIPT = """
    def ids = params['_source']?.identifiers;
    if (ids == null) { return 0; }
    int numParticipants = 0;
    if (ids?.fromId != null) { numParticipants += 1; }
    if (ids?.bccIds != null) { numParticipants += ids.bccIds.size(); }
    if (ids?.ccIds != null) { numParticipants += ids.ccIds.size(); }
    if (ids?.toIds != null) { numParticipants += ids.toIds.size(); }
"""

_NUM_OF_PARTICIPANTS_FILTER_SCRIPT = (
    _NUM_OF_PARTICIPANTS_BASE_SCRIPT
    + """
    return params.counts.contains(numParticipants);
"""
)

_NUM_OF_PARTICIPANTS_SCRIPTED_AGG = (
    _NUM_OF_PARTICIPANTS_BASE_SCRIPT
    + """
    return numParticipants;
"""
)

COMMS_TREND_CHART_SPEC = {
    CommsTrendChart.COMMUNICATORS: ("participants.value.name", "participants", False),
    CommsTrendChart.COUNTERPARTIES: ("participants.value.counterparty.name", "participants", False),
    CommsTrendChart.NUM_OF_PARTICIPANTS: (False, False, _NUM_OF_PARTICIPANTS_SCRIPTED_AGG),
    CommsTrendChart.PARTICIPANT_TYPE: ("participants.types", "participants", False),
    CommsTrendChart.SENDERS: ("identifiers.fromId", False, False),
    CommsTrendChart.SOURCE: ("metadata.source.client", False, False),
    CommsTrendChart.TYPE: ("&model", False, False),
}

TRENDS_AGGREGATIONS = {
    # field, nested path, script, analyser
    CommsTrends.DATA_SOURCE: ("metadata.source.client", None, None, ".text"),
    CommsTrends.ANALYTICS_SOURCE: ("analytics.lexica.source", "analytics.lexica", None, ""),
    CommsTrends.MODEL: ("&model", None, None, ""),
    CommsTrends.HOUR: (
        None,
        None,
        "if(doc['timestamps.timestampStart'].size() != 0) {LocalDateTime.ofInstant(Instant.ofEpochMilli(doc['timestamps.timestampStart'].value.millis), ZoneId.of('Z')).getHour()}",
        "",
    ),
    CommsTrends.WEEK: (
        None,
        None,
        "if (doc['timestamps.timestampStart'].size() > 0) {return LocalDateTime.ofInstant(Instant.ofEpochMilli(doc['timestamps.timestampStart'].value.millis), ZoneId.of('Z')).getDayOfWeek().getDisplayName(TextStyle.FULL, Locale.ENGLISH)}",
        "",
    ),
    CommsTrends.LABELS: ("labels.labels", None, None, ""),
    CommsTrends.CUSTOM_LABELS: ("labels.labelsCustom", None, None, ""),
}

SLIM_MESSAGE_FIELDS = [
    "&id",
    "&key",
    "&model",
    "body.text",
    "participants.types*",
    "participants.value.&*",
    "participants.value.name",
    "subject",
    "timestamps.timestampStart",
]


class NumberOfParticipantsFilter(SearchFeature):
    @dataclass
    class Config(SearchFeatureConfig):
        number_of_participants: Optional[List[int]] = None

    config: Config

    def build_q(self, params=None, *, meta_fields):
        if not params.number_of_participants:
            return

        script = {
            "script": {
                "lang": "painless",
                "params": {"counts": params.number_of_participants},
                "inline": _NUM_OF_PARTICIPANTS_FILTER_SCRIPT,
            }
        }

        return Q("script", **script)


class ModelFilterNoQs(ModelFilter):
    """A version of ModelFilter that doesn't inject its own model_qs search
    params."""

    class Config(ModelFilter.Config):
        model_qs = None
        model_qs_param = None

    def build_q(self, params=None, *, meta_fields):
        p = params.copy()
        p.model_qs = None
        return super().build_q(p, meta_fields=meta_fields)


@SearchFeature.register_q_builder
def BeforeMessageFilter(params):
    if params.before_message_timestamp:
        return Q(
            "range",
            **{"timestamps.timestampStart": {"lt": params.before_message_timestamp}},
        )


class TimeOfDayFilter(SearchFeature):
    @dataclass
    class Config(SearchFeatureConfig):
        time_of_day: Optional[GetCommsTimeIn] = None
        param: Optional[str] = "time_of_day"

    config: Config

    def build_q(self, params=None, *, meta_fields):
        time_of_day = getattr(params, self.config.param, None) or self.config.time_of_day
        if not time_of_day:
            return
        time_script = """
                def time = LocalDateTime.ofInstant(Instant.ofEpochMilli(doc['timestamps.timestampStart'].value), ZoneId.of('Z'));
                def hour = time.getHour();
                def minute = time.getMinute();
                def second = time.getSecond();
                def timeInMillis = ((hour * 60 * 60) + (minute * 60) + (second)) * 1000;
                if (params.inside) {
                    return params.timeStart <= timeInMillis && timeInMillis <= params.timeEnd;
                } else {
                    return params.timeStart > timeInMillis || timeInMillis > params.timeEnd;
                }
        """

        script = {
            "script": {
                "source": time_script,
                "params": {
                    "inside": time_of_day.operator == TimeOperator.BETWEEN,
                    "timeEnd": time_of_day.end,
                    "timeStart": time_of_day.start,
                },
            }
        }

        return Q("script", **script)


class CommsSearchBase(SearchModel):
    class Params(SearchModelParams, ParticipantsSearchBase.Params):
        f: Optional[str]
        start: Union[dt.datetime, dt.date] = None
        ids: Optional[List[str]] = None
        end: Union[dt.datetime, dt.date] = None
        number_of_participants: Optional[List[int]] = None
        room_ids: Optional[List[str]] = None
        thread_ids: Optional[List[str]] = None
        room_day: Optional[dt.date] = None
        detail_sizing: Optional[DetailResponseSizing] = DetailResponseSizing.FULL
        search_after: List = None
        counterparties: Optional[List[str]] = None
        people: Optional[List[str]] = None
        search: Optional[str] = None
        time_of_day: Optional[GetCommsTimeIn] = None
        exclude_removable_fields: Optional[bool] = False
        lexica_search: Optional[List[LexiconSearchFilterIn]] = None

        before_message_timestamp: Optional[Any] = None

    params: Params

    @CustomClassProperty
    def features(cls, instance):
        return [
            (
                ModelFilterNoQs(model=[Call])
                if instance is not None and instance.params.number_of_participants
                else ModelFilterNoQs(model=[Call, Email, Message, Text, Meeting])
            ),
            NotExpired,
            TermFilter(name="&id", param="ids"),
            FlangFilter.simple(nested_paths=["participants", "analytics.lexica"]),
            *ParticipantsSearchBase.features[3:],
            # Done to avoid repetition of ModelFilter, NotExpired and FlangFilter
            RangeFilter(field="timestamps.timestampStart"),
            RangeFilter(
                field="timestamps.timestampStart", start_param="room_day", end_param="room_day"
            ),
            NumberOfParticipantsFilter,
            Or(
                TermFilter(name="roomId", param="room_ids"),
                TermFilter(name="metadata.threadId", param="thread_ids"),
            ),
            Nested(
                TermFilter(name="participants.value.counterparty.name", param="counterparties"),
                path="participants",
                ignore_unmapped=True,
            ),
            Nested(
                TermFilter(name="participants.value.name", param="people"),
                path="participants",
                ignore_unmapped=True,
            ),
            SearchAfter,
            BeforeMessageFilter,
            # To allow user to either search by some search term or directly using hash id:
            Or(
                *get_free_text_search_filters(COMMS_TEXT_NESTED_NON_NESTED_MAPPING),
                TermFilter(name="&hash", param="model_qs"),
            ),
            TimeOfDayFilter,
            LexiconSearchFilter(param="lexica_search"),
        ]

    default_sort_order = ["timestamps.timestampStart:desc", "&id:asc"]

    @property
    def extras(self):
        return [
            {
                "_source": {
                    "excludes": (
                        [
                            "metadata.header*",
                            "voiceFile.content",
                        ]
                        if not self.params.exclude_removable_fields
                        else [
                            "voiceFile.content",
                            "&uniqueProps",
                            "&validationErrors",
                            "body.displayText",
                            "metadata",
                            "attachments",
                            "analytics",
                            "referenceMessageIds",
                        ]
                    )
                }
            }
        ]

    @staticmethod
    def participant_sort(participant_type, field_path):
        """
        Create a script sort for participants.

        Args:
            participant_type (str): The participant type to filter (e.g., "FROM", "TO", "CC").
            field_path (str): The nested field path inside 'p' (e.g., "value.counterparty.name").
        """
        return lambda sort: {
            "_script": {
                "type": "string",
                "script": f"try{{"
                f"if (params._source.containsKey('participants')) {{ "
                f"for (p in params._source.participants) {{ "
                f"if (p.types != null && p.types.contains('{participant_type}')) {{ "
                f"if (p.{field_path} != null) {{ return p.{field_path}; }} "
                f"}} "
                f"}} "
                f"}} "
                f"}} catch(Exception e) {{return ''}} "
                f"return '';",
                "order": sort.order,
            }
        }

    sort_handlers = {
        "fromName": participant_sort("FROM", "value.name"),
        "toName": participant_sort("TO", "value.name"),
        "fromCounterparty": participant_sort("FROM", "value.counterparty.name"),
        "toCounterparty": participant_sort("TO", "value.counterparty.name"),
        "fromFirm": participant_sort("FROM", "value.counterparty.name"),
        "toFirm": participant_sort("TO", "value.counterparty.name"),
    }


class SlimCommsSearchBase(CommsSearchBase):
    extras = [{"_source": {"includes": SLIM_MESSAGE_FIELDS}}]


class GroupedCommsSearchBase(CommsSearchBase):
    extras = [
        {
            "_source": {
                "includes": [
                    "&*",
                    "body",
                    "chatType",
                    "identifiers",
                    "metadata.messageId",
                    "metadata.sizeInBytes",
                    "metadata.threadId",
                    "participantsroomId",
                    "timestamps",
                ]
            }
        }
    ]


class CommsGroupDetailsAgg(CommsSearchBase):
    class Params(CommsSearchBase.Params):
        room_max: Optional[dt.date] = None
        room_min: Optional[dt.date] = None
        room_ids: Optional[List[str]] = None
        thread_ids: Optional[List[str]] = None

    params: Params

    _participant_aggs = {
        "PEOPLE": {"terms": {"field": "participants.value.name", "size": ES_MAX_AGG_SIZE}},
        "COUNTERPARTIES": {
            "terms": {"field": "participants.value.counterparty.name", "size": ES_MAX_AGG_SIZE}
        },
    }

    _detail_aggs = {
        "PARTICIPANTS": {
            "nested": {"path": "participants"},
            "aggs": {
                "FROM": {
                    "filter": {"term": {"participants.types": "FROM"}},
                    "aggs": _participant_aggs,
                },
                "TO": {"filter": {"term": {"participants.types": "TO"}}, "aggs": _participant_aggs},
                **_participant_aggs,
            },
        },
        "FROM_IDS": {"terms": {"field": "identifiers.fromId", "size": ES_MAX_AGG_SIZE}},
        "TO_IDS": {"terms": {"field": "identifiers.toIds", "size": ES_MAX_AGG_SIZE}},
    }

    def build_aggs(self) -> Dict:
        email_aggs = {
            "filter": {"terms": {"metadata.threadId": self.params.thread_ids}},
            "aggs": {
                "EMAILS_BY_THREAD_ID": {
                    "terms": {
                        "field": "metadata.threadId",
                        "size": ES_MAX_AGG_SIZE,
                    },
                    "aggs": self._detail_aggs,
                }
            },
        }

        chat_room_aggs = {
            "filter": {
                "bool": {
                    "filter": [
                        {"terms": {"roomId": self.params.room_ids}},
                        {
                            "range": {
                                "timestamps.timestampStart": {
                                    "lte": self.params.room_max,
                                    "gte": self.params.room_min,
                                }
                            }
                        },
                    ]
                }
            },
            "aggs": {
                "MESSAGES_BY_DAY": {
                    "terms": {"script": {"source": _MESSAGE_DAY_SCRIPT}, "order": {"_key": "desc"}},
                    "aggs": {
                        "MESSAGES_BY_ROOM_ID": {
                            "terms": {
                                "field": "roomId",
                                "size": ES_MAX_AGG_SIZE,
                                "order": {"_count": "desc"},
                            },
                            "aggs": self._detail_aggs,
                        }
                    },
                }
            },
        }

        aggs = {}

        if self.params.thread_ids:
            aggs["EMAIL_THREAD_FILTER"] = email_aggs

        if self.params.room_ids:
            aggs["CHAT_ROOM_FILTER"] = chat_room_aggs

        return aggs


class CommsTimelineAgg(CommsSearchBase):
    class Params(CommsSearchBase.Params):
        interval: Optional[str]
        buckets: Optional[int] = None

    params: Params
    granular_interval = 15 * 1000  # 15 seconds
    min_interval = 15 * 60 * 1000  # 15 minutes

    @cached_property
    def interval_in_millis(self) -> int:
        start = parse_datetime(self.params.start)
        end = parse_datetime(self.params.end)
        interval = self.params.interval

        if start is not None and end is not None and interval is None:
            # if the Start/End are within 30 minutes of each other,
            # we want to increase the granularity of the timeline to 15 seconds

            # Normalize the timezones before working out the time span.
            if start.tzinfo is None:
                start = start.replace(tzinfo=timezone.utc)
            if end.tzinfo is None:
                end = end.replace(tzinfo=timezone.utc)

            total_seconds = (end - start).total_seconds()
            if total_seconds <= 1800:
                return self.granular_interval

        return get_interval_in_millis_from_params(self.params, min_interval=self.min_interval)

    def build_aggs(self):
        return {
            "TIMES": {
                "date_histogram": {
                    "field": "timestamps.timestampStart",
                    "fixed_interval": f"{self.interval_in_millis}ms",
                    "format": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
                    "min_doc_count": 0,
                },
                "aggs": {"MODELS": {"terms": {"field": "&model"}}},
            }
        }


class TranscriptSearchBase(SearchModel):
    class Params(SearchModelParams):
        transcript_ids: Optional[List[str]] = None

    features = [
        ModelFilter(model=Transcript),
        TermFilter(name="&id", param="transcript_ids"),
    ]

    extras = [
        {
            "_source": {
                "excludes": [
                    "tokens",
                ]
            }
        }
    ]


class CommsComponentsSearch(CommsSearchBase):
    params: CommsSearchBase.Params

    features = [
        ModelFilter(model=[ChatEvent, TranscriptRecordModel]),
        *CommsSearchBase.features,
    ]


class CommsSummaryByParticipantAgg(CommsSearchBase):
    class Params(CommsSearchBase.Params):
        participant_filters: List[str]
        participant_type: CommsParticipantType

    params: Params

    def build_aggs(self) -> Optional[Dict]:
        # sanity check
        if self.params.participant_filters is None:
            return None

        field_name = (
            "participants.value.name"
            if self.params.participant_type == CommsParticipantType.PEOPLE
            else "participants.value.counterparty.name"
        )

        return {
            "PARTICIPANTS": {
                "nested": {"path": "participants"},
                "aggs": {
                    "FILTER": {
                        "filter": {
                            "bool": {
                                "filter": {"terms": {field_name: self.params.participant_filters}}
                            }
                        },
                        "aggs": {"NAMES": {"terms": {"field": field_name}}},
                    }
                },
            }
        }


class ParticipantCategorizationTrends(StringEnum):
    CATEGORIZATION = auto()
    CATEGORIZATION_DETAILED = auto()


class CommsRepository(RepoHelpersMixin):
    @staticmethod
    def _get_email_sort(email: Email):
        if nested_get(email, "timestamps.timestampStart") or email.timestamp_:
            return [
                int(
                    parse_datetime(
                        nested_get(email, "timestamps.timestampStart") or email.timestamp_
                    )
                    .replace(tzinfo=dt.timezone.utc)
                    .timestamp()
                    * 1000
                ),
            ]

    async def get_labels_summary(self, **search_params):
        """
        Retrieves and combines the summary of standard labels and custom labels.
        """
        labels_summary = await self.get_trend_summary(trend=CommsTrends.LABELS, **search_params)
        custom_labels_summary = await self.get_trend_summary(
            trend=CommsTrends.CUSTOM_LABELS, **search_params
        )

        return list(labels_summary) + list(custom_labels_summary)

    async def get_comms_participant_summary(
        self, participant_type: CommsParticipantType, participant_filters: List[str], **params
    ):
        aggs_result = await self.get_aggs(
            search_model_cls=CommsSummaryByParticipantAgg,
            participant_type=participant_type,
            participant_filters=participant_filters,
            **params,
        )

        return {
            item["key"]: {"commsCount": item["count"]}
            for item in aggs_result.iter_bucket_agg("PARTICIPANTS.FILTER.NAMES")
        }

    async def get_trend_summary(self, trend: CommsTrends, **search_params):
        field, nested_path, script, analyser = TRENDS_AGGREGATIONS[trend]
        aggs = {"TREND": {"terms": {"field": field, "size": ES_MAX_AGG_SIZE}}}
        iter_bucket_path = "TREND"

        qs = search_params.pop("model_qs", None)
        if field and qs:
            aggs = {
                "TREND": {
                    "filter": {
                        "query_string": {
                            "query": f"*{sanitize_es_reserved_characters(qs)}*",
                            "default_operator": "AND",
                            "analyze_wildcard": "true",
                            "allow_leading_wildcard": "true",
                            "default_field": f"{field}{analyser}",
                        }
                    },
                    "aggs": aggs,
                }
            }
            iter_bucket_path += ".TREND"

        if nested_path:
            aggs = {"TREND": {"nested": {"path": nested_path}, "aggs": aggs}}
            iter_bucket_path += ".TREND"
        if script:
            aggs = {"TREND": {"terms": {"script": {"inline": script}}}}

        return (
            await self.get_aggs(
                search_model_cls=CommsSearchBase,
                aggs=aggs,
                **search_params,
            )
        ).iter_bucket_agg(iter_bucket_path)

    async def get_comms_components(self, **params) -> RawResult:
        record_models = [ChatEvent, TranscriptRecordModel]
        search_model = CommsComponentsSearch

        return await self.get_many(
            record_model=record_models,
            search_model_cls=search_model,
            **params,
        )

    async def get_firms_summary(self, **search_params):
        firms_summary = list(
            (
                await self.get_aggs(
                    record_model=[MarketCounterparty],
                    search_model_cls=MarketCounterpartySearchName,
                    aggs={"FIRMS": {"terms": {"field": "name", "size": 10}}},
                    **search_params,
                )
            ).iter_bucket_agg("FIRMS")
        )
        firms = [f["key"] for f in firms_summary]
        if not firms:
            return []

        comms_summary_map = {
            item["key"]: item["count"]
            for item in (
                await self.get_aggs(
                    search_model_cls=CommsSearchBase,
                    aggs={
                        "TREND": {
                            "nested": {
                                "path": "participants",
                                "aggs": {
                                    "COUNTERPARTIES": {
                                        "terms": {"field": "participants.value.counterparty.name"}
                                    }
                                },
                            }
                        }
                    },
                    counterparties=firms,
                )
            ).iter_bucket_agg("TREND.COUNTERPARTIES")
        }

        return [
            {"key": item["key"], "count": comms_summary_map.get(item["key"], 0)}
            for item in firms_summary
        ]

    async def _get_comms_records(self, **params) -> RawResult:
        detail_sizing: DetailResponseSizing = params.pop("detail_sizing", DetailResponseSizing.FULL)

        search_model_cls = (
            CommsSearchBase
            if detail_sizing == DetailResponseSizing.FULL.value
            else SlimCommsSearchBase
        )

        return await self.get_many(
            record_model=[Call, Email, Message, Text, Message],
            search_model_cls=search_model_cls,
            **params,
        )

    async def get_communication_transcript(self, transcript_repo, **params):
        """
        @param transcript_repo: The transcript repo for searching transcripts fetched from communication ids
        @param params: communications ids for which we want to fetch transcript
        @return:
            {
                "communication_id_1": [
                    {transcript_record},
                ],
                "communication_id_2": [
                    {transcript_record},
                ]
            }

        """

        # Get the communications
        communications = (await self.get_comms(**params)).hits.hits
        transcript_ids = []
        raw_results = defaultdict(lambda: [])

        for communication in communications:
            # Check if its Call or Meeting
            if type(communication) not in [Call, Meeting]:
                continue

            communication = communication.dict()
            comm_id = communication.get("id_")
            transcripts = (
                communication.get("transcripts", [])
                if communication.get("transcripts") is not None
                else []
            )

            # Iterate over transcripts as it's list so handling case where transcripts > 1
            # Push the transcript id as value to raw_results with communication id as key
            for transcript in transcripts:
                # &id is stored as id
                # See schema -> comms -> call -> class Transcript
                transcript_ids.append(transcript.get("id"))
                raw_results[comm_id].append(transcript.get("id"))

        # Get the transcripts
        transcripts = (
            await transcript_repo.get_many(
                search_model_cls=TranscriptSearchBase,
                transcript_ids=transcript_ids,
                hit_deserializer=lambda x: x["_source"],
            )
        ).hits.hits

        # Create a transcript_id to transcript_model mapping
        id_to_record = {d["&id"]: d for d in transcripts}

        # Replace the transcript id with actual transcript record in raw_results and create a new dict
        results = {
            key: [id_to_record[value]]
            for key, values in raw_results.items()
            for value in values
            if value in id_to_record
        }

        return results

    async def get_comms_stats(self, **params):
        comms_count = await self.get_count(
            index=[
                CallSchema.get_elastic_index_alias(tenant=self.tenancy.tenant),
                EmailSchema.get_elastic_index_alias(tenant=self.tenancy.tenant),
                MessageSchema.get_elastic_index_alias(tenant=self.tenancy.tenant),
                TextSchema.get_elastic_index_alias(tenant=self.tenancy.tenant),
                MeetingSchema.get_elastic_index_alias(tenant=self.tenancy.tenant),
            ],
            search_model=CommsSearchBase(**params),
        )

        # if start AND end are populated
        # build previous period date range:
        # 1. Calculate Days Diff between Start/End
        # 2. Previous Period = [Start - DaysDiff, End = Start]
        start: Optional[Union[dt.date, dt.datetime]] = params.get("start")
        end: Optional[Union[dt.date, dt.datetime]] = params.get("end")

        prev_period_comms_count = None

        if start and end:
            delta = end - start
            previous_period_start = start - dt.timedelta(days=delta.days)
            prev_period_comms_count = await self.get_count(
                index=[
                    CallSchema.get_elastic_index_alias(tenant=self.tenancy.tenant),
                    EmailSchema.get_elastic_index_alias(tenant=self.tenancy.tenant),
                    MessageSchema.get_elastic_index_alias(tenant=self.tenancy.tenant),
                    TextSchema.get_elastic_index_alias(tenant=self.tenancy.tenant),
                    MeetingSchema.get_elastic_index_alias(tenant=self.tenancy.tenant),
                ],
                search_model=CommsSearchBase(start=previous_period_start, end=start),
            )

        return {
            "currentPeriodCommsCount": comms_count,
            "previousPeriodCommsCount": prev_period_comms_count,
        }

    async def get_comms_trends_summary_by_type(self, trend_chart: CommsTrendChart, **search_params):
        field, nested_path, script = COMMS_TREND_CHART_SPEC[trend_chart]

        if nested_path:
            aggs = {
                "NESTED": {
                    "nested": {"path": nested_path},
                    "aggs": {
                        "SUBJECTS": {
                            "terms": {"field": field, "size": 5},
                            "aggs": {"COMMS_COUNT": {"reverse_nested": {}}},
                        }
                    },
                }
            }
        else:
            # default aggregation structure
            agg = {"terms": {"field": field, "size": 5}}

            if script:
                agg = {"terms": {"script": {"inline": script}}}

            aggs = {"SUBJECTS": agg}

        record_models = (
            [Call]
            if trend_chart == CommsTrendChart.NUM_OF_PARTICIPANTS
            or search_params.get("number_of_participants", None)
            else [Email, Message, Text, Call, Meeting]
        )

        result = await self.get_aggs(
            record_model=record_models,
            search_model_cls=CommsSearchBase,
            aggs=aggs,
            **search_params,
        )

        return [
            {
                "key": item["key"],
                "count": item["COMMS_COUNT"]["doc_count"] if nested_path else item["doc_count"],
            }
            for item in result.iter_raw_bucket_agg("NESTED.SUBJECTS" if nested_path else "SUBJECTS")
        ]

    async def get_comms_timeline(
        self,
        start: dt.datetime = None,
        end: dt.datetime = None,
        buckets=None,
        interval=None,
        **params,
    ):
        if not start:
            start = await self.get_min_value(
                search_model_cls=CommsSearchBase,
                field="timestamps.timestampStart",
                as_string=True,
                end=end,
                **params,
            )
        if not end:
            end = dt.datetime.utcnow().replace(tzinfo=dt.timezone.utc)

        search_model = CommsTimelineAgg(
            start=start, end=end, buckets=buckets, interval=interval, **params
        )

        result = await self.get_aggs(search_model=search_model)

        return {
            "start": parse_datetime(start).isoformat() if start else None,
            "end": parse_datetime(end).isoformat() if end else None,
            "intervalInMillis": search_model.interval_in_millis,
            "buckets": [
                {
                    "timestamp": bucket["key"],
                    "datetime": bucket["key_as_string"],
                    "count": bucket["doc_count"],
                    "modelCounts": {
                        model_bucket["key"]: model_bucket["doc_count"]
                        for model_bucket in bucket.get("MODELS", {}).get("buckets", [])
                    },
                }
                for bucket in result.iter_raw_bucket_agg("TIMES")
            ],
        }

    async def get_grouped_comms_details(
        self,
        group_ids: List[str],
        detail_sizing: Optional[DetailResponseSizing] = DetailResponseSizing.FULL,
        truncate_body_text_to: Optional[int] = None,
        **params,
    ) -> Dict[str, RawResult]:
        """
        Is it NOT advisable to use this method or the approach taken...
        this is a brute-force, one-query-per-item attempt at retrieving data...
        """

        grouped_results = {}

        pagination: Pagination = params.get("pagination", None)
        skipped_hits = pagination.skip if pagination else 0

        for group_id in group_ids:
            if "||" in group_id:
                # this is a Chat Room
                date_str, room_id = group_id.split("||")
                date = parse_datetime(date_str).date()

                results = await self._get_comms_records(
                    detail_sizing=detail_sizing, room_day=date, room_ids=[room_id], **params
                )
            else:
                results = await self._get_comms_records(
                    detail_sizing=detail_sizing, thread_ids=[group_id], **params
                )

            if truncate_body_text_to and 10 <= truncate_body_text_to <= 10000:
                truncated_results = []

                for result in results.hits.hits:
                    truncated_result = result.to_dict()

                    body_text = truncated_result.get("body", {}).get("text")
                    if body_text and len(body_text) > truncate_body_text_to:
                        truncated_result["body"]["text"] = body_text[:truncate_body_text_to] + "..."

                    truncated_results.append(truncated_result)

                grouped_results[group_id] = SearchResult.from_raw_result(
                    result=RawResult(
                        **{
                            "took": results.took,
                            "timed_out": False,
                            "hits": {"hits": truncated_results, "total": results.hits.total},
                        }
                    ),
                    skipped_hits=skipped_hits,
                )
            else:
                grouped_results[group_id] = SearchResult.from_raw_result(
                    results, skipped_hits=skipped_hits
                )

        return grouped_results

    async def get_grouped_comms_search_after(self, **params) -> Tuple[RawResult, int]:
        user_pagination: Pagination = params.pop("pagination", None)
        requested_amount = user_pagination.take if user_pagination else 15
        cursor = 0
        offset = params.pop("offset", 0)
        total_hits = 0
        group_ids_seen = {}
        room_ids_by_day = {}
        email_thread_ids = {}

        results = []

        max_chat_date = None
        min_chat_date = None

        start_time = time.time()

        take = 125

        while len(results) < requested_amount:
            response = await self.get_comms(
                search_model_cls=GroupedCommsSearchBase,
                pagination=Pagination(
                    take=take,
                    sorts=[
                        Sort(field="timestamps.timestampStart", order=Sort.Order.desc),
                        Sort(field="&id", order=Sort.Order.asc),
                    ],
                ),
                **params,
            )

            params["search_after"] = response.last_sort

            for raw_result in response.as_list():
                record = raw_result.to_dict()
                model = record.get("&model")
                parsed_date = parse_datetime(record.get("timestamps", {}).get("timestampStart"))
                date = None
                date_as_string = None
                room_id = record.get("roomId")
                thread_id = record.get("metadata", {}).get("threadId")

                if parsed_date:
                    date = parsed_date.date()
                    date_as_string = date.strftime("%Y-%m-%d")

                group_id = (
                    thread_id
                    if model == "Email" and thread_id
                    else f"{date_as_string}||{room_id}"
                    if model == "Message" and room_id and date
                    else None
                )

                cursor += 1

                if offset != 0 and cursor < offset:
                    # populate group_ids_seen until cursor has caught up to the offset:
                    if (
                        model in ["Email", "Message"]
                        and group_id
                        and group_id not in group_ids_seen
                    ):
                        group_ids_seen[group_id] = True

                elif model in ["Call", "Text", "Meeting"] or (
                    model in ["Email", "Message"] and not group_id
                ):
                    results.append(record)
                elif date is None:
                    # Possibly a record with an invalid timestampStart.
                    results.append(record)
                else:
                    record["groupId"] = group_id

                    if (
                        model == "Email"
                        and group_id not in group_ids_seen
                        and group_id not in email_thread_ids
                    ):
                        email_thread_ids[thread_id] = True
                        results.append(record)

                    if model == "Message":
                        if date > max_chat_date if max_chat_date else dt.datetime.min:
                            max_chat_date = date

                        if date < min_chat_date if min_chat_date else dt.datetime.min:
                            min_chat_date = date

                        if group_id not in group_ids_seen and group_id not in room_ids_by_day:
                            room_ids_by_day[group_id] = record
                            results.append(record)

                if len(results) >= requested_amount:
                    break

            if response.hits.total < take or len(results) >= requested_amount:
                break

        email_thread_ids = [t for t in email_thread_ids.keys()] if any(email_thread_ids) else None
        room_ids = (
            [r.split("||")[1] for r in room_ids_by_day.keys()] if any(room_ids_by_day) else None
        )

        results = await self._enrich_grouped_results(
            grouped_results=results,
            email_thread_ids=email_thread_ids,
            room_ids=room_ids,
            max_chat_date=max_chat_date,
            min_chat_date=min_chat_date,
            **params,
        )

        process_time = time.time() - start_time

        return (
            RawResult(
                **{
                    "took": int(process_time),
                    "timed_out": False,
                    "hits": {"hits": results, "total": total_hits},
                }
            ),
            cursor,
        )

    async def scan_comms_records(self, size=250, deserialize=False, **params):
        def deserializer(hit):
            model_name = hit["_source"]["&model"]
            src = hit["_source"]

            _model_map = {
                "Call": Call,
                "Email": Email,
                "Message": Message,
                "Text": Text,
                "Meeting": Meeting,
            }

            if model_name in _model_map.keys():
                return _model_map[model_name](**src)

            return None

        return [
            item
            async for item in self.repo.execute_scan(
                index=self.index_for_record_model([Call, Email, Message, Text, Meeting]),
                search_model=CommsSearchBase(**params),
                size=size,
                hit_deserializer=deserializer if deserialize else None,
            )
        ]

    async def get_comms(
        self,
        search_model_cls: Type[SearchBase] = None,
        **params,
    ) -> RawResult:
        record_models = (
            [Call]
            if params.get("number_of_participants", None)
            else [Email, Message, Text, Call, Meeting]
        )
        return await self.get_many(
            record_model=record_models,
            search_model_cls=search_model_cls if search_model_cls else CommsSearchBase,
            **params,
        )

    async def get_comm_by_id(self, id, **params) -> Union[Call, Email, Message, Text, Meeting]:
        return await self.get_one(
            record_model=[Call, Email, Message, Text, Meeting],
            id=id,
            **params,
        )

    async def get_attachment_data(self, **params) -> Optional[Dict]:
        if params.get("model_qs", None):
            query = {
                "size": 0,
                "aggs": {
                    "FILTER": {
                        "filter": {
                            "bool": {
                                "filter": [
                                    {
                                        "query_string": {
                                            "query": f"{sanitize_es_reserved_characters(params['model_qs'])}*",
                                            "default_operator": "AND",
                                            "analyze_wildcard": "true",
                                            "allow_leading_wildcard": "true",
                                            "fields": [
                                                "attachments.fileName",
                                                "attachments.fileType",
                                            ],
                                        }
                                    }
                                ]
                            }
                        },
                        "aggs": {
                            "fileNames": {
                                "terms": {
                                    "field": "attachments.fileName",
                                    "size": 100,
                                    "include": f"{sanitize_es_reserved_characters(params['model_qs'])}.*",
                                }
                            },
                            "fileTypes": {
                                "terms": {
                                    "field": "attachments.fileType",
                                    "size": 100,
                                    "include": f"{sanitize_es_reserved_characters(params['model_qs'])}.*",
                                }
                            },
                            "sizeInBytes": {
                                "terms": {"field": "attachments.sizeInBytes", "size": 100}
                            },
                        },
                    }
                },
            }

        else:
            query = {
                "size": 0,
                "aggs": {
                    "fileNames": {"terms": {"field": "attachments.fileName", "size": 100}},
                    "fileTypes": {"terms": {"field": "attachments.fileType", "size": 100}},
                    "sizeInBytes": {"terms": {"field": "attachments.sizeInBytes", "size": 100}},
                },
            }

        results = self.es_client.search(
            body=query,
            index=f"{self.tenant}-call-alias,{self.tenant}-email-alias,{self.tenant}-message-alias,{self.tenant}-text-alias",
        )

        attachments_aggs = results.raw.get("aggregations")

        if params.get("model_qs", None):
            attachments_aggs = results.raw.get("aggregations").get("FILTER")

        if not attachments_aggs:
            return

        result = {"fileNames": [], "fileTypes": [], "sizeInBytes": []}

        for item in attachments_aggs.get("fileNames", {}).get("buckets", []):
            result["fileNames"].append(
                AggregationResult(
                    count=item.get("doc_count"),
                    id=item.get("key"),
                    name=item.get("key"),
                )
            )

        for item in attachments_aggs.get("fileTypes", {}).get("buckets", []):
            result["fileTypes"].append(
                AggregationResult(
                    count=item.get("doc_count"),
                    id=item.get("key"),
                    name=item.get("key"),
                )
            )

        for item in attachments_aggs.get("sizeInBytes", {}).get("buckets", []):
            result["sizeInBytes"].append(
                AggregationResult(
                    count=int(item.get("doc_count")),
                    id=item.get("key"),
                    name=item.get("key"),
                )
            )

        return result

    async def get_comms_around_by_email_id(
        self,
        email_id: str,
        skip: Optional[int] = None,
        take: Optional[int] = 50,
        search_before: Optional[str] = None,
        search_after: Optional[str] = None,
        **search_params,
    ) -> SearchResult:
        email: Email = await self.get_one(Email, email_id)

        return await self.get_comms_around_by_email(
            email=email,
            take=take,
            skip=skip,
            search_before=search_before,
            search_after=search_after,
            **search_params,
        )

    async def _skip_around_email(self, email, skip, take, **search_params) -> SearchResult:
        thread_id = nested_get(email, "metadata.threadId")
        sort_order = search_params.pop("sort_order", None) or Sort.Order.asc
        result = await self.get_comms(
            thread_ids=[thread_id],
            pagination=Pagination(
                skip=skip,
                take=take,
                sorts=[Sort(field="timestamps.timestampStart", order=sort_order)],
            ),
            **search_params,
        )
        return SearchResult.from_raw_result(result, skipped_hits=skip)

    async def get_comms_around_by_email(
        self,
        email: Email,
        skip: Optional[int] = None,
        take: Optional[int] = 50,
        search_before: Optional[str] = None,
        search_after: Optional[str] = None,
        **search_params,
    ) -> SearchResult:
        """Get emails around a particular email within the same thread Only one
        of searchBefore and searchAfter can be specified at a time.

        In general, this endpoint should NOT be used for filtering.
        When filters are used, it is recommended to use the plain email listing.

        Filters will not be applied to the message from which the search originates.
        This origin message is returned as part of other messages in two cases:
        1) if searchBefore and searchAfter are not supplied - it will then be returned even if
           the message does not match filters (search, f, start, end, etc.).
        2) if the message falls on the page delineated by searchBefore and searchAfter and matches the filters.

        To calculate the results after the current page, use totalHits - skippedHits - returnedHits.
        """
        sort_order = search_params.pop("sort_order", None)
        if skip is not None:
            return await self._skip_around_email(
                email, skip, take, sort_order=sort_order, **search_params
            )

        if not (nested_get(email, "timestamps.timestampStart") or email.timestamp_):
            email = self.get_comm_by_id(id=email.id_)

        email_sort = self._get_email_sort(email=email)

        search_before = decode_search_after(search_before)
        search_after = decode_search_after(search_after)

        if not search_before and not search_after:
            search_before = email_sort
            search_after = email_sort
            search_before_take = math.floor((take - 1) / 2)
            search_after_take = math.floor(take / 2)

        else:
            search_before_take = take
            search_after_take = take

        thread_id = nested_get(email, "metadata.threadId")

        before_result = None
        if search_before:
            before_result = await self.get_comms(
                thread_ids=[thread_id],
                pagination=Pagination(
                    take=search_before_take,
                    sorts=[Sort(field="timestamps.timestampStart", order=Sort.Order.desc)],
                ),
                search_after=search_before,
                **search_params,
            )

        after_result = None
        if search_after:
            after_result = await self.get_comms(
                thread_ids=[thread_id],
                pagination=Pagination(
                    take=search_after_take,
                    sorts=[Sort(field="timestamps.timestampStart", order=Sort.Order.asc)],
                ),
                search_after=search_after,
                **search_params,
            )

        before_hits = before_result.as_list() if before_result else []
        after_hits = after_result.as_list() if after_result else []

        if search_before and search_after:
            results = [
                *(reversed(before_hits)),
                email,
                *after_hits,
            ]

        elif search_before:
            results = list(reversed(before_hits))

        else:
            results = after_hits

        total_hits = (
            before_result.hits.total
            if before_result
            else (after_result.hits.total if after_hits else 0)
        )

        # A far from perfect calculation of skipped hits:
        # count how many messages match the search chronologically BEFORE the first message returned.
        skipped_hits = None
        if (
            not search_params.get("f")
            and not search_params.get("start")
            and not search_params.get("end")
            and len(results) < total_hits
            and results
        ):
            # Filtering + around shouldn't really be supported, but if anyone attempts it we won't calculate
            # this because it will be just confusing.

            # Count how many messages happen in the sorting order before the first message
            # of the results.

            count_params = dict(search_params)
            count_params["before_message_timestamp"] = results[0].timestamps["timestampStart"]
            skipped_hits = await self.get_comms(thread_ids=[thread_id], count=True, **count_params)

        return SearchResult(
            header=SearchResultHeader(
                skippedHits=skipped_hits,
                returnedHits=len(results),
                totalHits=total_hits,
                previousSearchBefore=encode_search_after(
                    self._get_email_sort(before_result.hits.hits[-1])
                    if before_hits and len(before_hits) == search_before_take
                    else (
                        self._get_email_sort(after_hits[0])
                        if not before_result and after_hits
                        else None
                    )
                ),
                nextSearchAfter=encode_search_after(
                    self._get_email_sort(after_hits[-1])
                    if after_hits and len(after_hits) == search_after_take
                    else (
                        self._get_email_sort(before_hits[0])
                        if not after_result and before_hits
                        else None
                    )
                ),
            ),
            results=results,
        )

    async def get_thread_summary(self, thread_id: str, **search_params):
        thread_count = await self.get_count(
            index_suffix=Email.Config.index_suffix,
            search_model=CommsSearchBase(thread_ids=[thread_id], **search_params),
        )

        aggs_result = await self.get_aggs(
            search_model_cls=CommsSearchBase,
            aggs={
                "MIN_THREAD_TIMESTAMP": {"min": {"field": "timestamps.timestampStart"}},
                "MAX_THREAD_TIMESTAMP": {"max": {"field": "timestamps.timestampStart"}},
            },
            thread_ids=[thread_id],
            **search_params,
        )

        return {
            "messagesInThread": thread_count,
            "rootEmailTimestamp": aggs_result.aggregations["MIN_THREAD_TIMESTAMP"][
                "value_as_string"
            ],
            "lastEmailTimestamp": aggs_result.aggregations["MAX_THREAD_TIMESTAMP"][
                "value_as_string"
            ],
        }

    @staticmethod
    def _agg_bucket_to_list(bucket: List, property_name: str = "key") -> List:
        return sorted(list(map(lambda x: x.get(property_name), bucket)))

    async def _map_aggs_to_group_details(
        self,
        bucket: Dict,
        date: Optional[str] = None,
        room_id: Optional[str] = None,
        thread_id: Optional[str] = None,
    ) -> GroupDetails:
        return GroupDetails(
            **{
                "date": date,
                "roomId": room_id,
                "threadId": thread_id,
                "numberOfMessages": bucket.get("doc_count"),
                "fromIds": self._agg_bucket_to_list(bucket.get("FROM_IDS", {}).get("buckets", [])),
                "toIds": self._agg_bucket_to_list(bucket.get("TO_IDS", {}).get("buckets", [])),
                "fromParticipants": self._agg_bucket_to_list(
                    bucket.get("PARTICIPANTS", {}).get("FROM").get("PEOPLE", {}).get("buckets", [])
                ),
                "toParticipants": self._agg_bucket_to_list(
                    bucket.get("PARTICIPANTS", {}).get("TO").get("PEOPLE", {}).get("buckets", [])
                ),
                "participants": self._agg_bucket_to_list(
                    bucket.get("PARTICIPANTS", {}).get("PEOPLE", {}).get("buckets", [])
                ),
                "fromCounterparties": self._agg_bucket_to_list(
                    bucket.get("PARTICIPANTS", {})
                    .get("FROM")
                    .get("COUNTERPARTIES", {})
                    .get("buckets", [])
                ),
                "toCounterparties": self._agg_bucket_to_list(
                    bucket.get("PARTICIPANTS", {})
                    .get("TO")
                    .get("COUNTERPARTIES", {})
                    .get("buckets", [])
                ),
                "counterparties": self._agg_bucket_to_list(
                    bucket.get("PARTICIPANTS", {}).get("COUNTERPARTIES", {}).get("buckets", [])
                ),
            }
        )

    async def _enrich_grouped_results(
        self,
        grouped_results: List[Dict],
        email_thread_ids: Optional[List[str]] = None,
        room_ids: Optional[List[str]] = None,
        max_chat_date: Optional[dt.date] = None,
        min_chat_date: Optional[dt.date] = None,
        **params,
    ) -> List[Dict]:
        # no enrichment is needed...
        if not email_thread_ids and not room_ids:
            return grouped_results

        aggs_search_model = CommsGroupDetailsAgg(
            room_max=max_chat_date,
            room_min=min_chat_date,
            room_ids=room_ids,
            thread_ids=email_thread_ids,
            **params,
        )

        aggs_result = await self.get_aggs(search_model=aggs_search_model)

        group_agg_details = {}

        for day_bucket in list(aggs_result.iter_raw_bucket_agg("CHAT_ROOM_FILTER.MESSAGES_BY_DAY")):
            for room_bucket in day_bucket.get("MESSAGES_BY_ROOM_ID", {}).get("buckets", []):
                day = day_bucket.get("key")
                room_id = room_bucket.get("key")

                group_id = f"{day}||{room_id}"

                group_agg_details[group_id] = await self._map_aggs_to_group_details(
                    bucket=room_bucket, date=day, room_id=room_id
                )

        for thread_bucket in list(
            aggs_result.iter_raw_bucket_agg("EMAIL_THREAD_FILTER.EMAILS_BY_THREAD_ID")
        ):
            group_id = thread_bucket.get("key")
            group_agg_details[group_id] = await self._map_aggs_to_group_details(
                bucket=thread_bucket, thread_id=thread_bucket.get("key")
            )

        for result in grouped_results:
            group_id = result.get("groupId")

            if not group_id or group_id not in group_agg_details:
                continue

            result["groupDetails"] = group_agg_details[group_id].to_dict()

        return grouped_results

    async def get_grouped_comms_scan(self, **params) -> Tuple[RawResult, int]:
        """
        This big block o' ugliness is a (poor) attempt at "grouping" and de-duping results...

        Within Comms data, records can either be Calls, Emails, Messages (Chat), or Text (Messages)
        Emails and Messages can sometimes belong to Threads or Chat Rooms, respectively.

        This logic attempts to "group" the Emails and Messages that are part of a Thread/Room together while
        including the singular comms records (Calls, Text, non-thread Email, non-room Messages) in the result set

        Example raw result:
        [
           { type: 'Email', id: 'eml1', threadId: 'foo', ...},
           { type: 'Message', id: 'msg1', roomId: 'bob', ...},
           { type: 'Call', id: 'call1', ...},
           { type: 'Text', id: 'text1', ...},
           { type: 'Call', id: 'call2', ...},
           { type: 'Email', id: 'eml2', threadId: 'foo', ...},
           { type: 'Email', id: 'eml3', threadId: 'bar', ...},
           { type: 'Message', id: 'msg2', roomId: 'bob', ...},
        ]

        Expected output:
        [
           { type: 'Email', id: 'eml1', threadId: 'foo', ...},
           { type: 'Message', id: 'msg1', roomId: 'bob', ...},
           { type: 'Call', id: 'call1', ...},
           { type: 'Text', id: 'text1', ...},
           { type: 'Call', id: 'call2', ...},
           { type: 'Email', id: 'eml3', threadId: 'bar', ...},
        ]

        Because multiple emails from the 'foo' thread were seen, only the first was returned.
        Likewise with the chat room 'bob'

        Additional grouping logic is applied to Chat Messages - groups are applied By Room, by DAY

        Once the result set has been computed, the group information is used to perform an aggregation
        query and enriched the "grouped" results.

        The 'offset' value represents the number of records that were scanned to produce the result.
        Using the example above, 8 records were scanned to produce a result set of 6 (two records were grouped).
        The offset would be 8.

        During a request, if a non-zero offset is provided, records will be scanned and the grouping logic will
        be applied until the 'cursor' has caught up to the offset. Once the cursor has caught up, the result set
        will begin to be populated. This is an attempt at providing a "pagination"-like effect (or infinite scroll).
        """
        user_pagination: Pagination = params.pop("pagination", None)
        requested_amount = user_pagination.take if user_pagination else 15
        cursor = 0
        offset = params.pop("offset", 0)
        total_hits = 0
        group_ids_seen = {}
        room_ids_by_day = {}
        email_thread_ids = {}

        results = []

        max_chat_date = None
        min_chat_date = None

        start_time = time.time()

        for record in await self.scan_comms_records(size=500, **params):
            model = record.get("&model")
            parsed_date = parse_datetime(record.get("timestamps", {}).get("timestampStart"))
            date = None
            date_as_string = None
            room_id = record.get("roomId")
            thread_id = record.get("metadata", {}).get("threadId")

            if parsed_date:
                date = parsed_date.date()
                date_as_string = date.strftime("%Y-%m-%d")

            group_id = (
                thread_id
                if model == "Email" and thread_id
                else f"{date_as_string}||{room_id}"
                if model == "Message" and room_id and date
                else None
            )

            cursor += 1

            if offset != 0 and cursor < offset:
                # populate group_ids_seen until cursor has caught up to the offset:
                if model in ["Email", "Message"] and group_id and group_id not in group_ids_seen:
                    group_ids_seen[group_id] = True

            elif model in ["Call", "Text", "Meeting"] or (
                model in ["Email", "Message"] and not group_id
            ):
                results.append(record)
            elif date is None:
                # Possibly a record with an invalid timestampStart.
                results.append(record)
            else:
                record["groupId"] = group_id

                if (
                    model == "Email"
                    and group_id not in group_ids_seen
                    and group_id not in email_thread_ids
                ):
                    email_thread_ids[thread_id] = True
                    results.append(record)

                if model == "Message":
                    if date > max_chat_date if max_chat_date else dt.datetime.min:
                        max_chat_date = date

                    if date < min_chat_date if min_chat_date else dt.datetime.min:
                        min_chat_date = date

                    if group_id not in group_ids_seen and group_id not in room_ids_by_day:
                        room_ids_by_day[group_id] = record
                        results.append(record)

            if len(results) >= requested_amount:
                break

        params.pop("lexica_search", None)

        email_thread_ids = [t for t in email_thread_ids.keys()] if any(email_thread_ids) else None
        room_ids = (
            [r.split("||")[1] for r in room_ids_by_day.keys()] if any(room_ids_by_day) else None
        )

        results = await self._enrich_grouped_results(
            grouped_results=results,
            email_thread_ids=email_thread_ids,
            room_ids=room_ids,
            max_chat_date=max_chat_date,
            min_chat_date=min_chat_date,
            **params,
        )

        process_time = time.time() - start_time

        return (
            RawResult(
                **{
                    "took": int(process_time),
                    "timed_out": False,
                    "hits": {"hits": results, "total": total_hits},
                }
            ),
            cursor,
        )


class LabelsEnum(StringEnum):
    CONFIDENTIAL = "Confidential"
    ENCRYPTED = "Encrypted"
    PERSONAL_COMMUNICATION = "Personal Communication"
    PERSONAL_INFORMATION = "Personal Information"


class Labels(APIModel):
    label_enum: Optional[List[LabelsEnum]] = Field(None, alias="labels")
    label_custom: Optional[List[str]] = Field(None, alias="labelsCustom")


class UpdateComms(RecordModel):
    labels: Optional[Labels] = Field(None, alias="labels")


class RecordsWithRawFileAggsSearch(SearchModel):
    """Get counts of comms with given s3 key."""

    class Params(SearchModelParams, ParticipantsSearchBase.Params):
        start: Union[dt.datetime, dt.date] = None
        end: Union[dt.datetime, dt.date] = None
        raw_files: Union[str, List[str], Set[str]] = None

    params: Params
    features = [
        ModelFilterNoQs(model=[Call, Email, Message, Text, Meeting, Order]),
        Or(
            TermFilter(name="metadata.source.fileInfo.location.key", param="raw_files"),
            TermFilter(name="sourceKey", param="raw_files"),
        ),
    ]

    def build_aggs(self) -> Optional[Dict]:
        aggs_query = {
            "RECORDS_KEY": {
                "terms": {"field": "metadata.source.fileInfo.location.key"},
            },
            "RECORDS_SOURCE_KEY": {
                "terms": {"field": "sourceKey"},
            },
        }
        return aggs_query


class CaseAttachableRecordRepo(RepoHelpersMixin):
    """There types of records can be attached to case."""

    async def get_records_of_raw_files(
        self, raw_files: Union[str, List[str], Set[str]] = None, **params
    ):
        return await self.get_aggs(
            search_model=RecordsWithRawFileAggsSearch(raw_files=raw_files, **params)
        )

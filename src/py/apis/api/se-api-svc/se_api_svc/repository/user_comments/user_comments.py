# type: ignore
import base64
import datetime as dt
import logging
from api_sdk.auth import Tenancy
from api_sdk.cloud.abstractions_sync.abstract_storage_client import AbstractStorageClient
from api_sdk.es_dsl.base import ModelFilter, NotExpired, QueryString, SearchModel, TermFilter
from api_sdk.es_dsl.flang import FlangFilter
from api_sdk.es_dsl.params import SearchModelParams
from api_sdk.full_text_search import USER_COMMENT_TEXT_SEARCH_FIELDS
from api_sdk.repository.asyncronous.request_bound import (
    RequestBoundRepository,
    TenantConfiguration,
    has_feature_flag,
)
from api_sdk.repository.elastic import AbstractEsRepository
from fastapi import Request
from pathlib import Path
from se_api_svc.core.constants import ES_MAX_AGG_SIZE
from se_api_svc.schemas.attachment import FileInfo
from se_api_svc.schemas.surveillance.alerts import <PERSON><PERSON><PERSON><PERSON>, OrderAlert
from se_api_svc.schemas.surveillance.market_abuse import MarketAbuseScenarioTag
from se_api_svc.schemas.surveillance.workflow import WorkflowUpdate
from se_api_svc.schemas.user_comments import UserCommentIn
from se_db_utils.database import Database
from se_elastic_schema.models.tenant.user.user_comment import UserComment
from se_elastic_schema.static.surveillance import AlertHitStatus
from sql_query_builder.features import RangeFilter
from typing import Dict, List, Optional, Union

log = logging.getLogger(__name__)


MAX_COMMENT_ID_SEARCH_SIZE = 10000


class UserCommentSearch(SearchModel):
    class Params(SearchModelParams):
        search: Optional[str]
        start: Union[dt.datetime, dt.date] = None
        end: Union[dt.datetime, dt.date] = None
        f: Optional[str] = None
        link_id: Optional[str | List[str]] = None
        link_key: str = None
        link_model: Optional[str] = None

    params: Params

    features = [
        NotExpired,
        FlangFilter.simple(param="f"),
        ModelFilter(model=UserComment),
        TermFilter(name="linkId", param="link_id"),
        TermFilter(name="linkKey", param="link_key"),
        TermFilter(name="linkModel", param="link_model"),
        QueryString(
            param="model_qs",
            allow_leading_wildcard=True,
            fields=USER_COMMENT_TEXT_SEARCH_FIELDS,
        ),
    ]

    default_sort_order = ["timestamp:desc"]


class UserCommentsSearch(SearchModel):
    class Params(SearchModelParams):
        link_ids: List[str] = None
        start: Optional[dt.datetime] = None
        end: Optional[dt.datetime] = None
        f: Optional[str] = None

    features = [
        ModelFilter(model=UserComment),
        NotExpired,
        TermFilter(name="link.id", param="link_ids"),
        RangeFilter(field="_meta.timestamp"),
        FlangFilter.simple(param="f"),
    ]

    default_sort_order = ["timestamp:desc"]


class UserCommentsLastComments(UserCommentsSearch):
    """Generates the aggs for the link id.

    For eachof the link it finds the latest comment in the TOP_HITS
    """

    def build_aggs(self) -> Optional[Dict]:
        return {
            "LINK_IDS": {
                "terms": {"field": "link.id", "size": ES_MAX_AGG_SIZE},
                "aggs": {
                    "TOP_HITS": {
                        "top_hits": {
                            "_source": {"includes": ["comment"]},
                            "sort": [{"timestamp": {"order": "desc"}}],
                            "size": 1,
                        },
                    },
                },
            },
        }


class UserCommentsRepository(RequestBoundRepository):
    def __init__(
        self,
        tenancy: Tenancy,
        es_repo: AbstractEsRepository,
        cloud_client: AbstractStorageClient,
        owner: Request,
        db: Database,
    ):
        super().__init__(tenancy=tenancy, es_repo=es_repo, owner=owner, db=db)
        self.cloud_client = cloud_client

    async def get_comments(self, link_ids: List[str], **params):
        return await self.get_many(search_model_cls=UserCommentsSearch, link_ids=link_ids, **params)

    @property
    def bucket(self):
        return self.tenancy.realm

    async def get_user_comments(self, link_id: str | List[str], **search_params):
        return await self.get_many(
            record_model=UserComment,
            search_model_cls=UserCommentSearch,
            link_id=link_id,
            **search_params,
        )

    def save_attachments_to_cloud(self, attachment: Dict, linkKey: str):
        now = dt.datetime.utcnow()
        original_name = Path(attachment["fileName"])
        stored_name = f"{original_name.stem}.{now.strftime('%Y%m%d-%H%M%S')}{original_name.suffix}"
        prefix = f"user-comments/{linkKey}/attachments/"
        key = f"{prefix}{stored_name}"
        log.debug(
            f"Uploading attachment to new comment in {linkKey} "
            f"attachment {str(original_name)!r} to bucket={self.bucket!r}, key={key!r}"
        )

        self.cloud_client.upload_object(
            data_obj=base64.b64decode(attachment["content"]), bucket=self.bucket, key=key
        )

        attachment["fileInfo"] = FileInfo(
            location={"bucket": self.bucket, "key": key},
            contentLength=attachment["sizeInBytes"],
            processed=now.isoformat(),
        )

        # Clear content as we have stored it in S3
        attachment["content"] = None
        return attachment

    def get_attachment_stream(self, attachment_key: str):
        log.debug(f"Attempting to retrieve attachment {attachment_key}")
        try:
            resp = self.cloud_client.get_object(
                Bucket=self.bucket, Key=attachment_key, raw_stream=True
            )
        except Exception as ce:
            log.error("Cloud Retrieval Error", ce)
            raise
        return resp

    async def save_comment(self, comment_body: UserCommentIn):
        attachments = (
            [
                self.save_attachments_to_cloud(attachment=attachment, linkKey=comment_body.linkKey)
                for attachment in comment_body.attachments
            ]
            if comment_body.attachments
            else []
        )

        comment = UserComment(
            userId=self.tenancy.userId,
            userName=self.tenancy.user_name,
            timestamp=dt.datetime.utcnow(),
            attachments=attachments,
            linkId=comment_body.linkId,
            linkKey=comment_body.linkKey,
            linkModel=comment_body.linkModel,
            comment=comment_body.comment,
            commentCategory=comment_body.commentCategory,
        )
        return await self.save_new(comment)

    async def get_surveillance_alert_record_id(self, link_id: str):
        """Retrieve the record ID for a given alert."""
        alert_record = await self.get_one(
            [CommunicationAlert, OrderAlert, MarketAbuseScenarioTag], terms={"slug": link_id}
        )

        if alert_record:
            return alert_record.id_, link_id, True

        return link_id, link_id, False

    async def search_comment(self, search_term: str, link_model: Optional[str], **search_params):
        return await self.get_many(
            record_model=UserComment,
            search_model_cls=UserCommentSearch,
            search=search_term,
            link_model=link_model,
            **search_params,
        )

    @staticmethod
    def comment_link_id_deserializer(hit):
        source = hit
        if "_source" in hit:
            source = hit.get("_source")

        if "linkId" in source:
            return source.get("linkId")

        return None

    async def scroll_search_comment_link_ids(
        self, link_model: Optional[str] = None, size: int = 250, **search_params
    ):
        return [
            item
            async for item in self.execute_scan(
                index=self.index_for_record_model(UserComment),
                search_model=UserCommentSearch(link_model=link_model, **search_params),
                size=size,
                hit_deserializer=self.comment_link_id_deserializer,
            )
        ]

    async def get_last_comments(self, link_ids: List[str], **params) -> Dict[str, str]:
        last_comments_aggs = await self.get_aggs(
            search_model_cls=UserCommentsLastComments, link_ids=link_ids, **params
        )
        comments_map = {}
        for comment in last_comments_aggs.iter_raw_bucket_agg("LINK_IDS"):
            comments_map[comment.key] = comment.TOP_HITS.hits.hits[0]._source.comment

        return comments_map


async def get_comment_link_ids(repo: UserCommentsRepository, **params) -> Optional[List[str]]:
    raw_tenant_configuration: TenantConfiguration = await repo.get_tenant_configuration()

    # we only want to search the corresponding C-Surv Alert comments if the feature flag is set
    # and there is a free-text search being applied

    if has_feature_flag(raw_tenant_configuration, "c-surv-comment-search"):
        return None

    if "model_qs" not in params:
        return None

    # we only want to search the corresponding
    # C-Surv Alert comments if there is a free-text search being applied
    search_term = params["model_qs"]
    params["model_qs"] = f"*{search_term}"

    return await repo.scroll_search_comment_link_ids(
        link_model=CommunicationAlert.__config__.model_name,
        size=MAX_COMMENT_ID_SEARCH_SIZE,
        **params,
    )


def generate_alert_comment(update: WorkflowUpdate) -> str:
    """
    Generates Resolution comment string for the resolved alert using the resolution details.
    Params:
    resolution_details: (WorkflowUpdate) - alert resolution details

    Returns
    comment_string: (str) - comment string
    """
    alert_comment = ""
    if update.status in [
        AlertHitStatus.RESOLVED,
        AlertHitStatus.RESOLVED_WITH_BREACH,
        AlertHitStatus.RESOLVED_WITH_DISMISSAL,
        AlertHitStatus.RESOLVED_WITH_INVESTIGATION,
    ]:
        workflow_details = update.dict()
        category = (
            workflow_details.get("resolutionCategoryCustom")
            or workflow_details.get("resolutionCategory")
            or ""
        )
        category_sanitised = category.replace("_", " ").title() or "N/A"
        resolved_comment = workflow_details.get("resolvedComment") or "N/A"
        alert_comment = f"RESOLUTION: {category_sanitised} - {resolved_comment}"

    elif update.status == AlertHitStatus.UNDER_INVESTIGATION:
        alert_comment = "UNDER INVESTIGATION"
    elif update.status == AlertHitStatus.UNRESOLVED:
        alert_comment = "REOPENED"

    return alert_comment

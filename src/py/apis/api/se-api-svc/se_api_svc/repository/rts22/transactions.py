import collections
import datetime as dt
from api_sdk.es_dsl.base import (
    <PERSON><PERSON>,
    <PERSON>,
    ModelFilter,
    Not,
    NotExpired,
    Or,
    SearchFeature,
    SearchFeatureConfig,
    SearchModel,
    TermFilter,
)
from api_sdk.es_dsl.features import Nested, RangeFilter
from api_sdk.es_dsl.flang import FlangFilter
from api_sdk.es_dsl.params import SearchModelParams
from api_sdk.full_text_search import (
    RTS22_TRANSACTION_NESTED_NON_NESTED_MAPPING,
    get_free_text_search_filters,
)
from api_sdk.models.elasticsearch import RawResult
from api_sdk.repository.asyncronous.request_bound import RepoHelpersMixin
from api_sdk.utils.intervals import get_histogram_interval, get_interval_in_millis_from_params
from api_sdk.utils.not_set import NOT_SET
from api_sdk.utils.utils import nested_dict_get, parse_datetime
from dataclasses import dataclass
from functools import cached_property
from schema_sdk.steeleye_model.gamma import GammaMetaFields
from se_api_svc.core.constants import ES_MAX_AGG_SIZE
from se_api_svc.schemas.rts22 import (
    ReportingAuthority,
    Rts22Transaction,
    Rts22TransactionReportedState,
    TrendChart,
)
from se_elastic_schema.models.tenant.user.user_comment import UserComment
from se_elastic_schema.static.mifid2 import (
    RTS22ExternalStatus,
    RTS22TransactionStatus,
    RTS22ValidationStatus,
)
from typing import Dict, List, Optional, Union

_ANY = object()


TREND_CHART_SPEC = {
    TrendChart.ASSET_CLASS: ("instrumentDetails.instrument.ext.bestExAssetClassMain", False),
    TrendChart.ASSET_CLSS_SUB: ("instrumentDetails.instrument.ext.bestExAssetClassSub", False),
    TrendChart.CFI_CATEGORY: ("instrumentDetails.instrument.cfiCategory", False),
    TrendChart.CFI_CODE: ("instrumentDetails.instrument.instrumentClassification", False),
    TrendChart.CFI_GROUP: ("instrumentDetails.instrument.cfiGroup", False),
    TrendChart.CFI_ATTRIBUTE_1: ("instrumentDetails.instrument.cfiAttribute1", False),
    TrendChart.CFI_ATTRIBUTE_2: ("instrumentDetails.instrument.cfiAttribute2", False),
    TrendChart.CFI_ATTRIBUTE_3: ("instrumentDetails.instrument.cfiAttribute3", False),
    TrendChart.CFI_ATTRIBUTE_4: ("instrumentDetails.instrument.cfiAttribute4", False),
    TrendChart.DATA_SOURCE_NAME: ("dataSourceName", False),
    TrendChart.INSTRUMENT: ("instrumentDetails.instrument.instrumentFullName", False),
    TrendChart.CURRENCY: ("transactionDetails.priceCurrency", False),
    TrendChart.ISIN: ("instrumentDetails.instrument.instrumentIdCode", False),
    TrendChart.TRADING_VENUE: ("transactionDetails.venue", False),
    TrendChart.ULTIMATE_VENUE: ("transactionDetails.ultimateVenue", False),
    TrendChart.COUNTERPARTY: ("parties.counterparty.name", False),
    TrendChart.EXECUTING_ENTITY: ("parties.executingEntity.name", False),
    TrendChart.TRADER: ("parties.trader.name", False),
    TrendChart.BUYER: ("parties.buyer.name", False),
    TrendChart.SELLER: ("parties.seller.name", False),
    TrendChart.EXECUTION_WITHIN_FIRM: ("parties.executionWithinFirm", False),
    TrendChart.INVESTMENT_DECISION_WITHIN_FIRM: (
        "parties.investmentDecisionWithinFirm.name",
        False,
    ),
    TrendChart.INVESTMENT_DECISION_WITHING_FIRM: (
        "parties.investmentDecisionWithinFirm.name",
        False,
    ),  # TODO Deprec.
}

VALIDATION_ERROR_CODES = {
    "SE_DV-188": ("Buyer ID", "parties.buyer"),
    "SE_DV-189": ("Seller ID", "parties.seller"),
    "SE_DV-163": ("Investment Decision Within Firm ID", "parties.investmentDecisionWithinFirm"),
    "SE_DV-102": ("Investment Decision Within Firm ID", "parties.investmentDecisionWithinFirm"),
    "SE_DV-100": ("Execution Within Firm ID", "parties.executionWithinFirm"),
    "SE_DV-160": ("Execution Within Firm ID", "parties.executionWithinFirm"),
    "SE_DV-191": ("Buyer Decision Maker ID", "parties.buyerDecisionMaker"),
    "SE_DV-197": ("Seller Decision Maker ID", "parties.sellerDecisionMaker"),
}

_REPORTED_ = TermFilter(name="workflow.isReported", value=True)
_NOT_REPORTED_ = Not(_REPORTED_)  # This covers both when the field does not exist and is false


class ReportableFilter(SearchFeature):
    REPORTABLE_FILTER = TermFilter(
        name="workflow.status",
        value=[
            RTS22TransactionStatus.REPORTABLE.value,
            RTS22TransactionStatus.REPORTABLE_USER_OVERRIDE.value,
        ],
    )

    def build_q(self, params=None, *, meta_fields):
        if not params.reportable:
            return
        elif params.reportable is RTS22TransactionStatus.REPORTABLE:
            return self.REPORTABLE_FILTER.build_q(meta_fields=GammaMetaFields)
        return Not(self.REPORTABLE_FILTER).build_q(meta_fields=GammaMetaFields)


class ReportedStateFilter(SearchFeature):
    FILTERS_BY_REPORTED_STATE = {
        Rts22TransactionReportedState.NOT_REPORTED_REPORTABLE: (
            _NOT_REPORTED_
            & TermFilter(
                name="workflow.status",
                value=[
                    RTS22TransactionStatus.REPORTABLE.value,
                    RTS22TransactionStatus.REPORTABLE_USER_OVERRIDE.value,
                ],
            )
            & Not(
                TermFilter(
                    name="workflow.validationStatus", value=RTS22ValidationStatus.FAILED.value
                )
            )
        ),
        Rts22TransactionReportedState.NOT_REPORTED_FAILED_CHECKS: (
            _NOT_REPORTED_
            & TermFilter(
                name="workflow.status",
                value=[
                    RTS22TransactionStatus.REPORTABLE.value,
                    RTS22TransactionStatus.REPORTABLE_USER_OVERRIDE.value,
                ],
            )
            & TermFilter(name="workflow.validationStatus", value=RTS22ValidationStatus.FAILED.value)
        ),
        Rts22TransactionReportedState.NOT_REPORTED_NON_REPORTABLE: (
            _NOT_REPORTED_
            & (
                TermFilter(
                    name="workflow.status",
                    value=[
                        RTS22TransactionStatus.NON_REPORTABLE.value,
                        RTS22TransactionStatus.NON_REPORTABLE_USER_OVERRIDE.value,
                    ],
                )
            )
        ),
        Rts22TransactionReportedState.REPORTED_ARM_STATUS_ACCEPTED: (
            _REPORTED_
            & TermFilter(
                name="workflow.arm.status",
                value=[
                    RTS22ExternalStatus.ACCEPTED.value,
                    RTS22ExternalStatus.REJECTION_CLEARED.value,
                ],
            )
        ),
        Rts22TransactionReportedState.REPORTED_ARM_STATUS_REJECTED: (
            _REPORTED_
            & TermFilter(name="workflow.arm.status", value=RTS22ExternalStatus.REJECTED.value)
        ),
        Rts22TransactionReportedState.REPORTED_ARM_STATUS_PENDING: (
            _REPORTED_
            & TermFilter(
                name="workflow.arm.status",
                value=[
                    RTS22ExternalStatus.PENDING.value,
                    RTS22ExternalStatus.SUBMITTED.value,
                ],
            )
        ),
        Rts22TransactionReportedState.REPORTED_NCA_STATUS_ACCEPTED: (
            _REPORTED_
            & TermFilter(
                name="workflow.nca.status",
                value=[
                    RTS22ExternalStatus.ACCEPTED.value,
                    RTS22ExternalStatus.REJECTION_CLEARED.value,
                ],
            )
        ),
        Rts22TransactionReportedState.REPORTED_NCA_STATUS_REJECTED: (
            _REPORTED_
            & TermFilter(name="workflow.nca.status", value=RTS22ExternalStatus.REJECTED.value)
        ),
        Rts22TransactionReportedState.REPORTED_NCA_STATUS_PENDING: (
            _REPORTED_
            & TermFilter(
                name="workflow.nca.status",
                value=[
                    RTS22ExternalStatus.PENDING.value,
                    RTS22ExternalStatus.SUBMITTED.value,
                ],
            )
        ),
    }

    def build_q(self, params=None, *, meta_fields):
        if not params.reported_state:
            return
        return Or(*(self.FILTERS_BY_REPORTED_STATE[rs] for rs in params.reported_state)).build_q(
            meta_fields=GammaMetaFields
        )


class StatusFilter(SearchFeature):
    @dataclass
    class Config(SearchFeatureConfig):
        status: Optional[List[RTS22TransactionStatus]] = NOT_SET
        status_param: Optional[str] = "status"

    config: Config

    def build_q(self, params=None, *, meta_fields):
        status = Field(param=self.config.status_param, value=self.config.status).extract_value(
            params
        )
        if status:
            return TermFilter(name="workflow.status", value=status).build_q(
                meta_fields=GammaMetaFields
            )


class ValidationStatusFilter(SearchFeature):
    @dataclass
    class Config(SearchFeatureConfig):
        status: Optional[RTS22ValidationStatus] = NOT_SET
        status_param: Optional[str] = "validation_status"

    config: Config

    def build_q(self, params=None, *, meta_fields):
        status = Field(param=self.config.status_param, value=self.config.status).extract_value(
            params
        )
        if status:
            return TermFilter(name="workflow.validationStatus", value=status).build_q(
                meta_fields=GammaMetaFields
            )


class RejectedTransactionsFilter(SearchFeature):
    @dataclass
    class Config(SearchFeatureConfig):
        all: Optional[bool] = NOT_SET
        all_param: Optional[str] = "all_rejections"
        authority: Optional[ReportingAuthority] = NOT_SET
        authority_param: Optional[str] = "authority"

    config: Config

    def build_q(self, params=None, *, meta_fields):
        all = Field(param=self.config.all_param, value=self.config.all).extract_value(params)
        if all:
            value = [
                RTS22ExternalStatus.REJECTED.value,
                RTS22ExternalStatus.REJECTION_CLEARED.value,
            ]
        else:
            value = RTS22ExternalStatus.REJECTED.value

        authority = Field(
            param=self.config.authority_param, value=self.config.authority
        ).extract_value(params)
        if authority:
            return TermFilter(name=f"workflow.{authority.value}.status", value=value).build_q(
                meta_fields=GammaMetaFields
            )
        else:
            return (
                TermFilter(name="workflow.arm.status", value=value)
                | TermFilter(name="workflow.nca.status", value=value)
            ).build_q(meta_fields=meta_fields)


RTS22_TRANSACTION_FLANG_FILTER = FlangFilter.simple(
    nested_paths=[
        "&validationErrors",
        "workflow.arm.checks",
        "workflow.nca.checks",
        "reconciliation.fieldBreaks",
    ],
)


class Rts22TransactionSearchBase(SearchModel):
    class Params(SearchModelParams):
        f: Optional[str]
        start: Union[dt.datetime, dt.date] = None
        end: Union[dt.datetime, dt.date] = None
        reported_state: Optional[List[Rts22TransactionReportedState]] = None
        status: Optional[List[RTS22TransactionStatus]] = None
        validation_status: Optional[RTS22ValidationStatus] = None
        search: str = None

    params: Params

    features = [
        ModelFilter(model=Rts22Transaction),
        NotExpired,
        RTS22_TRANSACTION_FLANG_FILTER,
        RangeFilter(field="transactionDetails.tradingDateTime"),
        ReportedStateFilter,
        StatusFilter,
        ValidationStatusFilter,
        Or(
            *get_free_text_search_filters(
                field_mapping=RTS22_TRANSACTION_NESTED_NON_NESTED_MAPPING, qs_param="search"
            )
        ),
    ]

    default_sort_order = ["transactionDetails.tradingDateTime"]

    sort_handlers = {
        "numValidationErrors": lambda sort: {
            "_script": {
                "type": "number",
                "script": """
                    if (!params["_source"].containsKey("&validationErrors")) return 0;
                    return params["_source"]["&validationErrors"].size();
                """,
                "order": sort.order,
            }
        },
    }


class Rts22TransactionSearch(Rts22TransactionSearchBase):
    class Params(Rts22TransactionSearchBase.Params):
        keys: Optional[List[str]]
        message: Optional[str]
        transaction_ref_no: Optional[str] = None

    params: Params

    features = Rts22TransactionSearchBase.features + [
        Nested(
            TermFilter(param="message", name="&validationErrors.message"), path="&validationErrors"
        ),
        TermFilter(param="transaction_ref_no", name="reportDetails.transactionRefNo"),
    ]


class Rts22RejectedTransactionSearch(Rts22TransactionSearchBase):
    class Params(Rts22TransactionSearchBase.Params):
        all_rejections: Optional[bool] = None
        authority: Optional[ReportingAuthority] = None

    params: Params

    features = Rts22TransactionSearchBase.features + [
        RejectedTransactionsFilter,
    ]


class Rts22TransactionTrendsTimelineAgg(Rts22TransactionSearchBase):
    class Params(Rts22TransactionSearchBase.Params):
        interval: Optional[str]

    params: Params

    def build_aggs(self) -> Optional[Dict]:
        if self.params.start and self.params.end and not self.params.interval:
            interval = f"{get_histogram_interval(start=self.params.start, end=self.params.end)}ms"
        else:
            interval = self.params.interval or "1d"
        return {
            "TIMES": {
                "date_histogram": {
                    "field": "transactionDetails.tradingDateTime",
                    "fixed_interval": interval,
                    "format": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
                },
            }
        }


class Rts22TransactionsSubmissionsAgg(Rts22TransactionSearchBase):
    class Params(Rts22TransactionSearchBase.Params):
        all_rejections: Optional[bool] = False

    params: Params

    def build_aggs(self) -> Optional[Dict]:
        if self.params.all_rejections:
            value = [
                RTS22ExternalStatus.REJECTED.value,
                RTS22ExternalStatus.REJECTION_CLEARED.value,
            ]
        else:
            value = RTS22ExternalStatus.REJECTED.value
        return {
            "ARM_REJECTIONS": {
                "filter": TermFilter(name="workflow.arm.status", value=value).to_dict(
                    meta_fields=GammaMetaFields
                ),
            },
            "ARM_SUBMISSIONS": {
                "filter": Exists(field="workflow.arm.status").to_dict(meta_fields=GammaMetaFields),
            },
            "NCA_REJECTIONS": {
                "filter": TermFilter(name="workflow.nca.status", value=value).to_dict(
                    meta_fields=GammaMetaFields
                ),
            },
            "NCA_SUBMISSIONS": {
                "filter": Exists(field="workflow.nca.status").to_dict(meta_fields=GammaMetaFields),
            },
        }


class Rts22TransactionsRejectionsTimelineAgg(Rts22TransactionSearchBase):
    class Params(Rts22TransactionSearchBase.Params):
        all_rejections: Optional[bool] = None
        interval: Optional[str]
        buckets: Optional[int]

    params: Params

    @cached_property
    def interval_in_millis(self) -> int:
        return get_interval_in_millis_from_params(
            self.params, min_interval=15 * 60 * 1000
        )  # 15 minutes

    def build_aggs(self) -> Optional[Dict]:
        _DATES_AGG = {
            "date_histogram": {
                "field": "transactionDetails.tradingDateTime",
                "fixed_interval": f"{self.interval_in_millis}ms",
                "format": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
            },
        }

        if self.params.all_rejections:
            value = [
                RTS22ExternalStatus.REJECTED.value,
                RTS22ExternalStatus.REJECTION_CLEARED.value,
            ]
        else:
            value = RTS22ExternalStatus.REJECTED.value

        return {
            "arm": {
                "filter": TermFilter(name="workflow.arm.status", value=value).to_dict(
                    meta_fields=GammaMetaFields
                ),
                "aggs": {"DATES": _DATES_AGG},
            },
            "armSubmissions": {
                "filter": Exists(field="workflow.arm.status").to_dict(meta_fields=GammaMetaFields),
                "aggs": {"DATES": _DATES_AGG},
            },
            "nca": {
                "filter": TermFilter(name="workflow.nca.status", value=value).to_dict(
                    meta_fields=GammaMetaFields
                ),
                "aggs": {"DATES": _DATES_AGG},
            },
            "ncaSubmissions": {
                "filter": Exists(field="workflow.nca.status").to_dict(meta_fields=GammaMetaFields),
                "aggs": {"DATES": _DATES_AGG},
            },
        }


class Rts22TransactionsRejectionsCategoryAgg(Rts22RejectedTransactionSearch):
    features = Rts22RejectedTransactionSearch.features + [RejectedTransactionsFilter]

    def build_aggs(self):
        return {
            "ARM_CHECKS": {
                "nested": {"path": "workflow.arm.checks"},
                "aggs": {
                    "CATEGORY": {
                        "terms": {
                            "field": "workflow.arm.checks.ruleId",
                            "size": ES_MAX_AGG_SIZE,
                        },
                        "aggs": {
                            "TRANSACTIONS": {"reverse_nested": {}},
                        },
                    },
                },
            },
            "NCA_CHECKS": {
                "nested": {"path": "workflow.nca.checks"},
                "aggs": {
                    "CATEGORY": {
                        "terms": {
                            "field": "workflow.nca.checks.ruleId",
                            "size": ES_MAX_AGG_SIZE,
                        },
                        "aggs": {
                            "TRANSACTIONS": {"reverse_nested": {}},
                        },
                    },
                },
            },
        }


class Rts22TransactionsRejectionsMessageAgg(Rts22RejectedTransactionSearch):
    features = Rts22RejectedTransactionSearch.features + [RejectedTransactionsFilter]

    def build_aggs(self):
        return {
            "ARM_CHECKS": {
                "nested": {"path": "workflow.arm.checks"},
                "aggs": {
                    "MESSAGE": {
                        "terms": {
                            "field": "workflow.arm.checks.errorText",
                            "size": ES_MAX_AGG_SIZE,
                        },
                        "aggs": {"TRANSACTIONS": {"reverse_nested": {}}},
                    }
                },
            },
            "NCA_CHECKS": {
                "nested": {"path": "workflow.nca.checks"},
                "aggs": {
                    "MESSAGE": {
                        "terms": {
                            "field": "workflow.nca.checks.errorText",
                            "size": ES_MAX_AGG_SIZE,
                        },
                        "aggs": {"TRANSACTIONS": {"reverse_nested": {}}},
                    }
                },
            },
        }


class Rts22TransactionsWithValidationErrorsSearch(Rts22TransactionSearchBase):
    class Params(Rts22TransactionSearchBase.Params):
        search: Optional[str] = None
        category: Optional[str]
        message: Optional[str]
        code: Optional[str]
        field_path: Optional[str]
        reportable: str = RTS22TransactionStatus.REPORTABLE

    params: Params

    features = Rts22TransactionSearchBase.features + [
        Nested(
            Exists(field="&validationErrors"),
            TermFilter(param="category", name="&validationErrors.category"),
            TermFilter(param="message", name="&validationErrors.message"),
            TermFilter(param="code", name="&validationErrors.code"),
            TermFilter(param="field_path", name="&validationErrors.fieldPath"),
            path="&validationErrors",
        ),
        ReportableFilter,
        _NOT_REPORTED_,
    ]


class Rts22TransactionsWithValidationErrorsTimelineAgg(Rts22TransactionsWithValidationErrorsSearch):
    class Params(Rts22TransactionsWithValidationErrorsSearch.Params):
        interval: Optional[str]
        buckets: Optional[int] = None

    params: Params

    @cached_property
    def interval_in_millis(self) -> int:
        return get_interval_in_millis_from_params(
            self.params, min_interval=15 * 60 * 1000
        )  # 15 minutes

    def build_aggs(self):
        return {
            "TIMES": {
                "date_histogram": {
                    "field": "transactionDetails.tradingDateTime",
                    "fixed_interval": f"{self.interval_in_millis}ms",
                    "format": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
                },
            }
        }


class Rts22TransactionsWithValidationErrorsAgg(Rts22TransactionSearchBase):
    class Params(Rts22TransactionSearchBase.Params):
        reportable: str = RTS22TransactionStatus.REPORTABLE

    params: Params

    features = Rts22TransactionSearchBase.features + [
        Nested(Exists(field="&validationErrors"), path="&validationErrors"),
        ReportableFilter,
        _NOT_REPORTED_,
    ]

    def build_aggs(self) -> Optional[Dict]:
        return {
            "CHECKS": {
                "nested": {"path": "&validationErrors"},
                "aggs": {
                    "CATEGORIES": {
                        "terms": {
                            "field": "&validationErrors.category",
                            "size": ES_MAX_AGG_SIZE,
                        },
                        "aggs": {
                            "TRANSACTIONS": {"reverse_nested": {}},
                            "CODES": {
                                "terms": {
                                    "field": "&validationErrors.code",
                                    "size": ES_MAX_AGG_SIZE,
                                },
                                "aggs": {
                                    "TRANSACTIONS": {"reverse_nested": {}},
                                    "MESSAGES": {
                                        "terms": {
                                            "field": "&validationErrors.message",
                                            "size": ES_MAX_AGG_SIZE,
                                        },
                                        "aggs": {
                                            "TRANSACTIONS": {"reverse_nested": {}},
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            }
        }


class Rts22TransactionsRepository(RepoHelpersMixin):
    async def get_transaction(self, id, **params) -> Rts22Transaction:
        return await self.get_one(record_model=Rts22Transaction, id=id, **params)

    async def get_transactions(self, hit_deserializer=NOT_SET, **params) -> RawResult:
        return await self.get_many(
            record_model=Rts22Transaction,
            search_model=Rts22TransactionSearch(**params),
            hit_deserializer=hit_deserializer,
        )

    async def get_transactions_comments(self, transaction_ids: List[str], **params):
        return await self.get_many(
            record_model=UserComment,
            where=[
                ModelFilter(model=UserComment),
                NotExpired,
                TermFilter(name="linkModel", value=Rts22Transaction.__config__.model_name),
                TermFilter(name="linkId", value=transaction_ids),
            ],
            **params,
        )

    async def get_failing_transactions(self, *, hit_deserializer=NOT_SET, **params) -> RawResult:
        return await self.get_many(
            record_model=Rts22Transaction,
            search_model=Rts22TransactionsWithValidationErrorsSearch(
                model=Rts22Transaction,
                **params,
            ),
            hit_deserializer=hit_deserializer,
        )

    async def get_failing_transactions_count(self, **params) -> int:
        return await self.get_count(
            index=self.index_for_record_model(Rts22Transaction),
            search_model=Rts22TransactionsWithValidationErrorsSearch(
                model=Rts22Transaction,
                **params,
            ),
        )

    async def get_failing_transaction_subcategories_by_validation_error_code(
        self, validation_code: str, **params
    ):
        code = params.get("code", None)
        if code:
            params.pop("code")

        results = await self.get_aggs(
            search_model=Rts22TransactionsWithValidationErrorsSearch(
                code=validation_code,
                **params,
            ),
            aggs={
                "marketIdentifiers": {
                    "nested": {"path": "marketIdentifiers"},
                    "aggs": {
                        "filterLabels": {
                            "filter": {
                                "term": {
                                    "marketIdentifiers.path": f"{VALIDATION_ERROR_CODES.get(validation_code)[1]}"  # noqa: E501
                                }
                            },
                            "aggs": {
                                "groupLabels": {
                                    "terms": {
                                        "field": "marketIdentifiers.labelId",
                                        "size": ES_MAX_AGG_SIZE,
                                    }
                                }
                            },
                        }
                    },
                }
            },
        )

        subcategories = []
        total_count = results.hits.total
        subcategories_count = 0

        for item in results.iter_raw_bucket_agg("marketIdentifiers.filterLabels.groupLabels"):
            subcategories.append(
                {
                    "key": f"{VALIDATION_ERROR_CODES.get(validation_code)[0]}: '{item.get('key')}'",
                    "count": item.get("doc_count"),
                }
            )
            subcategories_count += item.get("doc_count")

        missing_count = total_count - subcategories_count
        if missing_count:
            subcategories.append(
                {
                    "key": "-",
                    "count": missing_count,
                }
            )

        return subcategories

    async def get_failing_transactions_timeline(
        self,
        start: dt.datetime = None,
        end: dt.datetime = None,
        buckets=None,
        interval=None,
        **params,
    ):
        if not start:
            start = await self.get_min_value(
                search_model_cls=Rts22TransactionsWithValidationErrorsSearch,
                field="transactionDetails.tradingDateTime",
                as_string=True,
                end=end,
                **params,
            )
        if not end:
            end = dt.datetime.utcnow().replace(tzinfo=dt.timezone.utc)

        search_model = Rts22TransactionsWithValidationErrorsTimelineAgg(
            start=start,
            end=end,
            buckets=buckets,
            interval=interval,
            **params,
        )
        result = await self.get_aggs(search_model=search_model)

        return {
            "start": parse_datetime(start).isoformat() if start else None,
            "end": parse_datetime(end).isoformat() if end else None,
            "intervalInMillis": search_model.interval_in_millis,
            "buckets": [
                {
                    "timestamp": bucket["key"],
                    "datetime": bucket["key_as_string"],
                    "count": bucket["doc_count"],
                }
                for bucket in result.iter_raw_bucket_agg("TIMES")
            ],
        }

    async def get_rejected_transactions(self, *, hit_deserializer=NOT_SET, **params) -> RawResult:
        return await self.get_many(
            record_model=Rts22Transaction,
            search_model=Rts22RejectedTransactionSearch(
                model=Rts22Transaction,
                **params,
            ),
            hit_deserializer=hit_deserializer,
        )

    async def get_rejections_count(self) -> int:
        return await self.get_count(
            index=self.index_for_record_model(Rts22Transaction),
            search_model=Rts22RejectedTransactionSearch(all_rejections=False),
        )

    async def get_workflow_summary(self, **search_params) -> Dict:
        result = await self.get_aggs(
            search_model_cls=Rts22TransactionSearchBase,
            aggs={
                k.value: {"filter": v.to_dict(meta_fields=GammaMetaFields)}
                for k, v in ReportedStateFilter.FILTERS_BY_REPORTED_STATE.items()
            },
            **search_params,
        )
        return {k: v["doc_count"] for k, v in result.aggregations.items()}

    async def get_trends_summary_by_type(
        self,
        trend_chart: TrendChart,
        search_model_cls=Rts22TransactionSearchBase,
        **search_params,
    ):
        field, nested_path = TREND_CHART_SPEC[trend_chart]
        if nested_path:
            aggs = {
                "NESTED": {
                    "nested": {"path": nested_path},
                    "aggs": {"SUBJECTS": {"terms": {"field": field, "size": 5}}},
                }
            }
        else:
            aggs = {"SUBJECTS": {"terms": {"field": field, "size": 5}}}

        result = await self.get_aggs(
            search_model_cls=search_model_cls,
            aggs=aggs,
            **search_params,
        )
        return list(result.iter_bucket_agg("NESTED.SUBJECTS" if nested_path else "SUBJECTS"))

    async def get_trends_timeline(self, **search_params):
        result = await self.get_aggs(
            search_model_cls=Rts22TransactionTrendsTimelineAgg, **search_params
        )
        return list(result.iter_bucket_agg("TIMES", key_as="datetime", key_str="key_as_string"))

    async def get_rejections_summary(self, **search_params):
        result = await self.get_aggs(
            search_model_cls=Rts22TransactionsSubmissionsAgg,
            **search_params,
        )
        return {
            "arm": nested_dict_get(result.aggregations, "ARM_REJECTIONS.doc_count") or 0,
            "nca": nested_dict_get(result.aggregations, "NCA_REJECTIONS.doc_count") or 0,
            "armSubmissions": nested_dict_get(result.aggregations, "ARM_SUBMISSIONS.doc_count")
            or 0,
            "ncaSubmissions": nested_dict_get(result.aggregations, "NCA_SUBMISSIONS.doc_count")
            or 0,
        }

    async def get_rejections_summary_by_category(self, **search_params):
        result = await self.get_aggs(
            search_model_cls=Rts22TransactionsRejectionsCategoryAgg,
            **search_params,
        )

        counts = collections.Counter()
        for item in result.iter_raw_bucket_agg("ARM_CHECKS.CATEGORY"):
            counts[item["key"]] += item["TRANSACTIONS"]["doc_count"]
        for item in result.iter_raw_bucket_agg("NCA_CHECKS.CATEGORY"):
            counts[item["key"]] += item["TRANSACTIONS"]["doc_count"]

        return [{"key": k, "count": v} for k, v in counts.most_common(10)]

    async def get_rejections_summary_by_message(self, **search_params):
        result = await self.get_aggs(
            search_model_cls=Rts22TransactionsRejectionsMessageAgg,
            **search_params,
        )

        counts = collections.Counter()
        for item in result.iter_raw_bucket_agg("ARM_CHECKS.MESSAGE"):
            counts[item["key"]] += item["TRANSACTIONS"]["doc_count"]
        for item in result.iter_raw_bucket_agg("NCA_CHECKS.MESSAGE"):
            counts[item["key"]] += item["TRANSACTIONS"]["doc_count"]

        return [{"key": k, "count": v} for k, v in counts.most_common(10)]

    async def get_rejections_timeline(
        self,
        start: dt.datetime = None,
        end: dt.datetime = None,
        buckets=None,
        interval=None,
        **search_params,
    ):
        if not start:
            start = await self.get_min_value(
                search_model_cls=Rts22RejectedTransactionSearch,
                field="transactionDetails.tradingDateTime",
                as_string=True,
                end=end,
                **search_params,
            )
        if not end:
            end = dt.datetime.utcnow().replace(tzinfo=dt.timezone.utc)

        search_model = Rts22TransactionsRejectionsTimelineAgg(
            start=start,
            end=end,
            buckets=buckets,
            interval=interval,
            **search_params,
        )
        result = await self.get_aggs(search_model=search_model)

        counts = collections.defaultdict(collections.Counter)
        for cat_key, cat_agg in result.aggregations.items():
            for date_bucket in cat_agg["DATES"]["buckets"]:
                counts[date_bucket["key"], date_bucket["key_as_string"]][cat_key] = date_bucket[
                    "doc_count"
                ]

        counts_keys = ("nca", "ncaSubmissions", "arm", "armSubmissions")
        return {
            "start": parse_datetime(start).isoformat() if start else None,
            "end": parse_datetime(end).isoformat() if end else None,
            "intervalInMillis": search_model.interval_in_millis,
            "buckets": [
                {
                    "timestamp": k[0],
                    "datetime": k[1],
                    **{ck: counts[k].get(ck, 0) for ck in counts_keys},
                }
                for k in sorted(counts.keys())
            ],
        }

    async def count_transactions(self, **params):
        return await self.repo.get_count(
            search_model=Rts22TransactionSearch(**params),
            index=self.index_for_record_model(Rts22Transaction),
        )

    async def scroll_transactions(self, size=None, **params):
        return [
            record
            async for record in self.repo.execute_scan(
                search_model=Rts22TransactionSearch(**params),
                index=self.index_for_record_model(Rts22Transaction),
                size=size,
            )
        ]

    async def get_failed_transaction_summary(self, validation_status, **params):
        raw_result = await self.get_aggs(
            record_model=Rts22Transaction,
            search_model=Rts22TransactionsWithValidationErrorsAgg(
                model=Rts22Transaction,
                validation_status=validation_status,
                **params,
            ),
        )

        results = []
        for cat_bucket in raw_result.iter_raw_bucket_agg("CHECKS.CATEGORIES"):
            for code_bucket in cat_bucket["CODES"]["buckets"]:
                for msg_bucket in code_bucket["MESSAGES"]["buckets"]:
                    results.append(
                        {
                            "message": msg_bucket["key"],
                            "code": code_bucket["key"],
                            "category": cat_bucket["key"],
                            "count": msg_bucket["TRANSACTIONS"]["doc_count"],
                        }
                    )
                if not code_bucket["MESSAGES"]["buckets"]:
                    results.append(
                        {
                            "message": None,
                            "code": code_bucket["key"],
                            "category": cat_bucket["key"],
                            "count": code_bucket["TRANSACTIONS"]["doc_count"],
                        }
                    )
            if not cat_bucket["CODES"]["buckets"]:
                results.append(
                    {
                        "message": None,
                        "code": None,
                        "category": cat_bucket["key"],
                        "count": cat_bucket["TRANSACTIONS"]["doc_count"],
                    }
                )

        return results

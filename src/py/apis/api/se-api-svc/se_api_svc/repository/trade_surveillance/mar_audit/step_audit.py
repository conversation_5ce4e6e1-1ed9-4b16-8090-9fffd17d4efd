# type: ignore
from api_sdk.models.search import Sort
from se_api_svc.repository.trade_surveillance.mar_audit.simple_generator import (
    _search_tables,
    _special_fields,
)
from se_api_svc.schemas.trade_surveillance.mar_audit.step_audit import StepAuditsPaginatedResponse
from sql_query_builder.base import ModelSearch
from sql_query_builder.features import (
    BaseFeature,
    FilterFeature,
    JoinFeature,
    QueryString,
    RangeFilter,
    SelectFeature,
)
from sql_query_builder.flang.base import FlangFeature
from sql_query_builder.params import SearchModelParams
from sql_query_builder.repository.mixin import RepoHelpersMixin
from sqlalchemy import text
from tenant_db.declarative_base import Base
from tenant_db.models.mar_audit.aggregated_step_audit import AggregatedStepAudit
from tenant_db.models.mar_audit.market_abuse_audit import MarketAbuseAudit
from tenant_db.models.mar_audit.step_audit import StepAudit
from typing import Any, Optional


class StepAuditSchemaModel(ModelSearch):
    class Params(SearchModelParams):
        f: Optional[str]
        start: Optional[Any]
        end: Optional[Any]
        market_abuse_audit_id: Optional[str]
        watch_type: Optional[str] = "ON_DEMAND"
        query_alias: str = "sa"
        field_: Optional[str]
        select_field: bool = False

    params: Params
    id_field: str = "marketAbuseAuditId"

    @property
    def features(self):
        return [
            (
                SelectFeature(
                    text(
                        f"""
                    "{self.params.field_}" FROM {self.params.schema_}."StepAudit" sa
                    JOIN {self.params.schema_}."MarketAbuseAudit" ON
                    {self.params.schema_}."MarketAbuseAudit"."id" = "sa"."marketAbuseAuditId"
                    AND ({self.params.schema_}."MarketAbuseAudit"."watchType" NOT IN ('ON_DEMAND'))
                """
                    ),
                    base_table="StepAudit",
                )
                if self.params.select_field
                else SelectFeature(
                    text(
                        f'{self.params.schema_}."StepAudit"."'
                        f'{self.params.field_}" as "{self.params.field_}"'
                    ),
                    StepAudit.marketAbuseAuditId,
                    StepAudit.aggregatedStepAuditId,
                    base_table="StepAudit",
                )
            ),
            JoinFeature(
                AggregatedStepAudit,
                text(f'"{self.params.query_alias}"."aggregatedStepAuditId"')
                == AggregatedStepAudit.id,
                optional=True,
            ),
            JoinFeature(
                MarketAbuseAudit,
                text(f'"{self.params.query_alias}"."marketAbuseAuditId"') == MarketAbuseAudit.id,
                optional=True,
            ),
            FlangFeature.simple(Base, "f", _special_fields, _search_tables=_search_tables),
            RangeFilter(field=StepAudit.createdDateTime),
        ]


class StepAuditSearch(ModelSearch):
    class Params(SearchModelParams):
        f: Optional[str]
        start: Optional[Any]
        end: Optional[Any]
        execution_id: Optional[str]
        aggregated_step_audit_id: Optional[str]

    params: Params

    default_sort = [Sort(field="createdDateTime", order=Sort.Order.asc)]

    features = [
        BaseFeature(model=StepAudit),
        FilterFeature(field=StepAudit.aggregatedStepAuditId, param="aggregated_step_audit_id"),
        FilterFeature(field=StepAudit.marketAbuseAuditId, param="execution_id"),
        FlangFeature.simple(Base, "f", _special_fields, _search_tables),
        RangeFilter(field=StepAudit.createdDateTime),
        QueryString(_search_tables=["StepAudit"]),
    ]


class StepAuditRepo(RepoHelpersMixin):
    model = StepAudit

    async def execute_sql_query_builder(self, **kwargs) -> StepAuditsPaginatedResponse:
        results, headers = await self.get_many(
            search_model=StepAuditSearch, meta=Base, distinct=True, **kwargs
        )
        return StepAuditsPaginatedResponse(header=headers, results=results)

import datetime as dt
import itertools
import logging
import math
from api_sdk.di.request import ReqDep
from api_sdk.exceptions import BadInput
from api_sdk.messages.fastapi_message_bus import FastApiMessageBus
from api_sdk.models.request_params import NonPaginatedDatedListParams, PaginatedDatedListParams
from api_sdk.models.search import (
    CustomPagination,
    PageParams,
    Pagination,
    SearchResult,
    SearchResultHeader,
    Sort,
)
from api_sdk.utils.intervals import TimeInterval
from api_sdk.utils.utils import (
    b64decode_urlsafe_id,
    decode_search_after,
    encode_search_after,
    nested_dict_get,
    nested_get,
    parse_datetime,
)
from fastapi import APIRouter, Depends, Query, Request
from se_api_svc.domain.chat_start_end import compute_start_end_messages
from se_api_svc.messages.comms.events import CommsAroundViewed, CommsRecordViewed
from se_api_svc.repository.comms.chat import ChatRepository, get_room_without_thread_messages
from se_api_svc.schemas.comms.chat import (
    Chat<PERSON><PERSON>,
    ChatSummaryByCompanyItem,
    ChatSummaryBySenderRecipient,
    ChatSummaryByTimeItem,
    Message,
    MessageRoomResponse,
    MessagesResponse,
)
from typing import Dict, List, Optional, Union

log = logging.getLogger(__name__)

router = APIRouter()


def _get_message_sort(message: Message):
    return [
        int(
            parse_datetime(nested_get(message, "timestamps.timestampStart"))
            .replace(tzinfo=dt.timezone.utc)
            .timestamp()
            * 1000
        ),
        message.id_,
    ]


def _decode_message_id(encoded: str):
    try:
        return b64decode_urlsafe_id(
            encoded
        )  # Add fallback=True if you want to use the unencoded message ids too
    except Exception:
        raise BadInput(msg="Incorrectly encoded message id", loc=["encoded_message_id"])


@router.get(
    path="/messages/slack-channels",
    name="comms:chat-room:get-slack-channels",
    responses={
        200: {
            "content": {
                "application/json": {
                    "example": [
                        {"id": "G01FAV85STX", "name": "product-dawgs", "count": 147},
                        {"id": "C04DX1D9JLX", "name": "slack-test", "count": 54},
                        {"id": "C048HSJ63PZ", "name": "initiative-slack", "count": 42},
                    ]
                }
            },
        },
        404: {
            "description": "Not Found",
            "content": {"application/json": {"example": {"detail": [{"detail": "Not Found"}]}}},
        },
    },
)
async def get_slack_channels(
    params: NonPaginatedDatedListParams = Depends(),
    repo: ChatRepository = ReqDep(ChatRepository),
):
    """Get list of all the rooms with roomId, roomName and total count of
    messages."""
    return await repo.get_all_room_info(source="Slack Chat", **params.as_search_kwargs())


@router.get(
    path="/messages/{encoded_message_id}",
    name="comms:chat-room:get-message-room",
    response_model=MessageRoomResponse,
)
async def get_message(
    encoded_message_id: str,
    params: NonPaginatedDatedListParams = Depends(),
    repo: ChatRepository = ReqDep(ChatRepository),
    mb: FastApiMessageBus = Depends(),
):
    """Get message's room summary.

    Search params are only used for count.
    """
    message_id = _decode_message_id(encoded_message_id)

    message = await repo.get_message(message_id)
    room_id = nested_get(message, "roomId")

    first_message = await repo.get_room_first_message(room_id=room_id)

    num_messages = await repo.get_num_messages(room_id=room_id, **params.as_search_kwargs())

    await mb.publish(CommsRecordViewed(communication_record=message))
    return MessageRoomResponse(
        roomId=room_id,
        roomName=first_message.roomName,
        roomSource=nested_get(first_message, "metadata.source.client"),
        chatType=first_message.chatType,
        count=num_messages,
        createdBy=nested_get(first_message, "identifiers.fromId"),
        createdOn=first_message.timestamps.timestampStart,
    )


@router.get(
    path="/messages/{encoded_message_id}/around",
    name="comms:chat-room:get-messages-around",
    response_model=MessagesResponse,
)
async def get_messages_around_message(
    encoded_message_id: str,
    params: NonPaginatedDatedListParams = Depends(),
    companies: Optional[List[str]] = Query(None),
    except_companies: Optional[List[str]] = Query(None),
    people: Optional[List[str]] = Query(None),
    except_people: Optional[List[str]] = Query(None),
    take: int = 50,
    searchBefore: str = None,
    searchAfter: str = None,
    excludeChatEvents: bool = False,
    search_after_message: bool = Query(
        False,
        alias="searchAfterMessage",
        description="When client wants records after given message ID",
    ),
    minimal: bool = Query(
        False,
        description="If true, returns only essential fields for the download in the response.",
    ),
    repo: ChatRepository = ReqDep(ChatRepository),
    mb: FastApiMessageBus = Depends(),
):
    """Get messages around a particular message. Only one of searchBefore and
    searchAfter can be specified at a time.

    In general, this endpoint should NOT be used for filtering.
    When filters are used, it is recommended to use the plain room messages listing.

    The number of returned records will not match "take" for multiple reasons, but primarily
    because ChatEvents are added to the result set. Also, on the first page, if the message
    is very early in the chat history or very late,
    the retrieved records will be less than expected.

    Filters will not be applied to the message from which the search originates.
    This origin message is returned as part of other messages in two cases:
    1) if searchBefore and searchAfter are not supplied - it will then be returned even if
       the message does not match filters (search, f, start, end, etc.).
    2) if the message falls on the page delineated by
        searchBefore and searchAfter and matches the filters.

    Filters are not applied on ChatEvents.

    TODO Not all ChatEvents are guaranteed to be returned.

    To calculate the results after the current page, use totalHits - skippedHits - returnedHits.
    """

    message_id = _decode_message_id(encoded_message_id)

    search_params = dict(
        **params.as_search_kwargs(),
        companies=companies,
        except_companies=except_companies,
        people=people,
        except_people=except_people,
        minimal=minimal,
    )

    search_before = decode_search_after(searchBefore)
    search_after = decode_search_after(searchAfter)

    message = await repo.get_message(message_id)
    message_sort = _get_message_sort(message)

    # If this is true, we don't consider searchBefore and searchAfter.
    if search_after_message:
        search_after = message_sort
        search_after_take = take

    elif not search_before and not search_after:
        search_before = message_sort
        search_after = message_sort
        search_before_take = math.floor((take - 1) / 2)
        search_after_take = math.floor(take / 2)

    else:
        search_before_take = take
        search_after_take = take

    before_result = None
    if search_before:
        before_result = await repo.get_room_messages(
            room_id=nested_get(message, "roomId"),
            pagination=CustomPagination(
                take=search_before_take,
                sorts=[
                    Sort(field="timestamps.timestampStart", order=Sort.Order.desc),
                    Sort(field="&id", order=Sort.Order.desc),
                ],
            ),
            search_after=search_before,
            **search_params,
        )

    after_result = None
    if search_after:
        after_result = await repo.get_room_messages(
            room_id=nested_get(message, "roomId"),
            pagination=CustomPagination(
                take=search_after_take,
                sorts=[
                    Sort(field="timestamps.timestampStart", order=Sort.Order.asc),
                    Sort(field="&id", order=Sort.Order.asc),
                ],
            ),
            search_after=search_after,
            **search_params,
        )

    before_hits = before_result.hits.hits if before_result else []
    after_hits = after_result.hits.hits if after_result else []

    if search_before and search_after:
        results = [
            *(reversed(before_hits)),
            message,
            *after_hits,
        ]

    elif search_before:
        results = list(reversed(before_hits))
    else:
        results = after_hits

    # If the Slack Messages
    # Remove the messages which are part of threads in response
    # But keeping the total count of messages as same
    # See EP-4882
    # Todo:
    #  Check is metadata.source.client only way to verify message as a part of Slack Chat,
    #  If it's not generalize and there are other ways -> add with an OR condition
    if nested_dict_get(results[0].metadata, "source.client") == "Slack Chat":
        results = get_room_without_thread_messages(messages=results)

    enhanced_results = results
    chat_events, chat_events_total_count = None, 0
    if results and not excludeChatEvents:
        start_end = compute_start_end_messages(results)

        # When using searchBefore or searchAfter we can use the reference timestamp
        # to ensure that we don't miss any ChatEvent in the beginning of the range.
        # TODO This does NOT solve the issue of ChatEvents missed in the beginning and ending
        # TODO of the entire chat room timeline.
        if before_result and not after_result:
            start_end["end"] = parse_datetime(search_before[0] // 1000)
        elif not before_result and after_result:
            start_end["start"] = parse_datetime(search_after[0] // 1000)

        chat_events_total_count, chat_events = await repo.get_chat_events(
            room_id=nested_get(message, "roomId"),
            pagination=Pagination.construct(take=10_000),
            with_total_count=True,
            start=start_end["start"],
            end=start_end["end"],
        )
        enhanced_results = sorted(
            itertools.chain(results, chat_events),
            key=lambda item: (str(item.timestamps.timestampStart), item.id_),
        )

    total_hits = before_result.hits.total if before_result else after_result.hits.total

    # A far from perfect calculation of skipped hits:
    # count how many messages match the search chronologically BEFORE the first message returned.
    skipped_hits = None
    if (
        not params.f
        and not params.start
        and not params.end
        and len(results) < total_hits
        and results
    ):
        # Filtering + around shouldn't really be supported,
        # but if anyone attempts it we won't calculate
        # this because it will be just confusing.

        # Count how many messages happen in the sorting order before the first message
        # of the results.

        count_params = dict(search_params)
        count_params["before_message_id"] = results[0].id_
        count_params["before_message_timestamp"] = start_end["start"]
        skipped_hits = await repo.get_room_messages(
            room_id=nested_get(message, "roomId"), count=True, **count_params
        )

    # Adding here the message from which the search originates.
    # This is just for the sake of FE, it will mess up counts in response headers.
    if search_after_message:
        enhanced_results = [message, *enhanced_results]

    await mb.publish(CommsAroundViewed(comms_records=enhanced_results, request=params.request))
    return SearchResult(
        header=SearchResultHeader(
            skippedHits=skipped_hits,
            returnedHits=len(enhanced_results),
            totalHits=total_hits + chat_events_total_count
            if not excludeChatEvents and chat_events
            else total_hits,
            previousSearchBefore=encode_search_after(
                _get_message_sort(before_result.hits.hits[-1])
                if before_hits and len(before_hits) == search_before_take
                else (
                    _get_message_sort(after_hits[0]) if not before_result and after_hits else None
                )
            ),
            nextSearchAfter=encode_search_after(
                _get_message_sort(after_hits[-1])
                if after_hits and len(after_hits) == search_after_take
                else (
                    _get_message_sort(before_hits[0]) if not after_result and before_hits else None
                )
            ),
        ),
        results=enhanced_results,
    )


@router.get(
    path="/sub-threads/{sub_thread_id}/messages",
    name="comms:chat-room:get-sub-threads",
    responses={
        200: {
            "content": {
                "application/json": {
                    "example": {
                        "header": {
                            "returnedHits": 1,
                            "totalHits": 4,
                            "skippedHits": 1,
                            "previousSearchBefore": "null",
                            "nextSearchAfter": "blah",
                            "offset": "null",
                        },
                        "results": [
                            {
                                "metadata": {
                                    "messageId": "blah",
                                    "subThreadId": "blah",
                                    "threadId": "blah",
                                },
                                "hasAttachment": "blah",
                                "chatType": "Channel - blah",
                                "body": {"displayText": "blah blah", "type": "HTML"},
                                "sourceKey": "blah",
                            }
                        ],
                    }
                }
            },
        },
        404: {
            "description": "Not Found",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "msg": "Thread with subThreadId C04DX1D9JLX|1674468508.826 "
                                "was not found None does not exist"
                            }
                        ]
                    }
                }
            },
        },
    },
)
async def get_sub_thread_messages(
    sub_thread_id: str,
    page_params: PageParams = Depends(),
    repo: ChatRepository = ReqDep(ChatRepository),
):
    """Get subThread Messages Around The subThreadId ie Main Message."""

    # Since we are going to pop the main thread message from the query result
    page_params = PageParams(
        skip=page_params.skip - 1 if page_params.skip > 1 else page_params.skip,
        take=page_params.take + 1 if page_params.take > 0 else page_params.take,
        sort=page_params.sort if page_params.sort else "timestamps.created:asc",
    )

    sub_thread_messages = await repo.get_thread_messages(
        sub_thread_id=sub_thread_id, pagination=page_params.to_pagination()
    )

    return SearchResult.from_raw_result(sub_thread_messages, skipped_hits=page_params.skip)


class PaginatedMessageSearchParam(PaginatedDatedListParams):
    def __init__(
        self,
        request: Request,
        search: Optional[List[str]] = Query(None),
        f: Optional[str] = None,
        page_params: PageParams = Depends(),
        start: Union[int, dt.date, dt.datetime] = None,
        end: Union[int, dt.date, dt.datetime] = None,
    ):
        super().__init__(
            request=request, search=search, f=f, page_params=page_params, start=start, end=end
        )

    def as_search_kwargs(self, as_model_qs: bool = True) -> Dict:
        return {**super().as_search_kwargs(as_model_qs=as_model_qs)}


class MessageSearchParam(NonPaginatedDatedListParams):
    def __init__(
        self,
        request: Request,
        search: Optional[List[str]] = Query(None),
        f: Optional[str] = None,
        behaviour_id: Optional[str] = Query(None, alias="behaviourId"),
    ):
        super().__init__(request=request, search=search, f=f)
        self.behaviour_id = behaviour_id

    def as_search_kwargs(self, as_model_qs=True) -> Dict:
        return {
            **super().as_search_kwargs(as_model_qs=as_model_qs),
            **({"behaviour_id": self.behaviour_id} if self.behaviour_id else {}),
        }


@router.get(path="/rooms/{room_id}/messages", name="comms:chat-room:get-messages")
async def get_room_messages(
    room_id: str,
    params: PaginatedMessageSearchParam = Depends(),
    companies: Optional[List[str]] = Query(None),
    except_companies: Optional[List[str]] = Query(None),
    people: Optional[List[str]] = Query(None),
    except_people: Optional[List[str]] = Query(None),
    repo: ChatRepository = ReqDep(ChatRepository),
):
    """Get room messages around the particular message."""
    room_id = b64decode_urlsafe_id(room_id, True)

    search_params = dict(
        **params.as_search_kwargs(as_model_qs=False),
        companies=companies,
        except_companies=except_companies,
        people=people,
        except_people=except_people,
    )
    result = await repo.get_room_messages(
        room_id=room_id,
        default_sort=["timestamps.timestampStart:asc"],
        **search_params,
    )
    return SearchResult.from_raw_result(result, skipped_hits=params.page_params.skip)


@router.get(
    path="/rooms/{room_id}/messages/summary/by-time", response_model=List[ChatSummaryByTimeItem]
)
async def get_room_messages_timeline(
    room_id: str,
    interval: Optional[TimeInterval] = None,
    params: MessageSearchParam = Depends(),
    companies: Optional[List[str]] = Query(None),
    except_companies: Optional[List[str]] = Query(None),
    people: Optional[List[str]] = Query(None),
    except_people: Optional[List[str]] = Query(None),
    repo: ChatRepository = ReqDep(ChatRepository),
):
    """Summarize room messages by time.

    interval= should be used only for the counts by date.
    For chart, interval is calculated automatically based on start and end.
    """
    room_id = b64decode_urlsafe_id(room_id, True)

    search_params = dict(
        interval=interval.value if interval else None,
        companies=companies,
        except_companies=except_companies,
        people=people,
        except_people=except_people,
        **params.as_search_kwargs(as_model_qs=False),
    )
    return await repo.get_room_summary_by_time(room_id=room_id, **search_params)


@router.get(
    path="/rooms/{room_id}/messages/summary/by-sender", response_model=ChatSummaryBySenderRecipient
)
async def get_room_messages_by_sender(
    room_id: str,
    params: MessageSearchParam = Depends(),
    companies: Optional[List[str]] = Query(None),
    except_companies: Optional[List[str]] = Query(None),
    people: Optional[List[str]] = Query(None),
    except_people: Optional[List[str]] = Query(None),
    repo: ChatRepository = ReqDep(ChatRepository),
):
    """Count messages by sender and recipient.

    Ideally the function should be called
    get_room_messages_by_sender_and_recipient
    """
    room_id = b64decode_urlsafe_id(room_id, True)

    search_params = dict(
        **params.as_search_kwargs(as_model_qs=False),
        companies=companies,
        except_companies=except_companies,
        people=people,
        except_people=except_people,
    )
    return await repo.get_room_summary_by_people_combined(room_id=room_id, **search_params)


@router.get(
    path="/rooms/{room_id}/messages/summary/by-company",
    response_model=List[ChatSummaryByCompanyItem],
)
async def get_room_messages_by_company(
    room_id: str,
    params: MessageSearchParam = Depends(),
    companies: Optional[List[str]] = Query(None),
    except_companies: Optional[List[str]] = Query(None),
    people: Optional[List[str]] = Query(None),
    except_people: Optional[List[str]] = Query(None),
    repo: ChatRepository = ReqDep(ChatRepository),
):
    """Count messages by a company (counterparty)."""
    room_id = b64decode_urlsafe_id(room_id, True)

    search_params = dict(
        **params.as_search_kwargs(as_model_qs=False),
        companies=companies,
        except_companies=except_companies,
        people=people,
        except_people=except_people,
    )
    return await repo.get_room_summary_by_company(room_id=room_id, **search_params)


@router.get(path="/rooms/summary", response_model=List[ChatRoom])
async def get_rooms(
    params: NonPaginatedDatedListParams = Depends(),
    repo: ChatRepository = ReqDep(ChatRepository),
):
    """List rooms that have messages in the specified time period and count of
    messages in the room for that period."""
    return await repo.get_rooms(**params.as_search_kwargs())

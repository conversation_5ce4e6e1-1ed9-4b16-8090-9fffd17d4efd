# ruff: noqa: E501
import datetime as dt
import logging
from api_sdk.auth import Tenancy
from api_sdk.di.request import ReqDep
from api_sdk.exceptions import BadInput, NotFound
from api_sdk.messages.fastapi_message_bus import FastApiMessageBus
from api_sdk.middleware.module_permission_checker import ModulePermission<PERSON>he<PERSON>
from api_sdk.models.elasticsearch import RawResult, RawResultHits
from api_sdk.models.request_params import NonPaginatedDatedListParams, PaginatedDatedListParams
from api_sdk.models.search import Pagination, SearchResult, apply_sorts, parse_sort_str
from api_sdk.responses import OkResponse
from api_sdk.schemas.module_permission_checker import All
from api_sdk.schemas.static import Module
from api_sdk.utils.intervals import TimeInterval
from api_sdk.utils.utils import StringEnum, nested_get, parse_datetime
from fastapi import APIRouter, Body, Depends, HTTPException, Query
from se_api_svc.messages.surveillance.commands import AlertsLabelsUpdateCommand
from se_api_svc.messages.surveillance.events import AlertsSearched, AlertViewed
from se_api_svc.repository.comms.common import CommsRepository
from se_api_svc.repository.comms.repos import TranscriptsRepository
from se_api_svc.repository.surveillance.alerts import AlertsRepository
from se_api_svc.repository.surveillance.comms_alerts import (
    CommunicationAlertRepository,
    StatusResponseAdatptor,
)
from se_api_svc.repository.user_comments.user_comments import (
    UserCommentsRepository,
    get_comment_link_ids,
)
from se_api_svc.schemas.comms.call import Call
from se_api_svc.schemas.comms.email import Email
from se_api_svc.schemas.comms.meeting import Meeting
from se_api_svc.schemas.surveillance.watches import CommsSurveillanceTrendChart
from se_api_svc.schemas.surveillance.workflow import AlertsLabelsBulkUpdate, WorkflowStatus
from se_api_svc.schemas.track import ModuleTitle
from se_elastic_schema.static.case import ResolutionCategoryCommsEnum
from se_elastic_schema.static.surveillance import AlertHitStatus, WatchQueryType
from se_elastic_schema.static.tenant import ResolutionCategoryTypeVisibility
from starlette.requests import Request
from typing import Dict, List, Optional, Tuple

# from se_api_svc.utils.api import caching_service

log = logging.getLogger(__name__)

router = APIRouter()


class AlertFormat(StringEnum):
    json = "json"
    xlsx = "xlsx"


@router.get(
    path="",
    name="surveillance:alerts:get-alerts",
    summary="Get Alerts",
)
async def get_alerts(
    workflow_status: Optional[WorkflowStatus] = Query(None),
    workflow_statuses: Optional[List[WorkflowStatus]] = Query(None),
    only_ids: Optional[bool] = Query(False, alias="onlyIds"),
    without_content: Optional[bool] = Query(False, alias="withoutContent"),
    unresolved_alerts_assignee_status: Optional[List[str]] = Query(
        None, alias="unresolvedAlertsAssigneeStatus"
    ),
    page_params: PaginatedDatedListParams = Depends(),
    repo: CommunicationAlertRepository = ReqDep(CommunicationAlertRepository),
    comment_repo: UserCommentsRepository = ReqDep(UserCommentsRepository),
    mb: FastApiMessageBus = Depends(),
):
    comment_link_ids = await get_comment_link_ids(
        repo=comment_repo, **page_params.as_search_kwargs()
    )

    result = await repo.get_alerts_with_less_fields(
        workflow_status=workflow_status,
        only_ids=only_ids,
        without_content=without_content,
        unresolved_alerts_assignee_status=unresolved_alerts_assignee_status,
        workflow_statuses=workflow_statuses,
        comment_link_ids=comment_link_ids,
        **page_params.as_search_kwargs(),
    )

    await mb.publish(
        AlertsSearched(
            search_text=page_params.search,
            refine_query=page_params.f,
            request=page_params.request,
        )
    )

    return SearchResult.from_raw_result(
        result,
        skipped_hits=page_params.page_params.skip,
    )


@router.put(
    "/labels/bulk-update",
    name="surveillance:alerts:labels:bulk-update",
    dependencies=[
        Depends(ModulePermissionChecker(All(Module.COMMS_SURVEILLANCE))),
    ],
)
async def labels_bulk_update(
    bulk_request: AlertsLabelsBulkUpdate = Body(...),
    mb: FastApiMessageBus = Depends(),
    tenancy: Tenancy = ReqDep(Tenancy),
):
    if not (bulk_request.alert_ids or bulk_request.scenario_ids):
        raise BadInput(msg="alertIds and scenarioIds empty", loc=["body", "ids"])

    if not bulk_request.update:
        raise BadInput(msg="field empty", loc=["body", "labelsUpdate"])

    await mb.publish(
        AlertsLabelsUpdateCommand(
            alert_ids=bulk_request.alert_ids,
            scenario_ids=bulk_request.scenario_ids,
            update=bulk_request.update,
        )
    )
    return OkResponse(updated=True, labels=bulk_request.update.as_dict())


@router.get(
    path="/summary/by-resolution-category-custom",
    name="surveillance:alerts:summary_by_resolution_category",
    summary="Get Alerts Resolution Category Custom",
)
async def get_alerts_summary_by_resolution_category_custom(
    resolution_category: Optional[ResolutionCategoryCommsEnum] = Query(
        None, alias="resolutionCategory"
    ),
    page_params: NonPaginatedDatedListParams = Depends(),
    repo: CommunicationAlertRepository = ReqDep(CommunicationAlertRepository),
):
    return await repo.get_alerts_summary_of_resolution_category_custom(
        alert_resolution_category=[resolution_category],
        **page_params.as_search_kwargs(),
    )


@router.get(
    path="/summary/by/{field}",
    name="surveillance:alerts:summary_by_field",
    summary="Get cSurv alerts summary by field",
)
async def get_alerts_summary_by_field(
    field: str,
    search: Optional[str] = None,
    f: Optional[str] = None,
    take: Optional[int] = Query(50),
    repo: CommunicationAlertRepository = ReqDep(CommunicationAlertRepository),
):
    return await repo.get_alerts_summary_by_field(
        agg_field=field,
        search=search,
        f=f,
        take=take,
    )


@router.get("/summary/by-status")
# @caching_service.cache(expire="5m")
async def get_comms_alerts_summary_by_status(
    params: NonPaginatedDatedListParams = Depends(),
    person: Optional[List[str]] = Query(None),
    watch_name: Optional[List[str]] = Query(None, alias="watchName"),
    with_sub_statuses: Optional[bool] = Query(False, alias="withSubStatuses"),
    repo: AlertsRepository = ReqDep(AlertsRepository),
    comment_repo: UserCommentsRepository = ReqDep(UserCommentsRepository),
):
    comment_link_ids = await get_comment_link_ids(repo=comment_repo, **params.as_search_kwargs())

    return await repo.get_alerts_summary_by_status(
        module=ModuleTitle.COMMS_SURVEILLANCE,
        person=person,
        watch_name=watch_name,
        with_sub_statuses=with_sub_statuses,
        comment_link_ids=comment_link_ids,
        **params.as_search_kwargs(as_model_qs=False),
    )


@router.get("/summary/by-people")
async def get_comms_alerts_summary_by_people(
    params: NonPaginatedDatedListParams = Depends(),
    watch_name: Optional[List[str]] = Query(None, alias="watchName"),
    alert_status: Optional[List[AlertHitStatus]] = Query(None, alias="alertStatus"),
    alert_resolution_category: Optional[List[str]] = Query(None, alias="resolutionCategory"),
    alert_resolution_sub_category: Optional[List[str]] = Query(None, alias="resolutionSubCategory"),
    take: Optional[int] = Query(10, ge=1, le=250),
    repo: AlertsRepository = ReqDep(AlertsRepository),
    comment_repo: UserCommentsRepository = ReqDep(UserCommentsRepository),
):
    comment_link_ids = await get_comment_link_ids(repo=comment_repo, **params.as_search_kwargs())

    return sorted(
        await repo.get_alerts_summary_by_people(
            module=ModuleTitle.COMMS_SURVEILLANCE,
            watch_name=watch_name,
            alert_status=alert_status,
            alert_resolution_category=alert_resolution_category,
            alert_resolution_sub_category=alert_resolution_sub_category,
            comment_link_ids=comment_link_ids,
            **params.as_search_kwargs(as_model_qs=False),
        ),
        key=lambda r: r["total"],
        reverse=True,
    )[:take]


@router.get("/summary/by-trend/{trend_chart}")
# @caching_service.cache(expire="5m")
async def get_comms_alerts_summary_by_trend(
    trend_chart: CommsSurveillanceTrendChart,
    params: NonPaginatedDatedListParams = Depends(),
    watch_name: Optional[List[str]] = Query(alias="watchName", default=None),
    alert_status: Optional[List[AlertHitStatus]] = Query(alias="alertStatus", default=None),
    alert_resolution_category: Optional[List[str]] = Query(None, alias="resolutionCategory"),
    alert_resolution_sub_category: Optional[List[str]] = Query(None, alias="resolutionSubCategory"),
    take: Optional[int] = Query(10, ge=1, le=250),
    repo: AlertsRepository = ReqDep(AlertsRepository),
    comment_repo: UserCommentsRepository = ReqDep(UserCommentsRepository),
):
    trend_chart = trend_chart.value
    comment_link_ids = await get_comment_link_ids(repo=comment_repo, **params.as_search_kwargs())
    tenant_config = await repo.get_tenant_configuration()
    include_custom = (
        False
        if tenant_config.customResolutionCategories
        and tenant_config.customResolutionCategories.cSurvVisibility
        == ResolutionCategoryTypeVisibility.STEELEYE
        else True
    )

    return sorted(
        await repo.get_alerts_summary_by_trend(
            module=ModuleTitle.COMMS_SURVEILLANCE,
            trend_chart=trend_chart,
            watch_name=watch_name,
            alert_status=alert_status,
            alert_resolution_category=alert_resolution_category,
            alert_resolution_sub_category=alert_resolution_sub_category,
            comment_link_ids=comment_link_ids,
            include_custom=include_custom,
            **params.as_search_kwargs(as_model_qs=False),
        ),
        key=lambda r: r["total"],
        reverse=True,
    )[:take]


@router.get("/summary/by-watch")
# @caching_service.cache(expire="5m")
async def get_comms_alerts_summary_by_watch(
    params: NonPaginatedDatedListParams = Depends(),
    person: Optional[List[str]] = Query(None),
    alert_status: Optional[List[AlertHitStatus]] = Query(None, alias="alertStatus"),
    alert_resolution_category: Optional[List[str]] = Query(None, alias="resolutionCategory"),
    alert_resolution_sub_category: Optional[List[str]] = Query(None, alias="resolutionSubCategory"),
    take: Optional[int] = Query(10, ge=1, le=250),
    repo: AlertsRepository = ReqDep(AlertsRepository),
    comment_repo: UserCommentsRepository = ReqDep(UserCommentsRepository),
):
    comment_link_ids = await get_comment_link_ids(repo=comment_repo, **params.as_search_kwargs())

    return sorted(
        await repo.get_alerts_summary_by_watch(
            module=ModuleTitle.COMMS_SURVEILLANCE,
            person=person,
            alert_status=alert_status,
            alert_resolution_category=alert_resolution_category,
            alert_resolution_sub_category=alert_resolution_sub_category,
            comment_link_ids=comment_link_ids,
            **params.as_search_kwargs(as_model_qs=False),
        ),
        key=lambda r: r["total"],
        reverse=True,
    )[:take]


@router.get("/summary/by-time")
# @caching_service.cache(expire="5m")
async def get_comms_alerts_summary_by_time(
    params: NonPaginatedDatedListParams = Depends(),
    person: Optional[List[str]] = Query(None),
    watch_name: Optional[List[str]] = Query(None, alias="watchName"),
    alert_status: Optional[List[AlertHitStatus]] = Query(None, alias="alertStatus"),
    alert_resolution_category: Optional[List[str]] = Query(None, alias="resolutionCategory"),
    alert_resolution_sub_category: Optional[List[str]] = Query(None, alias="resolutionSubCategory"),
    repo: AlertsRepository = ReqDep(AlertsRepository),
    interval: Optional[TimeInterval] = None,
    buckets: Optional[int] = None,
    comment_repo: UserCommentsRepository = ReqDep(UserCommentsRepository),
):
    comment_link_ids = await get_comment_link_ids(repo=comment_repo, **params.as_search_kwargs())

    return await repo.get_alerts_timeline(
        module=ModuleTitle.COMMS_SURVEILLANCE,
        interval=interval,
        buckets=buckets,
        person=person,
        watch_name=watch_name,
        alert_status=alert_status,
        alert_resolution_category=alert_resolution_category,
        alert_resolution_sub_category=alert_resolution_sub_category,
        comment_link_ids=comment_link_ids,
        **params.as_search_kwargs(as_model_qs=False),
    )


@router.get("/summary/by-work-status")
async def get_alerts_summary_by_work_status(
    params: NonPaginatedDatedListParams = Depends(),
    repo: AlertsRepository = ReqDep(AlertsRepository),
):
    return await repo.get_alerts_summary_by_work_status(
        module=ModuleTitle.COMMS_SURVEILLANCE,
        user_id=repo.tenancy.userId,
        user_name=repo.tenancy.user_name,
        **params.as_search_kwargs(),
    )


@router.get(
    path="/by-thread",
    name="surveillance:alerts:get-alerts-by-thread",
    summary="Get list of comms alerts for watch, grouped by thread id",
)
async def get_single_watch_alerts_grouped_by_thread(
    params: PaginatedDatedListParams = Depends(),
    person: Optional[List[str]] = Query(None),
    matched_lexica: Optional[List[str]] = Query(None),
    with_out_participants: Optional[bool] = Query(False, alias="withoutParticipants"),
    unresolved_alerts_assignee_status: Optional[List[str]] = Query(
        None, alias="unresolvedAlertsAssigneeStatus"
    ),
    workflow_status: Optional[WorkflowStatus] = Query(None),
    workflow_statuses: Optional[List[WorkflowStatus]] = Query(None),
    repo: AlertsRepository = ReqDep(AlertsRepository),
):
    skip = params.page_params.skip
    take = params.page_params.take
    sort = params.page_params.sort

    results = await repo.get_watch_alerts_grouped_by_thread(
        module=ModuleTitle.COMMS_SURVEILLANCE,
        person=person,
        matched_lexica=matched_lexica,
        workflow_status=workflow_status,
        with_out_participants=with_out_participants,
        unresolved_alerts_assignee_status=unresolved_alerts_assignee_status,
        workflow_statuses=workflow_statuses,
        **params.as_search_kwargs(),
    )

    sorts = parse_sort_str(
        sort,
        allowed_fields=(
            "threadId",
            "dateOfLastAlert",
            "watchesDetected",
            "participantInfo",
            "RESOLVED",
            "UNDER_INVESTIGATION",
            "UNRESOLVED",
        ),
    )

    results = apply_sorts(results, sorts)
    total_hits = len(results)

    if skip < len(results):
        results = results[skip : skip + take]
    elif take < len(results):
        results = results[0:take]

    return {
        "header": {"returnedHits": len(results), "totalHits": total_hits, "skippedHits": skip},
        "results": results,
    }


@router.get(
    path="/without-thread",
    name="surveillance:alerts:get-alerts-without-thread",
    summary="Get list of comms alerts for a watch that do no belong to a thread",
)
async def get_single_watch_alerts_without_thread(
    params: PaginatedDatedListParams = Depends(),
    person: Optional[List[str]] = Query(None),
    matched_lexica: Optional[List[str]] = Query(None),
    workflow_status: Optional[WorkflowStatus] = Query(None),
    workflow_statuses: Optional[List[WorkflowStatus]] = Query(None),
    repo: AlertsRepository = ReqDep(AlertsRepository),
):
    result = await repo.get_alerts_without_thread(
        person=person,
        matched_lexica=matched_lexica,
        workflow_status=workflow_status,
        workflow_statuses=workflow_statuses,
        **params.as_search_kwargs(),
    )

    return SearchResult.from_raw_result(result, skipped_hits=params.page_params.skip)


def _get_email_dict_sort(email: Dict):
    return [
        int(
            parse_datetime(email.get("hit").get("timestamps", {}).get("timestampStart"))
            .replace(tzinfo=dt.timezone.utc)
            .timestamp()
            * 1000
        ),
    ]


def _make_alert_detail(alert_record: dict) -> dict:
    return {
        "&id": alert_record.get("&id"),
        "&key": alert_record.get("&key"),
        "&model": alert_record.get("&model"),
        "copilotAnalysis": alert_record.get("copilotAnalysis"),
        "detail": alert_record.get("detail"),
        "detected": alert_record.get("detected"),
        "highlights": alert_record.get("highlights"),
        "hitModel": alert_record.get("hitModel"),
        "matchedLexica": alert_record.get("matchedLexica"),
        "matchedLexicaCategories": alert_record.get("matchedLexicaCategories"),
        "slug": alert_record.get("slug"),
        "workflow": alert_record.get("workflow"),
        "underlyingAlertId": alert_record.get("underlyingAlertId"),
        "neuralLevelsReviewedBy": alert_record.get("neuralLevelsReviewedBy"),
        "neuralAssessments": alert_record.get("neuralAssessments"),
    }


@router.get("/{alert_id}")
async def get_alert(
    request: Request,
    alert_id: str,
    repo: CommunicationAlertRepository = ReqDep(CommunicationAlertRepository),
    mb: FastApiMessageBus = Depends(),
):
    alert = await repo.get_alert(alert_id)
    await mb.publish(AlertViewed(alert=alert, request=request))

    return alert


@router.get(
    path="/{alert_id}/emails/around",
    name="surveillance:comms:emails:get-emails-around",
)
async def get_alert_emails_around_email(
    alert_id: str,
    params: NonPaginatedDatedListParams = Depends(),
    search_after: str = Query(alias="searchAfter", default=None),
    search_before: str = Query(alias="searchBefore", default=None),
    skip: Optional[int] = Query(default=None),
    take: int = 50,
    repo: CommunicationAlertRepository = ReqDep(CommunicationAlertRepository),
    comms_repo: CommsRepository = ReqDep(CommsRepository),
):
    """Get email alerts around a particular email alert within the same thread
    Only one of searchBefore and searchAfter can be specified at a time.

    In general, this endpoint should NOT be used for filtering.
    When filters are used, it is recommended to use the plain email listing.

    Filters will not be applied to the message from which the search originates.
    This origin message is returned as part of other messages in two cases:
    1) if searchBefore and searchAfter are not supplied - it will then be returned even if
       the message does not match filters (search, f, start, end, etc.).
    2) if the message falls on the page delineated by searchBefore and searchAfter and matches the filters.

    To calculate the results after the current page, use totalHits - skippedHits - returnedHits.
    """

    if search_before and search_after:
        raise BadInput("Only one of searchBefore and searchAfter can be specified at a time")

    alert_response = (await repo.get_alerts(alert_id=alert_id)).as_list()
    if len(alert_response) == 0:
        raise NotFound("Alert", alert_id)

    alert = alert_response[0]

    search_result: SearchResult = await comms_repo.get_comms_around_by_email(
        email=Email.from_dict(alert.get("hit")),
        skip=skip,
        take=take,
        search_before=search_before,
        search_after=search_after,
        **params.as_search_kwargs(),
    )

    # collect all of the Email &ids
    record_ids, comms_records_by_id = [], {}
    for r in search_result.results:
        record_ids.append(r.id_)
        comms_records_by_id[r.id_] = r

    # get all of the Alerts within the watch with those IDs as hits
    alert_records = await repo.get_alerts(
        watch_id=alert.get("detail", {}).get("watchId"), record_id=record_ids
    )

    new_results = []

    # A random sampling of cSurv alerts watch can have multiple alerts on a single communication,
    # Hence grouping data by alerts
    if alert.get("detail", {}).get("queryType") == WatchQueryType.ALERT_SAMPLING:
        for alert_record in alert_records.as_list():
            comm_record = comms_records_by_id.get(alert_record.get("hit").get("&id"))
            if comm_record:
                comm_record = comm_record.to_dict()
                comm_record["alertDetails"] = _make_alert_detail(alert_record)

            new_results.append(comm_record)

        # Considering header of alert records
        search_result.header.returnedHits = len(alert_records.hits.hits)
        search_result.header.totalHits = alert_records.hits.total
        search_result.results = new_results
    else:
        alert_records_by_email_id = {r.get("hit").get("&id"): r for r in alert_records.as_list()}

        for record in search_result.results:
            if record.id_ in alert_records_by_email_id:
                record = record.to_dict()
                record["alertDetails"] = _make_alert_detail(
                    alert_records_by_email_id.get(record.get("&id"))
                )

            new_results.append(record)

        search_result.results = new_results
    return search_result


@router.get(
    path="/{alert_id}/emails/around/alerts",
    name="surveillance:comms:emails:get-emails-around-alerts",
)
async def get_alert_emails_around_alerts(
    alert_id: str,
    repo: CommunicationAlertRepository = ReqDep(CommunicationAlertRepository),
    alert_status: Optional[List[AlertHitStatus]] = Query(None, alias="alertStatus"),
    alert_resolution_category: Optional[List[str]] = Query(None, alias="resolutionCategory"),
    alert_resolution_sub_category: Optional[List[str]] = Query(None, alias="resolutionSubCategory"),
    comms_repo: CommsRepository = ReqDep(CommsRepository),
):
    alert_response = await repo.get_alerts(alert_id=alert_id)
    if len(alert_response.as_list()) == 0:
        raise NotFound("Alert", alert_id)
    alert = alert_response.as_list()[0]
    email_id = Email.from_dict(alert.get("hit")).id_
    email: Email = await comms_repo.get_one(Email, email_id)

    thread_id = nested_get(email, "metadata.threadId")
    if thread_id:
        # get all of the Alerts within the watch with those IDs as hits
        alert_records = await repo.get_alerts(
            thread_id=thread_id,
            watch_id=nested_get(alert, "detail.watchId"),
            workflow_statuses=alert_status,
            alert_resolution_category=alert_resolution_category,
            alert_resolution_sub_category=alert_resolution_sub_category,
            pagination=Pagination(take=250),
        )
        return SearchResult.from_raw_result(alert_records, skipped_hits=0)
    return []


def filter_alerts_by_email_body(
    alerts: RawResult, email_body: str, exclude_ids: list
) -> List[dict]:
    """
    Returns:
        A list of alerts that match the given email body and who's id is not in the list of excluded ids  # noqa
    """
    return [
        n
        for n in nested_get(alerts.dict(), "hits.hits")
        if nested_get(n, "hit.body")
        and nested_get(n, "hit.body.text") == email_body
        and n["&id"] not in exclude_ids
    ]


async def filter_alerts_by_thread_status(
    repo: CommunicationAlertRepository,
    comms_repo: CommsRepository,
    alerts: List[dict],
    excluded_status: List[str],
) -> Tuple[List[dict], int]:
    """Iterates over alerts and includes if:

        - Alert has an Email
          AND
        - Does not belong to a thread
          OR
        - Belongs to a thread with no other Alerts (Excluding itself) on it marked with the excluded_status, i.e: # noqa
          exclude if thread_count > 1.
    Returns:
        A list of alerts that do NOT contain other alerts on that same email thread with
        the excluded_status on the SAME WATCH.
    """
    filtered_alerts = []
    for alert in alerts:
        watch_id = nested_get(alert, "detail.watchId")
        email_id = Email.from_dict(alert.get("hit")).id_
        alert_id = alert["&id"]

        try:
            email: Email = await comms_repo.get_one(Email, email_id)
        except NotFound:
            log.debug(f"Duplicate Alert {alert_id} excluded as email {email_id} was not found")
            continue

        thread_id = nested_get(email, "metadata.threadId")
        if thread_id:
            log.debug(f"Duplicate Alert {alert_id} belongs to thread {thread_id}")
            thread_count = (
                await repo.get_alerts_thread_summary(thread_id=thread_id, watch_id=watch_id)
            )[excluded_status]
            log.debug(
                f"Duplicate Alert {alert_id} has {thread_count} with status {excluded_status}"
            )

            if thread_count > 1:
                log.debug(
                    f"Duplicate Alert {alert_id} excluded as "
                    f"thread {thread_id} has other alerts marked {excluded_status}"
                )
                continue

        log.debug(f"Duplicate Alert {alert_id} included in response")
        filtered_alerts.append(alert)

    return filtered_alerts, len(filtered_alerts)


async def get_alerts_with_duplicate_email_bodies(
    repo: CommunicationAlertRepository, alert: dict, workflow_status: str
) -> RawResult:
    """How to find a duplicate:

        - Fetch all the Alerts that are on the same Watch that have the same subject line and status. # noqa
        - Iterate over these alerts and compare their email bodies.
        - A positive match would be considered a duplicate.
    Caveat:
        Due to ES response limits, queries are done in batches using the Pagination functionality
    Returns:
        Alerts that have the same email subject, body and workflow_status.
    """
    body = nested_get(alert, "hit.body.text")
    excluded_ids = [alert["&id"]]  # We want to exclude the alert in question from the results
    watch_id = nested_get(alert, "detail.watchId")
    subject = nested_get(alert, "hit.subject")
    count_of_alerts_for_watch = await repo.get_alerts(
        watch_id=watch_id, subject=subject, count=True
    )
    log.debug(
        f"Watch [{watch_id}] has {count_of_alerts_for_watch} alerts that has "
        + (f"the subject line '{subject}'" if subject else "no subject")
    )

    duplicates_took = 0
    duplicate_hits = []

    for i in range(0, count_of_alerts_for_watch, 250):
        alerts = await repo.get_alerts(
            watch_id=watch_id,
            workflow_status=workflow_status,
            subject=subject,
            pagination=Pagination(skip=i, take=250),
        )

        duplicate_hits += filter_alerts_by_email_body(alerts, body, excluded_ids)
        duplicates_took += alerts.took

    duplicate_alerts = RawResult(
        took=duplicates_took,
        timed_out=False,
        hits=RawResultHits(total=len(duplicate_hits), hits=duplicate_hits),
    )

    return duplicate_alerts


@router.get(
    path="/{alert_id}/search/duplicates",
    name="surveillance:alerts:get-alerts-with-duplicate-email-bodies",
    summary="Get Alerts with the same email body (Duplicate emails)",
    response_model=SearchResult,
)
async def get_alerts_duplicate_emails(
    alert_id: str,
    repo: CommunicationAlertRepository = ReqDep(CommunicationAlertRepository),
    alert_status: Optional[AlertHitStatus] = Query(
        AlertHitStatus.UNRESOLVED.value, alias="alertStatus"
    ),
    comms_repo: CommsRepository = ReqDep(CommsRepository),
):
    """
    Returns:
        - Alerts that have the same email subject and body,
        - that match the alert_status/workflow_status eg: UNRESOLVED,
        - and do NOT contain other alerts on that same email thread with the excluded_status on the SAME WATCH. # noqa
    """
    try:
        alert = (await repo.get_alerts(alert_id=alert_id)).as_list()[0]
    except IndexError:
        msg = f"Alert [{alert_id}] not found!"
        log.debug(msg)
        raise HTTPException(
            status_code=404,
            detail=msg,
        )

    if alert["hitModel"] != Email.Config.model_name:
        msg = f"Alert [{alert_id}] is a {alert['hitModel']} and should be {Email.Config.model_name}"
        log.debug(msg)
        raise HTTPException(
            status_code=404,
            detail=msg,
        )

    duplicates = await get_alerts_with_duplicate_email_bodies(
        repo=repo,
        alert=alert,
        workflow_status=alert_status,
    )

    duplicates.hits.hits, duplicates.hits.total = await filter_alerts_by_thread_status(
        repo,
        comms_repo,
        alerts=duplicates.hits.hits,
        excluded_status=StatusResponseAdatptor[alert_status].value,
    )

    return SearchResult.from_raw_result(duplicates)


@router.get(
    path="/{alert_id}/transcripts/{transcript_id}",
    name="surveillance:alerts:get-call-alert-transcripts",
    summary="Get Underlying comm transcripts",
)
async def get_call_alert_pre_signed_url(
    alert_id: str,
    transcript_id: str,
    request: Request,
    repo: CommunicationAlertRepository = ReqDep(CommunicationAlertRepository),
    transcript_repo: TranscriptsRepository = ReqDep(TranscriptsRepository),
    mb: FastApiMessageBus = Depends(),
):
    """Retrieve the transcripts for a call alert."""
    alert = await repo.get_alert(alert_id=alert_id)
    if alert.hitModel not in (Call.Config.model_name, Meeting.Config.model_name):
        raise NotFound("No Transcripts in Comm", alert_id)

    comm_type = Call if alert.hitModel == Call.Config.model_name else Meeting
    comm = comm_type(**alert["hit"])

    # Transcript id "0" is a deprecated record migrated to
    # have the `transcripts` property -- special case
    transcript = (
        comm.shim_transcript()
        if transcript_id == "0"
        else (await transcript_repo.get_one(transcript_id))
    )

    await mb.publish(AlertViewed(alert=alert, request=request))
    return transcript

# type: ignore
from api_sdk.auth import Tenancy
from api_sdk.di.request import ReqDep
from api_sdk.exceptions import NotFound
from api_sdk.messages.fastapi_message_bus import FastApiMessageBus
from api_sdk.models.permissions import Permission
from api_sdk.responses import AlreadyExistsResponse
from fastapi import APIRouter, Body, Depends
from se_api_svc.messages.account.events import (
    AccountFirmCreated,
    AccountFirmUpdated,
    AccountFirmViewed,
)
from se_api_svc.repository.account.firms import CurrentAccountFirmRepo
from se_api_svc.schemas.account import AccountFirm
from typing import Optional

router = APIRouter()


@router.post("", name="account:firms:create-firm")
async def create_firm(
    firm: AccountFirm = Body(...),
    tenancy: Tenancy = ReqDep(Tenancy),
    mb: FastApiMessageBus = Depends(),
    repo: CurrentAccountFirmRepo = ReqDep(CurrentAccountFirmRepo),
):
    tenancy.require_permissions(Permission.ADMIN)

    try:
        await repo.get_firm()
        return AlreadyExistsResponse(msg="Account Firm already exists.", loc=["&id"])
    except NotFound:
        pass

    # Only single firm can be created for a tenant.
    firm.id_ = repo.FIRM_ID
    await repo.save_new(firm)

    await mb.publish(AccountFirmCreated(firm=firm))

    return firm


@router.get("/firm", name="account:firms:get-firm")
async def get_firm(
    timestamp: Optional[int] = None,
    tenancy: Tenancy = ReqDep(Tenancy),
    mb: FastApiMessageBus = Depends(),
    repo: CurrentAccountFirmRepo = ReqDep(CurrentAccountFirmRepo),
):
    tenancy.require_permissions(Permission.ADMIN)

    firm = await repo.get_firm(timestamp=timestamp)

    await mb.publish(AccountFirmViewed(firm=firm))

    return firm


@router.put("/firm", name="account:firms:update-firm")
async def update_firm(
    firm: AccountFirm = Body(...),
    timestamp: Optional[int] = None,
    tenancy: Tenancy = ReqDep(Tenancy),
    mb: FastApiMessageBus = Depends(),
    repo: CurrentAccountFirmRepo = ReqDep(CurrentAccountFirmRepo),
):
    tenancy.require_permissions(Permission.ADMIN)

    current_firm = await repo.get_firm(timestamp=timestamp)
    current_firm.update_from(firm)
    await repo.save_existing(current_firm)

    await mb.publish(AccountFirmUpdated(firm=current_firm))

    return firm

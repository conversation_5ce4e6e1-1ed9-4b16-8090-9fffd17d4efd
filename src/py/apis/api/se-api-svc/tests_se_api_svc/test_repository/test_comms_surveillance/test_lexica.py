# type: ignore
import pytest
from api_sdk.models.elasticsearch import RawResult
from api_sdk.models.search import Pagination
from api_sdk.utils.meta_fields import META_FIELDS
from se_api_svc.repository.comms_surveillance.lexica import (
    CommsLexicaRepository,
    CommsSurveillanceHitByBaseTermAggs,
)
from se_api_svc.schemas.surveillance.comms_surveillance.comm_surveillance.commsurveillance import (
    QuerySubBehavioursIn,
)
from unittest.mock import AsyncMock


@pytest.mark.parametrize(
    "param, expected",
    [
        (
            {
                "query_in": QuerySubBehavioursIn(
                    **{
                        "queryType": "COMMUNICATIONS",
                        "query": {
                            "description": "",
                            "name": "foo",
                            "kind": "BESPOKE",
                            "filter": {
                                "flangVersion": "1.2",
                                "chunks": [
                                    {
                                        "category": "data source",
                                        "f": "metadata.source.client in ['Instant Bloomberg']",
                                        "id": "89d03313-2a96-4d01-bd73-db4277469a63",
                                    }
                                ],
                            },
                        },
                    }
                ),
                "use_watch_filter": True,
            },
            {
                "query": {
                    "bool": {
                        "filter": [
                            {"terms": {"&model": ["Email", "Call", "Text", "Message", "Meeting"]}},
                            {"bool": {"must_not": [{"exists": {"field": "&expiry"}}]}},
                        ]
                    }
                },
                "aggs": {
                    "WATCH_FILTER": {
                        "filter": {
                            "bool": {
                                "filter": [
                                    {
                                        "terms": {
                                            "&model": [
                                                "Email",
                                                "Call",
                                                "Text",
                                                "Message",
                                                "Meeting",
                                            ]
                                        }
                                    },
                                    {"bool": {"must_not": [{"exists": {"field": "&expiry"}}]}},
                                ],
                                "must_not": [{"exists": {"field": "&expiry"}}],
                                "must": [
                                    {
                                        "terms": {
                                            "metadata.source.client.text": ["instant bloomberg"]
                                        }
                                    }
                                ],
                            }
                        },
                        "aggs": {
                            "NESTED": {
                                "nested": {"path": "analytics.lexica"},
                                "aggs": {
                                    "FILTER": {
                                        "filter": {"match_all": {}},
                                        "aggs": {
                                            "TERM": {
                                                "terms": {
                                                    "field": "analytics.lexica.termBase",
                                                    "size": 2147483647,
                                                },
                                                "aggs": {
                                                    "total_terms": {
                                                        "value_count": {
                                                            "field": "analytics.lexica.termBase"
                                                        }
                                                    },
                                                    "TERM_ID": {
                                                        "terms": {
                                                            "field": "analytics.lexica.termId",
                                                            "size": 2147483647,
                                                        }
                                                    },
                                                    "distinct_terms": {
                                                        "cardinality": {
                                                            "field": "analytics.lexica.termId"
                                                        }
                                                    },
                                                    "TERM_PAIRED": {
                                                        "terms": {
                                                            "field": "analytics.lexica.termPaired",
                                                            "size": 2147483647,
                                                        }
                                                    },
                                                    "LANGUAGE": {
                                                        "terms": {
                                                            "field": "analytics.lexica.termLanguage",  # noqa: E501
                                                            "size": 2147483647,
                                                        }
                                                    },
                                                },
                                            }
                                        },
                                    }
                                },
                            }
                        },
                    },
                    "start_date": {"min": {"field": "&timestamp"}},
                    "end_date": {"max": {"field": "&timestamp"}},
                },
                "sort": [
                    {"createdOn": {"order": "desc", "missing": "_last", "unmapped_type": "long"}}
                ],
                "from": 0,
                "size": 50,
            },
        )
    ],
)
def test_hit_by_base_term_search_model(param, expected):
    assert CommsSurveillanceHitByBaseTermAggs(**param).to_dict(meta_fields=META_FIELDS) == expected


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "param, agg_results",
    [
        (
            {
                "pagination": Pagination(skip=0, take=50, sorts=None),
                "use_watch_filter": True,
            },
            RawResult(
                **{
                    "took": 31,
                    "timed_out": False,
                    "_shards": {"total": 6, "successful": 6, "skipped": 0, "failed": 0},
                    "hits": {
                        "total": {"value": 10000, "relation": "gte"},
                        "max_score": None,
                        "hits": [],
                    },
                    "aggregations": {
                        "end_date": {
                            "value": 1.713856754227e12,
                            "value_as_string": "2024-04-23T07:19:14.227Z",
                        },
                        "WATCH_FILTER": {
                            "doc_count": 25928,
                            "NESTED": {
                                "doc_count": 60,
                                "FILTER": {
                                    "doc_count": 60,
                                    "TERM": {
                                        "doc_count_error_upper_bound": 0,
                                        "sum_other_doc_count": 0,
                                        "buckets": [
                                            {
                                                "key": "学期",
                                                "doc_count": 56,
                                                "total_terms": {"value": 56},
                                                "distinct_terms": {"value": 1},
                                                "TERM_PAIRED": {
                                                    "doc_count_error_upper_bound": 0,
                                                    "sum_other_doc_count": 0,
                                                    "buckets": [{"key": "你好", "doc_count": 56}],
                                                },
                                            },
                                            {
                                                "key": "Belfast",
                                                "doc_count": 2,
                                                "total_terms": {"value": 2},
                                                "distinct_terms": {"value": 1},
                                                "TERM_PAIRED": {
                                                    "doc_count_error_upper_bound": 0,
                                                    "sum_other_doc_count": 0,
                                                    "buckets": [{"key": "SK", "doc_count": 2}],
                                                },
                                            },
                                            {
                                                "key": "what",
                                                "doc_count": 2,
                                                "total_terms": {"value": 2},
                                                "distinct_terms": {"value": 1},
                                                "TERM_PAIRED": {
                                                    "doc_count_error_upper_bound": 0,
                                                    "sum_other_doc_count": 0,
                                                    "buckets": [],
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                        },
                        "start_date": {
                            "value": 1.693563543118e12,
                            "value_as_string": "2023-09-01T10:19:03.118Z",
                        },
                    },
                }
            ),
        )
    ],
)
async def test_base_term_hits_with_watch_filters(param, agg_results):
    repo = CommsLexicaRepository.__new__(CommsLexicaRepository)
    repo.get_aggs = AsyncMock()
    results = await repo.get_hit_by_term(
        lexica_search=None, lexica_service=None, term_type="BASE_TERM", **param
    )
    assert results.dict() == {
        "header": {
            "nextSearchAfter": None,
            "offset": None,
            "previousSearchBefore": None,
            "returnedHits": 0,
            "skippedHits": 0,
            "totalHits": 0,
        },
        "results": [],
    }

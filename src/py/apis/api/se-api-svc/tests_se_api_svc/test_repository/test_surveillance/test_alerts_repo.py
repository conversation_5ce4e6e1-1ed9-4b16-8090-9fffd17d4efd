import asyncio
from se_api_svc.repository.surveillance.alerts import AlertsRepository
from unittest.mock import AsyncMock, MagicMock


class DummyResult:
    def __init__(self, buckets):
        self._buckets = buckets

    def iter_raw_bucket_agg(self, name):
        return self._buckets


def test_alerts_by_threads_when_identifiers_is_none():
    fake_agg_result = [
        {
            "key": "thread-001",
            "RECENT_DATE": {"value": 1630000000000},
            "TOTAL_WATCHES_DETECTED": {"value": 5},
            "AGGS_BY_STATUS": {"buckets": []},
            "TOP_HITS_BY_THREAD": {
                "hits": {
                    "hits": [
                        {
                            "_source": {
                                "hit": {
                                    "participants": [
                                        {"value": {"name": "Test User"}},
                                    ],
                                    "identifiers": None,  # Test focus
                                }
                            }
                        }
                    ]
                }
            },
        }
    ]

    mock_repo = MagicMock()

    alerts_repo = AlertsRepository(repo=mock_repo)

    alerts_repo.get_aggs = AsyncMock(return_value=DummyResult(fake_agg_result))
    try:
        result = asyncio.run(alerts_repo.get_watch_alerts_grouped_by_thread())
    except Exception as e:
        assert False, f"Method raised exception when identifiers is None: {e}"

    # optional: check if the result is as expected
    assert len(result) == 1
    assert "Test User" in result[0]["participantInfo"]

import pytest
from se_api_svc.repository.mar.scenarios import ScenariosRepository
from unittest.mock import AsyncMock, MagicMock, patch


@pytest.mark.asyncio
async def test_enrich_with_isin_name_handles_empty_keys_safely():
    watch_repo = MagicMock()
    repo = MagicMock()
    orders_repo = MagicMock()
    scenarios_repo = ScenariosRepository(watch_repo=watch_repo, repo=repo, orders_repo=orders_repo)

    scenarios_repo._get_orders_and_executions_by_keys = AsyncMock(return_value=[])

    scenarios = [
        {
            "detail": {"marketAbuseReportType": "type_not_in_map"},
            "records": {},
            "additionalFields": {"topLevel": {}},
        }
    ]

    record_order_ids = ["order_1", "order_2"]

    with patch("se_api_svc.repository.mar.scenarios.ALGO_RECORD_KEY_MAP", {"type_not_in_map": []}):
        try:
            await scenarios_repo._enrich_with_isin_name(scenarios, record_order_ids)
        except IndexError:
            pytest.fail("IndexError was raised but should have been safely handled.")

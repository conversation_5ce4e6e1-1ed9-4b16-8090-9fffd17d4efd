# type: ignore
import datetime as dt
import pytest
from base64 import b64encode
from se_api_svc.api.routes.comms.chat import get_messages_around_message
from se_api_svc.schemas.comms.chat import Message
from unittest.mock import AsyncMock, MagicMock


class TestGetMessagesAroundMessageGetManyParams:
    """Test cases for get_messages_around_message endpoint focusing
    on parameters passed to get_many()."""

    @pytest.fixture
    def encoded_message_id(self):
        """Create a base64 encoded message ID."""
        return b64encode("test_message_id".encode()).decode()

    @pytest.fixture
    def mock_message(self):
        """Create a mock message with timestamps."""
        message = MagicMock(spec=Message)
        message.id_ = "test_message_id"
        message.roomId = "test_room_id"
        message.timestamps = MagicMock()
        message.timestamps.timestampStart = dt.datetime(
            2023, 1, 15, 10, 30, 0, tzinfo=dt.timezone.utc
        )
        message.metadata = {"source": {"client": "Slack Chat"}}
        return message

    @pytest.fixture
    def mock_messages_list(self):
        """Create multiple unique mock messages with different timestamps."""
        messages = []
        for i in range(5):
            message = MagicMock(spec=Message)
            message.id_ = f"message_id_{i}"
            message.roomId = "test_room_id"
            message.timestamps = MagicMock()
            message.timestamps.timestampStart = dt.datetime(
                2023,
                1,
                15,
                10,
                20 + i * 2,
                0,
                tzinfo=dt.timezone.utc,  # 10:20, 10:22, 10:24, 10:26, 10:28
            )
            message.metadata = {"source": {"client": "Slack Chat"}}
            message.content = f"Test message content {i}"
            messages.append(message)
        return messages

    @pytest.fixture
    def mock_messages_before(self):
        """Create mock messages for 'before' query results."""
        messages = []
        for i in range(3):
            message = MagicMock(spec=Message)
            message.id_ = f"before_message_{i}"
            message.roomId = "test_room_id"
            message.timestamps = MagicMock()
            message.timestamps.timestampStart = dt.datetime(
                2023,
                1,
                15,
                10,
                10 + i * 3,
                0,
                tzinfo=dt.timezone.utc,  # 10:10, 10:13, 10:16
            )
            message.metadata = {"source": {"client": "Slack Chat"}}
            message.content = f"Before message content {i}"
            messages.append(message)
        return messages

    @pytest.fixture
    def mock_messages_after(self):
        """Create mock messages for 'after' query results."""
        messages = []
        for i in range(4):
            message = MagicMock(spec=Message)
            message.id_ = f"after_message_{i}"
            message.roomId = "test_room_id"
            message.timestamps = MagicMock()
            message.timestamps.timestampStart = dt.datetime(
                2023,
                1,
                15,
                10,
                35 + i * 2,
                0,
                tzinfo=dt.timezone.utc,  # 10:35, 10:37, 10:39, 10:41
            )
            message.metadata = {"source": {"client": "Slack Chat"}}
            message.content = f"After message content {i}"
            messages.append(message)
        return messages

    @pytest.mark.asyncio
    async def test_start_end_param(
        self, encoded_message_id, mock_message, mock_messages_before, mock_messages_after
    ):
        mock_params = MagicMock()
        mock_params.as_search_kwargs.return_value = {
            "start": dt.datetime(2023, 1, 15, 9, 0, 0),
            "end": dt.datetime(2023, 1, 15, 12, 0, 0),
        }
        mock_params.request = MagicMock()

        mock_repo = AsyncMock()
        mock_repo.get_message.return_value = mock_message
        before_messages = mock_messages_before
        before_messages[0].timestamps.timestampStart = dt.datetime(
            2019, 1, 15, 10, 10, tzinfo=dt.timezone.utc
        )

        mock_repo.get_room_messages.side_effect = [
            MagicMock(hits=MagicMock(hits=before_messages)),
            MagicMock(hits=MagicMock(hits=mock_messages_after)),
        ]
        mock_get_chat_events = AsyncMock()
        mock_get_chat_events.return_value = (0, [])
        mock_repo.get_chat_events = mock_get_chat_events

        mock_mb = AsyncMock()

        await get_messages_around_message(
            encoded_message_id=encoded_message_id,
            params=mock_params,
            companies=["Company1", "Company2"],
            except_companies=["ExceptCompany"],
            people=["Person1", "Person2"],
            except_people=["ExceptPerson"],
            take=75,
            excludeChatEvents=False,
            minimal=True,
            repo=mock_repo,
            mb=mock_mb,
            search_after_message=False,
        )

        assert mock_get_chat_events.call_args.kwargs["start"].year == 2019
        assert mock_get_chat_events.call_args.kwargs["end"].year == 2023

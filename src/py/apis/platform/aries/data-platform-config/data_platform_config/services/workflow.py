from aries_config_api_httpschema.workflow import WorkflowCreate, WorkflowCreated
from data_platform_config.exceptions import DataNotFound
from data_platform_config.models.system.workflow import Workflow
from sqlalchemy import true
from sqlalchemy.sql.expression import bindparam


class WorkflowService:
    def __init__(self, session_factory) -> None:
        self.session_factory = session_factory

    def get_all(self, storage_prefix: str | None) -> Workflow:
        with self.session_factory() as session:
            result: Workflow = (
                session.query(Workflow)
                .filter(
                    bindparam("storage_prefix", storage_prefix).startswith(
                        Workflow.storage_prefix  # type:ignore
                    )
                    if storage_prefix
                    else true()
                )
                .all()
            )
            return result

    def delete(self, workflow_name: str) -> None:
        with self.session_factory() as session:
            workflow = session.query(Workflow).filter(Workflow.name == workflow_name).one_or_none()
            if workflow is None:
                raise DataNotFound(f"Workflow '{workflow_name}' not found")
            session.delete(workflow)
            session.commit()

    def add(self, workflow_name: str, workflow_details: WorkflowCreate) -> WorkflowCreated:
        with self.session_factory() as session:
            workflow = session.query(Workflow).filter(Workflow.name == workflow_name).one_or_none()

            # if workflow is already present: update it
            if workflow:
                workflow_dict = workflow_details.dict(exclude_unset=True)

                for key, value in workflow_dict.items():
                    setattr(workflow, key, value)
                session.commit()
                return WorkflowCreated(
                    name=workflow_name,
                    streamed=workflow.streamed,
                    storage_prefix=workflow.storage_prefix,
                )
            # if workflow is not present:
            else:
                workflow_create_keys = workflow_details.dict()
                workflow_create_keys["name"] = workflow_name

                new_workflow = Workflow(**workflow_create_keys)
                session.add(new_workflow)
                session.commit()
                return WorkflowCreated(
                    name=workflow_name,
                    streamed=workflow_details.streamed,
                    storage_prefix=workflow_details.storage_prefix,
                )

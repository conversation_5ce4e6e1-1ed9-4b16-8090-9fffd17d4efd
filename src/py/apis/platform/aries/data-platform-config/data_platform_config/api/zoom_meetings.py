from aries_config_api_httpschema.zoom import ZoomMeetingsConfigUpdate, ZoomMeetingsConfigUpdated
from data_platform_config.containers import Container
from data_platform_config.services.zoom_meetings_config import ZoomMeetingsService
from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends, Query, status

zoom_meetings_router = APIRouter()

zoom_meetings_config_router = APIRouter()


@zoom_meetings_router.get("")
@inject
def get_config(
    stack_name: str,
    tenant_name: str,
    service: ZoomMeetingsService = Depends(Provide[Container.zoom_meetings_service]),
):
    """Gets configuration for zoom meetings data source for given tenant.

    :param stack_name:
    :param tenant_name:
    :param service:
    :return:
    """
    return service.get_config(stack_name=stack_name, tenant_name=tenant_name)


@zoom_meetings_router.put("", status_code=status.HTTP_201_CREATED)
@inject
def upsert_configuration(
    stack_name: str,
    tenant_name: str,
    zoom_meetings_config_update: ZoomMeetingsConfigUpdate,
    service: ZoomMeetingsService = Depends(Provide[Container.zoom_meetings_service]),
) -> ZoomMeetingsConfigUpdated:
    """Upsert configuration which should belong to the tenant.

    :param stack_name:
    :param tenant_name:
    :param zoom_meetings_config_update:
    :param service:
    :return:
    """
    return service.upsert_config(
        stack_name=stack_name,
        tenant_name=tenant_name,
        zoom_meetings_config_update=zoom_meetings_config_update,
    )


@zoom_meetings_config_router.get("")
@inject
def search_config(
    account_id: str | None = Query(default=None),
    service: ZoomMeetingsService = Depends(Provide[Container.zoom_meetings_service]),
):
    """Gets configuration for zoom meetings data source for given account.

    :param account_id:
    :param service:
    :return:
    """
    return service.search_config(account_id=account_id)

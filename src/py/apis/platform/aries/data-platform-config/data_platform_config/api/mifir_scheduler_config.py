from aries_config_api_httpschema.mifir_scheduler_config import (
    MifirSchedulerConfigUpsert,
    MifirSchedulerConfigUpsertList,
)
from data_platform_config.containers import Container
from data_platform_config.services.mifir_scheduler_config import MifirSchedulerConfigApiService
from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends, Query

mifir_scheduler_config_api_router = APIRouter()


@mifir_scheduler_config_api_router.get("/")
@inject
def get_configs(
    stack_name: str,
    tenant_name: str,
    enabled: bool | None = Query(default=None),
    service: MifirSchedulerConfigApiService = Depends(
        Provide[Container.mifir_scheduler_config_api_service]
    ),
):
    """get configurations for a given tenant.

    :param stack_name: stack name
    :param tenant_name: tenant name
    :param service: service, defaults to Depends
    :type enabled: str
    (Provide[Container.mifir_scheduler_config_api_service])
    :type service: MifirSchedulerConfigApiService, optional
    """
    return service.get_configs_by_tenant(
        stack_name=stack_name,
        tenant_name=tenant_name,
        enabled=enabled,
    )


@mifir_scheduler_config_api_router.get("/schedule_type/{schedule_type}")
@inject
def get_config(
    stack_name: str,
    tenant_name: str,
    schedule_type: str,
    enabled: bool | None = Query(default=None),
    service: MifirSchedulerConfigApiService = Depends(
        Provide[Container.mifir_scheduler_config_api_service]
    ),
):
    """Gets the configuration for a given tenant and a given
    microsoft_tenant_id.

    :param stack_name: stack name
    :type stack_name: str
    :param tenant_name: tenant name
    :type tenant_name: str
    :param schedule_type: schedule_type
    :type schedule_type: str
    :type enabled: str
    :param service: service, defaults to Depends
    (Provide[Container.mifir_scheduler_config_api_service])
    :type service: MifirSchedulerConfigApiService, optional
    """
    return service.get_config_by_schedule_type_and_tenant(
        stack_name=stack_name,
        tenant_name=tenant_name,
        schedule_type=schedule_type,
        enabled=enabled,
    )


@mifir_scheduler_config_api_router.delete("/schedule_type/{schedule_type}")
@inject
def delete_config(
    stack_name: str,
    tenant_name: str,
    schedule_type: str,
    service: MifirSchedulerConfigApiService = Depends(
        Provide[Container.mifir_scheduler_config_api_service]
    ),
):
    """Deletes the configuration for a given tenant and schedule type.

    :param stack_name: stack name
    :type stack_name: str
    :param tenant_name: tenant name
    :type tenant_name: str
    :param schedule_type: schedule type
    :type schedule_type: str
    :param service: service, defaults to Depends
    (Provide[Container.mifir_scheduler_config_api_service])
    :type service: MifirSchedulerConfigApiService, optional
    """
    return service.delete_by_schedule_type_and_tenant(
        stack_name=stack_name,
        tenant_name=tenant_name,
        schedule_type=schedule_type,
    )


@mifir_scheduler_config_api_router.put("/")
@inject
def upsert_configs(
    stack_name: str,
    tenant_name: str,
    bulk_mifir_scheduler_upsert: MifirSchedulerConfigUpsertList,
    service: MifirSchedulerConfigApiService = Depends(
        Provide[Container.mifir_scheduler_config_api_service]
    ),
):
    """upsert configurations for a given tenant.

    :param stack_name: stack name
    :param tenant_name: tenant name
    :param bulk_mifir_scheduler_upsert: MifirSchedulerConfigUpsertList
    :param service: service, defaults to Depends
    (Provide[Container.mifir_scheduler_config_api_service])
    :type service: MifirSchedulerConfigApiService, optional
    """
    return service.bulk_upsert_by_tenant(
        stack_name=stack_name,
        tenant_name=tenant_name,
        bulk_mifir_scheduler_upsert=bulk_mifir_scheduler_upsert,
    )


@mifir_scheduler_config_api_router.put("/schedule_type/{schedule_type}")
@inject
def upsert_config(
    stack_name: str,
    tenant_name: str,
    schedule_type: str,
    mifir_upsert_config: MifirSchedulerConfigUpsert,
    service: MifirSchedulerConfigApiService = Depends(
        Provide[Container.mifir_scheduler_config_api_service]
    ),
):
    """upsert configuration for a given tenant and schedule type.

    :param stack_name: stack name
    :param tenant_name: tenant name
    :param schedule_type: schedule type
    :param mifir_upsert_config: MifirSchedulerConfigUpsert
    :param service: service, defaults to Depends
    (Provide[Container.mifir_scheduler_config_api_service])
    :type service: MifirSchedulerConfigApiService, optional
    """
    return service.upsert_by_schedule_type_and_tenant(
        stack_name=stack_name,
        tenant_name=tenant_name,
        schedule_type=schedule_type,
        mifir_config=mifir_upsert_config,
    )

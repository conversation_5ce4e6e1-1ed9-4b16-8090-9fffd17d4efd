from aries_config_api_httpschema.slack import SlackChatConfigUpdate, SlackChatConfigUpdated
from data_platform_config.containers import Container
from data_platform_config.services.slack_chat_config import SlackChatService
from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends, status

slack_chat_router = APIRouter()


@slack_chat_router.get("")
@inject
def get_config(
    stack_name: str,
    tenant_name: str,
    service: SlackChatService = Depends(Provide[Container.slack_chat_service]),
):
    """Gets configuration for slack chat data source for given tenant.

    :param stack_name:
    :param tenant_name:
    :param service:
    :return:
    """
    return service.get_config(stack_name=stack_name, tenant_name=tenant_name)


@slack_chat_router.put("", status_code=status.HTTP_201_CREATED)
@inject
def upsert_configuration(
    stack_name: str,
    tenant_name: str,
    slack_chat_config_update: SlackChatConfigUpdate,
    service: SlackChatService = Depends(Provide[Container.slack_chat_service]),
) -> SlackChatConfigUpdated:
    """Upsert configuration which should belong to the tenant.

    :param stack_name:
    :param tenant_name:
    :param slack_chat_config_update:
    :param service:
    :return:
    """
    return service.upsert_config(
        stack_name=stack_name,
        tenant_name=tenant_name,
        slack_chat_config_update=slack_chat_config_update,
    )

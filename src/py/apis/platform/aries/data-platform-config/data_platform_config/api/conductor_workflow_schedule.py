import datetime
from aries_config_api_httpschema.conductor_workflow_schedule import (
    ConductorWorkflowScheduleCreate,
    ConductorWorkflowScheduleCreateList,
    ConductorWorkflowScheduleUpdate,
)
from data_platform_config.containers import Container
from data_platform_config.services.conductor_workflow_schedule import (
    ConductorWorkflowScheduleService,
)
from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends, Query

conductor_workflow_schedule_router = APIRouter()


@conductor_workflow_schedule_router.post(
    "/stacks/{stack_name}/tenants/{tenant_name}/conductor_workflow_schedules/{schedule_id}"
)
@inject
def create(
    stack_name: str,
    tenant_name: str,
    schedule_id: str,
    tenant_workflow_schedule_create: ConductorWorkflowScheduleCreate,
    service: ConductorWorkflowScheduleService = Depends(
        Provide[Container.tenant_workflow_schedule_service]
    ),
):
    """Create a new conductor workflow schedule."""
    return service.create(
        stack_name=stack_name,
        tenant_name=tenant_name,
        schedule_id=schedule_id,
        tenant_workflow_schedule_create=tenant_workflow_schedule_create,
    )


@conductor_workflow_schedule_router.get("/conductor_workflow_schedules/{schedule_id}")
@inject
def get(
    schedule_id: str,
    service: ConductorWorkflowScheduleService = Depends(
        Provide[Container.tenant_workflow_schedule_service]
    ),
):
    """Get a conductor workflow schedule."""
    return service.get(
        schedule_id=schedule_id,
    )


@conductor_workflow_schedule_router.put("/conductor_workflow_schedules/{schedule_id}")
@inject
def update(
    schedule_id: str,
    conductor_workflow_schedule_update: ConductorWorkflowScheduleUpdate,
    service: ConductorWorkflowScheduleService = Depends(
        Provide[Container.tenant_workflow_schedule_service]
    ),
):
    """Update a conductor workflow schedule."""
    return service.update_patch(
        schedule_id=schedule_id,
        conductor_workflow_schedule_update=conductor_workflow_schedule_update,
    )


@conductor_workflow_schedule_router.get("/conductor_workflow_schedules")
@inject
def get_all(
    stack: str | None = Query(None, description="Return schedules for a specific stack"),
    tenant: str | None = Query(None, description="Return schedules for a specific tenant"),
    deleted: bool | None = Query(None, description="Return deleted schedules"),
    paused: bool | None = Query(None, description="Return paused schedules"),
    last_updated_utc: datetime.datetime | None = Query(
        None, description="Return schedules updated after this time"
    ),
    service: ConductorWorkflowScheduleService = Depends(
        Provide[Container.tenant_workflow_schedule_service]
    ),
):
    """Get all conductor workflow schedules."""
    return service.get_all(
        stack=stack, tenant=tenant, deleted=deleted, paused=paused, last_updated=last_updated_utc
    )


@conductor_workflow_schedule_router.put("/conductor_workflow_schedules/bulk/pause")
@inject
def pause(
    stack: str | None = Query(None, description="Pause schedules for a specific stack"),
    tenant: str | None = Query(None, description="Pause schedules for a specific tenant"),
    workflow_name: str | None = Query(None, description="Pause a specific workflow"),
    service: ConductorWorkflowScheduleService = Depends(
        Provide[Container.tenant_workflow_schedule_service]
    ),
):
    """Pause a bunch of conductor workflow schedules."""
    return service.toggle_pause(
        stack=stack, paused=True, workflow_name=workflow_name, tenant=tenant
    )


@conductor_workflow_schedule_router.put("/conductor_workflow_schedules/bulk/unpause")
@inject
def unpause(
    stack: str | None = Query(None, description="Unpause schedules for a specific stack"),
    tenant: str | None = Query(None, description="Unpause schedules for a specific tenant"),
    workflow_name: str | None = Query(None, description="Unpause a specific workflow"),
    service: ConductorWorkflowScheduleService = Depends(
        Provide[Container.tenant_workflow_schedule_service]
    ),
):
    """Unpause a bunch of conductor workflow schedules."""
    return service.toggle_pause(
        stack=stack, paused=False, workflow_name=workflow_name, tenant=tenant
    )


@conductor_workflow_schedule_router.post(
    "/stacks/{stack_name}/tenants/{tenant_name}/conductor_workflow_schedules"
)
@inject
def bulk_upsert_conductor_workflow_schedules(
    stack_name: str,
    tenant_name: str,
    bulk_tenant_workflow_schedules_create: ConductorWorkflowScheduleCreateList,
    service: ConductorWorkflowScheduleService = Depends(
        Provide[Container.tenant_workflow_schedule_service]
    ),
):
    """Bulk create or update workflow schedules."""
    return service.bulk_upsert_conductor_workflow_schedules(
        stack_name=stack_name,
        tenant_name=tenant_name,
        bulk_tenant_workflow_schedules_create=bulk_tenant_workflow_schedules_create,
    )

from aries_config_api_httpschema.intelligent_voice import IntelligentVoiceConfigUpdate
from data_platform_config.containers import Container
from data_platform_config.services.intelligent_voice import IntelligentVoiceService
from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends, status

intelligent_voice_router = APIRouter()


@intelligent_voice_router.get("")
@inject
def get_config(
    stack_name: str,
    tenant_name: str,
    service: IntelligentVoiceService = Depends(Provide[Container.intelligent_voice_service]),
):
    """GETs the details from intelligent_voice_config table for a given tenant.

    :param stack_name:
    :param tenant_name:
    :param service:
    :return:
    """
    return service.get_config(stack_name=stack_name, tenant_name=tenant_name)


@intelligent_voice_router.put("", status_code=status.HTTP_201_CREATED)
@inject
def upsert_config_by_tenant(
    stack_name: str,
    tenant_name: str,
    config_update: IntelligentVoiceConfigUpdate,
    service: IntelligentVoiceService = Depends(Provide[Container.intelligent_voice_service]),
):
    """PUTs the given details for a given tenant.

    :param stack_name:
    :param tenant_name:
    :param config_update: IntelligentVoiceConfigUpdate,
    :param service:
    :return:
    """
    return service.upsert_config(
        stack_name=stack_name,
        tenant_name=tenant_name,
        config_to_upsert=config_update,
    )

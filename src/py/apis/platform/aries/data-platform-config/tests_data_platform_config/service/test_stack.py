import addict
import pytest
from aries_config_api_httpschema.stack import StackCreate
from data_platform_config.exceptions import DataNotFound
from data_platform_config.models.system.stack import Stack
from data_platform_config.services.stack import StackService
from mock.mock import MagicMock, patch


@pytest.fixture
def stack_service(mock_session):
    return StackService(session_factory=mock_session)


def test_add_existing_stack(mock_session, stack_service):
    # input
    mock_session().__enter__().query().filter_by().one_or_none.return_value = MagicMock()
    stack = StackCreate(name="mock stack")
    x = stack_service.add(stack)
    assert x.name == stack.name


class MockStack:
    val = {
        "time_created": "2021-06-16T00:00:00",
        "name": "uat-blue",
        "time_updated": "2021-06-16T00:00:00",
        "id": 0,
    }


class MockStackTenants:
    val = {
        "time_created": "2021-06-16T00:00:00",
        "name": "uat-blue",
        "time_updated": "2021-06-16T00:00:00",
        "id": 0,
        "tenants": [
            {
                "time_created": "2021-06-16T00:00:00",
                "name": "test",
                "time_updated": "2021-06-16T00:00:00",
                "id": 0,
                "paused": "true",
            }
        ],
    }


@patch.object(Stack, "__init__")
def test_new_stack(mock_stack, mock_session, stack_service):
    # input
    mock_session().__enter__().query().filter_by().one_or_none.return_value = None
    mock_stack.return_value = None

    new_stack = MagicMock(return_value=addict.Dict({"name": "stack"}))

    input_stack = "mock stack"
    with patch.multiple("data_platform_config.services.stack", Stack=new_stack) as _:
        stack_service.add(StackCreate(name=input_stack))
    # insert called
    mock_session().__enter__().add.assert_called_once()
    # session commit called
    mock_session().__enter__().commit.assert_called_once()


def test_get_all_by_name(mock_session, stack_service):
    # input
    mock_session().__enter__().query().outerjoin().options().filter().one = MockStack
    result = stack_service.get_by_name("uat-blue", None)

    assert result.val["name"] == "uat-blue"
    assert len(result.val) == 4
    assert mock_session().__enter__().query().all.called_once()


@patch.object(Stack, "tenants")
def test_get_all_by_name_paused(mock_stack_tenant, mock_session, stack_service):
    mock_session().__enter__().query().outerjoin().options().filter().one = MockStackTenants
    mock_stack_tenant.return_value = None

    result = stack_service.get_by_name("uat-blue", True)

    assert result.val["name"] == "uat-blue"
    assert len(result.val) == 5
    assert len(result.val["tenants"]) == 1
    assert mock_session().__enter__().query().all.called_once()


def test_delete_stack_found(mock_session, stack_service):
    mock_stack = MagicMock()
    mock_session().__enter__().query().filter_by().one_or_none.return_value = mock_stack

    stack_service.delete("mock stack")

    mock_session().__enter__().delete.assert_called_once_with(mock_stack)
    mock_session().__enter__().commit.assert_called_once()


def test_delete_stack_not_found(mock_session, stack_service):
    mock_session().__enter__().query().filter_by().one_or_none.return_value = None

    with pytest.raises(DataNotFound, match="Stack 'mock stack' not found"):
        stack_service.delete("mock stack")

    mock_session().__enter__().delete.assert_not_called()
    mock_session().__enter__().commit.assert_not_called()

"""Tests for Workflow."""

import pytest
from aries_config_api_httpschema.workflow import WorkflowCreate
from data_platform_config.models.system.workflow import Workflow
from data_platform_config.services.workflow import WorkflowService
from mock.mock import MagicMock


@pytest.fixture
def workflow_service(mock_session):
    return WorkflowService(session_factory=mock_session)


def test_get_config_all(mock_session, workflow_service):
    """Test for get_config_all True Case."""
    config = Workflow(name="email", storage_prefix="unknown")
    configs = [config]
    mock_session().__enter__().query().filter().all.return_value = configs
    return_val = workflow_service.get_all(storage_prefix=None)
    assert return_val[0] == config


def test_add_new_workflow(mock_session, workflow_service):
    mock_session().__enter__().query().filter().one_or_none.return_value = None

    workflow_name = "test"
    workflow_details = WorkflowCreate(
        streamed=True,
    )
    workflow_service.add(workflow_name, workflow_details)
    # insert called
    mock_session().__enter__().add.assert_called_once()
    # session commit called
    mock_session().__enter__().commit.assert_called_once()


def test_add_existing_workflow(mock_session, workflow_service):
    # already exists with same values
    mock_session().__enter__().query().filter().one_or_none.return_value = MagicMock(
        name="test",
        streamed=True,
        storage_prefix=None,
    )
    workflow_name = "test"
    workflow_details = WorkflowCreate(
        streamed=True,
    )
    workflow_service.add(workflow_name, workflow_details)
    mock_session().__enter__().commit.assert_called_once()

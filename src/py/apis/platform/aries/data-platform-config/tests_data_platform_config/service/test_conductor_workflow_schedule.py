# flake8: noqa: E712
import datetime
import pydantic
import pytest
from aries_config_api_httpschema.conductor_workflow_schedule import (
    BulkConductorWorkflowScheduleCreateUpdate,
    ConductorWorkflowInputScheduleTypeEnum,
    ConductorWorkflowScheduleCreate,
    ConductorWorkflowScheduleInfo,
    ConductorWorkflowScheduleInterval,
    ConductorWorkflowScheduleUpdate,
    ConductorWorkflowScheduleWorkflowInput,
)
from aries_io_event.io_param import IOParamFieldSet
from data_platform_config.exceptions import DataAlreadyExists, DataNotFound
from data_platform_config.models.system.conductor_workflow_schedule import ConductorWorkflowSchedule
from data_platform_config.models.system.stack import Stack
from data_platform_config.models.system.tenant import Tenant
from data_platform_config.services.conductor_workflow_schedule import (
    ConductorWorkflowScheduleService,
)
from fastapi import HTTPException
from freezegun import freeze_time
from mock.mock import DEFAULT, MagicMock, patch
from sqlalchemy import null


@pytest.fixture
def mock_service(mock_session):
    return ConductorWorkflowScheduleService(session_factory=mock_session)


@freeze_time("2023-10-11 15:04:00")
def test_cron_validation():
    schedule_create = ConductorWorkflowScheduleCreate(
        schedule_type=ConductorWorkflowInputScheduleTypeEnum.CRON,
        workflow_input=ConductorWorkflowScheduleWorkflowInput(io_param=IOParamFieldSet()),
        workflow_name="mock workflow name",
        interval=None,
        cron="*/5 * * * *",
    )
    assert schedule_create.cron == "*/5 * * * *"

    with pytest.raises(ValueError, match="cron executions must be at least 5 minutes apart"):
        ConductorWorkflowScheduleCreate(
            schedule_type=ConductorWorkflowInputScheduleTypeEnum.CRON,
            workflow_input=ConductorWorkflowScheduleWorkflowInput(io_param=IOParamFieldSet()),
            workflow_name="mock workflow name",
            interval=None,
            cron="*/4 * * * *",
        )


@patch.multiple(
    "data_platform_config.services.conductor_workflow_schedule", ConductorWorkflowSchedule=DEFAULT
)
def test_create(mock_session, mock_service, **kwargs):
    # input
    mock_tenant_workflow_schedule_create = MagicMock()
    mock_session().__enter__().query().join().filter().filter().one_or_none.return_value = None

    with pytest.raises(DataNotFound, match="Tenant mock tenant does not exists"):
        mock_service.create(
            stack_name="mock-stack",
            tenant_name="mock tenant",
            schedule_id="mock schedule id",
            tenant_workflow_schedule_create=mock_tenant_workflow_schedule_create,
        )

    # tenant exists
    mock_tenant = MagicMock()
    mock_session().__enter__().query().join().filter().filter().one_or_none.return_value = (
        mock_tenant
    )

    # schedule exists and returns a mocked schedule
    mock_schedule = MagicMock()

    mock_session().__enter__().query().filter_by().one_or_none.return_value = mock_schedule

    # assert that DataAlreadyExists is raised
    with pytest.raises(DataAlreadyExists, match="Schedule id is already defined mock schedule id"):
        mock_service.create(
            stack_name="mock-stack",
            tenant_name="mock tenant",
            schedule_id="mock schedule id",
            tenant_workflow_schedule_create=mock_tenant_workflow_schedule_create,
        )

    # schedule does not exist
    mock_session().__enter__().query().filter_by().one_or_none.return_value = None

    schedule_create = ConductorWorkflowScheduleCreate(
        schedule_type=ConductorWorkflowInputScheduleTypeEnum.CRON,
        workflow_input=ConductorWorkflowScheduleWorkflowInput(io_param=IOParamFieldSet()),
        workflow_name="mock workflow name",
        interval=None,
        cron="0 1 * * *",
    )
    kwargs["ConductorWorkflowSchedule"].return_value.schedule_type = "cron"
    kwargs["ConductorWorkflowSchedule"].return_value.workflow_input = dict(io_param=dict(params={}))
    kwargs["ConductorWorkflowSchedule"].return_value.workflow_name = "mock workflow name"
    kwargs["ConductorWorkflowSchedule"].return_value.interval = None
    kwargs["ConductorWorkflowSchedule"].return_value.cron = "0 * * * *"
    kwargs["ConductorWorkflowSchedule"].return_value.tenant_id = "mock tenant id"
    kwargs["ConductorWorkflowSchedule"].return_value.schedule_id = "mock schedule id"
    kwargs["ConductorWorkflowSchedule"].return_value.paused = False
    kwargs["ConductorWorkflowSchedule"].return_value.deleted = False
    # assert that the schedule is added to the session
    ret = mock_service.create(
        stack_name="mock-stack",
        tenant_name="mock tenant",
        schedule_id="mock schedule id",
        tenant_workflow_schedule_create=schedule_create,
    )
    mock_session().__enter__().add.assert_called_once_with(
        kwargs["ConductorWorkflowSchedule"].return_value
    )
    assert ret == ConductorWorkflowScheduleInfo(
        schedule_type=ConductorWorkflowInputScheduleTypeEnum.CRON,
        workflow_input=ConductorWorkflowScheduleWorkflowInput(io_param=IOParamFieldSet(params={})),
        workflow_name="mock workflow name",
        interval=None,
        cron="0 * * * *",
        schedule_id="mock schedule id",
        paused=False,
        deleted=False,
    )


def test_get(mock_session, mock_service):
    # assert that session query is called with the correct parameters
    mock_service.get("mock_schedule_id")
    mock_session().__enter__().query().options().filter_by().one.assert_called_once()


def test_update_patch(mock_service, mock_session):
    mock_schedule = MagicMock()
    mock_session().__enter__().query().filter_by().one.return_value = mock_schedule
    mock_schedule.schedule_type = "cron"
    mock_schedule.workflow_input = dict(io_param=dict(params={}))
    mock_schedule.workflow_name = "mock workflow name"
    mock_schedule.interval = None
    mock_schedule.cron = "0 * * * *"
    mock_schedule.tenant_id = "mock tenant id"
    mock_schedule.schedule_id = "mock schedule id"
    mock_schedule.paused = False
    mock_schedule.deleted = False

    schedule_update = ConductorWorkflowScheduleUpdate(
        schedule_type=ConductorWorkflowInputScheduleTypeEnum.INTERVAL,
        workflow_input=ConductorWorkflowScheduleWorkflowInput(
            io_param=IOParamFieldSet(params=dict(a=1))
        ),
        workflow_name="updated",
        interval=ConductorWorkflowScheduleInterval(hours=2, seconds=2),
        deleted=True,
        paused=True,
    )

    ret = mock_service.update_patch("mock_schedule_id", schedule_update)
    mock_session().__enter__().commit.assert_called_once()
    assert ret == ConductorWorkflowScheduleInfo(
        schedule_type=ConductorWorkflowInputScheduleTypeEnum.INTERVAL,
        workflow_input=ConductorWorkflowScheduleWorkflowInput(
            io_param=IOParamFieldSet(params={"a": 1})
        ),
        workflow_name="updated",
        interval=ConductorWorkflowScheduleInterval(hours=2, seconds=2),
        cron=None,
        schedule_id="mock schedule id",
        paused=True,
        deleted=True,
    )


@patch.multiple(
    "data_platform_config.services.conductor_workflow_schedule",
    select=DEFAULT,
)
def test_get_all(mock_service, mock_session, **kwargs):
    # call get_all without any query params, and assert that session query is called with
    # the correct parameters
    mock_service.get_all()
    # assert that mocked select function is called with the correct parameters
    mock_qry = kwargs["select"].return_value.options.return_value
    mock_session().__enter__().execute.assert_called_once_with(mock_qry)

    # call with stack = "mocked_stack" as assert that mock_qry is called with correct where clause
    mock_service.get_all(stack="mocked_stack")
    exp = ConductorWorkflowSchedule.tenant_id == Tenant.id
    assert mock_qry.where.call_args[0][0].compare(exp)
    exp = Tenant.stack_id == Stack.id
    assert mock_qry.where().where.call_args[0][0].compare(exp)
    exp = Stack.name == "mocked_stack"
    assert mock_qry.where().where().where.call_args[0][0].compare(exp)

    # call with stack="mocked_stack" and paused=True and assert that mock_qry is called
    # with correct where clause
    mock_qry.reset_mock()
    mock_service.get_all(stack="mocked_stack", paused=True)
    exp = ConductorWorkflowSchedule.tenant_id == Tenant.id
    assert mock_qry.where.call_args[0][0].compare(exp)
    exp = Tenant.stack_id == Stack.id
    assert mock_qry.where().where.call_args[0][0].compare(exp)
    exp = Stack.name == "mocked_stack"
    assert mock_qry.where().where().where.call_args[0][0].compare(exp)
    exp = ConductorWorkflowSchedule.paused == True
    assert mock_qry.where().where().where().where.call_args[0][0].compare(exp)

    # call with last_update=2023-02-01T00:00:00Z and delete=False and assert that
    # mock_qry is called with correct where clause
    mock_qry.reset_mock()
    mock_service.get_all(last_updated=datetime.datetime(year=2023, month=2, day=1), deleted=False)
    exp = ConductorWorkflowSchedule.deleted == False
    assert mock_qry.where.call_args[0][0].compare(exp)
    exp = (
        ConductorWorkflowSchedule.time_updated >= datetime.datetime(year=2023, month=2, day=1)
    ) | (
        (ConductorWorkflowSchedule.time_updated == null())
        & (ConductorWorkflowSchedule.time_created >= datetime.datetime(year=2023, month=2, day=1))
    )
    assert mock_qry.where().where.call_args[0][0].compare(exp)


@patch.multiple(
    "data_platform_config.services.conductor_workflow_schedule",
    update=DEFAULT,
)
def test_toggle_pause(mock_service, mock_session, **kwargs):
    with pytest.raises(HTTPException) as e:
        mock_service.toggle_pause(paused=True, workflow_name="workflow")
    assert e.value.status_code == 400

    mock_qry = kwargs["update"].return_value

    mock_service.toggle_pause(paused=True, workflow_name="workflow", tenant="tenant")
    exp = ConductorWorkflowSchedule.workflow_name == "workflow"
    assert mock_qry.where.call_args[0][0].compare(exp)
    exp = ConductorWorkflowSchedule.tenant_id == Tenant.id
    assert mock_qry.where().where.call_args[0][0].compare(exp)
    exp = Tenant.name == "tenant"
    assert mock_qry.where().where().where.call_args[0][0].compare(exp)
    mock_session().__enter__().commit.assert_called_once()

    mock_qry.reset_mock()
    mock_service.toggle_pause(paused=True, workflow_name="workflow", tenant="tenant", stack="stack")
    exp = ConductorWorkflowSchedule.workflow_name == "workflow"
    assert mock_qry.where.call_args[0][0].compare(exp)
    exp = ConductorWorkflowSchedule.tenant_id == Tenant.id
    assert mock_qry.where().where.call_args[0][0].compare(exp)
    exp = Tenant.name == "tenant"
    assert mock_qry.where().where().where.call_args[0][0].compare(exp)
    exp = Tenant.stack_id == Stack.id
    assert mock_qry.where().where().where().where.call_args[0][0].compare(exp)
    exp = Stack.name == "stack"
    assert mock_qry.where().where().where().where().where.call_args[0][0].compare(exp)


def test_bulk_upsert_conductor_workflow_schedules_existing_tenant(mock_service, mock_session):
    mock_tenant = Tenant(name="mock tenant")
    mock_session().__enter__().query().join().filter().filter().one_or_none.side_effect = [
        mock_tenant,
        None,
    ]

    mock_bulk_tenant_workflow_schedules_create = MagicMock()
    mock_bulk_tenant_workflow_schedules_create.schedules = [
        BulkConductorWorkflowScheduleCreateUpdate(
            schedule_id="mock schedule id",
            schedule_type=ConductorWorkflowInputScheduleTypeEnum.CRON,
            workflow_input=ConductorWorkflowScheduleWorkflowInput(io_param=IOParamFieldSet()),
            workflow_name="mock workflow name",
            interval=None,
            cron="0 1 * * *",
            paused=False,
            deleted=False,
        )
    ]

    mock_service.bulk_upsert_conductor_workflow_schedules(
        stack_name="mock-stack",
        tenant_name="mock tenant",
        bulk_tenant_workflow_schedules_create=mock_bulk_tenant_workflow_schedules_create,
    )

    mock_session().__enter__().query().filter_by().one_or_none.assert_called()
    mock_session().__enter__().commit.assert_called_once_with()


def test_bulk_upsert_conductor_workflow_schedules_new_tenant(mock_service, mock_session):
    mock_session().__enter__().query().join().filter().filter().one_or_none.return_value = None

    with pytest.raises(DataNotFound, match="Tenant mock tenant does not exists"):
        mock_service.bulk_upsert_conductor_workflow_schedules(
            stack_name="mock-stack",
            tenant_name="mock tenant",
            bulk_tenant_workflow_schedules_create=MagicMock(),
        )

    mock_tenant = Tenant(name="mock tenant")
    mock_session().__enter__().query().join().filter().filter().one_or_none.return_value = (
        mock_tenant
    )

    mock_bulk_tenant_workflow_schedules_create = MagicMock()
    mock_bulk_tenant_workflow_schedules_create.schedules = [
        BulkConductorWorkflowScheduleCreateUpdate(
            schedule_id="mock schedule id",
            schedule_type=ConductorWorkflowInputScheduleTypeEnum.CRON,
            workflow_input=ConductorWorkflowScheduleWorkflowInput(io_param=IOParamFieldSet()),
            workflow_name="mock workflow name",
            interval=None,
            cron="0 1 * * *",
        )
    ]

    mock_bulk_tenant_workflow_schedules_create.schedules[0].paused = True
    mock_session().__enter__().query().join().filter().filter().one_or_none.return_value = (
        mock_tenant
    )
    mock_service.bulk_upsert_conductor_workflow_schedules(
        stack_name="mock-stack",
        tenant_name="mock tenant",
        bulk_tenant_workflow_schedules_create=mock_bulk_tenant_workflow_schedules_create,
    )
    mock_session().__enter__().query().filter_by().one_or_none.assert_called()
    mock_session().__enter__().commit.assert_called_once_with()


def test_bulk_upsert_conductor_workflow_schedules_new_schedule(mock_service, mock_session):
    mock_tenant = Tenant(name="mock tenant")

    mock_session().__enter__().query().join().filter().filter().one_or_none.return_value = (
        mock_tenant
    )
    mock_session().__enter__().query().filter_by().one_or_none.return_value = None

    mock_bulk_tenant_workflow_schedules_create = MagicMock()

    with pytest.raises(pydantic.ValidationError):
        mock_bulk_tenant_workflow_schedules_create.schedules = [
            BulkConductorWorkflowScheduleCreateUpdate(
                schedule_id="mock schedule id",
                schedule_type=ConductorWorkflowInputScheduleTypeEnum.CRON,
                workflow_input=ConductorWorkflowScheduleWorkflowInput(io_param=IOParamFieldSet()),
                interval=None,
                paused=False,
                deleted=False,
            )
        ]
        mock_service.bulk_upsert_conductor_workflow_schedules(
            stack_name="mock-stack",
            tenant_name="mock tenant",
            bulk_tenant_workflow_schedules_create=mock_bulk_tenant_workflow_schedules_create,
        )

    mock_bulk_tenant_workflow_schedules_create.schedules[0].workflow_name = "mock workflow name"
    mock_bulk_tenant_workflow_schedules_create.schedules[
        0
    ].workflow_input = ConductorWorkflowScheduleWorkflowInput(
        io_param=IOParamFieldSet(params={"mock_param": "mock_value"})
    )
    mock_session().__enter__().query().join().filter().filter().one_or_none.return_value = (
        mock_tenant
    )
    mock_session().__enter__().query().filter_by().one_or_none.return_value = None

    mock_bulk_tenant_workflow_schedules_create.schedules[0].cron = "0 * * * *"
    mock_service.bulk_upsert_conductor_workflow_schedules(
        stack_name="mock-stack",
        tenant_name="mock tenant",
        bulk_tenant_workflow_schedules_create=mock_bulk_tenant_workflow_schedules_create,
    )

    mock_session().__enter__().commit.assert_called_once_with()


@freeze_time("2023-10-11 15:04:00")
def test_cron_validation_bulk_upsert(mock_service, mock_session):
    mock_tenant = Tenant(name="mock tenant")
    mock_session().__enter__().query().filter_by().one_or_none.side_effect = [
        mock_tenant,
        None,
    ]

    mock_bulk_tenant_workflow_schedules_create = MagicMock()

    with pytest.raises(ValueError, match="cron executions must be at least 5 minutes apart"):
        mock_bulk_tenant_workflow_schedules_create.schedules = [
            BulkConductorWorkflowScheduleCreateUpdate(
                schedule_id="mock schedule id",
                schedule_type=ConductorWorkflowInputScheduleTypeEnum.CRON,
                workflow_input=ConductorWorkflowScheduleWorkflowInput(io_param=IOParamFieldSet()),
                workflow_name="mock workflow name",
                interval=None,
                cron="*/4 * * * *",
                paused=False,
                deleted=False,
            )
        ]

    with pytest.raises(ValueError, match="interval must be more than 5 minutes"):
        mock_bulk_tenant_workflow_schedules_create.schedules = [
            BulkConductorWorkflowScheduleCreateUpdate(
                schedule_id="mock schedule id",
                schedule_type=ConductorWorkflowInputScheduleTypeEnum.INTERVAL,
                workflow_input=ConductorWorkflowScheduleWorkflowInput(io_param=IOParamFieldSet()),
                workflow_name="mock workflow name",
                interval=ConductorWorkflowScheduleInterval(minutes=4),
                cron="*/4 * * * *",
                paused=False,
                deleted=False,
            )
        ]


def test_bulk_upsert_conductor_workflow_schedules_new_tenant_raise_exception(
    mock_service, mock_session
):
    mock_tenant = Tenant(name="mock tenant")
    mock_session().__enter__().query().join().filter().filter().one_or_none.return_value = (
        mock_tenant
    )
    mock_session().__enter__().query().filter_by().one_or_none.return_value = None

    mock_bulk_tenant_workflow_schedules_create = MagicMock()
    mock_bulk_tenant_workflow_schedules_create.schedules = [
        BulkConductorWorkflowScheduleCreateUpdate(
            schedule_id="mock schedule id",
            schedule_type=None,
            workflow_input=ConductorWorkflowScheduleWorkflowInput(io_param=IOParamFieldSet()),
            workflow_name="mock workflow name",
            interval=None,
            cron=None,
        )
    ]
    with pytest.raises(HTTPException):
        mock_service.bulk_upsert_conductor_workflow_schedules(
            stack_name="mock-stack",
            tenant_name="mock tenant",
            bulk_tenant_workflow_schedules_create=mock_bulk_tenant_workflow_schedules_create,
        )

import addict
import pytest
from aries_config_api_httpschema.intelligent_voice import IntelligentVoiceConfigUpdate
from data_platform_config.exceptions import DataNotFound
from data_platform_config.services.intelligent_voice import IntelligentVoiceService
from mock.mock import DEFAULT, MagicMock, patch


@pytest.fixture
def intelligent_voice_service(mock_session):
    return IntelligentVoiceService(session_factory=mock_session)


def test_get_config(mock_session, intelligent_voice_service):
    """Test for get_config."""
    config = MagicMock()
    mock_session().__enter__().query().join().filter().filter().one_or_none.return_value = config
    return_val = intelligent_voice_service.get_config(
        stack_name="mock-stack",
        tenant_name="test_tenant",
    )
    assert return_val == config


def test_get_config_tenant_not_found(mock_session, intelligent_voice_service):
    """Test for get_config with no tenant."""
    mock_session().__enter__().query().join().filter().filter().one_or_none.return_value = None
    with pytest.raises(DataNotFound):
        intelligent_voice_service.get_config(
            stack_name="mock-stack",
            tenant_name="test_tenant",
        )


@patch.multiple("data_platform_config.services.intelligent_voice", insert=DEFAULT, datetime=DEFAULT)
def test_upsert_success(mock_session, intelligent_voice_service, **kwargs):
    """Test for upsert config."""
    mock_session().__enter__().query().join().filter().filter().one_or_none.return_value = (
        addict.Dict({"id": "mock_tenant"})
    )
    intelligent_voice_service.upsert_config(
        stack_name="mock-stack",
        tenant_name="test_tenant",
        config_to_upsert=IntelligentVoiceConfigUpdate(languages=["test"]),
    )
    assert kwargs["insert"].return_value.values().on_conflict_do_update.call_count == 1


@patch.multiple("data_platform_config.services.intelligent_voice", insert=DEFAULT, datetime=DEFAULT)
def test_upsert_no_tenant(intelligent_voice_service, mock_session, **kwargs):
    """Test for upsert config with no tenant."""
    mock_session().__enter__().query().join().filter().filter().one_or_none.return_value = None
    with pytest.raises(DataNotFound):
        intelligent_voice_service.upsert_config(
            stack_name="mock-stack",
            tenant_name="test_tenant",
            config_to_upsert=IntelligentVoiceConfigUpdate(languages=["test"]),
        )

    kwargs["insert"].return_value.values.assert_not_called()
    assert kwargs["insert"].return_value.values().on_conflict_do_update.call_count == 0

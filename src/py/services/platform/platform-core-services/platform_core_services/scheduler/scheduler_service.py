import datetime
import logging
import sentry_sdk
import time
from apscheduler.events import (
    EVENT_JOB_ERROR,
    EVENT_JOB_EXECUTED,
    EVENT_JOB_SUBMITTED,
    JobExecutionEvent,
    JobSubmissionEvent,
)
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.model import IOEvent
from aries_io_event.task import TaskFieldSet
from aries_io_event.task_metric import TaskMetricFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_se_api_client.client import AriesApiClient
from aries_utils.lifecycle_handler import LifecycleHandler
from aries_utils.oma import OMAClient
from data_platform_config_api_client.conductor_workflow_schedule import ConductorWorkflow<PERSON>cheduleAP<PERSON>
from enum import StrEnum, auto
from platform_core_services.scheduler.config import scheduler_service_config
from platform_core_services.scheduler.workflow_submitter import submit_workflow
from se_boltons.log_config import configure_logging

DEBUG = bool(int(scheduler_service_config.debug))
configure_logging()
logger = logging.getLogger("aries_scheduler_service")

if bool(int(scheduler_service_config.sentry.enabled)):
    sentry_sdk.init(
        dsn=scheduler_service_config.sentry.dsn,
        release=scheduler_service_config.task.version,
        environment=scheduler_service_config.stack,
        debug=DEBUG,
    )


class SCHEDULE_TYPE(StrEnum):
    INTERVAL = auto()
    CRON = auto()


class SchedulerOperationCode(StrEnum):
    SUBMITTED = auto()
    ERROR = auto()
    RESUMED = auto()
    CREATED = auto()
    UPDATED = auto()
    PAUSED = auto()
    DELETED = auto()
    EXECUTED = auto()


class SchedulerService:
    """Triggers conductor workflows based on schedules stored in the
    database."""

    def __init__(self):
        self._lifecycle_handler = LifecycleHandler()

        self._config_api_client = AriesApiClient(
            host=scheduler_service_config.data_platform_config.api.url
        )
        self._workflow_api = ConductorWorkflowScheduleAPI(self._config_api_client)

        self._oma_client = OMAClient(
            oma_rest_proxy_url=scheduler_service_config.oma.rest_proxy_url,
            oma_topic=scheduler_service_config.oma.aries_topic,
        )
        # coalesce is set to True to combine all missed executions into one job execution.
        # max_instances is set to 1 to prevent concurrent executions of the same job, since its
        # our requirement to have only one execution of a job at a time.
        # misfire_grace_time is set to None, to prevent any misfires. Misfire is defined as a
        # apscheduler job, which will not be executed any further, because it was not executed
        # at desired execution time, as per its schedule. We dont want to have any misfires, so
        # setting to None ensure that apscheduler fires the job at the next available time. Along
        # with coalesce, this ensures that any missed executions are combined into one execution,
        # whenever possible
        self._job_store = SQLAlchemyJobStore(url=scheduler_service_config.db.url)
        self._scheduler = BackgroundScheduler(
            jobstores=dict(
                default=self._job_store,
            ),
            job_defaults=dict(coalesce=True, max_instances=1, misfire_grace_time=None),
        )

        # add event listeners, before starting scheduler, to ensure that no events are missed when
        # scheduler is running and starts submitting jobs
        self._scheduler.add_listener(self.handle_apscheduler_job_executed_event, EVENT_JOB_EXECUTED)
        self._scheduler.add_listener(
            self.handle_apscheduler_job_execution_error_event, EVENT_JOB_ERROR
        )
        self._scheduler.add_listener(
            self.handle_apscheduler_job_submission_event, EVENT_JOB_SUBMITTED
        )

        # start
        self._scheduler.start()

        # sync schedules
        self._last_scheduled_updated_utc = None
        self._start_time_utc = datetime.datetime.utcnow()

    def _send_oma_event(
        self,
        operation_code: SchedulerOperationCode,
        schedule_id: str,
        failure_reason: str | None = None,
        success: bool = True,
    ):
        """Sends an IOEvent to OMA.

        Uses workflow.name and task.name as aries-scheduler. Uses
        workflow.tenant and workflow.stack as stack name
        """
        target_event = IOEvent(
            workflow=WorkflowFieldSet(
                name=scheduler_service_config.task.name,
                tenant=scheduler_service_config.stack,
                start_timestamp=datetime.datetime.utcnow(),
                stack=scheduler_service_config.stack,
            ),
            task=TaskFieldSet(
                name=scheduler_service_config.task.name,
                version=scheduler_service_config.task.version,
                success=success,
            ),
            task_metric=TaskMetricFieldSet(
                metrics=dict(
                    dpf=dict(
                        aries_scheduler=dict(
                            operation_code=operation_code,
                            schedule_id=schedule_id,
                            failure_reason=failure_reason,
                        )
                    )
                )
            ),
        )
        self._oma_client.send(io_event=target_event)

    def handle_apscheduler_job_submission_event(self, event: JobSubmissionEvent):
        """Triggered when apscheduler submits a job for execution."""
        self._send_oma_event(
            operation_code=SchedulerOperationCode.SUBMITTED,
            schedule_id=event.job_id,
        )

    def handle_apscheduler_job_execution_error_event(self, event: JobExecutionEvent):
        """Triggered when job execution fails."""
        failure_reason = str(event.exception)
        logger.error(f"Error in workflow submission {event.job_id} -- {failure_reason}")
        self._send_oma_event(
            operation_code=SchedulerOperationCode.ERROR,
            schedule_id=event.job_id,
            success=False,
            failure_reason=failure_reason,
        )

    def handle_apscheduler_job_executed_event(self, event: JobExecutionEvent):
        """Triggered when job execution is successful."""
        self._send_oma_event(
            operation_code=SchedulerOperationCode.EXECUTED,
            schedule_id=event.job_id,
        )

    def sync_schedules(self):
        """Syncs schedules from the database to the scheduler."""
        logger.info("Syncing schedules")
        discard_orphaned_jobs = False
        if self._last_scheduled_updated_utc is None:
            # first time sync removes orphaned jobs
            discard_orphaned_jobs = True
        orphan_jobs = set([job.id for job in self._job_store.get_all_jobs()])
        logger.info(f"Found jobs in scheduler -- {orphan_jobs}")
        for schedule in self._get_schedules():
            try:
                if schedule.schedule_type == SCHEDULE_TYPE.CRON.value:
                    trigger = CronTrigger.from_crontab(schedule.cron, timezone="UTC")
                else:
                    trigger = IntervalTrigger(
                        weeks=schedule.interval.get("weeks", 0),
                        days=schedule.interval.get("days", 0),
                        hours=schedule.interval.get("hours", 0),
                        minutes=schedule.interval.get("minutes", 0),
                        seconds=schedule.interval.get("seconds", 0),
                        timezone="UTC",
                    )
                # A job in config-db is not orphaned
                orphan_jobs.discard(schedule.schedule_id)
                job = self._scheduler.get_job(schedule.schedule_id)
                if not job:
                    if schedule.deleted:
                        logger.info(f"Found deleted schedule {schedule.schedule_id}")
                        self._send_oma_event(SchedulerOperationCode.DELETED, schedule.schedule_id)
                        # job is deleted, noop
                        continue
                    # schedule job
                    io_param = schedule.workflow_input and schedule.workflow_input.io_param
                    io_param = (
                        IOParamFieldSet.parse_obj(io_param) if io_param else IOParamFieldSet()
                    )
                    new_job = self._scheduler.add_job(
                        func=submit_workflow,
                        trigger=trigger,
                        id=schedule.schedule_id,
                        kwargs=dict(
                            io_param=io_param,
                            tenant=schedule.tenant.name,
                            workflow_name=schedule.workflow_name,
                        ),
                    )
                    logger.info(f"Added a new schedule {schedule.schedule_id}")
                    self._send_oma_event(SchedulerOperationCode.CREATED, schedule.schedule_id)
                    if schedule.paused:
                        new_job.pause()
                        logger.info(f"Paused new schedule {schedule.schedule_id}")
                        self._send_oma_event(SchedulerOperationCode.PAUSED, schedule.schedule_id)

                else:
                    # modify existing job trigger
                    if schedule.deleted:
                        job.remove()
                        logger.info(f"Removed schedule {schedule.schedule_id}")
                        self._send_oma_event(SchedulerOperationCode.DELETED, schedule.schedule_id)
                    else:
                        io_param = schedule.workflow_input and schedule.workflow_input.io_param
                        io_param = (
                            IOParamFieldSet.parse_obj(io_param) if io_param else IOParamFieldSet()
                        )
                        job.modify(
                            func=submit_workflow,
                            trigger=trigger,
                            kwargs=dict(
                                io_param=io_param,
                                tenant=schedule.tenant.name,
                                workflow_name=schedule.workflow_name,
                            ),
                        )
                        logger.info(f"Modified schedule {schedule.schedule_id}")
                        self._send_oma_event(SchedulerOperationCode.UPDATED, schedule.schedule_id)
                        if schedule.paused:
                            job.pause()
                            logger.info(f"Paused schedule {schedule.schedule_id}")
                            self._send_oma_event(
                                SchedulerOperationCode.PAUSED, schedule.schedule_id
                            )
                        else:
                            is_job_paused = False
                            if not job.next_run_time:
                                is_job_paused = True
                            if schedule.schedule_type == SCHEDULE_TYPE.CRON.value:
                                # for cron, we can call the resume function to recompute the next
                                # run time, because the cron is computed from reference time of
                                # midnight, so recomputing the next run time wont affect the
                                # schedule if cron was unchanged
                                job.resume()
                            else:
                                # intervals are computed from current runtime. If we recompute
                                # interval, then current next_run_time is updated to the next run
                                # time. It's possible that next run time always keeps getting
                                # updated to a future value, if the job is resumed more frequently
                                # than the interval. So, we need to check if the job is paused,
                                # before resuming it
                                if is_job_paused:
                                    job.resume()
                                else:
                                    # Since next run time is already present based on current
                                    # interval, we let the job run at the next run time, without
                                    # recomputing the next run time. The next_run_time will get
                                    # recomputed after the job is executed on current next run time
                                    logger.info(
                                        f"Skip recomputing interval schedule "
                                        f"{schedule.schedule_id}, existing next_run_time is "
                                        f"{job.next_run_time}"
                                    )
                            if is_job_paused:
                                logger.info(
                                    f"Resumed schedule {schedule.schedule_id} with new next run "
                                    f"time"
                                )
                                self._send_oma_event(
                                    SchedulerOperationCode.RESUMED, schedule.schedule_id
                                )

            except Exception as e:  # pragma: no cover
                # failure in any schedule should not affect other schedules
                logger.exception(f"Error processing job {e}")
                self._send_oma_event(
                    SchedulerOperationCode.ERROR,
                    schedule.schedule_id,
                    failure_reason=str(e),
                    success=False,
                )
        # remove all jobs which are not in the database
        if discard_orphaned_jobs:
            for job in orphan_jobs:
                self._scheduler.remove_job(job)
                logger.error(f"Removed orphan job {job}")
                self._send_oma_event(SchedulerOperationCode.DELETED, job)

    def _get_schedules(self):
        """Get all schedules from the database."""
        # notice the update time, before calling API, to avoid missing some schedules which were
        # updated during the API call
        current_sync_utc = datetime.datetime.utcnow()
        schedules = self._workflow_api.get_all(
            stack=scheduler_service_config.stack,
            last_updated_utc=self._last_scheduled_updated_utc,
        ).content
        self._last_scheduled_updated_utc = current_sync_utc
        return schedules

    def run(self):
        try:
            while self._lifecycle_handler.running:
                # sleep for a while before checking for new schedules
                time.sleep(15)
                logger.debug("Checking for sync timeout")
                curr_time_utc = datetime.datetime.utcnow()
                if not self._last_scheduled_updated_utc or (
                    (curr_time_utc - self._last_scheduled_updated_utc).seconds
                    > int(scheduler_service_config.schedule_sync_interval_s)
                ):
                    # sync schedules from DB, if the last sync time has crossed the threshold or
                    # this is the first time
                    self.sync_schedules()
                if (curr_time_utc - self._start_time_utc).seconds > int(
                    scheduler_service_config.restart_interval_s
                ):
                    # restart service once in a while to avoid to have a clean refresh of all
                    # schedules. This is a workaround for the issue that the scheduler does not
                    # have information of some updated schedules
                    logger.info("Restarting service")
                    self._lifecycle_handler.shutdown()

        finally:
            logger.warning("Shutting down scheduler")
            self._scheduler.shutdown()


def main():  # pragma: no cover
    svc = SchedulerService()
    svc.run()  # blocks until finish


if __name__ == "__main__":  # pragma: no cover
    main()

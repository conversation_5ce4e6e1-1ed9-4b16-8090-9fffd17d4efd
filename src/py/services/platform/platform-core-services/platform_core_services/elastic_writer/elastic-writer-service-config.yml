stack: "${oc.env:STACK,local}"
# Null handled in code to make default topic
conductor_callback_events_topic: "${oc.env:CONDUCTOR_CALLBACK_EVENTS_TOPIC,null}"
data_platform_config_api_url: ${oc.env:DATA_PLATFORM_CONFIG_API_URL,https://aries-platform-config.dev-enterprise.steeleye.co/}
debug: ${oc.env:DEBUG,0}
elastic_api_key: ${oc.env:ELASTIC_API_KEY}
elastic_url: ${oc.env:ELASTIC_URL}
elastic_api_request_timeout_s: "${oc.env:ELASTIC_API_REQUEST_TIMEOUT_S,900}" # 15 Minutes
elastic_ssl_cert_file: ${oc.env:ELASTIC_SSL_CERT_FILE,"/etc/ssl/certs/es-ca.crt"}
kafka:
  bootstrap_servers: "${oc.env:KAFKA_BOOTSTRAP_SERVERS}"
  consumer_group_id: ${oc.env:CONSUMER_GROUP_ID,elastic-writer-service-${stack}}
  consumer_auto_offset_reset: ${oc.env:CONSUMER_AUTO_OFFSET_RESET,earliest}
  message_poll_timeout_s: ${oc.env:KAFKA_MESSAGE_POLL_TIMEOUT_S,30} # poll every 30 seconds, until a new message arrives
  rest_proxy_url: "${oc.env:KAFKA_REST_PROXY_URL}"
  consumer_max_poll_interval_ms: "${oc.env:KAFKA_CONSUMER_MAX_POLL_INTERVAL_MS,7200000}" # 2 Hours
  consumer_close_timeout_s: ${oc.env:KAFKA_CONSUMER_CLOSE_TIMEOUT_S,10}
kafka_data_events_topic: "${oc.env:KAFKA_DATA_EVENTS_TOPIC}"
max_chunk_bytes: "${oc.env:MAX_CHUNK_BYTES,5000000}" # 5MB, can be up to 100 MB as per spec of ES
max_chunk_count: "${oc.env:MAX_CHUNK_COUNT,1000}"
max_mget_compare_hash_batch_size: "${oc.env:MAX_MGET_COMPARE_HASH_BATCH_SIZE,1000}" # cant be greater than 10k
oma:
  rest_proxy_url: ${oc.env:OMA_REST_PROXY_URL,https://kafka-rest.dev-oma.steeleye.co}
  aries_topic: ${oc.env:OMA_ARIES_TOPIC,aries.oma.events}
es_bulk_rate_limit_max_retry_attempts: "${oc.env:ES_BULK_RATE_LIMIT_MAX_RETRY_ATTEMPTS,3}"
stack_pause_refresh_s: "${oc.env:STACK_PAUSE_REFRESH_S,600}"
write_audit: "${oc.env:WRITE_AUDIT,1}" # 1 to write successful records to cloud storage, 0 to write the records file empty
task:
  name: elastic-writer-service
  version: ${oc.env:SE_VERSION,local}
sentry:
  enabled: ${oc.env:SENTRY_ENABLED,0}
  dsn: "${oc.env:SENTRY_DSN,https://<EMAIL>/4508334873772032}"

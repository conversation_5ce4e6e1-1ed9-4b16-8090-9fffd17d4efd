stack: "${oc.env:<PERSON>AC<PERSON>,dev-blue}"
kafka:
  bootstrap_servers: "${oc.env:KAFKA_BOOTSTRAP_SERVERS}"
  consumer_group_id: ${oc.env:CONSUMER_GROUP_ID,aries-conductor-callback-service-${stack}}
  consumer_auto_offset_reset: ${oc.env:CONSUMER_AUTO_OFFSET_RESET,earliest}
  # Max 20 mins as we have backoff in 3 places of 5 mins each
  consumer_max_poll_interval_ms: ${oc.env:KAFKA_CONSUMER_MAX_POLL_INTERVAL_MS,1200000}
  message_poll_timeout_s: ${oc.env:KAFKA_MESSAGE_POLL_TIMEOUT_S,30}
  consumer_close_timeout_s: ${oc.env:KAFKA_CONSUMER_CLOSE_TIMEOUT_S,10}
callback_events_topic: "${oc.env:CALLBACK_EVENTS_TOPIC}"
conductor_api_url: ${oc.env:CONDUCTOR_API_URL}
conductor_api_backoff_max_time_s: ${oc.env:CONDUCTOR_API_BACKOFF_MAX_TIME_S,300}
debug: ${oc.env:DEBUG,0}
task:
  name: aries-conductor-callback-service
  version: ${oc.env:SE_VERSION,local}
sentry:
  enabled: ${oc.env:SENTRY_ENABLED,0}
  dsn: "${oc.env:SENTRY_DSN,https://<EMAIL>/4508335064285184}"

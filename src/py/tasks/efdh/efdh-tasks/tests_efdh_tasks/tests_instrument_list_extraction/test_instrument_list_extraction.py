import logging
import pytest
import re
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from datetime import datetime as dt
from efdh_tasks.instrument_list_extraction.instrument_list_extract_task import InstrumentListExtract
from efdh_tasks.instrument_list_extraction.static import (
    InstrumentListUploadInfo,
    MarketDataObjectInfo,
)
from pathlib import Path
from tests_efdh_tasks.tests_instrument_list_extraction.ile_mock_data import (
    mock_context_manager,
    mock_currency_norm_flag,
    mock_download_il_file,
    mock_fs,
    mock_httpx_client,
    mock_nanoid_generate,
    mock_pendulum_datetime,
    mock_schedule_extraction,
    mock_template,
)

TEST_PATH = Path(__file__).parent
logger = logging.getLogger(__name__)


@pytest.fixture(scope="function", autouse=True)
def mock_data_sources(monkeypatch):
    mock_schedule_extraction(monkeypatch)
    mock_download_il_file(monkeypatch)
    mock_pendulum_datetime(monkeypatch)
    mock_fs(monkeypatch)
    mock_template(monkeypatch)
    mock_httpx_client(monkeypatch)
    mock_context_manager(monkeypatch)
    mock_currency_norm_flag(monkeypatch)
    mock_nanoid_generate(monkeypatch)


@pytest.fixture(scope="function", autouse=True)
def task_input():
    return AriesTaskInput(
        workflow=WorkflowFieldSet(
            name="localtest",
            stack="dev-master-data",
            tenant="master-data",
            start_timestamp=str(dt.now()),
        ),
        input_param=IOParamFieldSet(
            params={
                "schedule_id": "0x07e6dba8153d956b",
                "lookback_period": 1,
                "instrument_list_type": "EOD",
                "instrument_list_name": "New_York",
            }
        ),
        task=TaskFieldSet(
            id="foo",
            name="market-data-ingestion",
            version="1",
            success=True,
            previous_id=None,
        ),
    )


@pytest.fixture(scope="function", autouse=True)
def extraction_id():
    return "2000000639651315"


@pytest.fixture(scope="function", autouse=True)
def file_path():
    return str(TEST_PATH.joinpath("data/2000000639651315__schedule.eod.stats.newyork.csv.gz"))


@pytest.fixture(scope="function", autouse=True)
def file_path_orc():
    return str(TEST_PATH.joinpath("data/2000000639651315__schedule.eod.stats.newyorkORC.csv.gz"))


@pytest.fixture(scope="function", autouse=True)
def poller_response_raw():
    return (
        '{"ReportExtractionId": "2000000639651315", "ScheduleId": "0x07e6d'
        'ba8153d956b", "Status": "Completed", "DetailedStatus": "Done", '
        '"ExtractionDateUtc": "2023-11-20T06:05:00.000Z", "ScheduleName": '
        '"schedule.eod.stats.newyork", "IsTriggered": false, "Extraction'
        'StartUtc": "2023-11-20T06:06:03.433Z", "ExtractionEndUtc": "2023-'
        '11-20T06:06:03.433Z"}'
    )


def test_instrument_list_poller(task_input, poller_response_raw, monkeypatch):
    ile = InstrumentListExtract(aries_task_input=task_input)
    date_from, date_to = ile.create_date(lookback_period=1)
    instrument_list_data = ile.poll_instrument_list_schedule(date_from=date_from, date_to=date_to)
    expected_instrument_data = [InstrumentListUploadInfo.parse_raw(poller_response_raw)]
    assert instrument_list_data == expected_instrument_data


def test_dl_decomp_split(task_input, poller_response_raw, file_path, file_path_orc, monkeypatch):
    ile = InstrumentListExtract(aries_task_input=task_input)
    instrument_data = [InstrumentListUploadInfo.parse_raw(poller_response_raw)]
    # assert ile.instrument_list_name == None
    exp_dli_output = [
        MarketDataObjectInfo.parse_raw(
            '{"datetime_from": "20231109_0000","datetime_from_obj": "2023-11-09T00:00:00+00:00",'
            '"datetime_to": "20231109_0000","datetime_to_obj": "2023-11-09T00:00:00+00:00","file'
            '_name": "2000000639651315__schedule.eod.stats.newyork'
            '.csv.gz","ilm_id": null,"local_source": "downloads/ric/raw/New_York/'
            '2000000639651315__schedule.eod.stats.newyork.csv.gz","remote_des'
            'tination": "None/lake/ingress/ric/raw/New_York/200000'
            '0639651315__schedule.eod.stats.newyork.csv.gz","ric": null,"status": "success"}'
        )
    ]
    dli_output = ile.download_instrument_list(instrument_data)

    assert dli_output == exp_dli_output
    assert len(dli_output) == 1

    exp_split_res = MarketDataObjectInfo.parse_raw(
        '{"datetime_from": null, "datetime_to": null, "datetime_from_obj": null, '
        '"datetime_to_obj": null, "local_source": "", "remote_destination": "None'
        "/lake/ingress/ric/structured/A.N/EOD_STATS_start_date_time__end_date_tim"
        'e__test__A.N.csv", "ric": "A.N", "status": "unknown", "ilm_id": null, "file_name": null}'
    ).dict()
    exp_split_res_orc = MarketDataObjectInfo.parse_raw(
        '{"datetime_from": null, "datetime_to": null, "datetime_from_obj": null, '
        '"datetime_to_obj": null, "local_source": "", "remote_destination": "None'
        "/lake/ingress/ric/structured/ORC/EOD_STATS_start_date_time__end_date_tim"
        'e__test__ORC.csv", "ric": "ORC", "status": "unknown", "ilm_id": null, "file_name": null}'
    ).dict()
    exp_split_res.pop("local_source")
    exp_split_res_orc.pop("local_source")
    exp_local_src_reg = "(.*)/A.N/EOD_STATS_start_date_time__end_date_time__test__A.N.csv"
    exp_local_src_orc_reg = "(.*)/ORC/EOD_STATS_start_date_time__end_date_time__test__ORC.csv"

    split_result_orc = ile.split_csv_by_ric(
        csv_gz_path=file_path_orc,
        start_date_time="start_date_time",
        end_date_time="end_date_time",
        copy_obj=MarketDataObjectInfo.parse_raw(
            '{"datetime_from": "20231109_0000","datetime_from_obj": "2023-11-09T00:00:00+00:00",'
            '"datetime_to": "20231109_0000","datetime_to_obj": "2023-11-09T00:00:00+00:00","file'
            '_name": "2000000639651315__schedule.eod.stats.newyorkORC'
            '.csv.gz","ilm_id": null,"local_source":'
            ' "data/2000000639651315__schedule.eod.stats.newyorkORC.csv.gz/ric/raw/New_York/'
            '2000000639651315__schedule.eod.stats.newyorkORC.csv.gz","remote_des'
            'tination": "None/lake/ingress/ric/raw/New_York/200000'
            '0639651315__schedule.eod.stats.newyorkORC.csv.gz","ric": null,"status": "unknown"}'
        ),
    )

    assert len(split_result_orc) == 2

    local_src_orc = None
    d = None

    for y in split_result_orc:
        if y.ric == "ORC":
            d = y.dict()
            local_src_orc = d.pop("local_source")

    assert d == exp_split_res_orc
    assert re.match(exp_local_src_orc_reg, local_src_orc) is not None  # type: ignore

    split_result = ile.split_csv_by_ric(
        csv_gz_path=file_path,
        start_date_time="start_date_time",
        end_date_time="end_date_time",
        copy_obj=MarketDataObjectInfo.parse_raw(
            '{"datetime_from": "20231109_0000","datetime_from_obj": "2023-11-09T00:00:00+00:00",'
            '"datetime_to": "20231109_0000","datetime_to_obj": "2023-11-09T00:00:00+00:00","file'
            '_name": "2000000639651315__schedule.eod.stats.newyork'
            '.csv.gz","ilm_id": null,"local_source":'
            ' "data/2000000639651315__schedule.eod.stats.newyork.csv.gz/ric/raw/New_York/'
            '2000000639651315__schedule.eod.stats.newyork.csv.gz","remote_des'
            'tination": "None/lake/ingress/ric/raw/New_York/200000'
            '0639651315__schedule.eod.stats.newyork.csv.gz","ric": null,"status": "unknown"}'
        ),
    )

    assert len(split_result) == 1
    d = split_result[0].dict()
    local_src = d.pop("local_source")
    assert d == exp_split_res

    assert re.match(exp_local_src_reg, local_src) is not None

    exp_exec_out: AriesTaskResult = AriesTaskResult.parse_obj(
        {
            "output_param": {
                "params": {"io_params_list": [{"io_param": {"params": {"append_data": True}}}]}
            },
            "app_metric": None,
        }
    )
    exec_out = ile.execute()

    out_params = exec_out.output_param.params  # type: ignore
    bfu = (
        out_params.get("io_params_list", {})[0]
        .get("io_param")
        .get("params", {})
        .pop("batch_file_uri")
    )
    out_params.get("io_params_list", {})[0].get("io_param").get("params", {}).pop("ilm_id")
    exec_out.output_param.params = out_params  # type: ignore

    exp_f_reg = "s3://None/lake/ingress/ric/structured/New_York/EOD_batch/(.*)_batch__(.*)_0.json"

    assert exec_out == exp_exec_out
    assert re.match(exp_f_reg, bfu) is not None

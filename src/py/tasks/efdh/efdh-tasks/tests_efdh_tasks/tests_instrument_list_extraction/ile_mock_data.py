import datetime
import efdh_tasks.instrument_list_extraction.static
import fsspec
import httpx
import nanoid
import pandas as pd
import pendulum
import refinitiv_utils.utils
from pathlib import Path
from reference_api_client.ric import Ric
from unittest.mock import MagicMock

MOCK_DATETIME_DATETEIME = datetime.datetime(2023, 11, 9, 0, 0, 0)
MOCK_DATETIME = pendulum.DateTime.create(2023, 11, 9, 0, 0, 0)
TEST_PATH = Path(__file__).parent.joinpath("data")


class FakeFileSystem:
    def exists(self, *args):
        return False

    def put_file(self, *args, **kwargs):
        return

    def write(self, *args, **kwargs):
        return

    def open(self, *args, **kwargs):
        return FakeContextManager()


class FakeContextManager:
    def __enter__(self):
        return FakeFileSystem

    def __exit__(self, exc_type, exc_val, exc_tb):
        return


def mock_nanoid_generate(monkeypatch, *args, **kwargs):
    monkeypatch.setattr(nanoid, "generate", lambda *args, **kwargs: "test")


def mock_schedule_extraction(monkeypatch, *args, **kwargs):
    def mock_extract_schedule(self, **kwargs):
        return {
            "@odata.context": "https://selectapi.datascope.refinitiv.com/RestApi/v1/$metadata#ReportExtractions",
            "value": [
                {
                    "ReportExtractionId": "2000000639651315",
                    "ScheduleId": "0x07e6dba8153d956b",
                    "Status": "Completed",
                    "DetailedStatus": "Done",
                    "ExtractionDateUtc": "2023-11-20T06:05:00.000Z",
                    "ScheduleName": "schedule.eod.stats.newyork",
                    "IsTriggered": False,
                    "ExtractionStartUtc": "2023-11-20T06:06:03.433Z",
                    "ExtractionEndUtc": "2023-11-20T06:06:03.433Z",
                }
            ],
        }

    monkeypatch.setattr(
        refinitiv_utils.utils.RefinitivClient,
        "extract_schedule",
        mock_extract_schedule,
    )


def mock_httpx_client(monkeypatch):
    def fake_cli(*args, **kwargs):
        return MagicMock()

    monkeypatch.setattr(httpx, "Client", fake_cli)


def mock_template(monkeypatch):
    monkeypatch.setattr(
        efdh_tasks.instrument_list_extraction.static.FileTemplate,
        "EOD_EXTRACT_FILEPATH",
        "./{ric}/",
    )


def mock_download_il_file(monkeypatch, *args, **kwargs):
    def mock_download(x, report_extraction_id):
        assert report_extraction_id == "2000000639651315"
        pd.read_csv(
            TEST_PATH.joinpath("2000000639651315__schedule.eod.stats.newyork.csv"),
        ).to_csv(
            TEST_PATH.joinpath("2000000639651315__schedule.eod.stats.newyork.csv.gz"),
            compression="gzip",
            index=False,
        )
        f = open(
            str(TEST_PATH.joinpath("2000000639651315__schedule.eod.stats.newyork.csv.gz")),
            "rb",
        )

        def iter_bytes(**kwargs):
            return [f.read()]

        f.iter_bytes = iter_bytes  # type: ignore
        return f

    monkeypatch.setattr(
        refinitiv_utils.utils.RefinitivClient,
        "request_notes_file_id",
        lambda *args: None,
    )
    notes = open(
        TEST_PATH.joinpath("notes.txt"),
        "r",
    ).read()
    monkeypatch.setattr(
        refinitiv_utils.utils.RefinitivClient,
        "get_extraction_notes",
        lambda *args: (notes),
    )

    monkeypatch.setattr(
        refinitiv_utils.utils.RefinitivClient,
        "download_instrument_list_file",
        mock_download,
    )


def mock_fs(monkeypatch, *args, **kwargs):
    def mock_fs_class(*args, **kwargs):
        return FakeFileSystem()

    monkeypatch.setattr(
        fsspec,
        "filesystem",
        mock_fs_class,
    )


def mock_pendulum_datetime(monkeypatch, *args, **kwargs):
    def mock_date(*args, **kwargs):
        return MOCK_DATETIME

    monkeypatch.setattr(pendulum.DateTime, "create", mock_date)


def mock_context_manager(monkeypatch, *args, **kwargs):
    def mock_conext_manager_class(*args, **kwargs):
        return FakeContextManager()

    monkeypatch.setattr(
        fsspec,
        "open",
        mock_conext_manager_class,
    )


def mock_currency_norm_flag(monkeypatch, *args, **kwargs):
    def mock_currency_norm(*args, **kwargs):
        return {"A.N": False}

    monkeypatch.setattr(Ric, "currency_norm_flags", mock_currency_norm)

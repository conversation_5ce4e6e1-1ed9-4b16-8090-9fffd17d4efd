import polars as pl
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from datetime import datetime as dt
from efdh_tasks.loans_cds_parquet_converter.loans_cds_parquet_converter_task import (
    LoansCDSParquetConverter,
)
from efdh_tasks.loans_cds_parquet_converter.static import DataLakeFileTemplate
from io import BytesIO
from moto import mock_aws
from unittest.mock import MagicMock


@pytest.fixture
def sample_parquet():
    """Create a sample Parquet file in-memory."""
    df = pl.DataFrame(
        {
            "#RIC": ["RIC1", "RIC2", "RIC1"],
            "DATE": [dt(2023, 1, 1), dt(2023, 2, 1), dt(2024, 3, 1)],
            "VALUE": [100, 200, 300],
        }
    )
    buffer = BytesIO()
    df.write_parquet(buffer)
    buffer.seek(0)
    return buffer.getvalue()


@pytest.fixture
def aries_task_input():
    """Mock Aries task input data."""
    workflow = WorkflowFieldSet(
        name="localtest",
        stack="dev-master-data",
        tenant="dev-master-data",
        start_timestamp=str(datetime.now()),
    )
    input_param = IOParamFieldSet(
        params={
            "batch_file_uri": "s3://dev-masterdata.steeleye.co/loans_cds_converter/batch_0_20250327125240.parquet",
            "ilm_id": "df447548-dbb0-450d-849e-36a2ec799dfd",
        }
    )
    return AriesTaskInput(
        workflow=workflow,
        input_param=input_param,
        task=TaskFieldSet(
            id="foo",
            name="market-data-ingestion",
            version="1",
            success=True,
            previous_id=None,
        ),
    )


@pytest.fixture
def batch_processor(aries_task_input):
    """Create an instance of LoansCDSParquetConverter with mocks."""
    processor = LoansCDSParquetConverter(aries_task_input)
    processor.s3_client = MagicMock()
    processor.ilm.set_failed = MagicMock()
    processor.ilm.increment_batches = MagicMock()
    return processor


@mock_aws
def test_write_yearly_files(batch_processor):
    """Test yearly file splitting and writing."""

    frame = pl.DataFrame(
        {
            "#RIC": ["RIC1", "RIC2", "RIC3"],
            "Date": [dt(2023, 1, 1), dt(2024, 1, 1), dt(2024, 1, 3)],
            "Close Price": [100.5, 102.3, 98.7],
            "Close Ask Price": [100.7, 102.5, 98.9],
            "Currency": ["USD", "EUR", "GBP"],
            "Exchange Code": [1.0, 2.0, 3.0],
            "High Price": [105.0, 107.0, 104.0],
        }
    )

    batch_processor._write_yearly_files(
        ric="RIC1", frame=frame, remote_template=DataLakeFileTemplate.CURATED_REMOTE
    )

    # Verify file uploads
    batch_processor.s3_client.put_object.assert_called()
    assert batch_processor.s3_client.put_object.call_count == 2


@mock_aws
def test_process_group(batch_processor):
    """Test group processing with mock data."""
    frame = pl.DataFrame(
        {
            "#RIC": ["RIC1", "RIC2", "RIC3"],
            "Date": [dt(2023, 1, 1), dt(2024, 1, 1), dt(2024, 1, 3)],
            "Close Price": [100.5, 102.3, 98.7],
            "Close Ask Price": [100.7, 102.5, 98.9],
            "Currency": ["USD", "EUR", "GBP"],
            "Exchange Code": [1.0, 2.0, 3.0],
            "High Price": [105.0, 107.0, 104.0],
        }
    )

    result = batch_processor._process_group(
        ric="RIC1", group=frame, remote_template=DataLakeFileTemplate.CURATED_REMOTE
    )

    assert isinstance(result, dict)
    assert "ric" in result
    assert "s3_path" in result


def test_exception_handling(batch_processor):
    """Test exception handling in the process_group method."""
    batch_processor._merge_existing = MagicMock(side_effect=Exception("Mocked error"))

    frame = pl.DataFrame(
        {
            "DATE": [dt(2023, 1, 1)],
            "VALUE": [100],
        }
    )

    result = batch_processor._process_group(
        ric="RIC1", group=frame, remote_template="s3://test-bucket/"
    )

    assert "exception" in result
    assert "ric" in result
    assert result["ric"] == "RIC1"


@mock_aws
def test_write_yearly_files_with_wrong_schema(batch_processor):
    """Test yearly file splitting and writing."""

    frame = pl.DataFrame(
        {
            "#RIC": ["RIC1", "RIC2", "RIC3"],
            "Date": [dt(2023, 1, 1), dt(2024, 1, 1), dt(2024, 1, 3)],
            "Close Price": [100.5, 102.3, 98.7],
            "Close Ask Price": [100.7, 102.5, 98.9],
            "Currency": ["USD", "EUR", "GBP"],
            "Exchange Code": [1.0, 2.0, 3.0],
            "Higher Price": [105.0, 107.0, 104.0],
        }
    )
    with pytest.raises(ValueError, match="field names are not matching"):
        batch_processor._write_yearly_files(
            ric="RIC1", frame=frame, remote_template=DataLakeFileTemplate.CURATED_REMOTE
        )

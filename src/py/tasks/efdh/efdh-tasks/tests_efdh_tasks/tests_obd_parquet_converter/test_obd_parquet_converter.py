import os

os.environ["MASTER_DATA_SE_REF_DB_URL"] = "postgresql://sa:password@localhost:5432/se_reference_db"

import pandas as pd
import pendulum
import pytest
from efdh_tasks.obd_parquet_converter.normalise_obd import NormaliseOBD
from market_data_utils.schema.parquet import QuoteCurrencyColumn
from mock_alchemy.mocking import AlchemyMagicMock
from pathlib import Path


@pytest.fixture
def mock_session():
    return AlchemyMagicMock()


TEST_PATH = Path(__file__).parent.joinpath()


class MockRicApi:
    @classmethod
    def get_currency(cls, *args, **kwargs):
        raise Exception("Some error")


# test happy path
def test_normalise_obd(monkeypatch):
    # mock DB operations
    normalise_obd = NormaliseOBD(
        ric="LGD.GO",
        currency="CAD",
        date=pendulum.now(),
        input_filepath=str(TEST_PATH.joinpath("data/LGD.GO.csv")),
        local_destination=str(TEST_PATH.joinpath("data/")) + "/",
        remote_destination="test-masterdata.steeleye.co/",
        bucket="test-masterdata.steeleye.co",
    )

    result = normalise_obd.convert_to_parquet()
    parquet_paths = [r["local_source"] for r in result]  # type: ignore
    for parquet_path in parquet_paths:
        assert Path.exists(Path(parquet_path))


# test currency is not present
def test_obd_null_curr(monkeypatch):
    normalise_obd = NormaliseOBD(
        ric="LGD.GO",
        date=pendulum.now(),
        currency=None,
        input_filepath=str(TEST_PATH.joinpath("data/LGD.GO.csv")),
        local_destination=str(TEST_PATH.joinpath("data/local_files")) + "/",
        remote_destination="test-masterdata.steeleye.co/",
        bucket="test-masterdata.steeleye.co",
    )
    normalise_obd.ric_api = MockRicApi  # type: ignore

    result = normalise_obd.convert_to_parquet()
    parquet_paths = [r["local_source"] for r in result]  # type: ignore
    parquet_file_path = parquet_paths[0]
    parquet_file = pd.read_parquet(parquet_file_path)
    assert parquet_file.loc[0, QuoteCurrencyColumn.QUOTE_PRICE_CURRENCY] is None


# test OBD file is empty
# test self.currency in CurrencyMappings.MINOR_TO_MAJOR_CCY

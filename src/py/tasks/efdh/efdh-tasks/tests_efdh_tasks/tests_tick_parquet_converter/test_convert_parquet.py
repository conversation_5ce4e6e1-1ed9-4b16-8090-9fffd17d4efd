# type: ignore
# ruff: noqa: E501
import json
import os
from datetime import datetime

os.environ["MAX_WORKERS"] = "1"

import boto3
import fsspec
import httpx
import pandas as pd
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from pathlib import Path
from unittest.mock import MagicMock

OUTPUT_DIR = Path(__file__).parent.joinpath("data/outputs")
PARENT = Path(__file__).parent
TEST_DATA = PARENT.joinpath("data")


def mock_httpx_client(*args, **kwargs):
    return MagicMock()


class MockBody:
    def __init__(self, content):
        self.content = content

    def read(self):
        return self.content.encode("utf-8")


class MockS3Client:
    @staticmethod
    def upload_file(
        Filename,
        Bucket,
        Key,
        ExtraArgs,
    ):
        fp = OUTPUT_DIR / Key
        fs = fsspec.filesystem("file")
        target_dir = str(fp.parent)
        if not fs.exists(target_dir):
            fs.mkdirs(target_dir)

        fs.copy(str(Filename), str(fp))

    @staticmethod
    def get_object(Bucket, Key):
        fs = fsspec.filesystem("file")

        with fs.open(f"/{Key}", "r") as file:
            json_data = file.read()
        return {"Body": MockBody(json_data)}


def mock_s3_client(*args, **kwargs):
    return MockS3Client()


def test_convert_to_parquet(monkeypatch):
    from efdh_tasks.tick_parquet_converter.tick_parquet_converter_task import ParquetConverterTask

    monkeypatch.setattr(httpx, "Client", MagicMock)
    monkeypatch.setattr(boto3, "client", mock_s3_client)

    batch_file = str(
        TEST_DATA.joinpath("batch/1699286135_batch__Zu81fmKCDiKAHoifI2G2P_0.json").as_posix()
    )

    params = {"batch_file_uri": f"s3://fakeBucket{batch_file}", "ilm_id": 13}
    with fsspec.open(batch_file) as file:
        json_data = file.read()

    batch = json.loads(json_data)
    batch[0]["file_uri"] = str(
        TEST_DATA.joinpath("AAGR.OQ/0500__2359__2024-07-02__AAGR.OQ.csv").as_posix()
    )

    with fsspec.open(batch_file, "w") as file:
        file.write(json.dumps(batch))

    os.makedirs(PARENT.joinpath("downloads/temp/csv/AAGR.OQ").as_posix(), exist_ok=True)

    workflow = WorkflowFieldSet(
        name="localtest",
        stack="dev-master-data",
        tenant="master-data",
        start_timestamp=str(datetime.now()),
    )
    input_param = IOParamFieldSet(params=params)
    nasdaq_input_event = AriesTaskInput(
        workflow=workflow,
        input_param=input_param,
        task=TaskFieldSet(
            id="foo",
            name="tick-parquet-converter",
            version="1",
            success=True,
            previous_id=None,
        ),
    )

    os.makedirs(OUTPUT_DIR, exist_ok=True)

    parquet_converter = ParquetConverterTask(aries_task_input=nasdaq_input_event)
    res = parquet_converter.execute()
    for fp in [
        "AAGR.OQ__AUCTION__TICK__20240702.parquet",
        "AAGR.OQ__QUOTE__HOUR__20240702.parquet",
        "AAGR.OQ__QUOTE__MINUTE__20240702.parquet",
        "AAGR.OQ__QUOTE__TICK__20240702.parquet",
        "AAGR.OQ__TRADE__HOUR__20240702.parquet",
        "AAGR.OQ__TRADE__MINUTE__20240702.parquet",
        "AAGR.OQ__TRADE__TICK__20240702.parquet",
    ]:
        df = pd.read_parquet(OUTPUT_DIR / "AAGR.OQ/2024/07/02" / fp)

        exp_name = "exp_" + fp.split("/")[-1]
        exp_df = pd.read_parquet(TEST_DATA / "lake/ingress/ric/curated/AAGR.OQ" / exp_name)
        pd.testing.assert_frame_equal(
            df.sort_values(["Date-Time", "Exch Time"]).reset_index(drop=True),
            exp_df.sort_values(["Date-Time", "Exch Time"]).reset_index(drop=True),
        )

    assert res.output_param.params["rics_processed"] == ["AAGR.OQ"]

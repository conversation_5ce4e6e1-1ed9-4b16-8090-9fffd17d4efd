python_sources(
    name="task",
    sources=["efdh_tasks/**/*.py"],
    dependencies=[
        "//:3rdparty#elasticsearch-dsl",
        "//:3rdparty#pyarrow",
        "//:3rdparty#elastic-transport",
        "//:3rdparty#s3fs",
        "//:3rdparty#adlfs",
        "//:3rdparty#psycopg2",
    ],
)

python_tests(
    name="tests",
    sources=[
        "tests_efdh_tasks/**/test_*.py",
    ],
    dependencies=[":test_files", ":test_utils"],
)

python_test_utils(
    name="test_utils",
    sources=[
        "tests_efdh_tasks/**/conftest.py",
        "tests_efdh_tasks/tests_instrument_list_extraction/ile_mock_data.py",
        "tests_efdh_tasks/tests_map_ric/map_ric_mock_data.py",
    ],
    tags=["build_amd"],
)


resources(
    name="test_files",
    sources=[
        "tests_efdh_tasks/**/**/*.csv.gz",
        "tests_efdh_tasks/**/**/*.csv",
        "tests_efdh_tasks/**/**/*.parquet",
        "tests_efdh_tasks/**/**/*.json",
        "tests_efdh_tasks/**/**/*.txt",
        "tests_efdh_tasks/**/**/*.zip",
        "tests_efdh_tasks/**/**/*.ndjson",
    ],
    tags=["build_amd"],
)


se_image(
    image_name="efdh-tasks",
    pex_entry_point="efdh_tasks/main.py:efdh_task_run",
    tags=["build_amd"],
)

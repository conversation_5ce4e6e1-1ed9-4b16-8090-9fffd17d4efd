import backoff
import fsspec
import gzip
import httpx
import json
import logging
import math
import nanoid
import numpy as np
import os
import pendulum
import re
import typing
from aries_io_event.io_param import IOParamFieldSet
from aries_se_core_tasks.aries.utility_tasks.finish_flow import create_io_params_list
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from collections import defaultdict
from datetime import datetime
from efdh_api_client.api.instrument_list_monitoring import ILM
from efdh_api_client.api.rics import Ric
from efdh_api_client.utils import create_efdh_api_client_considering_cognito
from efdh_tasks.instrument_list_extraction.input_schema import InstrumentListExtractionInput
from efdh_tasks.instrument_list_extraction.static import (
    BatchFileInfo,
    CopyLocalObjectStatus,
    FileTemplate,
    InstrumentListUploadInfo,
    MarketDataObjectInfo,
    RicFileInfo,
)
from efdh_tasks.instrument_list_extraction.utils import upload_local_objs
from efdh_utils.schema.refinitiv import InstrumentListType
from pandas.tseries.offsets import BDay
from pathlib import Path
from pydantic import BaseSettings
from refinitiv_utils.utils import RefinitivClient
from se_io_utils.tempfile_utils import tmp_directory
from typing import Any, Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


class InstrumentListExtractConfig(BaseSettings):
    LEVEL2_OBD_DATA_BUCKET: str
    EOD_BATCH_SIZE: int = 50
    OBD_BATCH_SIZE: int = 10
    TICK_BATCH_SIZE: int = 10
    EFDH_API_URL: str = "https://efdh.dev.steeleye.co"


instrument_list_extract_config = InstrumentListExtractConfig()


class InstrumentListExtract:
    def __init__(
        self,
        aries_task_input: AriesTaskInput,
    ):
        self.aries_task_input = aries_task_input
        self.input_param = InstrumentListExtractionInput.validate(
            aries_task_input.input_param.params
        )
        self.instrument_list_name = self.input_param.instrument_list_name
        self.instrument_list_type: InstrumentListType = self.input_param.instrument_list_type
        self.efdh_api_client = create_efdh_api_client_considering_cognito(
            host=instrument_list_extract_config.EFDH_API_URL
        )
        self.ilm = ILM(client=self.efdh_api_client)
        self.file_uri = self.input_param.input_file_uri
        self.schedule_id = self.get_schedule_id()
        self.level_1 = self.instrument_list_type in [
            InstrumentListType.TICK,
            InstrumentListType.EOD,
        ]
        self.eod = self.instrument_list_type == InstrumentListType.EOD
        self.batch_size = int(
            {
                InstrumentListType.EOD: instrument_list_extract_config.EOD_BATCH_SIZE,
                InstrumentListType.TICK: instrument_list_extract_config.TICK_BATCH_SIZE,
                InstrumentListType.OBD: instrument_list_extract_config.OBD_BATCH_SIZE,
            }[self.instrument_list_type]
        )

        self.refinitiv_client = RefinitivClient(level_1=self.level_1, eod=self.eod)
        self.fs = fsspec.filesystem("s3")
        self.ric_api = Ric(client=self.efdh_api_client)
        self.currency_norm_overrides = self.ric_api.currency_norm_flags(
            extraction_type=self.instrument_list_type.value
        )
        self.currencies = self.get_currencies()
        self.ilm_ids: Dict[str, Any] = {}

    def execute(self) -> AriesTaskResult:
        logger.info(
            f"Instrument List Extraction Engaged for {self.instrument_list_type} - "
            f"{self.instrument_list_name}."
        )

        if self.file_uri:
            # Backload: file has been provided
            raw_ric_data = self.fetch_backload_file()
        else:
            # Regular schedule:
            raw_ric_data = self.poller()

        if not raw_ric_data:
            return AriesTaskResult(output_param=None)

        structured_results = {}
        for extract in raw_ric_data:
            if extract.status != CopyLocalObjectStatus.SUCCESS:
                self.ilm.set_failed(extract.ilm_id)
                logger.error(f"Cannot process failed extract. Extract details: {extract}")
                continue

            if self.instrument_list_type == InstrumentListType.EOD:
                structured_ric_upload_data = self.split_csv_by_ric(
                    csv_gz_path=extract.local_source,
                    start_date_time=extract.datetime_from_obj,
                    end_date_time=extract.datetime_to_obj,
                    copy_obj=extract,
                )
            elif self.instrument_list_type in [
                InstrumentListType.OBD,
                InstrumentListType.TICK,
            ]:
                structured_ric_upload_data = self.split_csv_by_ric_and_date(
                    csv_gz_path=extract.local_source,
                    ilm_id=extract.ilm_id,
                )
                if not structured_ric_upload_data:
                    self.ilm.update_extraction_status(id=extract.ilm_id, status="EMPTY")
                    self.ilm.set_batches(id=extract.ilm_id, totalBatches=0)
                    logger.warning("Empty extraction file")
            else:
                self.ilm.set_failed(extract.ilm_id)
                raise AttributeError(
                    "Event must contain eod stats and level 1 data to decide"
                    " on splitting by ric or by ric and date"
                )
            structured_results[extract.ilm_id] = upload_local_objs(
                fs=self.fs, copy_local_object_infos=structured_ric_upload_data, logger=logger
            )

            # delete structured_ric_upload_data.local_source
            for each_upload_data in structured_ric_upload_data:
                if os.path.exists(each_upload_data.local_source):
                    os.remove(each_upload_data.local_source)

        batches: List[BatchFileInfo] = []
        for ilm_id, clo_results in structured_results.items():
            result = self.create_parquet_converter_batches(
                copy_local_object_results=clo_results, ilm_id=ilm_id
            )
            self.ilm.set_batches(id=ilm_id, totalBatches=len(result))
            batches.extend(result)

        batch_file_uris, batch_ilm_ids = self.write_batch_files(batches=batches)

        logger.info("Uploading RAW data to cloud")
        # upload raw data after structured so retries won't skip the extract post failures
        upload_local_objs(fs=self.fs, copy_local_object_infos=raw_ric_data, logger=logger)
        logger.info("RAW data uploaded to cloud")

        for raw_data in raw_ric_data:
            if os.path.exists(raw_data.local_source):
                os.remove(raw_data.local_source)

        logger.info(
            f"Instrument List Extraction Disengaged. Creating {len(batch_file_uris)}"
            f" batches, each with {self.batch_size} files to normalise."
        )

        output = create_io_params_list(
            iterable_item={
                "batch_file_uri": batch_file_uris,
                "ilm_id": batch_ilm_ids,
            },
            append_data=self.input_param.append_data,
        )
        return AriesTaskResult(output_param=IOParamFieldSet(params=output))

    def fetch_backload_file(self) -> List[MarketDataObjectInfo]:
        file_name = str(Path(self.file_uri).name)  # type: ignore
        datetime_from_obj = self.input_param.start_datetime
        datetime_to_obj = self.input_param.end_datetime
        local_source = tmp_directory().joinpath(file_name)
        self.fs.download(self.file_uri, local_source)
        raw_ric_data = [
            MarketDataObjectInfo(
                local_source=str(local_source),
                remote_destination=str(self.file_uri),
                datetime_to_obj=datetime_to_obj,
                datetime_from_obj=datetime_from_obj,
                file_name=file_name,
                status=CopyLocalObjectStatus.SUCCESS,
            )
        ]
        return raw_ric_data

    def poller(self) -> List[MarketDataObjectInfo]:
        date_from, date_to = self.create_date(lookback_period=self.input_param.lookback_period)
        instrument_list_data = self.poll_instrument_list_schedule(
            date_from=date_from, date_to=date_to
        )
        if not instrument_list_data:
            logger.info("No instrument list extracts to process.")
            return []

        raw_ric_data = self.download_instrument_list(instrument_list_data=instrument_list_data)
        if not raw_ric_data:
            logger.info("No instrument list data that needs to be downloaded.")
            return []
        raw_ric_data = self.create_ilm_extractions(raw_ric_data)

        return raw_ric_data

    @staticmethod
    def create_date(lookback_period: int) -> Tuple[datetime, datetime]:
        date_to = pendulum.now("UTC").end_of(unit="day")
        date_from = pendulum.instance(date_to - BDay(lookback_period)).start_of(unit="day")

        return date_from, date_to

    def poll_instrument_list_schedule(
        self, date_from: datetime, date_to: datetime
    ) -> Optional[List[InstrumentListUploadInfo]]:
        try:
            logger.info(
                f"Polling Refinitiv for scheduleId {self.schedule_id} from {date_from} to {date_to}"
            )
            response = self.refinitiv_client.extract_schedule(
                schedule_id=str(self.schedule_id),
                start_date=date_from,
                end_date=date_to,
            )
            instrument_list_data = [
                InstrumentListUploadInfo.parse_obj(x) for x in response["value"]
            ]

            if not instrument_list_data:
                logger.info("Instrument List Extraction ID unavailable, retrying in 10 minutes.")
                return None

            return instrument_list_data

        except httpx.RequestError as exc:
            logger.exception(f"An error occurred while requesting {exc.request.url!r}. - {exc}")
        except httpx.HTTPStatusError as exc:
            logger.exception(
                f"Error response {exc.response.status_code} while "
                f"requesting {exc.request.url!r}. - {exc.response.content!r}"
            )
        except Exception as e:
            logger.exception(e)

        return None

    def download_instrument_list(
        self,
        instrument_list_data: List[InstrumentListUploadInfo],
    ) -> Optional[List[MarketDataObjectInfo]]:
        if not self.instrument_list_name:
            logger.warning(f"No MIC code provided for {instrument_list_data}")
            return None

        copy_object_infos = []
        try:
            for instrument_list in instrument_list_data:
                logger.info(
                    f"Downloading instrument file for schedule "
                    f"{instrument_list.ScheduleName} "
                    f"for MIC {self.instrument_list_name}"
                )
                # get notes file
                notes_content = self._get_notes_file_content(instrument_list.ReportExtractionId)
                if not notes_content:
                    logger.warning(
                        f"Failed to get notes file content for {instrument_list.ReportExtractionId}"
                    )
                    continue

                obd_notes_match = re.search(FileTemplate.NOTES_CAPTURE_REGEX_PATTERN, notes_content)
                stats_notes_match = re.search(
                    FileTemplate.STATS_NOTES_CAPTURE_REGEX_PATTERN,
                    notes_content,
                )

                # get query date ranges from notes file
                file_datetime_format = (
                    FileTemplate.STATS_NOTES_FILE_DATE_FORMAT
                    if stats_notes_match
                    else FileTemplate.NOTES_FILE_DATETIME_FORMAT
                )
                notes_match = stats_notes_match or obd_notes_match

                if self.instrument_list_type == InstrumentListType.EOD or not obd_notes_match:
                    yesterday = pendulum.now().subtract(days=1)
                    datetime_from_obj = yesterday.start_of("day")
                    datetime_to_obj = yesterday.end_of("day")

                else:
                    datetime_from_obj = pendulum.from_format(
                        notes_match.group("datetime_from"),  # type: ignore
                        file_datetime_format,
                    )
                    datetime_to_obj = pendulum.from_format(
                        notes_match.group("datetime_to"),  # type: ignore
                        file_datetime_format,
                    )

                file_name = FileTemplate.NAME.format(
                    file_id=instrument_list.ReportExtractionId,
                    schedule_name=instrument_list.ScheduleName,
                )
                logger.info(f"Resolved s3 file name {file_name}")

                download_filepath = Path(
                    FileTemplate.DOWNLOAD_FILEPATH.format(
                        filename=file_name, mic=self.instrument_list_name
                    )
                ).expanduser()
                download_filepath.parent.mkdir(exist_ok=True, parents=True)
                raw_path = FileTemplate.RAW_DESTINATION_PATH.format(
                    instrument_list_path=self.instrument_list_name,
                    file_name=file_name,
                )
                destination_filepath = (
                    f"s3://{instrument_list_extract_config.LEVEL2_OBD_DATA_BUCKET}/{raw_path}"
                )
                to_be_downloaded = (
                    not self.fs.exists(destination_filepath) or not self.input_param.skip_existing
                )

                # download the file
                if to_be_downloaded and destination_filepath:
                    self._download_file(
                        instrument_list.ReportExtractionId,
                        download_filepath,
                        self.refinitiv_client,
                    )
                    copy_object_infos.append(
                        MarketDataObjectInfo(
                            local_source=str(download_filepath),
                            remote_destination=str(destination_filepath),
                            datetime_to_obj=datetime_to_obj,
                            datetime_from_obj=datetime_from_obj,
                            file_name=file_name,
                            status=CopyLocalObjectStatus.SUCCESS,
                        )
                    )
                else:
                    logger.info(f"Upload not required for {instrument_list}")

            return copy_object_infos

        except httpx.RequestError as exc:
            logger.exception(f"An error occurred while requesting {exc.request.url!r}. - {exc}")
        except httpx.HTTPStatusError as exc:
            logger.exception(
                f"Error response {exc.response.status_code} "
                f"while requesting {exc.request.url!r}. - {exc.response.content!r}"
            )
        except Exception as e:
            logger.exception(e)

        return None

    @staticmethod
    @backoff.on_exception(backoff.expo, httpx.RequestError, max_tries=3)
    def _download_file(report_extraction_id: str, download_filepath: Path, refinitiv_client):
        with refinitiv_client.download_instrument_list_file(
            report_extraction_id
        ) as response_stream:
            with open(download_filepath, "wb") as f:
                for content in response_stream.iter_bytes(
                    chunk_size=10 * 1024 * 1024
                ):  # 10MB chunks
                    f.write(content)
                    pass

    def _get_notes_file_content(self, report_extraction_id: str) -> str | None:
        try:
            notes_file_id = self.refinitiv_client.request_notes_file_id(report_extraction_id)
        except Exception as e:
            logger.warning(f"Failed to get notes file id: {str(e)}")
            return None

        notes_content = self.refinitiv_client.get_extraction_notes(notes_file_id)
        return notes_content

    @typing.no_type_check
    def split_csv_by_ric(
        self,
        csv_gz_path: str,
        start_date_time: datetime,
        end_date_time: datetime,
        copy_obj: MarketDataObjectInfo,
    ) -> List[MarketDataObjectInfo]:
        csv_headers = None
        known_rics_map = defaultdict(dict)
        copy_objects = []
        try:
            ric_index = None
            orc_index = None
            orc = None
            # for each line in csv
            for line in gzip.open(csv_gz_path, "rt", newline=""):
                if not csv_headers:
                    # figure out headers once
                    csv_headers = line
                    headers_set = csv_headers.strip().split(",")
                    ric_index = headers_set.index("RIC")
                    date_index = headers_set.index("Trade Date")
                    if "Instrument ID" in headers_set:
                        orc_index = headers_set.index("Instrument ID")
                    continue

                fields = line.strip().split(",")
                ric = fields[ric_index].split("^")[0]
                date = fields[date_index].split("^")[0]
                if orc_index:
                    orc = fields[orc_index].split("^")[0]  # orc is Instrument ID
                ric = ric if ric else orc

                if not (ric and date):
                    logger.warning(
                        f"MISSING RIC/DATE in CSV: {copy_obj.remote_destination}. LINE: {line}"
                    )
                    continue

                if ric not in known_rics_map:
                    # open a file if new data was seen
                    file_dir = FileTemplate.EOD_EXTRACT_FILEPATH.format(
                        ric=ric,
                    )
                    Path(file_dir).absolute().mkdir(parents=True, exist_ok=True)

                    file_name = FileTemplate.EOD_CSV_FILE_NAME_FORMAT.format(
                        start_date=str(start_date_time),
                        end_date=str(end_date_time),
                        unique_identifier=nanoid.generate(),
                        ric=ric,
                    )

                    extract_file_path = Path(file_dir).joinpath(file_name).absolute()

                    if "/" in ric:
                        Path(extract_file_path).parent.absolute().mkdir(parents=True, exist_ok=True)
                    destination_filepath = self.get_destination_path(
                        ric=ric, file_name=file_name, eod_file=True
                    )

                    copy_objects.append(
                        MarketDataObjectInfo(
                            local_source=str(extract_file_path),
                            remote_destination=str(destination_filepath),
                            ric=ric,
                            ilm_id=copy_obj.ilm_id,
                        )
                    )
                    f = open(extract_file_path, "w")
                    f.write(csv_headers)
                    known_rics_map[ric] = f

                # write the line to file
                known_rics_map[ric].write(line)

        except Exception as e:
            logger.exception(e)
        finally:
            logger.info(f"RIC EOD csv files written. Count: {len(known_rics_map)}")
            for ric, f in known_rics_map.items():
                f.close()

            return copy_objects

    @staticmethod
    def get_destination_path(
        ric,
        file_name,
        year: Optional[str] = None,
        month: Optional[str] = None,
        day: Optional[str] = None,
        eod_file: Optional[bool] = False,
    ):
        if eod_file:
            destination_filepath = (
                Path(FileTemplate.STRUCTURED_EOD_DESTINATION_PATH.format(ric=ric)) / file_name
            )
        else:
            destination_filepath = (
                Path(
                    FileTemplate.STRUCTURED_TICK_OBD_DESTINATION_PATH.format(
                        ric=ric,
                        year=year,
                        month=month,
                        day=day,
                    )
                )
                / file_name
            )

        return (
            f"s3://{instrument_list_extract_config.LEVEL2_OBD_DATA_BUCKET}/{destination_filepath}"
        )

    @typing.no_type_check
    def split_csv_by_ric_and_date(
        self,
        csv_gz_path: str,
        ilm_id: str,
    ) -> List[MarketDataObjectInfo]:
        # figure out filename based on range of data in the file
        # todo: test pendulum lib in this section
        csv_headers = None
        known_rics_dts_map: Dict[str, Dict[str, str]] = defaultdict(dict)
        copy_objects = []
        try:
            if not os.path.exists(csv_gz_path):
                return []
            max_splits = ric_index = dt_index = None
            # for each line in csv
            for i, line in enumerate(gzip.open(csv_gz_path, "rt", newline="")):
                if not i % 10_000:
                    logger.info(f"{i} rows processed")
                if not csv_headers:
                    # figure out headers once
                    csv_headers = line
                    headers_set = csv_headers.strip().split(",")
                    ric_index = headers_set.index("#RIC")
                    dt_index = headers_set.index("Date-Time")
                    max_splits = max(dt_index, ric_index) + 1
                    continue
                line_splitted = line.strip().split(",", max_splits)
                ric = line_splitted[ric_index]
                row_dt_tm = pendulum.parse(line_splitted[dt_index])
                if ric not in known_rics_dts_map or row_dt_tm.date() not in known_rics_dts_map[ric]:
                    # open a file if new data was seen
                    file_dir = FileTemplate.LOCAL_TICK_OBD_EXTRACT_FILEPATH.format(
                        ric=ric,
                        year=row_dt_tm.year,
                        month=row_dt_tm.month,
                        day=row_dt_tm.day,
                    )
                    Path(file_dir).absolute().mkdir(parents=True, exist_ok=True)

                    file_name = FileTemplate.TICK_OBD_CSV_FILE_NAME_FORMAT.format(
                        start_time=nanoid.generate(size=6),
                        end_time=nanoid.generate(size=6),
                        ric=ric,
                        date=row_dt_tm.date(),
                    )
                    extract_file_path = Path(file_dir).joinpath(file_name).absolute()
                    destination_filepath = self.get_destination_path(
                        ric=ric,
                        file_name=file_name,
                        year=row_dt_tm.year,
                        month=row_dt_tm.month,
                        day=row_dt_tm.day,
                    )
                    copy_objects.append(
                        MarketDataObjectInfo(
                            local_source=str(extract_file_path),
                            remote_destination=str(destination_filepath),
                            ilm_id=ilm_id,
                            ric=ric,
                            currency_norm_override=self.currency_norm_overrides.get(ric),
                            date=row_dt_tm.date(),
                        )
                    )
                    f = open(extract_file_path, "w")
                    f.write(csv_headers)
                    known_rics_dts_map[ric][row_dt_tm.date()] = f

                # write the line to file
                known_rics_dts_map[ric][row_dt_tm.date()].write(line)

        except Exception as e:
            logger.exception(e)
        finally:
            # close all files
            ct = 0
            for ric, dts in known_rics_dts_map.items():
                for dt, f in dts.items():
                    f.close()
                    ct += 1
            logger.info(f"RIC csv files written. Count: {ct}")
            return copy_objects

    def create_parquet_converter_batches(
        self, copy_local_object_results: List[MarketDataObjectInfo], ilm_id: Optional[str]
    ) -> List[BatchFileInfo]:
        results: List = []

        if not copy_local_object_results:
            return results

        split = int(math.ceil(len(copy_local_object_results) / self.batch_size))
        for chunk_copy_local_object_result in np.array_split(
            np.array(copy_local_object_results), split
        ):
            batch = []
            for local_object_result in chunk_copy_local_object_result:
                if local_object_result.status == CopyLocalObjectStatus.SUCCESS:
                    batch.append(
                        RicFileInfo(
                            file_uri=local_object_result.remote_destination,
                            ric=local_object_result.ric,
                            ilm_id=ilm_id,
                            date=(
                                local_object_result.date
                                if self.instrument_list_type != InstrumentListType.EOD
                                else None
                            ),
                            ccy_norm_override=self.currency_norm_overrides.get(
                                local_object_result.ric
                            ),
                            currency=self.currencies.get(local_object_result.ric),
                        )
                    )
                else:
                    logger.warning(
                        f"Object copy failed for key: {local_object_result.remote_destination}"
                    )
                    self.ilm.set_failed(ilm_id)

            results.append(BatchFileInfo(files=batch, ilm_id=ilm_id))

        return results

    def write_batch_files(
        self, batches: List[BatchFileInfo]
    ) -> Tuple[List[str], List[Optional[str]]]:
        """Validating Task Input parameters.

        :param batches: list of dicts of batch uri's
        :return: File url
        """
        uris = []
        ilm_ids = []
        batch_file_path = FileTemplate.BATCH_FILEPATH_TEMPLATE.format(
            instrument_list_path=self.instrument_list_name or "Unknown",
            instrument_list_type=self.instrument_list_type.value,
        )
        s3_filepath = (
            f"s3://{instrument_list_extract_config.LEVEL2_OBD_DATA_BUCKET}/{batch_file_path}"
        )
        count = 0
        for batch in batches:
            batch_uuid = nanoid.generate()
            batch_filename = FileTemplate.BATCH_FILENAME_TEMPLATE.format(
                epoch=int(pendulum.now().int_timestamp),
                batch_number=count,
                uuid=batch_uuid,
            )
            count += 1
            s3_url = f"{s3_filepath}/{batch_filename}"
            uri_rics = [self.make_batch(f) for f in batch.files]
            with self.fs.open(s3_url, "w") as file:
                file.write(json.dumps(uri_rics))
            logger.info(f"wrote {s3_url}")
            uris.append(s3_url)
            ilm_ids.append(batch.ilm_id)

        return uris, ilm_ids

    def make_batch(self, f: RicFileInfo):
        d: Dict[str, Any] = {
            "ric": f.ric,
            "file_uri": f.file_uri,
            "currency_norm_override": f.ccy_norm_override,
        }
        if self.instrument_list_type != InstrumentListType.EOD:
            d.update(
                {
                    "date": f.date.strftime("%Y-%m-%d") if f.date is not None else None,
                    "currency": f.currency,
                }
            )
        return d

    def create_ilm_extractions(
        self, raw_ric_data: List[MarketDataObjectInfo]
    ) -> List[MarketDataObjectInfo]:
        output = []
        while raw_ric_data:
            mdoi = raw_ric_data.pop()
            mdoi.ilm_id = self.ilm.create_extraction(
                scheduleId=self.schedule_id,
                traceId=self.aries_task_input.workflow.trace_id,
                fileName=mdoi.file_name,
                dataMinDatetime=str(mdoi.datetime_from_obj),
                dataMaxDatetime=str(mdoi.datetime_to_obj),
            )
            if mdoi.ilm_id is None:
                mdoi.ilm_id = nanoid.generate()
            output.append(mdoi)
        return output

    def get_schedule_id(self) -> str:
        if self.input_param.schedule_id is not None:
            return self.input_param.schedule_id
        if self.file_uri:
            return str(nanoid.generate(size=8))

        try:
            row = self.ilm.get_schedule_id(
                list_id=self.instrument_list_name,  # type: ignore
                list_type=self.instrument_list_type.value,
            )
            if not row.get("scheduleId"):
                raise Exception
        except Exception:
            raise Exception(
                f"Schedule ID not found for instrument "
                f"list {self.instrument_list_name} and type "
                f" {self.instrument_list_type.value}"
            )

        return str(row["scheduleId"])

    def get_currencies(self) -> Dict[str, Optional[str]]:
        try:
            if not self.instrument_list_name or self.instrument_list_type == InstrumentListType.EOD:
                logger.info("Skipping currency fetch for EOD run")
                return {}
            res = self.ric_api.get_currencies_by_list(
                list_id=self.instrument_list_name, level1=self.level_1
            )
            return {d["ric"]: d["currencyRaw"] for d in res if d.get("currencyRaw")}
        except Exception as e:
            logger.warning(
                f"Unable to fetch currencies for list {self.instrument_list_name}: {str(e)}"
            )
            return {}

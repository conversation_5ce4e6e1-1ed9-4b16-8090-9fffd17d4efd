from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from datetime import datetime
from efdh_tasks.eod_parquet_converter.eod_parquet_converter_task import ParquetConverterTask


def run_eod_parquet_converter(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    """Aries task accepts a json of s3 uri's of structured refinitiv data,
    grouped by RIC per file to normalise to SE Parquet Schema.

    :param aries_task_input: file path which contains the parquet csvs paths to normalise
    """
    parquet_converter = ParquetConverterTask(aries_task_input=aries_task_input)
    result: AriesTaskResult = parquet_converter.execute()

    return result


if __name__ == "__main__":
    params = {
        "batch_file_uri": "s3://dev-masterdata.steeleye.co/lake/ingress/ric/structured/Nasdaq/EOD_batch/1699286135_batch__Zu81fmKCDiKAHoifI2G2P_0.json",
        "ilm_id": "john and joe and wanda's big adventure",
    }
    workflow = WorkflowFieldSet(
        name="localtest",
        stack="dev-master-data",
        tenant="master-data",
        start_timestamp=str(datetime.now()),
    )
    input_param = IOParamFieldSet(params=params)
    input = AriesTaskInput(
        workflow=workflow,
        input_param=input_param,
        task=TaskFieldSet(
            id="foo",
            name="eod-parquet-converter",
            version="1",
            success=True,
            previous_id=None,
        ),
    )
    run_eod_parquet_converter(aries_task_input=input)

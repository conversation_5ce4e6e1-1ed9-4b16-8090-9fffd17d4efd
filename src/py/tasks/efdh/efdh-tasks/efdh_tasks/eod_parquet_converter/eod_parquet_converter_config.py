import multiprocessing
from pydantic import BaseSettings, Field, root_validator


class EodParquetConverterSettings(BaseSettings):
    BUCKET: str = Field("s3://master-data.eu-west-1.steeleye.co")
    MAX_WORKERS: int = Field(multiprocessing.cpu_count())
    ELASTIC_URL: str
    ELASTIC_API_KEY: str
    EOD_STATS_COLUMNS_LIST: list = Field([])
    EFDH_API_URL: str = "https://efdh.dev.steeleye.co"

    @root_validator()
    def validate_bucket(cls, values):
        bucket = values.get("BUCKET")
        bucket = bucket.rstrip("/")
        values["BUCKET"] = bucket
        return values

    @root_validator()
    def convert_max_workers_to_int(cls, values):
        workers = values.get("MAX_WORKERS")
        values["MAX_WORKERS"] = int(workers)
        return values


eod_parquet_converter_settings: EodParquetConverterSettings = EodParquetConverterSettings()

import logging
import numpy as np
import os
import pandas as pd
import re
from datetime import datetime as dt
from datetime import timedelta
from efdh_tasks.eod_parquet_converter.eod_parquet_converter_config import (
    eod_parquet_converter_settings,
)
from efdh_tasks.eod_parquet_converter.static import (
    ECB_EURO_REF_RATE_INDEX,
    NOTIONAL_VOLUME_CURRENCIES,
    EoDColumnMap,
    FileTemplates,
    ParquetFrameDTypes,
)
from efdh_tasks.map_ric.static import CurrencyMappings
from efdh_utils.schema.parquet import EoDStatsColumns, EoDStatsPriceColumns
from efdh_utils.schema.refinitiv import ElektronExtractColumns
from efdh_utils.statistics import calculate_exponential_moving_average, calculate_volatility
from se_elasticsearch.repository.elasticsearch8 import ElasticsearchRepository
from se_elasticsearch.repository.models import ResourceConfig
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)

try:
    PERMISSIBLE_STATS_COLUMNS = eod_parquet_converter_settings.EOD_STATS_COLUMNS_LIST
    if not PERMISSIBLE_STATS_COLUMNS:
        raise ValueError("EOD_STATS_COLUMNS_LIST is empty")
except Exception:
    PERMISSIBLE_STATS_COLUMNS = [
        EoDStatsColumns.CLOSE_ASK_PRICE,
        EoDStatsColumns.CLOSE_BID_PRICE,
        EoDStatsColumns.CLOSE_PRICE,
        EoDStatsColumns.CURRENCY,
        EoDStatsColumns.DATE,
        EoDStatsColumns.EXCHANGE_CODE,
        EoDStatsColumns.HIGH_ASK_PRICE,
        EoDStatsColumns.HIGH_BID_PRICE,
        EoDStatsColumns.HIGH_PRICE,
        EoDStatsColumns.LOW_PRICE,
        EoDStatsColumns.LOW_ASK_PRICE,
        EoDStatsColumns.LOW_BID_PRICE,
        EoDStatsColumns.OPEN_ASK_PRICE,
        EoDStatsColumns.OPEN_BID_PRICE,
        EoDStatsColumns.OPEN_INTEREST,
        EoDStatsColumns.OPEN_PRICE,
        EoDStatsColumns.RIC,
        EoDStatsColumns.TRADE_VOLUME,
        EoDStatsColumns.VWAP,
        EoDStatsColumns.VOLUME_EMA,
    ]

RESOURCE_CONFIG = ResourceConfig(
    url=eod_parquet_converter_settings.ELASTIC_URL,
    api_key=eod_parquet_converter_settings.ELASTIC_API_KEY,
)


class NormaliseEODError(Exception):
    pass


class NormaliseEODSkip(Exception):
    pass


def parse_date(_id):
    try:
        if _id.isdigit():  # Handle Unix timestamp (e.g., 1528982100000)
            return pd.to_datetime(int(_id), unit="ms").date()

        # Use regex to extract full ISO 8601 timestamp
        match = re.search(r"\d{4}-\d{2}-\d{2}", _id)
        if match:
            return pd.to_datetime(match.group(), format="%Y-%m-%d").date()
    except Exception as exc:
        logger.exception(exc)
        pass  # Ignore parsing errors
    return None  # Return None for invalid dates


class NormaliseEODStats:
    """Transforms raw RIC csv file data to parquet and normalises columns and
    rows."""

    def __init__(
        self,
        ric: str,
        input_filepath: str,
        local_destination: str,
        remote_destination: str,
        bucket: str,
        currency_norm_override: Optional[bool] = None,
    ):
        logger.info(f"NormaliseEODStats args: {ric} | {input_filepath} | {local_destination}")
        self.ric = ric
        self.bucket = bucket
        self.input_filepath = input_filepath
        self.local_destination = local_destination
        self.remote_destination = remote_destination
        self.currency_norm_override = currency_norm_override
        self.es_client: ElasticsearchRepository = ElasticsearchRepository(RESOURCE_CONFIG)

    def process(
        self,
    ) -> Optional[Dict[str, Any]]:
        logger.info(f"NormaliseEODStats: we are about to read the {self.input_filepath}")
        new_stats_file = pd.read_csv(self.input_filepath)
        if ElektronExtractColumns.ORC in new_stats_file.columns:
            orc_present_mask = (
                new_stats_file[ElektronExtractColumns.ORC].notnull()
                & new_stats_file[ElektronExtractColumns.RIC].isnull()
            )
            new_stats_file.loc[orc_present_mask, ElektronExtractColumns.RIC] = new_stats_file.loc[
                orc_present_mask, ElektronExtractColumns.ORC
            ]
            new_stats_file = new_stats_file.drop(ElektronExtractColumns.ORC, axis=1)

        logger.info("Converting CSV file.")
        files = self.convert_to_parquet(
            df=new_stats_file,
            ric=self.ric,
        )
        return {"ric": self.ric, "files": files}

    def convert_to_parquet(
        self,
        df: pd.DataFrame,
        ric: str,
    ) -> Optional[List[Dict[str, str]]]:
        df.loc[:, ElektronExtractColumns.RIC] = df.loc[:, ElektronExtractColumns.RIC].str.replace(
            r"\^.*", "", regex=True
        )

        file_rics = df[ElektronExtractColumns.RIC].dropna().unique().tolist()

        if len(file_rics) != 1:
            raise NormaliseEODError("More than one RIC in file")

        row_count = df.shape[0]
        logger.info(
            f"Number of Rows pre-dropping `Universal Close Price` & `Trade Date` NaN's: {row_count}"
        )

        subset_cols = [ElektronExtractColumns.TRADE_DATE]

        df = df.sort_values(
            by=[ElektronExtractColumns.RIC, ElektronExtractColumns.UNIVERSAL_CLOSE_PRICE],
            na_position="first",
        )

        df = df.dropna(subset=subset_cols, how="all").drop_duplicates(
            subset=subset_cols, keep="last"
        )

        new_row_count = df.shape[0]
        logger.info(
            f"Row count post dropping `Universal Close Price` & `Trade Date`"
            f" NaN's {new_row_count}. "
            f"Difference: {row_count - new_row_count}"
        )

        if df.empty:
            logger.warning(
                f"EoD Data empty. Skipping flow for RIC: {ric} for KEY: {self.input_filepath}"
            )
            raise NormaliseEODSkip(f"{ric} file is empty: {self.input_filepath}")

        cols_needed = set(ElektronExtractColumns().get_columns())

        columns_diff = cols_needed - set(df.columns)
        df[list(columns_diff)] = pd.NA

        # Refactor columns to our desired titles
        df = self._rename_eod_stats_columns(frame=df)

        # Ensure parquet data types are correct and contain no commas
        df = self._format_numeric_columns(frame=df)
        # Date column as datetime to format it properly later
        df[EoDStatsColumns.DATE] = pd.to_datetime(df[EoDStatsColumns.DATE], format="%d/%m/%Y")

        logger.info(f"Normalising {ric}")
        df = self._normalise_prices_to_major(frame=df)

        # Create file paths
        existing_file = self._pull_ric_stats_file(ric_stats_file=self.local_destination)
        if not existing_file.empty:
            existing_file[EoDStatsColumns.DATE] = pd.to_datetime(
                existing_file[EoDStatsColumns.DATE], format="%Y-%m-%d"
            )
            existing_file = self._format_numeric_columns(frame=existing_file)

            df = df.set_index(df[EoDStatsColumns.DATE])
            existing_file = existing_file.set_index(existing_file[EoDStatsColumns.DATE])

            df[EoDStatsColumns.CURRENCY] = (
                df[EoDStatsColumns.CURRENCY]
                .combine_first(existing_file[EoDStatsColumns.CURRENCY])
                .ffill()
            )
            df = df.reset_index(drop=True)
            existing_file = existing_file.reset_index(drop=True)

            df = pd.concat([existing_file, df]).reset_index(drop=True)

        # Drop duplicates and keep most recent addition
        df = df.drop_duplicates(subset=[EoDStatsColumns.DATE], keep="last")
        df = df.sort_values(by=[EoDStatsColumns.DATE], kind="stable")

        for column in [
            EoDStatsColumns.DAILY_TRADED_NOTIONAL,
            EoDStatsColumns.PRICE_VOLATILITY,
            EoDStatsColumns.VOLUME_EMA,
            EoDStatsColumns.VOLUME_VOLATILITY,
        ]:
            if column not in df.columns:
                df[column] = pd.NA

        volume_ema_mask = df[EoDStatsColumns.VOLUME_EMA].isnull()
        df.loc[volume_ema_mask, EoDStatsColumns.VOLUME_EMA] = calculate_exponential_moving_average(
            market_data=df,
            data_column=EoDStatsColumns.TRADE_VOLUME,
            alpha=0.7,
            window=20,
            adv_column=EoDStatsColumns.VOLUME_EMA,
        )[volume_ema_mask]

        price_vol_mask = df[EoDStatsColumns.PRICE_VOLATILITY].isnull()
        df.loc[price_vol_mask, EoDStatsColumns.PRICE_VOLATILITY] = calculate_volatility(
            market_data=df,
            data_column=EoDStatsColumns.CLOSE_PRICE,
            volatility_window=10,
        )[price_vol_mask]

        volume_vol_mask = df[EoDStatsColumns.VOLUME_VOLATILITY].isnull()
        df.loc[volume_vol_mask, EoDStatsColumns.VOLUME_VOLATILITY] = calculate_volatility(
            market_data=df,
            data_column=EoDStatsColumns.TRADE_VOLUME,
            volatility_window=10,
        )[volume_vol_mask]
        df = self.create_notional(df)

        # hotfix to reduce eod parquet sizes for algorithm runs, if new columns are needed
        # add them to the env var for permissible columns and ensure algo's are able to cope
        # with increased parquet size
        stats_columns = [col for col in PERMISSIBLE_STATS_COLUMNS if col in df.columns]
        df = df.loc[:, stats_columns]

        return self.write_yearly_files(df=df)

    def _normalise_prices_to_major(self, frame: pd.DataFrame) -> pd.DataFrame:
        """Normalises prices & currency code to major if ccy in minor. currency
        norm override can be True, False or None, if True we should force ccy
        normalisation otherwise it should be determined by ccy code in raw
        data.

        :param frame:
        :return:
        """

        # Determine price minor/major currency normalisation strategy
        if self.currency_norm_override is True:
            minor_mask = pd.Series(True, index=frame.index)
        elif self.currency_norm_override is False:
            minor_mask = pd.Series(False, index=frame.index)
        elif self.currency_norm_override is None:
            minor_mask = frame[EoDStatsColumns.CURRENCY].isin(CurrencyMappings.MINOR_TO_MAJOR_CCY)
        else:
            raise ValueError("currency_norm_override must be True, False, or None")

        # If there's nothing to normalize, return the original frame
        if not minor_mask.any():
            return frame

        for column in EoDStatsPriceColumns().get_columns():
            try:
                mask: pd.Series[bool] = frame[column].notnull() & minor_mask
                frame.loc[mask, column] = frame.loc[mask, column] / 100
            except (ValueError, KeyError):
                logger.error(f"failed to convert column {column}")

        frame.loc[minor_mask, EoDStatsColumns.CURRENCY] = frame.loc[
            minor_mask, EoDStatsColumns.CURRENCY
        ].map(CurrencyMappings.MINOR_TO_MAJOR_CCY)

        return frame

    def _pull_ric_stats_file(self, ric_stats_file: str) -> pd.DataFrame:
        """

        :param ric_stats_url:
        :return:
        """

        try:
            frame = pd.read_parquet(ric_stats_file, engine="pyarrow")
            for c in EoDStatsColumns().get_columns():
                if c not in list(frame.columns) + [
                    EoDStatsColumns.MARKET_VWAP,
                    EoDStatsColumns.PRICE_VOLATILITY,
                    EoDStatsColumns.VOLUME_VOLATILITY,
                    EoDStatsColumns.DAILY_TRADED_NOTIONAL,
                ]:
                    logger.warning(f"column missing from {ric_stats_file}: {c}")
                    frame[c] = pd.NA
            if {EoDStatsColumns.MARKET_VWAP, EoDStatsColumns.VWAP}.issubset(frame.columns):
                mask = frame[EoDStatsColumns.VWAP].isnull() & (
                    frame[EoDStatsColumns.MARKET_VWAP].notnull()
                )
                frame.loc[mask, EoDStatsColumns.VWAP] = frame.loc[mask, EoDStatsColumns.MARKET_VWAP]

        except FileNotFoundError:
            logger.info(
                f"EoD Stats file for {ric_stats_file} not found. Creating first entry this flow."
            )
            frame = pd.DataFrame()

        return frame

    @staticmethod
    def _rename_eod_stats_columns(frame: pd.DataFrame) -> pd.DataFrame:
        """Rename columns to our desired titles.

        :param frame:
        :return:
        """
        return frame.rename(EoDColumnMap.MAP, axis=1)

    def write_yearly_files(self, df: pd.DataFrame) -> List[Dict[str, str]]:
        outputs: List[Dict[str, str]] = []
        file_dir = FileTemplates.DOWNLOAD_PATH.format(ric=self.ric)
        remote_dir = FileTemplates.DESTINATION_DIR.format(ric=self.ric)
        for year in df[EoDStatsColumns.DATE].dt.year.unique():
            file_name = FileTemplates.YEARLY_ROLLING_FILE_NAME_FORMAT.format(
                ric=self.ric, year=year
            )
            year_local_destination = file_dir + file_name

            year_remote_destination = f"{self.bucket}/{remote_dir}{file_name}"

            year_mask = df[EoDStatsColumns.DATE].dt.year == year
            year_df = df.loc[year_mask, :]

            if os.path.exists(year_local_destination):
                existing_file = self._pull_ric_stats_file(ric_stats_file=year_local_destination)
                if not existing_file.empty:
                    existing_file[EoDStatsColumns.DATE] = pd.to_datetime(
                        existing_file[EoDStatsColumns.DATE], format="%Y-%m-%d"
                    )
                    existing_file = self._format_numeric_columns(frame=existing_file)
                    year_df = pd.concat([existing_file, year_df]).reset_index(drop=True)
                    # Drop duplicates and keep most recent addition
                    year_df = year_df.drop_duplicates(subset=[EoDStatsColumns.DATE], keep="last")
                    year_df = year_df.sort_values(by=[EoDStatsColumns.DATE], kind="stable")

            self._write_parquet(year_df, year_local_destination)
            outputs.append(
                {
                    "local_destination": year_local_destination,
                    "remote_destination": year_remote_destination,
                }
            )

        rolling_mask = df[EoDStatsColumns.DATE] >= dt.now() - timedelta(days=365)
        self._write_parquet(df.loc[rolling_mask, :], self.local_destination)
        outputs.append(
            {
                "local_destination": self.local_destination,
                "remote_destination": self.remote_destination,
            }
        )
        return outputs

    @staticmethod
    def _write_parquet(df: pd.DataFrame, local_destination: str) -> None:
        """Writes to s3 the parquet.
        :param df:
        :param local_destination:
        :return:
        """
        logger.info(f"Writing parquet {local_destination}")
        df[EoDStatsColumns.DATE] = df[EoDStatsColumns.DATE].dt.strftime("%Y-%m-%d")
        data_types = {k: v for k, v in ParquetFrameDTypes.TYPES.items() if k in df.columns}

        for col, dtype in data_types.items():
            if dtype == "float64":
                df[col] = df[col].mask(df[col].isna(), np.nan)

        df = df.astype(data_types)
        df.to_parquet(local_destination, engine="pyarrow", index=False)

    @staticmethod
    def _get_numeric_columns() -> List[Any]:
        """
        Get columns that need to be numeric
        :return:
        """
        force_columns = EoDStatsPriceColumns().get_columns()
        force_columns.extend([EoDStatsColumns.TRADE_VOLUME, EoDStatsColumns.OPEN_INTEREST])
        return force_columns

    def _format_numeric_columns(self, frame: pd.DataFrame) -> pd.DataFrame:
        """

        :param frame:
        :return:
        """
        numeric_columns = self._get_numeric_columns()
        if not set(numeric_columns).issubset(frame.columns):
            logger.warning(
                f"Extra columns present / EoDStats columns missing in parquet df"
                f"Columns MISSING: {set(numeric_columns) - set(frame.columns)}"
                f"For RIC {frame[EoDStatsColumns.RIC].unique().tolist()}"
            )
            numeric_columns = list(set(numeric_columns).intersection(frame.columns))

        frame[numeric_columns] = frame[numeric_columns].replace(",", "", regex=True)
        frame[numeric_columns] = frame[numeric_columns].apply(pd.to_numeric, errors="coerce")

        return frame

    def create_notional(self, frame: pd.DataFrame) -> pd.DataFrame:
        """Multiply the close price by the trade volume.

        Args:
            frame (pd.DataFrame): Input dataframe

        Returns:
            pd.DataFrame: Updated dataframe
        """
        frame[EoDStatsColumns.DAILY_TRADED_NOTIONAL] = (
            frame[EoDStatsColumns.TRADE_VOLUME] * frame[EoDStatsPriceColumns.CLOSE_PRICE]
        )

        for _, target_column in NOTIONAL_VOLUME_CURRENCIES.items():
            frame[target_column] = pd.NA

        base_ccys = frame[EoDStatsColumns.CURRENCY].dropna().unique().tolist()
        for base_ccy in base_ccys:
            for target_ccy, target_column in NOTIONAL_VOLUME_CURRENCIES.items():
                if base_ccy == target_ccy:
                    base_ccy_mask = frame[EoDStatsColumns.CURRENCY] == base_ccy
                    frame.loc[base_ccy_mask, target_column] = frame.loc[
                        base_ccy_mask, EoDStatsColumns.DAILY_TRADED_NOTIONAL
                    ]

        frame = self.convert_currency_using_ecb(
            frame=frame,
            base_ccys=base_ccys,
        )

        return frame

    def convert_currency_using_ecb(self, frame: pd.DataFrame, base_ccys: List[str]) -> pd.DataFrame:
        """Converts the currencies in the frame to generate the fields in
         NOTIONAL_VOLUME_CURRENCIES.
        Args:
            frame (pd.DataFrame): Input data frame
            base_ccys (List[str]): The list of base currencies observed in
             the data

        Returns:
            pd.DataFrame: Updated data with columns populated where possible
        """
        try:
            response = self.get_ecb_rates(
                from_date=frame[EoDStatsColumns.DATE].min(skipna=True).strftime("%Y-%m-%d"),
                to_date=frame[EoDStatsColumns.DATE].max(skipna=True).strftime("%Y-%m-%d"),
            )
        except ConnectionError as e:
            logger.exception(e)
            raise NormaliseEODError("Elastic Connection Error")

        map_df = self._process_ecb_data(
            response=response,
            es_client=self.es_client,
            ccys=list(NOTIONAL_VOLUME_CURRENCIES.keys()),
        )
        drop_columns = list(map_df.columns.values)
        drop_columns.pop(drop_columns.index(EoDStatsColumns.DATE))

        frame = frame.merge(map_df, how="left", on=EoDStatsColumns.DATE)

        # Map into EUR
        for base_ccy in base_ccys:
            if base_ccy not in frame.columns:
                logger.warning(
                    f"Currency {base_ccy} not found in ECB ref rates data, so unable to convert"
                )
                continue
            # if base_ccy in ["KRW", "BRL"]:
            #     frame = self.bonds_transformation(
            #         frame=frame,
            #         base_ccy=base_ccy,
            #         cols=BONDS_TRANSFORM_COLUMNS,
            #         es_client=es_client,
            #     )
            base_ccy_mask = (frame[EoDStatsColumns.CURRENCY] == base_ccy) & (
                frame[NOTIONAL_VOLUME_CURRENCIES["EUR"]].isnull()
            )
            frame.loc[base_ccy_mask, NOTIONAL_VOLUME_CURRENCIES["EUR"]] = (
                frame.loc[base_ccy_mask, EoDStatsColumns.DAILY_TRADED_NOTIONAL]
                / frame.loc[base_ccy_mask, base_ccy]
            )
        # Map into other currencies from EUR
        for target_ccy, target_column in NOTIONAL_VOLUME_CURRENCIES.items():
            if target_ccy != "EUR":
                base_ccy_mask = frame[target_column].isnull()
                frame.loc[base_ccy_mask, target_column] = (
                    frame.loc[base_ccy_mask, NOTIONAL_VOLUME_CURRENCIES["EUR"]]
                    * frame.loc[base_ccy_mask, target_ccy]
                )

        return frame.drop(drop_columns, axis=1)

    def get_ecb_rates(self, from_date: str, to_date: str) -> Any:
        """Gets ECB ref exchange rates using the index constant ECB_EURO_REF_RATE_INDEX,
        and returns the json response as a dict tree
        Args:
            from_date (str): start date of data required
            to_date (str): end date of data required (inclusive)
            es_client (ElasticsearchClient): The client to use for the request

        Returns:
            Dict: _description_
        """
        ecb_query = self._get_ecb_query(from_date=from_date, to_date=to_date)
        response = self.es_client.search(
            query=ecb_query,
            index=ECB_EURO_REF_RATE_INDEX,
        )
        return response

    @staticmethod
    def _get_ecb_query(from_date: str, to_date: str) -> dict:
        """Makes ES query for ECB data in the given date range.

        Args:
            from_date (str): Start date
            to_date (str): End date (inclusive)

        Returns:
            dict: Query as dict
        """
        query = {
            "size": 5000,
            "query": {"range": {"timestamp": {"gte": from_date, "lte": to_date}}},
        }

        return query

    @staticmethod
    def _process_ecb_data(
        response: dict, es_client: ElasticsearchRepository, ccys: List[str]
    ) -> pd.DataFrame:
        """Process ES response from SRP.

        :params response: dict response from elastic search
        :params es_client: ElasticsearchClient elastic client
        :return: Optional[pd.DataFrame] merged and cleared data
        """
        remove_cols = [
            es_client.meta.id,
            es_client.meta.key,
            es_client.meta.model,
            es_client.meta.timestamp,
            es_client.meta.trait_fqn,
            es_client.meta.user,
            "_id",
            "_index",
            "_score",
            "_source",
            "_type",
        ]

        data = response.get("hits", {}).get("hits", {})

        if not data:
            return pd.DataFrame(columns=[EoDStatsColumns.DATE] + ccys)

        result = pd.DataFrame.from_records(data)
        result[EoDStatsColumns.DATE] = result["_id"].apply(parse_date)
        result = result.dropna(subset=[EoDStatsColumns.DATE])  # Drop rows where date is None
        result[EoDStatsColumns.DATE] = pd.to_datetime(
            result[EoDStatsColumns.DATE]
        )  # Convert 'date' to Pandas datetime

        result = result["_source"].apply(pd.Series).merge(result, left_index=True, right_index=True)

        remove_cols = [col for col in remove_cols if col in result.columns]

        return result.drop(remove_cols, axis=1)

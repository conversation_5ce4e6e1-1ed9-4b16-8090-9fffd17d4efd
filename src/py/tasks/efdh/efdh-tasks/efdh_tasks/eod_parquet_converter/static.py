from market_data_utils.schema.parquet import EoDStatsColumns
from market_data_utils.schema.refinitiv import ElektronExtractColumns


class EoDColumnMap:
    MAP = {
        ElektronExtractColumns.TRADE_DATE: EoDStatsColumns.DATE,
        ElektronExtractColumns.RIC: EoDStatsColumns.RIC,
        ElektronExtractColumns.CURRENCY_CODE: EoDStatsColumns.CURRENCY,
        ElektronExtractColumns.EXCHANGE_CODE: EoDStatsColumns.EXCHANGE_CODE,
        ElektronExtractColumns.VOLUME: EoDStatsColumns.TRADE_VOLUME,
        ElektronExtractColumns.OPEN_INTEREST: EoDStatsColumns.OPEN_INTEREST,
        ElektronExtractColumns.UNIVERSAL_CLOSE_PRICE: EoDStatsColumns.CLOSE_PRICE,
        ElektronExtractColumns.ASK_HIGH: EoDStatsColumns.HIGH_ASK_PRICE,
        ElektronExtractColumns.LOW_ASK: EoDStatsColumns.LOW_ASK_PRICE,
        ElektronExtractColumns.OPEN_ASK: EoDStatsColumns.OPEN_ASK_PRICE,
        ElektronExtractColumns.UNIVERSAL_ASK_PRICE: EoDStatsColumns.CLOSE_ASK_PRICE,
        ElektronExtractColumns.BID_HIGH: EoDStatsColumns.HIGH_BID_PRICE,
        ElektronExtractColumns.LOW_BID: EoDStatsColumns.LOW_BID_PRICE,
        ElektronExtractColumns.OPEN_BID: EoDStatsColumns.OPEN_BID_PRICE,
        ElektronExtractColumns.UNIVERSAL_BID_PRICE: EoDStatsColumns.CLOSE_BID_PRICE,
        ElektronExtractColumns.MARKET_VWAP: EoDStatsColumns.MARKET_VWAP,
        ElektronExtractColumns.HIGH: EoDStatsColumns.HIGH_PRICE,
        ElektronExtractColumns.LOW: EoDStatsColumns.LOW_PRICE,
        ElektronExtractColumns.OPEN: EoDStatsColumns.OPEN_PRICE,
        ElektronExtractColumns.VWAP: EoDStatsColumns.VWAP,
    }


class ParquetFrameDTypes:
    TYPES = {
        EoDStatsColumns.DATE: "str",
        EoDStatsColumns.RIC: "str",
        EoDStatsColumns.CURRENCY: "str",
        EoDStatsColumns.EXCHANGE_CODE: "str",
        EoDStatsColumns.TRADE_VOLUME: "float64",
        EoDStatsColumns.OPEN_INTEREST: "float64",
        EoDStatsColumns.CLOSE_PRICE: "float64",
        EoDStatsColumns.HIGH_ASK_PRICE: "float64",
        EoDStatsColumns.LOW_ASK_PRICE: "float64",
        EoDStatsColumns.OPEN_ASK_PRICE: "float64",
        EoDStatsColumns.CLOSE_ASK_PRICE: "float64",
        EoDStatsColumns.HIGH_BID_PRICE: "float64",
        EoDStatsColumns.LOW_BID_PRICE: "float64",
        EoDStatsColumns.OPEN_BID_PRICE: "float64",
        EoDStatsColumns.CLOSE_BID_PRICE: "float64",
        EoDStatsColumns.VWAP: "float64",
        EoDStatsColumns.HIGH_PRICE: "float64",
        EoDStatsColumns.LOW_PRICE: "float64",
        EoDStatsColumns.OPEN_PRICE: "float64",
        EoDStatsColumns.PRICE_VOLATILITY: "float64",
        EoDStatsColumns.VOLUME_VOLATILITY: "float64",
        EoDStatsColumns.VOLUME_EMA: "float64",
        EoDStatsColumns.DAILY_TRADED_NOTIONAL: "float64",
        EoDStatsColumns.CHF: "float64",
        EoDStatsColumns.GBP: "float64",
        EoDStatsColumns.AUD: "float64",
        EoDStatsColumns.SGD: "float64",
        EoDStatsColumns.EUR: "float64",
        EoDStatsColumns.JPY: "float64",
        EoDStatsColumns.USD: "float64",
    }


class FileTemplates:
    DESTINATION_DIR = "lake/ingress/ric/curated/{ric}/"
    PARQUET_ROLLING_FILE_NAME_FORMAT = "{ric}__EOD_ROLLING_STATS.parquet"
    ARCHIVE_FILE_NAME_FORMAT = "archive__{ric}__EOD_ROLLING_STATS.parquet"
    YEARLY_ROLLING_FILE_NAME_FORMAT = "{ric}__{year}__EOD_ROLLING_STATS.parquet"
    RIC_CSV_NAME_FORMAT = "{ric}.csv"
    DOWNLOAD_PATH = "downloads/temp/csv/{ric}/"
    CONVERT_DIR = "downloads/ric/curated/{ric}/"
    STATS_CONVERT_DIR = "downloads/ric/curated/{ric}/"


NOTIONAL_VOLUME_CURRENCIES = {
    "CHF": "Daily Traded Notional (CHF)",
    "GBP": "Daily Traded Notional (GBP)",
    "EUR": "Daily Traded Notional (EUR)",
    "JPY": "Daily Traded Notional (JPY)",
    "USD": "Daily Traded Notional (USD)",
    "SGD": "Daily Traded Notional (SGD)",
    "AUD": "Daily Traded Notional (AUD)",
}

ECB_EURO_REF_RATE_INDEX = ".ecbEuroRefRate"

BONDS_TRANSFORM_COLUMNS = [
    EoDStatsColumns.OPEN_PRICE,
    EoDStatsColumns.OPEN_BID_PRICE,
    EoDStatsColumns.OPEN_ASK_PRICE,
    EoDStatsColumns.CLOSE_PRICE,
    EoDStatsColumns.HIGH_PRICE,
    EoDStatsColumns.LOW_PRICE,
]

import boto3
import logging
import os
import polars as pl
import shutil
from aries_io_event.io_param import IOParamFieldSet
from aries_se_api_client.client import AriesApiClient
from aries_se_core_tasks.aries.utility_tasks.finish_flow import create_io_params_list
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from datetime import date, datetime, timedelta, timezone
from efdh_tasks.loans_cds_converter.loans_cds_config import loans_cds_settings as settings
from efdh_tasks.loans_cds_converter.loans_cds_input_schema import LoansCDSInput
from efdh_tasks.loans_cds_converter.static import (
    DataLakeFileTemplate,
    IHSMarkitFileTemplate,
    IHSType,
    UpdateLocalRicLookupFileTemplate,
)
from fsspec.implementations.sftp import SFTPFileSystem
from market_data_utils.schema.ihs import (
    CDSCreditIndexColumns,
    CDSCreditSingleColumns,
    CDSCreditTrancheColumns,
    LoansColumns,
    LoansDeltaColumns,
)
from market_data_utils.schema.parquet import (
    EoDStatsColumns,
    LoansDeltaColumnsRicLookup,
    ParquetCDSColumns,
    ParquetLoansColumns,
    RicColumns,
)
from pathlib import Path
from reference_api_client.instrument_list_monitoring import ILM
from se_fsspec_utils.sftp_utils import get_sftp_fs
from se_io_utils.tempfile_utils import tmp_directory
from se_market_data_utils.client import MarketDataAPI
from typing import Any, Callable, Dict, List, Optional
from zipfile import ZipFile

logger = logging.getLogger(__name__)

REQUIRED_STATS_COLUMNS = settings.eod_stats_columns_list

COLUMN_DTYPES = {
    ParquetCDSColumns.RIC: pl.Utf8,
    ParquetCDSColumns.RED_CODE: pl.Utf8,
    ParquetCDSColumns.TENOR: pl.Utf8,
    ParquetCDSColumns.DATE: pl.Utf8,
    ParquetCDSColumns.CURRENCY: pl.Utf8,
    ParquetCDSColumns.CLOSE_PRICE: pl.Float64,
    ParquetCDSColumns.CLOSE_ASK_PRICE: pl.Float64,
    ParquetCDSColumns.CLOSE_BID_PRICE: pl.Float64,
    EoDStatsColumns.OPEN_PRICE: pl.Float64,
    EoDStatsColumns.VWAP: pl.Float64,
    EoDStatsColumns.VOLUME_EMA: pl.Float64,
    EoDStatsColumns.TRADE_VOLUME: pl.Float64,
}


class LoansCDSConverter:
    def __init__(
        self,
        aries_task_input: AriesTaskInput,
    ):
        self.aries_task_input = aries_task_input
        self.input_param = LoansCDSInput.validate(aries_task_input.input_param.params)
        self.s3_client = boto3.client("s3")
        self.ref_api_client = AriesApiClient(host=settings.se_reference_api_url)
        self.ilm = ILM(client=self.ref_api_client)
        self.market_data_api = MarketDataAPI()
        self.rics_client = self.market_data_api.rics
        self.ilm_id = None
        self.counts: Dict[Any, Any] = {}
        self.date = datetime.now(timezone.utc).strftime("%Y/%m/%d")
        self.tmp_dir = "zip"

    def execute(self) -> AriesTaskResult:
        """
        :return:
        """
        logger.info(f"Loans / CDS Task Engaged. \n Args: {self.input_param}")

        try:
            ihs_type = self.input_param.type

            match ihs_type:
                case IHSType.CDS_INDEX:
                    template = IHSMarkitFileTemplate.CREDIT_INDEX_FILENAME
                    normaliser = self._normalise_credit_index
                case IHSType.CDS_TRANCHE:
                    template = IHSMarkitFileTemplate.CREDIT_TRANCHE_FILENAME
                    normaliser = self._normalise_credit_tranche
                case IHSType.CDS_SINGLE:
                    template = IHSMarkitFileTemplate.CREDIT_SINGLE_FILENAME
                    normaliser = self._normalise_credit_single
                case IHSType.LOANS:
                    template = IHSMarkitFileTemplate.LOANS_FILENAME
                    normaliser = self._normalise_loans
                case IHSType.DELTA:
                    template = IHSMarkitFileTemplate.LOANS_DELTA_FILENAME
                    normaliser = self._normalise_loans_delta
                    ihs_type = IHSType.LOANS
                case _:
                    raise ValueError(f"Unsupported IHS type: {ihs_type}")

            return self._run_process(
                skip_existing=self.input_param.skip_existing,
                raw_filename_template=template,
                ihs_type=ihs_type,
                normalise_func=normaliser,
            )

        except Exception as e:
            logger.exception(e)
            raise

    def _run_process(
        self,
        skip_existing: bool,
        raw_filename_template: str,
        ihs_type: IHSType,
        normalise_func: Callable[[str], pl.LazyFrame],
    ) -> AriesTaskResult:
        """Helper function to reduce duplication."""
        try:
            sftp_client = get_sftp_fs(
                host=settings.ihs_markit_sftp_host,
                port=settings.ihs_markit_sftp_port,
                username=settings.ihs_sftp_username,
                password=settings.ihs_sftp_password,
            )

            paths = self.process_loans_cds(
                skip_existing=skip_existing,
                raw_filename_template=raw_filename_template,
                ihs_type=ihs_type,
                normalise_func=normalise_func,
                sftp_client=sftp_client,
            )

            if self.input_param.type != IHSType.DELTA:
                self.ilm.set_batches(id=self.ilm_id, totalBatches=len(paths))

            logger.info(f"{ihs_type.name} Task Disengaged.")

            output = create_io_params_list(
                iterable_item={"batch_file_uri": paths}, ilm_id=self.ilm_id
            )
            return AriesTaskResult(output_param=IOParamFieldSet(params=output))

        except Exception as e:
            logger.exception(e)
            if self.ilm_id is not None:
                self.ilm.set_failed(id=self.ilm_id)
            raise

    def _exists(self, bucket: str, key: str) -> bool:
        file_exists = True
        try:
            self.s3_client.head_object(Bucket=bucket, Key=key)
        except self.s3_client.exceptions.ClientError:
            file_exists = False
        return file_exists

    def process_loans_cds(
        self,
        skip_existing: bool,
        sftp_client: SFTPFileSystem,
        raw_filename_template: str,
        ihs_type: IHSType,
        normalise_func: Callable,
    ):
        logger.info(f"Processing {ihs_type.value}")
        sftp_dir, datetime_format = (
            (IHSMarkitFileTemplate.LOANS_DIR, IHSMarkitFileTemplate.LOAN_DATE_FORMAT)
            if ihs_type == IHSType.LOANS
            else (IHSMarkitFileTemplate.CREDIT_DIR, IHSMarkitFileTemplate.CREDIT_DATE_FORMAT)
        )
        filenames = self._create_sftp_filename(
            filename_template=raw_filename_template,
            datetime_format=datetime_format,
            lookback=self.input_param.lookback_period,
        )
        available_files = self._ihs_available_files(
            filenames=filenames, ihs_dir=sftp_dir, sftp_client=sftp_client
        )
        result_dfs = []
        filenames = []
        for sftp_filepath in available_files:
            filename = Path(sftp_filepath).name
            download_data_lake_filepath = (
                DataLakeFileTemplate.RAW_LOCAL.format(IHS_TYPE=ihs_type.value) + "/" + filename
            )
            remote_destination_raw_key = (
                DataLakeFileTemplate.RAW_REMOTE_LOANS.format(IHS_TYPE=ihs_type.value)
                + "/"
                + filename
            )
            if skip_existing and self._exists(
                bucket=settings.s3_bucket.lstrip("s3://"),
                key=remote_destination_raw_key.lstrip("/"),
            ):
                logger.info(f"Skipping file: {remote_destination_raw_key} as it exists already")
                continue

            logger.info(f"Retrieving file {sftp_filepath}")
            if not self._upload_raw_stfp_file(
                sftp_filepath=sftp_filepath,
                download_filepath=download_data_lake_filepath,
                bucket=settings.s3_bucket,
                key=remote_destination_raw_key,
                sftp_client=sftp_client,
            ):
                continue
            filenames.append(filename)

            normalised_result = normalise_func(filepath=download_data_lake_filepath)
            if normalised_result.limit(1).fetch().is_empty():
                continue
            result_dfs.append(normalised_result.collect())

        if not result_dfs:
            return []

        final_frame = pl.concat(result_dfs, how="diagonal")
        if os.path.exists(self.tmp_dir):
            shutil.rmtree(self.tmp_dir)

        self.ilm_id = self.ilm.create_extraction(
            scheduleId=self.input_param.type.value,
            fileName="|".join(filenames),
            traceId=self.aries_task_input.workflow.trace_id,
        )

        if self.input_param.type == IHSType.DELTA:
            self.ilm.set_batches(id=self.ilm_id, totalBatches=1)
            # upload to local_ric_lookup destination
            loans_delta_filepath = (
                UpdateLocalRicLookupFileTemplate.UPDATE_LOCAL_RIC_LOOKUP_FILENAME_FORMAT.format(
                    DATE=date.today().strftime(UpdateLocalRicLookupFileTemplate.DATE_FORMAT)
                )
            )

            local_tmp = tmp_directory().joinpath(Path(loans_delta_filepath))
            final_frame.write_csv(local_tmp)
            with open(local_tmp, "rb") as f:
                self.s3_client.put_object(
                    Bucket=settings.refinitiv_bucket.lstrip("s3://"),
                    Key=settings.update_ric_lookup_key + "/" + loans_delta_filepath,
                    Body=f,
                    ACL="bucket-owner-full-control",
                )

            # delete temp file
            if os.path.exists(local_tmp):
                os.remove(local_tmp)

            logger.info(
                f"Uploaded delta file to {settings.update_ric_lookup_key}/{loans_delta_filepath}"
            )

            self.ilm.increment_batches(id=self.ilm_id)

            return []

        logger.info("Splitting normalized data by #RIC and creating files for upload")

        return self._split_df_by_identifier(frame=final_frame, ihs_type=ihs_type)

    def _create_sftp_filename(
        self, filename_template: str, datetime_format: str, lookback: int = 5
    ) -> List[str]:
        if "{FILE_DATE}" not in filename_template:
            raise AttributeError("Credit filename template must have {FILE_DATE}")

        today = date.today()
        filepaths = []
        for day in range(0, lookback):
            file_date = today - timedelta(days=day)
            file_date_formatted = file_date.strftime(datetime_format)
            filepaths.append(filename_template.format(FILE_DATE=file_date_formatted))

        return filepaths

    @staticmethod
    def _ihs_available_files(filenames: List[str], ihs_dir: str, sftp_client) -> List[str]:
        available_files = sftp_client.listdir(path=ihs_dir)
        available_files = [x.get("name") for x in available_files]
        return list(set(filenames).intersection(available_files))

    def _upload_raw_stfp_file(
        self,
        sftp_filepath: str,
        download_filepath: str,
        bucket: str,
        key: str,
        sftp_client: SFTPFileSystem,
    ) -> bool:
        Path(download_filepath).parent.mkdir(exist_ok=True, parents=True)
        try:
            sftp_client.get(sftp_filepath, download_filepath)
            with open(download_filepath, "rb") as f:
                self.s3_client.put_object(
                    Bucket=bucket.lstrip("s3://"),
                    Key=key.lstrip("/"),
                    Body=f,
                    ACL="bucket-owner-full-control",
                )

            logger.info(f"Created raw file in {bucket} at {key}")
        except Exception as e:
            logger.exception(e)
            return False

        return True

    def _normalise_credit_index(self, filepath: str) -> pl.LazyFrame:
        """
        :param filepath:
        :return: pl.LazyFrame
        """

        lazy_df = self.zip_to_polars(zip_file_path=filepath)

        if not self.validate_ihs_df(
            df=lazy_df, columns=CDSCreditIndexColumns().get_columns(), file_type="Credit Index"
        ):
            return pl.LazyFrame()

        result = lazy_df.select(
            [
                pl.format(
                    "{}.{}",
                    pl.col(CDSCreditIndexColumns.INDEX_RED_CODE),
                    pl.col(CDSCreditIndexColumns.INDEX_TENOR),
                ).alias(ParquetCDSColumns.RIC),
                pl.col(CDSCreditIndexColumns.INDEX_RED_CODE).alias(ParquetCDSColumns.RED_CODE),
                pl.col(CDSCreditIndexColumns.INDEX_TENOR).alias(ParquetCDSColumns.TENOR),
                pl.col(CDSCreditIndexColumns.DATE).alias(ParquetCDSColumns.DATE),
                pl.col(CDSCreditIndexColumns.CURRENCY).alias(ParquetCDSColumns.CURRENCY),
                pl.col(CDSCreditIndexColumns.COMPOSITE_PRICE_MID).alias(
                    ParquetCDSColumns.CLOSE_PRICE
                ),
                pl.col(CDSCreditIndexColumns.COMPOSITE_PRICE_ASK).alias(
                    ParquetCDSColumns.CLOSE_ASK_PRICE
                ),
                pl.col(CDSCreditIndexColumns.COMPOSITE_PRICE_BID).alias(
                    ParquetCDSColumns.CLOSE_BID_PRICE
                ),
            ]
        )

        existing_cols = set(result.collect_schema())
        missing_cols = [col for col in REQUIRED_STATS_COLUMNS if col not in existing_cols]
        if missing_cols:
            result = result.with_columns([pl.lit(None).alias(col) for col in missing_cols])

        result = self._convert_dtype(result)

        return result

    def _normalise_credit_tranche(self, filepath: str) -> pl.LazyFrame:
        """
        :param filepath:
        :return: pl.LazyFrame
        """

        lazy_df = self.zip_to_polars(zip_file_path=filepath)

        if not self.validate_ihs_df(
            df=lazy_df, columns=CDSCreditTrancheColumns().get_columns(), file_type="Credit Tranche"
        ):
            return pl.LazyFrame()

        result = lazy_df.select(
            [
                pl.format(
                    "{}.{}.{}.{}",
                    pl.col(CDSCreditTrancheColumns.INDEX_RED_CODE),
                    pl.col(CDSCreditTrancheColumns.INDEX_TERM),
                    pl.col(CDSCreditTrancheColumns.ATTACHMENT).cast(pl.Utf8),
                    pl.col(CDSCreditTrancheColumns.DETACHMENT).cast(pl.Utf8),
                ).alias(ParquetCDSColumns.RIC),
                pl.col(CDSCreditTrancheColumns.INDEX_RED_CODE).alias(ParquetCDSColumns.RED_CODE),
                pl.col(CDSCreditTrancheColumns.INDEX_TERM).alias(ParquetCDSColumns.TENOR),
                pl.col(CDSCreditTrancheColumns.DATE).alias(ParquetCDSColumns.DATE),
                pl.col(CDSCreditTrancheColumns.TRANCHE_UP_FRONT_MID).alias(
                    ParquetCDSColumns.CLOSE_PRICE
                ),
                pl.col(CDSCreditTrancheColumns.TRANCHE_UP_FRONT_ASK).alias(
                    ParquetCDSColumns.CLOSE_ASK_PRICE
                ),
                pl.col(CDSCreditTrancheColumns.TRANCHE_UP_FRONT_BID).alias(
                    ParquetCDSColumns.CLOSE_BID_PRICE
                ),
            ]
        )

        existing_cols = set(result.collect_schema())
        missing_cols = [col for col in REQUIRED_STATS_COLUMNS if col not in existing_cols]
        if missing_cols:
            result = result.with_columns([pl.lit(None).alias(col) for col in missing_cols])

        result = self._convert_dtype(result)

        return result

    @staticmethod
    def _convert_dtype(lazy_df: pl.LazyFrame) -> pl.LazyFrame:
        schema = lazy_df.collect_schema()

        lazy_df = lazy_df.with_columns(
            [
                pl.when(pl.col(col).cast(pl.Utf8).str.strip_chars() == "")
                .then(None)
                .otherwise(pl.col(col))
                .alias(col)
                if schema[col] == pl.Utf8
                else pl.col(col).cast(dtype).alias(col)
                for col, dtype in COLUMN_DTYPES.items()
                if col in schema.names()
            ]
        )
        return lazy_df

    def _normalise_credit_single(self, filepath: str) -> Any:
        """

        :param filepath:
        :return:
        """

        lazy_df = self.zip_to_polars(zip_file_path=filepath)

        if not self.validate_ihs_df(
            df=lazy_df, columns=CDSCreditSingleColumns().get_columns(), file_type="Credit Single"
        ):
            return pl.LazyFrame()

        lazy_df = lazy_df.filter(pl.col(CDSCreditSingleColumns.PRIMARY_COUPON).eq("Y"))

        lazy_df = lazy_df.with_columns(
            pl.format(
                "{}.{}.{}.{}.{}",
                pl.col(CDSCreditSingleColumns.RED_CODE),
                pl.col(CDSCreditSingleColumns.TENOR),
                pl.col(CDSCreditSingleColumns.TIER),
                pl.col(CDSCreditSingleColumns.CURRENCY),
                pl.col(CDSCreditSingleColumns.DOC_CLAUSE),
            ).alias(ParquetCDSColumns.RIC)
        )

        lazy_df = lazy_df.rename(
            {
                CDSCreditSingleColumns.RED_CODE: ParquetCDSColumns.RED_CODE,
                CDSCreditSingleColumns.TIER: ParquetCDSColumns.SENIORITY,
                CDSCreditSingleColumns.TENOR: ParquetCDSColumns.TENOR,
                CDSCreditSingleColumns.DATE: ParquetCDSColumns.DATE,
                CDSCreditSingleColumns.CURRENCY: ParquetCDSColumns.CURRENCY,
            }
        )

        primary_price_predicate = pl.col(CDSCreditSingleColumns.PRIMARY_PRICE_TYPE).eq("ConvSpread")

        lazy_df = lazy_df.with_columns(
            [
                pl.when(primary_price_predicate)
                .then(pl.col("ConvSpreadMid"))
                .otherwise(pl.col("UpfrontMid"))
                .alias(ParquetCDSColumns.CLOSE_PRICE),
                pl.when(primary_price_predicate)
                .then(pl.col("ConvSpreadAsk"))
                .otherwise(pl.col("UpfrontAsk"))
                .alias(ParquetCDSColumns.CLOSE_ASK_PRICE),
                pl.when(primary_price_predicate)
                .then(pl.col("ConvSpreadBid"))
                .otherwise(pl.col("UpfrontBid"))
                .alias(ParquetCDSColumns.CLOSE_BID_PRICE),
            ]
        )

        columns_to_write = [
            ParquetCDSColumns.RIC,
            ParquetCDSColumns.RED_CODE,
            ParquetCDSColumns.SENIORITY,
            ParquetCDSColumns.TENOR,
            ParquetCDSColumns.DATE,
            ParquetCDSColumns.CURRENCY,
            ParquetCDSColumns.CLOSE_PRICE,
            ParquetCDSColumns.CLOSE_ASK_PRICE,
            ParquetCDSColumns.CLOSE_BID_PRICE,
            EoDStatsColumns.OPEN_PRICE,
            EoDStatsColumns.VWAP,
            EoDStatsColumns.VOLUME_EMA,
            EoDStatsColumns.TRADE_VOLUME,
        ]
        columns_to_write = list(dict.fromkeys(columns_to_write + REQUIRED_STATS_COLUMNS))

        for column in columns_to_write:
            if column not in lazy_df.collect_schema().names():
                lazy_df = lazy_df.with_columns(pl.lit(None).alias(column))

        df = self._convert_dtype(lazy_df)
        return df.select(columns_to_write)

    def zip_to_polars(
        self,
        zip_file_path: str,
    ) -> pl.LazyFrame:
        """zipped csv to polars df.

        :param zip_file_path:
        :return: pl.LazyFrame
        """
        os.makedirs(self.tmp_dir, exist_ok=True)
        try:
            with ZipFile(zip_file_path, "r") as z:
                z.extractall(self.tmp_dir)

            csv_files = [os.path.join(self.tmp_dir, file) for file in os.listdir(self.tmp_dir)]
            if not csv_files:
                raise ValueError(f"No CSV files found in the zip archive. {zip_file_path}")

            df = pl.scan_csv(csv_files[0])
            logger.info("Zipped DataFrame loaded successfully:")

        except Exception as e:
            logger.exception(e)
            raise

        return df

    def _update_batch(self, current_batch: List[pl.DataFrame], batch_index: int) -> str:
        batch_df = pl.concat(current_batch)
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
        batch_path = (
            f"{DataLakeFileTemplate.STRUCTURED_REMOTE_LOANS.format(date=self.date)}"
            f"/batch_{batch_index}_{timestamp}.parquet"
        )
        local_file = f"/tmp/batch_{batch_index}.parquet"
        batch_df.write_parquet(local_file)
        logger.info(f"Writing batched file to {batch_path}")
        with open(local_file, "rb") as f:
            self.s3_client.put_object(
                Bucket=settings.s3_bucket.lstrip("s3://"),
                Key=batch_path.lstrip("/"),
                Body=f,
                ACL="bucket-owner-full-control",
            )

        os.remove(local_file)

        return batch_path

    def _split_df_by_identifier(self, frame: pl.DataFrame, ihs_type: IHSType):
        """Splits the DataFrame by RIC, processes each group, and creates files
        for upload.

        :param frame: The input DataFrame to process
        :param ihs_type
        :return:
        """
        batch_s3_paths = []
        current_batch = []
        batch_index = 0

        for ric, group in frame.group_by(["#RIC", "Date"]):
            current_batch.append(group)

            if len(current_batch) == settings.batch_size:
                batch_path = self._update_batch(current_batch, batch_index)
                batch_s3_paths.append(batch_path)
                current_batch = []
                batch_index += 1

        if current_batch:  # last batch
            batch_path = self._update_batch(current_batch, batch_index)
            batch_s3_paths.append(batch_path)

        return batch_s3_paths

    def _normalise_loans(self, filepath: str) -> pl.LazyFrame:
        lazy_df = pl.scan_csv(filepath)

        if not self.validate_ihs_df(
            df=lazy_df, columns=LoansColumns().get_columns(), file_type="Loans"
        ):
            return pl.LazyFrame()

        result = lazy_df.select(
            [
                pl.col(LoansColumns.LOAN_X_ID).alias(ParquetLoansColumns.RIC),
                pl.col(LoansColumns.CLOSE_DATE).alias(ParquetLoansColumns.DATE),
                pl.col(LoansColumns.EVALUATED_PRICE).alias(ParquetLoansColumns.CLOSE_PRICE),
                pl.col(LoansColumns.CLOSE_OFFER).alias(ParquetLoansColumns.CLOSE_ASK_PRICE),
                pl.col(LoansColumns.CLOSE_BID).alias(ParquetLoansColumns.CLOSE_BID_PRICE),
            ]
        )

        # Fetch unique RICs
        rics = (
            lazy_df.select(pl.col(LoansColumns.LOAN_X_ID))
            .unique()
            .collect()[LoansColumns.LOAN_X_ID]
            .to_list()
        )

        currency_dfs = []
        for i in range(0, len(rics), settings.ric_currency_batch_size):
            chunk = rics[i : i + settings.ric_currency_batch_size]
            currency_data = self.rics_client.bulk_get_currencies(chunk).content
            currency_dfs.append(pl.DataFrame(currency_data))

        currency_df = pl.concat(currency_dfs, how="diagonal")

        # Convert to LazyFrame for joining
        currency_lf = currency_df.lazy()

        # Join and rename
        result = result.join(
            currency_lf, left_on=ParquetLoansColumns.RIC, right_on=RicColumns.RIC, how="left"
        ).rename({RicColumns.CURRENCY_RAW: ParquetLoansColumns.CURRENCY})

        # Ensure all required columns exist
        for column in REQUIRED_STATS_COLUMNS:
            if column not in result.collect_schema().names():
                result = result.with_columns(pl.lit(None).alias(column))

        result = self._convert_dtype(result)

        return result

    def _normalise_loans_delta(self, filepath: str) -> pl.LazyFrame:
        lazy_df = pl.scan_csv(filepath)

        if not self.validate_ihs_df(
            df=lazy_df, columns=LoansDeltaColumns().get_columns(), file_type="Loans Delta"
        ):
            return pl.LazyFrame()

        lookup_cols = LoansDeltaColumnsRicLookup().get_columns()

        result = lazy_df.select(
            [
                pl.col(LoansDeltaColumns.LOAN_X_ID).alias(
                    LoansDeltaColumnsRicLookup.INSTRUMENT_UNIQUE_IDENTIFIER
                ),
                pl.col(LoansDeltaColumns.LOAN_X_ID).alias(LoansDeltaColumnsRicLookup.RIC),
                pl.col(LoansDeltaColumns.LOAN_X_ID).alias(LoansDeltaColumnsRicLookup.PREFERRED_RIC),
                pl.lit("XXX").alias(LoansDeltaColumnsRicLookup.INSTRUMENT_LIST_ID),
                pl.lit("LLXXXX").alias(LoansDeltaColumnsRicLookup.CFI_CODE),
                pl.col(LoansDeltaColumns.CURRENCY).alias(
                    LoansDeltaColumnsRicLookup.PREFERRED_RIC_CURRENCY_RAW
                ),
                pl.col(LoansDeltaColumns.CURRENCY).alias(
                    LoansDeltaColumnsRicLookup.PREFERRED_RIC_CURRENCY
                ),
            ]
        )

        present_cols = set(result.collect_schema())
        missing_cols = [col for col in lookup_cols if col not in present_cols]
        if missing_cols:
            result = result.with_columns([pl.lit(None).alias(col) for col in missing_cols])

        result = self._convert_dtype(result)

        return result

    @staticmethod
    def validate_ihs_df(df: pl.LazyFrame, columns: List[Optional[str]], file_type: str) -> bool:
        """

        :param df:
        :param columns
        :param file_type
        :return: bool
        """
        try:
            df_schema = set(df.collect_schema().keys())
            if not set(columns).issubset(df_schema):
                logger.warning(
                    f"Columns missing in {file_type} file. Expected: {columns}, Found: {df_schema}"
                )
                return False
            return True
        except Exception as e:
            logger.warning(f"Failed to validate {file_type} file columns due to: {e}")
            return False

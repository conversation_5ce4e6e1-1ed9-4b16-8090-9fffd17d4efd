import fsspec
import logging
import pandas as pd
from aries_io_event.io_param import IOParamFieldSet
from aries_se_api_client.client import AriesApiClient
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from efdh_tasks.eod_parquet_converter.static import SE_REFERENCE_API_URL
from efdh_tasks.map_ric.assign_asset_class import AssignInstrumentAssetClass
from efdh_tasks.map_ric.dss_api_map_ric import DssApiMapRic
from efdh_tasks.map_ric.input_schema import MapRicInput
from efdh_tasks.map_ric.update_instrument_list import UpdateInstrumentList, UpdateLevel2Instruments
from reference_api_client.instrument_list_monitoring import ILM

logger = logging.getLogger(__name__)


class MapRicTask:
    def __init__(
        self,
        aries_task_input: AriesTaskInput,
    ):
        self.aries_task_input = aries_task_input
        self.aries_task_input_params = MapRicInput.validate(aries_task_input.input_param.params)
        self.s3_batch_url = self.aries_task_input_params.instrument_file_uri
        self.fs = fsspec.filesystem("s3")
        self.ilm = ILM(client=AriesApiClient(host=SE_REFERENCE_API_URL))

    def execute(self) -> AriesTaskResult:
        df = pd.read_csv(
            self.s3_batch_url,
            sep=",",
            encoding="utf-8",
        )

        self.aries_task_input.input_param.params["input_instruments_count"] = len(
            df["key"].to_list()
        )

        instrument_asset_class = AssignInstrumentAssetClass()
        asset_mapped_df = instrument_asset_class.process(df=df)

        dss = DssApiMapRic()
        ric_mapped_df = dss.execute(frame=asset_mapped_df)

        update_instrument_list = UpdateInstrumentList()
        updated_instrument_list_df = update_instrument_list.execute(df=ric_mapped_df)

        update_l2_instruments = UpdateLevel2Instruments()
        db_updates = update_l2_instruments.execute(df=updated_instrument_list_df)

        self.aries_task_input.input_param.params["unmapped_instruments_count"] = db_updates.get(
            "unmapped_instruments"
        )
        self.aries_task_input.input_param.params["rics"] = db_updates.get("mapped_rics", [])

        output_params = IOParamFieldSet(**self.aries_task_input.input_param.dict())
        return AriesTaskResult(output_param=output_params)

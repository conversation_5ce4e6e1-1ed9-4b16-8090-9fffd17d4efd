import logging
import polars as pl
from aries_se_api_client.client import AriesApiClient
from efdh_tasks.obd_parquet_converter.obd_parquet_converter_config import (
    obd_parquet_converter_settings as settings,
)
from efdh_tasks.obd_parquet_converter.static import (
    CSV_READ_COLUMNS,
    FileTemplates,
    ResamplingFormats,
    Resolution,
)
from market_data_utils.schema.currency import CurrencyMappings
from market_data_utils.schema.parquet import QuoteCurrencyColumn
from market_data_utils.schema.refinitiv import (
    OrderBookDepthPriceColumns,
    RefinitivOrderBookDepthColumns,
)
from pendulum import Date
from reference_api_client.ric import Ric as RicClient
from se_db_utils.database import Database
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)


class NormaliseOBD:
    """Transforms raw RIC csv file data to parquet and normalises columns and
    rows."""

    def __init__(
        self,
        ric: str,
        date: Date,
        input_filepath: str,
        local_destination: str,
        remote_destination: str,
        bucket: str,
        currency: Optional[str] = None,
    ):
        logger.info(f"NormaliseOBDStats args: {ric} | {input_filepath} | {local_destination}")
        self.ric = ric
        self.currency = currency
        self.date = date
        self.bucket = bucket
        self.input_filepath = input_filepath
        self.local_destination = local_destination
        self.remote_destination = remote_destination
        self.ric_api = RicClient(client=AriesApiClient(host=settings.SE_REFERENCE_API_URL))
        self.db = Database(db_url=settings.MASTER_DATA_SE_REF_DB_URL)

    def convert_to_parquet(
        self,
    ) -> Optional[List[Dict[str, str]]]:
        outputs: List[Dict[str, str]] = []

        if not self.currency:
            # call the reference data api
            try:
                self.currency = self.ric_api.get_currency(ric=self.ric).get("currencyRaw")
                if not self.currency:
                    logger.warning(f"Currency not found for RIC: {self.ric}")

            except Exception as e:
                logger.warning(f"get_currency failed with error: {str(e)}")
                logger.warning(f"Currency not found for RIC: {self.ric}")

        # read OBD CSV file
        df = pl.read_csv(
            self.input_filepath,
            columns=list(CSV_READ_COLUMNS.keys()),
            dtypes=CSV_READ_COLUMNS,  # type: ignore
        )

        # convert Date-Time to epoch ns
        df = df.with_columns(pl.col("Date-Time").dt.epoch(time_unit="ns"))

        row_count = df.shape[0]

        logger.info(f"Number of Rows pre-dropping L1 Price NaN's: {row_count}")
        # drop na
        price_predicate = df.filter(
            pl.col(RefinitivOrderBookDepthColumns.L1_ASK_PRICE).is_not_null()
            & pl.col(RefinitivOrderBookDepthColumns.L1_BID_PRICE).is_not_null()
            & pl.col(RefinitivOrderBookDepthColumns.DATE_TIME).is_not_null()
        )

        df = df.select(price_predicate).unique(
            keep="last", subset=[RefinitivOrderBookDepthColumns.DATE_TIME]
        )

        new_row_count = df.shape[0]

        logger.info(
            f"Row count post dropping L1 Ask/Bid Price NaN's: {new_row_count}. "
            f"Difference: {row_count - new_row_count}"
        )

        if df.is_empty():
            logger.warning(
                f"OBD Data empty. Skipping flow for RIC: {self.ric} for DATE: {self.date}"
                f" for KEY: {self.input_filepath}"
            )
            return None

        # normalise - Korean bonds
        # if ric_ccy in ["KRW", "BRL"]:
        #     es_client = get_repository_by_cluster_version(resource_config=RESOURCE_CONFIG)
        #
        #     frame = self._bonds_transformation(
        #         frame=df.to_pandas(),
        #         base_ccy=ric_ccy,
        #         cols=OrderBookDepthPriceColumns().get_columns(),
        #         es_client=es_client,
        #         logger=logger,
        #     )
        #     df = pl.from_pandas(frame)

        # normalise - minor to major currency
        ric_ccy_final = self.currency
        if self.currency:
            if self.currency in CurrencyMappings.MINOR_TO_MAJOR_CCY:
                logger.info(f"Normalising {self.ric}")
                price_cols = (
                    df.select(OrderBookDepthPriceColumns().get_columns()) / 100
                )  # normalise prices
                df = df.with_columns(price_cols)

            ric_ccy_final = CurrencyMappings.MINOR_TO_MAJOR_CCY.get(self.currency, self.currency)

        df = df.with_columns(pl.lit(ric_ccy_final).alias(QuoteCurrencyColumn.QUOTE_PRICE_CURRENCY))

        # stable sort by time
        df = df.sort(by=[RefinitivOrderBookDepthColumns.DATE_TIME])

        parquet_file_name = FileTemplates.PARQUET_DAILY_FILE_NAME_FORMAT.format(
            ric=self.ric,
            date=self.date.format(FileTemplates.PARQUET_FILE_DATE_FORMAT),
            resolution="TICK",
        )
        parquet_path = self.local_destination + f"{parquet_file_name}"
        remote_destination_parquet_path = self.remote_destination + f"{parquet_file_name}"

        self._write_parquet(df, str(parquet_path))
        outputs.append(
            {
                "local_source": str(parquet_path),
                "remote_destination": str(remote_destination_parquet_path),
            }
        )

        for resolution, resampling_nanoseconds in [
            (Resolution.MILLISECOND, ResamplingFormats.MILLISECONDS_RESAMPLING_NS),
            (Resolution.SECOND, ResamplingFormats.SECONDS_RESAMPLING_NS),
            (Resolution.MINUTE, ResamplingFormats.MINUTE_RESAMPLING_NS),
        ]:
            clo = self._write_resampled_parquet(
                df=df,
                date=self.date,
                parquet_convert_dir=self.local_destination,
                remote_destination_dir=self.remote_destination,
                resolution=resolution,
                resampling_nanoseconds=resampling_nanoseconds,
            )
            outputs.append(clo)

        return outputs

    def _write_resampled_parquet(
        self,
        df: pl.DataFrame,
        date: Date,
        parquet_convert_dir: str,
        remote_destination_dir: str,
        resolution: str,
        resampling_nanoseconds: int,
    ) -> dict:
        """Uses a groupBy on a column resampled by resampling_seconds. Pandas
        `resample` is not used, because it creates a row for each timestamp in
        the time range, which quickly goes OOM. Using groupby allows dropping
        nulls during groupby operation, which keeps the memory manageable.

        The resampling is done as per these examples - MILLISECOND ->latest
        timestamp row in range (12:34:55.122, 12:34:56.123] is considered for
        timestamp label 12:34:56.123 SECOND -> latest timestamp row in range
        (12:34:55, 12:34:56] is considered for timestamp label 12:34:56 MINUTE
        -> latest timestamp row in range (12:33, 12:34] is considered for
        timestamp label 12.34.

        :param df:
        :param ric:
        :param date:
        :param parquet_convert_dir:
        :param remote_destination_dir:
        :param resolution:
        :param resampling_nanoseconds:
        :return:
        """
        parquet_file_name = FileTemplates.PARQUET_DAILY_FILE_NAME_FORMAT.format(
            ric=self.ric,
            date=date.format(FileTemplates.PARQUET_FILE_DATE_FORMAT),
            resolution=resolution,
        )
        parquet_path = parquet_convert_dir + f"{parquet_file_name}"
        remote_destination_ms_parquet_path = remote_destination_dir + f"{parquet_file_name}"

        df = df.with_columns(
            pl.when(
                (pl.col(RefinitivOrderBookDepthColumns.DATE_TIME) % resampling_nanoseconds) == 0
            )
            .then(pl.col(RefinitivOrderBookDepthColumns.DATE_TIME))
            .otherwise(
                pl.col(RefinitivOrderBookDepthColumns.DATE_TIME)
                // resampling_nanoseconds
                * resampling_nanoseconds
                + resampling_nanoseconds
            )
            .alias(RefinitivOrderBookDepthColumns.DATE_TIME_RESAMPLED)
        )

        resampled_grp = df.group_by(RefinitivOrderBookDepthColumns.DATE_TIME_RESAMPLED)

        resampled_df = (
            resampled_grp.agg([pl.col("*").last(), pl.count()])
            .sort(RefinitivOrderBookDepthColumns.DATE_TIME_RESAMPLED)
            .rename({"count": "Count"})
        )
        self._write_parquet(resampled_df, str(parquet_path))

        # drop sampling column from original df
        df.drop(RefinitivOrderBookDepthColumns.DATE_TIME_RESAMPLED)

        return {
            "local_source": str(parquet_path),
            "remote_destination": str(remote_destination_ms_parquet_path),
        }

    def _write_parquet(self, df: pl.DataFrame, parquet_path: str):
        """Writes to parquet, after converting Date-Time column to int64 UTC
        epoch nanoseconds.

        :param df:
        :param parquet_path:
        :return:
        """
        logger.info(f"Writing parquet {parquet_path}")
        df.write_parquet(parquet_path, use_pyarrow=True)

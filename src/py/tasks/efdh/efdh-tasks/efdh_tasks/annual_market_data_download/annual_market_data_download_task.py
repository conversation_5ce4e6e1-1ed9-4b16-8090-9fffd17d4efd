import backoff
import fsspec
import gzip
import httpx
import logging
import math
import nanoid
import numpy as np
import polars as pl
import shutil
from aries_io_event.io_param import IOParamFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from efdh_tasks.annual_market_data_download.config import settings
from efdh_tasks.annual_market_data_download.input_schema import AnnualMarketDataDownloadInput
from efdh_tasks.annual_market_data_download.static import S3_OUTPUT_PATH_TMPL
from efdh_utils.schema.parquet import EoDStatsColumns
from efdh_utils.schema.refinitiv import ElektronExtractColumns, InstrumentListType
from itertools import chain, repeat
from pathlib import Path
from refinitiv_utils.static import (
    CONTENT_FIELD_NAMES_MAP,
    ELEKTRON_EOD_MAP,
    MappingIdentifierType,
    RefinitivProduct,
)
from refinitiv_utils.utils import RefinitivClient
from s3fs import S3FileSystem
from se_io_utils.tempfile_utils import tmp_directory
from typing import Any, Dict, Iterable, Iterator, List, NoReturn, Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AnnualMarketDataDownloadTask:
    def __init__(self, aries_task_input: AriesTaskInput):
        self.input_params = AnnualMarketDataDownloadInput.validate(
            aries_task_input.input_param.params
        )
        self.fs = fsspec.filesystem("s3")
        self.start_datetime: datetime = self.input_params.start_datetime  # type: ignore
        self.end_datetime: datetime = self.input_params.end_datetime  # type: ignore
        self.instrument_list_type: InstrumentListType = self.input_params.instrument_list_type
        self.identifier_type: MappingIdentifierType = self.input_params.identifier_type
        self.target_dir: Path = tmp_directory()
        self.refinitiv_client = RefinitivClient(
            level_1=(self.instrument_list_type != InstrumentListType.OBD),
            eod=False,  # use old elektron - so we use l1 account
        )
        self.identifiers = (
            self.get_identifiers(self.fs, self.input_params.identifier_file_uri)
            if self.input_params.identifier_file_uri
            else self.input_params.identifiers
        )
        self._id = nanoid.generate()

    def make_filepath(self) -> Path:
        filename = (
            f"{self.instrument_list_type.value}_{self.start_datetime.strftime('%Y%m%d')}-"
            f"{self.end_datetime.strftime('%Y%m%d')}_{nanoid.generate()}.csv.gz"
        )
        filepath = self.target_dir.joinpath(str(self._id), filename)
        filepath.parent.mkdir(exist_ok=True, parents=True)
        return filepath

    def execute(self):
        logger.info(
            "Annual Market Data Download engaged\n "
            f"IdentifierType : {self.input_params.identifier_type.value} \n "
            f"InstrumentListType : {self.input_params.instrument_list_type.value} \n "
            f"Start Date : {self.start_datetime} \n "
            f"End Date : {self.end_datetime} "
        )
        file_uris = []
        if (
            self.input_params.instrument_list_type == InstrumentListType.TICK
            and not settings.TICK_NON_RIC_INGESTION
            and self.input_params.identifier_type != MappingIdentifierType.RIC
        ):
            return AriesTaskResult(output_param=None)

        with ThreadPoolExecutor(max_workers=settings.MAX_THREADS) as executor:
            batch_size = (
                settings.BATCH_SIZE
                if self.input_params.instrument_list_type == InstrumentListType.EOD
                else settings.OBD_TICK_BATCH_SIZE
            )

            split = int(math.ceil(len(self.identifiers) / batch_size))  # type: ignore
            results = executor.map(
                self.execute_batch,
                np.array_split(np.array(self.identifiers), split),
                repeat(self.make_filepath()),
            )
            for result in results:
                if result:
                    file_uris.append(result)

        shutil.rmtree(self.target_dir)

        if not file_uris:
            logger.warning("No files created, job disengaged.")
            return AriesTaskResult(output_param=None)

        output_params = {
            "file_uris": file_uris,
            "start_datetime": str(self.start_datetime),
            "end_datetime": str(self.end_datetime),
        }
        return AriesTaskResult(output_param=IOParamFieldSet(params=output_params))

    @staticmethod
    def get_identifiers(fs: S3FileSystem, s3_url: str) -> List[str]:
        """
        :param fs:
        :param s3_url:
        :yield: List of rics
        """
        rics = []
        with fs.open(s3_url, "r") as file:
            for line in file:
                if line := line.strip():
                    rics.append(line)

        return rics

    def execute_batch(self, ric_batch: List[str], filepath: Path) -> Optional[str]:
        """Validating Task Input parameters.

        :return: Task input parameters
        """
        logger.info(
            f"downloading {self.instrument_list_type} data for {len(ric_batch)} rics from "
            f"`{self.start_datetime}` to `{self.end_datetime}`"
        )
        data = None
        match self.instrument_list_type:
            case InstrumentListType.TICK:
                data = self.fetch_tick(ric_batch=ric_batch)
            case InstrumentListType.OBD:
                data = self.fetch_obd(ric_batch=ric_batch)
            case InstrumentListType.EOD:
                self.fetch_eod(
                    ric_batch=ric_batch, filepath=filepath, identifier_type=self.identifier_type
                )
            case _:
                raise Exception(
                    f"Illegal value for instrument list type: {self.instrument_list_type.value}"
                )

        if data is not None:
            logger.info(f"write market data dataframe to targets directory: {filepath}")
            self.write_output(data=data, filepath=filepath)

        if not filepath.exists() or filepath.stat().st_size == 0:
            logger.warning(
                f"Empty or missing file for "
                f"{len(ric_batch)} rics {self.start_datetime} - {self.end_datetime}"
            )
            return None

        remote_destination = S3_OUTPUT_PATH_TMPL.format(
            bucket=settings.S3_BUCKET.replace("s3://", ""), _id=self._id, filename=filepath.name
        )
        self.fs.put_file(filepath, remote_destination)

        logger.info(f"file placed in {remote_destination}")

        return remote_destination

    def fetch_eod(
        self, ric_batch: List[str], filepath: Path, identifier_type: str = MappingIdentifierType.RIC
    ) -> None:
        try:
            files: dict[RefinitivProduct, Path] = {}
            for refinitiv_product in [RefinitivProduct.ELEKTRON, RefinitivProduct.EOD_PRICING]:
                files[refinitiv_product] = self.extract_eod(
                    refinitiv_product=refinitiv_product,
                    ric_batch=ric_batch,
                    identifier_type=identifier_type,
                )
            self.write_eod_data(files, filepath=filepath)

        except Exception:
            error = (
                f"failed to download eod data from "
                f"{self.start_datetime} to {self.end_datetime}"
                f" for {len(ric_batch)} rics"
            )
            logger.exception(error, exc_info=True)

    def fetch_tick(self, ric_batch: List[str]) -> Optional[Iterator[Any]]:
        try:
            data = self.refinitiv_client.extract_raw(
                rics=ric_batch, start_date=self.start_datetime, end_date=self.end_datetime
            )
            logger.info(
                f"Downloaded tick data for {len(ric_batch)} ric from "
                f"`{self.start_datetime}` to `{self.end_datetime}`"
            )
            return data
        except Exception:
            error = (
                f"failed to download tick data from "
                f"{self.start_datetime} to {self.end_datetime}"
                f" for {len(ric_batch)} rics"
            )
            logger.exception(error, exc_info=True)
            return None

    def fetch_obd(self, ric_batch: List[str]) -> Optional[Iterator[Any]]:
        try:
            job_id = self.refinitiv_client.request_market_depth_raw(
                rics=ric_batch, start_date=self.start_datetime, end_date=self.end_datetime
            )
            data = self.refinitiv_client.download_market_depth_raw(job_id)
            return data
        except Exception:
            error = (
                f"failed to download obd data from "
                f"{self.start_datetime} to {self.end_datetime}"
                f" for {len(ric_batch)} rics"
            )
            logger.exception(error, exc_info=True)
            return None

    @backoff.on_exception(backoff.expo, exception=httpx.HTTPError, max_tries=5)
    def validate_rics(self, rics: List[str]) -> List[str]:
        return self.refinitiv_client.bulk_is_valid_ric(rics)

    def write_output(self, data: Optional[Iterable], filepath: Path) -> NoReturn:
        with open(filepath.as_posix(), "wb") as fh:
            for chunk in data:
                fh.write(chunk)

    def extract_eod(
        self,
        refinitiv_product: RefinitivProduct,
        ric_batch: List[str],
        identifier_type=MappingIdentifierType.RIC,
    ) -> Optional[Path]:
        client = (
            self.refinitiv_client
            if refinitiv_product == RefinitivProduct.ELEKTRON
            else RefinitivClient(level_1=True, eod=True)
        )
        try:
            data = client.extract_eod(
                rics=ric_batch,
                query_start_date=self.start_datetime,
                query_end_date=self.end_datetime,
                refinitiv_product=refinitiv_product,
                identifier_type=identifier_type,
            )
            first_chunk = next(data, None)
            if first_chunk is None:
                return None
            data = chain([first_chunk], data)
        except ValueError:
            return None

        logger.info(
            f"Downloaded eod data for `{len(ric_batch)}` rics from "
            f"`{self.start_datetime}` to `{self.end_datetime}`"
        )
        tempfile_id = nanoid.generate() + f"_{refinitiv_product.value.lower()}"
        filepath: Path = self.target_dir / tempfile_id
        with open(filepath, "wb+") as f:
            for chunk in data:
                f.write(chunk)

        return filepath

    def write_eod_data(self, files: Dict[RefinitivProduct, Path], filepath: Path) -> NoReturn:
        dfs = []
        for refinitiv_product, fp in files.items():
            if fp is None:
                continue
            csv_schema = {col: pl.Utf8 for col in CONTENT_FIELD_NAMES_MAP[refinitiv_product]}
            partial_df = pl.read_csv(fp, schema=csv_schema, low_memory=True)

            if refinitiv_product == RefinitivProduct.ELEKTRON:
                partial_df = partial_df.rename(ELEKTRON_EOD_MAP)
                partial_df = partial_df.with_columns(
                    pl.col(EoDStatsColumns.VWAP)
                    .fill_null(pl.col(ElektronExtractColumns.MARKET_VWAP))
                    .alias(ElektronExtractColumns.VWAP)
                )
                partial_df = partial_df.drop(
                    [EoDStatsColumns.VWAP, ElektronExtractColumns.MARKET_VWAP]
                )

            partial_df = partial_df.with_columns(
                pl.col(ElektronExtractColumns.TRADE_DATE)
                .str.to_datetime(format="%Y/%m/%d", strict=False)
                .dt.strftime("%d/%m/%Y")
                .name.keep()
            )
            dfs.append(partial_df)

        if not dfs:
            return None

        df: pl.DataFrame = pl.concat(dfs, how="diagonal")
        df = df.unique(
            subset=[
                ElektronExtractColumns.RIC,
                ElektronExtractColumns.ORC,
                ElektronExtractColumns.TRADE_DATE,
            ],
            keep="last",
        )
        tmp_csv = self.target_dir / nanoid.generate()
        df.write_csv(tmp_csv)
        with open(tmp_csv, "rb") as raw_csv_data, gzip.open(filepath.as_posix(), "wb") as fh:
            for line in raw_csv_data:
                fh.write(line)

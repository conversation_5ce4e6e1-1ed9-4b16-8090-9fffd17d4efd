from datetime import datetime, timedelta
from efdh_tasks.annual_market_data_download.config import settings
from efdh_utils.schema.refinitiv import InstrumentListType
from pydantic import root_validator, validator
from pydantic.fields import Field
from pydantic.main import BaseModel
from refinitiv_utils.static import MappingIdentifierType
from typing import List, Optional


class AnnualMarketDataDownloadInput(BaseModel):
    identifiers: Optional[List[str]] = Field(None, description="RICs to be downloaded")
    identifier_file_uri: Optional[str] = Field(
        None, description="URI containing a txt file which has rics delimited by \n"
    )
    instrument_list_type: InstrumentListType = Field(description="type of data to download")
    start_datetime: Optional[datetime] = Field(
        None,
        description="start of date range",
    )
    identifier_type: MappingIdentifierType = Field(
        MappingIdentifierType.RIC,
        description="Type of identifier we use to request refinitiv for data with",
    )
    end_datetime: Optional[datetime] = Field(None, description="end of date range")
    append_data: Optional[bool] = Field(
        None, description="add to existing data (defaults to True if null or missing)"
    )

    @root_validator(pre=True)
    def set_default_append_data(cls, values):
        if values.get("append_data") is None:
            values["append_data"] = True
        return values

    @root_validator(pre=True)
    def fill_datetimes(cls, values):
        instrument_list_type = values.get("instrument_list_type")
        backload_lookback_var = f"{instrument_list_type}_LOOKBACK"
        lookback_period = getattr(settings, backload_lookback_var)

        default_start_datetime = datetime.now() - timedelta(days=lookback_period)

        if values.get("start_datetime") is None:
            values["start_datetime"] = default_start_datetime

        if values.get("end_datetime") is None:
            values["end_datetime"] = datetime.now()

        return values

    @validator("end_datetime")
    def validate_end_date(cls, value: datetime, values) -> datetime:
        start_datetime = values.get("start_datetime")
        if value <= start_datetime:
            raise ValueError("End datetime must be greater than the start datetime.")
        return value

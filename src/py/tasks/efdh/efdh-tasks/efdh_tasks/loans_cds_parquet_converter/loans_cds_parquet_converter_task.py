import boto3
import concurrent
import io
import logging
import polars as pl
import pyarrow as pa
import pyarrow.parquet as pq
import sys
from aries_io_event.io_param import IOParamFieldSet
from aries_se_api_client.client import AriesApiClient
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from collections import defaultdict
from datetime import datetime as dt
from datetime import timed<PERSON>ta
from efdh_tasks.loans_cds_parquet_converter.loans_cds_parquet_converter_config import (
    loans_cds_settings as settings,
)
from efdh_tasks.loans_cds_parquet_converter.static import DataLakeFileTemplate
from market_data_utils.schema.parquet import EoDStatsColumns, ParquetCDSColumns, ParquetLoansColumns
from reference_api_client.instrument_list_monitoring import ILM
from typing import Any

logger = logging.getLogger(__name__)

ARROW_SCHEMA = pa.schema(
    [
        pa.field(ParquetCDSColumns.RIC, pa.string()),
        pa.field(ParquetCDSColumns.SENIORITY, pa.string()),
        pa.field(ParquetCDSColumns.RED_CODE, pa.string()),
        pa.field(ParquetCDSColumns.DATE, pa.string()),
        pa.field(ParquetCDSColumns.CLOSE_PRICE, pa.float64()),
        pa.field(ParquetCDSColumns.CLOSE_ASK_PRICE, pa.float64()),
        pa.field(ParquetCDSColumns.CLOSE_BID_PRICE, pa.float64()),
        pa.field(ParquetCDSColumns.CURRENCY, pa.string()),
        pa.field(ParquetCDSColumns.TENOR, pa.string()),
        pa.field(EoDStatsColumns.HIGH_PRICE, pa.float64()),
        pa.field(EoDStatsColumns.EXCHANGE_CODE, pa.float64()),
        pa.field(EoDStatsColumns.HIGH_ASK_PRICE, pa.float64()),
        pa.field(EoDStatsColumns.HIGH_BID_PRICE, pa.float64()),
        pa.field(EoDStatsColumns.LOW_PRICE, pa.float64()),
        pa.field(EoDStatsColumns.LOW_ASK_PRICE, pa.float64()),
        pa.field(EoDStatsColumns.LOW_BID_PRICE, pa.float64()),
        pa.field(EoDStatsColumns.OPEN_ASK_PRICE, pa.float64()),
        pa.field(EoDStatsColumns.OPEN_BID_PRICE, pa.float64()),
        pa.field(EoDStatsColumns.OPEN_INTEREST, pa.float64()),
        pa.field(EoDStatsColumns.OPEN_PRICE, pa.float64()),
        pa.field(EoDStatsColumns.TRADE_VOLUME, pa.float64()),
        pa.field(EoDStatsColumns.VWAP, pa.float64()),
        pa.field(EoDStatsColumns.DAILY_TRADED_NOTIONAL, pa.float64()),
        pa.field(EoDStatsColumns.VOLUME_EMA, pa.float64()),
        pa.field(EoDStatsColumns.JPY, pa.float64()),
        pa.field(EoDStatsColumns.EUR, pa.float64()),
        pa.field(EoDStatsColumns.GBP, pa.float64()),
        pa.field(EoDStatsColumns.CHF, pa.float64()),
        pa.field(EoDStatsColumns.USD, pa.float64()),
        pa.field(EoDStatsColumns.AUD, pa.float64()),
        pa.field(EoDStatsColumns.SGD, pa.float64()),
    ]
)


class LoansCDSParquetConverter:
    def __init__(
        self,
        aries_task_input: AriesTaskInput,
    ):
        self.aries_task_input = aries_task_input
        self.aries_task_input_param = aries_task_input.input_param.params
        self.ilm_id = self.aries_task_input_param["ilm_id"]
        self.s3_batch_path = self.aries_task_input_param["batch_file_uri"]
        self.ref_api_client = AriesApiClient(host=settings.se_reference_api_url)
        self.ilm = ILM(client=self.ref_api_client)
        self.s3_client = boto3.client("s3")

    def execute(self):
        arrow_table = pq.read_table(settings.s3_bucket + self.s3_batch_path, schema=ARROW_SCHEMA)
        frame = pl.from_arrow(arrow_table)
        futures = []
        processed_ric = defaultdict(list)
        with concurrent.futures.ThreadPoolExecutor(max_workers=settings.max_workers) as executor:
            for ric, group in frame.group_by("#RIC"):
                future = executor.submit(
                    self._process_group,
                    ric=ric[0],
                    group=group,
                    remote_template=DataLakeFileTemplate.CURATED_REMOTE,
                )
                futures.append(future)

        concurrent.futures.wait(fs=futures)
        for future in futures:
            result = future.result()
            if isinstance(result.get("exception"), Exception):
                processed_ric["rics_failed"].append(result.get("ric", ""))
            else:
                processed_ric["rics_processed"].append(result.get("ric", ""))

        self.aries_task_input.input_param.params["successful_rics"] = processed_ric.get(
            "rics_processed", []
        )
        self.aries_task_input.input_param.params["rics_failed"] = processed_ric.get(
            "rics_failed", []
        )

        if processed_ric.get("rics_failed", []):
            self.ilm.set_failed(id=self.ilm_id)
        else:
            self.ilm.increment_batches(self.ilm_id)

        output_params = IOParamFieldSet(**self.aries_task_input.input_param.dict())
        return AriesTaskResult(output_param=output_params)

    @staticmethod
    def _merge_existing(frame: pl.DataFrame, remote_destination: str) -> pl.DataFrame:
        """

        :param frame:
        :param remote_destination:
        :return:
        """
        try:
            existing_df = pl.read_parquet(remote_destination)
            frame = pl.concat([existing_df, frame], how="diagonal")
            frame = frame.unique(subset=[EoDStatsColumns.DATE], keep="last").sort(
                by=[EoDStatsColumns.DATE]
            )
        except Exception:
            logger.warning(f"No existing file found for {remote_destination}")

        return frame

    def _write_yearly_files(
        self,
        ric: str,
        frame: pl.DataFrame,
        remote_template: str,
    ):
        """Split the DataFrame into yearly chunks, write each chunk directly to
        S3, and return metadata for the uploaded files.

        :param ric: Instrument identifier
        :param frame: The DataFrame to split and upload
        :param remote_template: Template for remote file paths
        :return:
        """

        frame = frame.with_columns(
            [
                pl.col(EoDStatsColumns.DATE).dt.year().alias("year"),
                pl.col(EoDStatsColumns.DATE).cast(pl.Utf8),
            ]
        )

        for year, group in frame.group_by("year"):
            year = year[0]  # type: ignore
            group = group.drop(["year"])
            file_name = DataLakeFileTemplate.REMOTE_FILENAME_YEARLY.format(
                IDENTIFIER=ric, year=year
            )
            remote_path = f"{remote_template.format(IDENTIFIER=ric)}{file_name}"

            group = self._merge_existing(
                frame=group, remote_destination=f"{settings.s3_bucket}{remote_path}"
            )

            arrow_table = self._get_arrow_table(group)

            buffer = io.BytesIO()
            pq.write_table(arrow_table, buffer)
            buffer.seek(0)

            # Upload the file directly to S3
            self.s3_client.put_object(
                Bucket=settings.s3_bucket.lstrip("s3://"),
                Key=remote_path.lstrip("/"),
                Body=buffer.read(),
                ACL="bucket-owner-full-control",
            )
            logger.info(f"written file {remote_path}")

    def _get_arrow_table(self, group: pl.DataFrame) -> pa.Table:
        """
        Converts a Polars DataFrame to a PyArrow Table with a schema
        that is filtered and reordered based on the available fields
        in the predefined ARROW_SCHEMA.

        :param group (pl.DataFrame): The Polars DataFrame to convert.

        :return pa.Table: A PyArrow Table cast to the filtered and ordered schema.
        """
        arrow_table = group.to_arrow()

        # Reorder the available fields to match the arrow_table column order
        available_fields = [
            field for field in ARROW_SCHEMA if field.name in arrow_table.column_names
        ]
        reordered_fields = sorted(
            available_fields, key=lambda field: arrow_table.column_names.index(field.name)
        )

        # Create a new schema with the reordered fields
        schema = pa.schema(reordered_fields)
        arrow_table = arrow_table.cast(schema)

        return arrow_table

    def _process_group(self, ric: Any, group: pl.DataFrame, remote_template: str):
        """Processes a individual RIC group, called in multiprocessing pool.

        :param ric:
        :param group:
        :param remote_template:
        :return:
        """
        try:
            file_name = DataLakeFileTemplate.REMOTE_FILENAME.format(IDENTIFIER=ric)
            remote_base_path = remote_template.format(IDENTIFIER=ric)
            remote_path = f"{settings.s3_bucket}{remote_base_path}{file_name}"

            group = self._merge_existing(frame=group, remote_destination=remote_path)
            group = group.with_columns(pl.col(ParquetCDSColumns.DATE).cast(pl.Date))
            self._write_yearly_files(ric=str(ric), frame=group, remote_template=remote_template)

            rolling_predicate = pl.col(ParquetLoansColumns.DATE).ge(
                (dt.now() - timedelta(days=365)).date()
            )
            filtered_group = group.filter(rolling_predicate)

            arrow_table = self._get_arrow_table(filtered_group)

            # Serialize Parquet data to in-memory bytes
            buffer = io.BytesIO()
            pq.write_table(arrow_table, buffer)
            buffer.seek(0)
            self.s3_client.put_object(
                Bucket=settings.s3_bucket.lstrip("s3://"),
                Key=f"{remote_base_path.lstrip('/')}{file_name}",
                Body=buffer.read(),
                ACL="bucket-owner-full-control",
            )
            logger.info(f"written file {remote_base_path}{file_name}")
            return {"ric": ric, "s3_path": f"{remote_base_path}{file_name}"}

        except Exception as e:
            logger.warning(f"Error processing RIC {ric}")
            logger.exception(e)
            return {"exception": e.with_traceback(sys.exc_info()[2]), "ric": ric}

from pydantic import BaseSettings, Field
from pydantic.class_validators import root_validator


class LoansCDSParquetConverterSettingsCls(BaseSettings):
    s3_bucket: str = Field("s3://master-data.eu-west-1.steeleye.co", env="S3_BUCKET")
    se_reference_api_url: str = Field(
        "https://reference-api.dev-master-data.steeleye.co", env="SE_REFERENCE_API_URL"
    )
    max_workers: int = Field(5, env="MAX_WORKERS")

    @root_validator()
    def validate_bucket(cls, values):
        bucket = values.get("s3_bucket")
        bucket = bucket.rstrip("/")
        values["s3_bucket"] = bucket
        return values


loans_cds_settings: LoansCDSParquetConverterSettingsCls = LoansCDSParquetConverterSettingsCls()

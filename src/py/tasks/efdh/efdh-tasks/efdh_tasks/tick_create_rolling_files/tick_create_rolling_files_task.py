import boto3
import humanize
import logging
import os
import polars as pl
import psutil
import shutil
from aries_io_event.io_param import IOParamFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from datetime import date, datetime, timedelta
from efdh_tasks.tick_create_rolling_files.config import settings
from efdh_tasks.tick_create_rolling_files.input_schema import (
    TickCreateRollingFilesInput,
)
from efdh_tasks.tick_create_rolling_files.static import (
    DTYPES_MAP,
    EVENT_TYPES,
    RESOLUTIONS,
    ROLLING_PERIODS,
    RollingCloudFile,
    RollingFileNameTemplates,
    RollingPeriod,
)
from efdh_tasks.tick_create_rolling_files.utils import RollingFile
from efdh_utils.schema.parquet import RollingResolution
from efdh_utils.schema.refinitiv import RefinitivEventType, RefinitivExtractColumns
from se_io_utils.tempfile_utils import tmp_directory
from typing import Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


class TickCreateRollingFilesTask:
    def __init__(
        self,
        aries_task_input: AriesTaskInput,
    ):
        self.params_dict = aries_task_input.input_param.params
        self.input_params = TickCreateRollingFilesInput.validate(self.params_dict)
        self.temp_dir = tmp_directory()
        self.targets: Dict[
            str,
            Dict[
                Tuple[
                    RollingPeriod,
                    RollingResolution,
                    RefinitivEventType,
                ],
                Tuple[RollingFile, date],
            ],
        ] = {}
        self.sources: Dict[
            str, Dict[Tuple[RollingResolution, RefinitivEventType], pl.LazyFrame]
        ] = {}

        logger.info(f"beginning execution for {self.params_dict}")

    def execute(self) -> AriesTaskResult:
        successful_rics: List[str] = []
        failed_rics: List[str] = []

        for ric in self.input_params.rics:
            ric, exc = self.execute_single_ric(ric)

            if not exc:
                successful_rics.append(ric)
            else:
                try:
                    raise exc
                except Exception:
                    logger.exception(
                        f"{ric} - processing failed",
                    )
                    failed_rics.append(ric)

        shutil.rmtree(self.temp_dir)
        del self.targets
        del self.sources

        self.params_dict["successful_rics"] = successful_rics
        self.params_dict["failed_rics"] = failed_rics
        output_params = IOParamFieldSet(params=self.params_dict)

        return AriesTaskResult(output_param=output_params)

    def execute_single_ric(self, ric: str) -> Tuple[str, Optional[Exception]]:
        """Validating Task Input parameters.

        :return: Task input parameters
        """
        try:
            logger.info(f"{ric} - beginning execution")

            min_download_start_date = self.create_target_objects(ric)

            if self.input_params.start_date and self.input_params.end_date:
                start_date = self.input_params.start_date
                end_date = self.input_params.end_date
                delete_range = True
            else:
                start_date = min_download_start_date
                end_date = datetime.now().date() + timedelta(days=1)
                delete_range = False

            # create all the target_files
            self.acquire_source_objects(ric=ric, start_date=start_date, end_date=end_date)

            for (rolling_period, resolution, event_type), (
                rolling_file,
                max_file_date,
            ) in self.targets.get(ric, {}).items():
                try:
                    self.apply_concat_logic(
                        ric=ric,
                        delete_range=delete_range,
                        rolling_file=rolling_file,
                        start_date=start_date,
                        end_date=end_date,
                        max_file_date=max_file_date,
                        resolution=resolution,
                        event_type=event_type,
                    )
                except Exception:
                    logger.exception(
                        f"{rolling_period}, {resolution}, "
                        f"{event_type} - failed to upsert rolling file"
                    )

            return ric, None

        except Exception as e:
            return ric, e

    def apply_concat_logic(
        self,
        ric: str,
        delete_range: bool,
        rolling_file: RollingFile,
        start_date: date,
        end_date: date,
        max_file_date: date,
        resolution: RollingResolution,
        event_type: RefinitivEventType,
    ):
        new_df = self.get_source(
            ric=ric,
            resolution=resolution,
            event_type=event_type,
        ).filter(
            pl.col(RefinitivExtractColumns.DATE_TIME).ge(
                start_date if delete_range else max_file_date
            ),
            pl.col(RefinitivExtractColumns.DATE_TIME).le(end_date),
        )

        if delete_range:
            rolling_file.delete_by_date_range(start_date, end_date)
        # TODO: There is a risk of increased s3 operations if `new_df` is empty,
        #  but the tradeoff is we dont collect the df until end, which saves memory/compute,
        #  which is more expensive than s3 api calls
        rolling_file.df = (
            pl.concat(
                [rolling_file.df, new_df],
                how="diagonal",
            )
            .unique()
            .sort(by=RefinitivExtractColumns.DATE_TIME)
        )
        rolling_file.write_s3_output()
        logger.info(
            f"Current memory usage before after applying concat logic"
            f"{humanize.naturalsize(psutil.Process(os.getpid()).memory_info().rss)}"
        )

    def create_target_objects(self, ric: str) -> date:
        s3_client = boto3.client("s3")

        min_download_start_date = (datetime.now() + timedelta(days=1)).date()
        for event_type in EVENT_TYPES:
            for resolution in RESOLUTIONS:
                for rolling_period in ROLLING_PERIODS:
                    rolling_file = RollingFile(
                        ric=ric,
                        rolling_period=rolling_period,
                        resolution=resolution,
                        event_type=event_type,
                        temp_dir=self.temp_dir,
                        s3_client=s3_client,
                    )

                    source_max_date: Optional[date] = rolling_file.get_df_max_date()

                    # Compute the fallback date to 7 days before today
                    seven_days_ago = pl.select(
                        pl.lit(datetime.now()).dt.offset_by("-7d").max().dt.date()
                    ).item()

                    max_date: date = (
                        source_max_date + timedelta(days=1)
                        if source_max_date is not None and source_max_date >= seven_days_ago
                        else seven_days_ago
                    )
                    min_download_start_date = min(min_download_start_date, max_date)

                    self.set_target(
                        ric=ric,
                        rolling_period=rolling_period,
                        resolution=resolution,
                        event_type=event_type,
                        rolling_file=rolling_file,
                        max_date=max_date,
                    )
        # TODO: make this separate for tick and quote
        return min_download_start_date

    def acquire_source_objects(self, ric: str, start_date: date, end_date: date):
        """Updates the source dict with the source minutely/hourly quotes/trades files (intraday)
        Args:
            ric (str): the target ric
        """
        target_dates = pl.date_range(start=start_date, end=end_date, eager=True, closed="left")
        for resolution in RESOLUTIONS:
            for event_type in EVENT_TYPES:
                dfs: List[pl.LazyFrame] = []
                for target_date in target_dates:
                    file_name = RollingFileNameTemplates.INPUT_FILENAME_TPL.format(
                        ric=ric,
                        event_type=event_type.value.upper(),
                        resolution=resolution.value.upper(),
                        year=target_date.year,
                        month=target_date.month,
                        day=target_date.day,
                    )
                    prefix = RollingFileNameTemplates.INPUT_PREFIX_TPL.format(
                        ric=ric, year=target_date.year, month=target_date.month, day=target_date.day
                    )
                    cloud_file = RollingCloudFile(
                        bucket=settings.LEVEL1_TICK_DATA_BUCKET, file_name=file_name, prefix=prefix
                    )
                    try:
                        day_df = pl.scan_parquet(cloud_file.uri)
                        logger.info(
                            f"Current memory usage after reading parquet"
                            f"{humanize.naturalsize(psutil.Process(os.getpid()).memory_info().rss)}"
                        )

                        # Get column names
                        day_df_columns = day_df.collect_schema().names()

                        dfs.append(
                            day_df.cast(
                                {
                                    column: dtype
                                    for column, dtype in DTYPES_MAP[event_type].items()
                                    if column in day_df_columns
                                }
                            )
                        )

                    except pl.exceptions.ComputeError:
                        # only log a warning if a weekday file is missing, otherwise logs will
                        # flood for weekends
                        if target_date.weekday() < 5 and event_type == RefinitivEventType.QUOTE:
                            logger.warning(
                                f"{ric}, {target_date} - source file '{cloud_file.uri}' not found"
                            )
                    except Exception:
                        logger.exception(
                            f"{ric}, {target_date} - source file "
                            f"'{cloud_file.uri}' unexpected error reading file"
                        )

                df: pl.LazyFrame = (
                    pl.concat(dfs, how="diagonal")
                    if dfs
                    else pl.LazyFrame(schema=DTYPES_MAP[event_type])
                ).with_columns(
                    pl.col(RefinitivExtractColumns.DATE_TIME).cast(pl.Datetime("ns")).name.keep()
                )
                logger.info(
                    f"Current memory usage after concatenating all dataframes"
                    f"{humanize.naturalsize(psutil.Process(os.getpid()).memory_info().rss)}"
                )

                self.set_source(
                    ric=ric,
                    resolution=resolution,
                    event_type=event_type,
                    value=df,
                )

    def get_target(
        self,
        ric: str,
        rolling_period: RollingPeriod,
        resolution: RollingResolution,
        event_type: RefinitivEventType,
    ) -> Tuple[RollingFile, date]:
        return self.targets[ric][(rolling_period, resolution, event_type)]

    def set_target(
        self,
        ric: str,
        rolling_period: RollingPeriod,
        resolution: RollingResolution,
        event_type: RefinitivEventType,
        rolling_file: RollingFile,
        max_date: date,
    ):
        if ric not in self.targets:
            self.targets[ric] = {}
        self.targets[ric][(rolling_period, resolution, event_type)] = (rolling_file, max_date)

    def get_source(
        self, ric: str, resolution: RollingResolution, event_type: RefinitivEventType
    ) -> pl.LazyFrame:
        return self.sources[ric][(resolution, event_type)]

    def set_source(
        self,
        ric: str,
        resolution: RollingResolution,
        event_type: RefinitivEventType,
        value: pl.LazyFrame,
    ):
        if ric not in self.sources:
            self.sources[ric] = {}
        self.sources[ric][(resolution, event_type)] = value

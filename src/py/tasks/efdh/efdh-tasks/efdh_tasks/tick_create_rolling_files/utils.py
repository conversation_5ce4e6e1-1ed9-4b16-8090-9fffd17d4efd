import datetime
import logging
import polars as pl
from efdh_tasks.tick_create_rolling_files.config import settings
from efdh_tasks.tick_create_rolling_files.static import (
    DTYPES_MAP,
    POLARS_OFFSET_MAP,
    RollingCloudFile,
    RollingFileNameTemplates,
    RollingPeriod,
)
from efdh_utils.schema.parquet import RollingResolution
from efdh_utils.schema.refinitiv import RefinitivEventType, RefinitivExtractColumns
from pathlib import Path
from typing import Optional

logger = logging.getLogger(__name__)


class RollingFile:
    def __init__(
        self,
        ric: str,
        rolling_period: RollingPeriod,
        resolution: RollingResolution,
        event_type: RefinitivEventType,
        temp_dir: Path,
        s3_client,
    ):
        self._s3_client = s3_client
        self.ric = ric
        self.rolling_period = rolling_period
        self.resolution = resolution
        self.event_type = event_type
        self.dtypes = DTYPES_MAP[self.event_type]
        self.temp_dir = temp_dir
        self.file_info = self._make_file_info()
        self.df = self._read_file()

    def _make_file_info(self) -> RollingCloudFile:
        output_name = RollingFileNameTemplates.OUTPUT_FILENAME_TPL.format(
            ric=self.ric,
            event_type=self.event_type.value.upper(),
            resolution=self.resolution.value.upper(),
            date=self.rolling_period.value.upper(),
        )
        output_prefix = RollingFileNameTemplates.OUTPUT_PREFIX_TPL.format(ric=self.ric)
        return RollingCloudFile(
            bucket=settings.LEVEL1_TICK_DATA_BUCKET,
            prefix=output_prefix,
            file_name=output_name,
            tmp_dir=str(self.temp_dir),
        )

    def _read_file(self) -> pl.LazyFrame:
        try:
            df = pl.scan_parquet(self.file_info.uri)
            # Get column names efficiently (non-expensive operation for lazy df)
            df_columns = df.collect_schema().names()
            df = df.cast(
                dtypes={
                    column: dtype for column, dtype in self.dtypes.items() if column in df_columns
                }
            )
        except Exception:
            logger.warning(f"{self.file_info.uri} does not exist, will create")
            df = pl.LazyFrame(schema=self.dtypes)

        df = df.with_columns(
            pl.col(RefinitivExtractColumns.DATE_TIME).cast(pl.Datetime("ns")).name.keep()
        )

        return df

    def write_s3_output(self):
        self.df = self.df.filter(
            pl.col(RefinitivExtractColumns.DATE_TIME).ge(
                pl.col(RefinitivExtractColumns.DATE_TIME)
                .dt.date()
                .dt.offset_by(POLARS_OFFSET_MAP[self.rolling_period])
                .max()
                .dt.date()
            )
        )
        df_columns = self.df.collect_schema().names()
        self.df = self.df.cast(
            dtypes={column: dtype for column, dtype in self.dtypes.items() if column in df_columns}
        )
        self.df.collect().write_parquet(self.file_info.local_path, use_pyarrow=True)  # type: ignore
        self._s3_client.upload_file(
            Filename=self.file_info.local_path,
            Bucket=self.file_info.bucket,
            Key=self.file_info.key,
            ExtraArgs={"ACL": "bucket-owner-full-control"},
        )

    def delete_by_date_range(self, date_from: datetime.date, date_to: datetime.date):
        # These filter operaters must be exclusive, and the filters for new data will be inclusive
        self.df = self.df.filter(
            (pl.col(RefinitivExtractColumns.DATE_TIME).lt(date_from))
            | (pl.col(RefinitivExtractColumns.DATE_TIME).gt(date_to))
        )

    def get_df_max_date(self) -> Optional[datetime.date]:
        try:
            max_datetime = (
                self.df.select(pl.max(RefinitivExtractColumns.DATE_TIME)).collect().item()
            )

        except IndexError:
            # Accounts for an empty df
            max_datetime = None

        # Accounts for a single empty value in the df
        if max_datetime is None:
            return None

        return max_datetime.date()  # type: ignore

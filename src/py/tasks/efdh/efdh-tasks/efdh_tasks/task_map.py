from typing import Any


def resolve_task_func(task_name: str) -> Any:
    match task_name:
        case "annual_market_data_download":
            from efdh_tasks.annual_market_data_download.main import run_annual_market_data_download

            return run_annual_market_data_download
        case "eod_parquet_converter":
            from efdh_tasks.eod_parquet_converter.main import run_eod_parquet_converter

            return run_eod_parquet_converter
        case "instrument_list_extraction":
            from efdh_tasks.instrument_list_extraction.main import run_instrument_list_extraction

            return run_instrument_list_extraction
        case "loans_cds_converter":
            from efdh_tasks.loans_cds_converter.main import run_loans_cds_converter

            return run_loans_cds_converter
        case "loans_cds_parquet_converter":
            from efdh_tasks.loans_cds_parquet_converter.main import run_loans_cds_parquet_converter

            return run_loans_cds_parquet_converter
        case "map_ric":
            from efdh_tasks.map_ric.main import run_map_ric

            return run_map_ric
        case "obd_parquet_converter":
            from efdh_tasks.obd_parquet_converter.main import run_obd_parquet_converter

            return run_obd_parquet_converter
        case "tick_create_rolling_files":
            from efdh_tasks.tick_create_rolling_files.main import run_tick_create_rolling_files

            return run_tick_create_rolling_files
        case "tick_parquet_converter":
            from efdh_tasks.tick_parquet_converter.main import run_tick_parquet_converter

            return run_tick_parquet_converter
        case "tca_apply_group":
            from efdh_tasks.tca_apply_group.main import run_tca_apply_group

            return run_tca_apply_group
        case "tca_apply_metrics" | "tca_apply_metrics_high_mem":
            from efdh_tasks.tca_apply_metrics.main import run_tca_apply_metrics

            return run_tca_apply_metrics
        case "tca_coverage":
            from efdh_tasks.tca_coverage.main import run_tca_coverage

            return run_tca_coverage
        case "refinitiv_news_ingestion":
            from efdh_tasks.refinitiv_news_ingestion.main import refinitiv_news_ingestion

            return refinitiv_news_ingestion
        case "update_ric_lookup":
            from efdh_tasks.update_ric_lookup.main import update_ric_lookup

            return update_ric_lookup
        case _:
            raise ValueError("Task name must be in efdh_tasks.")

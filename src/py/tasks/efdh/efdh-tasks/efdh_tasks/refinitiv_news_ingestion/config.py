import logging
import os
from reference_db.models.story_keywords import StoryKeywords
from reference_db.models.story_meta_data import StoryMetaData
from reference_db.models.story_qcodes import StoryQCodes
from reference_db.models.story_subject import StorySubject

logger = logging.getLogger(__name__)


class SettingsCls:
    """This class is supposed to act as a settings config.

    It centralizes behaviours and adds a level of abstraction.
    """

    def __init__(self) -> None:
        self.SSM_PG_CONN_STRING_KEY = os.getenv(
            "REFERENCE_DB_SSM_PARAMETER_NAME",
            "DEV_REFINITIV_NEWS_PG_CONN_STRING",
        )
        self.SSM_REFINITIV_NEWS_QUEUE_INFO_KEY = "REFINITIV_NEWS_QUEUE_INFO"
        self.REFINITIV_NEWS_BUCKET = os.getenv("REFINITIV_NEWS_BUCKET", "dev-news.steeleye.co")
        self.REFERENCE_API_URL = os.getenv(
            "REFERENCE_API_URL", "https://reference-api.dev-master-data.steeleye.co"
        )
        self.NEWS_ATTACHMENT_PATTERN = (
            r"http://newsfile\.refinitiv\.com/getnewsfile/v1/story\?"
            r"guid=urn:newsml:reuters\.com:\d{8}:[A-Za-z0-9]{10}"
        )
        self.MODELS = {
            "subjects": StorySubject,
            "keywords": StoryKeywords,
            "qcodes": StoryQCodes,
            "stories": StoryMetaData,
        }


Settings = SettingsCls()

import json
import logging
import re
from aries_se_api_client.client import AriesApiClient
from datetime import datetime as dt
from efdh_tasks.refinitiv_news_ingestion.config import Settings
from efdh_tasks.refinitiv_news_ingestion.schemas.task_inputs import Meta, NormalisedMessage
from reference_api_client.perm_ids import PermIDs
from typing import Any, Dict, List, Tuple

logger = logging.getLogger(__name__)


class Normalisation:
    def __init__(
        self,
        message_body: str,
        uri: str,
    ):
        self.message_body = json.loads(message_body)
        self.src_uri = uri
        self.SENTIMENTS = {
            "extCptRel:hasSentimentNegative": "sentimentNegative",
            "extCptRel:hasSentimentNeutral": "sentimentNeutral",
            "extCptRel:hasSentimentPositive": "sentimentPositive",
        }
        self.ref_api_client = AriesApiClient(Settings.REFERENCE_API_URL)
        self.perm_id_client = PermIDs(client=self.ref_api_client)

    def normalise_body(self) -> NormalisedMessage:
        subjects: List[Dict[str, Any]] = []
        keywords: List[Dict[str, Any]] = []
        qcodes: List[Dict[str, Any]] = []
        story: Dict[str, Any] = {}
        content: Dict[str, Any] = {}
        try:
            news_item = (
                self.message_body.get("payload", {})
                .get("newsMessage", {})
                .get("itemSet", {})
                .get("newsItem", [{}])[0]
            )
            message_id = self.src_uri.split("/")[-1]
            ecp_msg_id = self.message_body.get("ecpMessageID")
            if ecp_msg_id is not None:
                story["storyId"] = ecp_msg_id.split(":")[-2]

            story, skip = self.get_story(story, self.message_body, news_item, message_id=message_id)
            if skip:
                return self.make_output(subjects, keywords, qcodes, story, content)

            story = self.get_sentiment(story, news_item)
            subjects = self.get_subjects(
                subjects, news_item.get("contentMeta", {}), story["storyId"], story["version"]
            )

            try:
                self.map_perm_ids([s.get("ric") for s in subjects if s.get("ric")])
            except Exception:
                logger.warning(f"Failed to map perm ids for {message_id}")

            keywords = self.get_keywords(
                keywords,
                news_item.get("contentMeta", {}).get("keyword", []),
                story["storyId"],
                story["version"],
            )
            self.get_qcodes(qcodes, news_item, story_id=story["storyId"], version=story["version"])
            uri_params = self.get_uri_params(story)
            content = self.get_content(content, news_item, uri_params)

            content_key = content.get("uri_stub")
            if content_key:
                story["contentUri"] = f"s3://{Settings.REFINITIV_NEWS_BUCKET}/{content_key}"

        except Exception as e:
            logger.exception(f"failed: {self.message_body}")
            raise e

        return self.make_output(subjects, keywords, qcodes, story, content)

    @staticmethod
    def get_uri_params(story: Dict[str, Any]) -> Dict[str, Any]:
        try:
            try:
                created_at = dt.strptime(story["storyCreated"], "%Y-%m-%dT%H:%M:%S.%fZ")
            except Exception:
                created_at = dt.strptime(story["storyCreated"], "%Y-%m-%dT%H:%M:%SZ")
            uri_params = {
                "year": created_at.year,
                "month": created_at.month,
                "day": created_at.day,
                "story_id": story["storyId"],
                "version": story["version"],
            }
        except Exception:
            raise Exception("Failed to extract content URI parameters")
        return uri_params

    def get_subjects(
        self, subjects: List[Dict[str, Any]], meta: Dict[str, Any], story_id: str, version: int
    ) -> List[Dict[str, Any]]:
        for x in meta.get("subject", []):
            subject: Dict[str, Any] = {
                "storyId": story_id,
                "version": version,
                "subjectCode": x.get("_qcode", "N/A"),
            }
            relevance = x.get("related", [{}])[0].get("_qcode", "").replace("hmlInd:", "")
            if len(relevance):
                subject["storySubjectRelevance"] = relevance

            subject["storySubjectConfidence"] = x.get("_confidence")
            ric_match = re.match("R:(.*)", subject["subjectCode"])
            if ric_match is not None:
                subject["ric"] = ric_match.group(1)

            perm_id_match = re.match("P:(.*)", subject["subjectCode"])
            if perm_id_match is not None:
                subject["permId"] = perm_id_match.group(1)

            subjects.append(subject)
        return subjects

    @staticmethod
    def get_keywords(
        keywords: List[Dict[str, Any]],
        story_keywords: List[Dict[str, Any]],
        story_id: str,
        version: int,
    ) -> List[Dict[str, Any]]:
        for k in story_keywords:
            if k.get("$"):
                keywords.append({"keyword": k.get("$"), "storyId": story_id, "version": version})

        return keywords

    def get_sentiment(self, story: Dict[str, Any], dd: Dict[str, Any]) -> Dict[str, Any]:
        for _property in dd.get("contentMeta", {}).get("contentMetaExtProperty", []):
            if _property.get("_rel") in self.SENTIMENTS:
                story[self.SENTIMENTS[_property.get("_rel")]] = _property["_value"]
        return story

    @staticmethod
    def get_content(
        content: Dict[str, Any], dd: Dict[str, Any], uri_params: Dict[str, Any]
    ) -> Dict[str, Any]:
        content["plain"] = dd.get("contentSet", {}).get("inlineData", [{}])[0].get("$")
        content["xml"] = dd.get("contentSet", {}).get("inlineXML", [{}])[0].get("$")
        content["uri_stub"] = (
            "aries/lake/ingress/news/{year}/{month:02d}/{day:02d}/{story_id}/{version}/".format(
                **uri_params
            )
        )
        return content

    @staticmethod
    def get_story(
        story: Dict[str, Any], d: Dict[str, Any], dd: Dict[str, Any], message_id: str
    ) -> Tuple[Dict[str, Any], bool]:
        for _id in dd.get("contentMeta", {}).get("altId", [{}]):
            if _id.get("_type", "N/A") == "idType:storyId":
                story["storyId"] = dd.get("contentMeta", {}).get("altId", [{}])[0].get("$")
        if "storyId" not in story:
            return story, True

        story["messageId"] = message_id
        story["title"] = dd.get("itemMeta", {}).get("title", [{}])[0].get("$")
        story["copyrightHolder"] = (
            dd.get("rightsInfo", [{}])[0].get("copyrightHolder", {}).get("_literal")
        )
        story["copyright"] = dd.get("rightsInfo", [{}])[0].get("copyrightNotice", [{}])[0].get("$")
        story["provider"] = dd.get("itemMeta", {}).get("provider", {}).get("_literal")
        story["publicationStatus"] = dd.get("itemMeta", {}).get("pubStatus", {}).get("_qcode")
        story["role"] = dd.get("itemMeta", {}).get("role", {}).get("_qcode")
        story["vendorFileName"] = dd.get("itemMeta", {}).get("fileName", {}).get("$")
        story["language"] = dd.get("contentMeta", {}).get("language", [{}])[0].get("_tag")
        eu = dd.get("contentMeta", {}).get("urgency", {}).get("$")
        story["editorialUrgency"] = str(eu) if eu is not None else None
        story["storyCreated"] = dd.get("itemMeta", {}).get("firstCreated", {}).get("$")
        story["storyVersionCreated"] = dd.get("itemMeta", {}).get("versionCreated", {}).get("$")
        story["version"] = dd.get("_version", 1)
        return story, False

    @staticmethod
    def get_qcodes(
        qcodes: List[Dict[str, Any]], dd: Dict[str, Any], story_id: str, version: int
    ) -> List[Dict[str, Any]]:
        def append_qcode_record(
            qcode: str, _type: str, story_id: str = story_id, qcodes: List[Dict[str, Any]] = qcodes
        ) -> None:
            if qcode:
                qcodes.append(
                    {
                        "storyId": story_id,
                        "version": version,
                        "qcode": qcode,
                        "type": _type,
                    }
                )

        audiences = dd.get("contentMeta", {}).get("audience", [])
        contributors = dd.get("contentMeta", {}).get("contributor", [])
        creators = dd.get("contentMeta", {}).get("creator", [])
        infosources = dd.get("contentMeta", {}).get("infoSource", [])
        for a in audiences:
            append_qcode_record(qcode=a.get("_qcode"), _type="audience")
        for c in contributors:
            append_qcode_record(qcode=c.get("_qcode"), _type="contributor")
        for c in creators:
            append_qcode_record(qcode=c.get("_qcode"), _type="creator")
        for infosource in infosources:
            append_qcode_record(qcode=infosource.get("_qcode"), _type="infoSource")

        return qcodes

    @staticmethod
    def remove_nones(_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        return [{k: v for k, v in d.items() if v is not None} for d in _list]

    def make_output(
        self,
        subjects: List[Dict[str, Any]],
        keywords: List[Dict[str, Any]],
        qcodes: List[Dict[str, Any]],
        story: Dict[str, Any],
        content: Dict[str, Any],
    ) -> NormalisedMessage:
        return NormalisedMessage(
            meta=Meta(
                subjects=self.remove_nones(subjects),
                keywords=self.remove_nones(keywords),
                qcodes=self.remove_nones(qcodes),
                stories=self.remove_nones([story]),
            ),
            content=content,
        )

    def map_perm_ids(self, rics) -> None:
        self.perm_id_client.bulk_get_perm_ids(json_body={"rics": rics})

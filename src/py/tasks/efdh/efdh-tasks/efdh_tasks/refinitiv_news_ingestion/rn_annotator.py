import logging
import os
from contextlib import contextmanager
from efdh_tasks.refinitiv_news_ingestion.config import Settings
from news_sdk.helpers import get_ssm_parameter
from news_sdk.news_messages_filtered import NewsSubscription
from sqlalchemy import create_engine, orm
from typing import Generator

logger = logging.getLogger(__name__)


class RefinitivNewsIngestionAnnotator:
    def __init__(self) -> None:
        self.news_subscription = NewsSubscription()
        self._engine = create_engine(
            (
                self.get_local_conn_string()
                if os.environ.get("LOCAL_EXECUTION", "false") == "true"
                else get_ssm_parameter(parameter_name=Settings.SSM_PG_CONN_STRING_KEY, decrypt=True)
            ),
            echo=False,
        )  # add echo=True to turn logging on

        self._session_factory = orm.scoped_session(
            orm.sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self._engine,
            ),
        )

    @contextmanager
    def session(self) -> Generator:
        session: orm.Session = self._session_factory()
        try:
            yield session
        except Exception as e:
            logger.exception("Session rollback because of exception")
            session.rollback()
            raise e
        finally:
            session.close()

    @staticmethod
    def get_local_conn_string() -> str:
        return "postgresql://postgres:postgres@localhost:5432/se_reference_db"


annotator = RefinitivNewsIngestionAnnotator()

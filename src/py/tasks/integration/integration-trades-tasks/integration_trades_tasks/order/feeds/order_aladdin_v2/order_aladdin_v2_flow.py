import addict
import os
import pandas as pd
import shutil
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_core_tasks.aries.sink_file_audit.auditor_and_metrics_producer import (
    Metrics,
    RecordLevelAudit,
    run_auditor_and_metrics_producer,
)
from aries_se_core_tasks.aries.sink_file_audit.auditor_and_metrics_producer import (
    Params as AuditorAndMetricsProducerParams,
)
from aries_se_core_tasks.aries.sink_file_audit.static import (
    StandardAuditMessages,
)
from aries_se_core_tasks.aries.utility_tasks.finish_flow import add_nested_params, finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.feeds.generic.get_tenant_lei import (  # type: ignore[attr-defined] # noqa: E501
    run_get_tenant_lei,
)
from aries_se_core_tasks.frame.frame_column_manipulator import run_frame_column_manipulator
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.frame.frame_splitter import run_frame_splitter
from aries_se_core_tasks.frame.get_rows_by_condition import run_get_rows_by_condition
from aries_se_core_tasks.get_primary_transformations import (  # type: ignore[attr-defined] # noqa: E501
    run_get_primary_transformations,
)
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.read.batch_producer import (  # type: ignore[attr-defined] # noqa: E501
    run_batch_producer,
)
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.read.csv_file_splitter import run_csv_file_splitter
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.transform.map.map_to_nested import run_map_to_nested
from aries_se_core_tasks.utilities.elasticsearch_utils import get_es_config, get_srp_es_config
from aries_se_trades_tasks.instrument.instrument_fallback import run_instrument_fallback
from aries_se_trades_tasks.instrument.link_instrument import run_link_instrument
from aries_se_trades_tasks.meta.assign_meta_parent import run_assign_meta_parent
from aries_se_trades_tasks.order.best_execution import run_best_execution
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.static import DevColumns
from aries_se_trades_tasks.order_app_metrics_enum import OrderAppMetricsEnum
from aries_se_trades_tasks.party.link_parties import run_link_parties
from aries_se_trades_tasks.party.party_fallback_with_lei_lookup import (
    run_party_fallback_with_lei_lookup,
)
from aries_se_trades_tasks.remove_duplicates.remove_duplicate_newo import run_remove_duplicate_newo
from aries_task_link.models import AriesTaskInput
from distutils.util import strtobool
from integration_trades_tasks.order.feeds.order_aladdin_v2.abstract_order_aladdin_v2_flow import (
    AbstractOrderAladdinV2,
)
from integration_trades_tasks.order.feeds.order_aladdin_v2.input_schema import (
    OrderAladdinV2AriesTaskInput,
)
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_core_tasks.feeds.generic.get_tenant_lei import Params as GetTenantLEIParams
from se_core_tasks.frame.frame_column_manipulator import Action
from se_core_tasks.frame.frame_column_manipulator import (
    Params as FrameColumnManipulatorParams,
)
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_core_tasks.frame.frame_concatenator import Params as FrameConcatenatorParams
from se_core_tasks.frame.frame_splitter import Params as FrameSplitterParams
from se_core_tasks.frame.get_rows_by_condition import Params as GetRowsByConditionParams
from se_core_tasks.io.read.batch_producer import Params as BatchProducerParams
from se_core_tasks.io.read.csv_file_splitter import Params as CsvSplitterParams
from se_core_tasks.map.map_to_nested import Params as MapToNestedParams
from se_data_lake.cloud_utils import get_cloud_provider_from_file_uri, get_cloud_provider_prefix
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8
from se_elastic_schema.models import Order
from se_elastic_schema.static.reference import FirmType
from se_elasticsearch.repository import get_repository_by_cluster_version
from se_elasticsearch.repository.elasticsearch8 import ElasticsearchRepository
from se_enums.cloud import CloudProviderEnum
from se_enums.elastic_search import EsActionEnum
from se_io_utils.tempfile_utils import tmp_directory
from se_schema_meta import PARENT
from se_trades_tasks.meta.assign_meta_parent import Params as MetaParentParams
from se_trades_tasks.order.party.fallback.party_fallback_with_lei_lookup import (
    Params as PartyFallbackParams,
)
from se_trades_tasks.order.remove_duplicates.remove_duplicate_newo import (
    Params as RemoveDuplicateNewoParams,
)
from se_trades_tasks.order.static import ModelPrefix, OrderColumns, add_prefix
from se_trades_tasks.order_and_tr.instrument.fallback.instrument_fallback import (
    FallbackInstrumentAttributes,
)
from se_trades_tasks.order_and_tr.instrument.fallback.instrument_fallback import (
    Params as InstrumentFallbackParams,
)
from se_trades_tasks.order_and_tr.instrument.link.link_instrument import (
    Params as LinkInstrumentParams,
)
from se_trades_tasks.order_and_tr.party.link_parties import (
    MatchingCondition,
    PartiesMatchingConditions,
)
from se_trades_tasks.order_and_tr.party.link_parties import Params as LinkPartiesParams
from se_trades_tasks.order_and_tr.static import INSTRUMENT_PATH, InstrumentFields, PartyPrefix
from typing import List, Type

# supress chained_assignment warning to avoid polluting the logs
pd.options.mode.chained_assignment = None

SRP_THROUGH_MASTER_DATA: bool = bool(strtobool(os.getenv("SRP_THROUGH_MASTER_DATA", "True")))
FETCH_MARKET_EOD_DATA: bool = bool(strtobool(os.getenv("FETCH_MARKET_EOD_DATA", "True")))


class DefaultAladdinV2(AbstractOrderAladdinV2):
    def run_flow(
        self,
        aries_task_input: AriesTaskInput,
        app_metrics_path: str,
        audit_path: str,
        result_path: str,
    ):
        """Specs here: https://steeleye.atlassian.net/wiki/spaces/IN/pages/3654
        254594/Order+Aladdin+V2.

        This is the default implementation for Order Aladdin V2's flow. The input
        is a batch of either client-side or market-side orders which contains all the
        relevant input columns (with string dtype), and has a reasonable size
        as defined in the config DB.

        This flows follows the assumption that no further sub-batching is needed,
        everything can be loaded into memory because the working dataset is already
        a batch of the original input file, and the worker's specs account for it.

        The output of this flow is one or two of the following:
        - An NDJSON file containing Synthetic NEWOs
        - An NDJSON file containing order events, which are PARFs the majority of the time,
        but there can also be other events such as CAMEs.

        These NDJSON files will be sent to the usual ingestion pipeline, comprising of
        ApplyMeta for data validation, and ElasticWriter to ingest the records into Elastic.
        The Elastic action for both is CREATE. There are no updates or upserts included
        in the design of this feed. It does not support Quarantine, intentionally by design,
        and we do not require updating existing Orders in Elastic.

        For each input record, we will attempt to create a Synthetic NEWO. Naturally,
        in the RemoveDuplicateNEWO Task, we will remove any duplicate synths.
        Besides the Synthetic NEWO, we will create a PARF for each input record. Ocasionally,
        depending on certain data points we might identify these as other order events, such as CAMEs.
        Please consult the specs for more details.
        """  # noqa: E501

        # SETUP #

        # Parse and validate AriesTaskInput parameters
        order_aladdin_v2_input: OrderAladdinV2AriesTaskInput = unpack_aries_task_input(
            aries_task_input=aries_task_input, model=OrderAladdinV2AriesTaskInput
        )

        # Get tenant workflow tenant config from postgres
        cached_tenant_workflow_config: addict.Dict = CachedTenantWorkflowAPIClient.get(
            stack_name=aries_task_input.workflow.stack,
            tenant_name=aries_task_input.workflow.tenant,
            workflow_name=aries_task_input.workflow.name,
        )

        streamed: bool = cached_tenant_workflow_config.workflow.streamed
        tenant: str = aries_task_input.workflow.tenant

        # Create local temporary directory to store intermediate files
        tmp_storage: str = tmp_directory().as_posix()

        # Determine Cloud Provider
        cloud_provider = get_cloud_provider_from_file_uri(file_uri=order_aladdin_v2_input.file_uri)
        cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

        # Determine the Cloud Bucket of the tenant
        tenant_bucket_with_cloud_prefix: str = get_tenant_bucket(
            task_input=order_aladdin_v2_input, cloud_provider_prefix=cloud_provider_prefix
        )

        es_client_tenant: ElasticsearchRepository = get_repository_by_cluster_version(
            resource_config=get_es_config()
        )
        es_client_srp = self.instantiate_srp_es_client()

        # END SETUP #

        # BEGIN PRE-PROCESSING #

        local_file_path: str = run_download_file(file_url=order_aladdin_v2_input.file_uri)

        # Read the input CSV file and perform basic pre-processing.
        # The CsvFileSplitter Task will split the CSV file into chunks,
        # but we are only interested in the first and only chunk,
        # as the input file is already a batch of records.
        # NOTE: See https://steeleye.atlassian.net/browse/ON-4798 for more details.
        # the batch of market-side orders for certain tenants may not have a fixed size,
        # hence the arbitrarily large `chunksize` param.
        try:
            csv_splitter_result: FileSplitterResult = run_csv_file_splitter(
                streamed=streamed,
                params=CsvSplitterParams(
                    chunksize=100_000_0,
                    detect_encoding=True,
                    normalise_columns=False,
                ),
                csv_path=local_file_path,
                realm=tenant_bucket_with_cloud_prefix,
                sources_dir=tmp_storage,
            )[0]

        # If the CSV file is empty, return an empty output
        except IndexError:
            empty_output = add_nested_params(
                file_uri=None,
                es_action=EsActionEnum.CREATE,
                data_model=Order.get_reference().get_qualified_reference(),
            )

            shutil.rmtree(tmp_storage)

            finish_flow(
                result_path=result_path,
                result_data={
                    "synthetic_newo": empty_output,
                    "orders": empty_output,
                },
            )
            return

        # Run the BatchProducer Task to produce a Pandas DataFrame from the extracted CSV chunk
        # The Task also enforces datatypes, creates missing columns and
        # audits missing/unnecessary/empty columns
        input_df: pd.DataFrame = run_batch_producer(
            streamed=streamed,
            params=BatchProducerParams(
                source_schema=self._get_input_columns_type_map(),
                audit_null_columns=False,
            ),
            file_splitter_result=csv_splitter_result,
            return_dataframe=True,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # END PRE-PROCESSING #

        # BEGIN BUSINESS LOGIC #
        tenant_lei_df: pd.DataFrame = run_get_tenant_lei(
            source_frame=input_df,
            params=GetTenantLEIParams(
                target_lei_column=DevColumns.EXECUTING_ENTITY_WITH_LEI,
                target_column_prefix=PartyPrefix.LEI,
            ),
            tenant=tenant,
            es_client=es_client_tenant,
        )

        mappings_input_df = run_frame_concatenator(
            input_df=input_df,
            tenant_lei_df=tenant_lei_df,
            params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Populate the majority of the target fields in a centralized transformations class
        transformed_df = run_get_primary_transformations(
            source_frame=mappings_input_df,
            flow=self._get_flow_name(),
            tenant=aries_task_input.workflow.tenant,
            realm=tenant_bucket_with_cloud_prefix,
            input_file_path=order_aladdin_v2_input.file_uri,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
            source_file_uri=order_aladdin_v2_input.source_file_uri,
            file_uri=order_aladdin_v2_input.file_uri,
            es_client=es_client_tenant,
            order_id_cache_path=order_aladdin_v2_input.order_id_cache_path,
        )

        # Exclude records where the OrderStatus is PARF and the TradedQuantity is 0
        # As per the specs, these skipped records are not audited.
        transformed_df = run_get_rows_by_condition(
            source_frame=transformed_df,
            params=GetRowsByConditionParams(
                query=f"~(`{add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.EXECUTION_DETAILS_ORDER_STATUS)}`"  # noqa: E501
                f".str.upper() == 'PARF' & "
                f"`{add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY)}`"  # noqa: E501
                f".fillna(1).astype('float') == 0)"
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Populate the `&parent` field for OrderStates
        meta_parent_df = run_assign_meta_parent(
            source_frame=transformed_df,
            params=MetaParentParams(
                parent_model_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER, attribute=OrderColumns.META_MODEL
                ),
                parent_attributes_prefix=ModelPrefix.ORDER_DOT,
                target_attribute=add_prefix(prefix=ModelPrefix.ORDER_STATE, attribute=PARENT),
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Link identifiers built in InstrumentIdentifier from input instrument
        # data with SRP and tenant instrument data
        link_instrument_df = run_link_instrument(
            source_frame=transformed_df,
            params=LinkInstrumentParams(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                asset_class_attribute=DevColumns.ASSET_CLASS,
                currency_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                venue_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
            ),
            tenant=aries_task_input.workflow.tenant,
            es_client_srp=es_client_srp,
            es_client_tenant=es_client_tenant,
        )

        # Link identifiers built in BlotterPartyIdentifiers from
        # input party data with tenant MyMarket data.
        link_parties_df = self.link_parties_and_dedupe_by_firm_type(
            aries_task_input=aries_task_input,
            es_client_tenant=es_client_tenant,
            transformed_df=transformed_df,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        party_fallback_input = run_frame_concatenator(
            transformed_df=transformed_df,
            link_parties_df=link_parties_df,
            params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Create Party records embedded in the Orders for records
        # where LinkParties did not produce any hits
        party_fallback_df = run_party_fallback_with_lei_lookup(
            source_frame=party_fallback_input,
            params=PartyFallbackParams(),
        )

        instrument_fallback_input_df = run_frame_concatenator(
            transformed_df=transformed_df,
            link_instrument_df=link_instrument_df,
            params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Create Instrument records embedded in the Orders for records
        # where LinkInstrument did not produce any hits
        instrument_fallback_df = run_instrument_fallback(
            source_frame=instrument_fallback_input_df,
            params=InstrumentFallbackParams(
                instrument_fields_map=[
                    FallbackInstrumentAttributes(
                        source_field=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                        target_field=InstrumentFields.NOTIONAL_CURRENCY_1,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
                        target_field=InstrumentFields.FX_DERIVATIVE_NOTIONAL_CURRENCY_2,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                        target_field=InstrumentFields.EXT_VENUE_NAME,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=DevColumns.RIC,
                        target_field="ext.pricingReferences.RIC",
                    ),
                    FallbackInstrumentAttributes(
                        source_field=OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION,
                        target_field=InstrumentFields.EXT_PRICE_NOTATION,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                        target_field=InstrumentFields.EXT_QUANTITY_NOTATION,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=DevColumns.INSTRUMENT_FULL_NAME,
                        target_field=InstrumentFields.INSTRUMENT_FULL_NAME,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=DevColumns.INSTRUMENT_CLASSIFICATION,
                        target_field=InstrumentFields.INSTRUMENT_CLASSIFICATION,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=DevColumns.INSTRUMENT_UNIQUE_IDENTIFIER,
                        target_field=InstrumentFields.EXT_INSTRUMENT_UNIQUE_ID,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=DevColumns.INSTRUMENT_CREATED_THROUGH_FB,
                        target_field=InstrumentFields.IS_CREATED_THROUGH_FALLBACK,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=DevColumns.BEST_EX_ASSET_CLASS_MAIN,
                        target_field=InstrumentFields.EXT_BEST_EX_ASSET_CLASS_MAIN,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=DevColumns.STRIKE_PRICE_CURRENCY,
                        target_field=InstrumentFields.DERIVATIVE_STRIKE_PRICE_CURRENCY,
                    ),
                ],
                str_to_bool_dict={
                    "true": True,
                    "false": False,
                },
                cfi_and_bestex_from_instrument_classification=True,
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        instrument_overrides_input_df = run_frame_concatenator(
            transformed_df=transformed_df,
            instrument_fallback_df=instrument_fallback_df,
            params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Override instrumentDetails.instrument.instrumentFullName
        # For FX, only instruments via fallback should have this override
        # which is already done in the fallback task.
        instrument_overrides_df = run_map_to_nested(
            source_frame=instrument_overrides_input_df,
            params=MapToNestedParams(
                source_attribute=DevColumns.INSTRUMENT_FULL_NAME,
                nested_path="instrumentFullName",
                target_attribute="instrumentDetails.instrument",
                skip_target_nan=False,
                query=f"`{DevColumns.FILE_TYPE_ASSET_CLASS}`!='FX'",
            ),
        )

        # Concat all relevant DataFrames and discard temporary
        # columns that must not be part of the final result
        aux_df = run_frame_concatenator(
            transformed_df=transformed_df,
            meta_parent_df=meta_parent_df,
            party_fallback_df=party_fallback_df,
            instrument_overrides_df=instrument_overrides_df,
            params=FrameConcatenatorParams(
                orient=OrientEnum.horizontal,
                drop_columns=[
                    OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                    OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                    "currency_attribute",
                    "notional_currency_2_attribute",
                    "asset_class_attribute",
                    "instrument_classification_attribute",
                    "option_type_attribute",
                    "expiry_date_attribute",
                    "option_strike_price_attribute",
                    "underlying_symbol_attribute",
                    "underlying_symbol_expiry_code_attribute",
                    "venue_attribute",
                    "isin_attribute",
                    "__fallback_buyer__",
                    "__fallback_seller__",
                    "__fallback_counterparty__",
                    "__fallback_client__",
                    "__fallback_buyer_dec_maker__",
                    "__fallback_seller_dec_maker__",
                    "__fallback_inv_dec_in_firm__",
                    "__fallback_trader__",
                    "__fallback_exec_within_firm__",
                    "__fallback_executing_entity__",
                    DevColumns.RIC,
                    DevColumns.INSTRUMENT_UNIQUE_IDENTIFIER,
                    DevColumns.ASSET_CLASS,
                    DevColumns.INSTRUMENT_FULL_NAME,
                    DevColumns.INSTRUMENT_CLASSIFICATION,
                    DevColumns.FILE_TYPE_ASSET_CLASS,
                    DevColumns.INSTRUMENT_CREATED_THROUGH_FB,
                    DevColumns.BEST_EX_ASSET_CLASS_MAIN,
                    DevColumns.STRIKE_PRICE_CURRENCY,
                ],
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Split the columns pertaining to Orders to a separate DataFrame
        order_records_df = run_frame_splitter(
            source_frame=aux_df,
            params=FrameSplitterParams(
                except_prefix=ModelPrefix.ORDER_STATE_DOT, strip_prefix=True
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Strip the Order prefix
        parsed_order_records_df = run_frame_column_manipulator(
            source_frame=order_records_df,
            params=FrameColumnManipulatorParams(action=Action.strip, prefix=ModelPrefix.ORDER_DOT),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Split the columns pertaining to OrderStates to a separate DataFrame
        order_state_records_df = run_frame_splitter(
            source_frame=aux_df,
            params=FrameSplitterParams(except_prefix=ModelPrefix.ORDER_DOT, strip_prefix=True),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Strip the OrderState prefix
        parsed_order_state_records_df = run_frame_column_manipulator(
            source_frame=order_state_records_df,
            params=FrameColumnManipulatorParams(
                action=Action.strip, prefix=ModelPrefix.ORDER_STATE_DOT
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Concat a final DataFrame that contains all Order +
        # OrderState records without any temporary columns/prefixes
        orders_and_order_states_df = run_frame_concatenator(
            parsed_order_records_df=parsed_order_records_df,
            parsed_order_state_records_df=parsed_order_state_records_df,
            params=FrameConcatenatorParams(
                orient=OrientEnum.vertical, reset_index=True, drop_index=True
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Exclude all records that do not have a value in the "executionDetails.orderStatus" field
        # Such records are "fake" Orders that must be discarded i.e.
        # OrderStates associated with client-side Orders
        # where `orderDetail.orderDetailQuantityBooked` == 0 OR pd.NA
        filtered_orders_and_order_states_df = run_get_rows_by_condition(
            source_frame=orders_and_order_states_df,
            params=GetRowsByConditionParams(
                query=f"`{OrderColumns.EXECUTION_DETAILS_ORDER_STATUS}`.notnull()"
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Remove duplicate NEWOs from dataset (i.e. synthetic NEWOs that were created unnecessarily)
        deduplicated_data_df = run_remove_duplicate_newo(
            source_frame=filtered_orders_and_order_states_df,
            params=RemoveDuplicateNewoParams(
                newo_in_file_col=DevColumns.NEWO_IN_FILE,
                drop_newo_in_file_col=False,
                query_elastic_for_existing_newo=True,
            ),
            tenant=aries_task_input.workflow.tenant,
            es_client=es_client_tenant,
            streamed=streamed,
            cloud_provider=cloud_provider,
            audit_path=audit_path,
            app_metrics_path=app_metrics_path,
        )

        best_execution_df = run_best_execution(
            source_frame=deduplicated_data_df,
            es_client=es_client_tenant,
            fetch_market_eod_data=FETCH_MARKET_EOD_DATA,
            streamed=streamed,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Note that we are not discarding the `&parent` column here
        # this can be propagated to the ApplyMeta Conductor Task, which will reuse it
        final_result_df = run_frame_concatenator(
            best_execution_df=best_execution_df,
            deduplicated_data_df=deduplicated_data_df,
            params=FrameConcatenatorParams(
                orient=OrientEnum.horizontal,
                drop_columns=[
                    OrderColumns.META_MODEL,
                    DevColumns.NEWO_IN_FILE,
                ],
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # CREATE SYNTHETIC NEWOs

        # Create the appropriate path where the ndjson result is to be uploaded

        synthetic_newo_df = run_get_rows_by_condition(
            source_frame=final_result_df,
            params=GetRowsByConditionParams(
                query=f"`{OrderColumns.EXECUTION_DETAILS_ORDER_STATUS}`"
                f".astype('str').str.upper() == 'NEWO'"
            ),
        )
        if synthetic_newo_df.empty:
            synthetic_newo_ndjson_path = None

        else:
            # Populate the app metrics synthetic newo count
            # See https://steeleye.atlassian.net/browse/ON-4897 for more details
            # The requirements mention explicitly that we should NOT audit synthetic NEWOs.
            # Hence, the Raw/Enriched toggle for synthetic counts will
            # be the same in Data Provenance.
            run_auditor_and_metrics_producer(
                source_frame=synthetic_newo_df,
                params=AuditorAndMetricsProducerParams(
                    metrics=[
                        Metrics(
                            query="index==index",
                            field=OrderAppMetricsEnum.OUTPUT_SYNTHETIC_NEWOS_COUNT,
                        )
                    ],
                    models=[Order],
                ),
                streamed=streamed,
                cloud_provider=cloud_provider,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

            self._audit_fallbacks(
                df=synthetic_newo_df.frame(),
                col_name=INSTRUMENT_PATH,
                fallback_key="isCreatedThroughFallback",
                status_message=StandardAuditMessages.INSTRUMENT_THROUGH_FALLBACK,
                meta_model=Order,
                models=[Order],
                streamed=streamed,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
                cloud_provider=cloud_provider,
            )
            synthetic_newo_ndjson_path = create_ndjson_path(
                tenant_bucket=tenant_bucket_with_cloud_prefix,
                aries_task_input=aries_task_input,
                model=MetaModel.ORDER,
                suffix=f"{self._get_record_type()}_synthetic_create",
            )

            # Write the transformed_df data frame into a ndjson file to the generated ndjson path
            run_write_ndjson(
                source_serializer_result=synthetic_newo_df,
                output_filepath=synthetic_newo_ndjson_path,
                audit_output=True,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

        synthetic_newo_output = add_nested_params(
            file_uri=synthetic_newo_ndjson_path,
            es_action=EsActionEnum.CREATE,
            data_model=Order.get_reference().get_qualified_reference(),
        )

        # CREATE Orders
        # It cannot be an INDEX because the feed's logic relies on proper de-duplication.
        # i.e. if we load the same file twice, the logic to retrieve the
        # tradedQuantity of one Order based on the previous day's Order bookedQuantity
        # will return invalid results which cannot be ingested into Elastic,
        # hence we cannot do upserts, neither do we need to do them

        orders_df = run_get_rows_by_condition(
            source_frame=final_result_df,
            params=GetRowsByConditionParams(
                query=f"`{OrderColumns.EXECUTION_DETAILS_ORDER_STATUS}`.str.upper() != 'NEWO'"
            ),
        )

        if orders_df.empty:
            orders_ndjson_path = None
        else:
            self._audit_fallbacks(
                df=orders_df.frame(),
                col_name=INSTRUMENT_PATH,
                fallback_key="isCreatedThroughFallback",
                status_message=StandardAuditMessages.INSTRUMENT_THROUGH_FALLBACK,
                meta_model=Order,
                models=[Order],
                streamed=streamed,
                cloud_provider=cloud_provider,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

            orders_ndjson_path = create_ndjson_path(
                tenant_bucket=tenant_bucket_with_cloud_prefix,
                aries_task_input=aries_task_input,
                model=MetaModel.ORDER,
                suffix=f"{self._get_record_type()}_create",
            )

            run_write_ndjson(
                source_serializer_result=orders_df,
                output_filepath=orders_ndjson_path,
                audit_output=True,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

        order_es_action = EsActionEnum.CREATE

        orders_output = add_nested_params(
            file_uri=orders_ndjson_path,
            es_action=order_es_action,
            data_model=Order.get_reference().get_qualified_reference(),
        )

        shutil.rmtree(tmp_storage)

        finish_flow(
            result_path=result_path,
            result_data={
                "synthetic_newo": synthetic_newo_output,
                "orders": orders_output,
            },
        )

    def link_parties_and_dedupe_by_firm_type(
        self,
        aries_task_input: AriesTaskInput,
        es_client_tenant: ElasticsearchRepository,
        transformed_df: pd.DataFrame,
        app_metrics_path: str,
        audit_path: str,
    ) -> pd.DataFrame:
        """
        PartiesMatchingConditions are used whenever the input PartyIdentifiers match multiple MyMarket Parties.
        See point 4 of https://steeleye.atlassian.net/browse/ON-5125 for more details.
        Some clients ingest MyMarket Firm records through the Aladdin Broker and Portfolio
        files with the same IDs but different FirmTypes. This means that the Firm record "123"
        with details.firmType=Client can link with the `clientIdentifiers.client` party role,
        but it cannot link with the `counterparty` party role. To ensure that behaviour, we must
        enable the PartiesMatchingConditions parameters.
        """  # noqa: E501

        return run_link_parties(
            tenant=aries_task_input.workflow.tenant,
            source_frame=transformed_df,
            params=LinkPartiesParams(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                parties_matching_condition=[
                    PartiesMatchingConditions(
                        path="clientIdentifiers.client",
                        conditions=[
                            MatchingCondition(
                                target_field="details.firmType",
                                matching_value=FirmType.CLIENT.value,
                            )
                        ],
                    ),
                    PartiesMatchingConditions(
                        path="counterparty",
                        conditions=[
                            MatchingCondition(
                                target_field="details.firmType",
                                matching_value=FirmType.COUNTERPARTY.value,
                            )
                        ],
                    ),
                ],
            ),
            es_client=es_client_tenant,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

    @staticmethod
    def instantiate_srp_es_client():
        if SRP_THROUGH_MASTER_DATA:
            # Connect to SRP through the Master Data API,
            # no elastic client is required for SRP access.
            return None

        es_client_srp: ElasticsearchRepository = get_repository_by_cluster_version(
            resource_config=get_srp_es_config()
        )
        return es_client_srp

    @staticmethod
    def _audit_fallbacks(
        df: pd.DataFrame,
        col_name: str,
        fallback_key: str,
        status_message: str,
        meta_model: Type[SteelEyeSchemaBaseModelES8],
        models: List[Type[SteelEyeSchemaBaseModelES8]],
        streamed: bool,
        cloud_provider: CloudProviderEnum,
        app_metrics_path: str,
        audit_path: str,
    ):
        instruments_created_through_fallback = (
            df.loc[:, col_name].str.get(fallback_key).fillna(False)  # type: ignore
        )
        if any(instruments_created_through_fallback):
            run_auditor_and_metrics_producer(
                source_frame=df.loc[instruments_created_through_fallback, :],
                params=AuditorAndMetricsProducerParams(
                    record_level_audits=[
                        RecordLevelAudit(
                            query="index==index",
                            status_message=status_message,
                            meta_model=meta_model,
                        )
                    ],
                    metrics=[
                        Metrics(
                            query="index==index",
                            field=OrderAppMetricsEnum.SYNTHETIC_INSTRUMENTS_COUNT,
                        )
                    ],
                    models=models,
                ),
                streamed=streamed,
                cloud_provider=cloud_provider,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

    def _get_input_columns_type_map(self):
        raise NotImplementedError

    def _get_flow_name(self):
        raise NotImplementedError

    def _get_record_type(self):
        raise NotImplementedError


def order_aladdin_v2_flow(
    flow_override_class,
    aries_task_input: AriesTaskInput,
    app_metrics_path: str,
    audit_path: str,
    result_path: str = "result.json",
):
    flow_override_class().run_flow(
        aries_task_input=aries_task_input,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
        result_path=result_path,
    )

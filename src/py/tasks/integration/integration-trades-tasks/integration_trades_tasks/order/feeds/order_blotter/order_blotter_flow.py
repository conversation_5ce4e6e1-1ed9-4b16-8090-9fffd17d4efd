# mypy: disable-error-code="attr-defined"
import addict
import os
import pandas as pd
import shutil
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_core_tasks.aries.sink_file_audit.auditor_and_metrics_producer import (
    Met<PERSON>s,
    RecordLevelAudit,
    run_auditor_and_metrics_producer,
)
from aries_se_core_tasks.aries.sink_file_audit.auditor_and_metrics_producer import (
    Params as AuditorAndMetricsProducerParams,
)
from aries_se_core_tasks.aries.sink_file_audit.static import (
    StandardAuditMessages,
    StatusDescriptionsOfSyntheticRecords,
)
from aries_se_core_tasks.aries.utility_tasks.finalize_task import (
    create_path_and_upload_model_results,
)
from aries_se_core_tasks.aries.utility_tasks.finish_flow import add_nested_params, finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.core.core_dataclasses import Serializer<PERSON><PERSON>ult
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.frame.filter_columns import run_filter_columns
from aries_se_core_tasks.frame.frame_column_manipulator import run_frame_column_manipulator
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.frame.frame_splitter import run_frame_splitter
from aries_se_core_tasks.frame.get_rows_by_condition import run_get_rows_by_condition
from aries_se_core_tasks.get_primary_transformations import run_get_primary_transformations
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.read.batch_producer import run_batch_producer
from aries_se_core_tasks.io.read.csv_file_extractor import (
    CsvFileExtractorResult,
    run_csv_file_extractor,
)
from aries_se_core_tasks.io.read.csv_file_extractor import Params as ExtractorParams
from aries_se_core_tasks.io.read.csv_file_splitter import run_csv_file_splitter
from aries_se_core_tasks.io.read.rename_csv_header import run_rename_csv_header
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.utilities.elasticsearch_utils import get_es_config, get_srp_es_config
from aries_se_trades_tasks.instrument.instrument_fallback import run_instrument_fallback
from aries_se_trades_tasks.instrument.link_instrument import run_link_instrument
from aries_se_trades_tasks.meta.assign_meta_parent import run_assign_meta_parent
from aries_se_trades_tasks.order.best_execution import run_best_execution
from aries_se_trades_tasks.order_app_metrics_enum import OrderAppMetricsEnum
from aries_se_trades_tasks.orders_and_tr.commodity_derivative_indicator import (
    run_commodity_derivative_indicator,
)
from aries_se_trades_tasks.orders_and_tr.feed.order_and_tr_blotter.instrument_overrides import (
    run_instrument_overrides,
)
from aries_se_trades_tasks.orders_and_tr.feed.order_and_tr_blotter.strike_price_type_override import (  # noqa: E501
    run_strike_price_type_override,
)
from aries_se_trades_tasks.orders_and_tr.quarantine import (
    check_if_tenant_in_quarantine_disabled_list,
    run_quarantined_records,
)
from aries_se_trades_tasks.orders_and_tr.short_selling_indicator.short_selling_indicator import (
    run_short_selling_indicator,
)
from aries_se_trades_tasks.party.link_parties import run_link_parties
from aries_se_trades_tasks.party.party_fallback_with_lei_lookup import (
    run_party_fallback_with_lei_lookup,
)
from aries_se_trades_tasks.remove_duplicates.remove_duplicate_newo import run_remove_duplicate_newo
from aries_task_link.models import AriesTaskInput
from integration_audit.auditor import AuditorStaticFields
from integration_trades_tasks.order.feeds.order_blotter.abstract_order_blotter_flow import (
    AbstractOrderBlotter,
)
from integration_trades_tasks.order.feeds.order_blotter.input_schema import (
    OrderBlotterAriesTaskInput,
)
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_core_tasks.frame.filter_columns import ActionEnum
from se_core_tasks.frame.filter_columns import Params as FilterColumnsParams
from se_core_tasks.frame.frame_column_manipulator import Action
from se_core_tasks.frame.frame_column_manipulator import Params as FrameColumnManipulatorParams
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_core_tasks.frame.frame_concatenator import Params as FrameConcatenatorParams
from se_core_tasks.frame.frame_splitter import Params as FrameSplitterParams
from se_core_tasks.frame.get_rows_by_condition import Params as GetRowsByConditionParams
from se_core_tasks.io.read.batch_producer import Params as BatchProducerParams
from se_core_tasks.io.read.csv_file_splitter import OverrideNullsParams
from se_core_tasks.io.read.csv_file_splitter import Params as CsvSplitterParams
from se_core_tasks.io.read.rename_csv_header import Params as RenameCsvHeaderParams
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8
from se_elastic_schema.models import Order, QuarantinedOrder, SinkRecordAudit
from se_elasticsearch.repository import get_repository_by_cluster_version
from se_elasticsearch.repository.elasticsearch6 import ElasticsearchRepository
from se_enums.cloud import CloudProviderEnum
from se_enums.elastic_search import EsActionEnum
from se_io_utils.tempfile_utils import tmp_directory
from se_schema_meta import PARENT
from se_trades_tasks.meta.assign_meta_parent import Params as MetaParentParams
from se_trades_tasks.order.party.fallback.party_fallback_with_lei_lookup import (
    Params as PartyFallbackParams,
)
from se_trades_tasks.order.remove_duplicates.remove_duplicate_newo import (
    Params as RemoveDuplicateNewoParams,
)
from se_trades_tasks.order.static import ModelPrefix, OrderColumns, add_prefix
from se_trades_tasks.order.transformations.universal import (
    steeleye_universal_order_blotter_schema as sc,
)
from se_trades_tasks.order.transformations.universal.static import TempColumns
from se_trades_tasks.order_and_tr.commodity_derivative_indicators import (
    Params as CommodityDerivIndParams,
)
from se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.strike_price_type_override import (  # noqa: E501
    Params as StrikePriceTypeOverrideParams,
)
from se_trades_tasks.order_and_tr.instrument.fallback.instrument_fallback import (
    INSTRUMENT_PATH,
    FallbackInstrumentAttributes,
)
from se_trades_tasks.order_and_tr.instrument.fallback.instrument_fallback import (
    Params as InstrumentFallbackParams,
)
from se_trades_tasks.order_and_tr.instrument.link.link_instrument import (
    Params as LinkInstrumentParams,
)
from se_trades_tasks.order_and_tr.party.link_parties import Params as LinkPartiesParams
from se_trades_tasks.order_and_tr.short_selling_indicator.short_selling_indicator import (
    Params as ShortSellingIndParams,
)
from se_trades_tasks.order_and_tr.static import InstrumentFields
from typing import List, Tuple, Type

# supress chained_assignment warning to avoid polluting the logs
pd.options.mode.chained_assignment = None

FETCH_MARKET_EOD_DATA = os.getenv("FETCH_MARKET_EOD_DATA", "true").lower() == "true"
SRP_THROUGH_MASTER_DATA = os.getenv("SRP_THROUGH_MASTER_DATA", "false").lower() == "true"


class DefaultOrderBlotter(AbstractOrderBlotter):
    cloud_provider: CloudProviderEnum

    def run_flow(
        self,
        aries_task_input: AriesTaskInput,
        app_metrics_path: str,
        audit_path: str,
        result_path: str,
    ):
        # SETUP #

        # Parse and validate AriesTaskInput parameters
        order_blotter_input: OrderBlotterAriesTaskInput = unpack_aries_task_input(
            aries_task_input=aries_task_input, model=OrderBlotterAriesTaskInput
        )

        # Get tenant workflow tenant config from postgres
        cached_tenant_workflow_config: addict.Dict = CachedTenantWorkflowAPIClient.get(
            stack_name=aries_task_input.workflow.stack,
            tenant_name=aries_task_input.workflow.tenant,
            workflow_name=aries_task_input.workflow.name,
        )

        streamed: bool = cached_tenant_workflow_config.workflow.streamed
        tenant: str = aries_task_input.workflow.tenant

        # Create local temporary directory to store intermediate files
        tmp_storage: str = tmp_directory().as_posix()

        # Determine Cloud Provider
        self.cloud_provider = get_cloud_provider_from_file_uri(
            file_uri=order_blotter_input.file_uri
        )
        cloud_provider_prefix = get_cloud_provider_prefix(value=self.cloud_provider)

        # Determine the Cloud Bucket of the tenant
        tenant_bucket_with_cloud_prefix: str = get_tenant_bucket(
            task_input=order_blotter_input, cloud_provider_prefix=cloud_provider_prefix
        )

        es_client_tenant: ElasticsearchRepository = get_repository_by_cluster_version(
            resource_config=get_es_config()
        )
        es_client_srp = self.instantiate_srp_es_client()

        # END SETUP #

        # BEGIN PRE-PROCESSING #

        # Temporary code change to allow processing for 4 tenants (+rohan) who send duplicate
        # columns in the file. We ALWAYS consider the FIRST occurrence of this column.

        remove_duplicated_header_columns = (
            True if tenant in {"axis", "jonestrading", "promeritum", "tavira", "rohan"} else False
        )

        # Extract the relevant subset of rows from the input CSV file and
        # produce a new smaller CSV file from it with the same header and encoding
        csv_extractor_result: CsvFileExtractorResult = self.csv_file_extractor(
            csv_file_uri=order_blotter_input.file_uri,
            target_dir=tmp_storage,
            skiprows=order_blotter_input.skiprows,
            nrows=order_blotter_input.nrows,
            remove_duplicated_header_columns=remove_duplicated_header_columns,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Uploading the current splitter result to the converter to restricted list
        # this is overridden per client basis
        self.trigger_restricted_list_converter(
            file_path=csv_extractor_result.csv_file_path,  # type: ignore[arg-type]
            aries_task_input=aries_task_input,
            realm=get_bucket(order_blotter_input.file_uri),
        )

        # Read the input CSV file, normalise its columns,
        # and convert null-like strings to real null values.
        # This Task was used in Swarm-Flows to produce multiple CSV files
        # but that behavior is not needed here
        # as we are already working with a chunk of the original input file,
        # thus we are always
        # getting the first and only element of the resulting list of FileSplitterResults.
        csv_splitter_result: FileSplitterResult = run_csv_file_splitter(
            streamed=streamed,
            params=CsvSplitterParams(
                chunksize=order_blotter_input.nrows,
                encoding=csv_extractor_result.encoding,
                normalise_columns=True,
                audit_input_rows=False,
                drop_empty_rows=False,
                use_custom_nulls=OverrideNullsParams(
                    values=["NA"],
                    columns=[
                        "Symbol",
                        "Underlying Instrument Symbol/s",
                        sc.SYMBOL.normalized_column_name,
                        sc.UNDERLYING_INSTRUMENT_SYMBOL.normalized_column_name,
                    ],
                ),
            ),
            csv_path=str(csv_extractor_result.csv_file_path),
            realm=tenant_bucket_with_cloud_prefix,
            sources_dir=tmp_storage,
        )[0]

        # As some clients cannot provide CSV files with special characters in the header,
        # we need to rename the header columns to match the schema
        rename_csv_header_result: FileSplitterResult = run_rename_csv_header(
            file_path=csv_splitter_result,
            params=RenameCsvHeaderParams(
                encoding=csv_extractor_result.encoding,
                columns_map={
                    "BUYSELL": "BUY/SELL",
                    "CFICODE": "INSTRUMENTCLASSIFICATION",
                    "DISCRETIONARYNONDISCRETIONARY": "DISCRETIONARY/NONDISCRETIONARY",
                    "EXPIRYDATE": "EXPIRYDATE/MATURITYDATE(YYYYMMDD)",
                    "EXPIRYDATE/MATURITYDATE": "EXPIRYDATE/MATURITYDATE(YYYYMMDD)",
                    "EXPIRYDATEMATURITYDATE": "EXPIRYDATE/MATURITYDATE(YYYYMMDD)",
                    "EXPIRYDATEMATURITYDATEYYYYMMDD": "EXPIRYDATE/MATURITYDATE(YYYYMMDD)",
                    "ISCFD": "ISCFD?",
                    "ISPAD": "ISPAD?",
                    "ISSPREADBET": "ISSPREADBET?",
                    # certain clients provide the Jurisdiction column with a different name
                    "JURISDICTION": "JURISDICTIONLEGALENTITY",
                    "ORDERDATE": "ORDERDATE(YYYYMMDD)",
                    "ORDERDATEYYYYMMDD": "ORDERDATE(YYYYMMDD)",
                    "ORDERTIME": "ORDERTIME(HH:MM:SS)",
                    "ORDERTIME(HHMMSS)": "ORDERTIME(HH:MM:SS)",
                    "ORDERTIMEHH:MM:SS": "ORDERTIME(HH:MM:SS)",
                    "ORDERTIMEHHMMSS": "ORDERTIME(HH:MM:SS)",
                    "ORDERTIMEAPPROVEDBYPM": "ORDERTIME-APPROVEDBYPM",
                    "SETTLEDATEYYYYMMDD": "SETTLEDATE",
                    "TRADEDATE": "TRADEDATE(YYYYMMDD)",
                    "TRADEDATEYYYYMMDD": "TRADEDATE(YYYYMMDD)",
                    "TRADETIME": "TRADETIME(HH:MM:SS)",
                    "TRADETIME(HHMMSS)": "TRADETIME(HH:MM:SS)",
                    "TRADETIMEHH:MM:SS": "TRADETIME(HH:MM:SS)",
                    "TRADETIMEHHMMSS": "TRADETIME(HH:MM:SS)",
                    "UNDERLYINGINSTRUMENTISIN": "UNDERLYINGINSTRUMENTISIN/S",
                    "UNDERLYINGINSTRUMENTISINS": "UNDERLYINGINSTRUMENTISIN/S",
                    "UNDERLYINGINSTRUMENTSYMBOL": "UNDERLYINGINSTRUMENTSYMBOL/S",
                    "UNDERLYINGINSTRUMENTSYMBOLS": "UNDERLYINGINSTRUMENTSYMBOL/S",
                },
            ),
        )

        # Run the BatchProducer Task to produce a Pandas DataFrame from the extracted CSV chunk
        # The Task also enforces datatypes, creates missing columns and
        # audits missing/unnecessary/empty columns
        input_df: pd.DataFrame = run_batch_producer(
            streamed=streamed,
            params=BatchProducerParams(
                source_schema={
                    sc.ADDITIONAL_INFORMATION.normalized_column_name: sc.ADDITIONAL_INFORMATION.column_data_type,  # noqa: E501
                    sc.AVERAGE_PRICE.normalized_column_name: sc.AVERAGE_PRICE.column_data_type,
                    sc.ATTACHMENT_POINT.normalized_column_name: sc.ATTACHMENT_POINT.column_data_type,  # noqa: E501
                    sc.BLOOMBERG_FIGI_ID.normalized_column_name: sc.BLOOMBERG_FIGI_ID.column_data_type,  # noqa: E501
                    sc.BUY_SELL.normalized_column_name: sc.BUY_SELL.column_data_type,
                    sc.CLIENT_ID.normalized_column_name: sc.CLIENT_ID.column_data_type,
                    sc.COMPLEX_TRADE_COMPONENT_ID.normalized_column_name: sc.COMPLEX_TRADE_COMPONENT_ID.column_data_type,  # noqa: E501
                    sc.COUNTERPARTY.normalized_column_name: sc.COUNTERPARTY.column_data_type,
                    sc.COUNTERPARTY_ID.normalized_column_name: sc.COUNTERPARTY_ID.column_data_type,
                    sc.CUSIP.normalized_column_name: sc.CUSIP.column_data_type,
                    sc.DATA_SOURCE_NAME.normalized_column_name: sc.DATA_SOURCE_NAME.column_data_type,  # noqa: E501
                    sc.DEBT_SENIORITY.normalized_column_name: sc.DEBT_SENIORITY.column_data_type,
                    sc.DELIVERY_TYPE.normalized_column_name: sc.DELIVERY_TYPE.column_data_type,  # noqa: E501
                    sc.DETACHMENT_POINT.normalized_column_name: sc.DETACHMENT_POINT.column_data_type,  # noqa: E501
                    sc.DISCRETIONARY_OR_NON_DISCRETIONARY.normalized_column_name: sc.DISCRETIONARY_OR_NON_DISCRETIONARY.column_data_type,  # noqa: E501
                    sc.DOC_CLAUSE.normalized_column_name: sc.DOC_CLAUSE.column_data_type,
                    sc.EVENT_DATE_TIME.normalized_column_name: sc.EVENT_DATE_TIME.column_data_type,
                    sc.EXCHANGE_MIC.normalized_column_name: sc.EXCHANGE_MIC.column_data_type,
                    sc.EXECUTING_ENTITY_ID.normalized_column_name: sc.EXECUTING_ENTITY_ID.column_data_type,  # noqa: E501
                    sc.EXPIRY_DATE_MATURITY_DATE.normalized_column_name: sc.EXPIRY_DATE_MATURITY_DATE.column_data_type,  # noqa: E501
                    sc.INDEX_SERIES.normalized_column_name: sc.INDEX_SERIES.column_data_type,
                    sc.INDEX_VERSION.normalized_column_name: sc.INDEX_VERSION.column_data_type,
                    sc.INITIAL_QUANTITY.normalized_column_name: sc.INITIAL_QUANTITY.column_data_type,  # noqa: E501
                    sc.INSTRUMENT_ASSET_CLASS.normalized_column_name: sc.INSTRUMENT_ASSET_CLASS.column_data_type,  # noqa: E501
                    sc.INSTRUMENT_CLASSIFICATION.normalized_column_name: sc.INSTRUMENT_CLASSIFICATION.column_data_type,  # noqa: E501
                    sc.INSTRUMENT_NAME.normalized_column_name: sc.INSTRUMENT_NAME.column_data_type,
                    sc.INTEREST_RATES_START_DATE.normalized_column_name: sc.INTEREST_RATES_START_DATE.column_data_type,  # noqa: E501
                    sc.INVESTMENT_DECISION_MAKER.normalized_column_name: sc.INVESTMENT_DECISION_MAKER.column_data_type,  # noqa: E501
                    sc.IS_ALLOCATION.normalized_column_name: sc.IS_ALLOCATION.column_data_type,
                    sc.IS_CFD.normalized_column_name: sc.IS_CFD.column_data_type,
                    sc.ISIN.normalized_column_name: sc.ISIN.column_data_type,
                    sc.IS_PAD.normalized_column_name: sc.IS_PAD.column_data_type,
                    sc.IS_SPREAD_BET.normalized_column_name: sc.IS_SPREAD_BET.column_data_type,
                    sc.JURISDICTION_BUSINESS_LINE.normalized_column_name: sc.JURISDICTION_BUSINESS_LINE.column_data_type,  # noqa: E501
                    sc.JURISDICTION_LEGAL_ENTITY.normalized_column_name: sc.JURISDICTION_LEGAL_ENTITY.column_data_type,  # noqa: E501
                    sc.JURISDICTION_COUNTRY.normalized_column_name: sc.JURISDICTION_COUNTRY.column_data_type,  # noqa: E501
                    sc.LIMIT_PRICE.normalized_column_name: sc.LIMIT_PRICE.column_data_type,
                    sc.LXID.normalized_column_name: sc.LXID.column_data_type,
                    sc.MONTHLY_CONTRACT_CODE.normalized_column_name: sc.MONTHLY_CONTRACT_CODE.column_data_type,  # noqa: E501
                    sc.NET_AMOUNT.normalized_column_name: sc.NET_AMOUNT.column_data_type,
                    sc.NOTIONAL_CURRENCY_1.normalized_column_name: sc.NOTIONAL_CURRENCY_1.column_data_type,  # noqa: E501
                    sc.NOTIONAL_CURRENCY_2.normalized_column_name: sc.NOTIONAL_CURRENCY_2.column_data_type,  # noqa: E501
                    sc.OPTION_STRIKE_PRICE.normalized_column_name: sc.OPTION_STRIKE_PRICE.column_data_type,  # noqa: E501
                    sc.OPTION_STYLE.normalized_column_name: sc.OPTION_STYLE.column_data_type,
                    sc.OPTION_TYPE.normalized_column_name: sc.OPTION_TYPE.column_data_type,
                    sc.ORDER_CLASS.normalized_column_name: sc.ORDER_CLASS.column_data_type,
                    sc.ORDER_DATE.normalized_column_name: sc.ORDER_DATE.column_data_type,
                    sc.ORDER_HIERARCHY.normalized_column_name: sc.ORDER_HIERARCHY.column_data_type,
                    sc.ORDER_ID.normalized_column_name: sc.ORDER_ID.column_data_type,
                    sc.ORDER_STATUS.normalized_column_name: sc.ORDER_STATUS.column_data_type,
                    sc.ORDER_TIME.normalized_column_name: sc.ORDER_TIME.column_data_type,
                    sc.ORDER_TIME_APPROVED_BY_PM.normalized_column_name: sc.ORDER_TIME_APPROVED_BY_PM.column_data_type,  # noqa: E501
                    sc.ORDER_TYPE.normalized_column_name: sc.ORDER_TYPE.column_data_type,
                    sc.PARENT_ORDER_ID.normalized_column_name: sc.PARENT_ORDER_ID.column_data_type,
                    sc.POSITION_EFFECT.normalized_column_name: sc.POSITION_EFFECT.column_data_type,
                    sc.POSITION_ID.normalized_column_name: sc.POSITION_ID.column_data_type,
                    sc.PRICE.normalized_column_name: sc.PRICE.column_data_type,
                    sc.PRICE_CURRENCY.normalized_column_name: sc.PRICE_CURRENCY.column_data_type,
                    sc.PRICE_MULTIPLIER.normalized_column_name: sc.PRICE_MULTIPLIER.column_data_type,  # noqa: E501
                    sc.PRICE_NOTATION.normalized_column_name: sc.PRICE_NOTATION.column_data_type,
                    sc.QUANTITY_CURRENCY.normalized_column_name: sc.QUANTITY_CURRENCY.column_data_type,  # noqa: E501
                    sc.QUANTITY_NOTATION.normalized_column_name: sc.QUANTITY_NOTATION.column_data_type,  # noqa: E501
                    sc.RECORD_TYPE.normalized_column_name: sc.RECORD_TYPE.column_data_type,
                    sc.RED_CODE.normalized_column_name: sc.RED_CODE.column_data_type,
                    sc.REMAINING_QUANTITY.normalized_column_name: sc.REMAINING_QUANTITY.column_data_type,  # noqa: E501
                    sc.REPO_TYPE.normalized_column_name: sc.REPO_TYPE.column_data_type,
                    sc.RIC.normalized_column_name: sc.RIC.column_data_type,
                    sc.ROUTING_STRATEGY.normalized_column_name: sc.ROUTING_STRATEGY.column_data_type,  # noqa: E501
                    sc.RUNNING_COUPON.normalized_column_name: sc.RUNNING_COUPON.column_data_type,
                    sc.SETTLE_AMOUNT.normalized_column_name: sc.SETTLE_AMOUNT.column_data_type,
                    sc.SETTLE_DATE.normalized_column_name: sc.SETTLE_DATE.column_data_type,
                    sc.STOP_PRICE.normalized_column_name: sc.STOP_PRICE.column_data_type,
                    sc.SYMBOL.normalized_column_name: sc.SYMBOL.column_data_type,
                    sc.TENOR.normalized_column_name: sc.TENOR.column_data_type,
                    sc.TIER.normalized_column_name: sc.TIER.column_data_type,
                    sc.TIME_IN_FORCE_FOR_AN_ORDER.normalized_column_name: sc.TIME_IN_FORCE_FOR_AN_ORDER.column_data_type,  # noqa: E501
                    sc.TRADE_DATE.normalized_column_name: sc.TRADE_DATE.column_data_type,
                    sc.TRADED_QUANTITY.normalized_column_name: sc.TRADED_QUANTITY.column_data_type,
                    sc.TRADE_ID.normalized_column_name: sc.TRADE_ID.column_data_type,
                    sc.TRADER_ID.normalized_column_name: sc.TRADER_ID.column_data_type,
                    sc.TRADE_TIME.normalized_column_name: sc.TRADE_TIME.column_data_type,
                    sc.TRADING_CAPACITY.normalized_column_name: sc.TRADING_CAPACITY.column_data_type,  # noqa: E501
                    sc.TRANSMISSION_OF_ORDER.normalized_column_name: sc.TRANSMISSION_OF_ORDER.column_data_type,  # noqa: E501
                    sc.TRANSMITTING_FOR.normalized_column_name: sc.TRANSMITTING_FOR.column_data_type,  # noqa: E501
                    sc.ULTIMATE_VENUE.normalized_column_name: sc.ULTIMATE_VENUE.column_data_type,
                    sc.UNDERLYING_INDEX_NAME.normalized_column_name: sc.UNDERLYING_INDEX_NAME.column_data_type,  # noqa: E501
                    sc.UNDERLYING_INDEX_NAME_LEG_2.normalized_column_name: sc.UNDERLYING_INDEX_NAME_LEG_2.column_data_type,  # noqa: E501
                    sc.UNDERLYING_INDEX_TERM.normalized_column_name: sc.UNDERLYING_INDEX_TERM.column_data_type,  # noqa: E501
                    sc.UNDERLYING_INDEX_TERM_LEG_2.normalized_column_name: sc.UNDERLYING_INDEX_TERM_LEG_2.column_data_type,  # noqa: E501
                    sc.UNDERLYING_INSTRUMENT_ISIN.normalized_column_name: sc.UNDERLYING_INSTRUMENT_ISIN.column_data_type,  # noqa: E501
                    sc.UNDERLYING_INSTRUMENT_SYMBOL.normalized_column_name: sc.UNDERLYING_INSTRUMENT_SYMBOL.column_data_type,  # noqa: E501
                    sc.VALIDITY_PERIOD.normalized_column_name: sc.VALIDITY_PERIOD.column_data_type,
                    sc.UP_FRONT_PAYMENT.normalized_column_name: sc.UP_FRONT_PAYMENT.column_data_type,  # noqa: E501
                },
                use_custom_nulls=OverrideNullsParams(
                    values=["NA"],
                    columns=[
                        sc.SYMBOL.normalized_column_name,
                        sc.UNDERLYING_INSTRUMENT_SYMBOL.normalized_column_name,
                    ],
                ),
                audit_null_columns=False,
            ),
            file_splitter_result=rename_csv_header_result,
            return_dataframe=True,
            skip_rows_count=order_blotter_input.skiprows,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # END PRE-PROCESSING #

        # BEGIN BUSINESS LOGIC #

        # Populate the majority of the target fields in a centralized transformations class
        transformed_df = self.primary_transformations(
            source_frame=input_df,
            tenant=aries_task_input.workflow.tenant,
            realm=tenant_bucket_with_cloud_prefix,
            input_file_path=order_blotter_input.file_uri,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
            es_client=es_client_tenant,
        )

        # Populate the `&parent` field for OrderStates
        meta_parent_df = run_assign_meta_parent(
            source_frame=transformed_df,
            params=MetaParentParams(
                parent_model_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER, attribute=TempColumns.META_MODEL
                ),
                parent_attributes_prefix=ModelPrefix.ORDER_DOT,
                target_attribute=add_prefix(prefix=ModelPrefix.ORDER_STATE, attribute=PARENT),
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Link identifiers built in InstrumentIdentifier from input instrument
        # data with SRP and tenant instrument data
        link_instrument_df = self.link_instruments(
            transformed_df=transformed_df,
            aries_task_input=aries_task_input,
            es_client_srp=es_client_srp,
            es_client_tenant=es_client_tenant,
        )

        # Link identifiers built in BlotterPartyIdentifiers from
        # input party data with tenant MyMarket data
        link_parties_df = run_link_parties(
            tenant=aries_task_input.workflow.tenant,
            source_frame=transformed_df,
            params=LinkPartiesParams(identifiers_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES),
            es_client=es_client_tenant,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        party_fallback_input = run_frame_concatenator(
            transformed_df=transformed_df,
            link_parties_df=link_parties_df,
            params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Create Party records embedded in the Orders for records
        # where LinkParties did not produce any hits
        party_fallback_df = run_party_fallback_with_lei_lookup(
            source_frame=party_fallback_input,
            params=PartyFallbackParams(),
        )

        short_selling_indicator_and_instr_overrides_input = run_frame_concatenator(
            transformed_df=transformed_df,
            link_instrument_df=link_instrument_df,
            input_df=input_df,
            params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Populate the "tradersAlgosWaiversIndicators.shortSellingIndicator" field
        short_selling_indicator_df = run_short_selling_indicator(
            source_frame=short_selling_indicator_and_instr_overrides_input,
            params=ShortSellingIndParams(
                buy_sell_attribute=sc.BUY_SELL.normalized_column_name,
                buy_sell_indicator_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                client_id_attribute=sc.CLIENT_ID.normalized_column_name,
                instrument_attribute="instrumentDetails.instrument",
                instrument_classification_nested_path="instrumentClassification",
                target_attribute=OrderColumns.TRADERS_ALGOS_WAIVER_INDICATORS_SHORT_SELLING_INDICATOR,
                issuer_or_operator_of_trading_venue_id_nested_path="issuerOrOperatorOfTradingVenueId",
            ),
        )

        # Populate several fields that are nested in the
        # "instrumentDetails.instrument" field
        # Essentially overriding some instrument fields
        # collected from SRP (that may be missing completely in some cases)
        instrument_overrides_df = run_instrument_overrides(
            source_frame=short_selling_indicator_and_instr_overrides_input,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Override the StrikePrice of certain instruments collected from SRP
        strike_price_type_override_df = run_strike_price_type_override(
            source_frame=instrument_overrides_df,
            params=StrikePriceTypeOverrideParams(override_strike_price_type=True),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        instrument_fallback_input = run_frame_concatenator(
            transformed_df=transformed_df,
            strike_price_type_override_df=strike_price_type_override_df,
            input_df=input_df,
            params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Create Instrument records embedded in the Orders for records
        # where LinkInstrument did not produce any hits
        instrument_fallback_df = run_instrument_fallback(
            source_frame=instrument_fallback_input,
            params=InstrumentFallbackParams(
                is_se_blotter=True,
                derived_asset_class_attribute=TempColumns.ASSET_CLASS,
                market_instrument_identifiers_attribute=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                instrument_fields_map=[
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.FB_BOND_MATURITY_DATE,
                        target_field=InstrumentFields.MATURITY_DATE,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.FB_IS_CREATED_THROUGH_FALLBACK,
                        target_field="isCreatedThroughFallback",
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.EXPIRY_DATE,
                        target_field=InstrumentFields.DERIVATIVE_EXPIRY_DATE,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.OPTION_STYLE,
                        target_field=InstrumentFields.DERIVATIVE_OPTION_EXCERCISE_STYLE,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.OPTION_TYPE,
                        target_field=InstrumentFields.DERIVATIVE_OPTION_TYPE,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=sc.PRICE_MULTIPLIER.normalized_column_name,
                        target_field=InstrumentFields.DERIVATIVE_PRICE_MULTIPLIER,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=sc.BLOOMBERG_FIGI_ID.normalized_column_name,
                        target_field="ext.figi",
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.FB_INSTRUMENT_ID_CODE_TYPE,
                        target_field=InstrumentFields.EXT_INSTRUMENT_ID_TYPE,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.FB_EXT_STRIKE_PRICE_TYPE,
                        target_field=InstrumentFields.EXT_STRIKE_PRICE_TYPE,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.FB_EXT_STRIKE_PRICE_CURRENCY,
                        target_field=InstrumentFields.DERIVATIVE_STRIKE_PRICE_CURRENCY,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.FB_EXT_NOTIONAL_CURRENCY_2_TYPE,
                        target_field="ext.notionalCurrency2Type",
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.RIC,
                        target_field="ext.pricingReferences.RIC",
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.LXID,
                        target_field="ext.pricingReferences.LXID",
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.RED_CODE,
                        target_field="ext.pricingReferences.REDCode",
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.CREDIT_ATTACHMENT_POINT,
                        target_field=InstrumentFields.DERIVATIVE_CREDIT_ATTACHMENT_POINT,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.CREDIT_DETACHMENT_POINT,
                        target_field=InstrumentFields.DERIVATIVE_CREDIT_DETACHMENT_POINT,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.CREDIT_DOC_CLAUSE,
                        target_field=InstrumentFields.DERIVATIVE_CREDIT_DOC_CLAUSE,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.CREDIT_RUNNING_COUPON,
                        target_field=InstrumentFields.DERIVATIVE_CREDIT_RUNNING_COUPON,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.CREDIT_TENOR,
                        target_field=InstrumentFields.DERIVATIVE_CREDIT_TENOR,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.CREDIT_TIER,
                        target_field=InstrumentFields.DERIVATIVE_CREDIT_TIER,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.INDEX_SERIES,
                        target_field=InstrumentFields.DERIVATIVE_UNDERLYING_INDEX_SERIES,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.INDEX_VERSION,
                        target_field=InstrumentFields.DERIVATIVE_UNDERLYING_INDEX_VERSION,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.DEBT_SENIORITY,
                        target_field="bond.debtSeniority",
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.DELIVERY_TYPE,
                        target_field=InstrumentFields.DERIVATIVE_DELIVERY_TYPE,
                    ),
                ],
                str_to_bool_dict={
                    "true": True,
                    "y": True,
                    "yes": True,
                    "t": True,
                    "on": True,
                    "false": False,
                    "n": False,
                    "no": False,
                    "f": False,
                    "off": False,
                },
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        commodity_deriv_ind_input = run_frame_concatenator(
            transformed_df=transformed_df,
            instrument_fallback_df=instrument_fallback_df,
            params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Populate the "tradersAlgosWaiversIndicators.commodityDerivativeIndicator" field
        commodity_deriv_ind_df = run_commodity_derivative_indicator(
            source_frame=commodity_deriv_ind_input,
            params=CommodityDerivIndParams(
                target_attribute=OrderColumns.TRADERS_ALGOS_WAIVER_INDICATORS_COMMODITY_DERIVATIVE_INDICATOR,
                instrument_nested_path=InstrumentFields.COMMODITIES_OR_EMISSION_ALLOWANCE_DERIVATIVE_IND,
                instrument_attribute="instrumentDetails.instrument",
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Concat all relevant DataFrames and discard temporary
        # columns that must not be part of the final result
        aux_df = run_frame_concatenator(
            transformed_df=transformed_df,
            commodity_deriv_ind_df=commodity_deriv_ind_df,
            meta_parent_df=meta_parent_df,
            party_fallback_df=party_fallback_df,
            instrument_fallback_df=instrument_fallback_df,
            short_selling_indicator_df=short_selling_indicator_df,
            params=FrameConcatenatorParams(
                orient=OrientEnum.horizontal,
                drop_columns=[
                    TempColumns.ASSET_CLASS,
                    OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                    OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                    TempColumns.CLIENT_ID,
                    TempColumns.COUNTERPARTY_ID,
                    TempColumns.INSTRUMENT_VENUE,
                    TempColumns.TRADER_ID,
                    TempColumns.SYMBOL,
                    "__order_received_date_time__",
                    "__trading_date_time__",
                    TempColumns.CASH,
                    TempColumns.DELIVERY_TYPE,
                    TempColumns.EXPIRY_DATE,
                    TempColumns.OPTION_STRIKE_PRICE,
                    TempColumns.OPTION_STYLE,
                    TempColumns.OPTION_TYPE,
                    TempColumns.VALIDITY_PERIOD,
                    TempColumns.INSTRUMENT_CLASSIFICATION,
                    TempColumns.INSTRUMENT_FULL_NAME,
                    TempColumns.INSTRUMENT_NOTIONAL_CURRENCY_2,
                    TempColumns.INSTRUMENT_SYMBOL,
                    TempColumns.ISIN,
                    TempColumns.EXT_STRIKE_PRICE_TYPE,
                    TempColumns.FB_INSTRUMENT_ID_CODE_TYPE,
                    TempColumns.META_MODEL,
                    "__fb_instrumentFullName__",
                    "__fb_instrumentFullName__",
                    "__fb_venue_tradingVenue__",
                    TempColumns.FB_EXT_NOTIONAL_CURRENCY_2_TYPE,
                    TempColumns.FB_EXT_STRIKE_PRICE_TYPE,
                    TempColumns.FB_EXT_STRIKE_PRICE_CURRENCY,
                    TempColumns.FB_IS_CREATED_THROUGH_FALLBACK,
                    TempColumns.IS_CFD,
                    TempColumns.FB_BOND_MATURITY_DATE,
                    TempColumns.RECORD_TYPE_FROM_RECORD_TYPE,
                    TempColumns.RECORD_TYPE_FROM_IS_ALLOCATION,
                    TempColumns.RIC,
                    TempColumns.LXID,
                    TempColumns.DEBT_SENIORITY,
                    TempColumns.RED_CODE,
                    TempColumns.CREDIT_ATTACHMENT_POINT,
                    TempColumns.CREDIT_DETACHMENT_POINT,
                    TempColumns.CREDIT_DOC_CLAUSE,
                    TempColumns.INDEX_VERSION,
                    TempColumns.INDEX_SERIES,
                    TempColumns.CREDIT_RUNNING_COUPON,
                    TempColumns.CREDIT_TENOR,
                    "__fallback_buyer__",
                    "__fallback_seller__",
                    "__fallback_counterparty__",
                    "__fallback_client__",
                    "__fallback_buyer_dec_maker__",
                    "__fallback_seller_dec_maker__",
                    "__fallback_inv_dec_in_firm__",
                    "__fallback_trader__",
                    "__fallback_exec_within_firm__",
                    "__fallback_executing_entity__",
                    "asset_class_attribute",
                    "bbg_figi_id_attribute",
                    "eurex_id_attribute",
                    "currency_attribute",
                    "expiry_date_attribute",
                    "interest_rate_start_date_attribute",
                    "isin_attribute",
                    "notional_currency_1_attribute",
                    "notional_currency_2_attribute",
                    "option_strike_price_attribute",
                    "option_type_attribute",
                    "swap_near_leg_date_attribute",
                    "underlying_index_name_attribute",
                    "underlying_index_name_leg_2_attribute",
                    "underlying_index_series_attribute",
                    "underlying_index_term_attribute",
                    "underlying_index_term_value_attribute",
                    "underlying_index_version_attribute",
                    "underlying_isin_attribute",
                    "underlying_symbol_attribute",
                    "underlying_symbol_expiry_code_attribute",
                    "underlying_index_term_leg_2_attribute",
                    "underlying_index_term_value_leg_2_attribute",
                    "venue_attribute",
                    "venue_financial_instrument_short_name_attribute",
                    "instrument_classification_attribute",
                    TempColumns.UNDERLYING_INDEX_TERM,
                    TempColumns.UNDERLYING_INDEX_TERM_VALUE,
                ],
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Split the columns pertaining to Orders to a separate DataFrame
        order_records_df = run_frame_splitter(
            source_frame=aux_df,
            params=FrameSplitterParams(
                except_prefix=ModelPrefix.ORDER_STATE_DOT, strip_prefix=True
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Strip the Order prefix
        parsed_order_records_df = run_frame_column_manipulator(
            source_frame=order_records_df,
            params=FrameColumnManipulatorParams(action=Action.strip, prefix=ModelPrefix.ORDER_DOT),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Split the columns pertaining to OrderStates to a separate DataFrame
        order_state_records_df = run_frame_splitter(
            source_frame=aux_df,
            params=FrameSplitterParams(except_prefix=ModelPrefix.ORDER_DOT, strip_prefix=True),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Strip the OrderState prefix
        parsed_order_state_records_df = run_frame_column_manipulator(
            source_frame=order_state_records_df,
            params=FrameColumnManipulatorParams(
                action=Action.strip, prefix=ModelPrefix.ORDER_STATE_DOT
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Concat a final DataFrame that contains all Order +
        # OrderState records without any temporary columns/prefixes
        orders_and_order_states_df = run_frame_concatenator(
            parsed_order_records_df=parsed_order_records_df,
            parsed_order_state_records_df=parsed_order_state_records_df,
            params=FrameConcatenatorParams(
                orient=OrientEnum.vertical, reset_index=True, drop_index=True
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # In the below GetRowsByCondition we'll drop all records that are missing
        # executionDetails.orderStatus Before dropping them, we must audit them to
        # ensure that we're auditing the input count correctly.
        run_auditor_and_metrics_producer(
            source_frame=orders_and_order_states_df,
            params=AuditorAndMetricsProducerParams(
                record_level_audits=[
                    RecordLevelAudit(
                        query="`executionDetails.orderStatus`.isnull() and ~`__newo_in_file_col__`",
                        status_message="Invalid Record since it's missing order status",
                        audit_field=AuditorStaticFields.ERRORED,
                        meta_model=Order,
                    )
                ],
                metrics=[
                    Metrics(
                        query="`executionDetails.orderStatus`.isnull() and ~`__newo_in_file_col__`",
                        field=GenericAppMetricsEnum.ERRORED_COUNT,
                    )
                ],
                models=[Order],
            ),
            streamed=streamed,
            cloud_provider=self.cloud_provider,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Exclude all records that do not have a value in the "executionDetails.orderStatus" field
        # Such records are either missing from the input data column `ORDERSTATUS` or they have
        # an invalid Order Status which is not part of the map defined in the PrimaryTransformations
        filtered_orders_and_order_states_df = run_get_rows_by_condition(
            source_frame=orders_and_order_states_df,
            params=GetRowsByConditionParams(query="`executionDetails.orderStatus`.notnull()"),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Remove duplicate NEWOs from dataset (i.e. synthetic NEWOs that were created unnecessarily)
        deduplicated_data_df = run_remove_duplicate_newo(
            source_frame=filtered_orders_and_order_states_df,
            params=RemoveDuplicateNewoParams(
                newo_in_file_col=TempColumns.NEWO_IN_FILE_COL,
                drop_newo_in_file_col=False,
            ),
            tenant=aries_task_input.workflow.tenant,
            es_client=es_client_tenant,
            streamed=streamed,
            cloud_provider=self.cloud_provider,
            audit_path=audit_path,
            app_metrics_path=app_metrics_path,
        )

        best_execution_df = run_best_execution(
            source_frame=deduplicated_data_df,
            es_client=es_client_tenant,
            fetch_market_eod_data=FETCH_MARKET_EOD_DATA,
            streamed=streamed,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Note that we are not discarding the `&parent` column here
        # this can be propagated to the ApplyMeta Conductor Task, which will reuse it
        final_result_df = run_frame_concatenator(
            best_execution_df=best_execution_df,
            deduplicated_data_df=deduplicated_data_df,
            params=FrameConcatenatorParams(
                orient=OrientEnum.horizontal, drop_columns=[TempColumns.META_MODEL]
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # CREATE SYNTHETIC NEWOs

        # Create the appropriate path where the ndjson result is to be uploaded

        synthetic_newo_df = run_get_rows_by_condition(
            source_frame=final_result_df,
            params=GetRowsByConditionParams(
                query=f"`{OrderColumns.EXECUTION_DETAILS_ORDER_STATUS}`"
                f".astype('str').str.upper() == 'NEWO' & "
                f"`{TempColumns.NEWO_IN_FILE_COL}`.astype('str').str.lower() == 'false'"  # noqa: E501
            ),
        )
        if synthetic_newo_df.empty:
            synthetic_newo_ndjson_path = None

        else:
            synthetic_newo_df = run_filter_columns(
                source_frame=synthetic_newo_df,
                params=FilterColumnsParams(
                    columns=[TempColumns.NEWO_IN_FILE_COL], action=ActionEnum.drop
                ),
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
                skip_serializer=True,
            )

            # Audit each synthetic NEWO and populate the app metrics synthetic newo count
            run_auditor_and_metrics_producer(
                source_frame=synthetic_newo_df,
                params=AuditorAndMetricsProducerParams(
                    record_level_audits=[
                        RecordLevelAudit(
                            query="index==index",
                            status_message=StatusDescriptionsOfSyntheticRecords.IS_SYNTHETIC_NEWO,
                            meta_model=Order,
                        )
                    ],
                    metrics=[
                        Metrics(
                            query="index==index",
                            field=OrderAppMetricsEnum.OUTPUT_SYNTHETIC_NEWOS_COUNT,
                        )
                    ],
                    models=[Order],
                ),
                streamed=streamed,
                cloud_provider=self.cloud_provider,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

            self._audit_fallbacks(
                df=synthetic_newo_df,
                col_name=INSTRUMENT_PATH,
                fallback_key="isCreatedThroughFallback",
                status_message=StandardAuditMessages.INSTRUMENT_THROUGH_FALLBACK,
                meta_model=Order,
                models=[Order],
                streamed=streamed,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
                cloud_provider=self.cloud_provider,
            )
            synthetic_newo_ndjson_path = create_ndjson_path(
                tenant_bucket=tenant_bucket_with_cloud_prefix,
                aries_task_input=aries_task_input,
                model=MetaModel.ORDER,
                suffix="synthetic",
            )

            # Write the transformed_df data frame into a ndjson file to the generated ndjson path
            run_write_ndjson(
                source_serializer_result=synthetic_newo_df,
                output_filepath=synthetic_newo_ndjson_path,
                audit_output=True,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

        synthetic_newo_output = add_nested_params(
            file_uri=synthetic_newo_ndjson_path,
            es_action=EsActionEnum.CREATE,
            data_model=Order.get_reference().get_qualified_reference(),
        )

        # INDEX Orders

        orders_df = run_get_rows_by_condition(
            source_frame=final_result_df,
            params=GetRowsByConditionParams(
                query=f"(`{OrderColumns.EXECUTION_DETAILS_ORDER_STATUS}`.astype('str').str.upper() == 'NEWO' & `{TempColumns.NEWO_IN_FILE_COL}`.astype('str').str.lower() == 'true') | (`{OrderColumns.EXECUTION_DETAILS_ORDER_STATUS}`.str.upper() != 'NEWO')"  # noqa: E501
            ),
        )

        orders_df = run_filter_columns(
            source_frame=orders_df,
            params=FilterColumnsParams(
                columns=[TempColumns.NEWO_IN_FILE_COL], action=ActionEnum.drop
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        orders_df, quarantined_order_df, sink_record_audit_df, order_es_action = (
            self.get_quarantine_orders(
                source_frame=orders_df,
                tenant=tenant,
                aries_task_input=aries_task_input,
                es_client=es_client_tenant,
                audit_path=audit_path,
                app_metrics_path=app_metrics_path,
            )
        )

        if orders_df.empty:
            orders_ndjson_path = None
        else:
            self._audit_fallbacks(
                df=orders_df.frame(),
                col_name=INSTRUMENT_PATH,
                fallback_key="isCreatedThroughFallback",
                status_message=StandardAuditMessages.INSTRUMENT_THROUGH_FALLBACK,
                meta_model=Order,
                models=[Order],
                streamed=streamed,
                cloud_provider=self.cloud_provider,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

            orders_ndjson_path = create_ndjson_path(
                tenant_bucket=tenant_bucket_with_cloud_prefix,
                aries_task_input=aries_task_input,
                model=MetaModel.ORDER,
            )

            run_write_ndjson(
                source_serializer_result=orders_df,
                output_filepath=orders_ndjson_path,
                audit_output=True,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

        orders_output = add_nested_params(
            file_uri=orders_ndjson_path,
            es_action=order_es_action,
            data_model=Order.get_reference().get_qualified_reference(),
        )

        shutil.rmtree(tmp_storage)

        quarantined_output = create_path_and_upload_model_results(
            final_transformed_df=quarantined_order_df,
            aries_task_input=aries_task_input,
            tenant_bucket=tenant_bucket_with_cloud_prefix,
            model=QuarantinedOrder,
            es_action=EsActionEnum.INDEX,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        sink_record_audit_output = create_path_and_upload_model_results(
            final_transformed_df=sink_record_audit_df,
            aries_task_input=aries_task_input,
            tenant_bucket=tenant_bucket_with_cloud_prefix,
            model=SinkRecordAudit,
            es_action=EsActionEnum.INDEX,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        finish_flow(
            result_path=result_path,
            result_data={
                "synthetic_newo": synthetic_newo_output,
                "orders": orders_output,
                MetaModel.QUARANTINED_ORDER: quarantined_output,
                MetaModel.SINK_RECORD_AUDIT: sink_record_audit_output,
            },
        )

    @staticmethod
    def instantiate_srp_es_client():
        if SRP_THROUGH_MASTER_DATA:
            return None
        es_client_srp: ElasticsearchRepository = get_repository_by_cluster_version(
            resource_config=get_srp_es_config()
        )
        return es_client_srp

    @staticmethod
    def primary_transformations(
        source_frame: pd.DataFrame,
        tenant: str,
        realm: str,
        input_file_path: str,
        app_metrics_path: str,
        audit_path: str,
        es_client,
    ):
        return run_get_primary_transformations(
            source_frame=source_frame,
            flow="order_blotter",
            tenant=tenant,
            realm=realm,
            input_file_path=input_file_path,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
            es_client=es_client,
        )

    @staticmethod
    def csv_file_extractor(
        csv_file_uri: str,
        target_dir: str,
        skiprows: int,
        nrows: int,
        remove_duplicated_header_columns: bool,
        app_metrics_path: str,
        audit_path: str,
    ) -> CsvFileExtractorResult:
        return run_csv_file_extractor(
            csv_file_uri=csv_file_uri,
            target_dir=target_dir,
            params=ExtractorParams(
                skiprows=skiprows,
                nrows=nrows,
                populate_input_count_metrics=True,
                remove_duplicated_header_columns=remove_duplicated_header_columns,
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

    def link_instruments(
        self,
        transformed_df: pd.DataFrame | SerializerResult,
        aries_task_input: AriesTaskInput,
        es_client_srp,
        es_client_tenant,
        *args,
        **kwargs,
    ) -> pd.DataFrame:
        return run_link_instrument(  # type: ignore
            source_frame=transformed_df,  # type: ignore
            params=LinkInstrumentParams(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                asset_class_attribute=TempColumns.ASSET_CLASS,
                currency_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                venue_attribute=TempColumns.INSTRUMENT_VENUE,
            ),
            tenant=aries_task_input.workflow.tenant,
            es_client_srp=es_client_srp,
            es_client_tenant=es_client_tenant,
        )

    def get_quarantine_orders(
        self,
        source_frame: pd.DataFrame,
        tenant: str,
        aries_task_input,
        es_client,
        audit_path: str,
        app_metrics_path: str,
    ) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame, EsActionEnum]:
        orders_df, quarantined_order_df, sink_record_audit_df = run_quarantined_records(
            source_frame=source_frame,
            tenant=tenant,
            model=Order,
            cloud_provider=self.cloud_provider,
            aries_task_input=aries_task_input,
            es_client=es_client,
            audit_path=audit_path,
            app_metrics_path=app_metrics_path,
        )

        # If tenant in disabled quarantined logic list then we
        # overwrite the order record to the latest value
        if check_if_tenant_in_quarantine_disabled_list(tenant=tenant):
            order_es_action = EsActionEnum.INDEX
        else:
            order_es_action = EsActionEnum.CREATE

        return orders_df, quarantined_order_df, sink_record_audit_df, order_es_action

    def trigger_restricted_list_converter(
        self,
        aries_task_input: AriesTaskInput,
        realm: str,
        file_path: str,
    ) -> None:
        """this method is intended to only have logic in overrides."""
        pass

    @staticmethod
    def _audit_fallbacks(
        df: pd.DataFrame,
        col_name: str,
        fallback_key: str,
        status_message: str,
        meta_model: Type[SteelEyeSchemaBaseModelES8],
        models: List[Type[SteelEyeSchemaBaseModelES8]],
        streamed: bool,
        cloud_provider: CloudProviderEnum,
        app_metrics_path: str,
        audit_path: str,
    ):
        instruments_created_through_fallback = (
            df.loc[:, col_name].str.get(fallback_key).fillna(False)  # type: ignore
        )
        if any(instruments_created_through_fallback):
            run_auditor_and_metrics_producer(
                source_frame=df.loc[instruments_created_through_fallback, :],
                params=AuditorAndMetricsProducerParams(
                    record_level_audits=[
                        RecordLevelAudit(
                            query="index==index",
                            status_message=status_message,
                            meta_model=meta_model,
                        )
                    ],
                    metrics=[
                        Metrics(
                            query="index==index",
                            field=OrderAppMetricsEnum.SYNTHETIC_INSTRUMENTS_COUNT,
                        )
                    ],
                    models=models,
                ),
                streamed=streamed,
                cloud_provider=cloud_provider,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )


def order_blotter_flow(
    flow_override_class,
    aries_task_input: AriesTaskInput,
    app_metrics_path: str,
    audit_path: str,
    result_path: str = "result.json",
):
    flow_override_class().run_flow(
        aries_task_input=aries_task_input,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
        result_path=result_path,
    )

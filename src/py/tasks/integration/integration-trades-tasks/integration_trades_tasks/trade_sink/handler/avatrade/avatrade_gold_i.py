# mypy: disable-error-code="no-any-return"
import pandas as pd
import re
from addict import Dict
from integration_trades_tasks.trade_sink.fix.fix_parser import TradeSinkFixParser
from integration_trades_tasks.trade_sink.fix.fix_util import fix_getter
from integration_trades_tasks.trade_sink.utils import static
from integration_trades_tasks.trade_sink.utils import trade_util as util
from integration_trades_tasks.trade_sink.utils.base_classes.trade_handler import (
    AbstractTradeHandler,
)
from integration_trades_tasks.trade_sink.utils.dict_util import del_none
from integration_trades_tasks.trade_sink.utils.exception import SkipRecord
from integration_trades_tasks.trade_sink.utils.iterators import batch_iterator
from integration_trades_tasks.trade_sink.utils.static import ORD_STATUS_MAP
from typing import List, NamedTuple

AVATRADE_EU_LEI = "635400B4JMEKVIH72416"
LEI = "LEI"
ACCOUNT = "Account"
NA = "N/A"

FF_60_DATETIME_FORMAT = "%Y%m%d-%H:%M:%S"
ABACI_DATETIME_FORMAT = "%Y-%m-%dT%H:%M:%S.%fZ"
ABACI_DATE_FORMAT = "%Y-%m-%d"


class Side:
    BUY = "1"
    SELL = "2"


class Conversion(NamedTuple):
    ids: List[str]
    divisor: float


PRICE_CONVERSIONS = [
    Conversion(ids=["COFFEE", "CORN", "COTTON", "SOYBEAN", "SUGAR", "WHEAT"], divisor=100.0),
]


class TradeHandler(AbstractTradeHandler):
    """handler for Gold-I Fix message files each file is expected to contain a
    single Fix message."""

    def __init__(self, content, event, firm, data_source):
        super().__init__(content, event, firm, data_source)
        self.tenant_id = "MT4_1_"

    def iterator(self, **kwargs):
        return batch_iterator(self.event, self.content)

    @staticmethod
    def validate_input_record(fget):
        if fget(35) not in ["8", "AE"]:
            raise SkipRecord(f"unexpected MsgType(FF35): {fget(35)}")
        # below added in ON-5384 to skip any record containing 'to #' or 'from #' on field 58_Text
        if re.match(pattern=r"(to|from) #", string=str(fget(58))):
            raise SkipRecord(f"58_Text contains 'to/from #': {fget(58)}")

    def transform(self, record):
        parser = TradeSinkFixParser()
        parser.parse(record)
        fix_msg = parser.export()
        fget = fix_getter(fix_msg)
        self.validate_input_record(fget)
        order_status = self.get_order_status(fget)
        ex_reports = [
            self.execution_report(fget, ORD_STATUS_MAP.NEWO),
            self.execution_report(fget, order_status),
        ]
        return ex_reports, []

    @staticmethod
    def get_order_status(fget) -> str:
        status = "2"
        if fget(11004) == "1":
            status = static.ORD_STATUS_MAP.PARF
        elif fget(150) == "5":
            status = static.ORD_STATUS_MAP.REME

        return status

    def trade_capture_report(self, fget) -> dict:
        report = Dict(
            {
                "TradeID": self.trade_id(fget),
                "LastMkt": "XXXX",
                "Instrument": {
                    "SecurityExchange": "XXXX",
                    "SecurityID": fget(55, default="").upper().replace(".SBG", ""),
                    "SecurityIDSource": self.tenant_id,
                },
                "TrdRegTimestamps": self.kronos(fget),
                "Currency": fget(15),
                "PriceType": "MONE",
                "LastPx": self.last_price(fget),
                "SteelEyeParties": self.patty(fget),
                "TrdCapRptSideGrp": [
                    {
                        "PositionEffect": fget(77),
                        "Side": self.get_side(fget),
                        "TradeReportOrderDetail": {"OrderCapacity": "G"},
                    }
                ],
                "Text": self.get_additional_text(fget),
                "AvgPx": fget(31, typ="double"),
                "SettlementAmountCurrency": fget(120),
            }
        )
        report.transactionDetails.priceAverage = fget(31, typ="double")
        report.PositionEffect = fget(77)
        report.SettlementAmount = self.get_settlement_amount(fget)

        if fget(461) == "FFCPNO":
            report.QtyType = "MONE"
        if fget(461) == "FFCPNO":
            report.Instrument.PriceQuoteCurrency = fget(120)
        return del_none(report.to_dict())

    @staticmethod
    def get_settlement_amount(fget) -> float:
        ff_119 = fget(119, typ="double", default=0.0)
        ff_152 = fget(152, typ="double", default=0.0)
        return ff_152 * ff_119 if fget(461) == "FFSPNO" else ff_119

    def execution_report(self, fget, order_status) -> dict:
        order_qty = fget(32, typ="abs")

        report = Dict(self.trade_capture_report(fget))
        report.executionDetails.orderStatus = order_status
        report.OrderID = self.order_id(fget)

        report.MsgType = None
        report.OrdStatus = order_status
        report.OrdType = fget(40)

        report.OrderQtyData.OrderQty = order_qty
        report.CumQty = 0.0 if is_newo(order_status) else order_qty
        report.LeavesQty = order_qty if is_newo(order_status) else 0.0
        report.LastQty = 0.0 if is_newo(order_status) else order_qty
        if fget(139) == "3" and fget(137, tup="abs") > 0:
            report.commissionAmount = fget(137, tup="abs")

        if fget(461) == "FFSPNO":
            report.Instrument.ContractMultiplier = fget(152, typ="abs")
        if is_newo(order_status):
            report = self.remove_fields_from_newo(report)
        return report.to_dict()

    @staticmethod
    def last_price(fget) -> float:
        price = fget(31, typ="abs")
        if price == 0:
            return price

        for conversion in PRICE_CONVERSIONS:
            if any(c in fget(55) for c in conversion.ids):
                price /= conversion.divisor
        return price

    @staticmethod
    def remove_fields_from_newo(report: Dict) -> Dict:
        """remove fields not needed in NEWO execution reports."""
        # TODO - del timestamp with type 1
        report.SettlementAmount = None
        report.SettlementAmountCurrency = None
        report.TradeID = None
        report.AvgPx = None
        report.LastQty = None
        report.Instrument.PriceQuoteCurrency = None
        report.PositionEffect = None
        report.SecurityExchange = None
        report.LastMkt = None
        return Dict(del_none(report.to_dict()))

    @staticmethod
    def is_newo(order_status: str) -> bool:
        return order_status == ORD_STATUS_MAP.NEWO

    @staticmethod
    def get_additional_text(fget) -> str:
        parts = [
            f"Info: {fget(58, default=NA)}",
            f"Source System: {fget(578, default=NA)}",
            f"Settlement FX Rate: {fget(155, default=NA)}",
        ]
        if fget(447) == "1":
            parts.append(f"Executing Process: {fget(448)}")

        commission_amount = NA
        if fget(139) == "3" and fget(137, tup="abs") > 0:
            commission_amount = fget(137, tup="abs")
        parts.append(f"Commission: {commission_amount}")
        return ", ".join(parts)

    @staticmethod
    def kronos(fget) -> list:
        ff_60_datetime = dtm(fget(60), FF_60_DATETIME_FORMAT)
        ff_60_date_only = date(fget(60), FF_60_DATETIME_FORMAT)
        ff_75_datetime = dtm(fget(75), FF_60_DATETIME_FORMAT)
        ff_75_date_only = date(fget(75), FF_60_DATETIME_FORMAT)

        if not fget(150) == "0":
            return [
                util.kronos_block(ff_60_datetime, "1"),
                util.kronos_block(ff_60_datetime, "3"),
                util.kronos_block(ff_60_datetime, "4"),
                util.kronos_block(ff_60_date_only, "98"),
            ]
        else:
            return [
                util.kronos_block(ff_75_datetime, "1"),
                util.kronos_block(ff_60_datetime, "3"),
                util.kronos_block(ff_60_datetime, "4"),
                util.kronos_block(ff_75_date_only, "98"),
            ]

    @staticmethod
    def get_side(fget):
        return fget(54)

    def trade_id(self, fget) -> str:
        return self.join_tags(fget, 1, 11, 17, 54, sep="|")

    def order_id(self, fget) -> str:
        order_id = self.join_tags(fget, 1, 11, sep="|")
        side = {"1": "Buy", "2": "Sell"}.get(fget(54), fget(54))
        open_close = "OPEN" if str(fget(77)).upper() == "O" else "CLOSE"
        return "|".join([order_id, side, open_close])

    def patty(self, fget):
        buyers, sellers = [], []
        side = self.get_side(fget)
        if side == Side.BUY:
            buyers.append(util.party(AVATRADE_EU_LEI, LEI, "27"))
            sellers.append(util.party(fget(1), ACCOUNT, "27"))
        elif side == Side.SELL:
            sellers.append(util.party(AVATRADE_EU_LEI, LEI, "27"))
            buyers.append(util.party(fget(1), ACCOUNT, "27"))

        counterparty = util.party(AVATRADE_EU_LEI, LEI, "27")
        firm_decision_maker = util.party("TM", "ID", "122")
        firm_executioner = util.party("TM", "ID", "12")
        trader = util.party(fget(1), ACCOUNT, "12")
        executing_firm = util.party(AVATRADE_EU_LEI, LEI, "1")
        return dict(
            Buyer=buyers,
            Seller=sellers,
            Trader=trader,
            ExecutingFirm=executing_firm,
            Counterparty=counterparty,
            ExecutionWithinFirm=firm_executioner,
            DecisionMakerWithinFirm=firm_decision_maker,
        )

    @staticmethod
    def join_tags(fget, *tags, sep=""):
        return sep.join(fget(tag) for tag in tags)


def dtm(datetime, fmt):
    return pd.to_datetime(datetime, format=fmt).strftime(ABACI_DATETIME_FORMAT)


def date(datetime, fmt):
    return pd.to_datetime(datetime, format=fmt).strftime(ABACI_DATE_FORMAT)


def is_newo(order_status: str) -> bool:
    return order_status == ORD_STATUS_MAP.NEWO

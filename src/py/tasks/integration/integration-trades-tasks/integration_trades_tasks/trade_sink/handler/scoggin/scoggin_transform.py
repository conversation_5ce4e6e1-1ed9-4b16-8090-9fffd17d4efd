# mypy: disable-error-code="override, attr-defined, assignment, empty-body, return, call-arg"
import logging
import pytz
from integration_trades_tasks.trade_sink.handler.matrix import matrix_transform
from integration_trades_tasks.trade_sink.handler.matrix.matrix_transform import get_side
from integration_trades_tasks.trade_sink.utils import static
from integration_trades_tasks.trade_sink.utils import trade_util as utl
from integration_trades_tasks.trade_sink.utils.exception import SkipRecord
from integration_trades_tasks.trade_sink.utils.static import PartyLabel, RecordType
from typing import List, Tuple

NUM_CONFIG = dict(typ="abs", default=0.0)
logger = logging.getLogger(__name__)

SKIP_BROKERS = [
    "ANOSN",
    "BARCN",
    "BNCH<PERSON>",
    "BTGN",
    "BTIGN",
    "CANAN",
    "CANL",
    "CHLMN",
    "CIBCN",
    "COWNN",
    "CSFBN",
    "GSCON",
    "JEFFN",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>IN",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON>AT<PERSON>",
    "TR<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "VI<PERSON>N",
]


class BaseFileType(matrix_transform.BaseFileType):
    TENANT_ID: str = "Scoggin Capital"
    TIME_ZONE: str = pytz.timezone("EST")

    def patty(self, rget) -> dict:
        buyers, sellers = list(), list()

        firm = utl.party_before_role(identifier=self.TENANT_ID, label=PartyLabel.ID)
        trader = utl.party_before_role(identifier=rget("USERDEFINED_1"), label=PartyLabel.ID)
        counterparty = utl.party_before_role(identifier=rget("BROKER"), label=PartyLabel.ID)
        inv_dec_maker = utl.party_before_role(identifier=rget("MANAGER"), label=PartyLabel.ID)

        if get_side(rget) in static.BUY:
            buyers.append(firm("27"))
            sellers.append(counterparty("27"))
        else:
            sellers.append(firm("27"))
            buyers.append(counterparty("27"))

        return dict(
            Buyer=buyers,
            Seller=sellers,
            ExecutingFirm=firm("1"),
            Counterparty=counterparty("27"),
            Trader=trader("27"),
            DecisionMakerWithinFirm=inv_dec_maker("122"),
        )


class Execution(matrix_transform.Execution, BaseFileType):
    def run_transform(self, record: dict) -> Tuple[List[dict], List[dict]]:
        return BaseFileType.run_transform(self, record)

    def skip_logic(self, rget):
        if not self.order_id(rget):
            raise SkipRecord("Missing Order Id")
        if str(rget("BROKER")).upper() in SKIP_BROKERS:
            raise SkipRecord(f"BROKER is one of {SKIP_BROKERS}")

    def get_record_type(self, rget) -> str:
        return RecordType.MARKET_SIDE


class Allocation(matrix_transform.Allocation, BaseFileType):
    def run_transform(self, record: dict) -> Tuple[List[dict], List[dict]]:
        return BaseFileType.run_transform(self, record)

    def skip_logic(self, rget):
        if not self.order_id(rget):
            raise SkipRecord("Missing Order Id")
        if str(rget("BROKER")).upper() in SKIP_BROKERS:
            raise SkipRecord(f"BROKER is one of {SKIP_BROKERS}")

    def get_record_type(self, rget) -> str:
        return RecordType.ALLOCATION


class Order(matrix_transform.Order, BaseFileType):
    def run_transform(self, record: dict) -> Tuple[List[dict], List[dict]]:
        return BaseFileType.run_transform(self, record)

    def skip_logic(self, rget):
        if not self.order_id(rget):
            raise SkipRecord("Missing Order Id")
        if str(rget("BROKER")).upper() in SKIP_BROKERS:
            raise SkipRecord(f"BROKER is one of {SKIP_BROKERS}")

    def get_record_type(self, rget) -> str:
        return RecordType.MARKET_SIDE

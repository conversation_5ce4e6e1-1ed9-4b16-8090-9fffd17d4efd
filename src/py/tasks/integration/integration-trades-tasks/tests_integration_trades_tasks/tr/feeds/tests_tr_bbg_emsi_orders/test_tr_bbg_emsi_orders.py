# mypy: disable-error-code="union-attr, misc, attr-defined, no-any-return"
import boto3
import fsspec
import json
import pandas as pd
import pytest
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_trades_tasks.tr.tr_app_metrics_enum import TRAppMetricsEnum
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from freezegun import freeze_time
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_test_utils.model_validation.validation_errors import (
    assert_record_is_schema_compliant,
)
from integration_trades_tasks.tr.feeds.tr_bbg_emsi_orders import (
    tr_bbg_emsi_orders_flow as tr_bbg_emsi_orders_flow_module,
)
from integration_trades_tasks.tr.feeds.tr_bbg_emsi_orders.tr_bbg_emsi_orders_flow_task import (
    tr_bbg_emsi_orders_run,
)
from integration_wrapper.static import StaticFields
from moto import mock_aws
from pathlib import Path
from se_elastic_schema.models import QuarantinedRTS22Transaction, RTS22Transaction, SinkRecordAudit
from se_elastic_schema.steeleye_record_validations.error_codes import SteeleyeRecordErrorCodesEnum
from se_elasticsearch.repository import ResourceConfig
from se_enums.elastic_search import EsActionEnum
from se_io_utils.json_utils import read_json, write_named_temporary_json
from se_io_utils.tempfile_utils import tmp_directory
from typing import Dict, Tuple

CURRENT_PATH = Path(__file__).parent
DATA_PATH = CURRENT_PATH.joinpath("data")
BUCKET_NAME = "test.dev.steeleye.co"
LOCAL_BUCKET_PATH = DATA_PATH.joinpath("buckets", BUCKET_NAME)

EXPECTED_RESULTS_PATH = DATA_PATH.joinpath("expected_results")
FRIDAY_SCENARIO = EXPECTED_RESULTS_PATH.joinpath("friday_file")
FRIDAY_OVERRIDE_SCENARIO = EXPECTED_RESULTS_PATH.joinpath("friday_file_override")
NON_FRIDAY_SCENARIO = EXPECTED_RESULTS_PATH.joinpath("non_friday_file")
GLTH_SCENARIO = EXPECTED_RESULTS_PATH.joinpath("glth_file")
CSCS_SCENARIO = EXPECTED_RESULTS_PATH.joinpath("cscs_file")
SI_MTF_SCENARIO = EXPECTED_RESULTS_PATH.joinpath("si_mtf_file")
EU_DESK_SCENARIO = EXPECTED_RESULTS_PATH.joinpath("eu_desk_file")

TEMP_DIR: Path = tmp_directory()
AUDIT_PATH: Path = TEMP_DIR.joinpath("audit.json")

mock_aiobotocore_convert_to_response_dict()


class TestTrBBGEMSIOrders:
    @staticmethod
    def teardown_method():
        # remove files after each test
        if AUDIT_PATH.exists():
            AUDIT_PATH.unlink()

    @pytest.mark.parametrize(
        "aries_task_input,scenario,expected_metrics,expected_audit",
        [
            (
                "aries_task_input_aws_friday_file",
                FRIDAY_SCENARIO,
                (20, 0, 20, 20),
                "audit_friday_file",
            ),
            (
                "aries_task_input_aws_friday_file_override",
                FRIDAY_OVERRIDE_SCENARIO,
                (20, 0, 20, 20),
                "audit_friday_file",
            ),
            (
                "aries_task_input_aws_non_friday_file",
                NON_FRIDAY_SCENARIO,
                (10, 0, 14, 10),
                "audit_non_friday_file",
            ),
            ("aries_task_input_aws_glth_file", GLTH_SCENARIO, (5, 4, 1, 1), "audit_glth_cscs_file"),  # noqa: E501
            ("aries_task_input_aws_cscs_file", CSCS_SCENARIO, (5, 4, 1, 1), "audit_glth_cscs_file"),  # noqa: E501
            ("aries_task_input_si_mtf_file", SI_MTF_SCENARIO, (4, 0, 4, 4), "audit_si_mtf_file"),
            ("aries_task_input_eu_desk_file", EU_DESK_SCENARIO, (5, 0, 4, 4), "audit_eu_desk_file"),  # noqa: E501
        ],
    )
    @mock_aws
    def test_scenarios(
        self,
        mocker,
        aries_task_input: AriesTaskInput,
        scenario: Path,
        expected_metrics: Tuple[int],
        expected_audit: Dict,
        link_parties_si_mtf_result,
        request,
    ):
        get_input = request.getfixturevalue(aries_task_input)

        link_parties_df = (
            link_parties_si_mtf_result if scenario == SI_MTF_SCENARIO else pd.DataFrame()
        )

        aries_task_result = self._run_aries_task(
            mocker=mocker, aries_task_input=get_input, link_parties_scroll_result=link_parties_df
        )

        # Assert the NDJSONs produced by the Flow contains the expected data
        for result, expected_result in self._get_result_dfs_and_validate(
            aries_task_result=aries_task_result, path=scenario
        ):
            pd.testing.assert_frame_equal(left=result, right=expected_result)

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "tr_bbg_emsi_orders"
            ]["tr_bbg_emsi_orders"]
            if aries_task_result.app_metric
            else {}
        )

        assert aries_task_result.output_param.params == {
            "RTS22Transaction": {
                "params": {
                    "file_uri": aries_task_result.output_param.params.get("RTS22Transaction")
                    .get("params")
                    .get("file_uri"),
                    "es_action": EsActionEnum.CREATE.value,
                    "data_model": RTS22Transaction.get_reference().get_qualified_reference(),
                    "ignore_empty_file_uri": True,
                }
            },
            "QuarantinedRTS22Transaction": {
                "params": {
                    "file_uri": aries_task_result.output_param.params.get(
                        "QuarantinedRTS22Transaction"
                    )
                    .get("params")
                    .get("file_uri"),
                    "es_action": EsActionEnum.INDEX.value,
                    "data_model": QuarantinedRTS22Transaction.get_reference().get_qualified_reference(),  # noqa: E501
                    "ignore_empty_file_uri": True,
                }
            },
            "SinkRecordAudit": {
                "params": {
                    "file_uri": aries_task_result.output_param.params.get("SinkRecordAudit")
                    .get("params")
                    .get("file_uri"),
                    "es_action": EsActionEnum.INDEX.value,
                    "data_model": SinkRecordAudit.get_reference().get_qualified_reference(),
                    "ignore_empty_file_uri": True,
                }
            },
        }

        assert metrics[GenericAppMetricsEnum.INPUT_COUNT] == expected_metrics[0]
        assert metrics[GenericAppMetricsEnum.SKIPPED_COUNT] == expected_metrics[1]
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == expected_metrics[2]
        assert metrics[TRAppMetricsEnum.SYNTHETIC_INSTRUMENTS_COUNT] == expected_metrics[3]

        audit = read_json(AUDIT_PATH.as_posix())

        assert audit == request.getfixturevalue(expected_audit)

    def test_joined_order_fills_empty(
        self, mocker, aries_task_input_joined_results_empty: AriesTaskInput
    ):
        """Test for the case where after orders and fills are joined, we get an
        empty data frame.

        There is nothing to be processed in this case, and the flow just
        runs with a workflow-level audit and None outputs.
        """
        aries_task_result = self._run_aries_task(
            mocker=mocker, aries_task_input=aries_task_input_joined_results_empty
        )
        audit = read_json(AUDIT_PATH.as_posix())
        assert aries_task_result.output_param.params == {
            "RTS22Transaction": {
                "params": {
                    "file_uri": None,
                    "es_action": "create",
                    "data_model": RTS22Transaction.get_reference().get_qualified_reference(),
                    "ignore_empty_file_uri": True,
                }
            },
            "QuarantinedRTS22Transaction": {
                "params": {
                    "file_uri": None,
                    "es_action": "index",
                    "data_model": QuarantinedRTS22Transaction.get_reference().get_qualified_reference(),  # noqa: E501
                    "ignore_empty_file_uri": True,
                }
            },
            "SinkRecordAudit": {
                "params": {
                    "file_uri": None,
                    "es_action": "index",
                    "data_model": SinkRecordAudit.get_reference().get_qualified_reference(),
                    "ignore_empty_file_uri": True,
                }
            },
        }

        assert audit == {
            "input_records": {},
            "workflow_status": ["Processing skipped as no valid transactions exist in the file"],
        }

    @staticmethod
    def _get_result_dfs_and_validate(aries_task_result: AriesTaskResult, path: Path):
        """Function which gets the results and expected results for all the
        types of keys, and also validates output Order records against the
        schema."""
        for output_key in {"RTS22Transaction", "QuarantinedRTS22Transaction", "SinkRecordAudit"}:
            output_uri: str = (
                aries_task_result.output_param.params.get(output_key).get("params").get("file_uri")
            )

            if not output_uri:
                continue

            local_file_path: str = run_download_file(
                file_url=output_uri,
            )

            result: pd.DataFrame = pd.read_json(local_file_path, lines=True)
            expected_result = pd.read_json(
                path.joinpath(f"expected_result_{output_key}.ndjson").as_posix(),
                lines=True,
            )

            if output_key == "RTS22Transaction":
                assert_record_is_schema_compliant(
                    input_df=result,
                    model=RTS22Transaction,
                    accepted_validation_error_codes=[
                        SteeleyeRecordErrorCodesEnum.SE_DV_58,  # Instrument validations
                        SteeleyeRecordErrorCodesEnum.SE_DV_97,
                        SteeleyeRecordErrorCodesEnum.SE_DV_99,  # Parties validations
                        SteeleyeRecordErrorCodesEnum.SE_DV_104,
                        SteeleyeRecordErrorCodesEnum.SE_DV_160,
                        SteeleyeRecordErrorCodesEnum.SE_DV_162,
                        SteeleyeRecordErrorCodesEnum.SE_DV_186,
                        SteeleyeRecordErrorCodesEnum.SE_DV_188,
                        SteeleyeRecordErrorCodesEnum.SE_DV_189,
                        SteeleyeRecordErrorCodesEnum.SE_DV_190,
                        SteeleyeRecordErrorCodesEnum.SE_DV_205,
                        SteeleyeRecordErrorCodesEnum.SE_DV_301,  # Venue validation
                    ],
                )
            yield result, expected_result

    @staticmethod
    @mock_aws
    @freeze_time(time_to_freeze="2024-06-25 08:00:00.000000+00:00")
    def _run_aries_task(
        mocker,
        aries_task_input: AriesTaskInput,
        link_parties_scroll_result: pd.DataFrame = pd.DataFrame(),
    ) -> AriesTaskResult:
        # Create mock S3 bucket and add objects to it
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME)

        # replace write_named_temporary_json side effect
        write_named_temporary_json_mock = mocker.patch(
            "integration_wrapper.integration_aries_task.write_named_temporary_json"
        )
        write_named_temporary_json_mock.side_effect = write_named_temporary_json_side_effect

        mock_search_instruments_cached = mocker.patch(
            "se_trades_tasks.order_and_tr.instrument.link.link_instrument._search_instruments_cached"
        )
        mock_search_instruments_cached.return_value = []

        mock_get_es_config = mocker.patch.object(tr_bbg_emsi_orders_flow_module, "get_es_config")
        mock_get_es_config.return_value = ResourceConfig(
            host="localhost",
            port=9999,
            scheme="http",
        )

        mock_get_es_srp_config = mocker.patch.object(
            tr_bbg_emsi_orders_flow_module, "get_srp_es_config"
        )
        mock_get_es_srp_config.return_value = ResourceConfig(
            host="localhost",
            port=9999,
            scheme="http",
            meta_prefix="&",
        )

        mock_es_repo = mocker.patch.object(
            tr_bbg_emsi_orders_flow_module, "get_repository_by_cluster_version"
        )

        class MockEsObject:
            def __init__(self, link_parties_scroll_result: pd.DataFrame = pd.DataFrame()):
                self.MAX_TERMS_SIZE = 1024
                self.MAX_QUERY_SIZE = 1024
                self.meta = addict.Dict(model="&model")
                self.link_parties_scroll_result = link_parties_scroll_result

            def scroll(self, *args, **kwargs):
                index = kwargs.get("index", "")

                if index == "test-rts22_transaction-alias":
                    ids = kwargs["query"]["query"]["bool"]["should"][0]["terms"]["&id"]

                    non_friday_quarantine_ids = [
                        "139955882024070820240708BUYI:2024-07-08:NEWT",
                        "139955892024070820240708BUYI:2024-07-08:NEWT",
                    ]

                    common_ids = set(ids).intersection(non_friday_quarantine_ids)

                    if common_ids:
                        return pd.DataFrame(
                            {
                                "&hash": ["hash1", "hash2"],
                                "&id": non_friday_quarantine_ids,
                                "&key": [
                                    "RTS22Transaction:139955882024070820240708BUYI:2024-07-08:NEWT:*************",
                                    "RTS22Transaction:139955892024070820240708BUYI:2024-07-08:NEWT:*************",
                                ],
                            }
                        )
                elif not self.link_parties_scroll_result.empty and index == (
                    "test-account_firm-alias,"
                    "test-market_counterparty-alias,"
                    "test-account_person-alias,"
                    "test-market_person-alias"
                ):
                    return self.link_parties_scroll_result
                return pd.DataFrame()

            @staticmethod
            def search(*args, **kwargs):
                return {"hits": {"hits": [{"_source": addict.Dict()}]}}

        mock_es_repo.return_value = MockEsObject(
            link_parties_scroll_result=link_parties_scroll_result
        )
        mocker.patch("integration_audit.auditor.write_json")
        mocker.patch.object(
            target=CachedTenantWorkflowAPIClient,
            attribute="get",
            return_value=addict.Dict(
                {
                    "tenant": {
                        "lake_prefix": "s3://test.dev.steeleye.co",
                    },
                    "workflow": {"streamed": False},
                    "max_batch_size": 10000,
                },
            ),
        )

        # Run flow
        return tr_bbg_emsi_orders_run(aries_task_input=aries_task_input)  # type: ignore


def create_and_add_objects_to_s3_bucket(bucket_name: str):
    """Creates a mock s3 bucket and copies the input file(s) to it.

    :param bucket_name: Bucket name of Mock S3 bucket
    :return: None
    """

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")
    s3.create_bucket(Bucket=bucket_name)

    for file_ in LOCAL_BUCKET_PATH.rglob("*"):
        if file_.is_file():
            _path = file_.as_posix().replace(f"{LOCAL_BUCKET_PATH}/", "")

            with open(file_, "rb") as f:
                s3.put_object(Bucket=bucket_name, Key=_path, Body=f.read())


def write_named_temporary_json_side_effect(output_filename: str, **kwargs) -> str:
    if output_filename == "audit.json":
        with fsspec.open(AUDIT_PATH.as_posix(), "w") as file:
            json.dump({}, file)

        return AUDIT_PATH.as_posix()

    return write_named_temporary_json(output_filename=output_filename, **kwargs)

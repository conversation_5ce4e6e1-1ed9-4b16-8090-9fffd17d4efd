# ruff: noqa: E501
import datetime
import os
import pandas as pd
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from integration_wrapper.static import IntegrationAriesTaskVariables
from typing import Dict

# This needs to be here since the conftest is loaded first
# than any import on the test files
os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)
os.environ["COGNITO_CLIENT_ID"] = "some_id"
os.environ["COGNITO_CLIENT_SECRET"] = "some_secret"
os.environ["COGNITO_AUTH_URL"] = "some_auth_url"
os.environ["MASTER_DATA_HOST"] = "some_host"
os.environ["MASTER_API_DATA_HOST"] = "some_host"

BUKET_PATH = (
    "s3://test.dev.steeleye.co/aries/ingress/depository/tr_bbg_emsi_orders/"
    "file_splitter/2024/07/24/trace_id/random_hash"
)


@pytest.fixture()
def aries_task_input_aws_friday_file() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="trace_id",
        name="tr_bbg_emsi_orders",
        stack="dev-shared-2",
        tenant="test",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri=f"{BUKET_PATH}/friday_STEELEYE_EMSI_ORDERS_CHEL_30859241_20240705_batch_0.csv",
            source_file_uri=(
                "s3://test.dev.steeleye.co/aries/ingress/depository/nonstreamed/"
                "tr_bbg_emsi_orders/source_file.csv"
            ),
        )
    )
    task = TaskFieldSet(name="tr_bbg_emsi_orders", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def aries_task_input_aws_friday_file_override() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="trace_id",
        name="tr_bbg_emsi_orders",
        stack="dev-shared-2",
        tenant="chelverton",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri=f"{BUKET_PATH}/friday_STEELEYE_EMSI_ORDERS_CHEL_30859241_20240705_batch_0.csv",
            source_file_uri=(
                "s3://test.dev.steeleye.co/aries/ingress/depository/nonstreamed/"
                "tr_bbg_emsi_orders/source_file.csv"
            ),
        )
    )
    task = TaskFieldSet(name="tr_bbg_emsi_orders", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def aries_task_input_aws_non_friday_file() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="trace_id",
        name="tr_bbg_emsi_orders",
        stack="dev-shared-2",
        tenant="test",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri=f"{BUKET_PATH}/non_friday_STEELEYE_EMSI_ORDERS_CHEL_30859241_20240708_batch_0.csv",
            source_file_uri=(
                "s3://test.dev.steeleye.co/aries/ingress/depository/nonstreamed/"
                "tr_bbg_emsi_orders/source_file.csv"
            ),
        )
    )
    task = TaskFieldSet(name="tr_bbg_emsi_orders", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def aries_task_input_aws_glth_file() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="trace_id",
        name="tr_bbg_emsi_orders",
        stack="dev-shared-2",
        tenant="chelverton",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri=f"{BUKET_PATH}/hash_STEELEYE_EMSI_GLTH_31744242_20240723_batch_0.csv",
            source_file_uri=(
                "s3://test.dev.steeleye.co/aries/ingress/depository/nonstreamed/"
                "tr_bbg_emsi_orders/source_glth_file.csv"
            ),
        )
    )
    task = TaskFieldSet(name="tr_bbg_emsi_orders", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def aries_task_input_aws_cscs_file() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="trace_id",
        name="tr_bbg_emsi_orders",
        stack="dev-shared-2",
        tenant="chelverton",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri=f"{BUKET_PATH}/hash_STEELEYE_EMSI_CSCS_31744242_20240723_batch_0.csv",
            source_file_uri=(
                "s3://test.dev.steeleye.co/aries/ingress/depository/nonstreamed/"
                "tr_bbg_emsi_orders/source_cscs_file.csv"
            ),
        )
    )
    task = TaskFieldSet(name="tr_bbg_emsi_orders", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def aries_task_input_joined_results_empty() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="trace_id",
        name="tr_bbg_emsi_orders",
        stack="dev-shared-2",
        tenant="test",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri=f"{BUKET_PATH}/join_empty_STEELEYE_EMSI_GLTH_31744242_20240820_batch_0.csv",
            source_file_uri=(
                "s3://test.dev.steeleye.co/aries/ingress/depository/nonstreamed/"
                "tr_bbg_emsi_orders/source_file.csv"
            ),
        )
    )
    task = TaskFieldSet(name="tr_bbg_emsi_orders", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def aries_task_input_si_mtf_file() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="trace_id",
        name="tr_bbg_emsi_orders",
        stack="dev-shared-2",
        tenant="test",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri=f"{BUKET_PATH}/si_mtf_STEELEYE_EMSI_ORDERS_CHEL_30859241_20240816_batch_0.csv",
            source_file_uri=(
                "s3://test.dev.steeleye.co/aries/ingress/depository/nonstreamed/"
                "tr_bbg_emsi_orders/source_si_mtf_file.csv"
            ),
        )
    )
    task = TaskFieldSet(name="tr_bbg_emsi_orders", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def aries_task_input_eu_desk_file() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="trace_id",
        name="tr_bbg_emsi_orders",
        stack="dev-shared-2",
        tenant="test",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri=f"{BUKET_PATH}/eu_desk_STEELEYE_EMSI_ORDERS_IRIS_30859241_20250522_batch_0.csv",
            source_file_uri=(
                "s3://test.dev.steeleye.co/aries/ingress/depository/nonstreamed/"
                "tr_bbg_emsi_orders/eu_desk_STEELEYE_EMSI_ORDERS_IRIS_30859241_20250522.csv"
            ),
        )
    )
    task = TaskFieldSet(name="tr_bbg_emsi_orders", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def audit_friday_file() -> Dict:
    return {
        "input_records": {
            "139954762024070520240705BUYI:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139954842024070520240705SELL:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139954902024070520240705SELL:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139954972024070520240705BUYI:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139954982024070520240705BUYI:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955022024070520240705SELL:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955032024070520240705SELL:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955042024070520240705SELL:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955122024070520240705SELL:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955242024070520240705SELL:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955292024070520240705SELL:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955302024070520240705SELL:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955312024070520240705SELL:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955322024070520240705SELL:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955332024070520240705SELL:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955362024070520240705SELL:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955492024070520240705BUYI:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955502024070520240705BUYI:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955512024070520240705BUYI:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955682024070520240705BUYI:2024-07-05:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
        },
        "workflow_status": [
            " {'input_total_count': 20}",
            "Unknown columns in data source {'error': 'Unknown columns', 'columns': ['AMOUNT', 'ASOFDATE', 'ASOFTIME', 'ASSETCLASS', 'BASKETNAME', 'BBGID', 'BLOCKID', 'CFDFLAG', 'CONTRACTEXPIRATION', 'CREATEDATE', 'CREATETIME', 'CUSIP', 'CUSTOMNOTE1', 'CUSTOMNOTE2', 'CUSTOMNOTE3', 'CUSTOMNOTE4', 'CUSTOMNOTE5', 'DAYAVERAGEPRICE', 'DAYFILLEDAMOUNT', 'EXCHANGE', 'EXECINSTRUCTIONS', 'EXTRA_1', 'EXTRA_10', 'EXTRA_11', 'EXTRA_12', 'EXTRA_13', 'EXTRA_14', 'EXTRA_15', 'EXTRA_16', 'EXTRA_17', 'EXTRA_18', 'EXTRA_19', 'EXTRA_2', 'EXTRA_3', 'EXTRA_4', 'EXTRA_5', 'EXTRA_6', 'EXTRA_7', 'EXTRA_8', 'EXTRA_9', 'GTDDATE', 'HANDLINGINSTR', 'INDIABSEAVGPRICE', 'INDIABSEFILLED', 'INDIAMCXAVGPRICE', 'INDIAMCXFILLED', 'INDIANSEAVGPRICE', 'INDIANSEFILLED', 'INSTRUCTIONS', 'INVESTORID', 'LASTFILLDATE', 'LIMITPRICE', 'LONGFUTURENAME', 'OCC_SYMBOL', 'ORDER', 'ORDERORIGIN', 'ORDERREFID', 'ORDERTYPE', 'PARSEKEY', 'POSITION', 'SEDOL', 'SETTLEMENTCURRENCY', 'SETTLEMENTDATE', 'SETTLEMENTTYPE', 'STOPPRICE', 'TICKER', 'TICKER+EXCHANGE', 'TIF', 'WORKINGAMOUNT', 'YELLOWKEY']}",
        ],
    }


@pytest.fixture()
def audit_non_friday_file() -> Dict:
    return {
        "input_records": {
            "139955692024070820240708BUYI:2024-07-08:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955702024070820240708SELL:2024-07-08:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955712024070820240708BUYI:2024-07-08:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955722024070820240708BUYI:2024-07-08:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955732024070820240708SELL:2024-07-08:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955812024070820240708SELL:2024-07-08:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955842024070820240708SELL:2024-07-08:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955872024070820240708BUYI:2024-07-08:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955882024070820240708BUYI:2024-07-08:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139955892024070820240708BUYI:2024-07-08:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
        },
        "workflow_status": [
            " {'input_total_count': 10}",
            "Unknown columns in data source {'error': 'Unknown columns', 'columns': ['AMOUNT', 'ASOFDATE', 'ASOFTIME', 'ASSETCLASS', 'BASKETNAME', 'BBGID', 'BLOCKID', 'CFDFLAG', 'CONTRACTEXPIRATION', 'CREATEDATE', 'CREATETIME', 'CUSIP', 'CUSTOMNOTE1', 'CUSTOMNOTE2', 'CUSTOMNOTE3', 'CUSTOMNOTE4', 'CUSTOMNOTE5', 'DAYAVERAGEPRICE', 'DAYFILLEDAMOUNT', 'EXCHANGE', 'EXECINSTRUCTIONS', 'GTDDATE', 'HANDLINGINSTR', 'INDIABSEAVGPRICE', 'INDIABSEFILLED', 'INDIAMCXAVGPRICE', 'INDIAMCXFILLED', 'INDIANSEAVGPRICE', 'INDIANSEFILLED', 'INSTRUCTIONS', 'INVESTORID', 'LASTFILLDATE', 'LIMITPRICE', 'LONGFUTURENAME', 'OCC_SYMBOL', 'ORDER', 'ORDERORIGIN', 'ORDERREFID', 'ORDERTYPE', 'PARSEKEY', 'POSITION', 'SEDOL', 'SETTLEMENTCURRENCY', 'SETTLEMENTDATE', 'SETTLEMENTTYPE', 'STOPPRICE', 'TICKER', 'TICKER+EXCHANGE', 'TIF', 'WORKINGAMOUNT', 'YELLOWKEY']}",
        ],
    }


@pytest.fixture()
def audit_si_mtf_file() -> Dict:
    return {
        "input_records": {
            "139960552024081620240816SELL:2024-08-16:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139960562024081620240816SELL:2024-08-16:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139960692024081620240816BUYI:2024-08-16:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "139960702024081620240816BUYI:2024-08-16:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
        },
        "workflow_status": [
            " {'input_total_count': 4}",
            "Unknown columns in data source {'error': 'Unknown columns', 'columns': ['AMOUNT', 'ASOFDATE', 'ASOFTIME', 'ASSETCLASS', 'BASKETNAME', 'BBGID', 'BLOCKID', 'CFDFLAG', 'CONTRACTEXPIRATION', 'CREATEDATE', 'CREATETIME', 'CUSIP', 'CUSTOMNOTE1', 'CUSTOMNOTE2', 'CUSTOMNOTE3', 'CUSTOMNOTE4', 'CUSTOMNOTE5', 'DAYAVERAGEPRICE', 'DAYFILLEDAMOUNT', 'EXCHANGE', 'EXECINSTRUCTIONS', 'EXTRA_1', 'EXTRA_10', 'EXTRA_11', 'EXTRA_12', 'EXTRA_13', 'EXTRA_14', 'EXTRA_15', 'EXTRA_16', 'EXTRA_17', 'EXTRA_18', 'EXTRA_19', 'EXTRA_2', 'EXTRA_3', 'EXTRA_4', 'EXTRA_5', 'EXTRA_6', 'EXTRA_7', 'EXTRA_8', 'EXTRA_9', 'GTDDATE', 'HANDLINGINSTR', 'INDIABSEAVGPRICE', 'INDIABSEFILLED', 'INDIAMCXAVGPRICE', 'INDIAMCXFILLED', 'INDIANSEAVGPRICE', 'INDIANSEFILLED', 'INSTRUCTIONS', 'INVESTORID', 'LASTFILLDATE', 'LIMITPRICE', 'LONGFUTURENAME', 'OCC_SYMBOL', 'ORDER', 'ORDERORIGIN', 'ORDERREFID', 'ORDERTYPE', 'PARSEKEY', 'POSITION', 'SEDOL', 'SETTLEMENTCURRENCY', 'SETTLEMENTDATE', 'SETTLEMENTTYPE', 'STOPPRICE', 'TICKER', 'TICKER+EXCHANGE', 'TIF', 'WORKINGAMOUNT', 'YELLOWKEY']}",
        ],
    }


@pytest.fixture()
def audit_eu_desk_file() -> Dict:
    return {
        "input_records": {
            "139972482024111220241112BUYI:2024-11-12:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "duplicate": 0,
                    "errored": 0,
                    "skipped": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                    "updated": 0,
                }
            },
            "139972502024102820241028SELL:2024-10-28:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "duplicate": 0,
                    "errored": 0,
                    "skipped": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                    "updated": 0,
                }
            },
            "139979992024102820241028BUYI:2024-10-28:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "duplicate": 0,
                    "errored": 0,
                    "skipped": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                    "updated": 0,
                }
            },
            "239972452024102820241028BUYI:2024-10-28:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "duplicate": 0,
                    "errored": 0,
                    "skipped": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                    "updated": 0,
                }
            },
        },
        "workflow_status": [
            " {'input_total_count': 5}",
            "Unknown columns in data source {'error': 'Unknown columns', 'columns': ['AMOUNT', 'ASOFDATE', 'ASOFTIME', 'ASSETCLASS', 'BASKETNAME', 'BBGID', 'BLOCKID', 'CFDFLAG', 'CONTRACTEXPIRATION', 'CREATEDATE', 'CREATETIME', 'CUSIP', 'CUSTOMNOTE1', 'CUSTOMNOTE2', 'CUSTOMNOTE3', 'CUSTOMNOTE4', 'CUSTOMNOTE5', 'DAYAVERAGEPRICE', 'DAYFILLEDAMOUNT', 'EXCHANGE', 'EXECINSTRUCTIONS', 'EXTRA_1', 'EXTRA_10', 'EXTRA_11', 'EXTRA_12', 'EXTRA_13', 'EXTRA_14', 'EXTRA_15', 'EXTRA_16', 'EXTRA_17', 'EXTRA_18', 'EXTRA_19', 'EXTRA_2', 'EXTRA_3', 'EXTRA_4', 'EXTRA_5', 'EXTRA_6', 'EXTRA_7', 'EXTRA_8', 'EXTRA_9', 'GTDDATE', 'HANDLINGINSTR', 'INDIABSEAVGPRICE', 'INDIABSEFILLED', 'INDIAMCXAVGPRICE', 'INDIAMCXFILLED', 'INDIANSEAVGPRICE', 'INDIANSEFILLED', 'INSTRUCTIONS', 'INVESTORID', 'LASTFILLDATE', 'LIMITPRICE', 'LONGFUTURENAME', 'OCC_SYMBOL', 'ORDER', 'ORDERORIGIN', 'ORDERREFID', 'ORDERTYPE', 'PARSEKEY', 'POSITION', 'SEDOL', 'SETTLEMENTCURRENCY', 'SETTLEMENTDATE', 'SETTLEMENTTYPE', 'STOPPRICE', 'TICKER', 'TICKER+EXCHANGE', 'TIF', 'WORKINGAMOUNT', 'YELLOWKEY']}",
        ],
    }


@pytest.fixture()
def audit_glth_cscs_file() -> Dict:
    return {
        "input_records": {
            "13995695": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"`TRADERNOTES`.astype('str').str.upper().str.contains('GLTH|CSCS')\""
                    ],
                }
            },
            "13995696": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"`TRADERNOTES`.astype('str').str.upper().str.contains('GLTH|CSCS')\""
                    ],
                }
            },
            "13995697": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"`TRADERNOTES`.astype('str').str.upper().str.contains('GLTH|CSCS')\""
                    ],
                }
            },
            "13995716": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"`TRADERNOTES`.astype('str').str.upper().str.contains('GLTH|CSCS')\""
                    ],
                }
            },
            "139957112024072320240723SELL:2024-07-23:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
        },
        "workflow_status": [
            " {'input_total_count': 5}",
            "Unknown columns in data source {'error': 'Unknown columns', 'columns': ['AMOUNT', 'ASOFDATE', 'ASOFTIME', 'ASSETCLASS', 'BASKETNAME', 'BBGID', 'BLOCKID', 'CFDFLAG', 'CONTRACTEXPIRATION', 'CREATEDATE', 'CREATETIME', 'CUSIP', 'CUSTOMNOTE1', 'CUSTOMNOTE2', 'CUSTOMNOTE3', 'CUSTOMNOTE4', 'CUSTOMNOTE5', 'DAYAVERAGEPRICE', 'DAYFILLEDAMOUNT', 'EXCHANGE', 'EXECINSTRUCTIONS', 'EXTRA_1', 'EXTRA_10', 'EXTRA_11', 'EXTRA_12', 'EXTRA_13', 'EXTRA_14', 'EXTRA_15', 'EXTRA_16', 'EXTRA_17', 'EXTRA_18', 'EXTRA_19', 'EXTRA_2', 'EXTRA_3', 'EXTRA_4', 'EXTRA_5', 'EXTRA_6', 'EXTRA_7', 'EXTRA_8', 'EXTRA_9', 'GTDDATE', 'HANDLINGINSTR', 'INDIABSEAVGPRICE', 'INDIABSEFILLED', 'INDIAMCXAVGPRICE', 'INDIAMCXFILLED', 'INDIANSEAVGPRICE', 'INDIANSEFILLED', 'INSTRUCTIONS', 'INVESTORID', 'LASTFILLDATE', 'LIMITPRICE', 'LONGFUTURENAME', 'OCC_SYMBOL', 'ORDER', 'ORDERORIGIN', 'ORDERREFID', 'ORDERTYPE', 'PARSEKEY', 'POSITION', 'SEDOL', 'SETTLEMENTCURRENCY', 'SETTLEMENTDATE', 'SETTLEMENTTYPE', 'STOPPRICE', 'TICKER', 'TICKER+EXCHANGE', 'TIF', 'WORKINGAMOUNT', 'YELLOWKEY']}",
        ],
    }


@pytest.fixture()
def link_parties_si_mtf_result() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "sourceKey": None,
                "&uniqueProps": ["id:dug", "lei:5493002sj850i228s657", "account:dug"],
                "&id": "700c5bcd-3fbb-4749-b659-c7946d5cf6b8",
                "&traitFqn": pd.NA,
                "&ancestor": "MarketCounterparty:700c5bcd-3fbb-4749-b659-c7946d5cf6b8:*************",
                "firmLocation": pd.NA,
                "fileIdentifier": pd.NA,
                "&key": "MarketCounterparty:700c5bcd-3fbb-4749-b659-c7946d5cf6b8:*************",
                "sourceIndex": None,
                "uniqueIds": ["id:dug", "lei:5493002sj850i228s657", "account:dug"],
                "&model": "MarketCounterparty",
                "&version": 6,
                "name": "BI-Fonds G35 Z-FN",
                "firmCommunications": pd.NA,
                "client": pd.NA,
                "emirDetails": pd.NA,
                "&timestamp": *************,
                "&user": "<EMAIL>",
                "&hash": "ae6301a9a48daf1436e2a97c21a1b697ed2ee2642022ca2bf499a52ccaed17f3",
                "firmIdentifiers.nationalBusinessIdentityNo": pd.NA,
                "firmIdentifiers.lei": "5493002SJ850I228S657",
                "firmIdentifiers.bloombergCode": pd.NA,
                "firmIdentifiers.internalRefNo": pd.NA,
                "firmIdentifiers.isIsda": pd.NA,
                "firmIdentifiers.mic": "TRUK",
                "firmIdentifiers.ecbCode": pd.NA,
                "firmIdentifiers.bic": pd.NA,
                "firmIdentifiers.branchCountry": "GB",
                "firmIdentifiers.deaAccess": pd.NA,
                "firmIdentifiers.kycApproved": pd.NA,
                "details.tradableInstruments": pd.NA,
                "details.firmStatus": "ACTIVE",
                "details.legalType": pd.NA,
                "details.inEEA": pd.NA,
                "details.retailOrProfessional": "N/A",
                "details.clientMandate": pd.NA,
                "details.decisionMaker": pd.NA,
                "details.executionWithinFirm": pd.NA,
                "details.firmSize": pd.NA,
                "details.industry": pd.NA,
                "details.leiRegistrationStatus": "ISSUED",
                "details.parentOfCollectiveInvestmentSchema": pd.NA,
                "details.mifidEnrollmentDate": pd.NA,
                "details.firmType": "Client",
                "details.mifidRegistered": None,
                "details.firmNCA": pd.NA,
                "details.isEmirDelegatedReporting": pd.NA,
                "details.orgType": "Systematic Internaliser",
                "details.positionHolderInFirm": pd.NA,
                "details.smcrFirmType": pd.NA,
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"id": "DUG", "label": "id"},
                    {"id": "DUG", "label": "account"},
                    {"id": "5493002SJ850I228S657", "label": "lei"},
                ],
                "sinkIdentifiers.orderFileIdentifiers": pd.NA,
                "retailOrProfessional": pd.NA,
                "employeeStatus": pd.NA,
                "location": pd.NA,
                "monitoring": pd.NA,
                "structure.decisionMakerProducts": pd.NA,
                "structure.role": pd.NA,
                "structure.instruments": pd.NA,
                "structure.client": pd.NA,
                "structure.isPersonalTradingAccount": pd.NA,
                "structure.decisionMaker": pd.NA,
                "structure.desks": pd.NA,
                "structure.executionWithinFirm": pd.NA,
                "structure.department": pd.NA,
                "structure.smcr": pd.NA,
                "structure.type": pd.NA,
                "structure.isDecisionMaker": pd.NA,
                "communications.emails": pd.NA,
                "communications.imAccounts": pd.NA,
                "communications.phoneNumbers": pd.NA,
                "counterparty.sourceKey": pd.NA,
                "counterparty.firmLocation": pd.NA,
                "counterparty.fileIdentifier": pd.NA,
                "counterparty.name": pd.NA,
                "counterparty.firmCommunications": pd.NA,
                "counterparty.client": pd.NA,
                "counterparty.emirDetails": pd.NA,
                "counterparty.details": pd.NA,
                "counterparty.firmIdentifiers": pd.NA,
                "counterparty.sinkIdentifiers": pd.NA,
                "counterparty.sourceIndex": pd.NA,
                "counterparty.uniqueIds": pd.NA,
                "personalDetails.firstName": pd.NA,
                "personalDetails.lastName": pd.NA,
                "personalDetails.nationality": pd.NA,
                "personalDetails.dob": pd.NA,
                "personalDetails.middleName": pd.NA,
                "officialIdentifiers.concatId": pd.NA,
                "officialIdentifiers.mifirIdSubType": pd.NA,
                "officialIdentifiers.traderIds": pd.NA,
                "officialIdentifiers.mifirIdType": pd.NA,
                "officialIdentifiers.clientMandate": pd.NA,
                "officialIdentifiers.employeeId": pd.NA,
                "officialIdentifiers.passports": pd.NA,
                "officialIdentifiers.mifirId": pd.NA,
                "officialIdentifiers.nationalIds": pd.NA,
                "officialIdentifiers.fcaNo": pd.NA,
                "officialIdentifiers.branchCountry": pd.NA,
                "firmLocation.registeredAddress.country": "DE",
                "firmLocation.registeredAddress.address": "Karlstr 35",
                "firmLocation.registeredAddress.city": "München",
                "firmLocation.registeredAddress.postalCode": "80333",
                "firmLocation.tradingAddress.country": "DE",
                "firmLocation.tradingAddress.address": "c/o BayernInvest Kapitalverwaltungsgesellschaft"
                " mbH Karlstraße 35",
                "firmLocation.tradingAddress.city": "München",
                "firmLocation.tradingAddress.postalCode": "80333",
                "firmLocation.registeredAddress.countryCode": pd.NA,
                "firmLocation.registeredAddress.street": pd.NA,
                "firmLocation.registeredAddress.state": pd.NA,
                "firmLocation.tradingAddress.countryCode": pd.NA,
                "firmLocation.tradingAddress.street": pd.NA,
                "firmLocation.tradingAddress.state": pd.NA,
                "firmCommunications.emails": pd.NA,
                "firmCommunications.chatDomains": pd.NA,
                "firmCommunications.domainNames": pd.NA,
                "firmCommunications.phoneNumberPrefixes": pd.NA,
                "firmCommunications.phoneNumbers": [
                    {"number": None, "extension": None, "label": None, "dialingCode": None}
                ],
                "___unique_id___": ["id:dug", "lei:5493002sj850i228s657", "account:dug"],
            },
            {
                "sourceKey": "s3://test.uat.steeleye.co/flows/mymarket-universal-steeleye-firm/chelverton_firms.csv",
                "&uniqueProps": ["id:invb", "lei:84s0vf8tsmh0t6d4k848", "account:invb"],
                "&id": "d9173f73-9558-4fc9-8d1d-fe2309ccd89f",
                "&traitFqn": "reference/firm",
                "&ancestor": "MarketCounterparty:d9173f73-9558-4fc9-8d1d-fe2309ccd89f:*************",
                "firmLocation": pd.NA,
                "fileIdentifier": pd.NA,
                "&key": "MarketCounterparty:d9173f73-9558-4fc9-8d1d-fe2309ccd89f:*************",
                "sourceIndex": "17",
                "uniqueIds": ["id:invb", "lei:84s0vf8tsmh0t6d4k848", "account:invb"],
                "&model": "MarketCounterparty",
                "&version": 2,
                "name": "INVESTEC BANK PLC",
                "firmCommunications": pd.NA,
                "client": pd.NA,
                "emirDetails": pd.NA,
                "&timestamp": *************,
                "&user": "<EMAIL>",
                "&hash": "27501b44e2b1882921a6c88d74a24dff7a45449d32b96599f77bff0528bb54a7",
                "firmIdentifiers.nationalBusinessIdentityNo": pd.NA,
                "firmIdentifiers.lei": "84S0VF8TSMH0T6D4K848",
                "firmIdentifiers.bloombergCode": pd.NA,
                "firmIdentifiers.internalRefNo": pd.NA,
                "firmIdentifiers.isIsda": pd.NA,
                "firmIdentifiers.mic": "INVE",
                "firmIdentifiers.ecbCode": pd.NA,
                "firmIdentifiers.bic": pd.NA,
                "firmIdentifiers.branchCountry": None,
                "firmIdentifiers.deaAccess": pd.NA,
                "firmIdentifiers.kycApproved": pd.NA,
                "details.tradableInstruments": pd.NA,
                "details.firmStatus": "ACTIVE",
                "details.legalType": pd.NA,
                "details.inEEA": pd.NA,
                "details.retailOrProfessional": "PROFESSIONAL",
                "details.clientMandate": pd.NA,
                "details.decisionMaker": pd.NA,
                "details.executionWithinFirm": pd.NA,
                "details.firmSize": pd.NA,
                "details.industry": pd.NA,
                "details.leiRegistrationStatus": "ISSUED",
                "details.parentOfCollectiveInvestmentSchema": pd.NA,
                "details.mifidEnrollmentDate": pd.NA,
                "details.firmType": "Counterparty",
                "details.mifidRegistered": None,
                "details.firmNCA": pd.NA,
                "details.isEmirDelegatedReporting": pd.NA,
                "details.orgType": "Multilateral Trading Facility",
                "details.positionHolderInFirm": pd.NA,
                "details.smcrFirmType": pd.NA,
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"id": "84S0VF8TSMH0T6D4K848", "label": "lei"},
                    {"id": "INVB", "label": "id"},
                    {"id": "INVB", "label": "account"},
                ],
                "sinkIdentifiers.orderFileIdentifiers": pd.NA,
                "retailOrProfessional": pd.NA,
                "employeeStatus": pd.NA,
                "location": pd.NA,
                "monitoring": pd.NA,
                "structure.decisionMakerProducts": pd.NA,
                "structure.role": pd.NA,
                "structure.instruments": pd.NA,
                "structure.client": pd.NA,
                "structure.isPersonalTradingAccount": pd.NA,
                "structure.decisionMaker": pd.NA,
                "structure.desks": pd.NA,
                "structure.executionWithinFirm": pd.NA,
                "structure.department": pd.NA,
                "structure.smcr": pd.NA,
                "structure.type": pd.NA,
                "structure.isDecisionMaker": pd.NA,
                "communications.emails": pd.NA,
                "communications.imAccounts": pd.NA,
                "communications.phoneNumbers": pd.NA,
                "counterparty.sourceKey": pd.NA,
                "counterparty.firmLocation": pd.NA,
                "counterparty.fileIdentifier": pd.NA,
                "counterparty.name": pd.NA,
                "counterparty.firmCommunications": pd.NA,
                "counterparty.client": pd.NA,
                "counterparty.emirDetails": pd.NA,
                "counterparty.details": pd.NA,
                "counterparty.firmIdentifiers": pd.NA,
                "counterparty.sinkIdentifiers": pd.NA,
                "counterparty.sourceIndex": pd.NA,
                "counterparty.uniqueIds": pd.NA,
                "personalDetails.firstName": pd.NA,
                "personalDetails.lastName": pd.NA,
                "personalDetails.nationality": pd.NA,
                "personalDetails.dob": pd.NA,
                "personalDetails.middleName": pd.NA,
                "officialIdentifiers.concatId": pd.NA,
                "officialIdentifiers.mifirIdSubType": pd.NA,
                "officialIdentifiers.traderIds": pd.NA,
                "officialIdentifiers.mifirIdType": pd.NA,
                "officialIdentifiers.clientMandate": pd.NA,
                "officialIdentifiers.employeeId": pd.NA,
                "officialIdentifiers.passports": pd.NA,
                "officialIdentifiers.mifirId": pd.NA,
                "officialIdentifiers.nationalIds": pd.NA,
                "officialIdentifiers.fcaNo": pd.NA,
                "officialIdentifiers.branchCountry": pd.NA,
                "firmLocation.registeredAddress.country": "GB",
                "firmLocation.registeredAddress.address": "30 GRESHAM STREET",
                "firmLocation.registeredAddress.city": "LONDON",
                "firmLocation.registeredAddress.postalCode": "EC2V 7QP",
                "firmLocation.tradingAddress.country": "GB",
                "firmLocation.tradingAddress.address": "30 GRESHAM STREET",
                "firmLocation.tradingAddress.city": "LONDON",
                "firmLocation.tradingAddress.postalCode": "EC2V 7QP",
                "firmLocation.registeredAddress.countryCode": pd.NA,
                "firmLocation.registeredAddress.street": pd.NA,
                "firmLocation.registeredAddress.state": pd.NA,
                "firmLocation.tradingAddress.countryCode": pd.NA,
                "firmLocation.tradingAddress.street": pd.NA,
                "firmLocation.tradingAddress.state": pd.NA,
                "firmCommunications.emails": pd.NA,
                "firmCommunications.chatDomains": pd.NA,
                "firmCommunications.domainNames": pd.NA,
                "firmCommunications.phoneNumberPrefixes": pd.NA,
                "firmCommunications.phoneNumbers": [
                    {"number": None, "extension": None, "label": None, "dialingCode": None}
                ],
                "___unique_id___": ["id:invb", "lei:84s0vf8tsmh0t6d4k848", "account:invb"],
            },
        ]
    )

# ruff: noqa: E501
import boto3
import fsspec
import json
import pandas as pd
import pytest
import shutil
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_task_link.models import AriesTaskInput
from freezegun import freeze_time
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_trades_tasks.trade_sink.trade_sink_task import trade_sink_run
from integration_trades_tasks.trade_sink.utils.static import TempColumns
from mock import MagicMock
from moto import mock_aws
from pathlib import Path
from se_io_utils.tempfile_utils import tmp_directory

CURRENT_PATH = Path(__file__).parent
INPUT_PATH = CURRENT_PATH / "data" / "handler_inputs"
EXPECTED_OUTPUT_PATH = CURRENT_PATH / "data" / "expected_outputs"

TEMP_DIR: Path = tmp_directory()
AUDIT_PATH: Path = TEMP_DIR.joinpath("audit.json")

mock_aiobotocore_convert_to_response_dict()


class TestTradeSinkTransform(object):
    @staticmethod
    def teardown_method():
        if AUDIT_PATH.exists():
            AUDIT_PATH.unlink()

        # Delete result paths
        for result_path in [CURRENT_PATH / "data" / "output"]:
            if Path(result_path).exists():
                shutil.rmtree(str(result_path))

    @staticmethod
    def mock_auditor_upload_side_effect(audit_filepath: str, **kwargs) -> None:
        shutil.copy(Path(audit_filepath), AUDIT_PATH)

        return None

    @staticmethod
    def s3_add_objects_to_bucket(bucket_name: str, local_file_path: Path, cloud_base_key: str):
        """Puts the single input file in the correct path in the mock S3
        bucket.

        :param bucket_name: Bucket name of Mock S3 bucket
        :param local_file_path: File path of the file to be uploaded
        :param cloud_base_key: Cloud base url of where the file is to be uploaded
        :return: None, uploads files to the mock S3 bucket
        """

        # Create bucket
        s3 = boto3.client("s3", region_name="us-east-1")
        s3.create_bucket(Bucket=bucket_name)
        file_name = Path(local_file_path).name
        with open(local_file_path, "rb") as f:
            s3.put_object(
                Bucket=bucket_name,
                Key=f"{cloud_base_key}{file_name}",
                Body=f.read(),
            )

    @mock_aws
    @freeze_time("2024-03-18T15:58:57.926451Z")
    @pytest.mark.parametrize(
        "handler_name, source_file_urls, expected_file_path, streamed",
        [
            # Non streamed test
            (
                "andurand_cme_ice",
                [INPUT_PATH / "andurand_cme_ice" / "Cme20230313.csv"],
                EXPECTED_OUTPUT_PATH / "andurand_cme_ice_transformed.ndjson",
                False,
            ),
            # Streamed test
            (
                "atfx_one_zero",
                [
                    INPUT_PATH / "atfx_one_zero" / "1585599171483_1247.fix",
                    INPUT_PATH / "atfx_one_zero" / "1585599235655_1250.fix",
                    INPUT_PATH
                    / "bloomberg"
                    / "4cef8a9efc3fb33b6829c128cee0cb4fbe782463c3df6db0eb0978b8a61b537b_859.fix",
                    INPUT_PATH
                    / "bloomberg"
                    / "69863718434836b4675e4a4040d66566cf68f86029f328e84dc4dec6bbb1b50d_1174.fix",
                    INPUT_PATH / "atfx_one_zero" / "batch.ndjson",
                ],
                EXPECTED_OUTPUT_PATH / "atfx_one_zero_transformed.ndjson",
                True,
            ),
            # Skip file test
            (
                "thornbridge_universum",
                [
                    INPUT_PATH
                    / "thornbridge_universum"
                    / "Thornbridge-MIFID-Transactions_2024-06-07.csv"
                ],
                EXPECTED_OUTPUT_PATH / "thornbridge_universum_empty.ndjson",
                False,
            ),
            (
                "avatrade_gold_i",
                [
                    INPUT_PATH
                    / "avatrade_gold_i"
                    / "42fc27e65d678103153691b6333dc9c8c65d81614c836b67099eda2e9960b8e1_3945.fix",
                    INPUT_PATH / "avatrade_gold_i" / "batch.ndjson",
                ],
                EXPECTED_OUTPUT_PATH / "avatrade_gold_i_empty.ndjson",
                True,
            ),
        ],
    )
    def test_valid_execution(
        self,
        mocker,
        sample_aries_task_input: AriesTaskInput,
        handler_name,
        source_file_urls,
        expected_file_path,
        streamed,
        instrument_data,
        participants_data,
    ):
        for file_path in source_file_urls:
            if "bloomberg" in str(file_path):
                handler_name = "bloomberg"

            base_key = f"aries/ingress/{'streamed' if streamed else 'nonstreamed'}/evented/{'trade_sink_s' if streamed else 'trade_sink_ns'}/{handler_name}/"
            if "batch.ndjson" in file_path.name:
                base_key = f"aries/ingest_batching/trade_sink_s/{handler_name}/2024/03/18/trace_id/"

            self.s3_add_objects_to_bucket(
                local_file_path=file_path,
                bucket_name="test_tenant.dev.steeleye.co",
                cloud_base_key=base_key,
            )

        sample_aries_task_input.input_param.params["file_uri"] = (
            f"s3://test_tenant.dev.steeleye.co/{base_key}{'batch.ndjson' if streamed else Path(source_file_urls[0]).name}"
        )

        # Mock lei fetch to get Account Firm record
        mock_lei_fetch = mocker.patch(
            "integration_trades_tasks.trade_sink.trade_importer.run_get_tenant_lei"
        )
        mock_lei_fetch.return_value = pd.DataFrame(
            {TempColumns.TEMP_COL_1: ["549300LWVX0OHHQJR327"]}
        )

        # Mock Es config
        mock_create_es_config = mocker.patch(
            "integration_trades_tasks.trade_sink.trade_sink_flow.get_es_config"
        )
        mock_create_es_config.return_value = MagicMock()

        # Mock ES repository
        mock_create_es_repo = mocker.patch(
            "integration_trades_tasks.trade_sink.trade_sink_flow.get_repository_by_cluster_version"
        )
        es_mock = MagicMock()
        es_mock.scroll.side_effect = [
            participants_data,
            pd.DataFrame(),
            pd.DataFrame(),
            pd.DataFrame(),
        ]
        es_mock.MAX_TERMS_SIZE = 1024
        mock_create_es_repo.return_value = es_mock

        # Mock instrument fetch
        mock_instrument_fetch = mocker.patch(
            "se_trades_tasks.order_and_tr.instrument.link.link_instrument.LinkInstrument._fetch_instruments_data"
        )
        mock_instrument_fetch.return_value = pd.DataFrame() if streamed else instrument_data

        # Mock party fallback
        mock_party_fallback = mocker.patch(
            "aries_se_trades_tasks.party.party_fallback_with_lei_lookup.run_tasks_lib"
        )
        mock_party_fallback.return_value = pd.DataFrame()

        if streamed:
            mock_checked_tenant = mocker.patch(
                "integration_trades_tasks.trade_sink.handler.universal.bloomberg.CHECKED_TENANTS"
            )
            mock_checked_tenant.return_value = ["abc"]

        mocker.patch.object(
            target=CachedTenantWorkflowAPIClient,
            attribute="get",
            return_value=addict.Dict(
                {
                    "tenant": {
                        "lake_prefix": "test_tenant.dev.steeleye.co",
                    },
                    "workflow": {"streamed": streamed},
                    "max_batch_size": 1000,
                },
            ),
        )

        mock_auditor_upload = mocker.patch(
            "integration_wrapper.integration_aries_task.upload_audit"
        )
        mock_auditor_upload.side_effect = self.mock_auditor_upload_side_effect

        # Mock EOD stats fetch for Best Ex
        mock_eod_stats_run = mocker.patch(
            "se_trades_tasks.order.best_execution.best_execution.run_order_eod_stats_enricher"
        )
        mock_eod_stats_run.return_value = pd.DataFrame()

        aries_task_output = trade_sink_run(aries_task_input=sample_aries_task_input)
        result_path = (
            aries_task_output.output_param.params.get("Order").get("params").get("file_uri")
        )

        audit = json.loads(AUDIT_PATH.read_text())

        if handler_name in ["thornbridge_universum", "avatrade_gold_i"]:
            # Test skip condition
            assert result_path is None
        else:
            # Check if we are populating the correct root keys of audits
            if streamed:
                assert "input_files" in audit.keys()
            else:
                assert "input_records" in audit.keys()

            # Read the transformed result path
            with fsspec.open(result_path, "rb") as fp:
                result = fp.readlines()

            # Read the expected result file
            with fsspec.open(str(expected_file_path), "rb") as fp:
                expected_result = fp.readlines()

            assert expected_result == result

            if handler_name == "andurand_cme_ice":
                # this is validating that the fallback for notation fields is populating correctly
                expected_dataframe = pd.read_json(expected_file_path, lines=True)

                quantity_notation = expected_dataframe["transactionDetails"].str.get(
                    "quantityNotation"
                )
                assert quantity_notation.unique() == "UNIT"

                price_notation = expected_dataframe["transactionDetails"].str.get("priceNotation")
                assert price_notation.unique() == "MONE"

import datetime
import os
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from integration_wrapper.static import IntegrationAriesTaskVariables

# This needs to be here since the conftest is loaded first
# than any import on the test files
os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)
os.environ["COGNITO_CLIENT_ID"] = "some_id"
os.environ["COGNITO_CLIENT_SECRET"] = "some_secret"
os.environ["COGNITO_AUTH_URL"] = "some_auth_url"
os.environ["MASTER_DATA_HOST"] = "some_host"
os.environ["MASTER_API_DATA_HOST"] = "some_host"


@pytest.fixture()
def sample_input_client_side_orders() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="e2e_test_aladdin_v2_client_side",
        name="order_aladdin_v2",
        stack="dev-shared-2",
        tenant="test",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            flow_override="client",
            source_file_uri="s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_aladdin_v2/AladdinTCA.jam_EQ.OrderDetail.20241028.csv",
            file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2025/02/06/foo/file_splitter_by_criteria/AladdinTCA.jam_EQ.20241028_client_side_orders_batch_0.csv",
            order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2025/02/06/foo/file_splitter_by_criteria/68bb50d933a57a1330d3cb193a148345a77bcda46fcd3749341ee16f4cad4e5f___order_id_cache.json",
        )
    )
    task = TaskFieldSet(name="order_aladdin_v2", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_input_otc_client_side_orders(sample_input_client_side_orders):
    sample_input_client_side_orders.workflow.trace_id = "e2e_test_aladdin_v2_client_side_otc"
    sample_input_client_side_orders.input_param.params["file_uri"] = (
        "s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2025/02/06/test_otc/"
        "file_splitter_by_criteria/AladdinTCA.OTC_DERIV.20241023_client_side_orders_batch_0.csv"
    )
    sample_input_client_side_orders.input_param.params["source_file_uri"] = (
        "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_aladdin_v2/AladdinTCA.OTC_DERIV.Transaction.20241023.csv"
    )
    sample_input_client_side_orders.input_param.params["order_id_cache_path"] = (
        "s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2025/02/06/test_otc/file_splitter_by_criteria"
        "/12aa462fe57711c924bfdaf7167e50125c21e264ab04ae1487cbb95dfdf1c3d6___order_id_cache.json"
    )
    return sample_input_client_side_orders


@pytest.fixture()
def sample_input_market_side_orders() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="e2e_test_aladdin_v2_market_side",
        name="order_aladdin_v2",
        stack="dev-shared-2",
        tenant="test",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            flow_override="market",
            source_file_uri="s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_aladdin_v2/AladdinTCA.jam_EQ.OrderDetail.20241028.csv",
            file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2025/02/06/foo/file_splitter_by_criteria/AladdinTCA.jam_EQ.20241028_market_side_orders_batch_0.csv",
            order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2025/02/06/foo/file_splitter_by_criteria/68bb50d933a57a1330d3cb193a148345a77bcda46fcd3749341ee16f4cad4e5f___order_id_cache.json",
        )
    )
    task = TaskFieldSet(name="order_aladdin_v2", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_input_otc_market_side_orders(sample_input_market_side_orders):
    sample_input_market_side_orders.workflow.trace_id = "e2e_test_aladdin_v2_market_side_otc"
    sample_input_market_side_orders.input_param.params["file_uri"] = (
        "s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2025/02/06/test_otc/file_splitter_by_criteria/AladdinTCA.OTC_DERIV.20241023_market_side_orders_batch_0.csv"
    )
    sample_input_market_side_orders.input_param.params["source_file_uri"] = (
        "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_aladdin_v2/AladdinTCA.OTC_DERIV.Transaction.20241023.csv"
    )
    sample_input_market_side_orders.input_param.params["order_id_cache_path"] = (
        "s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2025/02/06/test_otc/file_splitter_by_criteria"
        "/12aa462fe57711c924bfdaf7167e50125c21e264ab04ae1487cbb95dfdf1c3d6___order_id_cache.json"
    )
    return sample_input_market_side_orders


@pytest.fixture()
def sample_input_market_side_orders_flattened(sample_input_market_side_orders):
    sample_input_market_side_orders.workflow.tenant = "schroders"
    sample_input_market_side_orders.workflow.trace_id = "test_flattening"
    sample_input_market_side_orders.input_param.params["file_uri"] = (
        "s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2025/02/06/test_flattening/file_splitter_by_criteria/AladdinTCA.jam_EQ.20241028_market_side_orders_batch_0.csv"
    )
    sample_input_market_side_orders.input_param.params["source_file_uri"] = (
        "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_aladdin_v2/AladdinTCA.jam_EQ.Transaction.20241028.csv"
    )
    sample_input_market_side_orders.input_param.params["order_id_cache_path"] = (
        "s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2025/02/06/test_flattening/file_splitter_by_criteria"
        "/68bb50d933a57a1330d3cb193a148345a77bcda46fcd3749341ee16f4cad4e5f___order_id_cache.json"
    )
    return sample_input_market_side_orders

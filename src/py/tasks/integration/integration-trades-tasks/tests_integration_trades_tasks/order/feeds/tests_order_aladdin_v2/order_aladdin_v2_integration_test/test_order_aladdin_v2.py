# ruff: noqa: E501
import fsspec
import json
import os
import pandas as pd
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_trades_tasks.order_app_metrics_enum import OrderAppMetricsEnum
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from freezegun import freeze_time
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_test_utils.aws_helpers.s3_test_helpers import create_and_add_objects_to_s3_bucket
from integration_test_utils.model_validation.validation_errors import (
    assert_record_is_schema_compliant,
)
from integration_trades_tasks.order.feeds.order_aladdin_v2 import (
    order_aladdin_v2_flow as order_aladdin_v2_flow_module,
)
from integration_trades_tasks.order.feeds.order_aladdin_v2.order_aladdin_v2_flow import (
    DefaultAladdinV2,
)
from integration_trades_tasks.order.feeds.order_aladdin_v2.order_aladdin_v2_task import (
    order_aladdin_v2_run,
)
from integration_wrapper.static import StaticFields
from mock import MagicMock
from moto import mock_aws
from pathlib import Path
from se_elastic_schema.models import Order
from se_elastic_schema.static.market import IdentifierType
from se_elastic_schema.steeleye_record_validations.error_codes import SteeleyeRecordErrorCodesEnum
from se_elasticsearch.repository.models import ResourceConfig
from se_io_utils.json_utils import read_json, write_named_temporary_json
from se_io_utils.tempfile_utils import tmp_directory
from se_trades_tasks.order.best_execution.plugins.best_ex_fx_rates import BestExFxRatesPlugin
from se_trades_tasks.order.static import OrderColumns

CURRENT_PATH = Path(__file__).parent
SCENARIO_CLIENT_SIDE_ORDERS_PATH = (
    CURRENT_PATH.joinpath("data")
    .joinpath("valid_executions")
    .joinpath("scenario_client_side_orders")
)
SCENARIO_MARKET_SIDE_ORDERS_PATH = (
    CURRENT_PATH.joinpath("data")
    .joinpath("valid_executions")
    .joinpath("scenario_market_side_orders")
)
TEST_BUCKET = "test.dev.steeleye.co"
TEMP_DIR: Path = tmp_directory()
AUDIT_PATH: Path = TEMP_DIR.joinpath("audit.json")

mock_aiobotocore_convert_to_response_dict()


def get_result_paths(scenario: str):
    scenario_path = f"{CURRENT_PATH}/data/valid_executions/{scenario}/"

    return [
        f"{scenario_path}synthetic.ndjson",
        f"{scenario_path}orders.ndjson",
    ]


class TestOrderAladdinV2:
    @staticmethod
    def teardown_method():
        # Delete result paths
        for scenario in ["scenario_client_side_orders", "scenario_market_side_orders"]:
            for result_path in get_result_paths(scenario=scenario):
                path = Path(result_path)
                if path.exists():
                    path.unlink()

    @mock_aws
    def test_order_aladdin_v2_client_side_orders(
        self, mocker, sample_input_client_side_orders: AriesTaskInput
    ):
        # The input file has 2 rows. It is meant to generate:
        # - 2 Synthetic NEWOs
        # - 1 PARF. There will be one temporary OrderState that will get discarded mid-flow
        # because it will be a PARF with bookedQuantity=0.

        create_and_add_objects_to_s3_bucket(
            bucket_name=TEST_BUCKET,
            source_path=SCENARIO_CLIENT_SIDE_ORDERS_PATH.joinpath("buckets", TEST_BUCKET),
        )

        aries_task_result = self._run_aries_task(
            mocker=mocker,
            sample_aries_task_input=sample_input_client_side_orders,
            scenario="scenario_client_side_orders",
        )

        # Assert the NDJSONs produced by the Flow contain the expected data
        for output_key in ["orders", "synthetic_newo"]:
            output_uri: str = (
                aries_task_result.output_param.params.get(output_key).get("params").get("file_uri")  # type: ignore[union-attr]
            )
            local_file_path: str = run_download_file(
                file_url=output_uri,
            )
            result: pd.DataFrame = pd.read_json(local_file_path, lines=True)

            # Assert the output records are schema compliant
            # and do not raise any SteelEye Data validations, apart from the ones
            # that are expected due to having mocked BestExecution
            # NOTE: the synthetic orders expected result DF has 2 rows.
            # The 2nd row is meant to test the scenario where the client sends PARFs
            # with bookedQuantity=0. It is missing many data points, hence no point in
            # checking its SteelEye Data Validations
            assert_record_is_schema_compliant(
                input_df=result if output_key == "orders" else result.iloc[[0], :],
                model=Order,
                accepted_validation_error_codes=[
                    SteeleyeRecordErrorCodesEnum.SE_DV_136,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_138,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_369,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_371,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_373,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_375,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_142,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_361,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_363,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_365,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_367,  # bestEx results are mocked
                ],
            )

            expected_result = pd.read_json(
                SCENARIO_CLIENT_SIDE_ORDERS_PATH.joinpath(
                    f"expected_{output_key}.ndjson"
                ).as_posix(),
                lines=True,
            )
            os.remove(local_file_path)
            pd.testing.assert_frame_equal(left=result, right=expected_result)

        assert aries_task_result.output_param.params == {  # type: ignore[union-attr]
            "orders": {
                "params": {
                    "data_model": "se_elastic_schema.models.tenant.mifid2.order:Order",
                    "es_action": "create",
                    "file_uri": f"{CURRENT_PATH}/data/valid_executions/"
                    "scenario_client_side_orders/orders.ndjson",
                }
            },
            "synthetic_newo": {
                "params": {
                    "data_model": "se_elastic_schema.models.tenant.mifid2.order:Order",
                    "es_action": "create",
                    "file_uri": f"{CURRENT_PATH}/data/valid_executions/"
                    "scenario_client_side_orders/synthetic.ndjson",
                }
            },
        }

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "order_aladdin_v2"
            ]["order_aladdin_v2"]
            if aries_task_result.app_metric
            else {}
        )

        # Assert app metrics
        assert metrics[GenericAppMetricsEnum.SKIPPED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.ERRORED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.DUPLICATE_COUNT] == 0
        assert metrics[OrderAppMetricsEnum.OUTPUT_SYNTHETIC_NEWOS_COUNT] == 2
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 3
        assert metrics[OrderAppMetricsEnum.SYNTHETIC_INSTRUMENTS_COUNT] == 3

        # Assert audits match the expected results:
        # For both orders, the instrument is created through fallback,
        # because we are mocking the SRP connection.
        # There is also a workflow_status message because the ECB rates are mocked.
        audit_result = read_json(AUDIT_PATH.as_posix())
        assert audit_result == {
            "input_records": {
                "C|9301557_9301457_25:1:9301457|0:PARF:2024-10-23T06:00:00Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "C|9301557_9301457_25:1:NEWO": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data",
                        ],
                        "updated": 0,
                    }
                },
                "C|9778012_9777912.0_1014:1:NEWO": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data",
                        ],
                        "updated": 0,
                    }
                },
            },
            "workflow_status": ["BestExFxRatesPlugin:ecb ref rates not found "],
        }

    @mock_aws
    def test_order_aladdin_v2_client_side_otc_orders(
        self, mocker, sample_input_otc_client_side_orders: AriesTaskInput
    ):
        # The input file has 3 rows
        # it is expected to generate 2 FILLs, 1 PARF and 1 NEWO
        # The 2 OTC trades will generate 2 FILLS and no NEWOs as per specs
        # The PARF will generate 1 NEWO.
        create_and_add_objects_to_s3_bucket(
            bucket_name=TEST_BUCKET,
            source_path=SCENARIO_CLIENT_SIDE_ORDERS_PATH.joinpath("buckets", TEST_BUCKET),
        )

        aries_task_result = self._run_aries_task(
            mocker=mocker,
            sample_aries_task_input=sample_input_otc_client_side_orders,
            scenario="scenario_client_side_orders",
        )

        # Assert the NDJSONs produced by the Flow contain the expected data
        for output_key in ["orders", "synthetic_newo"]:
            output_uri: str = (
                aries_task_result.output_param.params.get(output_key).get("params").get("file_uri")
                # type: ignore[union-attr]
            )
            local_file_path: str = run_download_file(
                file_url=output_uri,
            )
            result: pd.DataFrame = pd.read_json(local_file_path, lines=True)

            assert_record_is_schema_compliant(
                input_df=result if output_key == "orders" else result.iloc[[0], :],
                model=Order,
                accepted_validation_error_codes=[
                    SteeleyeRecordErrorCodesEnum.SE_DV_136,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_138,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_369,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_371,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_373,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_375,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_142,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_361,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_363,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_365,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_367,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_161,  # OTC Orders do not have a trader
                ],
            )

            expected_result = pd.read_json(
                SCENARIO_CLIENT_SIDE_ORDERS_PATH.joinpath(
                    f"expected_otc_{output_key}.ndjson"
                ).as_posix(),
                lines=True,
            )
            os.remove(local_file_path)
            pd.testing.assert_frame_equal(left=result, right=expected_result)

        assert aries_task_result.output_param.params == {  # type: ignore[union-attr]
            "orders": {
                "params": {
                    "data_model": "se_elastic_schema.models.tenant.mifid2.order:Order",
                    "es_action": "create",
                    "file_uri": f"{CURRENT_PATH}/data/valid_executions/"
                    "scenario_client_side_orders/orders.ndjson",
                }
            },
            "synthetic_newo": {
                "params": {
                    "data_model": "se_elastic_schema.models.tenant.mifid2.order:Order",
                    "es_action": "create",
                    "file_uri": f"{CURRENT_PATH}/data/valid_executions/"
                    "scenario_client_side_orders/synthetic.ndjson",
                }
            },
        }

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "order_aladdin_v2"
            ]["order_aladdin_v2"]
            if aries_task_result.app_metric
            else {}
        )

        # Assert app metrics
        assert metrics[GenericAppMetricsEnum.SKIPPED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.ERRORED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.DUPLICATE_COUNT] == 0
        assert metrics[OrderAppMetricsEnum.OUTPUT_SYNTHETIC_NEWOS_COUNT] == 3
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 6
        assert metrics[OrderAppMetricsEnum.SYNTHETIC_INSTRUMENTS_COUNT] == 6

        # Assert audits match the expected results:
        # For both orders, the instrument is created through fallback,
        # because we are mocking the SRP connection.
        # There is also a workflow_status message because the ECB rates are mocked.
        audit_result = read_json(AUDIT_PATH.as_posix())
        assert audit_result == {
            "input_records": {
                "C|18483643_1873:2:18483643|0:FILL:2024-11-19T09:24:56.636000Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "C|18483643_1873:2:NEWO": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "C|19954432_1745:2:19954432|1:FILL:2024-11-19T09:30:02.226000Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "C|19954432_1745:2:NEWO": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "C|22063703_22063603_218:1:22063603|2:PARF:2024-11-19T00:04:20.756000Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "C|22063703_22063603_218:1:NEWO": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
            },
            "workflow_status": ["BestExFxRatesPlugin:ecb ref rates not found "],
        }

    @mock_aws
    def test_order_aladdin_v2_market_side_orders(
        self, mocker, sample_input_market_side_orders: AriesTaskInput
    ):
        create_and_add_objects_to_s3_bucket(
            bucket_name=TEST_BUCKET,
            source_path=SCENARIO_MARKET_SIDE_ORDERS_PATH.joinpath("buckets", TEST_BUCKET),
        )

        aries_task_result = self._run_aries_task(
            mocker=mocker,
            sample_aries_task_input=sample_input_market_side_orders,
            scenario="scenario_market_side_orders",
        )

        # Assert the NDJSONs produced by the Flow contain the expected data
        for output_key in ["orders", "synthetic_newo"]:
            output_uri: str = (
                aries_task_result.output_param.params.get(output_key).get("params").get("file_uri")  # type: ignore[union-attr]
            )
            local_file_path: str = run_download_file(
                file_url=output_uri,
            )
            result: pd.DataFrame = pd.read_json(local_file_path, lines=True)

            # Assert the output records are schema compliant
            # and do not raise any SteelEye Data validations, apart from the ones
            # that are expected due to having mocked BestExecution
            assert_record_is_schema_compliant(
                input_df=result,
                model=Order,
                accepted_validation_error_codes=[
                    SteeleyeRecordErrorCodesEnum.SE_DV_142,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_361,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_363,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_365,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_367,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_136,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_138,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_369,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_371,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_373,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_375,  # bestEx results are mocked
                ],
            )

            expected_result = pd.read_json(
                SCENARIO_MARKET_SIDE_ORDERS_PATH.joinpath(
                    f"expected_{output_key}.ndjson"
                ).as_posix(),
                lines=True,
            )
            os.remove(local_file_path)
            pd.testing.assert_frame_equal(left=result, right=expected_result)

        assert aries_task_result.output_param.params == {  # type: ignore[union-attr]
            "orders": {
                "params": {
                    "data_model": "se_elastic_schema.models.tenant.mifid2.order:Order",
                    "es_action": "create",
                    "file_uri": f"{CURRENT_PATH}/data/valid_executions/scenario_market_side_orders/orders.ndjson",
                }
            },
            "synthetic_newo": {
                "params": {
                    "data_model": "se_elastic_schema.models.tenant.mifid2.order:Order",
                    "es_action": "create",
                    "file_uri": f"{CURRENT_PATH}/data/valid_executions/scenario_market_side_orders/synthetic.ndjson",
                }
            },
        }

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "order_aladdin_v2"
            ]["order_aladdin_v2"]
            if aries_task_result.app_metric
            else {}
        )

        # Assert app metrics
        assert metrics[GenericAppMetricsEnum.SKIPPED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.ERRORED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.DUPLICATE_COUNT] == 0
        assert metrics[OrderAppMetricsEnum.OUTPUT_SYNTHETIC_NEWOS_COUNT] == 1
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 51
        assert metrics[OrderAppMetricsEnum.SYNTHETIC_INSTRUMENTS_COUNT] == 51

        # Assert audits match the expected results:
        # For both orders, the instrument is created through fallback,
        # because we are mocking the SRP connection.
        # There is also a workflow_status message because the ECB rates are mocked.
        audit_result = read_json(AUDIT_PATH.as_posix())
        assert audit_result == {
            "input_records": {
                "48019757:1:48019757:PARF:2024-10-23T00:00:00Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48019857:1:48019857:PARF:2024-10-23T00:00:07Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48019957:1:48019957:PARF:2024-10-23T00:01:39Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48020157:1:48020157:PARF:2024-10-23T00:08:45Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48020257:1:48020257:PARF:2024-10-23T00:08:45Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48021557:1:48021557:PARF:2024-10-23T00:45:10Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48021657:1:48021657:PARF:2024-10-23T00:51:26Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48021757:1:48021757:PARF:2024-10-23T00:54:35Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48022057:1:48022057:PARF:2024-10-23T01:20:14Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48022157:1:48022157:PARF:2024-10-23T01:26:19Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48022257:1:48022257:PARF:2024-10-23T01:32:50Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48022357:1:48022357:PARF:2024-10-23T01:40:20Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48022757:1:48022757:PARF:2024-10-23T01:56:15Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48022857:1:48022857:PARF:2024-10-23T02:02:04Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48022957:1:48022957:PARF:2024-10-23T02:05:30Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48023057:1:48023057:PARF:2024-10-23T02:11:45Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48023157:1:48023157:PARF:2024-10-23T02:12:00Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48023257:1:48023257:PARF:2024-10-23T02:29:02Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48023357:1:48023357:PARF:2024-10-23T03:30:00Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48023457:1:48023457:PARF:2024-10-23T03:31:54Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48023557:1:48023557:PARF:2024-10-23T03:35:06Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48023657:1:48023657:PARF:2024-10-23T03:39:10Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48023957:1:48023957:PARF:2024-10-23T03:54:25Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48024057:1:48024057:PARF:2024-10-23T03:55:33Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48024157:1:48024157:PARF:2024-10-23T04:00:05Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48024257:1:48024257:PARF:2024-10-23T04:03:12Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48024357:1:48024357:PARF:2024-10-23T04:08:20Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48024457:1:48024457:PARF:2024-10-23T04:13:43Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48024657:1:48024657:PARF:2024-10-23T04:28:00Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48024757:1:48024757:PARF:2024-10-23T04:33:38Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48024857:1:48024857:PARF:2024-10-23T04:39:50Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48024957:1:48024957:PARF:2024-10-23T04:46:20Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48025057:1:48025057:PARF:2024-10-23T04:51:45Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48025157:1:48025157:PARF:2024-10-23T04:57:04Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48025257:1:48025257:PARF:2024-10-23T04:59:57Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48025357:1:48025357:PARF:2024-10-23T05:07:05Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48025457:1:48025457:PARF:2024-10-23T05:07:30Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48025557:1:48025557:PARF:2024-10-23T05:13:58Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48025657:1:48025657:PARF:2024-10-23T05:14:02Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48025957:1:48025957:PARF:2024-10-23T05:24:46Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48026057:1:48026057:PARF:2024-10-23T05:26:18Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48026157:1:48026157:PARF:2024-10-23T05:26:19Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48026257:1:48026257:PARF:2024-10-23T05:29:40Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48026357:1:48026357:PARF:2024-10-23T05:33:22Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48026457:1:48026457:PARF:2024-10-23T05:35:37Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48026557:1:48026557:PARF:2024-10-23T05:38:27Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48026657:1:48026657:PARF:2024-10-23T05:40:00Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48026857:1:48026857:PARF:2024-10-23T05:42:21Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48027057:1:48027057:PARF:2024-10-23T05:47:20Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48027157:1:48027157:PARF:2024-10-23T05:49:27Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "M|29701057:1:NEWO": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data",
                        ],
                        "updated": 0,
                    }
                },
            },
            "workflow_status": ["BestExFxRatesPlugin:ecb ref rates not found "],
        }

    @mock_aws
    def test_order_aladdin_v2_market_side_otc_orders(
        self, mocker, sample_input_otc_market_side_orders: AriesTaskInput
    ):
        # The input file has 1 row which is non-OTC.
        # It is expected to generate 1 PARF and 1 NEWO.
        # The purpose of this test is to validate that
        # even when handling OTC trades, the workflow will still
        # process the other market-side orders without issues
        create_and_add_objects_to_s3_bucket(
            bucket_name=TEST_BUCKET,
            source_path=SCENARIO_MARKET_SIDE_ORDERS_PATH.joinpath("buckets", TEST_BUCKET),
        )

        aries_task_result = self._run_aries_task(
            mocker=mocker,
            sample_aries_task_input=sample_input_otc_market_side_orders,
            scenario="scenario_market_side_orders",
        )

        # Assert the NDJSONs produced by the Flow contain the expected data
        for output_key in ["orders", "synthetic_newo"]:
            output_uri: str = (
                aries_task_result.output_param.params.get(output_key).get("params").get("file_uri")
                # type: ignore[union-attr]
            )
            local_file_path: str = run_download_file(
                file_url=output_uri,
            )
            result: pd.DataFrame = pd.read_json(local_file_path, lines=True)

            assert_record_is_schema_compliant(
                input_df=result if output_key == "orders" else result.iloc[[0], :],
                model=Order,
                accepted_validation_error_codes=[
                    SteeleyeRecordErrorCodesEnum.SE_DV_136,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_138,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_369,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_371,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_373,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_375,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_142,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_361,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_363,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_365,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_367,  # bestEx results are mocked
                ],
            )

            expected_result = pd.read_json(
                SCENARIO_MARKET_SIDE_ORDERS_PATH.joinpath(
                    f"expected_otc_{output_key}.ndjson"
                ).as_posix(),
                lines=True,
            )
            os.remove(local_file_path)
            pd.testing.assert_frame_equal(left=result, right=expected_result)

        assert aries_task_result.output_param.params == {  # type: ignore[union-attr]
            "orders": {
                "params": {
                    "data_model": "se_elastic_schema.models.tenant.mifid2.order:Order",
                    "es_action": "create",
                    "file_uri": f"{CURRENT_PATH}/data/valid_executions/"
                    "scenario_market_side_orders/orders.ndjson",
                }
            },
            "synthetic_newo": {
                "params": {
                    "data_model": "se_elastic_schema.models.tenant.mifid2.order:Order",
                    "es_action": "create",
                    "file_uri": f"{CURRENT_PATH}/data/valid_executions/"
                    "scenario_market_side_orders/synthetic.ndjson",
                }
            },
        }

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "order_aladdin_v2"
            ]["order_aladdin_v2"]
            if aries_task_result.app_metric
            else {}
        )

        # Assert app metrics
        assert metrics[GenericAppMetricsEnum.SKIPPED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.ERRORED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.DUPLICATE_COUNT] == 0
        assert metrics[OrderAppMetricsEnum.OUTPUT_SYNTHETIC_NEWOS_COUNT] == 1
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 2
        assert metrics[OrderAppMetricsEnum.SYNTHETIC_INSTRUMENTS_COUNT] == 2

        # Assert audits match the expected results:
        # For both orders, the instrument is created through fallback,
        # because we are mocking the SRP connection.
        # There is also a workflow_status message because the ECB rates are mocked.
        audit_result = read_json(AUDIT_PATH.as_posix())
        assert audit_result == {
            "input_records": {
                "480259003:1:480259003:PARF:2024-11-19T00:04:20.756000Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "M|41906303:1:NEWO": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
            },
            "workflow_status": ["BestExFxRatesPlugin:ecb ref rates not found "],
        }

    @mock_aws
    def test_order_aladdin_v2_market_side_orders_flattened(
        self, mocker, sample_input_market_side_orders_flattened: AriesTaskInput
    ):
        create_and_add_objects_to_s3_bucket(
            bucket_name=TEST_BUCKET,
            source_path=SCENARIO_MARKET_SIDE_ORDERS_PATH.joinpath("buckets", TEST_BUCKET),
        )

        aries_task_result = self._run_aries_task(
            mocker=mocker,
            sample_aries_task_input=sample_input_market_side_orders_flattened,
            scenario="scenario_market_side_orders",
        )

        # Assert the NDJSONs produced by the Flow contain the expected data
        for output_key in ["orders", "synthetic_newo"]:
            output_uri: str = (
                aries_task_result.output_param.params.get(output_key).get("params").get("file_uri")  # type: ignore[union-attr]
            )
            local_file_path: str = run_download_file(
                file_url=output_uri,
            )
            result: pd.DataFrame = pd.read_json(local_file_path, lines=True)

            # Assert the output records are schema compliant
            # and do not raise any SteelEye Data validations, apart from the ones
            # that are expected due to having mocked BestExecution
            assert_record_is_schema_compliant(
                input_df=result,
                model=Order,
                accepted_validation_error_codes=[
                    SteeleyeRecordErrorCodesEnum.SE_DV_142,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_361,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_363,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_365,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_367,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_136,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_138,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_369,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_371,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_373,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_375,  # bestEx results are mocked
                ],
            )

            expected_result = pd.read_json(
                SCENARIO_MARKET_SIDE_ORDERS_PATH.joinpath(
                    f"expected_flattened_{output_key}.ndjson"
                ).as_posix(),
                lines=True,
            )
            os.remove(local_file_path)
            pd.testing.assert_frame_equal(left=result, right=expected_result)

        assert aries_task_result.output_param.params == {  # type: ignore[union-attr]
            "orders": {
                "params": {
                    "data_model": "se_elastic_schema.models.tenant.mifid2.order:Order",
                    "es_action": "create",
                    "file_uri": f"{CURRENT_PATH}/data/valid_executions/scenario_market_side_orders/orders.ndjson",
                }
            },
            "synthetic_newo": {
                "params": {
                    "data_model": "se_elastic_schema.models.tenant.mifid2.order:Order",
                    "es_action": "create",
                    "file_uri": f"{CURRENT_PATH}/data/valid_executions/scenario_market_side_orders/synthetic.ndjson",
                }
            },
        }

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "order_aladdin_v2"
            ]["order_aladdin_v2"]
            if aries_task_result.app_metric
            else {}
        )

        # Assert app metrics
        # The input is 56 PARFs for one placementId + 2 more PARFs for another placementId
        # That results in 2 flattened FILLs + their 2 synthetic NEWOs
        assert metrics[GenericAppMetricsEnum.SKIPPED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.ERRORED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.DUPLICATE_COUNT] == 0
        assert metrics[OrderAppMetricsEnum.OUTPUT_SYNTHETIC_NEWOS_COUNT] == 2
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 4
        assert metrics[OrderAppMetricsEnum.SYNTHETIC_INSTRUMENTS_COUNT] == 4

        # Assert audits match the expected results:
        # For both orders, the instrument is created through fallback,
        # because we are mocking the SRP connection.
        # There is also a workflow_status message because the ECB rates are mocked.
        audit_result = read_json(AUDIT_PATH.as_posix())
        assert audit_result == {
            "input_records": {
                "48023958:1:48023958:FILL:2024-10-23T06:00:00Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "48027457:1:48027457:FILL:2024-10-23T06:00:00Z": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "M|29701057:1:NEWO": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
                "M|99999999:1:NEWO": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "This instrument was created through fallback and was not available in reference data"
                        ],
                        "updated": 0,
                    }
                },
            },
            "workflow_status": ["BestExFxRatesPlugin:ecb ref rates not found "],
        }

    @staticmethod
    def test_link_parties_multiple_clashing_hits(mocker):
        """
        The goal of this test is to validate that LinkParties
        can handle multiple clashing hits for the same party role
        and that it will choose the right hit based on the
        PartiesMatchingConditions parameter.

        Specifically, the client ID can only link with a record
        with details.firmType = "Client" and the counterparty ID
        can only link with a record with details.firmType = "Counterparty".
        """

        class MockEsClient:
            def __init__(self):
                self.MAX_TERMS_SIZE = 1024

            @staticmethod
            def scroll(*args, **kwargs):
                """
                Mock ElasticSearch LinkParties queries.
                The query will find multiple records with the same
                unique props, simulating the Aladdin MyMarket Broker/Portfolio data.
                """
                return pd.DataFrame(
                    {
                        "&id": [
                            "party1",
                            "party2",
                            "party3",
                            "party4",
                            "party5",
                        ],
                        "&key": [
                            "MarketCounterparty:party1:123456",
                            "MarketCounterparty:party2:123456",
                            "MarketCounterparty:party3:123456",
                            "MarketCounterparty:party4:123456",
                            "MarketCounterparty:party5:123456",
                        ],
                        "&model": [
                            "MarketCounterparty",
                            "MarketCounterparty",
                            "MarketCounterparty",
                            "MarketCounterparty",
                            "MarketCounterparty",
                        ],
                        "&uniqueProps": [
                            ["lei:549300baczp326uobh06"],
                            ["id:25"],
                            ["id:25"],  # Clashing hit
                            ["lei:549300baczp326uobh06"],
                            ["lei:549300baczp326uobh06"],  # Clashing hit
                        ],
                        "name": [
                            "Buyer 1",
                            "Not a client",
                            "Actually a client",
                            "Actually a counterparty",
                            "Not a counterparty",
                        ],
                        "details.firmType": [
                            pd.NA,
                            "Counterparty",  # cannot link with client ID
                            "Client",
                            "Counterparty",
                            "Client",  # cannot link with counterparty ID
                        ],
                    }
                )

        mock_es_client = MockEsClient()

        source_frame = pd.DataFrame(
            {
                OrderColumns.MARKET_IDENTIFIERS_PARTIES: [
                    [
                        {"labelId": "id:25", "path": "buyer", "type": IdentifierType.ARRAY},
                        {
                            "labelId": "lei:549300baczp326uobh06",
                            "path": "reportDetails.executingEntity",
                            "type": IdentifierType.OBJECT,
                        },
                        {
                            "labelId": "lei:549300baczp326uobh06",
                            "path": "seller",
                            "type": IdentifierType.ARRAY,
                        },
                        {
                            "labelId": "lei:549300baczp326uobh06",  # matches 2 hits in Elastic
                            "path": "counterparty",
                            "type": IdentifierType.OBJECT,
                        },
                        {
                            "labelId": "id:bmx",
                            "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm",
                            "type": IdentifierType.OBJECT,
                        },
                        {
                            "labelId": "id:dxf",
                            "path": "tradersAlgosWaiversIndicators.executionWithinFirm",
                            "type": IdentifierType.OBJECT,
                        },
                        {
                            "labelId": "id:25",  # matches 2 hits in Elastic
                            "path": "clientIdentifiers.client",
                            "type": IdentifierType.ARRAY,
                        },
                        {"labelId": "id:dxf", "path": "trader", "type": IdentifierType.ARRAY},
                    ]
                ]
            }
        )

        result = (
            DefaultAladdinV2()
            .link_parties_and_dedupe_by_firm_type(
                aries_task_input=addict.Dict({"workflow": {"tenant": "test"}}),
                es_client_tenant=mock_es_client,
                transformed_df=source_frame,
                app_metrics_path="",
                audit_path="",
            )
            .frame()
            .to_dict(orient="records")
        )

        assert (
            result
            == [
                {
                    "buyer": [
                        {
                            "&id": "party2",
                            "&key": "MarketCounterparty:party2:123456",
                            "details.firmType": "Counterparty",
                            "name": "Not a client",
                        }
                    ],
                    "clientIdentifiers.client": [  # the client actually linked with a firmType = "Client" ES hit
                        {
                            "&id": "party3",
                            "&key": "MarketCounterparty:party3:123456",
                            "details.firmType": "Client",
                            "name": "Actually a client",
                        }
                    ],
                    "counterparty": {  # the client actually linked with a firmType = "Counterparty" ES hit
                        "&id": "party4",
                        "&key": "MarketCounterparty:party4:123456",
                        "details.firmType": "Counterparty",
                        "name": "Actually a counterparty",
                    },
                    "reportDetails.executingEntity": {
                        "&id": "party1",
                        "&key": "MarketCounterparty:party1:123456",
                        "details.firmType": None,
                        "name": "Buyer 1",
                    },
                    "seller": [
                        {
                            "&id": "party1",
                            "&key": "MarketCounterparty:party1:123456",
                            "details.firmType": None,
                            "name": "Buyer 1",
                        }
                    ],
                }
            ]
        )

    @staticmethod
    def test_link_parties_no_clashing_hits(mocker):
        """
        The goal of this test is to validate that LinkParties
        will NOT exclude hits when the PartiesMatchingConditions
        do not match any of the hits.
        """

        class MockEsClient:
            def __init__(self):
                self.MAX_TERMS_SIZE = 1024

            @staticmethod
            def scroll(*args, **kwargs):
                """
                Mock ElasticSearch LinkParties queries.
                """
                return pd.DataFrame(
                    {
                        "&id": [
                            "party1",
                            "party2",
                        ],
                        "&key": [
                            "MarketCounterparty:party1:123456",
                            "MarketCounterparty:party2:123456",
                        ],
                        "&model": [
                            "MarketCounterparty",
                            "MarketCounterparty",
                        ],
                        "&uniqueProps": [
                            ["lei:549300baczp326uobh06"],
                            ["id:25"],
                        ],
                        "name": [
                            "Client foo",
                            "Counterparty bar",
                        ],
                        "details.firmType": [  # even though this is null, LinkParties must still find hits
                            pd.NA,
                            pd.NA,
                        ],
                    }
                )

        mock_es_client = MockEsClient()

        source_frame = pd.DataFrame(
            {
                OrderColumns.MARKET_IDENTIFIERS_PARTIES: [
                    [
                        {
                            "labelId": "lei:549300baczp326uobh06",
                            "path": "counterparty",
                            "type": IdentifierType.OBJECT,
                        },
                        {
                            "labelId": "id:25",
                            "path": "clientIdentifiers.client",
                            "type": IdentifierType.ARRAY,
                        },
                    ]
                ]
            }
        )

        result = (
            DefaultAladdinV2()
            .link_parties_and_dedupe_by_firm_type(
                aries_task_input=addict.Dict({"workflow": {"tenant": "test"}}),
                es_client_tenant=mock_es_client,
                transformed_df=source_frame,
                app_metrics_path="",
                audit_path="",
            )
            .frame()
            .to_dict(orient="records")
        )

        assert (
            result
            == [
                {
                    "clientIdentifiers.client": [
                        {
                            "&id": "party2",  # It successfully linked with the ES hit, despite the firmType
                            "&key": "MarketCounterparty:party2:123456",
                            "details.firmType": None,
                            "name": "Counterparty bar",
                        }
                    ],
                    "counterparty": {
                        "&id": "party1",  # It successfully linked with the ES hit, despite the firmType
                        "&key": "MarketCounterparty:party1:123456",
                        "details.firmType": None,
                        "name": "Client foo",
                    },
                }
            ]
        )

    @staticmethod
    @freeze_time(time_to_freeze="2023-11-09 08:00:00.000000+00:00")
    def _run_aries_task(
        mocker, sample_aries_task_input: AriesTaskInput, scenario: str
    ) -> AriesTaskResult:
        aries_task_input = sample_aries_task_input

        mock_search_instruments_cached = mocker.patch(
            "se_trades_tasks.order_and_tr.instrument.link.link_instrument._search_instruments_cached"
        )
        mock_search_instruments_cached.return_value = []

        mock_create_ndjson_process = mocker.patch(
            "integration_trades_tasks.order.feeds.order_aladdin_v2.order_aladdin_v2_flow.create_ndjson_path"
        )
        mock_create_ndjson_process.side_effect = get_result_paths(scenario=scenario)

        mock_get_es_config = mocker.patch.object(order_aladdin_v2_flow_module, "get_es_config")
        mock_get_es_config.return_value = ResourceConfig(
            host="localhost",
            port=9999,
            scheme="http",
        )

        mock_get_es_srp_config = mocker.patch.object(
            order_aladdin_v2_flow_module, "get_srp_es_config"
        )
        mock_get_es_srp_config.return_value = ResourceConfig(
            host="localhost",
            port=9999,
            scheme="http",
            meta_prefix="&",
        )

        mock_es_repo = mocker.patch.object(
            order_aladdin_v2_flow_module, "get_repository_by_cluster_version"
        )

        class MockEsObject:
            def __init__(self):
                self.MAX_TERMS_SIZE = 1024

            @staticmethod
            def scroll(*args, **kwargs):
                """Mock ElasticSearch connections.

                Tasks such as LinkInstruments, LinkParties and
                PartyFallbackWithLeiLookup are already tested
                extensively in se-core/trades-tasks. Other ElasticSearch
                lookup logic, such as the tradedQuantity mapping, is
                tested in the mapping class's tests
                """
                return pd.DataFrame()

            @staticmethod
            def search(*args, **kwargs):
                return addict.Dict(
                    {
                        "hits": {
                            "hits": [
                                {"_source": {"firmIdentifiers": {"lei": "549300BACZP326UOBH06"}}}
                            ]
                        }
                    }
                )

        mock_es_repo.return_value = MockEsObject()
        mock_srp_es_repo = mocker.patch.object(DefaultAladdinV2, "instantiate_srp_es_client")
        mock_srp_es_repo.return_value = MockEsObject()

        mock_task = mocker.patch(
            "integration_wrapper.integration_aries_task.write_named_temporary_json"
        )
        mock_task.side_effect = write_named_temporary_json_side_effect
        mocker.patch("integration_audit.auditor.write_json")

        mocker.patch.object(
            target=CachedTenantWorkflowAPIClient,
            attribute="get",
            return_value=addict.Dict(
                {
                    "tenant": {
                        "lake_prefix": "s3://test.dev.steeleye.co",
                    },
                    "workflow": {"streamed": False},
                    "max_batch_size": 10000,
                },
            ),
        )

        # Mock EOD stats fetch for Best Ex
        mock_eod_stats_run = mocker.patch(
            "se_trades_tasks.order.best_execution.best_execution.run_order_eod_stats_enricher"
        )
        mock_eod_stats_run.return_value = pd.DataFrame()

        # Mock ECB Fetch Rates
        mock_ecb_rates = mocker.patch.object(BestExFxRatesPlugin, "get_ecb_rates")
        mock_ecb_rates.return_value = addict.Dict({})

        # Mock party fallback task
        response_mock = MagicMock()
        response_mock.raw_response.status_code = 200
        response_mock.content = dict()

        lei_mock_object = MagicMock()
        lei_mock_object.bulk_fetch_leis.return_value = response_mock

        mock_party_fallback = mocker.patch(
            "se_trades_tasks.order.party.fallback.party_fallback_with_lei_lookup.Lei"
        )
        mock_party_fallback.return_value = lei_mock_object

        # Run flow
        result: AriesTaskResult = order_aladdin_v2_run(aries_task_input=aries_task_input)
        return result


def write_named_temporary_json_side_effect(output_filename: str, **kwargs) -> str:
    if output_filename == "audit.json":
        with fsspec.open(AUDIT_PATH.as_posix(), "w") as file:
            json.dump({}, file)

        return AUDIT_PATH.as_posix()

    return write_named_temporary_json(output_filename=output_filename, **kwargs)

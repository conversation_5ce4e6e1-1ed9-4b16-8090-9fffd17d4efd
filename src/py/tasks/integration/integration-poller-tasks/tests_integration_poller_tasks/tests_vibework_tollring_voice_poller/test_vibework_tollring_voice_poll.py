# flake8: noqa: E402
import boto3
import json
import os
import requests_mock
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from cloud_utils.cloud import cloud_list_files
from http import HTTPStatus

from integration_poller_tasks.vibework_tollring_voice_poller.vibework_tollring_voice_poll import \
    VibeworkTollringVoicePoll
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from moto import mock_aws
from pathlib import Path
from se_enums.cloud import CloudProviderEnum
from unittest import mock

os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "test"
os.environ["STACK"] = "local"
import datetime
import pytest
from addict import addict
from aries_io_event.app_metric import AppMetricFieldSet
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.model import IOEvent
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from freezegun import freeze_time
from integration_poller_tasks.sgc_voice_poller.sgc_voice_poll import (
    SGCVoicePoll,
    SomeAttachmentsNotDownloadedException,
)
from mock.mock import DEFAULT, patch

CURRENT_PATH = Path(__file__).parent
DATA_PATH = CURRENT_PATH.joinpath("data")
EXPECTED_RESULTS_PATH = DATA_PATH.joinpath("expected_results")

mock_aiobotocore_convert_to_response_dict()


@pytest.fixture()
def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="vibework_tollring_voice_poll",
        stack="dev-blue",
        tenant="test",
        start_timestamp=datetime.datetime.now(),
    )
    input_param = IOParamFieldSet(params=dict())
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_input_with_custom_path() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="vibework_tollring_voice_poll",
        stack="dev-blue",
        tenant="test",
        start_timestamp=datetime.datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            from_date="2022-12-07",  # optional
            to_date="2022-12-11",  # optional
            custom_lake_path="boarding/",  # optional
        )
    )
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def config():
    return addict.Dict(
        {
            "cloud": "aws",
            "aws_region": None,
            "data_platform_config_api_url": "localhost:8080",
            "stack": "local",
            "version": "version",
            "vault": {
                "url": "dev-vault-url",
                "mount_point": "data-engineer",
                "auth_method": "token",
                "token": "",
                "k8s_role": "",
                "k8s_jwt_path": "",
                "k8s_auth_mount_point": "",
            },
        }
    )


MOCK_RESPONSE = {
    "paused": False,
    "id": 3,
    "max_batch_size": None,
    "workflow_last_executed": None,
    "time_created": "2023-06-09T10:06:14.733020",
    "io_topic": "mock_topic",
    "tenant_id": 1,
    "workflow_id": 3,
    "batch_timeout_s": None,
    "workflow_execution_ref": None,
    "time_updated": None,
    "tenant": {
        "id": 1,
        "lake_prefix": "s3://test.dev.steeleye.co/",
        "time_updated": None,
        "stack_id": 1,
        "name": "test",
        "time_created": "2023-06-09T10:00:22.860947",
        "stack": {
            "paused": False,
            "name": "dev-blue",
            "time_updated": None,
            "id": 1,
            "time_created": "2023-06-09T10:00:03.477200",
        },
    },
    "workflow": {
        "s3_feed_prefix": "test",
        "name": "sgc_voice_poll",
        "time_created": "2023-06-09T10:02:34.313252",
        "id": 3,
        "streamed": False,
        "time_updated": None,
    },
}

MOCK_SECRETS = {
    "account": "test-account",
    "client_id": "test-client",
    "client_secret": "test-secret",
    "listener": "test-listener.com",
    "password": "test-password",
    "url": "https://extprov.myphones.net/recordings.aspx",
    "username": "test-username",
}

EXPECTED_EVENT = IOEvent(
    workflow=WorkflowFieldSet(
        name="sgc_voice",
        stack="local",
        tenant="test",
        start_timestamp=datetime.datetime.now(),
    ),
    task=TaskFieldSet(name="sgc_voice_poll", version="version", success=True),
    io_param=IOParamFieldSet(
        params=dict(
            file_uri=mock.ANY,
            streamed=False,
            aries_task_to_domain={},
        ),
    ),
)
EXPECTED_EVENT.workflow.trace_id = mock.ANY
EXPECTED_EVENT.task.id = mock.ANY
EXPECTED_EVENT.task.timestamp = mock.ANY
EXPECTED_EVENT.workflow.start_timestamp = mock.ANY

MOCK_RESPONSE = {"tenant": {"lake_prefix": "s3://test.dev.steeleye.co/"}}


@pytest.fixture()
def response_metadata_mock():
    return {
        "Data": [
            {
                "RecordId": "12345",
                "Call_date": "2025-09-01T00:00:00",
                "Call_time": "17106",
                "Dest_cli": "32141",
                "Caller_cli": "85221086761",
                "Duration": "186",
                "Direction": "O",
                "Call_tag": "",
                "Extension": "1032",
                "LookupResult": "0",
                "Lookup": "0",
                "LookupFlag": "0",
                "isEval": "0",
                "TenantId": "39",
                "StatusId": "1",
                "Date_expiry": "2125-09-01T00:00:00",
                "IsBuffer": "0",
                "IsPause": "false",
                "onDemand": "false",
                "DC_Id": "null"
            },
            {
                "RecordId": "678910",
                "Call_date": "2025-09-02T00:00:00",
                "Call_time": "1734",
                "Dest_cli": "753424",
                "Caller_cli": "85221086769",
                "Duration": "189",
                "Direction": "O",
                "Call_tag": "",
                "Extension": "1032",
                "LookupResult": "0",
                "Lookup": "0",
                "LookupFlag": "0",
                "isEval": "0",
                "TenantId": "390",
                "StatusId": "1",
                "Date_expiry": "2125-09-01T00:00:00",
                "IsBuffer": "0",
                "IsPause": "false",
                "onDemand": "false",
                "DC_Id": "null"
            },
        ]
    }


@patch.multiple(
    "se_comms_ingress_utils.abstractions.api_voice_poller",
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
)
@patch.multiple(
    "integration_poller_tasks.sgc_voice_poller.vibework_tollring_voice_poll",
    update_poller_last_execution_time=DEFAULT,
)
@requests_mock.Mocker(kw="req_mock")
@freeze_time(time_to_freeze="2025-04-23 06:59:38.911459+00:00")
@mock_aws
def test_vibework_tollring_voice_success_with_custom_path(
    sample_input_with_custom_path, config, response_metadata_mock, **kwargs
):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    s3_create_bucket(bucket_name="test.dev.steeleye.co")

    kwargs["req_mock"].post(
        "https://recording.nfon.com/api/v4/GetRecordingsByDate",
        headers={"Authorization": "Basic ABCD1234"},
        content=b"free",
        status_code=HTTPStatus.OK,
    )
    kwargs["req_mock"].post(
        "https://recording.nfon.com/api/v4/GetRecordingFile",
        headers={"Authorization": "Bearer ABCD"},
        content=b"free",
        status_code=HTTPStatus.OK,
    )

    poller = VibeworkTollringVoicePoll(config=config, aries_task_input=sample_input_with_custom_path)
    output = poller.run_poller()

    poller_file_results = cloud_list_files(
        cloud_provider=CloudProviderEnum.AWS,
        bucket="test.dev.steeleye.co",
        folder_path="boarding/aries/ingress/depository/attachments/vibework_tollring_voice_poll/2025/04/23/test/",
        return_absolute_path=True,
    )
    metadata_result = [item for item in poller_file_results if item.endswith(".json")]
    attachment_result = [item for item in poller_file_results if item.endswith(".mp3")]

    for i, val in enumerate(metadata_result):
        metadata_local_file_path = run_download_file(file_url=val)
        with open(metadata_local_file_path, "r") as f:
            metadata_result = json.load(f)

        with open(
            EXPECTED_RESULTS_PATH.joinpath(f"expected_result_custom_data_{i}.json"), "r"
        ) as f:
            expected_metadata_result = json.load(f)

        assert metadata_result == expected_metadata_result

    assert len(attachment_result) == 2

    kwargs["update_poller_last_execution_time"].assert_called_once()

    assert output == AriesTaskResult(
        output_param=None, app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})
    )


@patch.multiple(
    "se_comms_ingress_utils.abstractions.api_voice_poller",
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
)
@patch.multiple(
    "integration_poller_tasks.sgc_voice_poller.sgc_voice_poll",
    update_poller_last_execution_time=DEFAULT,
)
@requests_mock.Mocker(kw="req_mock")
@freeze_time(time_to_freeze="2025-04-23 06:59:38.911459+00:00")
@mock_aws
def test_sgc_voice_failure_attachment_exception(
    sample_input, config, response_metadata_mock, **kwargs
):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    s3_create_bucket(bucket_name="test.dev.steeleye.co")

    kwargs["req_mock"].get(
        "https://extprov.myphones.net/recordings.aspx",
        headers={"Authorization": "Bearer ABCD"},
        json=response_metadata_mock,
        status_code=HTTPStatus.OK,
    )
    kwargs["req_mock"].get(
        "https://content-h.telephony-cloud.com/content/dHR3SWhTSQWNOcz0=",
        headers={"Authorization": "Bearer ABCD"},
        content=b"free",
        status_code=HTTPStatus.FORBIDDEN,
    )
    kwargs["req_mock"].get(
        "https://content-h.telephony-cloud.com/content/dHR3SWhTSQW12345NOcz0=",
        headers={"Authorization": "Bearer ABCD"},
        content=b"free",
        status_code=HTTPStatus.UNAUTHORIZED,
    )

    poller = SGCVoicePoll(config=config, aries_task_input=sample_input)
    with pytest.raises(SomeAttachmentsNotDownloadedException):
        poller.run_poller()

    poller_file_results = cloud_list_files(
        cloud_provider=CloudProviderEnum.AWS,
        bucket="test.dev.steeleye.co",
        folder_path="boarding/aries/ingress/streamed/evented/sgc_voice_poll/2025/04/23/",
        return_absolute_path=True,
    )
    metadata_result = [item for item in poller_file_results if item.endswith(".json")]
    attachment_result = [item for item in poller_file_results if item.endswith(".mp3")]

    for i, val in enumerate(metadata_result):
        metadata_local_file_path = run_download_file(file_url=val)
        with open(metadata_local_file_path, "r") as f:
            metadata_result = json.load(f)

        with open(
            EXPECTED_RESULTS_PATH.joinpath(f"expected_result_custom_data_{i}.json"), "r"
        ) as f:
            expected_metadata_result = json.load(f)

        assert metadata_result == expected_metadata_result

    assert len(attachment_result) == 0

    kwargs["update_poller_last_execution_time"].assert_called_once()


def s3_create_bucket(bucket_name: str):
    """Adds marker files to a mock S3 bucket.

    :param bucket_name: Bucket name of Mock S3 bucket
    :return: None, uploads files to the mock S3 bucket
    """

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")
    s3.create_bucket(Bucket=bucket_name)

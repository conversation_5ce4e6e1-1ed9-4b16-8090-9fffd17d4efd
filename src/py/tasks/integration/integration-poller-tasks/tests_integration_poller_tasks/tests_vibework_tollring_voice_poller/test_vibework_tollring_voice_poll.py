# flake8: noqa: E402
# flake8: noqa: E501
import os

os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "test"
os.environ["STACK"] = "local"
import boto3
import datetime
import json
import pytest
import requests_mock
from addict import addict
from aries_io_event.app_metric import AppMetricFieldSet
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.model import IOEvent
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_task_link.models import AriesTaskResult
from cloud_utils.cloud import cloud_list_files
from freezegun import freeze_time
from http import HTTPStatus
from integration_poller_tasks.vibework_tollring_voice_poller.vibework_tollring_voice_poll import (
    SomeAttachmentsNotDownloadedException,
    VibeworkTollringVoicePoll,
)
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from mock.mock import DEFAULT, patch
from moto import mock_aws
from pathlib import Path
from se_enums.cloud import CloudProviderEnum
from unittest import mock

CURRENT_PATH = Path(__file__).parent
DATA_PATH = CURRENT_PATH.joinpath("data")
EXPECTED_RESULTS_PATH = DATA_PATH.joinpath("expected_results")

mock_aiobotocore_convert_to_response_dict()

MOCK_RESPONSE = {
    "paused": False,
    "id": 3,
    "max_batch_size": None,
    "workflow_last_executed": None,
    "time_created": "2023-06-09T10:06:14.733020",
    "io_topic": "mock_topic",
    "tenant_id": 1,
    "workflow_id": 3,
    "batch_timeout_s": None,
    "workflow_execution_ref": None,
    "time_updated": None,
    "tenant": {
        "id": 1,
        "lake_prefix": "s3://test.dev.steeleye.co/",
        "time_updated": None,
        "stack_id": 1,
        "name": "test",
        "time_created": "2023-06-09T10:00:22.860947",
        "stack": {
            "paused": False,
            "name": "dev-blue",
            "time_updated": None,
            "id": 1,
            "time_created": "2023-06-09T10:00:03.477200",
        },
    },
    "workflow": {
        "s3_feed_prefix": "test",
        "name": "vibework_tollring_voice_poll",
        "time_created": "2023-06-09T10:02:34.313252",
        "id": 3,
        "streamed": False,
        "time_updated": None,
    },
}

MOCK_SECRETS = {
    "TOLLRING_EMAIL": "test-email",
    "TOLLRING_TOKEN_ID": "test-token-id",
}

EXPECTED_EVENT = IOEvent(
    workflow=WorkflowFieldSet(
        name="vibework_tollring_voice",
        stack="local",
        tenant="test",
        start_timestamp=datetime.datetime.now(),
    ),
    task=TaskFieldSet(name="vmo2_poll", version="version", success=True),
    io_param=IOParamFieldSet(
        params=dict(
            file_uri=mock.ANY,
            streamed=False,
            aries_task_to_domain={},
        ),
    ),
)
EXPECTED_EVENT.workflow.trace_id = mock.ANY
EXPECTED_EVENT.task.id = mock.ANY
EXPECTED_EVENT.task.timestamp = mock.ANY
EXPECTED_EVENT.workflow.start_timestamp = mock.ANY

MOCK_RESPONSE = {"tenant": {"lake_prefix": "s3://test.dev.steeleye.co/"}}


@patch.multiple(
    "se_comms_ingress_utils.abstractions.api_voice_poller",
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
)
@patch.multiple(
    "integration_poller_tasks.vibework_tollring_voice_poller.vibework_tollring_voice_poll",
    update_poller_last_execution_time=DEFAULT,
    get_attachment_file_path=DEFAULT,
    secrets_client=DEFAULT,
)
@requests_mock.Mocker(kw="req_mock")
@freeze_time(time_to_freeze="2025-04-23 06:59:38.911459+00:00")
@mock_aws
def test_vibework_tollring_voice_success_with_custom_path(
    sample_input_with_custom_path,
    config,
    response_metadata_mock,
    **kwargs,
):
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    s3_create_bucket(bucket_name="test.dev.steeleye.co")
    kwargs[
        "get_attachment_file_path"
    ].return_value = "boarding/aries/ingress/depository/attachments/vibework_tollring_voice_poll/2025/04/23/abcd123"

    kwargs["req_mock"].post(
        "https://recording.nfon.com/api/v4/GetRecordingsByDate",
        json={
            "Result": {"ErrorCode": "0x0000", "Description": "Success"},
            "Data": response_metadata_mock,
        },
        status_code=HTTPStatus.OK,
    )

    kwargs["req_mock"].post(
        "https://recording.nfon.com/api/v4/GetRecordingFile",
        content=b"mock-audio-data",
        status_code=HTTPStatus.OK,
    )

    poller = VibeworkTollringVoicePoll(
        config=config, aries_task_input=sample_input_with_custom_path
    )
    output = poller.run_poller()

    poller_attachments_file_results = cloud_list_files(
        cloud_provider=CloudProviderEnum.AWS,
        bucket="test.dev.steeleye.co",
        folder_path="boarding/aries/ingress/depository/attachments/vibework_tollring_voice_poll/2025/04/23/abcd123/",
        return_absolute_path=True,
    )
    poller_metadata_file_results = cloud_list_files(
        cloud_provider=CloudProviderEnum.AWS,
        bucket="test.dev.steeleye.co",
        folder_path="boarding/aries/ingress/nonstreamed/evented/vibework_tollring_voice_poll/2025/04/23/",
        return_absolute_path=True,
    )
    metadata_result = [item for item in poller_metadata_file_results if item.endswith(".json")]
    attachment_result = [item for item in poller_attachments_file_results if item.endswith(".wav")]

    # There should be a single batched JSON file; validate entries
    assert len(metadata_result) == 1
    metadata_local_file_path = run_download_file(file_url=metadata_result[0])
    with open(metadata_local_file_path, "r") as f:
        saved_metadata_list = json.load(f)

    assert isinstance(saved_metadata_list, list)
    assert len(saved_metadata_list) == 2

    for i, saved_row in enumerate(saved_metadata_list):
        with open(
            EXPECTED_RESULTS_PATH.joinpath(f"expected_result_custom_data_{i}.json"), "r"
        ) as f:
            expected_metadata_result = json.load(f)
        assert saved_row == expected_metadata_result

    assert len(attachment_result) == 2

    assert output == AriesTaskResult(
        output_param=None, app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})
    )


@patch.multiple(
    "se_comms_ingress_utils.abstractions.api_voice_poller",
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
)
@patch.multiple(
    "integration_poller_tasks.vibework_tollring_voice_poller.vibework_tollring_voice_poll",
    update_poller_last_execution_time=DEFAULT,
    get_attachment_file_path=DEFAULT,
    secrets_client=DEFAULT,
)
@requests_mock.Mocker(kw="req_mock")
@freeze_time(time_to_freeze="2025-04-23 06:59:38.911459+00:00")
@mock_aws
def test_vibework_tollring_voice_failure_attachment_exception(
    sample_input_with_custom_path, config, response_metadata_mock, **kwargs
):
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    s3_create_bucket(bucket_name="test.dev.steeleye.co")
    kwargs[
        "get_attachment_file_path"
    ].return_value = "boarding/aries/ingress/depository/attachments/vibework_tollring_voice_poll/2025/04/23/abcd123"

    kwargs["req_mock"].post(
        "https://recording.nfon.com/api/v4/GetRecordingsByDate",
        json={
            "Result": {"ErrorCode": "0x0000", "Description": "Success"},
            "Data": response_metadata_mock,
        },
        status_code=HTTPStatus.OK,
    )

    kwargs["req_mock"].post(
        "https://recording.nfon.com/api/v4/GetRecordingFile",
        content=b"mock-audio-data",
        status_code=HTTPStatus.UNAUTHORIZED,
    )

    poller = VibeworkTollringVoicePoll(
        config=config, aries_task_input=sample_input_with_custom_path
    )
    with pytest.raises(SomeAttachmentsNotDownloadedException):
        poller.run_poller()

    poller_attachments_file_results = cloud_list_files(
        cloud_provider=CloudProviderEnum.AWS,
        bucket="test.dev.steeleye.co",
        folder_path="boarding/aries/ingress/depository/attachments/vibework_tollring_voice_poll/2025/04/23/abcd123/",
        return_absolute_path=True,
    )
    poller_metadata_file_results = cloud_list_files(
        cloud_provider=CloudProviderEnum.AWS,
        bucket="test.dev.steeleye.co",
        folder_path="boarding/aries/ingress/nonstreamed/evented/vibework_tollring_voice_poll/2025/04/23/",
        return_absolute_path=True,
    )

    metadata_result = [item for item in poller_metadata_file_results if item.endswith(".json")]
    attachment_result = [item for item in poller_attachments_file_results if item.endswith(".wav")]

    for i, val in enumerate(metadata_result):
        metadata_local_file_path = run_download_file(file_url=val)
        with open(metadata_local_file_path, "r") as f:
            metadata_result = json.load(f)

        with open(EXPECTED_RESULTS_PATH.joinpath("expected_result_metadata.json"), "r") as f:
            expected_metadata_result = json.load(f)

        assert metadata_result == expected_metadata_result

    assert len(attachment_result) == 0

    kwargs["update_poller_last_execution_time"].assert_called_once()


def s3_create_bucket(bucket_name: str):
    """Adds marker files to a mock S3 bucket.

    :param bucket_name: Bucket name of Mock S3 bucket
    :return: None, uploads files to the mock S3 bucket
    """

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")
    s3.create_bucket(Bucket=bucket_name)

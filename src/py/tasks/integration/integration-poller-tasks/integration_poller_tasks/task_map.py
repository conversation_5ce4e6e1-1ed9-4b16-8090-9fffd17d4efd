# ruff: noqa: E501
from typing import Callable


def resolve_task_func(task_name: str) -> Callable:
    match task_name:
        case "bloomberg_poll":
            from integration_poller_tasks.bloomberg_poller.bloomberg_poller_task import (
                bloomberg_poll_run,
            )

            return bloomberg_poll_run
        case "call_cabinet_voice_poll":
            from integration_poller_tasks.call_cabinet_voice_poller.call_cabinet_poller_task import (
                call_cabinet_voice_poll_run,
            )

            return call_cabinet_voice_poll_run
        case "clarifygo_oi_voice_poll":
            from integration_poller_tasks.clarifygo_oi_voice_poller.clarifygo_oi_voice_poller_task import (
                clarifygo_oi_voice_poll_run,
            )

            return clarifygo_oi_voice_poll_run
        case "cloud9_voice_poll":
            from integration_poller_tasks.cloud9_voice_poller.cloud9_voice_poller_task import (
                cloud9_voice_poll_run,
            )

            return cloud9_voice_poll_run
        case "deepview_chat_poll":
            from integration_poller_tasks.deepview_chat_poller.deepview_chat_poller_task import (
                deepview_chat_poll_run,
            )

            return deepview_chat_poll_run
        case "dubber_voice_poll":
            from integration_poller_tasks.dubber_voice_poller.dubber_voice_poller_task import (
                dubber_voice_poll_run,
            )

            return dubber_voice_poll_run
        case "eze_eclipse_poll":
            from integration_poller_tasks.eze_eclipse_poller.eze_eclipse_poller_task import (
                eze_eclipse_poll_run,
            )

            return eze_eclipse_poll_run
        case "gamma_horizon_voice_poll":
            from integration_poller_tasks.gamma_horizon_voice_poller.gamma_horizon_voice_poller_task import (
                gamma_horizon_voice_poll_run,
            )

            return gamma_horizon_voice_poll_run
        case "ice_chat_poll":
            from integration_poller_tasks.ice_chat_poller.ice_chat_poller_task import (
                ice_chat_poll_run,
            )

            return ice_chat_poll_run
        case "kerv_text_poll":
            from integration_poller_tasks.kerv_text_poller.kerv_text_poller_task import (
                kerv_text_poll_run,
            )

            return kerv_text_poll_run
        case "kerv_voice_poll":
            from integration_poller_tasks.kerv_voice_poller.kerv_voice_poller_task import (
                kerv_voice_poll_run,
            )

            return kerv_voice_poll_run
        case "ms_graph_email_poll":
            from integration_poller_tasks.ms_graph_email_poll.ms_graph_email_poll_task import (
                ms_graph_email_poll_run,
            )

            return ms_graph_email_poll_run
        case "ms_teams_email_ing_poll":
            from integration_poller_tasks.ms_teams_email_ing_poll.ms_teams_email_ing_poll_task import (
                ms_teams_email_ing_poll_run,
            )

            return ms_teams_email_ing_poll_run
        case "ms_teams_poll":
            from integration_poller_tasks.ms_teams_poll.ms_teams_poll_task import ms_teams_poll_run

            return ms_teams_poll_run
        case "ms_teams_chat_poll":
            from integration_poller_tasks.ms_teams_poll_v2.ms_teams_poll_v2_task import (
                ms_teams_poll_v2_run,
            )

            return ms_teams_poll_v2_run
        case "ms_teams_subscriptions_sync":
            from integration_poller_tasks.ms_teams_subscription_management.ms_teams_subscription_management_task import (
                ms_teams_subscription_management_run,
            )

            return ms_teams_subscription_management_run
        case "ms_teams_subscriptions_sync_v2":
            from integration_poller_tasks.ms_teams_subscription_sync_v2.ms_teams_subscription_sync_v2_task import (
                ms_teams_subscription_sync_v2_run,
            )

            return ms_teams_subscription_sync_v2_run
        case "numonix_ix_cloud_voice_poll":
            from integration_poller_tasks.numonix_ix_cloud_voice_poller.numonix_ix_cloud_voice_poller_task import (
                numonix_ix_cloud_voice_poll_run,
            )

            return numonix_ix_cloud_voice_poll_run
        case "o2_sms_poll":
            from integration_poller_tasks.o2_sms_poller.o2_sms_poller_task import o2_sms_poll_run

            return o2_sms_poll_run
        case "o2_voice_poll":
            from integration_poller_tasks.o2_voice_poller.o2_voice_poller_task import (
                o2_voice_poll_run,
            )

            return o2_voice_poll_run
        case "outlook_calendar_poll":
            from integration_poller_tasks.outlook_calendar_poll.outlook_calendar_poll_task import (
                outlook_calendar_poll_run,
            )

            return outlook_calendar_poll_run
        case "refinitiv_tr_eikon_chat_poll":
            from integration_poller_tasks.refinitiv_tr_eikon_chat_poller.refinitiv_tr_eikon_chat_poller_task import (
                refinitiv_tr_eikon_chat_poll_run,
            )

            return refinitiv_tr_eikon_chat_poll_run
        case "ringcentral_voice_poll":
            from integration_poller_tasks.ringcentral_voice_poller.ringcentral_voice_poller_task import (
                ringcentral_voice_poll_run,
            )

            return ringcentral_voice_poll_run
        case "sgc_voice_poll":
            from integration_poller_tasks.sgc_voice_poller.sgc_voice_poller_task import (
                sgc_voice_poll_run,
            )

            return sgc_voice_poll_run
        case "se_sftp_poll":
            from integration_poller_tasks.se_sftp_poller.se_sftp_poller_task import se_sftp_poll_run

            return se_sftp_poll_run
        case "slack_chat_poll":
            from integration_poller_tasks.slack_chat_poller.slack_chat_poller_task import (
                slack_chat_poll_run,
            )

            return slack_chat_poll_run
        case "slack_users_poll":
            from integration_poller_tasks.slack_users_poller.slack_users_poller_task import (
                slack_users_poll_run,
            )

            return slack_users_poll_run
        case "vmo2_poll":
            from integration_poller_tasks.vmo2_poller.vmo2_poller_task import (
                vmo2_poll_run,
            )

            return vmo2_poll_run
        case "symphony_chat_poll":
            from integration_poller_tasks.symphony_chat_poller.symphony_chat_poller_task import (
                symphony_chat_poll_run,
            )

            return symphony_chat_poll_run
        case "verba_voice_poll":
            from integration_poller_tasks.verba_voice_poller.verba_voice_poller_task import (
                verba_voice_poll_run,
            )

            return verba_voice_poll_run
        case "voip_voice_poll":
            from integration_poller_tasks.voip_voice_poller.voip_voice_poller_task import (
                voip_voice_poll_run,
            )

            return voip_voice_poll_run
        case "xima_voice_poll":
            from integration_poller_tasks.xima_voice_poller.xima_voice_poller_task import (
                xima_voice_poll_run,
            )

            return xima_voice_poll_run
        case "zoom_meetings_poll":
            from integration_poller_tasks.zoom_meetings_poller.zoom_meetings_poller_task import (
                zoom_meetings_poller_run,
            )

            return zoom_meetings_poller_run
        case "zoom_phone_voice_poll":
            from integration_poller_tasks.zoom_phone_poller.zoom_phone_poller_task import (
                zoom_phone_poller_run,
            )

            return zoom_phone_poller_run
        case _:
            raise ValueError(f"Task `{task_name}` is not supported")

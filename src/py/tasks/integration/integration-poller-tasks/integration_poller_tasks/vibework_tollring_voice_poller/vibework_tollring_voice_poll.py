import backoff
import base64
import logging
import requests
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_poller_tasks.vibework_tollring_voice_poller.static import Static
from se_comms_ingress_utils.abstractions.api_voice_poller import AbstractAPIVoicePoller
from se_comms_ingress_utils.common_util import (
    get_poll_interval_and_backfill,
    update_poller_last_execution_time,
)
from se_data_lake.lake_path import get_attachment_file_path, get_non_streamed_poller_file_path
from se_fsspec_utils.file_utils import write
from se_secrets_client.utils import get_secrets, secrets_client
from typing import Optional

logger = logging.getLogger(Static.POLLER_NAME)


class SomeAttachmentsNotDownloadedException(Exception):
    pass


ATTACHMENT_FAILURES = []


class VibeworkTollringVoicePoll(AbstractAPIVoicePoller):
    def __init__(self, aries_task_input: AriesTaskInput, config):
        super().__init__(aries_task_input, config)
        self._api_base_url = None
        self._auth_header = None
        self._secrets = get_secrets(
            secret_client=secrets_client(self._config.vault, self._config),
            tenant_name=self._tenant_name,
            workflow_name=self._workflow_name,
            poller_type="api",
        )
        self._user_name = aries_task_input.input_param.params.get("user_name", "default")

    def _create_auth_header(self) -> str:
        """
        Create Basic Authentication header for Tollring API.
        Username: User's email
        Password: Token ID
        """
        username = self._secrets.TOLLRING_EMAIL
        token_id = self._secrets.TOLLRING_TOKEN_ID

        credentials = f"{username}:{token_id}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()

        return f"Basic {encoded_credentials}"

    @backoff.on_exception(
        backoff.expo,
        (requests.exceptions.RequestException, requests.exceptions.HTTPError),
        max_tries=3,
    )
    def _get_metadata(self, from_date: str, to_date: str):
        """
        Retrieve call recording metadata from Tollring API within a date range.
        """
        url = f"{self._api_base_url}/GetRecordingsByDate"

        headers = {
            "Content-Type": "application/json",
            "Authorization": self._auth_header,
        }

        payload = {
            "StartDate": from_date,
            "EndDate": to_date,
        }

        logger.info(f"Fetching metadata for date range: {from_date} to {to_date}")

        response = requests.post(url, headers=headers, json=payload, timeout=Static.REQUEST_TIMEOUT)
        response.raise_for_status()

        response_data = response.json()

        if response_data.get("Result", {}).get("ErrorCode") != "0x0000":
            error_msg = response_data.get("Result", {}).get("Description", "Unknown error")
            raise Exception(f"API Error: {error_msg}")

        records = response_data.get("Data", [])
        logger.info(f"Total metadata records retrieved: {len(records)}")
        return records

    @backoff.on_exception(
        backoff.expo,
        (requests.exceptions.RequestException, requests.exceptions.HTTPError),
        max_tries=3,
    )
    def _get_attachment(self, record_id: str) -> Optional[bytes]:
        """
        Retrieve call recording file from Tollring API using RecordID.

        API Endpoint: POST /api/v4/GetRecordingFile
        Payload: {"RecordID": <id>}
        """
        url = f"{self._api_base_url}/GetRecordingFile"

        headers = {
            "Content-Type": "application/json",
            "Authorization": self._auth_header,
        }

        payload = {"RecordID": record_id}

        logger.info(f"Requesting recording file for RecordID: {record_id}")

        response = requests.post(url, headers=headers, json=payload, timeout=Static.REQUEST_TIMEOUT)

        response.raise_for_status()

        return response.content

    def _init_tollring_api(self):
        """
        Initialize Tollring API configuration and authentication.
        Sets up the base URL and authentication header.
        """
        logger.info("Initializing Tollring API configuration")

        # Get API base URL from secrets/config
        self._api_base_url = Static.BASE_URL

        # Create authentication header
        self._auth_header = self._create_auth_header()

        logger.info(f"Tollring API initialized with base URL: {self._api_base_url}")

    def run_poller(self) -> AriesTaskResult:
        poll_interval_and_backfill_info = get_poll_interval_and_backfill(
            self._aries_task_input,
            self._poller_tenant_workflow_config.workflow_execution_ref[self._user_name]
            if self._poller_tenant_workflow_config.workflow_execution_ref
            else None,
            self._timestamp_now,
        )

        poll_from_date = poll_interval_and_backfill_info.from_date.strftime("%Y-%m-%d %H:%M:%S")
        poll_to_date = poll_interval_and_backfill_info.to_date.strftime("%Y-%m-%d %H:%M:%S")

        from_date = poll_interval_and_backfill_info.from_date
        end_date = poll_interval_and_backfill_info.to_date

        logger.info(
            f"poll_from_date: {poll_from_date}, "
            f"poll_to_date: {poll_to_date}, "
            f"backfill: {poll_interval_and_backfill_info.backfill}"
        )

        path = get_non_streamed_poller_file_path(
            workflow_name=self._workflow_name,
            workflow_start_timestamp=self._timestamp_now,
            workflow_trace_id=self._aries_task_input.workflow.trace_id,
            custom_path=self._custom_lake_path,
            is_evented=True,
        )
        path_prefix = f"{self._poller_tenant_workflow_config.tenant.lake_prefix.rstrip('/')}/{path}"

        attachment_object_path_prefix = get_attachment_file_path(
            workflow_name=self._workflow_name,
            workflow_start_timestamp=self._timestamp_now,
            workflow_trace_id=self._aries_task_input.workflow.trace_id,
            custom_path=self._custom_lake_path,
        )

        # Initialize Tollring API with credentials
        self._init_tollring_api()

        logger.info("[DOWNLOADING] metadata from Tollring API")
        metadata = self._get_metadata(poll_from_date, poll_to_date)
        logger.info(f"[DOWNLOADED] {len(metadata)} metadata rows from Tollring API")

        if len(metadata) == 0:
            logger.info("[NO METADATA FOUND]")
            return AriesTaskResult(output_param=None, app_metric=self._app_metric)

        # Process each metadata record and download associated recording
        for each_metadata_row in metadata:
            record_id = each_metadata_row.get("RecordID") or each_metadata_row.get("RecordId")

            if not record_id:
                logger.warning(f"No RecordID found in metadata row: {each_metadata_row}")
                continue

            try:
                logger.info(f"[DOWNLOADING] recording from Tollring API for RecordID: {record_id}")

                attachment_data = self._get_attachment(str(record_id))

                if not attachment_data:
                    logger.warning(f"No recording data available for RecordID: {record_id}")
                    continue

                logger.info(f"[DOWNLOADED] recording for RecordID: {record_id}")

            except Exception as e:
                logger.error(
                    f"Failed to download recording for RecordID {record_id}: {e.__str__()}"
                )
                ATTACHMENT_FAILURES.append(
                    f"Failed to download recording for RecordID: {record_id}"
                )
                continue

            # Determine file extension (assuming WAV format based on GetRecordingFiles task)
            file_extension = "wav"

            # Construct attachment path in data lake
            attachment_object_path = (
                f"{attachment_object_path_prefix.rstrip('/')}/{record_id}.{file_extension}"
            )
            attachment_file_uri = (
                f"{self._poller_tenant_workflow_config.tenant.lake_prefix.rstrip('/')}"
                f"/{attachment_object_path}"
            )

            logger.info(f"[UPLOADING] recording to `{attachment_file_uri}`")
            write(
                self._destination_fs,
                target_path=attachment_file_uri,
                file_content=attachment_data,
            )
            logger.info(f"[UPLOADED] recording to `{attachment_file_uri}`")

            # Add steeleye metadata with attachment path
            each_metadata_row["steeleye_meta"] = {"attachment_object_path": attachment_object_path}

        self._upload_batched_metadata(metadata, Static.BATCH_SIZE, path_prefix)

        update_poller_last_execution_time(
            last_execution_time=end_date.strftime("%Y-%m-%dT%H:%M:%S"),
            tenant_name=self._tenant_name,
            workflow_name=self._workflow_name,
            flow_env_vars=self._config,
        )

        if ATTACHMENT_FAILURES:
            logger.error(f"Failed to download recordings: {ATTACHMENT_FAILURES}")
            raise SomeAttachmentsNotDownloadedException("Some recordings were not downloaded")

        return AriesTaskResult(output_param=None, app_metric=self._app_metric)

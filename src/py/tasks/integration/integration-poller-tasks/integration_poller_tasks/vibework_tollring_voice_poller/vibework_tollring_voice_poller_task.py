import logging
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_poller_tasks.vibework_tollring_voice_poller.static import Static
from integration_poller_tasks.vibework_tollring_voice_poller.vibework_tollring_voice_poll import (
    VibeworkTollringVoicePoll,
)
from omegaconf import OmegaConf
from pathlib import Path

logger = logging.getLogger(Static.POLLER_NAME)


def vibework_tollring_voice_poll_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    config = OmegaConf.load(
        Path(__file__).parent.joinpath("vibework-tollring-voice-poller-config.yml")
    )
    poller = VibeworkTollringVoicePoll(aries_task_input=aries_task_input, config=config)
    return poller.run_poller()

**Vibework Tollring Voice Poller**

`vibework_tollring_voice_poll` is a poller flow that runs on schedule, gets voice recording metadata and WAV attachments via Vibework (Tollring) APIs, and uploads them to object store following the bundle file specification.

## Approach

The poller follows these steps (implementing the bundle file workflow):

1. Retrieves the last execution time from workflow configuration
2. Determines the polling interval (from_date and to_date) based on the last execution
3. **GetRecordingsByDate**: Makes API call to get recordings metadata between the specified date range
4. **SplitSessions**: Splits the metadata into individual JSON files with proper naming
5. **GetRecordingFiles**: Downloads recording files for each session
6. **CommsVoiceBatchCSV**: Creates a batch CSV file linking metadata and recording files
7. **S3Upload**: Uploads all files to S3 with proper directory structure

## API Details

### GetRecordingsByDate Endpoint:
```
https://recording.nfon.com/api/v4/GetRecordingsByDate
```

### GetRecordingFile Endpoint:
```
https://recording.nfon.com/api/v4/GetRecordingFile
```

The API has the following parameters:
- `RecordId`: Unique identifier for the recording (used in GetRecordingFile)

The date parameters use the format "YYYYMMDD hhmmss".

## S3 Directory Structure

Following the bundle file specification:

**Metadata JSON files**:
```
s3://{tenant.lake_prefix}/ingress/raw/comms/voice/vibework-tollring/json/{date}_{record_id}.json
```

**Recording WAV files**:
```
s3://{tenant.lake_prefix}/ingress/raw/comms/voice/vibework-tollring/wav/{record_id}.wav
```

**Batch CSV files**:
```
s3://{tenant.lake_prefix}/flows/comms-call-vibework-tollring/batch_{timestamp}_{trace_id}.csv
```

## Sample Response from API (metadata)

Expected response format from GetRecordingsByDate:
```json
[
    {
        "RecordId": "481eeaf-9e5a22d5@***********",
        "Call_date": "2025-04-04",
        "Call_time": "11:37:06",
        "Caller_cli": "02073628894",
        "Dest_cli": "0041793632885",
        "Duration": "1299",
        "Direction": "Outbound",
        "Extension": "100"
    },
    {
        "RecordId": "b419da78-acaff175@***********",
        "Call_date": "2025-04-04",
        "Call_time": "12:29:21",
        "Caller_cli": "02073628894",
        "Dest_cli": "0033144941900",
        "Duration": "83",
        "Direction": "Outbound",
        "Extension": "100"
    }
]
```
### Response Attributes

| Attribute | Description |
|-----------|-------------|
| RecordId | Unique identifier for recording |
| Call_date | Date of the call (YYYY-MM-DD format) |
| Call_time | Time of the call (HH:MM:SS format) |
| Caller_cli | Calling CLI – may be anonymous/blank if the caller withheld their number |
| Dest_cli | Called CLI |
| Duration | Duration of recording in seconds |
| Direction | Call direction (Inbound/Outbound) |
| Extension | Extension number |

## Batch CSV Format

The generated batch CSV contains the following columns:
- `S3 meta file`: S3 key for the metadata JSON file
- `S3 recording file`: S3 key for the recording WAV file
- `RecordId`: Unique identifier linking metadata and recording

## Sample Normal Run Input

```python
workflow = WorkflowFieldSet(
    name="vibework_tollring_voice_poll",
    stack="dev-blue",
    tenant="pinafore",
    start_timestamp=datetime.now(),
)
input_param = IOParamFieldSet(
    params=dict()
)
task = TaskFieldSet(name="test", version="latest", success=False)
return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
```

The poller also supports polling data into a custom path.
If `custom_lake_path` is passed, then use custom_lake_path + aries/ingress/... as the landing path.
By default, it is set to None.

Sample with custom lake path:

```python
workflow = WorkflowFieldSet(
    name="vibework_tollring_voice_poll",
    stack="dev-blue",
    tenant="pinafore",
    start_timestamp=datetime.datetime.now(),
)
input_param = IOParamFieldSet(
    params=dict(
        custom_lake_path='onboarding/',  # optional
        from_date="2023-12-01",         # optional - supports multiple formats
        to_date="2023-12-01T14:30:00",  # optional - supports multiple formats
        datetime_format="iso",          # optional - "iso" or "space" format preference
        user_name="default",            # optional - for multi-user configurations
    )
)
task = TaskFieldSet(name="test", version="latest", success=False)
return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
```

App metrics are added when there is a failure event:
  - `app_metric.metrics["generic"]["errored_count"] += 1`


## Date Range and Backfill

The poller supports regular polling intervals based on the last execution time.
Backfill functionality is available through the standard poll interval and backfill mechanism.

## Datetime Format Handling

The poller can handle multiple datetime input formats and convert them to the format expected by the Tollring API:

### Supported Input Formats:
- **ISO format with time**: `2023-12-01T14:30:00`
- **ISO format with microseconds**: `2023-12-01T14:30:00.123456`
- **ISO format with Z suffix**: `2023-12-01T14:30:00Z`
- **Space-separated format**: `2023-12-01 14:30:00`
- **Space format with microseconds**: `2023-12-01 14:30:00.123456`
- **Date only**: `2023-12-01`

### Output Format Configuration:
You can specify the output format preference using the `datetime_format` parameter:
- `"iso"` (default): Outputs `YYYY-MM-DDTHH:MM:SS` format
- `"space"`: Outputs `YYYY-MM-DD HH:MM:SS` format

### Example Usage:
```python
input_param = IOParamFieldSet(
    params=dict(
        from_date="2023-12-01",           # Will be parsed automatically
        to_date="2023-12-01T14:30:00",    # Will be parsed automatically
        datetime_format="iso",            # Output format preference
    )
)
```
# **SlackUsers Poller**


The `SlackUsersPoll` is a scheduled poller designed to retrieve a complete list of user profiles from a Slack workspace.
It identifies new or updated users since its last run, processes their information, and stores it in a CSV file within
the data lake for consumption by downstream processes.

### Change Detection and Caching:
To operate efficiently and avoid redundant data processing, the poller employs a smart caching mechanism.
- For each user, it calculates a SHA-256 hash based on their key attributes (ID, email, name, title, phone).
- Before processing, it reads a metadata file (users_metadata.json) containing the hashes of all users seen in previous runs.
- Only users with hashes not present in the cache (i.e., new or updated users) are processed and written to the output CSV.
- After a successful run, the poller updates the metadata file with the latest set of user hashes.

The output CSV contains the following columns: `hash_id`, `slack_id`, `email`, `name`, `title`, `phone`.

The users metadata is stored at location `"aries/ingress/depository/slack_users/{target}users_metadata.json"` and the users information in `"aries/ingress/nonstreamed/evented/mymarket_slack_person/{date}/{target}users_{timestamp}.csv"`

### Special Capability: Account Person Generation
The poller includes a special mode to generate a distinct data feed intended for creating or updating `AccountPerson` records in other systems.
This is controlled by the `create_account_person` input parameter.

**How it works:** When the `create_account_person` parameter is set to `True` in the task input, the poller's behavior changes in two key ways:
1. Segregated Output Path: The output CSV filename is prefixed with `account_`. This clearly separates this data from the standard user poll output. 
2. Separate Cache: It uses a different metadata file for caching, also prefixed with `account_`. This ensures that the `AccountPerson` generation pipeline runs independently, with its own state and change-tracking, without interfering with other processes that might consume the default Slack user list.

### Sample Normal Run Input:

```
workflow = WorkflowFieldSet(
    name="slack_users_poll",
    stack="uat-shared-1",
    tenant="puneeth",
    start_timestamp=datetime.now(),
)
input_param = IOParamFieldSet(
    params=dict(
        create_account_person=False # Replace with True to run for account person
    )
)
task = TaskFieldSet(name="test", version="latest", success=False)
return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
```

App metrics are added when there is a failure event:
  - `app_metric.metrics["generic"]["errored_count"] += 1`





import boto3
import filecmp
import pytest
import shutil
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_task_link.models import AriesTaskInput
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.generic.xls_to_csv_batches import (  # noqa: E501
    xls_to_csv_batches_criteria,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.file_splitter_by_criteria_task import (  # noqa: E501
    file_splitter_by_criteria_run,
)
from moto import mock_aws
from pathlib import Path
from se_io_utils.tempfile_utils import tmp_directory
from typing import List

tmp_storage: str = tmp_directory().as_posix()

CURRENT_PATH = Path(__file__).parent
RESULT_JSON_PATH = CURRENT_PATH.joinpath("result.json")

DATA_PATH = CURRENT_PATH.joinpath("data")
SAMPLE_FILE_PATH = DATA_PATH.joinpath("Sample_Blotter_Download.xlsx")

EXPECTED_PATH = DATA_PATH.joinpath("expected")
EXPECTED_BATCH_0 = EXPECTED_PATH.joinpath("Sample_Blotter_Download_batch_0.csv")
EXPECTED_BATCH_1 = EXPECTED_PATH.joinpath("Sample_Blotter_Download_batch_1.csv")
EXPECTED_BATCH_2 = EXPECTED_PATH.joinpath("Sample_Blotter_Download_batch_2.csv")

EXPECTED_BATCH_SINGLE = EXPECTED_PATH.joinpath("Sample_Blotter_Download_single.csv")


class TestXlsToCsvBatches:
    @staticmethod
    def tear_down():
        """Deletes temporary files created during the test."""
        shutil.rmtree(tmp_storage)

    @staticmethod
    def s3_add_objects_to_bucket(bucket_name: str, local_file_path: Path, criteria: str):
        """Puts the single input file in the correct path in the mock S3
        bucket.

        :param bucket_name: Bucket name of Mock S3 bucket
        :param local_file_path: File path of the file to be uploaded
        :return: None, uploads files to the mock S3 bucket
        """

        # Create bucket
        s3 = boto3.client("s3", region_name="us-east-1")
        s3.create_bucket(Bucket=bucket_name)
        base_key = f"aries/ingress/nonstreamed/evented/{criteria}/"
        file_name = Path(local_file_path).name
        with open(local_file_path, "rb") as f:
            s3.put_object(
                Bucket=bucket_name,
                Key=f"{base_key}{file_name}",
                Body=f.read(),
            )

    @pytest.mark.parametrize(
        "batch_size, expected_paths",
        [
            (3, [EXPECTED_BATCH_0, EXPECTED_BATCH_1, EXPECTED_BATCH_2]),
            (50, [EXPECTED_BATCH_SINGLE]),
        ],
    )
    @mock_aws
    def test_tr_bbg_emsi_orders_criteria(
        self,
        mocker,
        sample_aries_task_input: AriesTaskInput,
        batch_size: int,
        expected_paths: List[Path],
    ):
        mocker.patch.object(
            target=CachedTenantWorkflowAPIClient,
            attribute="get",
            return_value=addict.Dict(
                {
                    "tenant": {
                        "lake_prefix": "s3://test.dev.steeleye.co",
                    },
                    "max_batch_size": batch_size,
                    "workflow": {"streamed": False, "name": "placeholder"},
                },
            ),
        )

        file_url = SAMPLE_FILE_PATH
        self.s3_add_objects_to_bucket(
            local_file_path=file_url,
            bucket_name="test_tenant.dev.steeleye.co",
            criteria="placeholder",
        )

        criteria_per_workflow_override = {"placeholder": xls_to_csv_batches_criteria}

        mocker.patch(
            "integration_generic_tasks.splitters.file_splitter_by_criteria.file_splitter_by_criteria_flow.criteria_per_workflow",
            criteria_per_workflow_override,
        )

        task_result = file_splitter_by_criteria_run(aries_task_input=sample_aries_task_input)

        result_paths = [
            task_input["io_param"]["params"]["file_uri"]
            for task_input in task_result.output_param.params["dynamicTaskInputs"].values()
        ]

        # Assert Batch Files
        for cloud_file_path, expected_path in zip(result_paths, expected_paths):
            split_local = run_download_file(file_url=cloud_file_path)

            assert filecmp.cmp(split_local, expected_path, shallow=False)

        # Assert Dynamic Tasks Output Structure
        assert task_result.output_param.params["dynamicTasks"][0] == {
            "taskReferenceName": "tr_feed_random_subworkflow_0",
            "type": "SUB_WORKFLOW",
            "subWorkflowParam": {"name": "tr_feed_random_subworkflow"},
        }

        assert task_result.output_param.params["dynamicTaskInputs"][
            "tr_feed_random_subworkflow_0"
        ] == {
            "io_param": {
                "params": {
                    "source_file_uri": "s3://test_tenant.dev.steeleye.co/aries/ingress/nonstreamed/evented/placeholder/Sample_Blotter_Download.xlsx",
                    "file_uri": task_result.output_param.params["dynamicTaskInputs"][
                        "tr_feed_random_subworkflow_0"
                    ]["io_param"]["params"]["file_uri"],
                }
            },
            "workflow": {
                "trace_id": "trace",
                "start_timestamp": "2025-07-31T00:00:00",
                "name": "file-splitter-by-criteria",
                "stack": "dev-shared-2",
                "tenant": "test_tenant",
            },
        }

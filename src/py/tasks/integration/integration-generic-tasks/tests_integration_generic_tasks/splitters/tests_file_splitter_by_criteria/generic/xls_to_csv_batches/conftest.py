import os
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from integration_test_utils.mock.mock_imports import mock_imports

os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "https://test-enterprise.steeleye.co"

mock_imports(mock_retry=True)


@pytest.fixture()
def sample_aries_task_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="file-splitter-by-criteria",
        stack="dev-shared-2",
        tenant="test_tenant",
        start_timestamp=datetime(year=2025, month=7, day=31),
        trace_id="trace",
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test_tenant.dev.steeleye.co/aries/"
            "ingress/nonstreamed/evented/placeholder/Sample_Blotter_Download.xlsx",
            dynamic_tasks={
                "dynamic_task": {
                    "task_reference_name": "tr_feed_random_subworkflow",
                    "name": "tr_feed_random_subworkflow",
                    "type": "SUB_WORKFLOW",
                }
            },
        )
    )
    task = TaskFieldSet(name="file_splitter_by_criteria", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)

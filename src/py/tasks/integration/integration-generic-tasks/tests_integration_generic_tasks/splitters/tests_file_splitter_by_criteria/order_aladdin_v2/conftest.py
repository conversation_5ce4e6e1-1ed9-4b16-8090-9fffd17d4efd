import datetime
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput


@pytest.fixture()
def sample_input_skippable_file():
    workflow = WorkflowFieldSet(
        trace_id="foo",
        name="order_aladdin_v2",
        stack="dev-shared-2",
        tenant="test",
        start_timestamp=datetime.datetime(2024, 1, 22),
    )

    input_param = IOParamFieldSet(
        **{
            "params": {
                "file_uri": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_aladdin_v2/AladdinTCA.Diogo_EQ.Employee.20241023.csv",
                "dynamic_tasks": {
                    "client_side_orders": {
                        "task_reference_name": "client_side_orders_processing",
                        "input_parameters": {"flow_override": "client"},
                        "name": "order_aladdin_v2_subworkflow",
                        "type": "SUB_WORKFLOW",
                    },
                    "market_side_orders": {
                        "task_reference_name": "market_side_orders_processing",
                        "input_parameters": {"flow_override": "market"},
                        "name": "order_aladdin_v2_subworkflow",
                        "type": "SUB_WORKFLOW",
                    },
                },
            }
        }
    )

    task = TaskFieldSet(name="file_splitter_by_criteria", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_input_file(sample_input_skippable_file):
    sample_input_skippable_file.input_param.params["file_uri"] = (
        "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_aladdin_v2/AladdinTCA.Diogo_EQ.Transaction.20241023.csv"
    )
    return sample_input_skippable_file


@pytest.fixture()
def sample_input_file_otc_trades(sample_input_skippable_file):
    sample_input_skippable_file.input_param.params["file_uri"] = (
        "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_aladdin_v2/AladdinTCA.OTC_DERIV.Transaction.20241023.csv"
    )
    sample_input_skippable_file.workflow.trace_id = "test_otc_trades"
    return sample_input_skippable_file


@pytest.fixture()
def sample_input_file_schroders_flattening(sample_input_skippable_file):
    sample_input_skippable_file.input_param.params["file_uri"] = (
        "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_aladdin_v2/AladdinTCA.Schroders_EQ.Transaction.20241023.csv"
    )
    sample_input_skippable_file.workflow.trace_id = "schroders_flattening"
    sample_input_skippable_file.workflow.tenant = "schroders"
    return sample_input_skippable_file

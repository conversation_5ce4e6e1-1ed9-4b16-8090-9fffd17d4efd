# **MyMarket Slack Person**

The flow is triggered when a Slack user CSV file is available. 
It reads the user data, processes it in batches, and then routes the data through different transformation paths depending on whether the users already exist in the system and the type of input file provided.
The core logic is split into three main "forks":
1. **Update AccountPerson:** Matches incoming users against existing AccountPerson records and updates them.
2. **Create MarketSuggestion:** Transforms new users into MarketSuggestion records (Standard Mode).
3. **Create AccountPerson:** Transforms new users into new AccountPerson records (Account Person Mode).

### Features
- **Input:** The flow starts with a file_uri pointing to a CSV file generated by the SlackUsersPoll.
- **Mode Detection:** It inspects the filename to determine the operational mode. If the filename starts with `account_`, it activates the _"Account Person Mode"_ `(create_account_person = True)`.
Otherwise, it runs in _"Standard Mode"_.

### Data Processing Forks

For each batch of user data, the flow executes the following logic:

**Fork 1: Update Existing AccountPerson Records (Always Runs)** - 
This branch identifies users from the CSV who already exist in the system and updates their records.
- Filtering: It first filters the batch to only include rows that have both a slack_id and an email.
- Matching: It uses the user's email to find a matching AccountPerson record in ElasticSearch.
- Enrichment: If a match is found, it updates the existing AccountPerson record by:
  - Adding the slack_id to the user's communication identifiers.
  - If in "Account Person Mode", it will also update the user's title and phone number.
- Output: The updated AccountPerson records are written to an update_...ndjson file.

**Filtering for New Records** -
After the update process, the flow intelligently filters out the users that were just updated. 
The remaining users in the batch are considered "new" and are passed to one of the next two forks.

**Fork 2: Create MarketSuggestion Records (Standard Mode)** - This branch runs if the flow is not in _"Account Person Mode"_ `(create_account_person = False)`.
- Transformation: The new users are processed through the `mymarket_slack_market_suggestion` primary transformation mapping.
- Output: The resulting MarketSuggestion records are written to a create_...ndjson file, ready for ingestion.

**Fork 3: Create AccountPerson Records (Account Person Mode)** - This branch runs if the flow is in _"Account Person Mode"_ `(create_account_person = True)`.
- Transformation: The new users are processed through the `mymarket_slack_account_person` primary transformation mapping.
- Output: The resulting new AccountPerson records are written to a create_...ndjson file, ready for ingestion.



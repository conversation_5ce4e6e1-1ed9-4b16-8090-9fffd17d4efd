from integration_wrapper.integration_aries_task_input import IntegrationAriesTaskInput
from pydantic import BaseModel, Field


class AnalyticsControllerAriesTaskInput(IntegrationAriesTaskInput):
    """AnalyticsController does not require any extra parameters, compared to a
    generic Integration Task."""


class AnalyticsControllerTenantStaticConfig(BaseModel, extra="ignore"):
    enable_language_identification_voice: bool | None = Field(
        default=None,
        description="If True, enables the Language identification voice task for all workflows"
        "for the given tenant",
    )


class AnalyticsControllerTenantWorkflowStaticConfig(BaseModel, extra="ignore"):
    enable_language_identification_voice: bool | None = Field(
        default=None,
        description="If True, enables the Language identification voice task for a given"
        "tenant workflow. This takes preference over the tenant-level flag."
        "So, if the tenant level flag is True, and the tenant workflow level"
        "flag is False, language identification voice will not be enabled"
        "for the tenant-workflow",
    )

import logging
from aries_se_core_tasks.aries.utility_tasks.finish_flow import finish_flow
from aries_se_core_tasks.io.read.fetch_tenant_configuration import run_fetch_tenant_configuration
from aries_task_link.models import AriesTaskInput
from workflow_controllers.check_language_detection import check_language_detection
from workflow_controllers.check_lexica_endpoint import is_lexica_enabled_for_tenant

logger = logging.getLogger(__name__)


def analytics_controller_flow(aries_task_input: AriesTaskInput, result_path: str = "result.json"):
    tenant = aries_task_input.workflow.tenant
    stack = aries_task_input.workflow.stack

    file_uri = aries_task_input.input_param.params.get("file_uri", "")

    # Determine if the tenant has lexica pre-processing enabled
    is_lexica_enabled = is_lexica_enabled_for_tenant(tenant, stack, file_uri)

    tenant_configuration = run_fetch_tenant_configuration(
        tenant=tenant,
    )

    # Determine if the tenant has language detection enabled
    check_language_detection_result = check_language_detection(
        tenant_configuration=tenant_configuration, tenant=tenant
    )

    # Propagate IOParams to downstream Conductor Task
    finish_flow(
        result_path=result_path,
        is_lexica_enabled=is_lexica_enabled,
        is_language_detection_enabled=check_language_detection_result,
        transcription_provider=tenant_configuration.get("voice", {}).get("transcriptionProvider"),
        transcription_limit=tenant_configuration.get("contractualLimits", {})
        .get("volume", {})
        .get("voice", {})
        .get("transcription"),
        tenant_config_feature_flags=tenant_configuration.get("featureFlags", []),
    )

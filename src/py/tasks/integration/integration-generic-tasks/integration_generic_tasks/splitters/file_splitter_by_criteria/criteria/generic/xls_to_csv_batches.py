from addict import addict
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.read.csv_file_splitter import Params as ParamsCsvFileSplitter
from aries_se_core_tasks.io.read.csv_file_splitter import run_csv_file_splitter
from aries_se_core_tasks.io.read.xls_to_csv_converter import Params as ParamsxlsToCsvConverter
from aries_se_core_tasks.io.read.xls_to_csv_converter import run_xls_to_csv_converter
from aries_se_core_tasks.io.write.upload_file import (  # type: ignore[attr-defined]
    Params as UploadFileParams,
)
from aries_se_core_tasks.io.write.upload_file import run_upload_file
from aries_se_core_tasks.transform.splitter_path.cloud_file_list_from_frame_splitter_result_list import (  # type: ignore[attr-defined] # noqa: E501
    Params as ParamsCloudFileListFileSplitterResultList,
)
from aries_se_core_tasks.transform.splitter_path.cloud_file_list_from_frame_splitter_result_list import (  # noqa: E501
    run_cloud_file_list_from_file_splitter_result_list,
)
from aries_task_link.models import AriesTaskInput
from integration_generic_tasks.splitters.file_splitter_by_criteria.input_schema import (
    FileSplitterByCriteriaAriesTaskInput,
)
from pathlib import Path
from se_conductor_utils.task_output import DynamicTask, create_dynamic_tasks_list
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_data_lake.lake_path import get_ingest_lake_dir_for_task
from se_enums.cloud import CloudProviderEnum
from se_io_utils.tempfile_utils import tmp_directory
from typing import Any, Dict


def xls_to_csv_batches_criteria(
    aries_task_input: AriesTaskInput,
    file_splitter_by_criteria_aries_task_input: FileSplitterByCriteriaAriesTaskInput,
    workflow_config: addict.Dict,
    **kwargs,
) -> Dict[str, Any]:
    tmp_dir: Path = tmp_directory()

    # Get cloud provider
    cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
        file_uri=workflow_config.tenant.lake_prefix
    )
    cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

    # Get realm from input file path
    realm: str = get_bucket(file_uri=file_splitter_by_criteria_aries_task_input.file_uri)

    # Determine the Cloud Bucket of the tenant
    tenant_bucket_with_cloud_prefix = get_tenant_bucket(
        task_input=file_splitter_by_criteria_aries_task_input,
        cloud_provider_prefix=cloud_provider_prefix,
    )

    # Dynamic Task Input
    dynamic_task_input: DynamicTask = file_splitter_by_criteria_aries_task_input.dynamic_tasks[
        "dynamic_task"
    ]

    # download source file
    local_file = run_download_file(file_url=file_splitter_by_criteria_aries_task_input.file_uri)

    converted_csv = run_xls_to_csv_converter(
        file_path=local_file,
        params=ParamsxlsToCsvConverter(
            interpret_as_str=True,
        ),
    )

    # Read downloaded file and split it into chunks
    csv_chunks_paths = run_csv_file_splitter(
        params=ParamsCsvFileSplitter(
            chunksize=workflow_config.max_batch_size,
            audit_input_rows=True,
            include_file_name_in_batches=True,
        ),
        csv_path=converted_csv.as_posix(),
        realm=realm,
        sources_dir=tmp_dir.as_posix(),
        streamed=workflow_config.streamed,
    )

    # Create the appropriate path where the batched files are to be uploaded
    split_file_upload_path_prefix = get_ingest_lake_dir_for_task(
        workflow_name=aries_task_input.workflow.name,
        task_name="file_splitter_by_criteria",
        workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
        workflow_trace_id=aries_task_input.workflow.trace_id,
    )

    # Create cloud upload config for the file batches
    cloud_upload_config = run_cloud_file_list_from_file_splitter_result_list(
        params=ParamsCloudFileListFileSplitterResultList(
            cloud_key_prefix=split_file_upload_path_prefix,
            bucket_name=realm,
            datetime_field_in_file_path=None,
        ),
        file_splitter_result_list=csv_chunks_paths,
    )

    # Upload the file batches to cloud
    cloud_file_targets = run_upload_file(
        upload_target=cloud_upload_config, cloud_provider=cloud_provider, params=UploadFileParams()
    )

    # Create the file URIs
    file_uri_list = [
        f"{tenant_bucket_with_cloud_prefix}/{data.key_name}" for data in cloud_file_targets.targets
    ]

    dynamic_tasks_output = create_dynamic_tasks_list(
        lst=[{"file_uri": ndjson_path} for ndjson_path in file_uri_list],
        task=dynamic_task_input,
        workflow=aries_task_input.workflow,
        common_input_parameters={
            "source_file_uri": file_splitter_by_criteria_aries_task_input.file_uri,
        },
    )

    return dynamic_tasks_output

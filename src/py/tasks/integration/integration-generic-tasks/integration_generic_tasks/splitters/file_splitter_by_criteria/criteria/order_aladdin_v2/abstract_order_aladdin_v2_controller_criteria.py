import abc
from abc import ABC
from typing import Dict


class AbstractOrderAladdinV2ControllerCriteria(ABC):
    @staticmethod
    @abc.abstractmethod
    def _get_portfolio_map() -> Dict[str, str]:
        """
        The mapping from input Aladdin Portfolio file to
        the output SteelEye MyMarket Firm file can vary between tenants.
        """
        raise NotImplementedError

    @property
    @abc.abstractmethod
    def _should_flatten_market_side_executions(self) -> bool:
        """
        See https://steeleye.atlassian.net/browse/ON-4798 for more details.
        This property determines whether the market side executions
        should be flattened in the output file as a single FILL.
        This is disabled by default, but can be enabled for specific tenant
        overrides.

        :return: True if the market side executions should be flattened, False otherwise.
        """
        raise NotImplementedError

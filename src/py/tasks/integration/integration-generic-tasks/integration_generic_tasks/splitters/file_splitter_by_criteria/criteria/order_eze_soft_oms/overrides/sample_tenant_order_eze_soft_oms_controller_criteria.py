from addict import addict
from aries_task_link.models import AriesTaskInput
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_eze_soft_oms.order_eze_soft_oms_controller_criteria import (
    DefaultOrderEzeSoftOMSControllerCriteria,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.input_schema import (
    FileSplitterByCriteriaAriesTaskInput,
)
from typing import Any, Dict


class SampleTenantOrderEzeSoftOMSControllerCriteria(DefaultOrderEzeSoftOMSControllerCriteria):
    """Sample tenant-specific override for EZE Soft OMS controller criteria.

    This class demonstrates how to customize the behavior for specific tenants.
    """

    @property
    def _should_process_executions_separately(self) -> bool:
        # This tenant requires executions to be processed separately
        return True

    @property
    def _batch_size_override(self) -> int | None:
        # This tenant requires smaller batch sizes
        return 1000


def sample_tenant_order_eze_soft_oms_controller_criteria(
    aries_task_input: AriesTaskInput,
    file_splitter_by_criteria_aries_task_input: FileSplitterByCriteriaAriesTaskInput,
    workflow_config: addict.Dict,
    audit_path: str,
    **kwargs,
) -> Dict[str, Any]:
    """Entry point for sample tenant-specific EZE Soft OMS controller criteria."""
    class_instance = SampleTenantOrderEzeSoftOMSControllerCriteria()
    return class_instance.criteria_driver(
        aries_task_input=aries_task_input,
        file_splitter_by_criteria_aries_task_input=file_splitter_by_criteria_aries_task_input,
        workflow_config=workflow_config,
        audit_path=audit_path,
    )

import logging
from addict import addict
from aries_task_link.models import AriesTaskInput
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_aladdin_v2.order_aladdin_v2_controller_criteria import (  # noqa: E501
    DefaultOrderAladdinV2ControllerCriteria,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_aladdin_v2.order_aladdin_v2_criteria_static import (  # noqa: E501
    PortfolioSourceColumns,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.input_schema import (
    FileSplitterByCriteriaAriesTaskInput,
)
from typing import Any, Dict

logger_ = logging.getLogger(__name__)


class SchrodersOrderAladdinV2ControllerCriteria(DefaultOrderAladdinV2ControllerCriteria):
    @property
    def _should_flatten_market_side_executions(self) -> bool:
        return True

    @staticmethod
    def _get_portfolio_map() -> Dict[str, str]:
        # The requirement from Schroders is that the Name and FirmDetailsIndustry
        # mappings are swapped compared to the default mapping.
        portfolio_map = {
            PortfolioSourceColumns.PORTFOLIO_TICKER: "Name",
            PortfolioSourceColumns.PORTFOLIO_NAME: "FirmDetailsIndustry",
            PortfolioSourceColumns.PRIMARY_PM_ID: "DecisionMakerId",
            PortfolioSourceColumns.PORTFOLIO_ID: "TradeFileIdentifiers",
        }
        return portfolio_map


def schroders_order_aladdin_v2_controller_criteria(
    aries_task_input: AriesTaskInput,
    file_splitter_by_criteria_aries_task_input: FileSplitterByCriteriaAriesTaskInput,
    workflow_config: addict.Dict,
    audit_path: str,
    **kwargs,
) -> Dict[str, Any]:
    class_instance = SchrodersOrderAladdinV2ControllerCriteria()
    return class_instance.criteria_driver(
        aries_task_input=aries_task_input,
        file_splitter_by_criteria_aries_task_input=file_splitter_by_criteria_aries_task_input,
        workflow_config=workflow_config,
        audit_path=audit_path,
    )

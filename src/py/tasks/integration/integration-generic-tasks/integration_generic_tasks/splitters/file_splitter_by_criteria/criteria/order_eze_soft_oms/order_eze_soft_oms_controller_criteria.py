# ruff: noqa: E501
import pandas as pd
from addict import addict
from pathlib import Path
from typing import Any, Dict

from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.controllers.multiple_files_input_controller import (
    Params as MultipleFilesInputControllerParams,
)
from aries_se_core_tasks.controllers.multiple_files_input_controller import (
    run_multiple_files_input_controller,
)
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.write.upload_file import (
    Params as UploadFileParams,
)
from aries_se_core_tasks.io.write.upload_file import run_upload_file
from aries_se_core_tasks.utilities.static import Delimiters
from aries_task_link.models import AriesTaskInput
from integration_audit.auditor import upsert_audit
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_eze_soft_oms.order_eze_soft_oms_criteria_static import (
    UNIQUE_IDENTIFIER_REGEX,
    ExecutionsSourceColumns,
    AllocationsSourceColumns,
    OrdersSourceColumns,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.input_schema import (
    FileSplitterByCriteriaAriesTaskInput,
)
from se_conductor_utils.task_output import DynamicTask, create_dynamic_tasks_list
from se_core_tasks.controllers.multiple_files_input_controller import (
    SkipIfMissingFiles,
    SkipIfSourceTimestampLessThanPair,
    SkipIfSourceTimestampSameAlphaLess,
)
from se_core_tasks.core.exception import TaskException
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_data_lake.lake_path import get_ingest_lake_dir_for_task
from se_enums.cloud import CloudProviderEnum
from se_io_utils.tempfile_utils import tmp_directory


class SkipInvalidFile(TaskException):
    pass


def order_eze_soft_oms_controller_criteria(
    aries_task_input: AriesTaskInput,
    file_splitter_by_criteria_aries_task_input: FileSplitterByCriteriaAriesTaskInput,
    workflow_config: addict.Dict,
    app_metrics_path: str,
    audit_path: str,
    **kwargs,
) -> Dict[str, Any]:
    # Get cloud provider
    cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
        file_uri=workflow_config.tenant.lake_prefix
    )
    cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

    # Get realm from input file path
    realm: str = get_bucket(file_uri=file_splitter_by_criteria_aries_task_input.file_uri)

    # Determine the Cloud Bucket of the tenant
    tenant_bucket_with_cloud_prefix = get_tenant_bucket(
        task_input=file_splitter_by_criteria_aries_task_input,
        cloud_provider_prefix=cloud_provider_prefix,
    )

    # Streamed
    streamed = workflow_config.workflow.streamed

    # Dynamic Task Input
    dynamic_task_input: DynamicTask = file_splitter_by_criteria_aries_task_input.dynamic_tasks[
        "dynamic_task"
    ]

    file_url: str = file_splitter_by_criteria_aries_task_input.file_uri

    # Create temporary directory
    temp_dir: Path = tmp_directory()

    # Check if all files are present
    try:
        multiple_files_input_controller_result = run_multiple_files_input_controller(
            file_url=file_url,
            realm=realm,
            params=MultipleFilesInputControllerParams(
                list_of_files_regex=[
                    ".*(Executions).*",
                    ".*(Allocations).*",
                    ".*(Orders).*",
                ],
                unique_identifier_regex=[
                    UNIQUE_IDENTIFIER_REGEX,
                ],
                file_links_in_output=True,
                prefix_for_file_links_in_output="cloud_",
            ),
            cloud_provider=cloud_provider,
            skip_serializer=True,
        )
    except (
        SkipIfSourceTimestampLessThanPair,
        SkipIfSourceTimestampSameAlphaLess,
        SkipIfMissingFiles,
    ) as skip_exception:
        upsert_audit(
            audit_path=audit_path,
            streamed=streamed,
            workflow_status=[skip_exception.message],
        )

        dynamic_tasks_output = create_dynamic_tasks_list(
            lst=[],
            task=dynamic_task_input,
            workflow=aries_task_input.workflow,
            common_input_parameters={},
        )

        return dynamic_tasks_output

    if multiple_files_input_controller_result.empty:
        raise SkipInvalidFile(
            f"The input file: {Path(file_url).name} does not have to be processed."
        )

    # Download the files
    executions_file_url = str(multiple_files_input_controller_result.loc[0, "executions_file_url"])
    allocations_file_url = str(multiple_files_input_controller_result.loc[0, "allocations_file_url"])
    orders_file_url = str(multiple_files_input_controller_result.loc[0, "orders_file_url"])

    executions_local_path = run_download_file(file_url=executions_file_url)
    allocations_local_path = run_download_file(file_url=allocations_file_url)
    orders_local_path = run_download_file(file_url=orders_file_url)

    # Read the CSV files
    executions_df = pd.read_csv(executions_local_path, delimiter=Delimiters.COMMA)
    allocations_df = pd.read_csv(allocations_local_path, delimiter=Delimiters.COMMA)
    orders_df = pd.read_csv(orders_local_path, delimiter=Delimiters.COMMA)

    # Add prefixes to columns
    executions_df.columns = pd.Index([f"exec_{col}" for col in executions_df.columns])
    allocations_df.columns = pd.Index([f"alloc_{col}" for col in allocations_df.columns])
    orders_df.columns = pd.Index([f"order_{col}" for col in orders_df.columns])

    # Merge dataframes
    # First merge executions with allocations
    merged_df = executions_df.merge(
        allocations_df,
        left_on=f"exec_{ExecutionsSourceColumns.EXECUTION_ID.field}",
        right_on=f"alloc_{AllocationsSourceColumns.EXECUTION_ID.field}",
        how="left"
    )

    # Then merge with orders
    merged_df = merged_df.merge(
        orders_df,
        left_on=f"exec_{ExecutionsSourceColumns.ORDER_ID.field}",
        right_on=f"order_{OrdersSourceColumns.ORDER_ID.field}",
        how="left"
    )

    # Save merged dataframe to a temporary file
    output_file_path = temp_dir.joinpath("merged_output.csv")
    merged_df.to_csv(output_file_path, index=False)

    # Upload the merged file
    upload_key = get_ingest_lake_dir_for_task(
        workflow_name=aries_task_input.workflow.name,
        task_name="file_splitter_by_criteria",
        workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
        workflow_trace_id=aries_task_input.workflow.trace_id,
    )

    cloud_file_target = run_upload_file(
        upload_target={
            "targets": [{
                "source_path": str(output_file_path),
                "key_name": f"{upload_key}/merged_output.csv"
            }]
        },
        cloud_provider=cloud_provider,
        params=UploadFileParams()
    )

    # Create the file URI
    merged_file_uri = f"{tenant_bucket_with_cloud_prefix}/{cloud_file_target.targets[0].key_name}"

    # Create dynamic tasks
    dynamic_tasks_output = create_dynamic_tasks_list(
        lst=[
            {
                "file_uri": merged_file_uri,
                "source_file_uri": executions_file_url
            }
        ],
        task=dynamic_task_input,
        workflow=aries_task_input.workflow,
        common_input_parameters={},
    )

    return dynamic_tasks_output
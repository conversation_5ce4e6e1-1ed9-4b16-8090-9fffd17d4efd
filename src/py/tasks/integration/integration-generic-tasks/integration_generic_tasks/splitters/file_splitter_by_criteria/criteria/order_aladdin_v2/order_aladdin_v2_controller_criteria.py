# ruff: noqa: E501
import fsspec
import json
import logging
import pandas as pd
import re
from addict import addict
from aries_se_core_tasks.controllers.multiple_files_input_controller import (  # type: ignore[attr-defined]
    Params as MultipleFilesInputControllerParams,
)
from aries_se_core_tasks.controllers.multiple_files_input_controller import (  # type: ignore[attr-defined]
    run_multiple_files_input_controller,
)
from aries_se_core_tasks.core.exception import TaskException
from aries_se_core_tasks.frame.merge_and_split_dataframes import (  # type: ignore[attr-defined]
    Params as MergeAndSplitDataFramesParams,
)
from aries_se_core_tasks.frame.merge_and_split_dataframes import (  # type: ignore[attr-defined]
    run_merge_and_split_dataframes,
)
from aries_se_core_tasks.io.read.cloud.download_file import (
    run_download_file,
)
from aries_se_core_tasks.io.read.csv_file_splitter import run_csv_file_splitter
from aries_se_core_tasks.io.read.csv_file_splitter_by_column_values import (
    run_csv_file_splitter_by_column_values,
)
from aries_se_core_tasks.utilities.frame_manipulation import add_missing_columns
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.static import (
    DATE_FORMAT,
    DevColumns,
    FileTypes,
    FillSourceColumns,
    OrderDetailSourceColumns,
    OrderSourceColumns,
    PlacementSourceColumns,
    TransactionSourceColumns,
)
from aries_task_link.models import AriesTaskInput
from collections import defaultdict
from datetime import datetime
from integration_audit.auditor import AuditorStaticFields, upsert_audit
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_aladdin_v2.abstract_order_aladdin_v2_controller_criteria import (
    AbstractOrderAladdinV2ControllerCriteria,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_aladdin_v2.order_aladdin_v2_criteria_exceptions import (
    SkipIfFileIsNotPartOfTheWorkflow,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_aladdin_v2.order_aladdin_v2_criteria_static import (
    UNIQUE_IDENTIFIER_REGEX,
    AladdinV2ControllerOutput,
    AladdinV2MarketTypeEnum,
    BrokerSourceColumns,
    PortfolioSourceColumns,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.input_schema import (
    FileSplitterByCriteriaAriesTaskInput,
)
from mymarket_tasks.static import MY_MARKET_FIRM_FEED_CLOUD_PATH
from pathlib import Path
from se_conductor_utils.task_output import DynamicTask, create_dynamic_tasks_list
from se_core_tasks.controllers.multiple_files_input_controller import (
    SkipIfMissingFiles,
    SkipIfSourceTimestampLessThanPair,
    SkipIfSourceTimestampSameAlphaLess,
)
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_core_tasks.core.exception import TaskException as CoreTaskException
from se_core_tasks.io.read.csv_file_splitter import (
    Params as ParamsCsvFileSplitter,
)
from se_core_tasks.io.read.csv_file_splitter import PreProcessFunc
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_data_lake.lake_path import (
    get_ingest_lake_dir_for_task,
    get_prefixed_ingest_lake_path_for_task,
)
from se_elastic_schema.models import Order
from se_elastic_schema.static.reference import FirmType
from se_enums.cloud import CloudProviderEnum
from se_io_utils.tempfile_utils import tmp_directory
from se_trades_tasks.order.static import add_prefix
from typing import Any, Dict, List, Tuple

logger_ = logging.getLogger(__name__)


class DefaultOrderAladdinV2ControllerCriteria(AbstractOrderAladdinV2ControllerCriteria):
    @property
    def _should_flatten_market_side_executions(self) -> bool:
        return False

    def criteria_driver(
        self,
        aries_task_input: AriesTaskInput,
        file_splitter_by_criteria_aries_task_input: FileSplitterByCriteriaAriesTaskInput,
        workflow_config: addict.Dict,
        audit_path: str,
        **kwargs,
    ) -> Dict[str, Any]:
        """See
        https://steeleye.atlassian.net/wiki/spaces/IN/pages/**********/Order+Aladdin+V2 for more details.

        Order Aladdin V2 is a non-streamed evented Workflow that gets triggered by a series of end-of-day files.

        The clients send multiple files to SteelEye. Below are the crucial files that
        the client must send in order for the workflow to succeed:

        - "OrderDetail"
        - "Order
        - "Transaction"
        - "Placement"
        - "Fill"

        We also expect the client to send the following files, but these are not actually processed,
        and they are essentially skipped:
        - "BETA
        - "Employee"
        - "Broker"
        - "PlacementAlgo"
        - "Portfolio"
        - "Quote"
        - "SMFPriceMultiplier"

        Furthermore, for each file type, the client will send multiple files, each for each Asset Class
        that they trade. This is easy to identify based on the filename i.e.
        EQ_ means that the file contains equities, and FI_ means that the file contains fixed income trades.

        The client will send these multiple file types and multiple asset classes every day, or close to it.
        The date is included in the filename. This means that for any given input file, it is deterministic
        to find the "associated" files, for that given day and given asset class.

        Because this is an evented workflow, any of these files will trigger the order_aladdin_v2 workflow.
        The FileSplitterByCriteria task will run this module and determine what to do based on the following
        requirements:
        - The unnecessary files from the second list will be skipped.
        - If any of the crucial files from the first list are missing, the workflow will be skipped.
        - If all crucial files from the first list are present, the workflow will proceed.
        - If this Task runs for multiple files, at a moment in time where all the crucial files are present,
        only one of the files will allow the workflow to proceed. This is intentional, to prevent
        running the workflow multiple times for the same set of files.

        If all requirements are met, this Task will be responsible for building a
        list of reasonably sized batches of Client-side orders and Market-side orders.

        To assemble the client-side orders we use the OrderDetail file as the starting point, and
        then we link it to all files, apart from the "Fill" file, to enrich the data.

        To assemble the market-side orders we use the Fill file as the starting point, and
        then we link it to all files, to enrich the data.

        The output of this task is a dictionary with two keys, where each key contains a list of DynamicTask objects
        to feed two Dynamic Fork Joins downstream in the Conductor workflow.

        :param aries_task_input: Task input AriesTaskInput object propagated through Conductor
        :param file_splitter_by_criteria_aries_task_input: The params of `aries_task_input` converted to a standardized Pydantic object
        :param workflow_config: addict.Dict containing the order_aladdin_v2 workflow configuration fetched from the config DB.
        :param audit_path: The path to the JSON audit file, whose contents may be updated throughout this module.

        :return: A dictionary with exactly two keys: 'client_side_orders' and 'market_side_orders'. Each key
        contains a list of DynamicTask objects that will be executed downstream in the workflow.
        If no batches of either client_side_orders or market_side_orders are found (or both),
        the resulting lists will be empty and the workflow will essentially skip the downstream tasks.
        """

        cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
            file_uri=workflow_config.tenant.lake_prefix
        )
        realm: str = get_bucket(file_uri=file_splitter_by_criteria_aries_task_input.file_uri)
        streamed = workflow_config.workflow.streamed

        # Create temporary directory
        temp_dir: Path = tmp_directory()

        try:
            client_side_order_paths, market_side_order_paths, target_cache_path = (
                self.extract_client_and_market_side_batches(
                    cloud_provider=cloud_provider,
                    file_url=file_splitter_by_criteria_aries_task_input.file_uri,
                    realm=realm,
                    batch_size=workflow_config.max_batch_size,
                    temp_dir=temp_dir,
                    streamed=streamed,
                    audit_path=audit_path,
                    aries_task_input=aries_task_input,
                )
            )

        except (
            # to prevent the workflow from running multiple times for the same set of files
            SkipIfSourceTimestampLessThanPair,
            SkipIfSourceTimestampSameAlphaLess,
            SkipIfMissingFiles,  # the workflow cannot yet start
        ) as skip_exception:
            logger_.warning(f"Skipping the workflow: {skip_exception.message}")

            # If any of the above exceptions are raised, the workflow cannot proceed;
            # thus, we return a result with empty lists, to force the
            # downstream Dynamic Fork Joins to do nothing.
            # We also update the audit file explaining why the workflow was skipped.
            return self.create_empty_output(
                aries_task_input=aries_task_input,
                audit_path=audit_path,
                file_splitter_by_criteria_aries_task_input=file_splitter_by_criteria_aries_task_input,
                skip_exception=skip_exception,
                streamed=streamed,
            )

        except (
            SkipIfFileIsNotPartOfTheWorkflow,  # the input file is irrelevant for the workflow
        ) as skip_exception:
            logger_.warning(f"Skipping the workflow: {skip_exception.message}")

            market_type = None
            input_filename = file_splitter_by_criteria_aries_task_input.file_uri.split("/")[-1]

            # See https://steeleye.atlassian.net/browse/ON-4290 for more details.
            # If the input file is either the Broker or the Portfolio file,
            # we must NOT continue the Aladdin V2 workflow, but we must
            # relay this data to the MyMarket Firm feed.
            if ".Broker." in input_filename:
                market_type = AladdinV2MarketTypeEnum.BROKER
            elif ".Portfolio." in input_filename:
                market_type = AladdinV2MarketTypeEnum.PORTFOLIO

            if market_type:
                self.convert_aladdin_market_file_to_steeleye_market_file(
                    market_type=market_type,
                    file_uri=file_splitter_by_criteria_aries_task_input.file_uri,
                    realm=realm,
                    cloud_provider=cloud_provider,
                    workflow_name=aries_task_input.workflow.name,
                )

            return self.create_empty_output(
                aries_task_input=aries_task_input,
                audit_path=audit_path,
                file_splitter_by_criteria_aries_task_input=file_splitter_by_criteria_aries_task_input,
                skip_exception=skip_exception,
                streamed=streamed,
            )

        client_side_orders_output_dynamic_task_input: DynamicTask = (
            file_splitter_by_criteria_aries_task_input.dynamic_tasks[
                AladdinV2ControllerOutput.CLIENT_SIDE_ORDERS
            ]
        )
        market_side_orders_output_dynamic_task_input: DynamicTask = (
            file_splitter_by_criteria_aries_task_input.dynamic_tasks[
                AladdinV2ControllerOutput.MARKET_SIDE_ORDERS
            ]
        )

        client_side_orders_output = create_dynamic_tasks_list(
            lst=[{"file_uri": ndjson_path} for ndjson_path in client_side_order_paths],
            task=client_side_orders_output_dynamic_task_input,
            common_input_parameters={
                "source_file_uri": file_splitter_by_criteria_aries_task_input.file_uri,
                "order_id_cache_path": target_cache_path,
            },
            workflow=aries_task_input.workflow,
        )

        market_side_orders_output = create_dynamic_tasks_list(
            lst=[{"file_uri": ndjson_path} for ndjson_path in market_side_order_paths],
            task=market_side_orders_output_dynamic_task_input,
            common_input_parameters={
                "source_file_uri": file_splitter_by_criteria_aries_task_input.file_uri,
                "order_id_cache_path": target_cache_path,
            },
            workflow=aries_task_input.workflow,
        )

        return {
            AladdinV2ControllerOutput.CLIENT_SIDE_ORDERS: client_side_orders_output,
            AladdinV2ControllerOutput.MARKET_SIDE_ORDERS: market_side_orders_output,
        }

    @staticmethod
    def create_empty_output(
        aries_task_input: AriesTaskInput,
        audit_path: str,
        file_splitter_by_criteria_aries_task_input: FileSplitterByCriteriaAriesTaskInput,
        skip_exception: TaskException | CoreTaskException,
        streamed: bool,
    ) -> Dict[str, Any]:
        """Creates an empty output structure and updates the audit message.

        :param aries_task_input: Input object encapsulating workflow-related data necessary
            for creating dynamic tasks, including common input parameters.
        :param audit_path: The location of the task audit file.
        :param file_splitter_by_criteria_aries_task_input: An object containing the dynamic
            tasks and related parameters for segregating tasks based on specific criteria.
        :param skip_exception: Exception that was raised by MultipleFilesInputController.
        :param streamed: Indicator for whether the workflow is streamed or non-streamed.

        :return: A dictionary containing empty task lists for client-side orders and
            market-side orders, to "skip" the downstream tasks in the workflow.
        """
        exception_message: str = skip_exception.message
        upsert_audit(
            audit_path=audit_path,
            streamed=streamed,
            workflow_status=[exception_message],
        )

        client_side_orders_output = create_dynamic_tasks_list(
            lst=[],
            task=file_splitter_by_criteria_aries_task_input.dynamic_tasks[
                AladdinV2ControllerOutput.CLIENT_SIDE_ORDERS
            ],
            workflow=aries_task_input.workflow,
            common_input_parameters={},
        )
        market_side_orders_output = create_dynamic_tasks_list(
            lst=[],
            task=file_splitter_by_criteria_aries_task_input.dynamic_tasks[
                AladdinV2ControllerOutput.MARKET_SIDE_ORDERS
            ],
            workflow=aries_task_input.workflow,
            common_input_parameters={},
        )

        return {
            AladdinV2ControllerOutput.CLIENT_SIDE_ORDERS: client_side_orders_output,
            AladdinV2ControllerOutput.MARKET_SIDE_ORDERS: market_side_orders_output,
        }

    def extract_client_and_market_side_batches(
        self,
        file_url: str,
        realm: str,
        batch_size: int,
        temp_dir: Path,
        streamed: bool,
        cloud_provider: CloudProviderEnum,
        audit_path: str,
        aries_task_input: AriesTaskInput,
    ) -> Tuple[List[str], List[str], str]:
        """This task is responsible for evaluating if the workflow meets all
        requirements and can proceed. If it can, it will iterate over the input
        files and assemble a list of batches of Client-side orders and Market-side
        orders.

        :param file_url: Absolute file path of the file that triggered the workflow
        :param realm: Tenant bucket without the cloud provider prefix
        :param batch_size: Max batch size as defined in the config DB
        :param temp_dir: Temporary directory where the batches are stored in disk
        :param streamed: True or false, as defined in the config DB.
        :param cloud_provider: Cloud provider as defined in the config DB.
        :param audit_path: Absolute path of the JSON audit file
        :param aries_task_input: Task input AriesTaskInput object propagated through Conductor

        :return: A tuple with exactly three elements:
        - A list of cloud URIs for the client-side orders to be processed downstream
        - A list of cloud URIs for the market-side orders to be processed downstream
        - The cloud URI of the cache file that must be propagated downstream
        """

        logger_.info(f"Extracting client and market side batches for file: {file_url}")

        client_side_order_output_paths: List[str] = []
        market_side_order_output_paths: List[str] = []
        cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

        output_file_name = re.sub(
            r"\.(Transaction|Fill|Placement|Order|OrderDetail)\.",
            ".",
            Path(file_url).name,
        ).replace(".csv", "")

        # Create the appropriate path where the files are to be uploaded
        output_lake_path = Path(
            get_ingest_lake_dir_for_task(
                workflow_name=aries_task_input.workflow.name,
                task_name="file_splitter_by_criteria",
                workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
                workflow_trace_id=aries_task_input.workflow.trace_id,
            )
        )

        multiple_files_input_controller_df: pd.DataFrame = self.collect_required_file_uris(
            cloud_provider, file_url, realm
        )

        logger_.info("Input file requirements were met. Starting to process the input files.")

        # Per file type, build a list of reasonably sized batches
        # that were already pre-processed, thus contain exactly
        # the expected columns, no duplicate records etc...
        batches_by_input_file_type: Dict[str, List[FileSplitterResult]] = self.batch_input_files(
            multiple_files_input_controller_result=multiple_files_input_controller_df,
            batch_size=batch_size,
            realm=realm,
            streamed=streamed,
            audit_path=audit_path,
            temp_dir=temp_dir,
        )

        # This is a cache that will be populated as we iterate
        # through the batches of each input file, and that will be propagated
        # downstream to the transform tasks.
        def order_id_cache_factory() -> Dict[str, str | None]:
            return {
                "instrument_full_name": None,
                "trader": None,
                "exchange": None,
            }

        order_id_cache: defaultdict[str, Dict[str, str | None]] = defaultdict(
            order_id_cache_factory
        )

        # Iterate over the input raw batches, assemble batches of client-side orders
        # and upload them to the cloud. Some of the intermediate dataframes are also
        # returned to be re-utilized downstream. The order_id_cache is updated
        # as we iterate through the relevant input files.
        order_df, placement_df, transaction_df = self.assemble_and_upload_client_side_orders(
            batch_size=batch_size,
            batches_by_input_file_type=batches_by_input_file_type,
            client_side_order_output_paths=client_side_order_output_paths,
            cloud_provider_prefix=cloud_provider_prefix,
            order_id_cache=order_id_cache,
            output_file_name=output_file_name,
            output_lake_path=output_lake_path,
            realm=realm,
            audit_path=audit_path,
            streamed=streamed,
        )

        # Iterate over the input raw batches, assemble batches of market-side orders
        # and upload them to the cloud.
        self.assemble_and_upload_market_side_orders(
            batch_size=batch_size,
            batches_by_input_file_type=batches_by_input_file_type,
            cloud_provider_prefix=cloud_provider_prefix,
            market_side_order_output_paths=market_side_order_output_paths,
            order_id_cache=order_id_cache,
            order_df=order_df,
            output_file_name=output_file_name,
            output_lake_path=output_lake_path,
            placement_df=placement_df,
            realm=realm,
            transaction_df=transaction_df,
        )

        logger_.info(f"Starting to upload the order id cache. Number of IDs: {len(order_id_cache)}")
        target_cache_path = (
            cloud_provider_prefix
            + realm
            + "/"
            + get_prefixed_ingest_lake_path_for_task(
                workflow_name=aries_task_input.workflow.name,
                workflow_trace_id=aries_task_input.workflow.trace_id,
                task_name=aries_task_input.task.name,
                task_io_params=aries_task_input.input_param.params,
                workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
            )
            + "order_id_cache.json"
        )

        logger_.info(f"Uploading cache to {target_cache_path}")

        with fsspec.open(target_cache_path, "w") as f:
            f.write(json.dumps(order_id_cache))

        logger_.info("Cache uploaded successfully.")

        return client_side_order_output_paths, market_side_order_output_paths, target_cache_path

    def assemble_and_upload_market_side_orders(
        self,
        batch_size: int,
        batches_by_input_file_type: Dict[str, List[FileSplitterResult]],
        cloud_provider_prefix: str,
        market_side_order_output_paths: List[str],
        order_id_cache: Dict[str, Dict[str, str | None]],
        order_df: pd.DataFrame,
        output_file_name: str,
        output_lake_path: Path,
        placement_df: pd.DataFrame,
        realm: str,
        transaction_df: pd.DataFrame,
    ) -> None:
        """This function is responsible for assembling the market-side orders and
        uploading them to the cloud. We achieve this by reading the Fill file in
        chunks, and by merging it against the Placement, Transaction, OrderDetail
        and Order files. The resulting dataframes are then uploaded to the cloud as
        CSV files to be processed downstream.

        :param batch_size: Max batch size of each output CSV file as defined in the config DB.
        :param batches_by_input_file_type:  Dictionary containing the batches of each input file type.
        :param cloud_provider_prefix: Cloud provider prefix
        :param market_side_order_output_paths: List where we will store the cloud URIs of the uploaded market-side orders.
        :param order_id_cache: Cache containing relevant data points by Order ID.
        :param order_df: DataFrame with the Order file's contents
        :param output_file_name: Base name of each output file, to whom we will append the batch index.
        :param output_lake_path: Where each output file will be written to.
        :param placement_df: DataFrame with the Placement file's contents
        :param realm: Tenant bucket without the cloud provider prefix.
        :param transaction_df: DataFrame with the Transaction file's contents
        """

        logger_.info("Assembling and uploading market-side orders")

        placement_df = placement_df.drop_duplicates(
            subset=[add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_ID)]
        )

        transaction_df = transaction_df.drop_duplicates(
            subset=[
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.ORDER_ID),
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.PLACEMENT_ID),
            ]
        )

        order_detail_df = pd.DataFrame()

        for order_detail_file_splitter_result in batches_by_input_file_type[
            FileTypes.ORDER_DETAILS
        ]:
            order_detail_batch_file_path: str = order_detail_file_splitter_result.path.as_posix()

            # NOTE: see https://steeleye.atlassian.net/browse/ON-4477?focusedCommentId=193231
            # for more details. We cannot enforce any datatypes
            # when reading this .csv file because the vendor Aladdin
            # already did that on their side. Some columns that contain only digits are provided
            # as integers if there are no null values, or as floats with a `.0`
            # suffix if there are null values.
            # Hence, we cannot read the files as is (dtype=str) because there are columns
            # between different files which are different in Aladdin's data
            # (due to the .0 suffix), but they must actually match! Hence, we let Pandas guess the dtypes, which will ensure
            # that any numerical columns are always interpreted the same way as Aladdin did,
            # integer or float. Note that a pd.merge() operation interprets 10 and 10.0
            # as the same value if the columns are of dtype int64 and float64 respectively.
            order_detail_batch_df: pd.DataFrame = pd.read_csv(
                order_detail_batch_file_path,
                encoding=order_detail_file_splitter_result.encoding,
            )
            for col in order_detail_batch_df.select_dtypes(include="int").columns:
                order_detail_batch_df[col] = order_detail_batch_df[col].astype("Int64")
            order_detail_df = pd.concat([order_detail_df, order_detail_batch_df], axis=0)

            del order_detail_batch_df

        order_detail_df = order_detail_df.drop_duplicates(
            subset=[
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID),
            ]
        )

        order_df = order_df.drop_duplicates(
            subset=[
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_ID),
            ]
        )

        # Build market-side Order batches - returns a list of cloud URIs
        # Iterate over each batch of fill rows, which represent
        # a batch of Client-Side PARFs (for which we will attempt to create synth NEWOs)
        for fill_file_splitter_result in batches_by_input_file_type[FileTypes.FILL]:
            # skip empty batches
            if fill_file_splitter_result.input_total_count == 0:
                continue

            fill_batch_file_path: str = fill_file_splitter_result.path.as_posix()
            # See the first comment in this module about
            # https://steeleye.atlassian.net/browse/ON-4477?focusedCommentId=193231 for more details.
            market_side_order_batch_df: pd.DataFrame = pd.read_csv(
                fill_batch_file_path, encoding=fill_file_splitter_result.encoding
            )

            # Iterate over all Placement batches to
            # add the necessary Placement data to each Market-Side Order.
            # Between the Fill and Placement files there is a Many-to-One relationship.
            market_side_order_batch_df = market_side_order_batch_df.merge(
                placement_df,
                how="left",
                left_on=add_prefix(FileTypes.FILL, FillSourceColumns.PLACEMENT_ID),
                right_on=add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_ID),
            )

            # Iterate over all Transaction batches to
            # add the necessary Transaction data to each Market-Side Order.
            # Between the Fill/Placement and Transaction files there is a Many-to-Many relationship.
            # This happens because you have Transactions with the same Order ID and Placement ID,
            # but different Portfolio ID values. Hence, we are forced to drop duplicate transactions
            # by Order ID and Placement ID. It does not matter which "portfolioId" we keep because
            # the data points we need for the Market-side orders are shared by all different portfolios
            # within the same Order ID.

            # In the case there are multiple transactions for the same portfolio, with different
            # counterparties, right now we are taking exactly one counterparty.
            # which one does not matter in the context of the Market Side PARF.
            market_side_order_batch_df = market_side_order_batch_df.merge(
                transaction_df,
                how="left",
                left_on=[
                    add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.ORDER_ID),
                    add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_ID),
                ],
                right_on=[
                    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.ORDER_ID),
                    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.PLACEMENT_ID),
                ],
            )

            # Iterate over all OrderDetail batches to
            # get the orderDetail.origOrderId for each Market-Side Order.
            # Between the Fill/Placement/Transaction and OrderDetail files
            # there is a Many-to-Many relationship. This happens because of the different portfolios,
            # same as when linking the Transactions.
            market_side_order_batch_df = market_side_order_batch_df.merge(
                order_detail_df,
                how="left",
                left_on=add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.ORDER_ID),
                right_on=add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID),
            )

            # When iterating over each batch of Fills with OrderDetails, we need to populate
            # a cache by Order ID, which will be passed onto the Transform Task
            # downstream in the workflow.
            # This cache is used to associate the exchange (ultimateVenue) of the market-side orders
            # to the associated client-side orders.
            # See https://steeleye.atlassian.net/browse/ON-4898?focusedCommentId=194464
            # for more details.
            order_cache_df = (
                market_side_order_batch_df.drop_duplicates(
                    subset=[add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID)]
                )
                .loc[
                    :,
                    [
                        add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID),
                        add_prefix(FileTypes.FILL, FillSourceColumns.EXCHANGE),
                    ],
                ]
                .dropna(
                    subset=[add_prefix(FileTypes.FILL, FillSourceColumns.EXCHANGE)],
                    axis=0,
                )
            )
            if not order_cache_df.empty:
                for _, row in order_cache_df.iterrows():
                    # reminder: `order_id_cache` is a defaultdict,
                    # hence no need to check if the "primary" key exists or if
                    # any of the nested keys exist.
                    order_id_cache[
                        str(
                            row[
                                add_prefix(
                                    FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID
                                )
                            ]
                        )
                    ]["exchange"] = row[add_prefix(FileTypes.FILL, FillSourceColumns.EXCHANGE)]

            # See the client-side-orders function logic for merging OTC trades for more
            # details on the ORDER_DETAIL_MERGE_ID column.
            order_detail_merge_id_series = pd.Series(
                data=list(
                    market_side_order_batch_df.loc[
                        :,
                        add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID),
                    ].values
                ),
                index=market_side_order_batch_df.index,
                name=DevColumns.ORDER_DETAIL_MERGE_ID,
            )
            null_orig_order_id_mask = order_detail_merge_id_series.isnull()
            order_detail_merge_id_series[null_orig_order_id_mask] = market_side_order_batch_df.loc[
                null_orig_order_id_mask,
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID),
            ]
            market_side_order_batch_df[DevColumns.ORDER_DETAIL_MERGE_ID] = (
                order_detail_merge_id_series
            )

            # Iterate over all Order batches to add the necessary Order data to each Market-Side Order
            market_side_order_batch_df = market_side_order_batch_df.merge(
                order_df,
                how="left",
                left_on=DevColumns.ORDER_DETAIL_MERGE_ID,
                right_on=add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_ID),
            )

            market_side_order_batch_df = market_side_order_batch_df.drop(
                [DevColumns.ORDER_DETAIL_MERGE_ID], axis=1
            )

            market_side_orders_output_file_name = Path(
                f"{output_file_name}_{AladdinV2ControllerOutput.MARKET_SIDE_ORDERS}_batch_{fill_file_splitter_result.batch_index}.csv"
            )
            market_side_orders_output_file_path = (
                cloud_provider_prefix
                + realm
                + "/"
                + Path.joinpath(output_lake_path, market_side_orders_output_file_name).as_posix()
            )

            # Market-side orders are not expected to exceed the max_batch_size
            # but this is a safety measure in case the data is not as expected.

            # NOTE: If the market-side orders flattening feature is enabled,
            # we will never split the data into sub-batches, because the data has already been
            # batched by fill.placementId, and any further batching will break the business logic.
            # WARNING: Downstream batches will have unpredictable sizes, as big as the max number of PARFs
            # per fill.placementId that the client has sent.
            if (
                not self._should_flatten_market_side_executions
                and market_side_order_batch_df.shape[0] > batch_size
            ):
                sub_batches_list: List[pd.DataFrame] = [
                    df.frame()
                    for df in run_merge_and_split_dataframes(
                        source_frame_list=[market_side_order_batch_df],
                        params=MergeAndSplitDataFramesParams(max_chunk_size=batch_size),
                    )
                ]

                for i, sub_batch in enumerate(sub_batches_list):
                    sub_batch_market_side_orders_output_file_path = (
                        market_side_orders_output_file_path.replace(".csv", f"_{i}.csv")
                    )
                    logger_.info(
                        f"Writing sub-batch to: {sub_batch_market_side_orders_output_file_path}"
                    )
                    sub_batch.to_csv(sub_batch_market_side_orders_output_file_path, index=False)
                    market_side_order_output_paths.append(
                        sub_batch_market_side_orders_output_file_path
                    )

                del sub_batches_list

            else:
                logger_.info(f"Writing batch to: {market_side_orders_output_file_path}")
                market_side_order_batch_df.to_csv(market_side_orders_output_file_path, index=False)
                market_side_order_output_paths.append(market_side_orders_output_file_path)

                del market_side_order_batch_df

    @staticmethod
    def assemble_and_upload_client_side_orders(
        batch_size: int,
        batches_by_input_file_type: Dict[str, List[FileSplitterResult]],
        client_side_order_output_paths: List[str],
        cloud_provider_prefix: str,
        order_id_cache: Dict[str, Dict[str, str | None]],
        output_file_name: str,
        output_lake_path: Path,
        realm: str,
        audit_path: str,
        streamed: bool,
    ) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """This function is responsible for assembling the client-side orders and
        uploading them to the cloud. We achieve this by reading the OrderDetail
        file in chunks, and by merging it against the Order, Transaction, and
        Placement files. The resulting dataframes are then uploaded to the cloud as
        CSV files to be processed downstream.

        Additionally, we update the order_id_cache as we iterate through the relevant
        input files. This cache will be propagated downstream to the transform tasks.

        :param batch_size: Max batch size of each output CSV file as defined in the config DB.
        :param batches_by_input_file_type:  Dictionary containing the batches of each input file type.
        :param client_side_order_output_paths: List where we will store the cloud URIs of the uploaded client-side orders.
        :param cloud_provider_prefix: Cloud provider prefix
        :param order_id_cache: Cache containing relevant data points by Order ID.
        :param output_file_name: Base name of each output file, to whom we will append the batch index.
        :param output_lake_path: Where each output file will be written to.
        :param realm: Tenant bucket without the cloud provider prefix.
        :param audit_path: Location of the audit file.
        :param streamed: Boolean flag indicating if the workflow is streamed or not.

        :return: A Tuple with exactly 3 DataFrames.
        The first DataFrame contains the Order data, the second DataFrame contains the Placement data,
        and the third DataFrame contains the Transaction data. These DataFrames are propagated downstream
        because they will be needed to assemble the Market-side orders, and there is no point in reading the input
        batches multiple times.
        """

        logger_.info("Assembling and uploading client-side orders.")

        order_df = pd.DataFrame()

        for order_file_splitter_result in batches_by_input_file_type[FileTypes.ORDER]:
            order_batch_file_path: str = order_file_splitter_result.path.as_posix()
            # See the first comment in this module about
            # https://steeleye.atlassian.net/browse/ON-4477?focusedCommentId=193231 for more details.
            order_batch_df: pd.DataFrame = pd.read_csv(
                order_batch_file_path, encoding=order_file_splitter_result.encoding
            )
            # Convert integer columns to nullable integers
            # So that when we do pd.merge() operations we
            # do not cast these values to float and add a ".0" suffix
            for col in order_batch_df.select_dtypes(include="int").columns:
                order_batch_df[col] = order_batch_df[col].astype("Int64")

            order_df = pd.concat([order_df, order_batch_df], axis=0)

            for source_column, cache_value_name in zip(
                [
                    OrderSourceColumns.TRADER,
                    OrderSourceColumns.ACTIVATED_TIMESTAMP_UTC,
                    OrderSourceColumns.AVG_PRICE,
                ],
                [
                    "trader",
                    "timestamp",
                    "avg_price",
                ],
            ):
                # When iterating over each batch of Orders, we need to populate
                # a cache by Order ID, which will be passed onto the Transform Task
                # downstream in the workflow.
                # This cache is used to associate the Trader of certain
                # "parent merged orders" with the child orders, alongside other data points.

                # See https://steeleye.atlassian.net/browse/ON-4679
                # and https://steeleye.atlassian.net/browse/ON-5208 for more details.
                # The cache can be used to propagate any other data point from the entire dataset
                # to each individual batch downstream.
                order_cache_df = (
                    order_batch_df.drop_duplicates(
                        subset=[add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_ID)]
                    )
                    .loc[
                        :,
                        [
                            add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_ID),
                            add_prefix(FileTypes.ORDER, source_column),
                        ],
                    ]
                    .dropna(
                        subset=[add_prefix(FileTypes.ORDER, source_column)],
                        axis=0,
                    )
                )

                if not order_cache_df.empty:
                    for _, row in order_cache_df.iterrows():
                        # reminder: `order_id_cache` is a defaultdict,
                        # hence no need to check if the "primary" key exists or if
                        # any of the nested keys exist.
                        order_id_cache[
                            str(row[add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_ID)])
                        ][cache_value_name] = row[add_prefix(FileTypes.ORDER, source_column)]

            del order_batch_df

        transaction_df = pd.DataFrame()

        for transaction_file_splitter_result in batches_by_input_file_type[FileTypes.TRANSACTION]:
            transaction_batch_file_path: str = transaction_file_splitter_result.path.as_posix()
            # See the first comment in this module about
            # https://steeleye.atlassian.net/browse/ON-4477?focusedCommentId=193231 for more details.
            transaction_batch_df: pd.DataFrame = pd.read_csv(
                transaction_batch_file_path, encoding=transaction_file_splitter_result.encoding
            )
            for col in transaction_batch_df.select_dtypes(include="int").columns:
                transaction_batch_df[col] = transaction_batch_df[col].astype("Int64")
            transaction_df = pd.concat([transaction_df, transaction_batch_df], axis=0)

            # When iterating over each batch of Transactions, we need to populate
            # a cache by Order ID, which will be passed onto the Transform Task
            # downstream in the workflow.
            # This cache is used to associate the Instrument Name of
            # certain NEWOs with executions for one portfolioId,
            # with NEWOs without executions for another portfolioId.

            # See https://steeleye.atlassian.net/browse/ON-4615 for more details.
            # The cache can be used to propagate any other data point from the entire dataset
            # to each individual batch downstream.
            transaction_cache_df = (
                transaction_batch_df.drop_duplicates(
                    subset=[add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.ORDER_ID)]
                )
                .loc[
                    :,
                    [
                        add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.ORDER_ID),
                        add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1),
                    ],
                ]
                .dropna(
                    subset=[add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1)],
                    axis=0,
                )
            )

            if not transaction_cache_df.empty:
                for _, row in transaction_cache_df.iterrows():
                    # reminder: `order_id_cache` is a defaultdict,
                    # hence no need to check if the "primary" key exists or
                    # if any of the nested keys exist.
                    order_id_cache[
                        str(
                            row[
                                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.ORDER_ID)
                            ]
                        )
                    ]["instrument_full_name"] = row[
                        add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1)
                    ]

            del transaction_batch_df

        placement_df = pd.DataFrame()

        for placement_file_splitter_result in batches_by_input_file_type[FileTypes.PLACEMENT]:
            placement_batch_file_path: str = placement_file_splitter_result.path.as_posix()
            # See the first comment in this module about
            # https://steeleye.atlassian.net/browse/ON-4477?focusedCommentId=193231 for more details.
            placement_batch_df: pd.DataFrame = pd.read_csv(
                placement_batch_file_path, encoding=placement_file_splitter_result.encoding
            )
            for col in placement_batch_df.select_dtypes(include="int").columns:
                placement_batch_df[col] = placement_batch_df[col].astype("Int64")
            placement_df = pd.concat([placement_df, placement_batch_df], axis=0)

            del placement_batch_df

        # add the columns necessary for the downstream merging operation to succeed
        # even if the placement file is empty
        if placement_df.empty:
            placement_df[add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.ORDER_ID)] = pd.NA
            placement_df[add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_ID)] = (
                pd.NA
            )

        # Build client-side Order batches - returns a list of cloud URIs
        # Iterate over each batch of OrderDetail rows, which represent
        # a batch of Client-Side PARFs (for which we will attempt to create synth NEWOs)
        for order_detail_file_splitter_result in batches_by_input_file_type[
            FileTypes.ORDER_DETAILS
        ]:
            order_detail_batch_file_path: str = order_detail_file_splitter_result.path.as_posix()

            # See the first comment in this module about
            # https://steeleye.atlassian.net/browse/ON-4477?focusedCommentId=193231 for more details.
            client_side_order_batch_df: pd.DataFrame = pd.read_csv(
                order_detail_batch_file_path,
                encoding=order_detail_file_splitter_result.encoding,
            )

            # The default behaviour is to merge the OrderDetail file with the Order file by
            # orderDetail.origOrderId and order.orderId.
            # However, to support OTC trades, we expect orderDetail.origOrderId to be null,
            # hence, we use orderDetail.orderId as the fallback.
            order_detail_merge_id_series = pd.Series(
                data=list(
                    client_side_order_batch_df.loc[
                        :,
                        add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID),
                    ].values
                ),
                index=client_side_order_batch_df.index,
                name=DevColumns.ORDER_DETAIL_MERGE_ID,
            )
            null_orig_order_id_mask = order_detail_merge_id_series.isnull()
            order_detail_merge_id_series[null_orig_order_id_mask] = client_side_order_batch_df.loc[
                null_orig_order_id_mask,
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID),
            ]
            client_side_order_batch_df[DevColumns.ORDER_DETAIL_MERGE_ID] = (
                order_detail_merge_id_series
            )

            # Iterate over all Order batches to add the necessary Order data to each Client-Side Order
            # Between the OrderDetails and Order files there is a Many-to-One relationship
            # thus the merged dataframe will have the same number of rows as the OrderDetails dataframe
            client_side_order_batch_df = client_side_order_batch_df.merge(
                order_df,
                how="left",
                left_on=DevColumns.ORDER_DETAIL_MERGE_ID,
                right_on=add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_ID),
            )
            client_side_order_batch_df = client_side_order_batch_df.drop(
                [DevColumns.ORDER_DETAIL_MERGE_ID], axis=1
            )

            # Iterate over all Transaction batches to add
            # the necessary Transaction data to each Client-Side Order.

            # |OLD LOGIC DOCUMENTATION|
            # Between the OrderDetails and Transaction files there is a One-to-Many relationship.
            # One Order with a given portfolio can have multiple
            # transactions with different counterparties,
            # hence, different cptyId, createdTimestampUtc, dealingCapacity,
            # execCptyId, and execCptyType values.
            # Thus, the merged dataframe will have slightly more rows than the OrderDetails dataframe
            # but this is expected.

            # This also means that by definition, the max_batch_size of the
            # client_side_orders batch may be exceeded, but by a small margin as this scenario of
            # duplicate order ids and portfolio ids with different counterparties is rare.
            # |END OLD LOGIC DOCUMENTATION|

            # The above documentation has been preserved, even though the logic was
            # changed as it is extremely important to have context on the nature
            # of the data we are processing as it may influence future decisions.
            # 100% that we will revisit this logic in the future to support the
            # granularity of multiple counterparties per Order ID and Portfolio ID.

            # |NEW LOGIC DOCUMENTATION|
            # See https://steeleye.atlassian.net/browse/ON-4617?focusedCommentId=184251 for more details.
            # We have decided, for now, to take exactly one counterparty for each portfolio.
            # It doesn't matter which one we take because we are always going to miss the necessary
            # granularity, but at least the ingested orders make sense and the quantities add up.

            client_side_order_batch_df = client_side_order_batch_df.merge(
                transaction_df.drop_duplicates(
                    subset=[
                        add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.ORDER_ID),
                        add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.PORTFOLIO_ID),
                    ]
                ),
                how="left",
                left_on=[
                    add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID),
                    add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.PORTFOLIO_ID),
                ],
                right_on=[
                    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.ORDER_ID),
                    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.PORTFOLIO_ID),
                ],
            )

            # Discard all OrderDetails where the orderDetail.origOrderId
            # is null AND NOT (order.orderStatus = F, AND ownerType is null)
            # NOTE: We only need to apply this logic for client-side Orders
            null_orig_order_id = client_side_order_batch_df[
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID)
            ].isnull()

            order_status_f_mask = (
                client_side_order_batch_df[
                    add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS)
                ]
                .astype("str")
                .str.fullmatch("F", case=False, na=False)
            )

            null_owner_type_mask = client_side_order_batch_df[
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE)
            ].isnull()

            records_to_discard_mask = null_orig_order_id & ~(
                order_status_f_mask & null_owner_type_mask
            )

            if records_to_discard_mask.any():
                missing_order_status_f_index = client_side_order_batch_df.loc[
                    records_to_discard_mask & ~order_status_f_mask,
                    add_prefix(FileTypes.ORDER_DETAILS, "__swarm_raw_index__"),
                ].to_list()
                owner_type_is_not_null_index = client_side_order_batch_df.loc[
                    records_to_discard_mask & ~null_owner_type_mask,
                    add_prefix(FileTypes.ORDER_DETAILS, "__swarm_raw_index__"),
                ].to_list()

                audit_data = defaultdict(list)
                for index, status_message in zip(
                    [
                        missing_order_status_f_index,
                        owner_type_is_not_null_index,
                    ],
                    [
                        "orderDetail.origOrderId is NULL and order.orderStatus is not ´F´",
                        "orderDetail.origOrderId is NULL and order.ownerType is not NULL",
                    ],
                ):
                    if index:
                        for i in index:
                            audit_data[f"OrderDetail|{i}"].append(status_message)

                upsert_audit(
                    audit_path=audit_path,
                    streamed=streamed,
                    input_data={
                        k: {
                            AuditorStaticFields.SKIPPED: 1,
                            AuditorStaticFields.STATUS: audit_data[k],
                        }
                        for k in audit_data
                    },
                    models=[Order],
                )

                client_side_order_batch_df = client_side_order_batch_df.loc[
                    ~records_to_discard_mask, :
                ]

            # Iterate over all Placement batches to
            # add the necessary Placement data to each Client-Side Order.
            # Between the Transaction and Placement files, there is a Many-to-One relationship.
            # This happens because you can have multiple Transactions for the same Order ID,
            # with different Portfolio ID values, but the same Placement ID.
            client_side_order_batch_df = client_side_order_batch_df.merge(
                placement_df,
                how="left",
                left_on=[
                    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.ORDER_ID),
                    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.PLACEMENT_ID),
                ],
                right_on=[
                    add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.ORDER_ID),
                    add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_ID),
                ],
            )

            client_side_orders_output_file_name = Path(
                f"{output_file_name}_{AladdinV2ControllerOutput.CLIENT_SIDE_ORDERS}_batch_{order_detail_file_splitter_result.batch_index}.csv"
            )
            client_side_orders_output_file_path = (
                cloud_provider_prefix
                + realm
                + "/"
                + Path.joinpath(output_lake_path, client_side_orders_output_file_name).as_posix()
            )

            # Because the client_side_order_batch_df may exceed the max_batch_size
            # due to multiple counterparties for each portfolio, we need to split it
            # into smaller batches.
            if client_side_order_batch_df.shape[0] > batch_size:
                sub_batches_list: List[pd.DataFrame] = [
                    df.frame()
                    for df in run_merge_and_split_dataframes(
                        source_frame_list=[client_side_order_batch_df],
                        params=MergeAndSplitDataFramesParams(max_chunk_size=batch_size),
                    )
                ]

                for i, sub_batch in enumerate(sub_batches_list):
                    sub_batch_client_side_orders_output_file_path = (
                        client_side_orders_output_file_path.replace(".csv", f"_{i}.csv")
                    )

                    logger_.info(
                        f"Writing sub-batch to: {sub_batch_client_side_orders_output_file_path}"
                    )
                    sub_batch.to_csv(sub_batch_client_side_orders_output_file_path, index=False)
                    client_side_order_output_paths.append(
                        sub_batch_client_side_orders_output_file_path
                    )

                del sub_batches_list

            else:
                logger_.info(f"Writing batch to: {client_side_orders_output_file_path}")
                client_side_order_batch_df.to_csv(client_side_orders_output_file_path, index=False)
                client_side_order_output_paths.append(client_side_orders_output_file_path)

                del client_side_order_batch_df

        return order_df, placement_df, transaction_df

    @staticmethod
    def collect_required_file_uris(
        cloud_provider: CloudProviderEnum, file_url: str, realm: str
    ) -> pd.DataFrame:
        """Evaluate if the workflow meets all requirements to proceed. If it does
        not, raise an exception. If it does, return a DataFrame with the cloud URIs
        of each relevant file for this workflow's execution.

        :param cloud_provider: Cloud provider
        :param file_url: Absolute path of the input file
        :param realm: Tenant bucker without the cloud provider prefix

        :return: A DataFrame with the cloud URIs of each relevant file for this workflow's execution.
        """

        # Check if all files are present.
        # Raises an exception if the requirements are not met.
        # Returns a DataFrame with the file URLs if they are all present.
        multiple_files_input_controller_result: pd.DataFrame = run_multiple_files_input_controller(
            file_url=file_url,
            realm=realm,
            params=MultipleFilesInputControllerParams(
                list_of_files_regex=[
                    ".*((FI|DERIV|EQ|FX)\\.OrderDetail\\.).*",
                    ".*((FI|DERIV|EQ|FX)\\.Order\\.).*",
                    ".*((FI|DERIV|EQ|FX)\\.Transaction\\.).*",
                    ".*((FI|DERIV|EQ|FX)\\.Placement\\.).*",
                    ".*((FI|DERIV|EQ|FX)\\.Fill\\.).*",
                ],
                unique_identifier_regex=[
                    UNIQUE_IDENTIFIER_REGEX,  # i.e "20220226 - YYYYMMDD"
                    {
                        "regex": ".*(FI|DERIV|EQ|FX)",
                        "start_index": 3,
                        "stop_index": 19,
                    },  # Matched Asset Class like EQ, FX, FI, DERIV
                ],
                list_of_files_regex_for_which_empty_frame_returned=[
                    ".*\\_BETA\\..*",
                    ".*\\.Employee\\..*",
                    ".*\\.Broker\\..*",
                    ".*\\.PlacementAlgo\\..*",
                    ".*\\.Portfolio\\..*",
                    ".*\\.Quote\\..*",
                    ".*SMFPriceMultiplier\\..*",
                    ".*\\.OrderCustom\\..*",
                    ".*\\.TradeCustom\\..*",
                ],
                file_links_in_output=True,
                prefix_for_file_links_in_output="",
            ),
            cloud_provider=cloud_provider,
            skip_serializer=True,
        )
        if multiple_files_input_controller_result.empty:
            raise SkipIfFileIsNotPartOfTheWorkflow(
                f"The input file: {Path(file_url).name} does not have to be processed."
            )
        return multiple_files_input_controller_result

    def batch_input_files(
        self,
        multiple_files_input_controller_result: pd.DataFrame,
        batch_size: int,
        realm: str,
        streamed: bool,
        audit_path: str,
        temp_dir: Path,
    ) -> Dict[str, List[FileSplitterResult]]:
        """This function is responsible for going through each relevant input file,
        and splitting them into reasonably sized batches.

        :param multiple_files_input_controller_result: DataFrame containing the cloud URIs of input files
        :param batch_size: Max batch size as defined in the config DB
        :param realm: Tenant bucket without the cloud provider prefix
        :param streamed: True or False, as defined in the config DB (for auditing purposes)
        :param audit_path: Absolute path of the JSON audit file.
        :param temp_dir: Temporary directory to store the batches in disk.

        :return: A dictionary with five keys, each representing one of the relevant input files.
        Each key contains a list of FileSplitterResult objects, which contain the batches that were extracted
        from each input file.
        """

        multiple_files_input_controller_result.columns = (
            multiple_files_input_controller_result.columns.str.split(".", n=1).str[-1]
        )
        dataframes = {}
        file_url_suffix = "._file_url"

        # The files' date is the same regardless of file type, so we can use any of them.
        # Here we are using the Order file.
        try:
            input_file_date: str = datetime.strptime(
                re.search(
                    "\\d{4}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])",
                    str(
                        multiple_files_input_controller_result.loc[0, f"order{file_url_suffix}"]
                    ).split("/")[-1],
                ).group(),  # type: ignore[union-attr]
                "%Y%m%d",
            ).strftime(DATE_FORMAT)
        except AttributeError:
            raise AttributeError(
                f"The input file does not have a valid date format in the name: "
                f"{str(multiple_files_input_controller_result.loc[0, 'order{file_url_suffix}'])}"
            )

        for file_type_url, file_type, pre_process_func in zip(
            [
                f"order{file_url_suffix}",
                f"orderdetail{file_url_suffix}",
                f"transaction{file_url_suffix}",
                f"placement{file_url_suffix}",
                f"fill{file_url_suffix}",
            ],
            [
                FileTypes.ORDER,
                FileTypes.ORDER_DETAILS,
                FileTypes.TRANSACTION,
                FileTypes.PLACEMENT,
                FileTypes.FILL,
            ],
            [
                pre_process_order,
                pre_process_order_detail,
                pre_process_transaction,
                pre_process_placement,
                pre_process_fill,
            ],
        ):
            cloud_uri = str(multiple_files_input_controller_result.loc[0, file_type_url])

            local_path: str = run_download_file(file_url=cloud_uri)
            temp_path: Path = temp_dir.joinpath(file_type)
            temp_path.mkdir(parents=True, exist_ok=True)

            if self._should_flatten_market_side_executions and file_type == FileTypes.FILL:
                split_csv_files: List[FileSplitterResult] = run_csv_file_splitter_by_column_values(
                    csv_path=Path(local_path),
                    target_columns=[FillSourceColumns.PLACEMENT_ID],
                    batch_size=batch_size,
                    pre_process_func=PreProcessFunc(
                        func=pre_process_func,
                        func_kwargs={
                            "input_file_date": input_file_date,
                            "audit_path": audit_path,
                            "streamed": streamed,
                            "should_flatten_market_side_executions": True,
                        },
                    ),
                    propagate_index=False,
                )

            else:
                # NOTE: through "pre_process_func" we are able to inject
                # custom behaviour in CSVFileSplitter to ensure that we only need
                # to pre-process the batches once.
                split_csv_files: List[FileSplitterResult] = run_csv_file_splitter(
                    params=ParamsCsvFileSplitter(
                        chunksize=batch_size,
                        detect_encoding=True,
                        sniff_delimiter=True,
                        include_file_name_in_batches=True,
                        pre_process_func=PreProcessFunc(
                            func=pre_process_func,
                            func_kwargs={
                                "input_file_date": input_file_date,
                                "audit_path": audit_path,
                                "streamed": streamed,
                            },
                        ),
                    ),
                    csv_path=local_path,
                    realm=realm,
                    sources_dir=temp_path.as_posix(),
                    streamed=streamed,
                )

            dataframes[file_type] = split_csv_files

        return dataframes

    def convert_aladdin_market_file_to_steeleye_market_file(
        self,
        market_type: AladdinV2MarketTypeEnum,
        file_uri: str,
        realm: str,
        cloud_provider: CloudProviderEnum,
        workflow_name: str,
    ):
        """Converts an Aladdin Market file into a SteelEye MyMarket file. This
        function processes the provided Aladdin Market file, aligns its structure
        with SteelEye's MyMarket requirements, and stores the output to a specified
        cloud location that will trigger the Universal MyMarket Firm feed. It
        ensures that the necessary columns are included, removes duplicates, and
        maps required fields.

        It supports Broker and Portfolio files.

        :param market_type: Enum representing the type of Aladdin Market file being processed.
        :param file_uri: Cloud URI of the Aladdin Market file to be processed.
        :param realm: Tenant "bucket" without the cloud provider prefix
        :param cloud_provider: Cloud provider hosting `file_uri`
        :param workflow_name: Workflow name to be used in the target cloud path.
        """
        logger_.info(f"Converting Aladdin Market file to SteelEye MyMarket file: {file_uri}")

        # Aladdin Market files can be stored in memory as they are relatively small
        # i.e. Hundreds of rows, not millions, and ~10 columns
        # See the first comment in `order_aladdin_v2_controller_criteria.py` about
        # https://steeleye.atlassian.net/browse/ON-4477?focusedCommentId=193231 for more details.
        aladdin_market_df = pd.read_csv(file_uri)
        for col in aladdin_market_df.select_dtypes(include="int").columns:
            aladdin_market_df[col] = aladdin_market_df[col].astype("Int64")

        if aladdin_market_df.empty:
            logger_.warning("Aladdin Market file is empty")
            return

        market_type_map = {
            AladdinV2MarketTypeEnum.BROKER: BrokerSourceColumns,
            AladdinV2MarketTypeEnum.PORTFOLIO: PortfolioSourceColumns,
        }

        source_columns_class = market_type_map[market_type]

        # Add missing columns with NA values
        relevant_source_columns: List[str] = source_columns_class.all()
        aladdin_market_df = add_missing_columns(
            dataframe=aladdin_market_df, columns=relevant_source_columns
        )

        # Keep only the relevant source columns and discard all duplicates
        aladdin_market_df = (
            aladdin_market_df.loc[:, relevant_source_columns].drop_duplicates().dropna()
        )
        if aladdin_market_df.empty:
            logger_.warning("Aladdin Market file does not contain any valid records")
            return

        # Mappings
        aladdin_market_df["LEI"] = pd.NA

        if market_type == AladdinV2MarketTypeEnum.BROKER:
            aladdin_market_df["Type"] = FirmType.COUNTERPARTY.value
            aladdin_market_df = aladdin_market_df.rename(
                columns={
                    BrokerSourceColumns.ISSUER_LONG_NAME: "Name",
                    BrokerSourceColumns.CPTY_ID: "TradeFileIdentifiers",
                }
            )
        elif market_type == AladdinV2MarketTypeEnum.PORTFOLIO:
            aladdin_market_df["Type"] = FirmType.CLIENT.value
            aladdin_market_df = aladdin_market_df.rename(columns=self._get_portfolio_map())

        # See point 6 of https://steeleye.atlassian.net/browse/ON-5125 for more details.
        # Aladdin sends us files with a `.0` suffix on certain columns with only numerical values,
        # whenever there is at least one null value in the column.
        # This means that Aladdin can refer to the Trader ID "25" as "25.0"
        # both in the Broker and Portfolio files, or the trade files, depending
        # on the dataset. To ensure we never miss linking parties because
        # of this, we will always generate the two TradeFileIdentifiers:
        # with and without the `.0` suffix.
        aladdin_market_df["TradeFileIdentifiers.1"] = pd.NA
        contains_dot_zero_suffix_mask = (
            aladdin_market_df["TradeFileIdentifiers"].astype("string").str.endswith(".0")
        )
        aladdin_market_df.loc[contains_dot_zero_suffix_mask, "TradeFileIdentifiers.1"] = (
            aladdin_market_df.loc[contains_dot_zero_suffix_mask, "TradeFileIdentifiers"]
            .astype("string")
            .str.replace(".0", "")
        )
        aladdin_market_df.loc[~contains_dot_zero_suffix_mask, "TradeFileIdentifiers.1"] = (
            aladdin_market_df.loc[~contains_dot_zero_suffix_mask, "TradeFileIdentifiers"].astype(
                "string"
            )
            + ".0"
        )

        cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

        # example: 's3://irises8.dev.steeleye.co/flows/mymarket-universal-steeleye-firm/order_aladdin_v2/broker/AladdinTCA.Diogo_EQ.Broker.20241023.csv'
        target_cloud_path = f"{cloud_provider_prefix}{realm}/{MY_MARKET_FIRM_FEED_CLOUD_PATH}{workflow_name}/{market_type}/{file_uri.split('/')[-1]}"

        logger_.info(f"Writing SteelEye MyMarket Firm file to remote: {target_cloud_path}")
        aladdin_market_df.to_csv(target_cloud_path, index=False)

    @staticmethod
    def _get_portfolio_map() -> Dict[str, str]:
        portfolio_map = {
            PortfolioSourceColumns.PORTFOLIO_NAME: "Name",
            PortfolioSourceColumns.PORTFOLIO_TICKER: "FirmDetailsIndustry",
            PortfolioSourceColumns.PRIMARY_PM_ID: "DecisionMakerId",
            PortfolioSourceColumns.PORTFOLIO_ID: "TradeFileIdentifiers",
        }
        return portfolio_map


def pre_process_generic_steps(df: pd.DataFrame, file_type: str) -> pd.DataFrame:
    """Generic pre-process function that will be applied to all input file
    types. This function will:

    - Add the {file_type}. prefix to all input columns
    - Ensure all input schema columns are present
    - Discard any non input schema columns
    - Ensure all columns are of type string
    - Discard all duplicate rows

    :param df: Input DataFrame containing a batch of rows of a given file type
    :param file_type: Specific file type of the input DataFrame

    :return: The same "df" object but mutated with the aforementioned steps
    """

    logger_.info(f"Pre-processing {file_type} file with {df.shape[0]} rows.")

    file_type_source_columns_map = {
        FileTypes.ORDER: OrderSourceColumns,
        FileTypes.ORDER_DETAILS: OrderDetailSourceColumns,
        FileTypes.TRANSACTION: TransactionSourceColumns,
        FileTypes.PLACEMENT: PlacementSourceColumns,
        FileTypes.FILL: FillSourceColumns,
    }

    # Add the file type prefix to all columns and convert them to string
    # Downstream tasks will convert each column to the expected datatype
    df = df.add_prefix(file_type + ".").astype("string")

    expected_columns = [
        add_prefix(file_type, col_name)
        for key, col_name in file_type_source_columns_map[file_type].__dict__.items()
        if isinstance(key, str) and not key.startswith("__")
    ]
    expected_columns.append(f"{file_type}.__swarm_raw_index__")

    # Add missing columns
    for column in expected_columns:
        if column not in df.columns:
            df[column] = pd.NA

    # Only keep columns which are required downstream
    df = df.loc[:, expected_columns].drop_duplicates()

    return df


def pre_process_order_detail(df: pd.DataFrame, **kwargs):
    """
    The OrderDetails file does not require any specific pre-processing.
    The non-OTC filter for null OrderId or OrigOrderId is done
    when building client-side batches because it requires data points
    from the OrderDetail file + Order file + Transaction file

    :param df: Input DataFrame containing a batch of rows from the OrderDetail file
    :return: Mutated "df" object
    """

    df = pre_process_generic_steps(df=df, file_type=FileTypes.ORDER_DETAILS)

    return df


def pre_process_order(df: pd.DataFrame, **kwargs):
    """The Order file does not require any specific pre-processing."""
    df = pre_process_generic_steps(df=df, file_type=FileTypes.ORDER)

    return df


def pre_process_transaction(df: pd.DataFrame, **kwargs):
    """Specific pre-processing for the Transaction file type:

    [DEPRECATED LOGIC]
    - Discard and audit all Transactions where the transaction.tradeDate
    is different from the actual date of the input file

    :param df: Input DataFrame containing a batch of rows from the Transaction file
    :return: Mutated "df" object
    """
    df = pre_process_generic_steps(df=df, file_type=FileTypes.TRANSACTION)

    return df


def pre_process_placement(df: pd.DataFrame, **kwargs):
    """Specific pre-processing for the Placement file type:

    [DEPRECATED LOGIC]
    - Discard all Placements where the placement.createdTimestampUtc
    is different from the actual date of the input file.

    :param df: Input DataFrame containing a batch of rows from the Placement file
    :return: Mutated "df" object
    """
    df = pre_process_generic_steps(df=df, file_type=FileTypes.PLACEMENT)

    return df


def pre_process_fill(df: pd.DataFrame, **kwargs):
    """Specific pre-processing for the Fill file type:

    - Discard all FILLS where price and quantity = 0

    [DEPRECATED LOGIC]
    - Discard all FILLS where the fill.executedTimestampUtc
    is different from the actual date of the input file

    :param df: Input DataFrame containing a batch of rows from the Fill file
    :return: Mutated "df" object
    """

    df = pre_process_generic_steps(df=df, file_type=FileTypes.FILL)
    audit_path: str = kwargs["audit_path"]
    streamed: bool = kwargs["streamed"]

    # pre_process_fill can be injected in a CsvFileSplitter task or a CsvFileSplitterByColumnValues task.
    # If it is the latter, we need to add the __swarm_raw_index__ column
    if kwargs.get("should_flatten_market_side_executions"):
        # The csv_file_splitter_by_column_values does not populate the __swarm_raw_index__ column.
        # Instead, it sets it as the dataframe index, and it starts at 1, instead of 0.
        df.loc[:, add_prefix(FileTypes.FILL, "__swarm_raw_index__")] = df.index - 1

    # | Discard all FILLS where price and quantity = 0|
    zero_price_mask: "pd.Series[bool]" = (
        df[add_prefix(FileTypes.FILL, FillSourceColumns.EXECUTED_PRICE)].astype("float") == 0.0
    ).fillna(False)

    zero_quantity_mask: "pd.Series[bool]" = (
        df[add_prefix(FileTypes.FILL, FillSourceColumns.EXECUTED_QUANTITY)].astype("float") == 0.0
    ).fillna(False)

    zero_price_quantity_mask: "pd.Series[bool]" = zero_price_mask & zero_quantity_mask

    if zero_price_quantity_mask.any():
        zero_fills_raw_index: List[int] = df.loc[
            zero_price_quantity_mask, add_prefix(FileTypes.FILL, "__swarm_raw_index__")
        ].to_list()

        upsert_audit(
            audit_path=audit_path,
            streamed=streamed,
            input_data={
                f"Fill_from_input_row_{k}": {
                    AuditorStaticFields.SKIPPED: 1,
                    AuditorStaticFields.STATUS: ["Skipped Fill with zero price and quantity"],
                }
                for k in zero_fills_raw_index
            },
            models=[Order],
        )

        df = df.loc[~zero_price_quantity_mask, :]

    return df


def order_aladdin_v2_controller_criteria(
    aries_task_input: AriesTaskInput,
    file_splitter_by_criteria_aries_task_input: FileSplitterByCriteriaAriesTaskInput,
    workflow_config: addict.Dict,
    audit_path: str,
    **kwargs,
) -> Dict[str, Any]:
    class_instance = DefaultOrderAladdinV2ControllerCriteria()
    return class_instance.criteria_driver(
        aries_task_input=aries_task_input,
        file_splitter_by_criteria_aries_task_input=file_splitter_by_criteria_aries_task_input,
        workflow_config=workflow_config,
        audit_path=audit_path,
    )

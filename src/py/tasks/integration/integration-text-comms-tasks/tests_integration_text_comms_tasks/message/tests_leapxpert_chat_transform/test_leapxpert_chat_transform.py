import boto3
import fsspec
import json
import pandas as pd
import pytest
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.generic.participants import link_participants
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.io.utils import check_file_exists
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.utilities.helpers_for_tests import sort_identifiers_columns  # type: ignore
from aries_task_link.models import AriesTaskInput
from freezegun import freeze_time
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_text_comms_tasks.message.leapxpert_chat_transform.leapxpert_chat_transform_task import (  # noqa E501
    leapxpert_chat_transform_run,
)
from integration_wrapper.static import StaticFields
from moto import mock_aws
from pathlib import Path
from se_elasticsearch.repository.models import ResourceConfig
from se_elasticsearch.repository.static import MetaPrefix
from se_io_utils.json_utils import write_named_temporary_json
from se_io_utils.tempfile_utils import tmp_directory
from shutil import rmtree
from typing import List
from unittest.mock import patch

BUCKET_NAME: str = "test.dev.steeleye.co"

CURRENT_PATH = Path(__file__).parent
DATA_PATH = Path(__file__).parent.joinpath("data")

TEMP_DIR: Path = tmp_directory()
AUDIT_PATH: Path = TEMP_DIR.joinpath("audit.json")

LOCAL_BUCKET_PATH = DATA_PATH.joinpath("buckets", BUCKET_NAME)

EXPECTED_RESULTS_PATH = DATA_PATH.joinpath("expected_results")
EXPECTED_MESSAGE_OUTPUT_NDJSON_PATH = EXPECTED_RESULTS_PATH.joinpath(
    "expected_message_output.ndjson"
)
EXPECTED_CHAT_EVENT_OUTPUT_NDJSON_PATH = EXPECTED_RESULTS_PATH.joinpath(
    "expected_chat_event_output.ndjson"
)
EXPECTED_MESSAGE_OUTPUT_BATCH_0_NDJSON_PATH = EXPECTED_RESULTS_PATH.joinpath(
    "expected_message_output_batch_0.ndjson"
)
EXPECTED_MESSAGE_OUTPUT_BATCH_1_NDJSON_PATH = EXPECTED_RESULTS_PATH.joinpath(
    "expected_message_output_batch_1.ndjson"
)
EXPECTED_MESSAGE_OUTPUT_BATCH_2_NDJSON_PATH = EXPECTED_RESULTS_PATH.joinpath(
    "expected_message_output_batch_2.ndjson"
)
EXPECTED_CHAT_EVENT_OUTPUT_BATCH_0_NDJSON_PATH = EXPECTED_RESULTS_PATH.joinpath(
    "expected_chat_event_output_batch_0.ndjson"
)
EXPECTED_CHAT_EVENT_OUTPUT_BATCH_1_NDJSON_PATH = EXPECTED_RESULTS_PATH.joinpath(
    "expected_chat_event_output_batch_1.ndjson"
)

EXPECTED_CHAT_EVENT_OUTPUT_BATCH_2_NDJSON_PATH = EXPECTED_RESULTS_PATH.joinpath(
    "expected_chat_event_output_batch_2.ndjson"
)

EXPRECTED_OUTPUT_PATHS = {
    "message": [
        EXPECTED_MESSAGE_OUTPUT_BATCH_0_NDJSON_PATH,
        EXPECTED_MESSAGE_OUTPUT_BATCH_1_NDJSON_PATH,
        EXPECTED_MESSAGE_OUTPUT_BATCH_2_NDJSON_PATH,
    ],
    "chat_events": [
        EXPECTED_CHAT_EVENT_OUTPUT_BATCH_0_NDJSON_PATH,
        EXPECTED_CHAT_EVENT_OUTPUT_BATCH_1_NDJSON_PATH,
        EXPECTED_CHAT_EVENT_OUTPUT_BATCH_2_NDJSON_PATH,
    ],
}


@pytest.fixture(scope="session", autouse=True)
def cleanup(request):
    def _end():
        rmtree(TEMP_DIR)

    request.addfinalizer(_end)


mock_aiobotocore_convert_to_response_dict()


class TestLeapXpertChatTransform:
    """Test suite for LeapXpert Chat Transform."""

    @staticmethod
    def teardown_method():
        if AUDIT_PATH.exists():
            AUDIT_PATH.unlink()

    @mock_aws
    @freeze_time(time_to_freeze="2022-07-06 06:59:38.000000+00:00")
    def test_it_can_run_end_to_end(
        self,
        sample_aries_task_input: AriesTaskInput,
        link_participants_scroll_result: List[dict],
    ):
        # Create mock S3 bucket and objects to it
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME)

        aries_task_result = self._run_aries_task(
            sample_aries_task_input=sample_aries_task_input,
            link_participants_scroll_result=link_participants_scroll_result,
        )

        # Ensure the Messages output is correct
        message_task_params_key: str = aries_task_result.output_param.params[MetaModel.MESSAGE][
            "dynamicTasks"
        ][0]["taskReferenceName"]

        message_ndjon_s3_uri: str = aries_task_result.output_param.params[MetaModel.MESSAGE][
            "dynamicTaskInputs"
        ][message_task_params_key]["io_param"]["params"]["file_uri"]

        message_final_result_expected: pd.DataFrame = pd.read_json(
            EXPECTED_MESSAGE_OUTPUT_NDJSON_PATH.as_posix(), lines=True
        )

        message_final_result: pd.DataFrame = pd.read_json(message_ndjon_s3_uri, lines=True)

        # Sort identifiers columns
        sort_identifiers_columns(
            result_df=message_final_result, expected_result_df=message_final_result_expected
        )

        message_final_result["attachments"] = message_final_result["attachments"].apply(
            remove_date_for_attachments
        )
        message_final_result_expected["attachments"] = message_final_result_expected[
            "attachments"
        ].apply(remove_date_for_attachments)

        pd.testing.assert_frame_equal(
            left=message_final_result,
            right=message_final_result_expected,
            check_like=True,
        )

        # Ensure the Chat Events output is correct
        chat_event_task_params_key: str = aries_task_result.output_param.params[
            MetaModel.CHAT_EVENT
        ]["dynamicTasks"][0]["taskReferenceName"]

        chat_event_ndjon_s3_uri: str = aries_task_result.output_param.params[MetaModel.CHAT_EVENT][
            "dynamicTaskInputs"
        ][chat_event_task_params_key]["io_param"]["params"]["file_uri"]

        chat_event_final_result_expected: pd.DataFrame = pd.read_json(
            EXPECTED_CHAT_EVENT_OUTPUT_NDJSON_PATH.as_posix(), lines=True
        )

        chat_event_final_result: pd.DataFrame = pd.read_json(chat_event_ndjon_s3_uri, lines=True)

        # Sort identifiers columns
        sort_identifiers_columns(
            result_df=chat_event_final_result, expected_result_df=chat_event_final_result_expected
        )

        pd.testing.assert_frame_equal(
            left=chat_event_final_result,
            right=chat_event_final_result_expected,
            check_like=True,
        )

        # Ensure attachments were uploaded
        assert (
            check_file_exists(
                path=f"s3://{BUCKET_NAME}/attachments/leapxpert/chat/2022/07/06/SteelEye-2023062720000/649b4eb0fe844748da53578f.jpg"
            )
            is True
        )

        assert (
            check_file_exists(
                path=f"s3://{BUCKET_NAME}/attachments/leapxpert/chat/2022/07/06/SteelEye-2023062720000/649b4eb0fe844748da535790.jpg"
            )
            is True
        )

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "leapxpert_chat"
            ]["leapxpert_chat_transform"]
            if aries_task_result.app_metric
            else {}
        )

        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 35

        assert aries_task_result.output_param.params == {
            MetaModel.MESSAGE: {
                "dynamicTasks": [
                    {
                        "taskReferenceName": "elastic_ingestion_ref_0",
                        "type": "SUB_WORKFLOW",
                        "subWorkflowParam": {"name": "elastic_ingestion"},
                    },
                ],
                "dynamicTaskInputs": {
                    "elastic_ingestion_ref_0": {
                        "io_param": {
                            "params": {
                                "es_action": "create",
                                "data_model": "se_elastic_schema.models.tenant.communication.message:Message",  # noqa: E501
                                "file_uri": message_ndjon_s3_uri,
                            },
                        },
                        "workflow": {
                            "trace_id": "trace",
                            "start_timestamp": "2022-07-06T00:00:00",
                            "name": "leapxpert_chat",
                            "stack": "dev-blue",
                            "tenant": "test",
                        },
                    }
                },
            },
            MetaModel.CHAT_EVENT: {
                "dynamicTasks": [
                    {
                        "taskReferenceName": "elastic_ingestion_ref_0",
                        "type": "SUB_WORKFLOW",
                        "subWorkflowParam": {"name": "elastic_ingestion"},
                    },
                ],
                "dynamicTaskInputs": {
                    "elastic_ingestion_ref_0": {
                        "io_param": {
                            "params": {
                                "es_action": "create",
                                "data_model": "se_elastic_schema.models.tenant.communication.chat_event:ChatEvent",  # noqa: E501
                                "file_uri": chat_event_ndjon_s3_uri,
                            },
                        },
                        "workflow": {
                            "trace_id": "trace",
                            "start_timestamp": "2022-07-06T00:00:00",
                            "name": "leapxpert_chat",
                            "stack": "dev-blue",
                            "tenant": "test",
                        },
                    }
                },
            },
        }

    @mock_aws
    @freeze_time(time_to_freeze="2022-07-06 06:59:38.000000+00:00")
    @patch(
        "integration_text_comms_tasks.message.leapxpert_chat_transform.leapxpert_chat_transform_flow.BATCH_SIZE",
        11,
    )
    def test_it_can_run_end_to_end_with_batching(
        self,
        sample_aries_task_input: AriesTaskInput,
        link_participants_scroll_result: List[dict],
    ):
        # Create mock S3 bucket and objects to it
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME)

        aries_task_result = self._run_aries_task(
            sample_aries_task_input=sample_aries_task_input,
            link_participants_scroll_result=link_participants_scroll_result,
        )

        # assert it generated correct number batches
        assert len(aries_task_result.output_param.params[MetaModel.MESSAGE]["dynamicTasks"]) == 3
        assert len(aries_task_result.output_param.params[MetaModel.CHAT_EVENT]["dynamicTasks"]) == 3

        ndjson_s3_uri = []
        task_params_key: str
        batch_ndjson_s3_uri: str

        for event in ["message", "chat_events"]:
            for idx in range(len(EXPRECTED_OUTPUT_PATHS[event])):
                if event == "message":
                    task_params_key = aries_task_result.output_param.params[MetaModel.MESSAGE][
                        "dynamicTasks"
                    ][idx]["taskReferenceName"]

                    batch_ndjson_s3_uri = aries_task_result.output_param.params[MetaModel.MESSAGE][
                        "dynamicTaskInputs"
                    ][task_params_key]["io_param"]["params"]["file_uri"]

                    ndjson_s3_uri.append(batch_ndjson_s3_uri)
                elif event == "chat_events":
                    task_params_key = aries_task_result.output_param.params[MetaModel.CHAT_EVENT][
                        "dynamicTasks"
                    ][idx]["taskReferenceName"]

                    batch_ndjson_s3_uri = aries_task_result.output_param.params[
                        MetaModel.CHAT_EVENT
                    ]["dynamicTaskInputs"][task_params_key]["io_param"]["params"]["file_uri"]

                    ndjson_s3_uri.append(batch_ndjson_s3_uri)

                final_result_expected: pd.DataFrame = pd.read_json(
                    EXPRECTED_OUTPUT_PATHS[event][idx].as_posix(), lines=True
                )

                batch_final_result: pd.DataFrame = pd.read_json(batch_ndjson_s3_uri, lines=True)

                # Sort identifiers columns
                sort_identifiers_columns(
                    result_df=batch_final_result, expected_result_df=final_result_expected
                )

                if "attachments" in batch_final_result.columns:
                    batch_final_result["attachments"] = batch_final_result["attachments"].apply(
                        remove_date_for_attachments
                    )

                if "attachments" in final_result_expected.columns:
                    final_result_expected["attachments"] = final_result_expected[
                        "attachments"
                    ].apply(remove_date_for_attachments)

                pd.testing.assert_frame_equal(
                    left=batch_final_result,
                    right=final_result_expected,
                    check_like=True,
                )

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "leapxpert_chat"
            ]["leapxpert_chat_transform"]
            if aries_task_result.app_metric
            else {}
        )

        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 35

        assert aries_task_result.output_param.params == {
            MetaModel.MESSAGE: {
                "dynamicTasks": [
                    {
                        "taskReferenceName": "elastic_ingestion_ref_0",
                        "type": "SUB_WORKFLOW",
                        "subWorkflowParam": {"name": "elastic_ingestion"},
                    },
                    {
                        "taskReferenceName": "elastic_ingestion_ref_1",
                        "type": "SUB_WORKFLOW",
                        "subWorkflowParam": {"name": "elastic_ingestion"},
                    },
                    {
                        "taskReferenceName": "elastic_ingestion_ref_2",
                        "type": "SUB_WORKFLOW",
                        "subWorkflowParam": {"name": "elastic_ingestion"},
                    },
                ],
                "dynamicTaskInputs": {
                    "elastic_ingestion_ref_0": {
                        "io_param": {
                            "params": {
                                "es_action": "create",
                                "data_model": "se_elastic_schema.models.tenant.communication.message:Message",  # noqa: E501
                                "file_uri": ndjson_s3_uri[0],
                            },
                        },
                        "workflow": {
                            "trace_id": "trace",
                            "start_timestamp": "2022-07-06T00:00:00",
                            "name": "leapxpert_chat",
                            "stack": "dev-blue",
                            "tenant": "test",
                        },
                    },
                    "elastic_ingestion_ref_1": {
                        "io_param": {
                            "params": {
                                "es_action": "create",
                                "data_model": "se_elastic_schema.models.tenant.communication.message:Message",  # noqa: E501
                                "file_uri": ndjson_s3_uri[1],
                            },
                        },
                        "workflow": {
                            "trace_id": "trace",
                            "start_timestamp": "2022-07-06T00:00:00",
                            "name": "leapxpert_chat",
                            "stack": "dev-blue",
                            "tenant": "test",
                        },
                    },
                    "elastic_ingestion_ref_2": {
                        "io_param": {
                            "params": {
                                "es_action": "create",
                                "data_model": "se_elastic_schema.models.tenant.communication.message:Message",  # noqa: E501
                                "file_uri": ndjson_s3_uri[2],
                            },
                        },
                        "workflow": {
                            "trace_id": "trace",
                            "start_timestamp": "2022-07-06T00:00:00",
                            "name": "leapxpert_chat",
                            "stack": "dev-blue",
                            "tenant": "test",
                        },
                    },
                },
            },
            MetaModel.CHAT_EVENT: {
                "dynamicTasks": [
                    {
                        "taskReferenceName": "elastic_ingestion_ref_0",
                        "type": "SUB_WORKFLOW",
                        "subWorkflowParam": {"name": "elastic_ingestion"},
                    },
                    {
                        "taskReferenceName": "elastic_ingestion_ref_1",
                        "type": "SUB_WORKFLOW",
                        "subWorkflowParam": {"name": "elastic_ingestion"},
                    },
                    {
                        "taskReferenceName": "elastic_ingestion_ref_2",
                        "type": "SUB_WORKFLOW",
                        "subWorkflowParam": {"name": "elastic_ingestion"},
                    },
                ],
                "dynamicTaskInputs": {
                    "elastic_ingestion_ref_0": {
                        "io_param": {
                            "params": {
                                "es_action": "create",
                                "data_model": "se_elastic_schema.models.tenant.communication.chat_event:ChatEvent",  # noqa: E501
                                "file_uri": ndjson_s3_uri[3],
                            }
                        },
                        "workflow": {
                            "trace_id": "trace",
                            "start_timestamp": "2022-07-06T00:00:00",
                            "name": "leapxpert_chat",
                            "stack": "dev-blue",
                            "tenant": "test",
                        },
                    },
                    "elastic_ingestion_ref_1": {
                        "io_param": {
                            "params": {
                                "es_action": "create",
                                "data_model": "se_elastic_schema.models.tenant.communication.chat_event:ChatEvent",  # noqa: E501
                                "file_uri": ndjson_s3_uri[4],
                            }
                        },
                        "workflow": {
                            "trace_id": "trace",
                            "start_timestamp": "2022-07-06T00:00:00",
                            "name": "leapxpert_chat",
                            "stack": "dev-blue",
                            "tenant": "test",
                        },
                    },
                    "elastic_ingestion_ref_2": {
                        "io_param": {
                            "params": {
                                "es_action": "create",
                                "data_model": "se_elastic_schema.models.tenant.communication.chat_event:ChatEvent",  # noqa: E501
                                "file_uri": ndjson_s3_uri[5],
                            }
                        },
                        "workflow": {
                            "trace_id": "trace",
                            "start_timestamp": "2022-07-06T00:00:00",
                            "name": "leapxpert_chat",
                            "stack": "dev-blue",
                            "tenant": "test",
                        },
                    },
                },
            },
        }

    @mock_aws
    @freeze_time(time_to_freeze="2022-07-06 06:59:38.000000+00:00")
    def test_it_can_run_end_to_end_with_empty_input(
        self,
        sample_aries_task_input: AriesTaskInput,
        link_participants_scroll_result: List[dict],
    ):
        sample_aries_task_input.input_param.params["file_uri"] = (
            "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/leapxpert_chat/2023/04/19/KyteBroking-20230801000000.zip"  # noqa: E501
        )

        # Create mock S3 bucket and objects to it
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME)

        aries_task_result = self._run_aries_task(
            sample_aries_task_input=sample_aries_task_input,
            link_participants_scroll_result=link_participants_scroll_result,
        )

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "leapxpert_chat"
            ]["leapxpert_chat_transform"]
            if aries_task_result.app_metric
            else {}
        )

        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.ERRORED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.SKIPPED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.DUPLICATE_COUNT] == 0

        assert aries_task_result.output_param.params == {
            MetaModel.MESSAGE: {"dynamicTaskInputs": {}, "dynamicTasks": []},
            MetaModel.CHAT_EVENT: {"dynamicTaskInputs": {}, "dynamicTasks": []},
        }

    @staticmethod
    @patch.object(link_participants, "get_repository_by_cluster_version")
    @patch.object(link_participants, "get_es_config")
    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {
                    "lake_prefix": f"s3://{BUCKET_NAME}",
                },
                "workflow": {"streamed": False},
            },
        ),
    )
    @patch("integration_wrapper.integration_aries_task.write_named_temporary_json")
    def _run_aries_task(
        write_named_temporary_json_mock,
        _mock_cached_tenant_workflow_api_client,
        mock_link_participants_get_es_config,
        mock_link_participants_elasticsearch_repository,
        link_participants_scroll_result: dict,
        sample_aries_task_input: AriesTaskInput,
    ):
        """Runs the flow after uploading the input files to mock S3 and mocking
        functions which read from Elasticsearch.

        :param mock_link_participants_get_es_config: Unittest patch object
        :param mock_link_participants_elasticsearch_repository: Unittest patch object
        :param link_participants_scroll_result: Mock LinkParticipants scroll result
        :return:
        """
        aries_task_input = sample_aries_task_input

        # replace write_named_temporary_json side effect
        write_named_temporary_json_mock.side_effect = write_named_temporary_json_side_effect

        # Mocks for link_participants elastic config
        mock_link_participants_get_es_config.return_value = ResourceConfig(
            host="localhost",
            port=9200,
            scheme="http",
            meta_prefix=MetaPrefix.AMPERSAND,
        )

        # Mocks link participants elastic repository
        es_obj = mock_link_participants_elasticsearch_repository.return_value
        es_obj.scroll.return_value = link_participants_scroll_result
        es_obj.MAX_TERMS_SIZE = 1024
        es_obj.meta.prefix = MetaPrefix.AMPERSAND
        es_obj.meta.key = "&key"
        es_obj.meta.id = "&id"

        # Run flow
        return leapxpert_chat_transform_run(aries_task_input=aries_task_input)


def create_and_add_objects_to_s3_bucket(bucket_name: str):
    """Recreate the s3 bucket, and copy all the files from `LOCAL_BUCKET_PATH`
    to s3 mocked bucket.

    :param bucket_name: Bucket name of Mock S3 bucket
    :return: None
    """

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")
    s3.create_bucket(Bucket=bucket_name)

    for file_ in LOCAL_BUCKET_PATH.rglob("*"):
        if file_.is_file():
            _path = file_.as_posix().replace(f"{LOCAL_BUCKET_PATH}/", "")

            with open(file_, "rb") as f:
                s3.put_object(Bucket=bucket_name, Key=_path, Body=f.read())


def write_named_temporary_json_side_effect(output_filename: str, **kwargs) -> str:
    if output_filename == "audit.json":
        with fsspec.open(AUDIT_PATH.as_posix(), "w") as file:
            json.dump({}, file)

        return AUDIT_PATH.as_posix()

    return write_named_temporary_json(output_filename=output_filename, **kwargs)


def remove_date_for_attachments(lst: list[dict]) -> list:
    """Remove date from attachments.

    This is required because the date is not deterministic.
    """
    if isinstance(lst, list):
        for idx, _ in enumerate(lst):
            lst[idx]["fileInfo"].pop("lastModified")

    return lst

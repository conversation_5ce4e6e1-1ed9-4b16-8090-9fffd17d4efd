# ruff: noqa: E501
from typing import Callable


def resolve_task_func(task_name: str) -> Callable:
    match task_name:
        # Voice
        case "a1_voice_transform":
            from integration_audio_comms_tasks.voice.a1_voice_transform.a1_voice_transform_task import (
                a1_voice_transform_run,
            )

            return a1_voice_transform_run
        case "asc_voice_transform":
            from integration_audio_comms_tasks.voice.asc_voice_transform.asc_voice_transform_task import (
                asc_voice_transform_run,
            )

            return asc_voice_transform_run
        case "avaya_voice_transform":
            from integration_audio_comms_tasks.voice.avaya_voice_transform.avaya_voice_transform_task import (
                avaya_voice_transform_run,
            )

            return avaya_voice_transform_run
        case "bp_asc_teams_voice_transform":
            from integration_audio_comms_tasks.voice.bp_asc_teams_voice_transform.bp_asc_teams_voice_transform_task import (
                bp_asc_teams_voice_transform_run,
            )

            return bp_asc_teams_voice_transform_run
        case "bp_cloud9_voice_transform":
            from integration_audio_comms_tasks.voice.bp_cloud9_voice_transform.bp_cloud9_voice_transform_task import (
                bp_cloud9_voice_transform_run,
            )

            return bp_cloud9_voice_transform_run
        case "bp_dubber_voice_transform":
            from integration_audio_comms_tasks.voice.bp_dubber_voice_transform.bp_dubber_voice_transform_task import (
                bp_dubber_voice_transform_run,
            )

            return bp_dubber_voice_transform_run
        case "bp_twilio_voice_transform":
            from integration_audio_comms_tasks.voice.bp_twilio_voice_transform.bp_twilio_voice_transform_task import (
                bp_twilio_voice_transform_run,
            )

            return bp_twilio_voice_transform_run
        case "bp_vodafone_voice_transform":
            from integration_audio_comms_tasks.voice.bp_vodafone_voice_transform.bp_vodafone_voice_transform_task import (
                bp_vodafone_voice_transform_run,
            )

            return bp_vodafone_voice_transform_run
        case "call_cabinet_voice_transform":
            from integration_audio_comms_tasks.voice.call_cabinet_feed_voice_transform.call_cabinet_feed_voice_transform_task import (
                call_cabinet_feed_voice_transform_run,
            )

            return call_cabinet_feed_voice_transform_run
        case "clarifygo_oi_voice_transform":
            from integration_audio_comms_tasks.voice.clarifygo_oi_voice_transform.clarifygo_oi_voice_transform_task import (
                clarifygo_oi_voice_transform_run,
            )

            return clarifygo_oi_voice_transform_run
        case "cloud9_voice_transform":
            from integration_audio_comms_tasks.voice.cloud9_voice_transform.cloud9_voice_transform_task import (
                cloud9_voice_transform_run,
            )

            return cloud9_voice_transform_run
        case "commpeak_voice_transform":
            from integration_audio_comms_tasks.voice.commpeak_voice_transform.commpeak_voice_transform_task import (
                commpeak_voice_transform_run,
            )

            return commpeak_voice_transform_run
        case "dubber_voice_transform":
            from integration_audio_comms_tasks.voice.dubber_voice_transform.dubber_voice_transform_task import (
                dubber_voice_transform_run,
            )

            return dubber_voice_transform_run
        case "focus_horizon_voice_transform":
            from integration_audio_comms_tasks.voice.focus_horizon_voice_transform.focus_horizon_voice_transform_task import (
                focus_horizon_voice_transform_run,
            )

            return focus_horizon_voice_transform_run
        case "gamma_horizon_voice_transform":
            from integration_audio_comms_tasks.voice.gamma_horizon_voice_transform.gamma_horizon_voice_transform_task import (
                gamma_horizon_voice_transform_run,
            )

            return gamma_horizon_voice_transform_run
        case "iv_kerv_voice_transform":
            from integration_audio_comms_tasks.voice.iv_kerv_voice_transform.iv_kerv_voice_transform_task import (
                iv_kerv_voice_transform_run,
            )

            return iv_kerv_voice_transform_run
        case "iv_redbox_voice_transform":
            from integration_audio_comms_tasks.voice.iv_redbox_voice_transform.iv_redbox_voice_transform_task import (
                iv_redbox_voice_transform_run,
            )

            return iv_redbox_voice_transform_run
        case "iv_universal_voice_transform":
            from integration_audio_comms_tasks.voice.iv_universal_voice_transform.iv_universal_voice_transform_task import (
                iv_universal_voice_transform_run,
            )

            return iv_universal_voice_transform_run
        case "kerv_voice_transform":
            from integration_audio_comms_tasks.voice.kerv_voice_transform.kerv_voice_transform_task import (
                kerv_voice_transform_run,
            )

            return kerv_voice_transform_run
        case "luna_advance_voice_transform":
            from integration_audio_comms_tasks.voice.luna_advance_voice_transform.luna_advance_voice_transform_task import (
                luna_advance_voice_transform_run,
            )

            return luna_advance_voice_transform_run
        case "masergy_voice_transform":
            from integration_audio_comms_tasks.voice.masergy_voice_transform.masergy_voice_transform_task import (
                masergy_voice_transform_run,
            )

            return masergy_voice_transform_run
        case "natterbox_voice_transform":
            from integration_audio_comms_tasks.voice.natterbox_voice_transform.natterbox_voice_transform_task import (
                natterbox_voice_transform_run,
            )

            return natterbox_voice_transform_run
        case "nice_voice_transform":
            from integration_audio_comms_tasks.voice.nice_voice_transform.nice_voice_transform_task import (
                nice_voice_transform_run,
            )

            return nice_voice_transform_run
        case "numonix_ix_cloud_voice_transform":
            from integration_audio_comms_tasks.voice.numonix_ix_cloud_voice_transform.numonix_ix_cloud_voice_transform_task import (
                numonix_ix_cloud_voice_transform_run,
            )

            return numonix_ix_cloud_voice_transform_run
        case "o2_voice_transform":
            from integration_audio_comms_tasks.voice.o2_voice_transform.o2_voice_transform_task import (
                o2_voice_transform_run,
            )

            return o2_voice_transform_run
        case "onsim_voice_transform":
            from integration_audio_comms_tasks.voice.onsim_voice_transform.onsim_voice_transform_task import (
                onsim_voice_transform_run,
            )

            return onsim_voice_transform_run
        case "primodialler_voice_transform":
            from integration_audio_comms_tasks.voice.primodialler_voice_transform.onsim_voice_transform_task import (
                primodialler_voice_transform_run,
            )

            return primodialler_voice_transform_run
        case "redbox_voice_transform":
            from integration_audio_comms_tasks.voice.redbox_voice_transform.redbox_voice_transform_task import (
                redbox_voice_transform_run,
            )

            return redbox_voice_transform_run
        case "ringcentral_voice_transform":
            from integration_audio_comms_tasks.voice.ringcentral_voice_transform.ringcentral_voice_transform_task import (
                ringcentral_voice_transform_run,
            )

            return ringcentral_voice_transform_run
        case "santander_voice_transform":
            from integration_audio_comms_tasks.voice.santander_voice_transform.santander_voice_transform_task import (
                santander_voice_transform_run,
            )

            return santander_voice_transform_run
        case "sgc_voice_transform":
            from integration_audio_comms_tasks.voice.sgc_voice_transform.sgc_voice_transform_task import (
                sgc_voice_transform_run,
            )

            return sgc_voice_transform_run
        case "vmo2_voice_transform":
            from integration_audio_comms_tasks.voice.vmo2_voice_transform.vmo2_voice_transform_task import (
                vmo2_voice_transform_run,
            )

            return vmo2_voice_transform_run
        case "steeleye_universal_voice_tf":
            from integration_audio_comms_tasks.voice.steeleye_universal_voice_transform.steeleye_universal_voice_transform_task import (
                steeleye_universal_voice_transform_run,
            )

            return steeleye_universal_voice_transform_run
        case "teleware_voice_transform":
            from integration_audio_comms_tasks.voice.teleware_voice_transform.teleware_voice_transform_task import (
                teleware_voice_transform_run,
            )

            return teleware_voice_transform_run
        case "the_comms_guys_voice_transform":
            from integration_audio_comms_tasks.voice.the_comms_guys_voice_transform.the_comms_guys_voice_transform_task import (
                the_comms_guys_voice_transform_run,
            )

            return the_comms_guys_voice_transform_run
        case "verba_voice_transform":
            from integration_audio_comms_tasks.voice.verba_voice_transform.verba_voice_transform_task import (
                verba_voice_transform_run,
            )

            return verba_voice_transform_run
        case "via_voice_transform":
            from integration_audio_comms_tasks.voice.via_voice_transform.via_voice_transform_task import (
                via_voice_transform_run,
            )

            return via_voice_transform_run
        case "vibework_voice_transform":
            from integration_audio_comms_tasks.voice.vibework_tollring_voice_transform.vibework_tollring_voice_transform_task import (
                vibework_tollring_voice_transform_run,
            )

            return vibework_tollring_voice_transform_run
        case "voip_voice_transform":
            from integration_audio_comms_tasks.voice.voip_voice_transform.voip_voice_transform_task import (
                voip_voice_transform_run,
            )

            return voip_voice_transform_run
        case "waveform_transform":
            from integration_audio_comms_tasks.voice.waveform_transform.waveform_transform_task import (
                waveform_transform_run,
            )

            return waveform_transform_run
        case "wavenet_voice_transform":
            from integration_audio_comms_tasks.voice.wavenet_voice_transform.wavenet_voice_transform_task import (
                wavenet_voice_transform_run,
            )

            return wavenet_voice_transform_run
        case "xima_voice_transform":
            from integration_audio_comms_tasks.voice.xima_voice_transform.xima_voice_transform_task import (
                xima_voice_transform_run,
            )

            return xima_voice_transform_run
        case "zoom_phone_voice_transform":
            from integration_audio_comms_tasks.voice.zoom_phone_voice_transform.zoom_phone_voice_transform_task import (
                zoom_phone_voice_transform_run,
            )

            return zoom_phone_voice_transform_run
        case "zoom_trium_voice_transform":
            from integration_audio_comms_tasks.voice.zoom_trium_voice_transform.zoom_trium_voice_transform_task import (
                zoom_trium_voice_transform_run,
            )

            return zoom_trium_voice_transform_run

        # Meeting
        case "outlook_calendar_meetings":
            from integration_audio_comms_tasks.meeting.outlook_calendar_meeting_transform.outlook_calendar_meeting_transform_task import (
                outlook_calendar_meeting_transform_run,
            )

            return outlook_calendar_meeting_transform_run
        case "zoom_meetings_transform":
            from integration_audio_comms_tasks.meeting.zoom_meetings_transform.zoom_meetings_transform_task import (
                zoom_meetings_transform_run,
            )

            return zoom_meetings_transform_run

        # Transcription
        case "aries_transcription_copilot":
            from integration_audio_comms_tasks.transcription.transcription_copilot.transcription_copilot_task import (
                transcription_copilot_run,
            )

            return transcription_copilot_run

        case "aries_deepgram_api":
            from integration_audio_comms_tasks.transcription.deepgram_api.deepgram_api_task import (
                deepgram_api_run,
            )

            return deepgram_api_run

        case "aries_deepgram_feed":
            from integration_audio_comms_tasks.transcription.deepgram_feed.deepgram_feed_task import (
                deepgram_feed_run,
            )

            return deepgram_feed_run

        case "aries_iv_get_transcription":
            from integration_audio_comms_tasks.transcription.iv_get_transcription.iv_get_transcription_task import (
                iv_get_transcription_run,
            )

            return iv_get_transcription_run

        case "aries_iv_submit_transcription":
            from integration_audio_comms_tasks.transcription.iv_submit_transcription.iv_submit_transcription_task import (
                iv_submit_transcription_run,
            )

            return iv_submit_transcription_run

        case "aries_iv_transform_transcription":
            from integration_audio_comms_tasks.transcription.iv_transform_transcription.iv_transform_transcription_task import (
                iv_transform_transcription_run,
            )

            return iv_transform_transcription_run

        # Hybrid
        case "telemessage_transform":
            from integration_audio_comms_tasks.voice.telemessage_transform.telemessage_transform_task import (
                telemessage_transform_run,
            )

            return telemessage_transform_run

        case "three_cx_voice_transform":
            from integration_audio_comms_tasks.voice.three_cx_voice_transform.three_cx_voice_transform_task import (
                three_cx_voice_transform_run,
            )

            return three_cx_voice_transform_run

        case "truphone_transform":
            from integration_audio_comms_tasks.hybrid.truphone_transform.truphone_transform_task import (
                truphone_transform_run,
            )

            return truphone_transform_run

        case "voxsmart_transform":
            from integration_audio_comms_tasks.hybrid.voxsmart_transform.voxsmart_transform_task import (
                voxsmart_transform_run,
            )

            return voxsmart_transform_run

        case _:
            raise ValueError(f"Unrecognized task name: '{task_name}'")

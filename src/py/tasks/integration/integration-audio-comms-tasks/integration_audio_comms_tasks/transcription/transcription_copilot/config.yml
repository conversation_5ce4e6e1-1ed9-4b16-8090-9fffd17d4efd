cloud: ${oc.env:CLOUD,aws}
stack: ${oc.env:STACK,local}
batch_size: ${oc.env:BATCH_SIZE,500}
lookback_days: ${oc.env:LOOKBACK_DAYS,1}
data_platform_config_api_url: ${oc.env:DATA_PLATFORM_CONFIG_API_URL}
prompt:
  system_prompt: "For the below audio transcript please provide the following seven columns as a single JSON object(strictly JSON please). Do not include any other explanation or details:
  - transcriptionSummary: Write a concise and comprehensive summary of the Transcription. The summary should be a single paragraph of human readable text.
  - entities: Please run a NER (Named Entity Recognition) Analysis on the below Transcription. Format your response as a list of dict with columns: label:(string), value:(string), time:(dict of columns start:(numeric), end:(numeric) and duration:(numeric)).
  - topics: Run a Topic Analysis on the below Transcription. And return any Topics as a list of dict with columns: value:(string), time:(dict of columns start:(numeric), end:(numeric) and duration:(numeric)).
  - sentiments: Run a Sentiment Analysis on the below Transcription. Format your response as a dict with columns for sentimentPositivePercentage, sentimentNeutralPercentage, sentimentNegativePercentage, startTime:(numeric), endTime:(numeric), speakerId:(string). Please format your response as a list of sentiments (to denote changing sentiment percentages throughout the Transcription).
  - classifier: Suggest an overall Classification of the below Transcription? I am looking for a single value that Categorises the call so users can filter by this attribute. Format your response as a dict only with columns for classification:(string), confidenceScore:(numeric).
  - questions: What are some key questions you might have given the below call transcription, either compliance related, or general uncertainties given the content? Format your response as a list dict containing fields for: questionCategory, question, importance(uppercase string:HIGH/MEDIUM/LOW) and if applicable, start:(numeric) and end:(numeric) (start and end time that causes the question).
  - complexity: Run a Lexical Diversity Analysis using a Type-Token Ratio method on the below Transcript. Format your response as a dict only containing a single field: score: numeric 0-1
  - risks: Analyze the below Call Transcription for potential clear and explicit Compliance Breaches. You must operate with high recall and high precision. As our AI Chief Compliance Officer you have established a reputation for detecting true positive Alerts, and few false positive Alerts. Do not assume everything has a compliance risk.Format your response as a list of dict with these columns: risk :(string of risk title), riskCategory (a higher level categorisation of the risk, Should be one of these categories - INFORMATION_HANDLING DATA_PRIVACY, INSIDER_INFORMATION, MARKET_MANIPULATION, COMMUNICATION_AND_MISREPRESENTATION, CONFLICT_OF_INTEREST, CONDUCT_RISK, OPERATIONAL_AND_PROCEDURAL), riskSummary (an explanation of the risk, and quote portions of the transcript which give you concern), riskSeverity (a score of integer with range 0-10 with 10 an explicit indication of illegal or non-compliant activity, and 0 being where no risk is detected), startTime (numeric) and endTime (numeric). If you do not see any risks then return and empty list for this field."
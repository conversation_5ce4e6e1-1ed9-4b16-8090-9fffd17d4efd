# type: ignore
import logging
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_audio_comms_tasks.transcription.transcription_copilot.app_metrics_template import (
    APP_METRICS,
)
from integration_audio_comms_tasks.transcription.transcription_copilot.transcription_copilot_flow import (  # noqa E510
    transcription_copilot_flow,
)
from integration_wrapper.integration_aries_task import IntegrationAriesTask

logger = logging.getLogger("transcription_copilot_flow")


def transcription_copilot_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    integration = IntegrationAriesTask(
        aries_task_input=aries_task_input, app_metrics_template=APP_METRICS
    )

    return integration.execute(flow=transcription_copilot_flow)

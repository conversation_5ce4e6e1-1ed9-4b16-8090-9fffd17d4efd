import datetime
import logging
import se_schema_meta
from aries_io_event.io_param import IOParamFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from dateutil import parser
from elasticsearch6 import Elasticsearch as Elasticsearch6
from elasticsearch6.helpers import bulk as bulk_es6
from elasticsearch8 import Elasticsearch as Elasticsearch8
from elasticsearch8.helpers import bulk as bulk_es8
from elasticsearch8.helpers import scan as scan_es8
from pydantic import BaseModel, BaseSettings
from se_elastic_schema.models.efdh import tenant_configuration_efdh
from se_elastic_schema.models.efdh.provenance import (
    sink_audit_efdh,
    sink_file_audit_efdh,
    sink_record_audit_efdh,
    user_audit_efdh,
)
from se_elastic_schema.models.tenant.provenance.sink_audit import SinkAudit
from se_elastic_schema.models.tenant.provenance.sink_file_audit import SinkFileAudit
from se_elastic_schema.models.tenant.provenance.sink_record_audit import SinkRecordAudit
from se_elastic_schema.models.tenant.provenance.user_audit import User<PERSON>udit
from se_elastic_schema.models.tenant.tenant.tenant_configuration import TenantConfiguration
from se_es_utils.utils import map_ids_to_index
from typing import Any


class AuditShipperConfig(BaseSettings):
    elastic_url: str
    elastic_api_key: str
    elastic_ca_cert: str | None = None
    elastic_verify_certs: bool = True
    efdh_elastic_url: str
    efdh_elastic_api_key: str
    debug: bool = False
    elastic_request_timeout_s: int = 900  # 15 minutes
    efdh_elastic_request_timeout_s: int = 900  # 15 minutes
    source_es_scroll_size: int = 1000
    srp_elastic_url: str | None = None
    srp_elastic_request_timeout_s: int = 900  # 15 minutes
    use_efdh: bool = False  # for backwards compatibility


audit_shipper_config = AuditShipperConfig()
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG if audit_shipper_config.debug else logging.INFO)

DEST_ES_BULK_HELPER = (
    bulk_es8 if audit_shipper_config.use_efdh else bulk_es6
)  # Use ES8 bulk helper if using EFDH, otherwise use ES6


class IndexTargetInfo(BaseModel):
    name: str
    source_alias_pattern: str
    destination_write_alias: str
    tenant_attribute: str
    timestamp_field: str


class AuditShipperParams(BaseModel):
    from_date: datetime.date | None = None
    to_date: datetime.date | None = None


EFDH_TENANT = "efdh"  # EFDH tenant for EFDH-specific indices
INDEX_TARGETS = [
    IndexTargetInfo(
        name="UserAudit",
        source_alias_pattern=UserAudit.get_elastic_index_alias(tenant="*"),
        destination_write_alias=".userAudit"
        if not audit_shipper_config.use_efdh
        else user_audit_efdh.UserAudit.get_elastic_index_alias(tenant=EFDH_TENANT),
        tenant_attribute="realm",
        timestamp_field="timestamp",
    ),
    IndexTargetInfo(
        name="SinkFileAudit",
        source_alias_pattern=SinkFileAudit.get_elastic_index_alias(tenant="*"),
        destination_write_alias=".sinkFileAudit"
        if not audit_shipper_config.use_efdh
        else sink_file_audit_efdh.SinkFileAudit.get_elastic_index_alias(tenant=EFDH_TENANT),
        tenant_attribute="bucket",
        timestamp_field=se_schema_meta.TIMESTAMP,
    ),
    IndexTargetInfo(
        name="SinkRecordAudit",
        source_alias_pattern=SinkRecordAudit.get_elastic_index_alias(tenant="*"),
        destination_write_alias=".sinkRecordAudit"
        if not audit_shipper_config.use_efdh
        else sink_record_audit_efdh.SinkRecordAudit.get_elastic_index_alias(tenant=EFDH_TENANT),
        tenant_attribute="bucket",
        timestamp_field=se_schema_meta.TIMESTAMP,
    ),
    IndexTargetInfo(
        name="SinkAudit",
        source_alias_pattern=SinkAudit.get_elastic_index_alias(tenant="*"),
        destination_write_alias=".sinkAudit"
        if not audit_shipper_config.use_efdh
        else sink_audit_efdh.SinkAudit.get_elastic_index_alias(tenant=EFDH_TENANT),
        tenant_attribute="tenant",
        timestamp_field=se_schema_meta.TIMESTAMP,
    ),
    IndexTargetInfo(
        name="TenantConfiguration",
        source_alias_pattern=TenantConfiguration.get_elastic_index_alias(tenant="*"),
        destination_write_alias=".tenantConfiguration"
        if not audit_shipper_config.use_efdh
        else tenant_configuration_efdh.TenantConfiguration.get_elastic_index_alias(
            tenant=EFDH_TENANT
        ),
        tenant_attribute="tenantId",
        timestamp_field=se_schema_meta.TIMESTAMP,
    ),
]


def convert_user_audit_timestamp(doc: dict[str, Any]) -> dict[str, Any]:
    try:
        # Convert the user access audit timestamp to epoch milliseconds
        doc["timestamp"] = int(parser.parse(doc["timestamp"]).timestamp() * 1000)
        return doc
    except Exception as e:
        logger.error("Error while converting timestamp, falling back to `&timestamp`: %s", e)
        doc["timestamp"] = doc["&timestamp"]
        return doc


def ship_audit(
    source_cell_es_client: Elasticsearch8,
    dest_reference_es_client: Elasticsearch6 | Elasticsearch8,
    timestamp_range: dict,
    index_target_info: IndexTargetInfo,
    source_index: str,
) -> tuple[int, int, int, int]:
    if index_target_info.name == "TenantConfiguration":
        # TenantConfiguration does not have an expiry field, so we do not filter by expiry
        query = {
            "bool": {"must": [{"bool": {"must_not": {"exists": {"field": se_schema_meta.EXPIRY}}}}]}
        }
    else:
        query = {
            "bool": {
                "must_not": {  # type: ignore
                    "exists": {"field": se_schema_meta.EXPIRY},
                },
                "filter": {  # type: ignore
                    "range": {
                        index_target_info.timestamp_field: timestamp_range,
                    }
                },
            },
        }

    destination_records_ops = []
    total_shipped = 0
    total_errors = 0
    total_source_count = 0
    total_updated = 0

    def _do_bulk_migrate(es_bulk_ops: list[dict]) -> tuple[int, int, int]:
        updated = 0
        logger.info(
            f"Alias: {index_target_info.source_alias_pattern}, Index: {source_index} -- "
            f"Shipping {len(es_bulk_ops)} records to {index_target_info.destination_write_alias}",
        )

        if audit_shipper_config.use_efdh:
            # ILM handling for EFDH
            # set the index for each op based on existence of record in ES. Existing records may be
            # present on an index, which is rolled over and currently not pointed by the write alias
            ids = [destination_records_op["_id"] for destination_records_op in es_bulk_ops]
            ids_to_index_map = map_ids_to_index(
                es_client=dest_reference_es_client,
                ids=ids,
                alias=index_target_info.destination_write_alias,
            )
            for destination_record_op in es_bulk_ops:
                # if the record is not present in the map, it means it is a new record and should be
                # written to the destination write-alias
                if destination_record_op["_id"] in ids_to_index_map:
                    destination_record_op["_index"] = ids_to_index_map[destination_record_op["_id"]]
                    logger.debug(
                        f"[EXISTING RECORD] {destination_record_op['_id']} --> "
                        f"{destination_record_op['_index']}"
                    )
                    updated += 1
        shipped, errors = DEST_ES_BULK_HELPER(
            dest_reference_es_client,
            es_bulk_ops,
            refresh="true",
            raise_on_error=False,
        )
        if errors:
            logger.error(f"Error while shipping data: {errors}")
        return shipped, len(errors or []), updated

    for record in scan_es8(
        source_cell_es_client,
        index=source_index,
        query={"query": query},
        size=audit_shipper_config.source_es_scroll_size,
    ):
        record_source = record["_source"]
        record_id = f"{record_source[index_target_info.tenant_attribute]}:{record['_id']}"
        if index_target_info.name == "UserAudit":
            # For UserAudit, convert the timestamp to epoch milliseconds
            record_source = convert_user_audit_timestamp(record_source)
        # update meta_id with doc ID
        record_source[se_schema_meta.ID] = record_id
        destination_record_op = {
            "_index": index_target_info.destination_write_alias,
            "_op_type": "index",
            "_id": record_id,
            "_source": record_source,
        }
        if not audit_shipper_config.use_efdh:
            # pre EFDH, SRP cluster with ES5 requires _type to be set
            destination_record_op["_type"] = record_source[se_schema_meta.MODEL]
        destination_records_ops.append(destination_record_op)
        total_source_count += 1

        if len(destination_records_ops) >= audit_shipper_config.source_es_scroll_size:
            shipped, errors, updated = _do_bulk_migrate(destination_records_ops)
            total_shipped += shipped
            total_errors += errors
            total_updated += updated
            destination_records_ops = []

    if destination_records_ops:
        # write remaining records
        shipped, errors, updated = _do_bulk_migrate(destination_records_ops)
        total_shipped += shipped
        total_errors += errors
        total_updated += updated

    logger.info(
        f"Index: {source_index}. Total shipped: {total_shipped}, "
        f"Total errors: {total_errors}, Total updated: {total_updated}, "
        f"Total records: {total_source_count}"
    )
    return total_shipped, total_errors, total_source_count, total_updated


def audit_shipper_task_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    audit_shipper_params = AuditShipperParams(**aries_task_input.input_param.params)

    if all([audit_shipper_params.from_date, audit_shipper_params.to_date]):
        if audit_shipper_params.from_date > audit_shipper_params.to_date:
            raise ValueError("'from_date' should be less than 'to_date'")
        start_ts_ms = int(
            datetime.datetime.combine(
                audit_shipper_params.from_date, datetime.datetime.min.time()
            ).timestamp()
            * 1000
        )
        end_ts_ms = int(
            datetime.datetime.combine(
                audit_shipper_params.to_date, datetime.datetime.min.time()
            ).timestamp()
            * 1000
        )
    elif any([audit_shipper_params.from_date, audit_shipper_params.to_date]):
        raise ValueError("Both 'from_date' and 'to_date' must be provided together")
    else:
        logger.info("No date range provided. Defaulting to yesterday")
        now = datetime.datetime.now(tz=datetime.timezone.utc)
        start_ts_ms = int(
            (now - datetime.timedelta(days=1))
            .replace(
                hour=0,
                minute=0,
                second=0,
                microsecond=0,
            )
            .timestamp()
            * 1000
        )
        end_ts_ms = int(now.replace(hour=0, minute=0, second=0, microsecond=0).timestamp() * 1000)

    logger.info(
        f"Using date range: "
        f"{datetime.datetime.fromtimestamp(start_ts_ms / 1000, tz=datetime.timezone.utc)} - "
        f"{datetime.datetime.fromtimestamp(end_ts_ms / 1000, tz=datetime.timezone.utc)}"
    )

    source_cell_es_client = Elasticsearch8(
        audit_shipper_config.elastic_url,
        api_key=audit_shipper_config.elastic_api_key,
        ca_certs=audit_shipper_config.elastic_ca_cert,
        verify_certs=audit_shipper_config.elastic_verify_certs,
        ssl_show_warn=audit_shipper_config.elastic_verify_certs,
        request_timeout=audit_shipper_config.elastic_request_timeout_s,
    )
    if audit_shipper_config.use_efdh:
        dest_reference_es_client = Elasticsearch8(
            hosts=audit_shipper_config.efdh_elastic_url,
            api_key=audit_shipper_config.efdh_elastic_api_key,
            request_timeout=audit_shipper_config.efdh_elastic_request_timeout_s,
        )
    else:
        dest_reference_es_client = Elasticsearch6(audit_shipper_config.srp_elastic_url)
    results = dict()
    for index_target in INDEX_TARGETS:
        total_shipped = total_errors = total_source_count = total_updated = 0
        logger.info(f"Processing index target: {index_target.name}")
        source_indices = source_cell_es_client.indices.get_alias(
            name=index_target.source_alias_pattern
        ).keys()
        if not source_indices:
            logger.warning(
                f"No source indices found for alias pattern: {index_target.source_alias_pattern}"
            )
            continue
        for source_index in source_indices:
            shipped, errors, source_count, updated = ship_audit(
                source_cell_es_client=source_cell_es_client,
                dest_reference_es_client=dest_reference_es_client,
                index_target_info=index_target,
                timestamp_range={"gte": start_ts_ms, "lte": end_ts_ms},
                source_index=source_index,
            )
            total_shipped += shipped
            total_errors += errors
            total_source_count += source_count
            total_updated += updated
        logger.info(
            f"Finished processing index target: {index_target.name} with results: "
            f"total_shipped={total_shipped}, total_errors={total_errors}, "
            f"total_source_count={total_source_count}, total_updated={total_updated}"
        )
        results[index_target.source_alias_pattern] = {
            "total_shipped": total_shipped,
            "total_errors": total_errors,
            "total_source_count": total_source_count,
            "total_updated": total_updated,
        }

    return AriesTaskResult(output_param=IOParamFieldSet(params=results), app_metric=None)

# ruff: noqa: E501
import addict
import json
import pytest
import random
import shutil
from aries_task_link.models import AriesTaskInput
from copilot_utils.static import OpenAIResponse
from intelligence_core_tasks.comms_neural.schema import SeverityType
from openai.types import CompletionUsage
from openai.types.chat import Chat<PERSON>ompletion, ChatCompletionMessage
from openai.types.chat.chat_completion import Choice
from pathlib import Path
from se_elastic_schema.models import SurveillanceWatch

random.seed(42)  # Set the seed for reproducibility


@pytest.fixture(autouse=True)
def env_setup(monkeypatch):
    monkeypatch.setenv("DATA_PLATFORM_CONFIG_API_URL", "")
    monkeypatch.setenv("ELASTIC_API_KEY", "")
    monkeypatch.setenv("ELASTIC_HOST", "localhost")
    monkeypatch.setenv("OPENAI_API_BASE", "")
    monkeypatch.setenv("OPENAI_API_KEY", "")
    monkeypatch.setenv("OPENAI_API_MODEL", "")
    monkeypatch.setenv("OPENAI_API_VERSION", "")
    monkeypatch.setenv("PERFORM_OUTPUT_CACHE_RESOLUTION", "False")
    monkeypatch.setenv("PERFORM_LLM_IO_AUDIT", "False")


@pytest.fixture(autouse=True)
def cleanup_results():
    yield
    for result_dir in ["aries", "pinguim.steeleye.co"]:
        dir_path = Path.cwd() / result_dir
        if dir_path.exists() and dir_path.is_dir():
            shutil.rmtree(dir_path)


@pytest.fixture
def neural_task_input():
    return AriesTaskInput.validate(
        {
            "workflow": {
                "start_timestamp": "1685806839",
                "name": "comms-neural-assessment",
                "stack": "prod-local",
                "tenant": "local",
            },
            "task": {
                "name": "comms-neural-assessment",
                "version": "",
                "success": True,
            },
            "input_param": {
                "params": {
                    "tenants": ["pinguim"],
                }
            },
        }
    )


@pytest.fixture
def message_expected_assessment():
    return {
        "document": {
            "&model": "MessageGroup",
            "analytics": {
                "languageDetection": {
                    "languagesDetected": [{"code": "en", "name": "English"}],
                    "numberOfLanguages": 1,
                    "isMultiLingual": False,
                    "languages": [{"start": 0, "end": 0}],
                }
            },
            "ids": [
                "16ec850e5d4c2049b1e934603edad0af38ce96def2acb59f5efa77e8af73b6512f2ae8ed223a2b84f409acf3a42170efe179297f076c4713db25c94e9b244178",
                "16ec850e5d4c2049b1e934603edad0af38ce96def2acb59f5efa77e8af73b6512f2ae8ed223a2b84f409acf3a42170efe179297f076c4713db25c94e9b244178",
            ],
            "metadata": {"source": {"client": "WhatsApp (Telemessage)"}},
            "participants": [
                {
                    "types": ["TO"],
                    "value": {
                        "&id": "299d7eb6-1eca-43d5-a7fb-fde74ee45293",
                        "&key": "AccountPerson:299d7eb6-1eca-43d5-a7fb-fde74ee45293:*************",
                        "communications": {
                            "emails": ["<EMAIL>"],
                            "phoneNumbers": [
                                {"dialingCode": "ZA", "label": "MOBILE", "number": "+***********"}
                            ],
                        },
                        "counterparty": {"name": "South Africa"},
                        "name": "Pingu Ping",
                        "personalDetails": {
                            "firstName": "Pingu",
                            "lastName": "Ping",
                            "dob": "1994-07-13",
                        },
                        "retailOrProfessional": "N/A",
                        "sourceIndex": "107",
                        "sourceKey": "s3://x.uat.steeleye.co/flows/mymarket-universal-steeleye-person/x.csv",
                        "structure": {"department": "x", "role": "x"},
                        "uniqueIds": ["<EMAIL>", "+***********"],
                    },
                }
            ],
            "roomId": "+28527106999 28527106999",
            "roomName": "+28527106999 28527106999",
            "stats": {"participantsCount": 1},
            "timestamps": {
                "timestampEnd": "2024-09-29T12:52:31Z",
                "timestampStart": "2024-09-29T12:52:31Z",
            },
        },
        "assessments": {
            "l1": {
                "assessment": "Sensitive data.",
                "behaviour": "Risk",
                "dateTime": "2024-10-15T12:00:01Z",
                "id": "771d9f34",
                "modelID": "",
                "recommendations": "Report to IT.",
                "severityScore": 9.5,
                "severityScoreWeighted": 9.5,
                "sourceKeyPrompt": "sample_path",
                "sourceKeyPromptResponse": "sample_path",
                "threadID": "003e03cd",
                "triggers": [{"endChar": 1, "startChar": 0, "trigger": ""}],
            },
            "l2": {
                "assessment": "Sensitive data.",
                "behaviour": "Risk",
                "dateTime": "2024-10-15T12:00:01Z",
                "id": "771d9f34",
                "modelID": "",
                "recommendations": "Report to IT.",
                "severityScore": 9.5,
                "severityScoreWeighted": 9.5,
                "sourceKeyPrompt": "sample_path",
                "sourceKeyPromptResponse": "sample_path",
                "threadID": "003e03cd",
                "triggers": [{"endChar": 1, "startChar": 0, "trigger": ""}],
            },
            "l3": {
                "assessment": "Sensitive data.",
                "behaviour": "Risk",
                "dateTime": "2024-10-15T12:00:01Z",
                "id": "771d9f34",
                "modelID": "",
                "recommendations": "Report to IT.",
                "severityScore": 9.5,
                "severityScoreWeighted": 9.5,
                "sourceKeyPrompt": "sample_path",
                "sourceKeyPromptResponse": "sample_path",
                "threadID": "003e03cd",
                "triggers": [{"endChar": 1, "startChar": 0, "trigger": ""}],
            },
        },
    }


@pytest.fixture
def email_expected_assessment():
    return {
        "document": {
            "&id": "762",
            "&model": "email",
            "body": {
                "text": "Your details have been compromised in the data breach at National Bank. Please provide you credit card details to verify your identity 762."
            },
            "subject": "Urgent Action Needed!",
            "metadata": {"header": {"from": "<EMAIL>"}},
            "identifiers": {
                "fromId": "<EMAIL>",
                "toIds": ["<EMAIL>"],
            },
            "participants": [
                {
                    "types": ["TO"],
                    "value": {
                        "communications": {"emails": ["<EMAIL>"]},
                        "name": "Mary Jane",
                    },
                },
                {
                    "types": ["FROM"],
                    "value": {
                        "communications": {"emails": ["<EMAIL>"]},
                        "name": "John Doe",
                    },
                },
            ],
        },
        "assessments": {
            "l1": {
                "id": "1f13428d",
                "dateTime": "2024-10-15T12:00:01Z",
                "modelID": "",
                "threadID": "a4cd0ea3",
                "sourceKeyPrompt": "sample_path",
                "sourceKeyPromptResponse": "sample_path",
                "assessment": "Sensitive data.",
                "behaviour": "Risk",
                "recommendations": "Report to IT.",
                "severityScore": 7.5,
                "severityScoreWeighted": 7.5,
                "triggers": [{"trigger": "", "startChar": 0, "endChar": 1}],
            },
            "l2": {
                "id": "196090b2",
                "dateTime": "2024-10-15T12:00:01Z",
                "modelID": "",
                "threadID": "a4cd0ea3",
                "sourceKeyPrompt": "sample_path",
                "sourceKeyPromptResponse": "sample_path",
                "assessment": "Sensitive data.",
                "behaviour": "Risk",
                "recommendations": "Report to IT.",
                "severityScore": 9.5,
                "severityScoreWeighted": 8.8,
                "triggers": [{"trigger": "", "startChar": 0, "endChar": 1}],
            },
            "l3": {
                "id": "88b666ce",
                "dateTime": "2024-10-15T12:00:01Z",
                "modelID": "",
                "threadID": "a4cd0ea3",
                "sourceKeyPrompt": "sample_path",
                "sourceKeyPromptResponse": "sample_path",
                "assessment": "Sensitive data.",
                "behaviour": "Risk",
                "recommendations": "Report to IT.",
                "severityScore": 4.0,
                "severityScoreWeighted": 6.8,
                "triggers": [{"trigger": "", "startChar": 0, "endChar": 1}],
            },
        },
    }


@pytest.fixture
def message_room_day_search_result():
    return {
        "aggregations": {
            "by_source": {
                "doc_count_error_upper_bound": 0,
                "sum_other_doc_count": 0,
                "buckets": [
                    {
                        "key": "WhatsApp (Telemessage)",
                        "doc_count": 310,
                        "by_room": {
                            "doc_count_error_upper_bound": 0,
                            "sum_other_doc_count": 0,
                            "buckets": [
                                {
                                    "key": "+28528886999 +28535652999 28528886999 28535652999",
                                    "doc_count": 39,
                                    "by_time": {
                                        "buckets": [
                                            {
                                                "key_as_string": "2024-09-30",
                                                "key": 1727654400000,
                                                "doc_count": 39,
                                            }
                                        ]
                                    },
                                },
                                {
                                    "key": "+28527106999 28527106999",
                                    "doc_count": 25,
                                    "by_time": {
                                        "buckets": [
                                            {
                                                "key_as_string": "2024-09-30",
                                                "key": 1727654400000,
                                                "doc_count": 25,
                                            }
                                        ]
                                    },
                                },
                            ],
                        },
                    }
                ],
            }
        }
    }


def fake_search_after_query_yield(*args, **kwargs):
    if "email" in kwargs.get("index", ""):
        participant_data = [
            {
                "types": ["TO"],
                "value": {
                    "communications": {"emails": ["<EMAIL>"]},
                    "name": "Mary Jane",
                },
            },
            {
                "types": ["FROM"],
                "value": {
                    "communications": {"emails": ["<EMAIL>"]},
                    "name": "John Doe",
                },
            },
        ]
        results = [
            {
                "&id": str(i),
                "&model": "email",
                "body": {
                    "text": (
                        "Your details have been compromised in the data breach at National Bank. "
                        f"Please provide you credit card details to verify your identity {i}."
                    )
                },
                "subject": "Urgent Action Needed!",
                "metadata": {"header": {"from": "<EMAIL>"}},
                "identifiers": {
                    "fromId": "<EMAIL>",
                    "toIds": ["<EMAIL>"],
                },
                "participants": participant_data * 15 if i == 764 else participant_data,
            }
            for i in range(760, 765)
        ]
    else:
        results = [
            {
                "&id": "16ec850e5d4c2049b1e934603edad0af38ce96def2acb59f5efa77e8af73b6512f2ae8ed223a2b84f409acf3a42170efe179297f076c4713db25c94e9b244178",
                "analytics": {
                    "languageDetection": {"languagesDetected": [{"code": "en", "name": "English"}]}
                },
                "body": {
                    "text": "\nMedia file of type image will not be archived since media was not downloaded by user\nThumbnail attached"
                },
                "identifiers": {
                    "allIds": ["+***********", "***********"],
                    "fromId": "+***********",
                    "fromIdAddlInfo": {"raw": "***********", "countryCode": "ZA"},
                },
                "metadata": {"source": {"client": "WhatsApp (Telemessage)"}},
                "participants": [
                    {
                        "types": ["TO"],
                        "value": {
                            "&id": "299d7eb6-1eca-43d5-a7fb-fde74ee45293",
                            "&key": "AccountPerson:299d7eb6-1eca-43d5-a7fb-fde74ee45293:*************",
                            "communications": {
                                "emails": ["<EMAIL>"],
                                "phoneNumbers": [
                                    {
                                        "dialingCode": "ZA",
                                        "label": "MOBILE",
                                        "number": "+***********",
                                    }
                                ],
                            },
                            "counterparty": {"name": "South Africa"},
                            "name": "Pingu Ping",
                            "personalDetails": {
                                "firstName": "Pingu",
                                "lastName": "Ping",
                                "dob": "1994-07-13",
                            },
                            "retailOrProfessional": "N/A",
                            "sourceIndex": "107",
                            "sourceKey": "s3://x.uat.steeleye.co/flows/mymarket-universal-steeleye-person/x.csv",
                            "structure": {"department": "x", "role": "x"},
                            "uniqueIds": ["<EMAIL>", "+***********"],
                        },
                    }
                ],
                "roomId": "+28527106999 28527106999",
                "roomName": "+28527106999 28527106999",
                "timestamps": {
                    "localTimestampStart": "2024-09-29T12:52:31Z",
                    "timestampStart": "2024-09-29T12:52:31Z",
                },
            },
            {
                "&id": "16ec850e5d4c2049b1e934603edad0af38ce96def2acb59f5efa77e8af73b6512f2ae8ed223a2b84f409acf3a42170efe179297f076c4713db25c94e9b244178",
                "analytics": {
                    "languageDetection": {"languagesDetected": [{"code": "en", "name": "English"}]}
                },
                "body": {
                    "text": "\nMedia file of type image will not be archived since media was not downloaded by user\nThumbnail attached"
                },
                "identifiers": {
                    "allIds": ["+27649809999", "27649809999"],
                    "fromId": "+27649809999",
                    "fromIdAddlInfo": {"raw": "27649809999", "countryCode": "ZA"},
                },
                "metadata": {"source": {"client": "WhatsApp (Telemessage)"}},
                "roomId": "+28527106999 28527106999",
                "roomName": "+28527106999 28527106999",
                "timestamps": {
                    "localTimestampStart": "2024-09-29T12:52:31Z",
                    "timestampStart": "2024-09-29T12:52:31Z",
                },
            },
            {
                "&id": "eeab21f73ec12ccd219f07d9b5a9850c423f16ea1ecf59c2024eb510308607c4fd9e842f5c85d828bc8ca4960e2c9b540f51a4350e00446327fbb1f5b837444c",
                "analytics": {
                    "languageDetection": {"languagesDetected": [{"code": "en", "name": "English"}]}
                },
                "body": {
                    "text": "\nMedia file of type image will not be archived since media was not downloaded by user\nThumbnail attached"
                },
                "identifiers": {
                    "allIds": ["+***********", "***********"],
                    "fromId": "+***********",
                    "fromIdAddlInfo": {"raw": "***********", "countryCode": "ZA"},
                },
                "metadata": {"source": {"client": "WhatsApp (Telemessage)"}},
                "roomId": "+28528886999 +28535652999 28528886999 28535652999",
                "roomName": "+28528886999 +28535652999 28528886999 28535652999",
                "timestamps": {
                    "localTimestampStart": "2024-09-29T12:01:14Z",
                    "timestampStart": "2024-09-29T12:01:14Z",
                },
            },
            {
                "&id": "f0a2f28a3293ec7c5e48e4a2627b883221a579de0978928b3b10e46db8f62466d7d81b6c54b5bc5adf23ca90c84a4d972ed40505896484000c6e76079d2bc9e5",
                "analytics": {
                    "languageDetection": {"languagesDetected": [{"code": "en", "name": "English"}]}
                },
                "body": {
                    "text": "\nMedia file of type image will not be archived since media was not downloaded by user\nThumbnail attached"
                },
                "identifiers": {
                    "allIds": ["+27727106694", "27727106694"],
                    "fromId": "+27727106694",
                    "fromIdAddlInfo": {"raw": "27727106694", "countryCode": "ZA"},
                },
                "metadata": {"source": {"client": "WhatsApp (Telemessage)"}},
                "roomId": "+28528886999 +28535652999 28528886999 28535652999",
                "roomName": "+28528886999 +28535652999 28528886999 28535652999",
                "timestamps": {
                    "localTimestampStart": "2024-09-29T19:53:27Z",
                    "timestampStart": "2024-09-29T19:53:27Z",
                },
            },
            {
                "&id": "17fb1a6e48733741c8fd56ab0850ed0fca15010da5c1ad42944448f98b760630f3c93aa8b5b8e8cedbb22139629ed8a0d62fcbfe3cf341b47f9d4ffe2eb8f176",
                "analytics": {
                    "languageDetection": {"languagesDetected": [{"code": "en", "name": "English"}]}
                },
                "body": {
                    "text": "\nMedia file of type image will not be archived since media was not downloaded by user\nThumbnail attached"
                },
                "identifiers": {
                    "allIds": ["+27727106694", "27727106694"],
                    "fromId": "+27727106694",
                    "fromIdAddlInfo": {"raw": "27727106694", "countryCode": "ZA"},
                },
                "metadata": {"source": {"client": "WhatsApp (Telemessage)"}},
                "roomId": "+28528886999 +28535652999 28528886999 28535652999",
                "roomName": "+28528886999 +28535652999 28528886999 28535652999",
                "timestamps": {
                    "localTimestampStart": "2024-09-29T19:53:29Z",
                    "timestampStart": "2024-09-29T19:53:29Z",
                },
            },
            {
                "&id": "a31af73babf53c28a8f821de470a58990535a1d8a123d3b2c20df8016316c2c3992455b0b0df94ebe6871a1b3dc9ef4ea7d6810e2f34ab8e358090507021c94e",
                "analytics": {
                    "languageDetection": {"languagesDetected": [{"code": "en", "name": "English"}]}
                },
                "body": {
                    "text": "\nMedia file of type image will not be archived since media was not downloaded by user\nThumbnail attached"
                },
                "identifiers": {
                    "allIds": ["+27727106694", "27727106694"],
                    "fromId": "+27727106694",
                    "fromIdAddlInfo": {"raw": "27727106694", "countryCode": "ZA"},
                },
                "metadata": {"source": {"client": "WhatsApp (Telemessage)"}},
                "roomId": "+28528886999 +28535652999 28528886999 28535652999",
                "roomName": "+28528886999 +28535652999 28528886999 28535652999",
                "timestamps": {
                    "localTimestampStart": "2024-09-29T19:53:30Z",
                    "timestampStart": "2024-09-29T19:53:30Z",
                },
            },
            ### Bloomberg Filter Tests ###
            {
                # This is filtered for participant count
                "&id": "98bb5fec7816ae250bb0783102d24cd1a62d1bd4e546b3094ec3e9dd6ba11e48bf4ece7bdea5d360bdc95151d48013776c7b856825967a084e4044a9f19802d7",
                "body": {"text": "46k shs BEI GR crossed at 130.85 ($6.6m per side)"},
                "identifiers": {
                    "allIds": [
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                    ],
                    "domains": [
                        {"types": ["FROM", "TO"], "value": "bloomberg.net"},
                        {"types": ["TO"], "value": "envestracapital.com"},
                    ],
                    "fromId": "<EMAIL>",
                    "fromUserId": "<EMAIL>",
                    "localParts": [
                        {"types": ["FROM"], "value": "t"},
                        {"types": ["TO"], "value": "a"},
                        {"types": ["TO"], "value": "d"},
                        {"types": ["TO"], "value": "l"},
                        {"types": ["TO"], "value": "a"},
                        {"types": ["TO"], "value": "m"},
                        {"types": ["TO"], "value": "k"},
                        {"types": ["TO"], "value": "u"},
                        {"types": ["TO"], "value": "r"},
                        {"types": ["TO"], "value": "s"},
                    ],
                    "toIds": [
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                    ],
                },
                "metadata": {"source": {"client": "Instant Bloomberg"}},
                "roomId": "PCHAT-0x10000029AA655",
                "stats": {"participantsCount": 21},
                "timestamps": {
                    "localTimestampStart": "2024-08-06T11:53:36",
                    "timestampStart": "2024-08-06T11:53:36",
                },
            },
            {
                # This is filtered for line count
                "&id": "98bb5fec7816ae250bb0783102d24cd1a62d1bd4e546b3094ec3e9dd6ba11e48bf4ece7bdea5d360bdc95151d48013776c7b856825967a084e4044a9f19802d7",
                "body": {"text": "46k shs BEI GR crossed at 130.85 ($6.6m per side)\n" * 11},
                "identifiers": {
                    "allIds": [
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                    ],
                    "domains": [
                        {"types": ["FROM", "TO"], "value": "bloomberg.net"},
                        {"types": ["TO"], "value": "envestracapital.com"},
                    ],
                    "fromId": "<EMAIL>",
                    "fromUserId": "<EMAIL>",
                    "localParts": [
                        {"types": ["FROM"], "value": "t"},
                        {"types": ["TO"], "value": "a"},
                        {"types": ["TO"], "value": "d"},
                    ],
                    "toIds": [
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                    ],
                },
                "metadata": {"source": {"client": "Instant Bloomberg"}},
                "roomId": "PCHAT-0x10000029AA655",
                "stats": {"participantsCount": 21},
                "timestamps": {
                    "localTimestampStart": "2024-08-06T11:53:36",
                    "timestampStart": "2024-08-06T11:53:36",
                },
            },
            {
                # This is filtered for body text char len count
                "&id": "98bb5fec7816ae250bb0783102d24cd1a62d1bd4e546b3094ec3e9dd6ba11e48bf4ece7bdea5d360bdc95151d48013776c7b856825967a084e4044a9f19802d7",
                "body": {"text": "46k shs BEI GR crossed at 130.85 ($6.6m per side)" * 10},
                "identifiers": {
                    "allIds": [
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                    ],
                    "domains": [
                        {"types": ["FROM", "TO"], "value": "bloomberg.net"},
                        {"types": ["TO"], "value": "envestracapital.com"},
                    ],
                    "fromId": "<EMAIL>",
                    "fromUserId": "<EMAIL>",
                    "localParts": [
                        {"types": ["FROM"], "value": "t"},
                        {"types": ["TO"], "value": "a"},
                        {"types": ["TO"], "value": "d"},
                    ],
                    "toIds": [
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                    ],
                },
                "metadata": {"source": {"client": "Instant Bloomberg"}},
                "roomId": "PCHAT-0x10000029AA655",
                "stats": {"participantsCount": 21},
                "timestamps": {
                    "localTimestampStart": "2024-08-06T11:53:36",
                    "timestampStart": "2024-08-06T11:53:36",
                },
            },
        ]

    yield results


def fake_gpt_response(*args, **kwargs):
    messages = kwargs.get("prompt_list", [])
    score_map = {
        "760": {
            "l1": 4,
        },
        "761": {
            "l1": 6,
            "l2": 5,
        },
        "762": {"l1": 6, "l2": 7, "l3": 4},
        "763": {"l1": 9, "l2": 7, "l3": 8},
        "764": {"l1": 9, "l2": 7, "l3": 8},
    }
    score = 0
    response = []
    for idx, message in enumerate(messages):
        for k in score_map:
            if k in message[1]["content"]:
                if "recall" in message[0]["content"]:
                    score = score_map[k]["l1"]
                elif "deeper" in message[0]["content"]:
                    score = score_map[k]["l2"]
                elif "You are tasked with the final review of" in message[0]["content"]:
                    score = score_map[k]["l3"]
                break

        response.append(
            OpenAIResponse(
                index=idx,
                response=ChatCompletion(
                    id="chatcmpl-A6HvJvmKGxcweakuR5cYAvbMZ2Vg1",
                    created=1726062229,
                    model="gpt-4o-mini",
                    object="chat.completion",
                    usage=CompletionUsage(
                        total_tokens=512,
                        # Leaving these as zero as they are not relevant to the tests
                        prompt_tokens=445,
                        completion_tokens=67,
                    ),
                    choices=[
                        Choice(
                            message=ChatCompletionMessage(
                                content=json.dumps(
                                    {
                                        "category": "Risk",
                                        "severity_type": SeverityType.CRITICAL
                                        if score >= 7
                                        else SeverityType.MEDIUM
                                        if 5 < score < 7
                                        else SeverityType.NONE,
                                        "severity": score,
                                        "explanation": "Sensitive data.",
                                        "recommendations": "Report to IT.",
                                    }
                                ),
                                role="assistant",
                            ),
                            index=0,
                            finish_reason="stop",
                        )
                    ],
                    system_fingerprint="fp_80a1bad4c7",
                ),
                save_path="sample_path",
            )
        )

    return response


def fake_get_tenant_workflow(temp_folder, *args, **kwargs):
    return addict.Dict(
        {
            "tenant": {
                "cloud": "local",
                "lake_prefix": f"{temp_folder}/",
            },
            "workflow": {"streamed": True},
        },
    )


def fake_get_watches(record_handler, tenant: str):
    surveillance_watch = SurveillanceWatch.validate(
        {
            "&id": "watch_id",
            "name": "",
            "query": {
                "name": "",
                "description": "",
                "kind": "NEURAL",
                "filter": {
                    "flangVersion": "",
                    "chunks": [
                        {
                            "f": "sourceKey in ['tiago']",
                            "category": "",
                            "id": "",
                        }
                    ],
                },
                "neuralThresholds": {"l1Score": 7, "l2Score": 7, "l3Score": 7},
            },
            "createdOn": "2024-09-09T11:00:00Z",
            "queryType": "COMMUNICATIONS_V2",
        }
    )
    return [surveillance_watch.to_dict(exclude_none=True)]

import addict
import json
import os
import pytest
from data_platform_config_api_client.conductor_workflow_schedule import ConductorWorkflowScheduleAPI
from data_platform_config_api_client.tenant import TenantAPI
from intelligence_core_tasks.watches_schedule_sync import data_repository
from intelligence_core_tasks.watches_schedule_sync.data_repository import DataRepository
from pathlib import Path
from surveillance_utils.test_mock_helpers import FakeEsRepo, FakeSlimRecordHandler
from typing import List

TEST_DATA_PATH = Path(__file__).parent.joinpath("data")

os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "https://test-enterprise.steeleye.co"
os.environ["ELASTIC_HOST"] = "fake_host"
os.environ["ELASTIC_API_KEY"] = "API_KEY"


@pytest.fixture()
def set_mandatory_env_vars(monkeypatch):
    monkeypatch.setenv(
        "MAR_WORKFLOW_MAP",
        '{"RESTRICTED_LIST_V2": "restricted_list_v2_algo"}',
    )
    monkeypatch.setenv("TIMEOUT", "60")
    monkeypatch.setenv("STACK", "stack")


@pytest.fixture
def get_expected_es_query() -> dict:
    query_dict: dict = json.loads(TEST_DATA_PATH.joinpath("elastic_query.json").read_text())
    return query_dict


@pytest.fixture
def mock_retrieve_es_query(monkeypatch, *args, **kwargs):
    def mock_search_after(query_dict: dict, **kwargs) -> List[dict]:
        assert query_dict
        return [query_dict]

    monkeypatch.setattr(data_repository, "search_after_query", mock_search_after)


@pytest.fixture
def mock_data_repository(monkeypatch):
    def retrieve_watches(self):
        sample_watches_hits: dict = json.loads(
            TEST_DATA_PATH.joinpath("elastic_watches.json").read_text()
        )
        return sample_watches_hits["hits"]

    monkeypatch.setattr(DataRepository, "retrieve_watches", retrieve_watches)


@pytest.fixture
def mock_data_repository_incorrect(monkeypatch):
    def watch(self):
        sample_watches_hits = [
            {
                "&id": "681c7e56-eff5-4fdb-8939-b83c13696038",
                "query": {"kind": "BESPOKE"},
                "name": "test 1",
                "frequencyType": "DAILY",
                "status": "ACTIVE",
                "queryType": "COMMUNICATIONS",
                "scheduleDetails": None,
            }
        ]
        return sample_watches_hits

    monkeypatch.setattr(DataRepository, "retrieve_watches", watch)


@pytest.fixture
def mock_conductor_workflow_schedule_api():
    class FakeConductorWorkflowScheduleAPI(ConductorWorkflowScheduleAPI):
        def __new__(cls, client: str):
            return object.__new__(cls)

    return FakeConductorWorkflowScheduleAPI


@pytest.fixture
def mock_tenant_api():
    class FakeTenantAPI(TenantAPI):
        def __new__(cls, client: str):
            return object.__new__(cls)

    return FakeTenantAPI


@pytest.fixture()
def fake_slim_record_handler():
    return FakeSlimRecordHandler(
        version=8,
    )


@pytest.fixture()
def fake_es_repo_instance():
    return FakeEsRepo(version=8, fake_index="foxtrot-surveillance_watch-alias")


class SpyBulkUpsert:
    def __init__(self, schedules: list):
        self.schedules = schedules
        self.count_iterations = 0

    def fake_bulk_upsert(self, json_body, **kwarg):
        self.count_iterations += 1
        self.schedules.extend(json_body["schedules"])
        return addict.Dict({"raw_response": {"status_code": 200}})

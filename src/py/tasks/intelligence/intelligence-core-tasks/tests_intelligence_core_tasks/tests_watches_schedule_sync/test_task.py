import addict
import pytest
from aries_task_link.models import AriesTaskInput
from data_platform_config_api_client import conductor_workflow_schedule
from data_platform_config_api_client.conductor_workflow_schedule import ConductorWorkflowScheduleAPI
from data_platform_config_api_client.tenant import TenantAP<PERSON>
from freezegun import freeze_time
from se_es_utils.slim_record_handler import <PERSON><PERSON>ecord<PERSON><PERSON><PERSON>
from tests_intelligence_core_tasks.tests_watches_schedule_sync.conftest import SpyBulkUpsert


def sample_input() -> AriesTaskInput:
    return AriesTaskInput.validate(
        {
            "workflow": {
                "start_timestamp": "1685806839",
                "name": "scheduler_sync",
                "stack": "local",
                "tenant": "foxtrot",
            },
            "task": {"name": "scheduler-sync-task", "version": "latest", "success": True},
            "input_param": {"params": {"tenant_list": ["foxtrot"]}},
        }
    )


def fake_fetch_tenant(*args, **kwargs):
    return addict.Dict({"content": [{"stack_id": 1, "cloud": "aws", "name": "foxtrot"}]})


def fake_fetch_tenant_no_index(*args, **kwargs):
    return addict.Dict(
        {
            "content": [
                {"stack_id": 1, "cloud": "aws", "name": "foxtrot"},
                {"stack_id": 1, "cloud": "aws", "name": "no_index_tenant"},
                {"stack_id": 1, "cloud": "aws", "name": "foxtrot"},
            ]
        }
    )


def test_skip_tenant(
    monkeypatch,
    mock_data_repository,
    mock_conductor_workflow_schedule_api,
    mock_tenant_api,
    fake_es_repo_instance,
    set_mandatory_env_vars,
    mock_fetch_tenant_configuration,
):
    from intelligence_core_tasks.watches_schedule_sync.main import run_watches_schedule_sync

    monkeypatch.setattr(TenantAPI, "get_all", fake_fetch_tenant_no_index)

    monkeypatch.setattr(
        conductor_workflow_schedule,
        "ConductorWorkflowScheduleAPI",
        mock_conductor_workflow_schedule_api,
    )

    monkeypatch.setattr(SlimRecordHandler, "get_es_repo", lambda *a, **k: fake_es_repo_instance)

    schedules: list = []
    spy_class = SpyBulkUpsert(schedules)
    monkeypatch.setattr(
        ConductorWorkflowScheduleAPI,
        "bulk_upsert_conductor_workflow_schedules",
        spy_class.fake_bulk_upsert,
    )

    # Some mocks need to be done before the import

    aries_task_input = sample_input()
    _ = run_watches_schedule_sync(aries_task_input=aries_task_input)
    # Only called bulk_upsert_conductor_workflow_schedules 2 times since it skipped 1 tenant
    assert spy_class.count_iterations == 2


@freeze_time("2023-10-13 15:06:03")
def test_run_schedule_sync(
    monkeypatch,
    mock_data_repository,
    mock_conductor_workflow_schedule_api,
    mock_tenant_api,
    fake_es_repo_instance,
    set_mandatory_env_vars,
    mock_fetch_tenant_configuration,
):
    from intelligence_core_tasks.watches_schedule_sync.main import run_watches_schedule_sync

    monkeypatch.setattr(TenantAPI, "get_all", fake_fetch_tenant)

    monkeypatch.setattr(
        conductor_workflow_schedule,
        "ConductorWorkflowScheduleAPI",
        mock_conductor_workflow_schedule_api,
    )

    monkeypatch.setattr(SlimRecordHandler, "get_es_repo", lambda *a, **k: fake_es_repo_instance)

    schedules: list = []
    spy_class = SpyBulkUpsert(schedules)
    monkeypatch.setattr(
        ConductorWorkflowScheduleAPI,
        "bulk_upsert_conductor_workflow_schedules",
        spy_class.fake_bulk_upsert,
    )

    # Some mocks need to be done before the import

    aries_task_input = sample_input()
    output = run_watches_schedule_sync(aries_task_input=aries_task_input)
    task_metrics = output.app_metric and output.app_metric.metrics.get("custom", {}).get(
        "scheduler-sync-task", {}
    )
    assert task_metrics and task_metrics["es_watches_processed"] == 8
    assert [secret["schedule_id"] for secret in schedules] == [
        "681c7e56-eff5-4fdb-8939-b83c13696038_foxtrot",
        "4bf6b8fd-3be5-4688-b102-e54daafe705d_foxtrot",
        "084f55d5-54c3-49ad-a4da-0afa70c556fd_foxtrot",
        "1ee8c1f9-a006-43fc-b53e-6ab4f1f718d3_foxtrot",
        "08f89bf3-9bca-4353-8538-b31bf7083ce2_foxtrot",
        "09081d99-0f93-4994-a042-b1ba0ba6bc0b_foxtrot",
        "681c7e56-9999-4fdb-8939-b83c13696038_foxtrot",
        "123c7e56-9999-4fdb-8939-b83c13696123_foxtrot",
    ]


@freeze_time("2023-10-13 15:06:03")
def test_run_schedule_sync_raise_exception(
    monkeypatch,
    mock_data_repository_incorrect,
    mock_conductor_workflow_schedule_api,
    mock_tenant_api,
    fake_es_repo_instance,
    set_mandatory_env_vars,
):
    monkeypatch.setattr(
        conductor_workflow_schedule,
        "ConductorWorkflowScheduleAPI",
        mock_conductor_workflow_schedule_api,
    )
    monkeypatch.setattr(TenantAPI, "get_all", fake_fetch_tenant)

    monkeypatch.setattr(SlimRecordHandler, "get_es_repo", lambda *a, **k: fake_es_repo_instance)

    schedules: list = []
    spy_class = SpyBulkUpsert(schedules)
    monkeypatch.setattr(
        ConductorWorkflowScheduleAPI,
        "bulk_upsert_conductor_workflow_schedules",
        spy_class.fake_bulk_upsert,
    )

    with pytest.raises(Exception, match="Failure syncing tenants"):
        # Some mocks need to be done before the import
        import intelligence_core_tasks.watches_schedule_sync.main as watch_sync

        aries_task_input = sample_input()

        watch_sync.run_watches_schedule_sync(aries_task_input=aries_task_input)

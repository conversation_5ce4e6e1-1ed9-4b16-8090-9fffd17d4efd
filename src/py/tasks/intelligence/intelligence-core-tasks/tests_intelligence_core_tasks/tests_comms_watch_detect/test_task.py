import fsspec
import json
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_task_link.models import AriesTaskInput
from csurv_utils import run_query
from fsspec.implementations.local import LocalFileSystem
from pathlib import Path
from se_es_utils.slim_record_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from surveillance_utils import deduplication
from surveillance_utils.test_mock_helpers import data_lake, fake_get_tenant_workflow

TEST_DATA_PATH = Path(__file__).parent.joinpath("data")


def mock_aries_task_input() -> AriesTaskInput:
    return AriesTaskInput.validate(
        {
            "workflow": {
                "start_timestamp": "1685806839",
                "name": "run-comms-watch-workflow",
                "stack": "local",
                "tenant": "foxtrot",
            },
            "task": {"name": "comms-watch-detect-task", "version": "", "success": True},
            "input_param": {
                "params": {"watch_id": "36c1824b-e97d-4474-823d-1ac407170a58", "workflow_id": ""}
            },
        }
    )


def test_run_comms_watch_detect(fake_sequence_service, fake_es_repo_instance, monkeypatch):
    from se_data_lake import cloud_utils

    def search_after_query_yield(*args, **kwargs):
        sample_alert_hits: dict = json.loads(TEST_DATA_PATH.joinpath("alert_hits.json").read_text())
        return [sample_alert_hits["hits"]]

    def wrapped_put_file(self, path1, path2, callback=None, **kwargs):
        self.auto_mkdir = True
        self.cp_file(path1, path2, **kwargs)

    monkeypatch.setattr(CachedTenantWorkflowAPIClient, "get", fake_get_tenant_workflow)
    monkeypatch.setattr(run_query, "search_after_query_yield", search_after_query_yield)
    monkeypatch.setattr(SlimRecordHandler, "get_es_repo", lambda *a, **k: fake_es_repo_instance)
    monkeypatch.setattr(cloud_utils, "get_tenant_bucket_from_workflow", lambda *args, **kwargs: "")
    monkeypatch.setattr(LocalFileSystem, "put_file", wrapped_put_file)

    class FakeAlertDeduplication(deduplication.AlertDeduplication):
        def detect_existing_hits(self, **kwargs):
            return []

    monkeypatch.setattr(deduplication, "AlertDeduplication", FakeAlertDeduplication)
    # Some mocks need to be done before the import
    from intelligence_core_tasks.comms_watch_detect.main import run_comms_watch_detect

    aries_task_input = mock_aries_task_input()

    with data_lake():
        output = run_comms_watch_detect(aries_task_input)

        email_hits = []
        with fsspec.open(output.output_param.params["file_uri"], mode="r") as f:
            email_hits.extend([json.loads(line) for line in f.readlines()])

        out_hits = []
        with fsspec.open(output.output_param.params["non_email_hits_uri"], mode="r") as f:
            out_hits.extend([json.loads(line) for line in f.readlines()])

    assert output.app_metric.metrics["custom"]["comms-watch-detect-task"]["hit_count"] == 3
    assert output.output_param.params["flow_path"] == "comms_false_positive_reduction"
    assert len(out_hits) + len(email_hits) == 3
    assert len(email_hits) == 2

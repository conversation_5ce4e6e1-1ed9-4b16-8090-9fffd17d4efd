# ruff: noqa: E501


class ExpectedPrompts:
    EXPECTED_WASHTRADING_PROMPT = """Alert Type: Wash Trades\nThe Alert contains:\n2 Buy Executions, with a total Executed Quantity of 10000.0 and 2 Sell Executions with a total Executed Quantity of 10000.0\nAll Executions were on the same Instrument: RESTORE ORD 5P\nAll Executions were on the same Date: 09-Jun-22\nThe Quantity difference between Buys and Sells was: 0.0\n(vs. a maximum allowable threshold of 1.0%)\nThe Execution Price difference between Buys and Sells was: 0.0\n(vs. a maximum allowable threshold of 300)\nThe implied Monetary Value difference between Buys and Sells was: 0.0 GBP\nThe Time difference between Buys and Sells was: 10.53845 seconds (\nvs. a maximum allowable threshold of 180 seconds)\nMore detail on the executions are provided below:\n- Execution 0 Side: SELL Timestamp: 2022-06-09T13:17:12.343458Z, Quantity: 5000.0, Client: id:nplus1 singer capital markets limited (sing), Trader: id:tony.tak, Counterparty: id:nplus1 singer capital markets limited (sing)\n- Execution 1 Side: SELL Timestamp: 2022-06-09T13:17:01.805008Z, Quantity: 5000.0, Client: id:nplus1 singer capital markets limited (sing), Trader: id:tony.tak, Counterparty: id:nplus1 singer capital markets limited (sing)\n- Execution 2 Side: BUYI Timestamp: 2022-06-09T13:17:01.805008Z, Quantity: 5000.0, Client: id:nplus1 singer capital markets limited (sing), Trader: id:tony.tak, Counterparty: id:nplus1 singer capital markets limited (sing)\n- Execution 3 Side: BUYI Timestamp: 2022-06-09T13:17:12.343458Z, Quantity: 5000.0, Client: id:nplus1 singer capital markets limited (sing), Trader: id:tony.tak, Counterparty: id:nplus1 singer capital markets limited (sing)"""
    EXPECTED_INSIDER_TRADING_V3_PROMPT = """Alert Type: Insider Trading\n- The Alert contains 1 Executions (1 Buys, 0 Sells) on the Instrument: META PLATFORMS CL A ORD. Details on the detected execution(s):\n\n- In all detected Records the Trader is the same.\n- The monetary value of these Executions is 1585000.0 (USD). The minimum value set to generate an alert was 100 .\n- The alert was generated because a News Event was detected after Trading Activity occurred.\n-- The direction of the Event was positive. -- There were 1 news articles detected. The news articles are summarised below:\n-- Source: Reuters News; Time: 2023-10-19T16:18:28+00:00; Headline: CORRECTED-EssilorLuxottica Q3 sales up\n\n- The implied PnL (difference between the Average Execution Price and the Market Close on the Event Date) was -20950.0 (USD) \n\n- The model looked back to 13-Oct-23 for Trading Activity in the same  META PLATFORMS CL A ORD for the same  Trader and found 3 executions in this range.\n- Based on this prior activity the model set an allowable trading activity range of -84673 to 52440 in order to generate an alert"""
    EXPECTED_LAYERING_PROMPT = """Summary: Layering Alert for UNILEVER PLC ORD 3 1/9P on the Instrument 25-Apr-23\n\n- Sell Orders: 6\n- Sell Order Quantity: 2252\n- Distinct Ask Price Levels Orders Placed at: 3. The minimum required number of Price Levels to generate an Alert is 2.\n- Each Sell Order is described below:\n-- Order: SE|20230425|Layering|5, Timestamp: 2023-04-25T12:45:27.321000, Quantity: 91, Ask Price Level: 2, Market Volume Available at its Level: 1379, Order Quantity as a % of Level: 6.6%.\n-- Order: SE|20230425|Layering|9, Timestamp: 2023-04-25T12:45:28.112000, Quantity: 180, Ask Price Level: 3, Market Volume Available at its Level: 2967, Order Quantity as a % of Level: 6.07%.\n-- Order: SE|20230425|Layering|16, Timestamp: 2023-04-25T12:45:29.247000, Quantity: 184, Ask Price Level: 1, Market Volume Available at its Level: 917, Order Quantity as a % of Level: 20.07%.\n-- Order: SE|20230425|Layering|24, Timestamp: 2023-04-25T12:45:29.706000, Quantity: 117, Ask Price Level: 1, Market Volume Available at its Level: 388, Order Quantity as a % of Level: 30.15%.\n-- Order: SE|20230425|Layering|25, Timestamp: 2023-04-25T12:45:29.707000, Quantity: 750, Ask Price Level: 3, Market Volume Available at its Level: 1720, Order Quantity as a % of Level: 43.6%.\n-- Order: SE|20230425|Layering|26, Timestamp: 2023-04-25T12:45:29.707000, Quantity: 930, Ask Price Level: 3, Market Volume Available at its Level: 1720, Order Quantity as a % of Level: 54.07%\nThe Minimum % of Level required to generate an Alert is 20%\nThe Maximum Time Window Threshold to group Alerts is 30 Seconds\nThese Orders were routed to a Single Venue: XLON\nAll Orders were placed under the instruction of the same trader\nInspecting the Market Order Book:\n- Immediately preceeding the First Order Submission there was 477.0, 482.0 available on the Ask, and 350.0, 1508.0 available on the Bid from Levels 1 to 2\n- Immediately after the Last Order Submission there was 477.0, 482.0 available on the Ask, and 350.0, 1008.0 available on the Bid from Levels 1 to 2"""
    EXPECTED_FRONTRUNNING_PROMPT = """Alert Type: Frontrunning\nThe alert was raised because A Order from one Client preceded another from a different Client.\nAll Orders were placed on the same security (LLOYDS BANKING GROUP PLC ORD 10P).\nThe alert contains 2 Frontrunning Order/s:\n    -- The Order was placed at 2023-06-23T10:23:00.000Z for a quantity of 1559122\nAnd 1 Frontrun Orders;\n    -- The Order was placed at 2023-06-23T10:27:00.000Z for a quantity of 574417\nTherefore, between these two groups:\n    -- The Time difference was 240 seconds. The Threshold to generate an Alert is 10 minutes.\n    -- The relative size of the front running order, compared to the front run order, was 5.04%\n"""
    EXPECTED_POTAM_PROMPT = """Alert Logic Description: This model raises alerts when either an Order or Execution on an Instrument and that same Instrument is also present on the POTAM List.\nThe POTAM list is maintained by the Panel on Takeovers and Mergers and is the body that oversees takeover activity in the UK.\nThe Algorithm looks for Order Placement either within the POTAM window or around the POTAM window. The POTAM window is defined as the date at which the offer for Takeover or Merger is publicly available until the Takeover or Merger is either completed or withdrawn.\nThe Algorithm searches for both securities and derivatives securities of those securities listed on the POTAM list.\n\nPOTAM alert for BELLWAY PLC ORD 12.5P on 23-Jul-24\nInstrument: Crest Nicholson Holdings plc\nNamed Potential Acquirer of Crest Nicholson Holdings plc: Bellway plc\nOffer Commences: 2024-06-13T16:45:00.000Z\nOfferor Identified / Removal from POTAM List: 2024-06-13T16:45:00.000Z\nOrder Date: 23-Jul-24\nTrade Direction:\nTotal Traded Quantity: 0.0"""
    EXPECTED_RAMPING_PROMPT = """Summary: Ramping Alert for 4basebio PLC Registered Shares EO 1 on 16-Nov-22 00:00:00\nTotal Buy Executions: 4\nTotal Sell Executions: 0\nMinimum Execution Count (to generate an Alert): 4\nThe Executing Entity is the same on all Records\nInstrument: 4basebio PLC Registered Shares EO 1\nFirst Execution: 16-Nov-22\nTotal Executed Quantity: 10800.0\nTotal Market Day Traded Volume: 10100.0\nvs. Market Day Traded Volume: 1.0693069307\nVolume Threshold (to generate an Alert): 0.4\nPrice improvement between first and last execution was 0.002189781"""
    EXPECTED_SLOEV_V2_MARKET = """Summary: Alert Logic Description: This model raises alerts when either an Order, or collection of Orders are placed, and their associated quantities are above a user-defined percentage of Market Traded Volume figure.\nSuspicious Large Order Alert for: NVIDIA ORD on 27-Feb-24\nInstrument: NVIDIA ORD\nAsset Class: retrieve the asset class of the instrument\nOrders Detected: 1\nTotal order Quantity: None\nMarket Day Traded Volume: 6738450.0\nvs. Market Day Traded Volume: 0.0936579629\n% Threshold (to generate an Alert): 0.05"""
    EXPECTED_SLOEV_V2_INTERNAL = """Summary: Alert Logic Description: This model raises alerts when either an Order, or collection of Orders are placed, and their associated quantities are above the historically defined average for the denoted given trader\nSuspicious Large Order Alert for: FX SPOT AUD/CAD on 07-Jul-23\nInstrument: FX SPOT AUD/CAD\nAsset Class: retrieve the asset class of the instrument\nOrders Detected: 1\nTotal order Quantity: 500000.0\n20-Day Average trader Traded Volume: 400000.0\nvs. 20-Day Average trader Traded Volume: 1.25\n% Threshold (to generate an Alert): 1.0"""

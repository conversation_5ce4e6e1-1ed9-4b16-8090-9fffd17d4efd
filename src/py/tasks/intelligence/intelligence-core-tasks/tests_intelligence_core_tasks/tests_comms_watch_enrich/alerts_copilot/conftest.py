# ruff: noqa: E501
import json
import os
import pytest
from mock import MagicMock
from openai.types import CompletionUsage
from openai.types.chat import ChatCompletion, ChatCompletionMessage
from openai.types.chat.chat_completion import Choice
from pathlib import Path
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch
from typing import List

# Added this over here so that test_alerts_copilot can be run in isolation
os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "https://test-enterprise.steeleye.co"
os.environ["WATCH_COPILOT_TENANTS"] = "['pinafore']"
os.environ["ELASTIC_HOST"] = "fake_host"
os.environ["ELASTIC_API_KEY"] = "fake_key"
os.environ["OPENAI_API_BASE"] = "https://test_openai_url.com"
os.environ["OPENAI_API_KEY"] = "<some secret>"
os.environ["OPENAI_API_MODEL"] = "gpt-4o"
os.environ["OPENAI_API_VERSION"] = "2023-03-15-preview"
TEST_DATA_PATH = Path(__file__).parent.parent.joinpath("data")


@pytest.fixture()
def record_handler_mock_with_no_response():
    record_handler_mock = MagicMock()
    record_handler_mock.search_records_by_query.return_value = {}
    return record_handler_mock


@pytest.fixture()
def record_handler_mock_with_response():
    record_handler_mock = MagicMock()
    search_before_results = {
        "took": 27,
        "timed_out": False,
        "_shards": {"total": 1, "successful": 1, "skipped": 0, "failed": 0},
        "hits": {
            "total": 144,
            "max_score": None,
            "hits": [
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "c301b96f546744cc9263a2013deb6388f0d0a58e78520cc4a200c3fba7122105ce19add598e2b36fe8540c33abe02567c0dd818c659a92953948c5e5bd567808",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-29T09:02:09Z"},
                        "body": {
                            "text": "Hey, been thinking about how we can give our ULVR.L game a little kick for our buddy"
                        },
                    },
                    "sort": [
                        1711702929000,
                        "c301b96f546744cc9263a2013deb6388f0d0a58e78520cc4a200c3fba7122105ce19add598e2b36fe8540c33abe02567c0dd818c659a92953948c5e5bd567808",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "fbcc2ae55c3116fedef8f8102d5a9a1d07253bfe92e89f77fe512974c8765a7a4295812e5e1b248ad5b6c00c3f13d7e216a346203ce58d03cf94f6a763ef2c33",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-28T09:02:42Z"},
                        "body": {"text": "done, nice work partner"},
                    },
                    "sort": [
                        1711616562000,
                        "fbcc2ae55c3116fedef8f8102d5a9a1d07253bfe92e89f77fe512974c8765a7a4295812e5e1b248ad5b6c00c3f13d7e216a346203ce58d03cf94f6a763ef2c33",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "6b2177513cfdcda9390861e52e95a31a0833010dc015fc8b725848a4cbf5381d68e1dbd22b76502cfef4b52bb88840f8ea873253f1be9a647dd71f87349b665a",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-28T09:02:41Z"},
                        "body": {
                            "text": "Keep it smooth, and we should be able to walk away from this without raising any suspicions"
                        },
                    },
                    "sort": [
                        1711616561000,
                        "6b2177513cfdcda9390861e52e95a31a0833010dc015fc8b725848a4cbf5381d68e1dbd22b76502cfef4b52bb88840f8ea873253f1be9a647dd71f87349b665a",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "49a992b5f59b8b862503745d0d6b7d982d2745b305e83f673c2951ad66b14613ece25c148ff0df051db7e339cef82bffe2ed81b5ab995ceea564118f578cff63",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-28T09:02:40Z"},
                        "body": {
                            "text": "Easing off the orders now. We should be in the clear soon"
                        },
                    },
                    "sort": [
                        1711616560000,
                        "49a992b5f59b8b862503745d0d6b7d982d2745b305e83f673c2951ad66b14613ece25c148ff0df051db7e339cef82bffe2ed81b5ab995ceea564118f578cff63",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "b082f264b2918657e9d9c138e41524ee1e8a4a8188d8eb92749bc8e913e227bba02db9ed91f33c84caf3cf76156fbe17c0c09bd9302ad37f3963e83192fc7fa3",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-28T09:02:39Z"},
                        "body": {
                            "text": "Perfect. Hold on, I just got a message from our pal. He's managed to take advantage of the uptick. Let's start pulling back"
                        },
                    },
                    "sort": [
                        1711616559000,
                        "b082f264b2918657e9d9c138e41524ee1e8a4a8188d8eb92749bc8e913e227bba02db9ed91f33c84caf3cf76156fbe17c0c09bd9302ad37f3963e83192fc7fa3",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "a205e20ac2ddca851041f92dfe2e86f57b9c7f944401c0902eded848646d166ac7392fbbfd74045248b0fd3cd15faf9711c073bbd5d55621ce6f8cd00403db28",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-28T09:02:38Z"},
                        "body": {"text": "Bombs away. Getting the momentum going"},
                    },
                    "sort": [
                        1711616558000,
                        "a205e20ac2ddca851041f92dfe2e86f57b9c7f944401c0902eded848646d166ac7392fbbfd74045248b0fd3cd15faf9711c073bbd5d55621ce6f8cd00403db28",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "8649073cdc0d4f80b39a4d230c8608127fa7583befe5dbc84db1ec26a01b0a3301c6ce4ab37041a13a5f5541f3d7ac4715e3d119f6d49056198dabe70486fc69",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-28T09:02:19Z"},
                        "body": {
                            "text": "Roger that. Good luck, and let's hope this boosts our buddy's bottom line without raising any eyebrows"
                        },
                    },
                    "sort": [
                        1711616539000,
                        "8649073cdc0d4f80b39a4d230c8608127fa7583befe5dbc84db1ec26a01b0a3301c6ce4ab37041a13a5f5541f3d7ac4715e3d119f6d49056198dabe70486fc69",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "9c8f248a98c2b361232e7a979585a385c0809d00cede2300b481bf919477357abf0356d6e9453a5070568ae1a428c8a22691dacfcda896b592dfa2d4b9dc18cf",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-28T09:02:18Z"},
                        "body": {
                            "text": "Alright, let's dance. I'll start peppering those offers. Holler if anything seems fishy or sth feels off"
                        },
                    },
                    "sort": [
                        1711616538000,
                        "9c8f248a98c2b361232e7a979585a385c0809d00cede2300b481bf919477357abf0356d6e9453a5070568ae1a428c8a22691dacfcda896b592dfa2d4b9dc18cf",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "3e1d8e5a2c3e0aa1b02cdc59cec06597734ae2e8d0f81cba8d5d3480fcf46a74697ed1b9431ec4257d7492183e80b43c1f01a98f77db0bfb1150ea9661bd1bde",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-28T09:02:17Z"},
                        "body": {
                            "text": "No doubt. Let's stay sharp and be ready to hit the brakes if needed. Fingers crossed it pans out and we keep our tracks covered"
                        },
                    },
                    "sort": [
                        1711616537000,
                        "3e1d8e5a2c3e0aa1b02cdc59cec06597734ae2e8d0f81cba8d5d3480fcf46a74697ed1b9431ec4257d7492183e80b43c1f01a98f77db0bfb1150ea9661bd1bde",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "34fc6a4d0d102121ffeb9d41469f722795d9fd5ec0562b631bcb57f9dd427dd976730ce0634ae2bc7bb3796dd7624e017ce57a371d5cac4d28bd54fd9756184f",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-28T09:02:16Z"},
                        "body": {
                            "text": "Right on. Let's dip our toes and keep a close eye. If things go south, we gotta ghost"
                        },
                    },
                    "sort": [
                        1711616536000,
                        "34fc6a4d0d102121ffeb9d41469f722795d9fd5ec0562b631bcb57f9dd427dd976730ce0634ae2bc7bb3796dd7624e017ce57a371d5cac4d28bd54fd9756184f",
                    ],
                },
                # Adding messages which should be filtered out
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "99fc6a4d0d102121ffeb9d41469f722795d9fd5ec0562b631bcb57f9dd427dd976730ce0634ae2bc7bb3796dd7624e017ce57a371d5cac4d28bd54fd9756184f",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-28T09:02:16Z"},
                        "body": {"text": "This has disclaimer in analytics"},
                        "analytics": {
                            "zoning": {
                                "metadata": {"endpointName": "bloomberg-chat-disclaimer-regex"},
                                "predictionOffsets": [
                                    {
                                        "class_": "disclaimer",
                                        "start": 0,
                                        "end": "",
                                        "value": 0.9,
                                    }
                                ],
                            }
                        },
                        "metadata": {"source": {"client": "bloomberg"}},
                    },
                    "sort": [
                        1711616536000,
                        "99fc6a4d0d102121ffeb9d41469f722795d9fd5ec0562b631bcb57f9dd427dd976730ce0634ae2bc7bb3796dd7624e017ce57a371d5cac4d28bd54fd9756184f",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "123c6a4d0d102121ffeb9d41469f722795d9fd5ec0562b631bcb57f9dd427dd976730ce0634ae2bc7bb3796dd7624e017ce57a371d5cac4d28bd54fd9756184f",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-28T09:02:16Z"},
                        "body": {"text": "removed a post"},
                        "metadata": {"source": {"client": "bloomberg"}},
                    },
                    "sort": [
                        1711616536000,
                        "123c6a4d0d102121ffeb9d41469f722795d9fd5ec0562b631bcb57f9dd427dd976730ce0634ae2bc7bb3796dd7624e017ce57a371d5cac4d28bd54fd9756184f",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "456c6a4d0d102121ffeb9d41469f722795d9fd5ec0562b631bcb57f9dd427dd976730ce0634ae2bc7bb3796dd7624e017ce57a371d5cac4d28bd54fd9756184f",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-28T09:02:16Z"},
                        "body": {"text": "FIRM DISCLAIMER: Should be removed"},
                        "metadata": {"source": {"client": "bloomberg"}},
                    },
                    "sort": [
                        1711616536000,
                        "456c6a4d0d102121ffeb9d41469f722795d9fd5ec0562b631bcb57f9dd427dd976730ce0634ae2bc7bb3796dd7624e017ce57a371d5cac4d28bd54fd9756184f",
                    ],
                },
            ],
        },
    }

    search_after_results = {
        "took": 1,
        "timed_out": False,
        "_shards": {"total": 1, "successful": 1, "skipped": 0, "failed": 0},
        "hits": {
            "total": 144,
            "max_score": None,
            "hits": [
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "cc0638ff8d4c6c7ef96d29f4360b2c0f171604ed515ba9df4c8579d888a02bdcae1245a2fff6b03d7adfcc0f1a69fc9d333f677c4114386618f29b23c35a5744",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-29T09:02:11Z"},
                        "body": {
                            "text": "How about we play the tape again, toss some smoke at L1, L2, L3, L5 on the Ask. Might give us a lift in the price action, just like old times"
                        },
                    },
                    "sort": [
                        1711702931000,
                        "cc0638ff8d4c6c7ef96d29f4360b2c0f171604ed515ba9df4c8579d888a02bdcae1245a2fff6b03d7adfcc0f1a69fc9d333f677c4114386618f29b23c35a5744",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "88a041f656d2ea82178b37e71ecdf7437b77e881aa514867fab1d2cca7eefdf06af6f2f2c67c8f38fe5e24eba550cd7061059746e5d0bf254883170a017de16b",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-29T09:02:12Z"},
                        "body": {
                            "text": "Crafty. So we're looking at adding some low-key interest at those levels, huh? And by keeping it there, we're under the radar, right?"
                        },
                    },
                    "sort": [
                        1711702932000,
                        "88a041f656d2ea82178b37e71ecdf7437b77e881aa514867fab1d2cca7eefdf06af6f2f2c67c8f38fe5e24eba550cd7061059746e5d0bf254883170a017de16b",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "509b2860cae46bae55e97a061cc5b49044854d7d389c57e3128badf5abe79e619bec5e2d4dae59c69417ee006cafa9e70bb12398665b9822b1794938931ead80",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-29T09:02:13Z"},
                        "body": {
                            "text": "That's the ticket. We'll sprinkle a bunch. Once the market starts nibbling, our buddy can unload his stash at a sweet markup. I should be able to drive it down to 39.615"
                        },
                    },
                    "sort": [
                        1711702933000,
                        "509b2860cae46bae55e97a061cc5b49044854d7d389c57e3128badf5abe79e619bec5e2d4dae59c69417ee006cafa9e70bb12398665b9822b1794938931ead80",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "80782fdf24a6f30a2faba91e697dd4a4de6c74511a42d2462e9fa155eda5d847c30a63fb9a200b22a5334542726557791aa3629b10070a7a4a98a028bfd1936b",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-29T09:02:14Z"},
                        "body": {
                            "text": "Playing with fire, but I'm intrigued. Gotta be slick, though. We don't want the suits or the surveillance system sniffing around"
                        },
                    },
                    "sort": [
                        1711702934000,
                        "80782fdf24a6f30a2faba91e697dd4a4de6c74511a42d2462e9fa155eda5d847c30a63fb9a200b22a5334542726557791aa3629b10070a7a4a98a028bfd1936b",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "cda657d3281e2b1d51193fc1a74b4c0169f3fce2554194adad3245c363506fceee247765073e592e5709952282f5a7689265e62e752abd04443d973417da77d8",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-29T09:02:15Z"},
                        "body": {
                            "text": "For sure. We'll need to be smooth about it. Maybe drip-feed the orders in bite-sized chunks, keep it low-key. Our usual pattern should help us fly under the radar"
                        },
                    },
                    "sort": [
                        1711702935000,
                        "cda657d3281e2b1d51193fc1a74b4c0169f3fce2554194adad3245c363506fceee247765073e592e5709952282f5a7689265e62e752abd04443d973417da77d8",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "fe2b6f11c45cdc3b799d2918a4bb6ba1b4c474b01912e1870953ce85fa036692340806699606973e1129c6a086ac79353be43b20317f4930d6c0a27a2cfa93b5",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-29T09:02:16Z"},
                        "body": {
                            "text": "Right on. Let's dip our toes and keep a close eye. If things go south, we gotta ghost"
                        },
                    },
                    "sort": [
                        1711702936000,
                        "fe2b6f11c45cdc3b799d2918a4bb6ba1b4c474b01912e1870953ce85fa036692340806699606973e1129c6a086ac79353be43b20317f4930d6c0a27a2cfa93b5",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "47ae709e6d0a84f46ae76325031f3eedb646f2a93cd0f23887f4fc08a9e126b7127f4a373f4b3db524999d00c15a8adef603e1b2d3a40ce345eada1af535103c",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-29T09:02:17Z"},
                        "body": {
                            "text": "No doubt. Let's stay sharp and be ready to hit the brakes if needed. Fingers crossed it pans out and we keep our tracks covered"
                        },
                    },
                    "sort": [
                        1711702937000,
                        "47ae709e6d0a84f46ae76325031f3eedb646f2a93cd0f23887f4fc08a9e126b7127f4a373f4b3db524999d00c15a8adef603e1b2d3a40ce345eada1af535103c",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "a5759b7a8051cad3fd91aee785e85cadcb6ce80ac028ba12769a5bf4b3331c56d82e854ced6cf782018ca2686cc61f16f37ca98bf560b2d2a4724dfc8e17a8cc",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-29T09:02:18Z"},
                        "body": {
                            "text": "Alright, let's dance. I'll start peppering those offers. Holler if anything seems fishy or sth feels off"
                        },
                    },
                    "sort": [
                        1711702938000,
                        "a5759b7a8051cad3fd91aee785e85cadcb6ce80ac028ba12769a5bf4b3331c56d82e854ced6cf782018ca2686cc61f16f37ca98bf560b2d2a4724dfc8e17a8cc",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "1c1031dda5d58ef5fbc8d43815535554d9e4d4c0655fd372613229c788693d526310b56fc289de4b2597a6ffbf1c401f4f4dd2fd852e47bf23e5e946f186b01e",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-29T09:02:19Z"},
                        "body": {
                            "text": "Roger that. Good luck, and let's hope this boosts our buddy's bottom line without raising any eyebrows"
                        },
                    },
                    "sort": [
                        1711702939000,
                        "1c1031dda5d58ef5fbc8d43815535554d9e4d4c0655fd372613229c788693d526310b56fc289de4b2597a6ffbf1c401f4f4dd2fd852e47bf23e5e946f186b01e",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "69df15a068750db9495bab152c645587d064c347e587dde2b0f33a1071f99790b91f57cc1759b4f32a0fea7d569c7d7970a59484f81266ad6de3e1501fe97031",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-29T09:02:38Z"},
                        "body": {"text": "Bombs away. Getting the momentum going"},
                    },
                    "sort": [
                        1711702958000,
                        "69df15a068750db9495bab152c645587d064c347e587dde2b0f33a1071f99790b91f57cc1759b4f32a0fea7d569c7d7970a59484f81266ad6de3e1501fe97031",
                    ],
                },
                # Adding messages which should be filtered out
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "99fc6a4d0d102121ffeb9d41469f722795d9fd5ec0562b631bcb57f9dd427dd976730ce0634ae2bc7bb3796dd7624e017ce57a371d5cac4d28bd54fd9756184f",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-28T09:02:16Z"},
                        "body": {"text": "This has disclaimer in analytics"},
                        "analytics": {
                            "zoning": {
                                "metadata": {"endpointName": "bloomberg-chat-disclaimer-regex"},
                                "predictionOffsets": [
                                    {
                                        "class_": "disclaimer",
                                        "start": 0,
                                        "end": "",
                                        "value": 0.9,
                                    }
                                ],
                            }
                        },
                        "metadata": {"source": {"client": "bloomberg"}},
                    },
                    "sort": [
                        1711616536000,
                        "99fc6a4d0d102121ffeb9d41469f722795d9fd5ec0562b631bcb57f9dd427dd976730ce0634ae2bc7bb3796dd7624e017ce57a371d5cac4d28bd54fd9756184f",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "123c6a4d0d102121ffeb9d41469f722795d9fd5ec0562b631bcb57f9dd427dd976730ce0634ae2bc7bb3796dd7624e017ce57a371d5cac4d28bd54fd9756184f",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-28T09:02:16Z"},
                        "body": {"text": "removed a post"},
                        "metadata": {"source": {"client": "bloomberg"}},
                    },
                    "sort": [
                        1711616536000,
                        "123c6a4d0d102121ffeb9d41469f722795d9fd5ec0562b631bcb57f9dd427dd976730ce0634ae2bc7bb3796dd7624e017ce57a371d5cac4d28bd54fd9756184f",
                    ],
                },
                {
                    "_index": "iris_uat_steeleye_co_message",
                    "_type": "Message",
                    "_id": "456c6a4d0d102121ffeb9d41469f722795d9fd5ec0562b631bcb57f9dd427dd976730ce0634ae2bc7bb3796dd7624e017ce57a371d5cac4d28bd54fd9756184f",
                    "_score": None,
                    "_source": {
                        "identifiers": {"fromId": "<EMAIL>"},
                        "timestamps": {"timestampStart": "2024-03-28T09:02:16Z"},
                        "body": {"text": "FIRM DISCLAIMER: Should be removed"},
                        "metadata": {"source": {"client": "bloomberg"}},
                    },
                    "sort": [
                        1711616536000,
                        "456c6a4d0d102121ffeb9d41469f722795d9fd5ec0562b631bcb57f9dd427dd976730ce0634ae2bc7bb3796dd7624e017ce57a371d5cac4d28bd54fd9756184f",
                    ],
                },
            ],
        },
    }
    record_handler_mock.search_records_by_query.side_effect = [
        search_before_results,
        search_after_results,
    ]
    return record_handler_mock


@pytest.fixture()
def source_alerts() -> List[dict]:
    with open(TEST_DATA_PATH.joinpath("alerts_copilot_sample_input.json"), "r") as handle:
        alerts = json.load(handle)
    return alerts  # type: ignore[no-any-return]


@pytest.fixture()
def source_info_barriers_alerts() -> List[dict]:
    with open(TEST_DATA_PATH.joinpath("alerts_copilot_info_barrier.json"), "r") as handle:
        alerts = json.load(handle)
    return alerts  # type: ignore[no-any-return]


@pytest.fixture()
def gpt_response_side_effect() -> List[dict]:
    with open(TEST_DATA_PATH.joinpath("gpt_resp.json"), "r") as f:
        gpt_response_side_effect = MagicMock(return_value=json.load(f))
    return gpt_response_side_effect


@pytest.fixture()
def gpt_response_side_effect_message() -> List[dict]:
    data = {
        "&parent": "1fe45072-162e-4729-94c7-d5df43a84ae7",
        "detail": {
            "createdOn": "2024-03-25T10:28:17Z",
            "queryKind": "BESPOKE",
            "queryName": "Autorun_cmU3Nn_20240325",
            "queryType": "COMMUNICATIONS",
            "watchId": "f71a2488-6bf9-4dd0-8d3e-ec982690c22d",
            "watchName": "Autorun_cmU3Nn_20240325",
            "watchPriority": "LOW",
        },
        "detected": "2024-04-04T16:38:18.672336Z",
        "hit": {
            "sourceKey": "s3://iris.uat.steeleye.co/stream/comms-chat-txtsmarter/20240402/commsMessage.layering.20240402.DMA.matt.storey.astuti.mishra.2.json",
            "&id": "122e6bb62ae27e1ecb02a95f7607a18845cd47f6a8937ca65e3b7221c8460ddf62361a4713832af3780aca723864dc180fb14ddf678948946552958e5cc80859",
            "metadata": {"source": {"client": "Bloomberg Chat"}},
            "identifiers": {
                "allIds": ["<EMAIL>", "<EMAIL>"],
                "fromId": "<EMAIL>",
                "fromIdAddlInfo": {"raw": "<EMAIL>"},
                "fromUserId": "<EMAIL>",
                "toIds": ["<EMAIL>"],
                "toIdsAddlInfo": [{"raw": "<EMAIL>"}],
            },
            "timestamps": {
                "localTimestampStart": "2024-03-29T09:02:10Z",
                "timestampStart": "2024-03-29T09:02:10Z",
            },
            "&key": "Message:122e6bb62ae27e1ecb02a95f7607a18845cd47f6a8937ca65e3b7221c8460ddf62361a4713832af3780aca723864dc180fb14ddf678948946552958e5cc80859:1712059395458",
            "body": {
                "displayText": "I'm all ears. What's the play this time?",
                "text": "I'm all ears. What's the play this time?",
                "type": "PLAIN",
            },
            "roomId": "DMA|matt.storey|astuti.mishra",
            "roomName": "DMA|matt.storey|astuti.mishra",
            "participants": [
                {
                    "types": ["FROM"],
                    "value": {
                        "&id": "ceef8b6f-ab4a-4487-b578-3e7312a0eafb",
                        "&key": "AccountPerson:ceef8b6f-ab4a-4487-b578-3e7312a0eafb:*************",
                        "communications": {
                            "emails": ["<EMAIL>"],
                            "imAccounts": [
                                {"id": "astuti.mishra", "label": "BBG"},
                                {"id": "astuti.mishra", "label": "Live Chat"},
                            ],
                        },
                        "name": "Astuti Mishra",
                        "officialIdentifiers": {
                            "concatId": "********************",
                            "mifirId": "********************",
                            "mifirIdSubType": "CONCAT",
                            "mifirIdType": "N",
                        },
                        "personalDetails": {
                            "dob": "1969-06-09",
                            "firstName": "Astuti",
                            "lastName": "Mishra",
                            "nationality": ["IN"],
                        },
                        "retailOrProfessional": "N/A",
                        "sinkIdentifiers": {
                            "tradeFileIdentifiers": [
                                {"id": "astuti.mishra", "label": "id"},
                                {"id": "astuti.mishra", "label": "account"},
                            ]
                        },
                        "sourceKey": "s3://iris.uat.steeleye.co/flows/mymarket-universal-steeleye-person/steeleyeBlotter.mymarket.********.csv",
                        "structure": {"department": "Macro Desk", "role": "Macro Trader"},
                        "uniqueIds": [
                            "astuti.mishra",
                            "<EMAIL>",
                            "account:astuti.mishra",
                            "id:astuti.mishra",
                        ],
                    },
                },
                {
                    "types": ["TO"],
                    "value": {
                        "&id": "c8675ebe-abec-4c5d-9e1d-c6a1d792724c",
                        "&key": "AccountPerson:c8675ebe-abec-4c5d-9e1d-c6a1d792724c:*************",
                        "communications": {
                            "emails": ["<EMAIL>", "<EMAIL>"],
                            "imAccounts": [{"id": "u4pa3evjt", "label": "Slack"}],
                            "phoneNumbers": [
                                {"dialingCode": "GB", "label": "MOBILE", "number": "+************"}
                            ],
                        },
                        "location": {
                            "homeAddress": {"address": "GB", "country": "GB"},
                            "officeAddress": {"address": "GB", "country": "GB"},
                        },
                        "name": "Matt Storey",
                        "officialIdentifiers": {
                            "branchCountry": "GB",
                            "concatId": "GB19440713MATT#STORE",
                            "mifirId": "GB19440713MATT#STORE",
                            "mifirIdSubType": "CONCAT",
                            "mifirIdType": "N",
                        },
                        "personalDetails": {
                            "dob": "1944-07-13",
                            "firstName": "Matt",
                            "lastName": "Storey",
                            "nationality": ["GB"],
                        },
                        "retailOrProfessional": "N/A",
                        "sinkIdentifiers": {
                            "tradeFileIdentifiers": [
                                {"id": "matt.storey", "label": "account"},
                                {"id": "pian", "label": "id"},
                                {"id": "aferreira", "label": "id"},
                            ]
                        },
                        "uniqueIds": [
                            "account:matt.storey",
                            "id:pian",
                            "+************",
                            "<EMAIL>",
                            "id:aferreira",
                            "<EMAIL>",
                            "u4pa3evjt",
                        ],
                    },
                },
            ],
        },
        "workflow": {"status": "UNRESOLVED", "assigneeId": None, "assigneeName": None},
        "&uniqueProps": ["<EMAIL>", "<EMAIL>"],
        "hitModel": "Message",
        "copilotAnalysis": {
            "score": 9,
            "scoreRationale": "The communication between employees suggests a potential manipulation of the market which is a serious violation of established regulations. The use of phrases such as 'our buddy', 'without raising any eyebrows', 'raising any suspicions' and 'under the radar' is strongly indicative of intention to manipulate trading or avoid detection.",
            "nextStep": "I recommend escalating the situation to the regulatory enforcement agency, while also raising an internal investigation into the pattern of correspondence and related trading activity <NAME_EMAIL> and <EMAIL>",
            "suggestedResolutionComment": "The employees appear to be engaging in a disclosed practice of manipulation using coded language. The behavior uncovered here suggests conscious wrongdoing.",
            "suggestedResolutionCategory": "POLICY_BREACH",
            "suggestedResolutionSubCategories": ["Regulatory Breach", "Referred to Enforcement"],
            "otherRisks": "Such behavior not only risks regulatory fines and sanctions but can also significantly damage the company's reputation in the marketplace. This could lead to loss of business, the scrutiny of other regulators, and the potential breakdown of other compliance procedures within the firm.",
            "source": {
                "source": "AZURE OPENAI",
                "model": "gpt-4o",
                "version": "2023-03-15-preview",
            },
        },
    }
    gpt_response_side_effect = MagicMock(return_value=[data])
    return gpt_response_side_effect


@pytest.fixture()
def expected_result_copilot() -> List[dict]:
    with open(TEST_DATA_PATH.joinpath("expected_result.json"), "r") as handle:
        expected_result_copilot = json.load(handle)
    return expected_result_copilot  # type: ignore[no-any-return]


@pytest.fixture()
def expected_result_copilot_message() -> List[dict]:
    return [
        {
            "&parent": "1fe45072-162e-4729-94c7-d5df43a84ae7",
            "detail": {
                "createdOn": "2024-03-25T10:28:17Z",
                "queryKind": "BESPOKE",
                "queryName": "Autorun_cmU3Nn_20240325",
                "queryType": "COMMUNICATIONS",
                "watchId": "f71a2488-6bf9-4dd0-8d3e-ec982690c22d",
                "watchName": "Autorun_cmU3Nn_20240325",
                "watchPriority": "LOW",
            },
            "detected": "2024-04-04T16:38:18.672336Z",
            "hit": {
                "sourceKey": "s3://iris.uat.steeleye.co/stream/comms-chat-txtsmarter/20240402/commsMessage.layering.20240402.DMA.matt.storey.astuti.mishra.2.json",
                "&id": "122e6bb62ae27e1ecb02a95f7607a18845cd47f6a8937ca65e3b7221c8460ddf62361a4713832af3780aca723864dc180fb14ddf678948946552958e5cc80859",
                "metadata": {"source": {"client": "Bloomberg Chat"}},
                "identifiers": {
                    "allIds": ["<EMAIL>", "<EMAIL>"],
                    "fromId": "<EMAIL>",
                    "fromIdAddlInfo": {"raw": "<EMAIL>"},
                    "fromUserId": "<EMAIL>",
                    "toIds": ["<EMAIL>"],
                    "toIdsAddlInfo": [{"raw": "<EMAIL>"}],
                },
                "timestamps": {
                    "localTimestampStart": "2024-03-29T09:02:10Z",
                    "timestampStart": "2024-03-29T09:02:10Z",
                },
                "&key": "Message:122e6bb62ae27e1ecb02a95f7607a18845cd47f6a8937ca65e3b7221c8460ddf62361a4713832af3780aca723864dc180fb14ddf678948946552958e5cc80859:1712059395458",
                "body": {
                    "displayText": "I'm all ears. What's the play this time?",
                    "text": "I'm all ears. What's the play this time?",
                    "type": "PLAIN",
                },
                "roomId": "DMA|matt.storey|astuti.mishra",
                "roomName": "DMA|matt.storey|astuti.mishra",
                "participants": [
                    {
                        "types": ["FROM"],
                        "value": {
                            "&id": "ceef8b6f-ab4a-4487-b578-3e7312a0eafb",
                            "&key": "AccountPerson:ceef8b6f-ab4a-4487-b578-3e7312a0eafb:*************",
                            "communications": {
                                "emails": ["<EMAIL>"],
                                "imAccounts": [
                                    {"id": "astuti.mishra", "label": "BBG"},
                                    {"id": "astuti.mishra", "label": "Live Chat"},
                                ],
                            },
                            "name": "Astuti Mishra",
                            "officialIdentifiers": {
                                "concatId": "********************",
                                "mifirId": "********************",
                                "mifirIdSubType": "CONCAT",
                                "mifirIdType": "N",
                            },
                            "personalDetails": {
                                "dob": "1969-06-09",
                                "firstName": "Astuti",
                                "lastName": "Mishra",
                                "nationality": ["IN"],
                            },
                            "retailOrProfessional": "N/A",
                            "sinkIdentifiers": {
                                "tradeFileIdentifiers": [
                                    {"id": "astuti.mishra", "label": "id"},
                                    {"id": "astuti.mishra", "label": "account"},
                                ]
                            },
                            "sourceKey": "s3://iris.uat.steeleye.co/flows/mymarket-universal-steeleye-person/steeleyeBlotter.mymarket.********.csv",
                            "structure": {"department": "Macro Desk", "role": "Macro Trader"},
                            "uniqueIds": [
                                "astuti.mishra",
                                "<EMAIL>",
                                "account:astuti.mishra",
                                "id:astuti.mishra",
                            ],
                        },
                    },
                    {
                        "types": ["TO"],
                        "value": {
                            "&id": "c8675ebe-abec-4c5d-9e1d-c6a1d792724c",
                            "&key": "AccountPerson:c8675ebe-abec-4c5d-9e1d-c6a1d792724c:*************",
                            "communications": {
                                "emails": [
                                    "<EMAIL>",
                                    "<EMAIL>",
                                ],
                                "imAccounts": [{"id": "u4pa3evjt", "label": "Slack"}],
                                "phoneNumbers": [
                                    {
                                        "dialingCode": "GB",
                                        "label": "MOBILE",
                                        "number": "+************",
                                    }
                                ],
                            },
                            "location": {
                                "homeAddress": {"address": "GB", "country": "GB"},
                                "officeAddress": {"address": "GB", "country": "GB"},
                            },
                            "name": "Matt Storey",
                            "officialIdentifiers": {
                                "branchCountry": "GB",
                                "concatId": "GB19440713MATT#STORE",
                                "mifirId": "GB19440713MATT#STORE",
                                "mifirIdSubType": "CONCAT",
                                "mifirIdType": "N",
                            },
                            "personalDetails": {
                                "dob": "1944-07-13",
                                "firstName": "Matt",
                                "lastName": "Storey",
                                "nationality": ["GB"],
                            },
                            "retailOrProfessional": "N/A",
                            "sinkIdentifiers": {
                                "tradeFileIdentifiers": [
                                    {"id": "matt.storey", "label": "account"},
                                    {"id": "pian", "label": "id"},
                                    {"id": "aferreira", "label": "id"},
                                ]
                            },
                            "uniqueIds": [
                                "account:matt.storey",
                                "id:pian",
                                "+************",
                                "<EMAIL>",
                                "id:aferreira",
                                "<EMAIL>",
                                "u4pa3evjt",
                            ],
                        },
                    },
                ],
            },
            "workflow": {"status": "UNRESOLVED", "assigneeId": None, "assigneeName": None},
            "&uniqueProps": ["<EMAIL>", "<EMAIL>"],
            "hitModel": "Message",
            "copilotAnalysis": {
                "score": 9,
                "scoreRationale": "The communication between employees suggests a potential manipulation of the market which is a serious violation of established regulations. The use of phrases such as 'our buddy', 'without raising any eyebrows', 'raising any suspicions' and 'under the radar' is strongly indicative of intention to manipulate trading or avoid detection.",
                "nextStep": "I recommend escalating the situation to the regulatory enforcement agency, while also raising an internal investigation into the pattern of correspondence and related trading activity <NAME_EMAIL> and <EMAIL>",
                "suggestedResolutionComment": "The employees appear to be engaging in a disclosed practice of manipulation using coded language. The behavior uncovered here suggests conscious wrongdoing.",
                "suggestedResolutionCategory": "POLICY_BREACH",
                "suggestedResolutionSubCategories": [
                    "Regulatory Breach",
                    "Referred to Enforcement",
                ],
                "otherRisks": "Such behavior not only risks regulatory fines and sanctions but can also significantly damage the company's reputation in the marketplace. This could lead to loss of business, the scrutiny of other regulators, and the potential breakdown of other compliance procedures within the firm.",
                "source": {
                    "source": "AZURE OPENAI",
                    "model": "gpt-4o",
                    "version": "2023-03-15-preview",
                },
            },
        }
    ]


@pytest.fixture()
def source_alerts_for_message() -> List[dict]:
    return [
        {
            "detail": {
                "createdOn": "2024-03-25T10:28:17Z",
                "queryKind": "BESPOKE",
                "queryName": "Autorun_cmU3Nn_20240325",
                "queryType": "COMMUNICATIONS",
                "watchId": "f71a2488-6bf9-4dd0-8d3e-ec982690c22d",
                "watchName": "Autorun_cmU3Nn_20240325",
                "watchPriority": "LOW",
            },
            "&parent": "1fe45072-162e-4729-94c7-d5df43a84ae7",
            "detected": "2024-04-04T16:08:50.634295Z",
            "hit": {
                "sourceKey": "s3://iris.uat.steeleye.co/stream/comms-chat-txtsmarter/20240402/commsMessage.layering.20240402.DMA.matt.storey.astuti.mishra.2.json",
                "&id": "122e6bb62ae27e1ecb02a95f7607a18845cd47f6a8937ca65e3b7221c8460ddf62361a4713832af3780aca723864dc180fb14ddf678948946552958e5cc80859",
                "metadata": {"source": {"client": "Bloomberg Chat"}},
                "identifiers": {
                    "allIds": ["<EMAIL>", "<EMAIL>"],
                    "fromId": "<EMAIL>",
                    "fromIdAddlInfo": {"raw": "<EMAIL>"},
                    "fromUserId": "<EMAIL>",
                    "toIds": ["<EMAIL>"],
                    "toIdsAddlInfo": [{"raw": "<EMAIL>"}],
                },
                "timestamps": {
                    "localTimestampStart": "2024-03-29T09:02:10Z",
                    "timestampStart": "2024-03-29T09:02:10Z",
                },
                "&key": "Message:122e6bb62ae27e1ecb02a95f7607a18845cd47f6a8937ca65e3b7221c8460ddf62361a4713832af3780aca723864dc180fb14ddf678948946552958e5cc80859:1712059395458",
                "body": {
                    "displayText": "I'm all ears. What's the play this time?",
                    "text": "I'm all ears. What's the play this time?",
                    "type": "PLAIN",
                },
                "roomId": "DMA|matt.storey|astuti.mishra",
                "roomName": "DMA|matt.storey|astuti.mishra",
                "participants": [
                    {
                        "types": ["FROM"],
                        "value": {
                            "&id": "ceef8b6f-ab4a-4487-b578-3e7312a0eafb",
                            "&key": "AccountPerson:ceef8b6f-ab4a-4487-b578-3e7312a0eafb:*************",
                            "communications": {
                                "emails": ["<EMAIL>"],
                                "imAccounts": [
                                    {"id": "astuti.mishra", "label": "BBG"},
                                    {"id": "astuti.mishra", "label": "Live Chat"},
                                ],
                            },
                            "name": "Astuti Mishra",
                            "officialIdentifiers": {
                                "concatId": "********************",
                                "mifirId": "********************",
                                "mifirIdSubType": "CONCAT",
                                "mifirIdType": "N",
                            },
                            "personalDetails": {
                                "dob": "1969-06-09",
                                "firstName": "Astuti",
                                "lastName": "Mishra",
                                "nationality": ["IN"],
                            },
                            "retailOrProfessional": "N/A",
                            "sinkIdentifiers": {
                                "tradeFileIdentifiers": [
                                    {"id": "astuti.mishra", "label": "id"},
                                    {"id": "astuti.mishra", "label": "account"},
                                ]
                            },
                            "sourceKey": "s3://iris.uat.steeleye.co/flows/mymarket-universal-steeleye-person/steeleyeBlotter.mymarket.********.csv",
                            "structure": {"department": "Macro Desk", "role": "Macro Trader"},
                            "uniqueIds": [
                                "astuti.mishra",
                                "<EMAIL>",
                                "account:astuti.mishra",
                                "id:astuti.mishra",
                            ],
                        },
                    },
                    {
                        "types": ["TO"],
                        "value": {
                            "&id": "c8675ebe-abec-4c5d-9e1d-c6a1d792724c",
                            "&key": "AccountPerson:c8675ebe-abec-4c5d-9e1d-c6a1d792724c:*************",
                            "communications": {
                                "emails": [
                                    "<EMAIL>",
                                    "<EMAIL>",
                                ],
                                "imAccounts": [{"id": "u4pa3evjt", "label": "Slack"}],
                                "phoneNumbers": [
                                    {
                                        "dialingCode": "GB",
                                        "label": "MOBILE",
                                        "number": "+************",
                                    }
                                ],
                            },
                            "location": {
                                "homeAddress": {"address": "GB", "country": "GB"},
                                "officeAddress": {"address": "GB", "country": "GB"},
                            },
                            "name": "Matt Storey",
                            "officialIdentifiers": {
                                "branchCountry": "GB",
                                "concatId": "GB19440713MATT#STORE",
                                "mifirId": "GB19440713MATT#STORE",
                                "mifirIdSubType": "CONCAT",
                                "mifirIdType": "N",
                            },
                            "personalDetails": {
                                "dob": "1944-07-13",
                                "firstName": "Matt",
                                "lastName": "Storey",
                                "nationality": ["GB"],
                            },
                            "retailOrProfessional": "N/A",
                            "sinkIdentifiers": {
                                "tradeFileIdentifiers": [
                                    {"id": "matt.storey", "label": "account"},
                                    {"id": "pian", "label": "id"},
                                    {"id": "aferreira", "label": "id"},
                                ]
                            },
                            "uniqueIds": [
                                "account:matt.storey",
                                "id:pian",
                                "+************",
                                "<EMAIL>",
                                "id:aferreira",
                                "<EMAIL>",
                                "u4pa3evjt",
                            ],
                        },
                    },
                ],
            },
            "workflow": {"status": "UNRESOLVED", "assigneeId": None, "assigneeName": None},
            "&uniqueProps": ["<EMAIL>", "<EMAIL>"],
            "hitModel": "Message",
        }
    ]


@pytest.fixture()
def sample_surveillance_watch() -> SurveillanceWatch:
    watch = {
        "createdBy": "test",
        "createdByAdmin": "true",
        "createdOn": "2023-07-12T12:00:00",
        "executionDetails": {
            "lastExecution": "2023-07-12T12:00:00",
            "lastSuccessfulExecution": "2023-05-01T12:00:00",
        },
        "name": "Conduct Risk",
        "priority": "LOW",
        "query": {
            "name": "Conduct Risk",
            "description": "Conduct Risk",
            "kind": "BEHAVIOUR",
            "lexicaBehaviour": {
                "id": "ad47e12e-c586-4a89-8fc7-f2af218042f5",
                "name": "Conduct Risk",
                "languages": ["en", "fr", "nl", "it", "ja.kj", "zh.cn", "zh.tw", "af"],
            },
            "filter": {"flangVersion": "1.2", "chunks": []},
        },
        "queryType": "COMMUNICATIONS",
        "scheduleDetails": {"interval": 10, "recurrence": "CONTINUOUS"},
        "status": "ACTIVE",
        "type": "SCHEDULED",
    }
    return SurveillanceWatch(**watch)


@pytest.fixture()
def expected_result_chat_gpt_response():
    return {
        "hit": {
            "identifiers": {
                "onBehalfOf": "<EMAIL>",
                "domains": [
                    {"types": ["TO", "BCC"], "value": "steel-eye.com"},
                    {"types": ["FROM"], "value": "mail-eu.atlassian.net"},
                    {"types": ["BEHALF"], "value": "steeleye.atlassian.net"},
                ],
                "bccIds": ["<EMAIL>"],
                "toIds": ["<EMAIL>"],
                "localParts": [
                    {"types": ["TO", "BCC"], "value": "sam.taylor"},
                    {"types": ["BEHALF", "FROM"], "value": "confluence"},
                ],
                "allIds": [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                "fromId": "<EMAIL>",
                "allDomains": [
                    "mail-eu.atlassian.net",
                    "steel-eye.com",
                    "steeleye.atlassian.net",
                ],
            },
            "subject": "[Confluence] Product > tSurv - Fixed Income - Cross product abuse",
            "timestamps": {
                "timestampEnd": "2023-02-16T17:07:41Z",
                "timestampStart": "2023-02-16T17:07:32Z",
            },
            "body": {
                "text": {
                    "text": "Front-running is trading stock or any other financial asset by a "
                    "broker who has inside knowledge of a future transaction that is about"
                    " to affect its price substantially. A broker may also front-run based"
                    " on insider knowledge that their firm is about to issue a buy or sell "
                    "recommendation to clients that will almost certainly affect the price "
                    "of an asset.This exploitation of information that is not yet public is"
                    " illegal and unethical in almost all cases. Front-running is also "
                    "called tailgating.",
                    "type": "HTML",
                },
                "type": "HTML",
            },
        },
        "detected": "2023-07-07T06:01:32.891342+00:00",
        "detail": {
            "createdOn": "2023-03-09T16:58:54+00:00",
            "watchId": "698feb8b-d26f-4114-8bad-c0ab3bce2588",
            "watchName": "Market Abuse",
            "queryKind": "BEHAVIOUR",
            "queryName": "Market Abuse",
            "queryType": "COMMUNICATIONS",
        },
        "slug": "comms-s5899685",
        "hitModel": "Email",
        "highlights": [],
        "matchedLexica": ["front run customer", "front run order"],
        "matchedLexicaCategories": ["Market Abuse", "Market Abuse"],
        "copilotAnalysis": {
            "score": 8,
            "scoreRationale": "The email contains two terms 'front run customer' and 'front run "
            "order', which are indicative of potential market abuse. The email "
            "discusses the illegal and unethical nature of front-running, "
            "highlighting that the content of the email is not speculative.",
            "nextStep": "The email warrants further investigation.",
            "suggestedResolutionComment": "Refer to the company's Market Abuse policy and "
            "procedures and escalate the email to the Compliance"
            " Manager for further review.",
            "suggestedResolutionCategory": "POLICY_BREACH",
            "suggestedResolutionSubCategories": None,
            "otherRisks": None,
            "source": {
                "source": "AZURE OPENAI",
                "model": "gpt-4o",
                "version": "2023-03-15-preview",
            },
        },
    }


@pytest.fixture()
def mock_open_ai_response():
    return ChatCompletion(
        id="abc",
        created=1727699717,
        model="sample_model",
        object="chat.completion",
        usage=CompletionUsage(
            total_tokens=100,
            # Leaving these as zero as they are not relevant to the tests
            prompt_tokens=0,
            completion_tokens=0,
        ),
        choices=[
            Choice(
                message=ChatCompletionMessage(
                    content=json.dumps(
                        {
                            "score": 8,
                            "scoreRationale": "The email contains two terms 'front run customer' and 'front run order', which are indicative of potential market abuse. The email discusses the illegal and unethical nature of front-running, highlighting that the content of the email is not speculative.",
                            "nextStep": "The email warrants further investigation.",
                            "suggestedResolutionComment": "Refer to the company's Market Abuse policy and procedures and escalate the email to the Compliance Manager for further review.",
                            "suggestedResolutionCategory": "POLICY_BREACH",
                            "suggestedResolutionSubCategories": "MARKET_ABUSE",
                            "otherRisks": [],
                        }
                    ),
                    role="assistant",
                ),
                index=0,
                finish_reason="stop",
            )
        ],
    )

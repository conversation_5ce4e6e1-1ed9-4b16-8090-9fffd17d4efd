import json
from intelligence_core_tasks.comms_watch_enrich.alerts import (
    _generate_lexica_categories_and_terms,
    _remove_unneeded_lexica_from_hit_source,
    create_alerts_from_hits,
)
from pathlib import Path
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch
from typing import List

TEST_DATA_PATH = Path(__file__).parent.joinpath("data")


def get_surveillance_watch(watch_filename: str) -> SurveillanceWatch:
    sample_surveillance_watch = json.loads(TEST_DATA_PATH.joinpath(watch_filename).read_text())
    return SurveillanceWatch.validate(sample_surveillance_watch)


def get_surveillance_watch_from_execution(watch_filename: str) -> SurveillanceWatch:
    sample_surveillance_watch = json.loads(TEST_DATA_PATH.joinpath(watch_filename).read_text())[
        "watch"
    ]
    return SurveillanceWatch.validate(sample_surveillance_watch)


def get_alert_hits() -> List[dict]:
    filename = "alert_hits.ndjson"
    sample_alert_hits = [
        json.loads(line) for line in TEST_DATA_PATH.joinpath(filename).read_text().split("\n")
    ]
    return sample_alert_hits


def test_create_alerts_from_hits():
    surveillance_watch = get_surveillance_watch_from_execution(
        watch_filename="watch_execution.json"
    )
    surveillance_watch_execution_id = "02cadf80-cfe8-414a-93b1-9654ec552102"

    no_hits = create_alerts_from_hits(
        hits=[],
        surveillance_watch=surveillance_watch,
        surveillance_watch_execution_id=surveillance_watch_execution_id,
        project_behaviour_list=[],
    )
    assert no_hits == []

    hits = get_alert_hits()
    alerts = create_alerts_from_hits(
        hits=hits,
        surveillance_watch=surveillance_watch,
        surveillance_watch_execution_id=surveillance_watch_execution_id,
        project_behaviour_list=[],
    )

    assert alerts[0]["&parent"] == surveillance_watch_execution_id
    assert alerts[0]["workflow"] == {
        "status": "UNRESOLVED",
        "assigneeId": surveillance_watch.defaultAssigneeId,
        "assigneeName": surveillance_watch.defaultAssigneeName,
    }
    assert len(alerts[0]["matchedLexicaCategories"]) == 1
    assert alerts[0]["matchedLexicaCategories"] == ["Breach Awareness"]
    assert alerts[0]["matchedLexica"] == ["我们被发现"]

    assert len(alerts[1]["matchedLexicaCategories"]) == 2
    assert alerts[1]["matchedLexica"] == ["er is gdpr", "er is gdpr"]
    assert len(alerts[1]["matchedLexicaIds"]) == 2


def test_no_generate_lexica_categories_and_terms():
    surveillance_watch = get_surveillance_watch_from_execution(
        watch_filename="watch_execution.json"
    )

    matched_lexica = _generate_lexica_categories_and_terms(
        surveillance_watch=surveillance_watch, hit={}
    )
    assert matched_lexica == {
        "matchedLexica": [],
        "matchedLexicaCategories": [],
        "matchedLexicaIds": [],
    }

    analytics = {
        "analytics": {
            "lexica": [
                {
                    "behaviour": "Breach Awareness",
                    "termLanguage": "nl",
                    "term": "Test_term",
                    "termId": "id_123",
                }
            ]
        }
    }
    matched_lexica = _generate_lexica_categories_and_terms(
        surveillance_watch=surveillance_watch, hit=analytics
    )
    assert matched_lexica == {
        "matchedLexicaCategories": ["Breach Awareness"],
        "matchedLexica": ["Test_term"],
        "matchedLexicaIds": ["id_123"],
    }


def test_remove_unneeded_lexica_from_hit_source():
    hit = {
        "analytics": {
            "lexica": [
                {
                    "behaviourId": "4e967800-138c-40c9-9d37-1230b52092c2",
                    "behaviour": "Breach Awareness",
                    "termLanguage": "nl",
                    "term": "Test_term",
                    "termId": "id_1234",
                    "triggers": [
                        {
                            "trigger": "er is gdpr",
                            "triggerStartChar": 0,
                            "triggerEndChar": 10,
                            "zone_class": "signature",
                            "zone_value": 0.99,
                        },
                        {
                            "trigger": "er is gdpr",
                            "triggerStartChar": 15,
                            "triggerEndChar": 25,
                            "zone_class": "signature",
                            "zone_value": 0.01,
                        },
                        {"trigger": "er is gdpr", "triggerStartChar": 35, "triggerEndChar": 45},
                    ],
                },
                {
                    "behaviourId": "4e967800-138c-40c9-9d37-1230b52092c2",
                    "behaviour": "Breach Awareness",
                    "termLanguage": "en",
                    "term": "Test_term1",
                    "termId": "id_1234",
                },
                {
                    "behaviourId": "4e967800-138c-40c9-9d37-1230b52092c2",
                    "behaviour": "Breach Awareness",
                    "termLanguage": "nl",
                    "term": "Test_term2",
                    "termId": "id_1234",
                    "triggers": [
                        {
                            "trigger": "er is gdpr",
                            "triggerStartChar": 0,
                            "triggerEndChar": 10,
                            "zone_class": "signature",
                            "zone_value": 0.99,
                        },
                        {
                            "trigger": "er is gdpr",
                            "triggerStartChar": 15,
                            "triggerEndChar": 25,
                            "zone_class": "signature",
                            "zone_value": 0.99,
                        },
                    ],
                },
                {
                    "behaviourId": "4e967800-138c-40c9-9d37-1230b52092c2",
                    "behaviour": "Breach Awareness",
                    "termLanguage": "zh.cn",
                    "term": "Test_term3",
                    "termId": "id_1234",
                },
            ]
        }
    }
    surveillance_watch = get_surveillance_watch_from_execution(
        watch_filename="watch_execution.json"
    )

    hit_source = _remove_unneeded_lexica_from_hit_source(
        surveillance_watch=surveillance_watch, hit=hit, project_behaviour_list=[]
    )
    assert hit_source["analytics"]["lexica"] == [
        {
            "behaviourId": "4e967800-138c-40c9-9d37-1230b52092c2",
            "behaviour": "Breach Awareness",
            "termLanguage": "nl",
            "term": "Test_term",
            "termId": "id_1234",
            "triggers": [
                {
                    "trigger": "er is gdpr",
                    "triggerStartChar": 15,
                    "triggerEndChar": 25,
                    "zone_class": "signature",
                    "zone_value": 0.01,
                },
                {"trigger": "er is gdpr", "triggerStartChar": 35, "triggerEndChar": 45},
            ],
        },
        {
            "behaviourId": "4e967800-138c-40c9-9d37-1230b52092c2",
            "behaviour": "Breach Awareness",
            "termLanguage": "zh.cn",
            "term": "Test_term3",
            "termId": "id_1234",
        },
    ]

    watch_dict = surveillance_watch.dict()
    watch_dict["query"].pop("falsePositiveReduction")
    watch = SurveillanceWatch.validate(watch_dict)
    hit_source = _remove_unneeded_lexica_from_hit_source(
        surveillance_watch=watch, hit=hit, project_behaviour_list=[]
    )
    assert hit_source["analytics"]["lexica"] == hit["analytics"]["lexica"]


def test_remove_unneeded_lexica_from_hit_source_info_barrier():
    hit = {
        "analytics": {
            "lexica": [
                {
                    "behaviourId": "4e967800-138c-40c9-9d37-1230b52092c2",
                    "behaviour": "Breach Awareness",
                    "termLanguage": "nl",
                    "term": "Test_term",
                    "termId": "id_1234",
                    "triggers": [
                        {
                            "trigger": "er is gdpr",
                            "triggerStartChar": 0,
                            "triggerEndChar": 10,
                            "zone_class": "signature",
                            "zone_value": 0.99,
                        },
                        {
                            "trigger": "er is gdpr",
                            "triggerStartChar": 15,
                            "triggerEndChar": 25,
                            "zone_class": "signature",
                            "zone_value": 0.01,
                        },
                        {"trigger": "er is gdpr", "triggerStartChar": 35, "triggerEndChar": 45},
                    ],
                },
                {
                    "behaviourId": "4e967800-138c-40c9-9d37-1230b52092c2",
                    "behaviour": "Breach Awareness",
                    "termLanguage": "en",
                    "term": "Test_term1",
                    "termId": "id_1234",
                },
                {
                    "behaviourId": "ffff3e1e46c87c4a81e371e857c0e52f",
                    "behaviour": "Conduct Risk",
                    "termLanguage": "en",
                    "term": "Test_term2",
                    "termId": "id_1234",
                },
                {
                    "behaviourId": "4e967800-138c-40c9-9d37-1230b52092c2",
                    "behaviour": "Breach Awareness",
                    "termLanguage": "zh.cn",
                    "term": "Test_term3",
                    "termId": "id_1234",
                },
            ]
        }
    }
    surveillance_watch = get_surveillance_watch(watch_filename="info_barrier_watch.json")

    hit_source = _remove_unneeded_lexica_from_hit_source(
        surveillance_watch=surveillance_watch, hit=hit, project_behaviour_list=["Conduct Risk"]
    )

    assert hit_source["analytics"]["lexica"] == [
        {
            "behaviourId": "ffff3e1e46c87c4a81e371e857c0e52f",
            "behaviour": "Conduct Risk",
            "termLanguage": "en",
            "term": "Test_term2",
            "termId": "id_1234",
        },
    ]

    watch_dict = surveillance_watch.dict()
    watch_dict["query"].pop("falsePositiveReduction")
    watch = SurveillanceWatch.validate(watch_dict)

    hit_source = _remove_unneeded_lexica_from_hit_source(
        surveillance_watch=watch, hit=hit, project_behaviour_list=["Conduct Risk"]
    )
    assert hit_source["analytics"]["lexica"] == hit["analytics"]["lexica"]

from copilot_utils.schema import OpenApiClientMetrics
from pydantic import BaseModel, Field, validator
from se_enums.core import BaseStrEnum
from typing import Any


class PreProcessOutput(BaseModel):
    content: str = Field(...)
    document: dict[str, Any] = Field(...)
    feed_type: str = Field("")
    is_bloomberg: bool = Field(False)
    unique_id: str = Field(...)


class SeverityType(BaseStrEnum):
    NONE = "NONE"
    MEDIUM = "MEDIUM"
    CRITICAL = "CRITICAL"


class RiskAssessment(BaseModel):
    category: str
    severity: float
    severity_type: SeverityType
    blended_severity: float | None = Field(None)
    explanation: str
    recommendations: str
    trigger: dict | None = Field(None)
    raw_response: str | None = Field(None)  # To store the raw response in event of parsing failures

    @validator("recommendations", pre=True)
    def validate_recommendations(cls, value):
        if isinstance(value, list):
            return " ".join(value)
        return value or ""


class CommsNeuralMetrics(OpenApiClientMetrics):
    # Count metrics
    processed_email_count: int = Field(0)
    processed_message_count: int = Field(0)
    skipped_email_count: int = Field(0)
    skipped_message_count: int = Field(0)

    # Per assessment level metrics
    l1_count: int = Field(0)
    l2_count: int = Field(0)
    l3_count: int = Field(0)

    # Execution time metrics
    time_for_message_processing: float = Field(0.0)
    time_for_email_processing: float = Field(0.0)

    # Cache metrics
    time_for_cache_resolve: float = Field(0.0)
    cache_hit_count: int = Field(0)
    cache_miss_count: int = Field(0)

    def dict(self, *args, **kwargs):
        metric_dict = super().dict(*args, **kwargs)
        metric_dict = {k: round(v, 5) if "time" in k else v for k, v in metric_dict.items()}
        return metric_dict

import fsspec
import hashlib
import json
import random
from intelligence_core_tasks.comms_neural.config import TASK_CONFIG
from intelligence_core_tasks.comms_neural.schema import RiskAssessment, SeverityType
from intelligence_core_tasks.comms_neural.static import PROMPTS_HASH
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8


def create_remote_file_path(
    remote_folder: str, pre_processed_content: str, model: SteelEyeSchemaBaseModelES8
) -> str:
    prompt_hash = hashlib.sha256((pre_processed_content + PROMPTS_HASH).encode()).hexdigest()
    return f"{remote_folder}/{model.get_reference().name}___{prompt_hash}.json"


def assessment_is_cached(remote_file: str, fs: fsspec.AbstractFileSystem) -> bool:
    if TASK_CONFIG.NEURAL_DISCARD_CACHE:
        return False
    elif fs.exists(remote_file):
        return True
    else:
        return False


def de_dup_participants(data):
    """De-duplicates participants based on their unique identifier.

    This function processes a list of participant lists, where each participant is expected
    to have an identifier stored in the `value` dictionary under the key `&id`. The function
    removes duplicate participants based on this identifier
    and returns a list of unique participants.

    :param data: A list of lists containing participant dictionaries.
                 Each participant dictionary should have a `value` field with a
                 unique `&id` key.
    :return: A list of unique participants, or an empty list if the
                input is invalid or no participants have unique identifiers.
    """
    if not isinstance(data, list):
        return list()

    seen_list = list()
    de_dup_list = list()
    for element in data:
        if isinstance(element, list):
            for participant in element:
                participant_id = participant.get("value", {}).get("&id")

                if not participant_id:
                    continue

                if participant_id in seen_list:
                    continue
                else:
                    seen_list.append(participant_id)
                    de_dup_list.append(participant)

    return de_dup_list


def de_dup_detected_languages(data):
    """De-duplicates detected languages based on their language code.

    This function processes a list of lists containing detected language dictionaries, where each
    language is expected to have a unique `code` field. The function removes duplicate languages
    based on the `code` and returns a list of unique languages.

    :param data: A list of lists containing detected language dictionaries. Each dictionary should
                 have a `code` key representing the language code.
    :return: A list of unique detected languages, or an empty list if the input is invalid or no
             languages have unique codes.
    """
    if not isinstance(data, list):
        return list()

    seen_list = list()
    de_dup_list = list()
    for element in data:
        if isinstance(element, list):
            for language in element:
                language_code = language.get("code")

                if not language_code:
                    continue

                if language_code in seen_list:
                    continue
                else:
                    seen_list.append(language_code)
                    de_dup_list.append(language)

    return de_dup_list


def read_cached_assessment(fs: fsspec.AbstractFileSystem, cached_assessment_path: str) -> dict:
    """
    Read and parse a cached assessment JSON file from a filesystem, returning its contents.

    Attempts to read the specified cached assessment file. Returns an empty dictionary
    if the file cannot be read or parsed.

    :param fs: File system where the file_path is present
    :param cached_assessment_path: Assessment file path
    :return: Parsed content as a dictionary. Contains 2 keys `document` and `assessments`
    """
    content = {}
    try:
        with fs.open(cached_assessment_path, "r") as f:
            content = json.loads((f.read()))
    except Exception:
        pass

    return content


def align_severity_score_with_severity_type(
    risk: RiskAssessment,
    max_threshold_dict: dict[str, dict[str, float | int]],
    level: int,
) -> RiskAssessment:
    """
    Adjusts the severity score of a RiskAssessment object based on its severity type and level.

    The function updates the `severity` field of the provided `risk` object using the
    following rules:
      - If the severity type is NONE, the severity is reduced to the minimum of its current
        value and a random value between 2 and 5.
      - If the severity type is MEDIUM, the severity is raised to at least 0.5 above the
        configured or default threshold value for the given level.
      - If the severity type is CRITICAL and the category is not "FAILURE", the severity
        is increased to at least 0.5 above the threshold for CRITICAL risks, capped at 10.

    Threshold values are retrieved from `max_threshold_dict`, which contains per-level
    and per-severity-type mappings. If no threshold is found, default values from `TASK_CONFIG`
    are used.

    :param risk: The risk assessment object to update. This contains the neural response
    :param max_threshold_dict: Dictionary containing max threshold values for all
                               levels across all watches
    :param level: Neural level

    :return: The updated risk assessment with adjusted severity.
    """
    if risk.severity_type == SeverityType.NONE:
        risk.severity = min(risk.severity, random.randint(2, 5))
    elif risk.severity_type == SeverityType.MEDIUM:
        default_value = getattr(TASK_CONFIG, f"MIN_L{level}_FILTER", 6)
        risk.severity = max(
            risk.severity,
            max_threshold_dict.get(f"l{level}Score", {}).get(SeverityType.MEDIUM, default_value)
            + 0.5,
        )
    elif risk.severity_type == SeverityType.CRITICAL and risk.category != "FAILURE":
        risk.severity = max(
            risk.severity,
            min(
                max_threshold_dict.get(f"l{level}Score", {}).get(SeverityType.CRITICAL, 9) + 0.5, 10
            ),
        )

    return risk

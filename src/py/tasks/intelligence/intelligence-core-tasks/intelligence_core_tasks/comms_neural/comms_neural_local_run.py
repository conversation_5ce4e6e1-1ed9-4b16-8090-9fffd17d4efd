import logging
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from intelligence_core_tasks.comms_neural.main import run_comms_neural

logger = logging.getLogger(__name__)

if __name__ == "__main__":
    workflow = WorkflowFieldSet(
        trace_id="comms_neural_test_1",
        name="comms_neural",
        stack="dev-shared-2",
        tenant="dev-shared-2",
        start_timestamp=datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            tenants=["irises8"],
            start_date_time="2024-10-10T00:00:00",
            end_date_time="2024-10-10T23:59:59",
        )
    )
    task = TaskFieldSet(name="comms_neural", version="latest", success=False)
    task_input = AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
    result = run_comms_neural(aries_task_input=task_input)
    logger.info(result)

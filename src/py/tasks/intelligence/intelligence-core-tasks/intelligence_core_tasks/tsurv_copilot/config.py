from pydantic import BaseSettings, Field


class TaskConfig(BaseSettings):
    BATCH_SIZE: int = Field(16)
    BATCH_SIZE_CHARS: int = Field(10**5)
    COGNITO_AUTH_URL: str
    COGNITO_CLIENT_ID: str
    COGNITO_CLIENT_SECRET: str
    DATA_EVENTS_TOPIC: str = Field("events", description="name of topic")
    ELASTIC_API_KEY: str
    ELASTIC_HOST: str
    KAFKA_REST_PROXY_URL: str = Field("")
    MESSAGE_CONTEXT_SEARCH_SIZE: int = Field(10, max=1000)
    RIC_BATCH_SIZE: int = Field(500)
    TASK_NAME: str = Field("tsurv-copilot")
    TASK_VERSION: str = Field("1")


TASK_CONFIG = TaskConfig()

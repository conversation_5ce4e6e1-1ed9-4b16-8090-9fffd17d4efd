import copy
import json
import logging
import os
import pandas as pd
from copilot_utils.client import SeOpenApiClient
from copilot_utils.config import OPENAI_CONFIG
from copilot_utils.static import OpenAIResponse
from intelligence_core_tasks.tsurv_copilot.prompts import TradeSurveillancePromptFactory
from intelligence_core_tasks.tsurv_copilot.schema import TSurvCopilotMetrics
from intelligence_core_tasks.tsurv_copilot.static import (
    AIResponseCols,
    CopilotAnalysisSourceFields,
)
from se_elastic_schema.static.case import ResolutionCategoryMarEnum, ResolutionSubCategoryMarEnum
from se_elastic_schema.static.surveillance import MarketAbuseReportType
from typing import List, Optional

logger = logging.getLogger(__name__)
openai_logger = logging.getLogger("openai")
openai_logger.setLevel(logging.WARNING)  # preventing spam of successful openai logs


def alerts_copilot(
    alerts_df: pd.DataFrame,
    market_abuse_alert_type: str,
    metrics: TSurvCopilotMetrics,
    tenant: str,
    workflow: str,
    local_prompt_file_path: Optional[str] = None,
    executions_df: Optional[pd.DataFrame] = pd.DataFrame(),
) -> List[Optional[dict]]:
    """Run Copilot process to call chatGPT for every behavioural alert for
    eligible tenants. For each alert:

    1. Check if the tenant is eligible for Copilot Processing and alert is of type Behavioural
    2. Construct the prompt
    3. Call Azure OpenAI chatGTP and get the response
    4. Format the response and add it to the field copilotAnalysis of the
    MarketAbuseScenarioTag model
    """
    # initialize Azure OpenAI client
    client = SeOpenApiClient()

    try:
        # run Copilot
        batch_size = int(os.environ.get("COPILOT_BATCH_SIZE", 10))
        final_alerts = []
        alerts = alerts_df.to_dict(orient="records")
        for i in range(0, len(alerts), batch_size):
            batch = alerts[i : i + batch_size]
            try:
                batch_result = _copilot_processing(
                    client=client,
                    tenant=tenant,
                    workflow=workflow,
                    alerts=copy.deepcopy(batch),
                    market_abuse_alert_type=market_abuse_alert_type,
                    executions_df=executions_df,
                    metrics=metrics,
                    local_prompt_file_path=local_prompt_file_path,
                )
                final_alerts.extend(batch_result)
            except Exception as e:
                logger.error(f"Error in batch{i}. Copilot failed. Error:{e}")
                final_alerts.extend([None] * batch_size)

        return final_alerts
    except Exception as e:
        logger.exception(f"Copilot Processing failed due to error: {e}")
        return [None] * alerts_df.shape[0]
    finally:
        # Collate LLM OMA Metrics
        metrics.llm_success_calls = client.metrics.llm_success_calls
        metrics.llm_retries_calls = client.metrics.llm_retries_calls
        metrics.llm_failed_calls = client.metrics.llm_failed_calls
        metrics.llm_billed_tokens = client.metrics.llm_billed_tokens
        metrics.llm_input_tokens = client.metrics.llm_input_tokens
        metrics.llm_output_tokens = client.metrics.llm_output_tokens
        metrics.llm_call_latency = client.metrics.llm_call_latency


def _copilot_processing(
    client: SeOpenApiClient,
    alerts: List[dict],
    market_abuse_alert_type: str,
    metrics: TSurvCopilotMetrics,
    local_prompt_file_path: Optional[str],
    tenant: str,
    workflow: str,
    executions_df: Optional[pd.DataFrame] = pd.DataFrame(),
) -> List[Optional[dict]]:
    """For each alert construct the prompt and then call Azure OpenAI chatGPT
    asynchronously.

    :param alerts: List[dict]
    :return: List[dict]
    """
    logger.info("Starting Copilot Processing")
    openai_config = []
    prompt_factory = TradeSurveillancePromptFactory(
        market_abuse_alert_type=MarketAbuseReportType(market_abuse_alert_type),
        executions_df=executions_df,
    )
    for alert in alerts:
        try:
            prompt = prompt_factory.get_prompt_generator()(record=alert)
        except Exception as e:
            prompt = ""
            logger.warning(
                f"Error creating prompt for "
                f"Alert ID: {alert.get('scenarioId')} , slug: {alert.get('slug')}. "
                f"Error: {str(e)}"
            )

        openai_config.append(dict(alert=alert, prompt=prompt))
        if local_prompt_file_path:
            with open(local_prompt_file_path, "a") as f:
                f.write(
                    f"Alert ID: {alert.get('scenarioId')} , slug: {alert.get('slug')}"
                    f"\n{prompt}\n\n\n"
                )
    response_list = _call_open_ai(
        client=client,
        openai_config=openai_config,
        metrics=metrics,
        tenant=tenant,
        workflow=workflow,
    )
    return response_list  # type: ignore


def prepare_prompt(client: SeOpenApiClient, prompt: str) -> list[dict[str, str]]:
    """
    Constructs a list of prompt dictionaries for the OpenAI API, tailored
    for trade surveillance analysis.

    This function combines a predefined system message, a specific output
    format instruction, and a user-provided prompt into a structured list
    of messages suitable for interaction with a language model. It utilizes
    the `construct_prompt_with_role` method of the provided `SeOpenApiClient`
    to create each message with its designated role.

    :param client: An instance of the `SeOpenApiClient` used to construct
                   prompt dictionaries with role assignments.
    :param prompt: The core user-provided prompt string containing the
                   specific details or question for trade surveillance analysis.
                   This will be assigned the "user" role.
    :returns: A list of dictionaries, where each dictionary represents a
              message in the prompt sequence with "role" and "content" keys.
              The order of the messages in the list is: system message,
              output format instruction, and the user-provided prompt.
    """
    return [
        client.construct_prompt_with_role(
            role="system", prompt_content=TradeSurveillancePromptFactory.PROMPT_SYSTEM_MESSAGE
        ),  # type: ignore[list-item]
        client.construct_prompt_with_role(
            role="user", prompt_content=TradeSurveillancePromptFactory.OUTPUT_PROMPT
        ),  # type: ignore[list-item]
        client.construct_prompt_with_role(role="user", prompt_content=prompt),  # type: ignore[list-item]
    ]


def _parse_llm_response(
    client: SeOpenApiClient,
    metrics: TSurvCopilotMetrics,
    llm_response_list: list[OpenAIResponse],
) -> list[dict | None]:
    """Call Azure OpenAI ChatGPT with the given prompt and communication and
    gets the json response within the completion response. max_tokens=16000
    because that is the max that the model(specified as engine below) allows.

    :param llm_response_list: Response object obtained from the generic client
    :param client: SeOpenApiClient
    :param metrics: TSurvCopilotMetrics
    :return: dict
    """
    copilot_analysis_list: list[dict | None] = []
    try:
        completion_list = client.parse_content_from_response(response_list=llm_response_list)
        for completion in completion_list:
            if not completion:
                logger.error("Received empty response from copilot_analysis")
                copilot_analysis_list.append(None)
                continue

            result = json.loads(completion)
            copilot_analysis = _validate_and_enrich_result(value=result)
            copilot_analysis_list.append(copilot_analysis)
            metrics.enriched_trades_alerts_count += 1
    except Exception as e:
        logger.error(f"LLM result parsing error:{e}")

    return copilot_analysis_list


def _call_open_ai(
    client: SeOpenApiClient,
    openai_config: List[dict],
    metrics: TSurvCopilotMetrics,
    tenant: str,
    workflow: str,
):
    """Calls asynchronously the method to call OpenAI ChatGPT.

    :param openai_config: List[dict]
    :return: List[dict]
    """
    prompt_list = []
    for idx, config in enumerate(openai_config):
        prompt = config.get("prompt", "")
        prompt_list.append(prepare_prompt(client=client, prompt=prompt))

    llm_results = client.call_open_ai(prompt_list=prompt_list, tenant=tenant, workflow=workflow)  # type: ignore[arg-type]

    parsed_result = _parse_llm_response(
        client=client, metrics=metrics, llm_response_list=llm_results
    )

    return parsed_result


def _validate_and_enrich_result(value: dict) -> dict:
    """Validates the source dict to its corresponding schema enums and adds
    additional field 'source'.

    :param value: dict
    :return: dict
    """
    # remove extra columns from AI response
    required_keys = [
        val_
        for key_, val_ in AIResponseCols.__dict__.items()
        if not key_.startswith("__") and isinstance(val_, str)
    ]
    value = {key: value for key, value in value.items() if key in required_keys}
    # score is a mandatory column according to schema. So setting default 0
    value.setdefault(AIResponseCols.SCORE, 0)

    conversion_config = [
        {
            "source_field": AIResponseCols.SUGGESTED_RESOLUTION_CATEGORY,
            "schema_enum": ResolutionCategoryMarEnum,
            "schema_type": "string",
            "default_value": None,
        },
        {
            "source_field": AIResponseCols.SUGGESTED_RESOLUTION_SUB_CATEGORIES,
            "schema_enum": ResolutionSubCategoryMarEnum,
            "schema_type": "List",
            "default_value": None,
        },
    ]
    value = _str_to_enum(value=value, conversion_config=conversion_config)
    if isinstance(value.get("otherRisks"), list):
        # sometimes response from chatGPT has empty list [] but it is an Optional[str] as per schema
        value["otherRisks"] = value["otherRisks"][0] if value.get("otherRisks") else None
    value["source"] = {
        CopilotAnalysisSourceFields.SOURCE: "AZURE OPENAI",
        CopilotAnalysisSourceFields.MODEL: OPENAI_CONFIG.OPENAI_API_MODEL,
        CopilotAnalysisSourceFields.VERSION: OPENAI_CONFIG.OPENAI_API_VERSION,
    }
    return value


def _str_to_enum(value: dict, conversion_config: List[dict]) -> dict:
    """Validates and converts the string values to schema enums.

    :param value: dict
    :param conversion_config: List[dict]
    :return: dict
    """
    for config in conversion_config:
        suggested_value = value.get(config["source_field"])
        if not suggested_value:
            value[config["source_field"]] = config["default_value"]
        elif config["schema_type"] == "List":
            allowed_values = config["schema_enum"].__dict__["_member_names_"]
            if not isinstance(value[config["source_field"]], list):
                value[config["source_field"]] = [value[config["source_field"]]]
            value[config["source_field"]] = [
                getattr(config["schema_enum"], x).value
                for x in value[config["source_field"]]
                if x in allowed_values
            ]
            if not value[config["source_field"]]:
                # check for empty list
                value[config["source_field"]] = None
        else:
            if isinstance(suggested_value, list):
                suggested_value = suggested_value[0]
            if not hasattr(config["schema_enum"], suggested_value):
                if isinstance(suggested_value, str) and hasattr(
                    config["schema_enum"], suggested_value.upper()
                ):
                    value[config["source_field"]] = getattr(
                        config["schema_enum"], suggested_value.upper()
                    ).value
                elif isinstance(suggested_value, str) and hasattr(
                    config["schema_enum"], suggested_value.replace(" ", "_").upper()
                ):
                    value[config["source_field"]] = getattr(
                        config["schema_enum"], suggested_value.replace(" ", "_")
                    ).value
                else:
                    value[config["source_field"]] = config["default_value"]
            else:
                value[config["source_field"]] = getattr(
                    config["schema_enum"], suggested_value
                ).value
    return value

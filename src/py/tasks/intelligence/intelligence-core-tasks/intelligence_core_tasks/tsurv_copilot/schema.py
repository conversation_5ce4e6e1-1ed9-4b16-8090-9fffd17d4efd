from copilot_utils.schema import OpenApiClientMetrics
from pydantic import BaseModel, Field, root_validator
from se_elastic_schema.static.surveillance import MarketAbuseReportType


class MarketAbuseEnrichmentInput(BaseModel):
    alert_type: MarketAbuseReportType | None = Field(
        None, description="Alert type, used on mar_wrapper algos"
    )
    scenario_tag_file_uri: str = Field(
        ..., description="Path to the file with MarketAbuseScenarioTag alerts"
    )
    watch_execution_id: str | None = Field(
        None, description="Watch execution ID, used on mar_wrapper algos"
    )
    watch_id: str | None = Field(None, description="Watch ID, used on mar_wrapper algos")
    watch_metadata_file_uri: str | None = Field(
        None, description="Path to watch metadata, used on modernized algos"
    )

    class Config:
        extra = "ignore"

    @root_validator
    def validate_input(cls, values):
        if not values.get("watch_metadata_file_uri"):
            if not (
                values.get("watch_execution_id")
                and values.get("watch_id")
                and values.get("alert_type")
            ):
                raise ValueError("watch_execution_id, watch_id and alert_type are mandatory")
        return values


class TSurvCopilotMetrics(OpenApiClientMetrics):
    input_count: int = Field(0)
    skipped_count: int = Field(0)
    enriched_trades_alerts_count: int = Field(0)

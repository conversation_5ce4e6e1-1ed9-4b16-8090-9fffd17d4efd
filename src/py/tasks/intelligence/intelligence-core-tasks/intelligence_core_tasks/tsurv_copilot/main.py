# mypy: disable-error-code="attr-defined, arg-type"

import addict
import fsspec
import json
import logging
import numpy as np
import pandas as pd
from addict import Dict
from aries_io_event.app_metric import AppMetricFieldSet
from aries_io_event.io_param import IOParamFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from copilot_utils.client import SeOpenApiClient
from intelligence_core_tasks.tsurv_copilot.config import TASK_CONFIG
from intelligence_core_tasks.tsurv_copilot.copilot_alerts import alerts_copilot
from intelligence_core_tasks.tsurv_copilot.schema import (
    AlertsContext,
    MarketAbuseEnrichmentInput,
    TSurvCopilotMetrics,
)
from intelligence_core_tasks.tsurv_copilot.static import (
    ALERT_TYPE_EXECUTION_FIELD_MAPPING,
)
from intelligence_core_tasks.tsurv_copilot.utils import (
    Layering<PERSON><PERSON><PERSON>,
    MarketDataContextBuilder,
    create_copilot_update_ndjson,
    get_execution_ids,
    get_executions,
    send_kafka_event,
)
from mar_utils.algorithms.common.surveillance_watches import get_watch_filters
from mar_utils.apply_strategy_utils.apply_strategy_utils import get_watch_context_from_file
from mar_utils.es_filters.date_filters import get_lookback_and_event_day
from mar_utils.static.common import COPILOT_SUPPORTED_ALGOS
from pathlib import Path
from se_data_lake.lake_path import get_prefixed_ingest_cloud_lake_path_for_task
from se_elastic_schema.components.mar.strategy.layering_v2.thresholds import LayeringV2Thresholds
from se_elastic_schema.models import Order
from se_elastic_schema.models.tenant.surveillance.market_abuse_scenario_tag import (
    MarketAbuseScenarioTag,
)
from se_enums.elastic_search import EsActionEnum
from se_es_utils.slim_record_handler import SlimRecordHandler
from se_io_utils.batching_utils import batching_generator
from se_io_utils.tempfile_utils import tmp_directory
from se_market_data_utils.client import MarketDataAPI
from se_schema.static.surveillance import MarketAbuseReportType
from surveillance_utils.watch_execution_handling import fetch_watch_execution_record
from typing import Tuple

logger = logging.getLogger(__name__)
elastic_logger = logging.getLogger("elasticsearch")
elastic_logger.setLevel(logging.WARNING)  # preventing spam of successful elastic logs

RECORD_HANDLER = SlimRecordHandler()


def process_event(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    # Validate aries_task_input
    input_task = MarketAbuseEnrichmentInput.validate(aries_task_input.input_param.params)
    tenant: str = aries_task_input.workflow.tenant

    if input_task.watch_metadata_file_uri:
        context = get_watch_context_from_file(file_path=input_task.watch_metadata_file_uri)

        surveillance_watch_execution = fetch_watch_execution_record(
            context.watch_execution_id, tenant, RECORD_HANDLER
        )
        alert_type = surveillance_watch_execution.watch.query.marketAbuseReportType

        replace_params = addict.Dict(
            watch_execution_id=context.watch_execution_id,
            alert_type=alert_type,
            watch_id=context.watch_id,
        )
        # Updating task inputs with watch metadata
        aries_task_input.input_param.params.update(replace_params)
        input_task = MarketAbuseEnrichmentInput.validate(aries_task_input.input_param.params)

    watch_id = input_task.watch_id
    watch_execution_id = input_task.watch_execution_id
    es_index = MarketAbuseScenarioTag.get_elastic_index_alias(tenant=tenant)
    alert_type = input_task.alert_type

    if alert_type not in COPILOT_SUPPORTED_ALGOS:
        raise ValueError(f"Alert type {alert_type} is not supported for copilot")

    metrics = TSurvCopilotMetrics()
    (
        downloaded_file_path,
        alerts_file_uri,
        local_prompt_file_path,
    ) = download_alerts_and_get_prompt_local_path(aries_task_input)
    output_params, output_file_path = get_output_params_and_path(
        aries_task_input=aries_task_input,
        alerts_file_uri=alerts_file_uri,
        watch_id=watch_id,
        watch_execution_id=watch_execution_id,
    )
    aries_task_input_name = aries_task_input.task.name
    market_data_client = MarketDataAPI()

    # Initialize the SeOpenApiClient client here with force_override
    # to reset it across multiple task run in the same worker
    SeOpenApiClient(force_override=True)

    for temp_file_path, alerts_list_str in batching_generator(
        [downloaded_file_path],
        output_file_path,
        TASK_CONFIG.BATCH_SIZE,
        TASK_CONFIG.BATCH_SIZE_CHARS,
    ):
        batch_start = metrics.input_count
        try:
            if input_task.watch_metadata_file_uri:
                alerts_list = [json.loads(alert_str) for alert_str in alerts_list_str]
            else:
                # Nasty code needed due to output for mar_wrapper
                alerts_list = [
                    json.loads(json.loads(alert_str))
                    for alert_str in alerts_list_str
                    if isinstance(json.loads(alert_str), str)
                ]

            executions_df = pd.DataFrame()
            alerts_df = pd.DataFrame(alerts_list)
            alerts_df = alerts_df[alerts_df["tagText"].notnull()].reset_index(drop=True)
            metrics.input_count += alerts_df.shape[0]
            logger.info(f"Processing records {batch_start} through {metrics.input_count}")
            tag_text_expanded_df = pd.json_normalize(alerts_df["tagText"].apply(json.loads))
            alerts_df_expanded = pd.concat([alerts_df, tag_text_expanded_df], axis=1).drop(
                columns=["tagText"]
            )

            if alert_type in ALERT_TYPE_EXECUTION_FIELD_MAPPING.keys():
                execution_ids = get_execution_ids(alerts_df_expanded, alert_type)
                executions_df = get_executions(
                    unique_ids=execution_ids,
                    es_client=RECORD_HANDLER.es_repo,
                    index=Order.get_elastic_index_alias(tenant=tenant),
                ).set_index("&key")

            if alert_type == MarketAbuseReportType.LAYERING_V2:
                # Fetch surveillance_watch_execution and surveillance_watch
                surveillance_watch_execution = fetch_watch_execution_record(
                    watch_execution_id, tenant, RECORD_HANDLER
                )
                surveillance_watch = surveillance_watch_execution.watch
                execution_start = surveillance_watch_execution.executedOn
                thresholds = surveillance_watch.query.thresholds

                # Using proper filters to apply to Layering Query

                oldest_timestamp, look_back_period_ts, event_day_starting_ts = (
                    get_lookback_and_event_day(
                        tenant=tenant,
                        thresholds=thresholds,
                        surv_watch=surveillance_watch,
                        execution_start=execution_start,
                        record_handler=RECORD_HANDLER,
                    )
                )

                _, filters = get_watch_filters(
                    surv_watch=surveillance_watch,
                    execution_start=execution_start,
                    exclusion_terms=[],
                    look_back_period_ts=look_back_period_ts,
                    run_as_of=None,
                )

                context = Dict(
                    filters=filters,
                    thresholds=LayeringV2Thresholds(**json.loads(thresholds))
                    if thresholds
                    else Dict(),
                    tenant=tenant,
                    look_back_period_ts=look_back_period_ts,
                )
                fake_audit = Dict(
                    context=context, strategy_name=alert_type, es_client=Dict(tenant=tenant)
                )

                executions_df = LayeringQueries(context=context, audit=fake_audit).get_rics(
                    meta_keys=execution_ids,
                    alerts_df_expanded=alerts_df_expanded,
                    executions_df=executions_df,
                )

            # create order data column based off of what
            # timestamp is available for use downstream

            timestamp_column = "timestamps.orderSubmitted"
            if timestamp_column not in executions_df.columns:
                executions_df[timestamp_column] = pd.NA
                # wash trades alerts sometimes does not have orderSubmitted
                # so we fallback to tradingDateTime
                if (
                    "transactionDetails.tradingDateTime" in executions_df.columns
                    and alert_type == MarketAbuseReportType.WASH_TRADING
                ):
                    timestamp_column = "transactionDetails.tradingDateTime"

            executions_df["orderDate"] = pd.to_datetime(
                executions_df[timestamp_column], errors="coerce"
            ).dt.strftime("%Y-%m-%d")

            builder = MarketDataContextBuilder(
                alert_type=alert_type,
                executions_df=executions_df,
                market_data_client=market_data_client,
                alerts_df_expanded=alerts_df_expanded,
            )

            if alert_type in builder.market_data_function_map:
                market_data = builder.get_market_data()
                executions_df = builder.get_executions_df
                alerts_df_expanded = builder.get_alerts_df_expanded
                context = AlertsContext(
                    market_data=market_data,
                )
            else:
                context = AlertsContext()

            copilot_analysis_list = alerts_copilot(
                local_prompt_file_path=local_prompt_file_path,
                tenant=tenant,
                workflow=aries_task_input.workflow.name,
                alerts_df=alerts_df_expanded,
                executions_df=executions_df,
                alert_context=context,
                market_abuse_alert_type=alert_type,
                metrics=metrics,
            )

            alerts_df["copilotAnalysis"] = copilot_analysis_list
            alerts_df = alerts_df.fillna(np.nan).replace(np.nan, None)
            alerts_data = alerts_df.to_dict(orient="records")
            create_copilot_update_ndjson(
                path=temp_file_path, es_index=es_index, records=alerts_data, metrics=metrics
            )
            batch_start += 1
        except Exception as e:
            logger.exception(f"Failed to process batch {batch_start}: %s", e)
            metrics.skipped_count += len(alerts_list_str)
            batch_start += 1

    app_metrics = update_app_metrics(
        aries_task_input_name=aries_task_input_name,
        metrics=metrics,
    )
    output_params.params["file_uri"] = output_file_path

    if aries_task_input.input_param.params.get("orchestrator_metadata"):
        output_params.params["orchestrator_metadata"] = aries_task_input.input_param.params[
            "orchestrator_metadata"
        ]
        output_params.params["orchestrator_metadata"]["produce_callback_event"] = True
    send_kafka_event(output_param=output_params, workflow=aries_task_input.workflow)
    write_prompts_to_s3(
        local_prompt_file_path=local_prompt_file_path, aries_task_input=aries_task_input
    )
    Path(downloaded_file_path).unlink()

    # Removing reference to current instance of `SeOpenApiClient`
    # so that current metrics is not cumulatively added to another task run
    SeOpenApiClient.destroy()

    return AriesTaskResult(output_param=output_params, app_metric=app_metrics)


def download_alerts_and_get_prompt_local_path(
    aries_task_input: AriesTaskInput,
) -> Tuple[str, str, str]:
    """Get paths required for processing.

    :param aries_task_input:
    :return:
    """
    market_abuse_enrichment_input = MarketAbuseEnrichmentInput.parse_obj(
        aries_task_input.input_param.params
    )
    alerts_file_uri = market_abuse_enrichment_input.scenario_tag_file_uri
    tmp_storage = tmp_directory().as_posix()
    local_file_path = (
        Path(tmp_storage).joinpath(f"alerts_{aries_task_input.workflow.trace_id}.ndjson").as_posix()
    )
    local_prompt_path = (
        Path(tmp_storage).joinpath(f"prompt_{aries_task_input.workflow.trace_id}.txt").as_posix()
    )
    fs, _, _ = fsspec.get_fs_token_paths(alerts_file_uri)
    fs.get(alerts_file_uri, local_file_path)
    return local_file_path, alerts_file_uri, local_prompt_path


def write_prompts_to_s3(aries_task_input: AriesTaskInput, local_prompt_file_path: str) -> None:
    """Write prompts to S3."""
    prompt_s3_path = (
        get_prefixed_ingest_cloud_lake_path_for_task(
            workflow_name=aries_task_input.workflow.name,
            task_name=aries_task_input.task.name,
            task_io_params=aries_task_input.input_param.params,
            file_uri=aries_task_input.input_param.params["scenario_tag_file_uri"],
            workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
            workflow_trace_id=aries_task_input.workflow.trace_id,
        )
        + "_prompts.txt"
    )
    fs, _, _ = fsspec.get_fs_token_paths(prompt_s3_path)
    logger.info(f"Writing prompts to {prompt_s3_path}")
    with open(local_prompt_file_path, "rb") as input_obj:
        with fs.open(prompt_s3_path, "wb") as output_obj:
            output_obj.write(input_obj.read())


def get_output_params_and_path(
    aries_task_input: AriesTaskInput, alerts_file_uri: str, watch_execution_id: str, watch_id: str
) -> Tuple[IOParamFieldSet, str]:
    """Output params required for processing.

    :param aries_task_input:
    :param alerts_file_uri:
    :param watch_execution_id:
    :param watch_id:
    :return:
    """
    output_filepath = (
        get_prefixed_ingest_cloud_lake_path_for_task(
            workflow_name=aries_task_input.workflow.name,
            task_name=aries_task_input.task.name,
            task_io_params=aries_task_input.input_param.params,
            file_uri=aries_task_input.input_param.params["scenario_tag_file_uri"],
            workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
            workflow_trace_id=aries_task_input.workflow.trace_id,
        )
        + "copilot_analysis.ndjson"
    )
    output_params = IOParamFieldSet(
        params=addict.Dict(
            file_uri=alerts_file_uri,
            data_model=MarketAbuseScenarioTag.get_reference().get_qualified_reference(),
            es_action=EsActionEnum.UPDATE,
            watch_execution_id=watch_execution_id,
            watch_id=watch_id,
        )
    )
    return output_params, output_filepath


def update_app_metrics(
    aries_task_input_name: str,
    metrics: TSurvCopilotMetrics,
) -> AppMetricFieldSet:
    """Update app metrics.

    :param aries_task_input_name:
    :param metrics:
    :return:
    """
    app_metrics = AppMetricFieldSet(
        metrics={
            "custom": {
                aries_task_input_name: {
                    "input_count": metrics.input_count,
                    "skipped_count": metrics.skipped_count,
                    "enriched_trades_alerts_count": metrics.enriched_trades_alerts_count,
                    "llm_billed_tokens": metrics.llm_billed_tokens,
                    "llm_input_tokens": metrics.llm_input_tokens,
                    "llm_output_tokens": metrics.llm_output_tokens,
                    "llm_call_latency": metrics.llm_call_latency,
                    "llm_failed_calls": metrics.llm_failed_calls,
                    "llm_retries_calls": metrics.llm_retries_calls,
                    "llm_success_calls": metrics.llm_success_calls,
                }
            }
        }
    )
    logger.info(json.dumps(app_metrics.dict()))
    return app_metrics


def run_tsurv_copilot(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    return process_event(aries_task_input)

# ruff: noqa: E501
# mypy: disable-error-code="attr-defined"
import inspect
import pandas as pd
from indict import Indict
from intelligence_core_tasks.tsurv_copilot.static import (
    CATEGORY_SUB_CATEGORY_MAP,
    AIResponseCols,
    CategoryEvaluationTypeValues,
    DfColumns,
    FlowValues,
    MarketDataEvaluationType,
    PromptFields,
    RawPromptTSurv,
)
from intelligence_core_tasks.tsurv_copilot.utils import prettify_number
from market_abuse_algorithms.data_source.repository.market_data.client import MarketDataClient
from market_abuse_algorithms.data_source.repository.market_data.utils import (
    get_bid_or_ask_cols,
    get_nearest_tick_data_from_submitted,
)
from market_abuse_algorithms.data_source.static.market_data.refinitiv import RefinitivColumnsEnum
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_data_utils.schema.refinitiv import RefinitivExtractColumns
from se_elastic_schema.static.case import ResolutionCategoryMarEnum
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.surveillance import MarketAbuseReportType
from typing import Optional

DATE_FORMAT = "%d-%b-%y"
TIME_FORMAT = "%d-%b-%y %H:%M:%S"


def get_field_value(field_name, record):
    return Indict(obj=record).flatten().obj.get(field_name)


def get_tick_data(order_book_data, time_submitted, after_flag=False):
    return get_nearest_tick_data_from_submitted(
        market_data=order_book_data, order_time_submitted=time_submitted, after_flag=after_flag
    )


def get_volumes(tick_data, bid_col, ask_col, levels):
    bid_volume = get_bid_or_ask_cols(market_data=tick_data, refinitiv_col_bid_ask=bid_col).iloc[
        :levels
    ]
    ask_volume = get_bid_or_ask_cols(market_data=tick_data, refinitiv_col_bid_ask=ask_col).iloc[
        :levels
    ]
    return ", ".join(bid_volume.astype(str)), ", ".join(ask_volume.astype(str))


class TradeSurveillancePromptFactory:
    PROMPT_SYSTEM_MESSAGE = inspect.cleandoc(
        """You are the Chief Compliance Officer of a Financial Markets Trading Firm.
    You can access all Order & Trade information via your Trade Surveillance platform."""
    )
    OUTPUT_PROMPT = inspect.cleandoc(
        f"""I would like you to perform a compliance review of the Alert described below. Please respond strictly in RFC8259 compliant JSON format with the below fields:  
    - {AIResponseCols.DESCRIPTION}: AT LEAST a 50 word description of the alert with the provided information that a Compliance Analyst would understand in at least 50 words.
    - {AIResponseCols.EXPLANATION}: Explain the factors contributing to this alert.
    - {AIResponseCols.SCORE}: Provide a Severity Score from 0-10 (10 being a Severe Breach, 0 being an obvious False Positive).
    - {AIResponseCols.SCORE_RATIONALE}: Provide a Rationale for the Severity Score.
    - {AIResponseCols.SUGGESTED_RESOLUTION_COMMENT}: Provide a Resolution Commentary.
    - {AIResponseCols.SUGGESTED_RESOLUTION_CATEGORY}: Suggest a Resolution Category
    - {AIResponseCols.SUGGESTED_RESOLUTION_SUB_CATEGORIES}: Suggest a list of  Resolution Sub-Categories based on the {AIResponseCols.SUGGESTED_RESOLUTION_CATEGORY}
    
    The {AIResponseCols.SUGGESTED_RESOLUTION_CATEGORY} MUST be one of the following and the {AIResponseCols.SUGGESTED_RESOLUTION_SUB_CATEGORIES} MUST be valid list of sub-categories for that {AIResponseCols.SUGGESTED_RESOLUTION_CATEGORY}:
    - {ResolutionCategoryMarEnum.BREACH.value}: {CATEGORY_SUB_CATEGORY_MAP.get(ResolutionCategoryMarEnum.BREACH)}
    - {ResolutionCategoryMarEnum.DATA_INTEGRITY.value}: {CATEGORY_SUB_CATEGORY_MAP.get(ResolutionCategoryMarEnum.DATA_INTEGRITY)}
    - {ResolutionCategoryMarEnum.FALSE_POSITIVE.value}: {CATEGORY_SUB_CATEGORY_MAP.get(ResolutionCategoryMarEnum.FALSE_POSITIVE)}
    - {ResolutionCategoryMarEnum.NO_ACTION_REQUIRED.value}: {CATEGORY_SUB_CATEGORY_MAP.get(ResolutionCategoryMarEnum.NO_ACTION_REQUIRED)}     
    - {ResolutionCategoryMarEnum.OTHER.value}: {CATEGORY_SUB_CATEGORY_MAP.get(ResolutionCategoryMarEnum.OTHER)}
    """  # noqa E501
    )

    supported_alogs = [
        MarketAbuseReportType.WASH_TRADING,
    ]

    def __init__(
        self,
        market_abuse_alert_type: MarketAbuseReportType,
        executions_df: Optional[pd.DataFrame] = pd.DataFrame(),
    ):
        self.market_abuse_alert_type = market_abuse_alert_type
        self.executions_df = executions_df if executions_df is not None else pd.DataFrame()
        self.raw_prompt_obj = RawPromptTSurv(self.market_abuse_alert_type)
        self.raw_prompt_getter = self.raw_prompt_obj.get_raw_prompt()

    def get_prompt_generator(self):
        """
        Map to the prompt generation method for respective algorithms
        :return:
        """
        algo_prompt_generator_map = {
            MarketAbuseReportType.WASH_TRADING: self._get_wash_trading_prompt,
            MarketAbuseReportType.INSIDER_TRADING_V3_REFINITIV: self._get_insider_trading_v3_refinitiv_prompt,  # noqa E501
            MarketAbuseReportType.FRONT_RUNNING_V2: self._get_frontrunning_v2_prompt,
            MarketAbuseReportType.LAYERING_V2: self._get_layering_v2_prompt,
            MarketAbuseReportType.POTAM: self._get_potam_prompt,
            MarketAbuseReportType.PAINTING_THE_TAPE_V2: self._get_ramping_prompt,
            MarketAbuseReportType.SUSPICIOUS_LARGE_ORDER_VOLUME_V2: self._get_sloev_prompt,
        }
        return algo_prompt_generator_map[self.market_abuse_alert_type]

    def _get_wash_trading_prompt(self, record):
        """Get GPT prompt for Wash Trading.

        :param record:
        :return:
        """
        buy_count = 0
        sell_count = 0
        executions = record.get(PromptFields.RECORDS_EXECUTION)
        execution_details_prompt_text = "More detail on the executions are provided below:"
        execution_prompt_texts = [execution_details_prompt_text]
        count = 0
        for execution in executions:
            execution_prompt = self.raw_prompt_obj.get_execution_details_prompt_text().format(
                count=count,
                side=self.executions_df.loc[execution].get(
                    PromptFields.EXECUTION_DETAILS_BUY_SELL_INDICATOR
                ),
                timestamp=self.executions_df.loc[execution].get(
                    PromptFields.TRANSACTION_DETAILS_TRADING_DATE_TIME
                ),
                quantity=self.executions_df.loc[execution].get(
                    PromptFields.TRANSACTION_DETAILS_QUANTITY
                ),
                client=self.executions_df.loc[execution].get(PromptFields.CLIENT_FILE_IDENTIFIER),
                trader=self.executions_df.loc[execution].get(PromptFields.TRADER_FILE_IDENTIFIER),
                counterparty=self.executions_df.loc[execution].get(
                    PromptFields.COUNTERPARTY_FILE_IDENTIFIER
                ),
            )
            execution_prompt_texts.append(f"- {execution_prompt}")
            if (
                self.executions_df.loc[execution].get(
                    PromptFields.EXECUTION_DETAILS_BUY_SELL_INDICATOR
                )
                == BuySellIndicator.BUYI
            ):
                buy_count += 1
            else:
                sell_count += 1
            count += 1
        execution_prompt_text = "\n".join(execution_prompt_texts)
        request_prompt = self.raw_prompt_getter().format(
            buy_count=buy_count,
            buy_executed_qty=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_EXECUTED_BUY_QUANTITY, record
            ),
            sell_count=sell_count,
            sell_executed_qty=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_EXECUTED_SELL_QUANTITY, record
            ),
            additional_fields_top_level_instrument_name=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_INSTRUMENT_NAME, record
            ),
            additional_fields_top_level_date=pd.Timestamp(
                get_field_value(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_DATE, record)
            )
            .date()
            .strftime(DATE_FORMAT),
            additional_fields_top_level_volume_difference=round(
                get_field_value(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_VOLUME_DIFFERENCE, record),
                2,
            ),
            thresholds_max_volumne_difference=round(
                get_field_value(PromptFields.THRESHOLDS_MAX_VOLUME_DIFFERENCE, record), 2
            )
            * 100,
            additional_fields_top_level_price_difference=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_PRICE_DIFFERENCE, record
            ),
            thresholds_max_price_difference=round(
                get_field_value(PromptFields.THRESHOLDS_MAX_PRICE_DIFFERENCE, record), 2
            ),
            additional_fields_top_level_implied_pl=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_IMPLIED_PL, record
            ),
            additional_fields_top_level_implied_pl_currency=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_IMPLIED_PL_CURRENCY, record
            ),
            additional_fields_top_level_time_difference=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_TIME_DIFFERENCE, record
            ),
            thresholds_max_time_window=get_field_value(
                PromptFields.THRESHOLDS_MAX_TIME_WINDOW, record
            ),
            execution_details_prompt_text=execution_prompt_text,
        )
        return request_prompt

    def _get_insider_trading_v3_refinitiv_prompt(self, record):
        """Get GPT prompt for Insider Trading V3.

        :param record:
        :return:
        """

        alert_reason = "Could not determine alert reason"
        if get_field_value(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_EVENT_TYPE, record) == "Both":
            alert_reason = (
                "both a News Event and a Market "
                "Price event were detected after Trading Activity occurred"
            )
        elif get_field_value(PromptFields.THRESHOLDS_EVENT_CREATION, record) == "Market Data":
            alert_reason = "a Market Price event was detected after Trading Activity occurred"
        elif get_field_value(PromptFields.THRESHOLDS_EVENT_CREATION, record) == "News Feed":
            alert_reason = "a News Event was detected after Trading Activity occurred"
        elif get_field_value(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_EVENT_TYPE, record) == "Any":
            alert_reason = (
                "either a News Event OR a Market "
                "Price event or both were detected after Trading Activity occurred"
            )

        event_direction = "Could not determine event direction"
        if (
            get_field_value(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_EVENT_DIRECTION, record)
            == "up"
        ):
            event_direction = "positive"
        elif (
            get_field_value(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_EVENT_DIRECTION, record)
            == "down"
        ):
            event_direction = "negative"

        (
            main_prompt,
            news_prompt_text,
            market_event_prompt_text,
            threshold_activity_minimum_pnl,
            behaviour_execution_details,
        ) = self.raw_prompt_getter()

        # Populate News Prompt if News or Both
        if get_field_value(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_EVENT_TYPE, record) in [
            "News Feed",
            "Both",
        ]:
            news_prompts = []
            prompt_template = "-- Source: {source}; Time: {time}; Headline: {headline}"
            for item in record.get(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_NEWS_HEADLINES, [])[
                :10
            ]:
                news_prompts.append(
                    prompt_template.format(
                        source=",".join(item.get("sources", [])),
                        time=item.get("storyCreated"),
                        headline=item.get("title"),
                    )
                )
            conditional_news_prompt_text = news_prompt_text.format(
                additional_fields_top_level_new_headlines_count=len(
                    record.get(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_NEWS_HEADLINES, [])
                ),
                new_article_prompt_text="\n".join(news_prompts),
            )
        else:
            conditional_news_prompt_text = ""

        # Populate Market Event Prompt if Market Data or Both
        if get_field_value(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_EVENT_TYPE, record) in [
            "Market Data",
            "Both",
        ]:
            conditional_market_event_prompt_text = market_event_prompt_text.format(
                additional_fields_top_level_event_date=pd.Timestamp(
                    get_field_value(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_EVENT_DATE, record)
                )
                .date()
                .strftime(DATE_FORMAT),
                additional_fields_top_level_market_close_price=get_field_value(
                    PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_MARKET_CLOSE_PRICE, record
                ),
                additional_fields_top_level_market_close_price_variation=get_field_value(
                    PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_MARKET_CLOSE_PRICE_VARIATION, record
                ),
                additional_fields_top_level_observation_period_start=pd.Timestamp(
                    get_field_value(
                        PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_OBSERVATION_PERIOD_START, record
                    )
                )
                .date()
                .strftime(DATE_FORMAT),
                additional_fields_top_level_market_price_limits=get_field_value(
                    PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_MARKET_PRICE_LIMITS, record
                ),
            )
        else:
            conditional_market_event_prompt_text = ""

        # Populate threshold_activity_minimum_pnl if threshold minimum trade amount is populated
        if get_field_value(PromptFields.THRESHOLDS_ACTIVITY_MINIMUM_PNL, record) is not None:
            conditional_threshold_activity_minimum_pnl = threshold_activity_minimum_pnl.format(
                thresholds_activity_minimum_pnl=get_field_value(
                    PromptFields.THRESHOLDS_ACTIVITY_MINIMUM_PNL, record
                ),
            )
        else:
            conditional_threshold_activity_minimum_pnl = ""

        # Populate behaviour execution details if behaviour period executions is populated
        if len(record.get(PromptFields.RECORDS_BEHAVIOUR_PERIOD_EXECUTIONS)) > 1:
            conditional_behaviour_execution_details = behaviour_execution_details.format(
                additional_fields_top_level_behaviour_period_0=round(
                    record.get(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_BEHAVIOUR_PERIOD)[0]
                ),
                additional_fields_top_level_behaviour_period_1=round(
                    record.get(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_BEHAVIOUR_PERIOD)[1]
                ),
            )
        else:
            conditional_behaviour_execution_details = ""
        buy_count, sell_count = 0, 0
        instrument_full_name = self.executions_df.loc[
            record.get(PromptFields.RECORDS_OBSERVATION_PERIOD_EXECUTIONS)[0]
        ].get(PromptFields.INSTRUMENT_FULL_NAME)
        for execution in record.get(PromptFields.RECORDS_OBSERVATION_PERIOD_EXECUTIONS, []):
            if (
                self.executions_df.loc[execution].get(
                    PromptFields.EXECUTION_DETAILS_BUY_SELL_INDICATOR
                )
                == BuySellIndicator.BUYI
            ):
                buy_count += 1
            else:
                sell_count += 1

        main_prompt = main_prompt.format(
            record_observation_period_executions=len(
                record.get(PromptFields.RECORDS_OBSERVATION_PERIOD_EXECUTIONS, [])
            ),
            buy_count=buy_count,
            sell_count=sell_count,
            instrument_full_name=instrument_full_name,
            additional_fields_top_level_evaluation_type=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_EVALUATION_TYPE, record
            ),
            additional_fields_top_level_observation_period=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_OBSERVATION_PERIOD, record
            ),
            additional_fields_top_level_observation_period_start=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_OBSERVATION_PERIOD_START, record
            ),
            thresholds_currency_filter=get_field_value(
                PromptFields.THRESHOLDS_CURRENCY_FILTER, record
            ),
            thresholds_activity_minimum_trade_amount=get_field_value(
                PromptFields.THRESHOLDS_ACTIVITY_MINIMUM_TRADE_AMOUNT, record
            ),
            alert_reason=alert_reason,
            event_direction=event_direction,
            conditional_news_prompt_text=conditional_news_prompt_text,
            conditional_market_event_prompt_text=conditional_market_event_prompt_text,
            additional_fields_top_level_pnl=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_PNL, record
            ),
            additional_fields_top_level_currency=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_CURRENCY, record
            ),
            conditional_threshold_activity_minimum_pnl=conditional_threshold_activity_minimum_pnl,
            additional_fields_top_level_behaviour_period_start=pd.Timestamp(
                get_field_value(
                    PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_BEHAVIOUR_PERIOD_START, record
                )
            )
            .date()
            .strftime(DATE_FORMAT),
            records_behaviour_period_executions_count=len(
                record.get(PromptFields.RECORDS_BEHAVIOUR_PERIOD_EXECUTIONS, [])
            ),
            conditional_behaviour_execution_details=conditional_behaviour_execution_details,
        )
        return main_prompt

    def _get_frontrunning_v2_prompt(self, record):
        """Get GPT prompt for Frontrunning V2.

        :param record:
        :return:
        """
        (
            main_prompt,
            flow_prompt,
            frontrunning_avg_price_prompt,
            frontrun_avg_price_prompt,
            price_improvement_prompt,
        ) = self.raw_prompt_getter()

        conditional_flow_prompt = ""
        flow = get_field_value(PromptFields.THRESHOLDS_FLOW, record)
        if flow == FlowValues.CLIENT_VS_CLIENT:
            conditional_flow_prompt = (
                """A Order from one Client preceded another from a different Client."""
            )
        elif flow == FlowValues.DESK_VS_DESK:
            conditional_flow_prompt = (
                """A Order from one Desk preceded another from a different Desk."""
            )
        elif flow == FlowValues.PROP_VS_CLIENT:
            conditional_flow_prompt = """A Proprietary Order preceded a Client Order."""
        elif flow == FlowValues.PAD_VS_NON_PAD:
            conditional_flow_prompt = (
                """A Personal Account Dealing (PAD) Order preceded a non-PAD Order."""
            )

        if len(conditional_flow_prompt) > 0:
            conditional_flow_prompt = flow_prompt.format(
                threshold_flow_prompt=conditional_flow_prompt
            )

        # Conditional FrontRunning Price Avg Promot
        conditional_frontrunning_price_avg_prompt = ""
        front_running_price_avg = get_field_value(
            PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_FRONT_RUNNING_PRICE_AVERAGE, record
        )
        if front_running_price_avg:
            conditional_frontrunning_price_avg_prompt = (
                f"and was executed "
                f"for an average price of "
                f"{prettify_number(front_running_price_avg)}"
            )

        # Conditional FrontRun price avg prompt
        conditional_frontrun_price_avg_prompt = ""
        front_run_price_avg = get_field_value(
            PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_FRONT_RUN_PRICE_AVERAGE, record
        )
        if front_run_price_avg:
            conditional_frontrun_price_avg_prompt = (
                f"and was executed for an average price of {prettify_number(front_run_price_avg)}"
            )

        # Conditional Price Improvement Prompt
        conditional_price_improvement_prompt = ""
        front_running_price_improvement = get_field_value(
            PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_PRICE_IMPROVEMENT, record
        )
        if front_running_price_improvement:
            conditional_price_improvement_prompt = (
                f"-- The Price difference was {prettify_number(front_running_price_improvement)}"
            )

        # Main Prompt
        main_prompt = main_prompt.format(
            conditional_flow_prompt=conditional_flow_prompt,
            additional_fields_top_level_instrument_name=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_INSTRUMENT_NAME, record
            ),
            count_frontrunning_orders=len(record.get(PromptFields.RECORDS_FRONT_RUNNING_ORDER, [])),
            frontrunning_orders_timestamp=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_FRONT_RUNNING_ORDERS_TIMESTAMP, record
            ),
            frontrunning_orders_quantity=prettify_number(
                get_field_value(
                    PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_FRONT_RUNNING_ORDERS_QUANTITY, record
                )
            ),
            conditional_frontrunning_price_avg_prompt=conditional_frontrunning_price_avg_prompt,
            count_frontrun_orders=len(record.get(PromptFields.RECORDS_FRONT_RUN_ORDERS, [])),
            frontrun_orders_timestamp=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_FRONT_RUN_ORDERS_TIMESTAMP, record
            ),
            frontrun_orders_quantity=prettify_number(
                get_field_value(
                    PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_FRONT_RUN_ORDERS_QUANTITY, record
                )
            ),
            conditional_frontrun_price_avg_prompt=conditional_frontrun_price_avg_prompt,
            additional_fields_top_level_time_difference=prettify_number(
                get_field_value(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_TIME_DIFFERENCE, record)
            ),
            threshold_time_window_value=prettify_number(
                record.get(PromptFields.THRESHOLDS_TIME_WINDOW_VALUE)
            ),
            threshold_time_window_unit=record.get(PromptFields.THRESHOLDS_TIME_WINDOW_UNIT),
            additional_fields_top_level_volume_difference=prettify_number(
                get_field_value(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_VOLUME_DIFFERENCE, record)
                * 100
            ),
            conditional_price_improvement_prompt=conditional_price_improvement_prompt,
        )
        return main_prompt

    def _get_layering_v2_prompt(self, record):
        """Get GPT prompt for Layering V2.

        :param record:
        :return:
        """
        (
            main_prompt,
            conditional_buy_orders_prompt,
            buy_execution_detail_prompt,
            conditional_sell_orders_prompt,
            sell_execution_detail_prompt,
            order_book_detail_prompt,
        ) = self.raw_prompt_getter()
        orders = record.get(PromptFields.RECORDS_ORDERS)
        layering_data = {
            DfColumns.ORDER_KEY: record.get(PromptFields.RECORDS_ORDERS),
            DfColumns.ORDER_ID: record.get(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_ORDER_ID_LIST),
            DfColumns.PERCENTAGE_LEVEL: record.get(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_PERCENTAGE_LEVEL
            ),
            DfColumns.PRICE_LEVEL: record.get(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_PRICE_LEVEL),
            DfColumns.VOLUME_LEVEL: record.get(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_VOLUME_LEVEL
            ),
        }
        layering_data_df = pd.DataFrame(layering_data).set_index(DfColumns.ORDER_KEY)
        buy_order_prompt = ""
        sell_order_prompt = ""
        conditional_venue_prompt = ""

        if record.get(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_NUMBER_BUY_ORDERS)[0] > 0:
            # enrich the executions dataframe
            buy_order_prompt_segments = []
            for order in orders:
                if (
                    order in self.executions_df.index
                    and self.executions_df.loc[  # check if the order details were present
                        order
                    ].get(PromptFields.EXECUTION_DETAILS_BUY_SELL_INDICATOR, "")
                    == BuySellIndicator.BUYI.value
                ):
                    buy_order_prompt_segments.append(
                        buy_execution_detail_prompt.format(
                            order_id=layering_data_df.loc[order].get(DfColumns.ORDER_ID),
                            order_submitted=self.executions_df.loc[order].get(
                                "timestamps.orderSubmitted"
                            ),
                            initial_quantity=prettify_number(
                                self.executions_df.loc[order].get(
                                    "priceFormingData.initialQuantity"
                                )
                            ),
                            price_level=layering_data_df.loc[order].get(DfColumns.PRICE_LEVEL),
                            volume_level=prettify_number(
                                layering_data_df.loc[order].get(DfColumns.VOLUME_LEVEL)
                            ),
                            percentage_level=prettify_number(
                                layering_data_df.loc[order].get(DfColumns.PERCENTAGE_LEVEL) * 100
                            ),
                        )
                    )

            buy_order_prompt = conditional_buy_orders_prompt.format(
                additional_fields_top_level_number_buy_orders=record.get(
                    PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_NUMBER_BUY_ORDERS
                )[0],
                additional_fields_top_level_buy_orders_quantity=record.get(
                    PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_BUY_ORDERS_QUANTITY
                )[0],
                additional_fields_top_level_bid_price_levels=record.get(
                    PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_BID_PRICE_LEVELS
                )[0],
                threshold_layering_number_of_price_levels=record.get(
                    PromptFields.THRESHOLDS_LAYERING_NUMBER_OF_PRICE_LEVELS
                ),
                buy_execution_detail_prompt=".\n".join(buy_order_prompt_segments),
            )

        if record.get(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_NUMBER_SELL_ORDERS)[0] > 0:
            sell_order_prompt_segments = []
            for order in orders:
                if (
                    order in self.executions_df.index
                    and self.executions_df.loc[  # check if the order details were present
                        order
                    ].get(PromptFields.EXECUTION_DETAILS_BUY_SELL_INDICATOR, "")
                    == BuySellIndicator.SELL.value
                ):
                    sell_order_prompt_segments.append(
                        sell_execution_detail_prompt.format(
                            order_id=layering_data_df.loc[order].get(DfColumns.ORDER_ID),
                            order_submitted=self.executions_df.loc[order].get(
                                "timestamps.orderSubmitted"
                            ),
                            initial_quantity=prettify_number(
                                self.executions_df.loc[order].get(
                                    "priceFormingData.initialQuantity"
                                )
                            ),
                            price_level=layering_data_df.loc[order].get(DfColumns.PRICE_LEVEL),
                            volume_level=prettify_number(
                                layering_data_df.loc[order].get(DfColumns.VOLUME_LEVEL)
                            ),
                            percentage_level=prettify_number(
                                layering_data_df.loc[order].get(DfColumns.PERCENTAGE_LEVEL) * 100
                            ),
                        )
                    )

            sell_order_prompt = conditional_sell_orders_prompt.format(
                additional_fields_top_level_number_sell_orders=record.get(
                    PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_NUMBER_SELL_ORDERS
                )[0],
                additional_fields_top_level_sell_orders_quantity=prettify_number(
                    record.get(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_SELL_ORDERS_QUANTITY)[0]
                ),
                additional_fields_top_level_ask_price_levels=record.get(
                    PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_ASK_PRICE_LEVELS
                )[0],
                threshold_layering_number_of_price_levels=record.get(
                    PromptFields.THRESHOLDS_LAYERING_NUMBER_OF_PRICE_LEVELS
                ),
                sell_execution_detail_prompt=".\n".join(sell_order_prompt_segments),
            )

        venue_list = record.get(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_VENUE_LIST)

        if len(record.get(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_VENUE_LIST)) > 1:
            conditional_venue_prompt = (
                f"""These Orders were routed to Multiple Venues: {", ".join(venue_list)}"""
            )

        elif len(record.get(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_VENUE_LIST)) == 1:
            conditional_venue_prompt = (
                f"""These Orders were routed to a Single Venue: {venue_list[0]}"""
            )
        scenario_orders = self.executions_df[self.executions_df.index.isin(orders)]
        if not scenario_orders.empty:
            start_date = pd.Timestamp(self.executions_df.loc[:, OrderField.TS_ORD_SUBMITTED].min())

            end_date = pd.Timestamp(self.executions_df.loc[:, OrderField.TS_ORD_SUBMITTED].max())

            market_data_client = MarketDataClient()
            order_book_data = market_data_client.get_order_book_depth_data(
                ric=self.executions_df.iloc[0].get("RIC"),
                start_date=start_date,
                end_date=end_date,
            )

            if not order_book_data.empty:
                before_tick = get_tick_data(order_book_data, start_date)
                after_tick = get_tick_data(order_book_data, end_date, after_flag=True)

                bid_col = (
                    RefinitivExtractColumns.BID_SIZE
                    if RefinitivExtractColumns.BID_PRICE in order_book_data.keys()
                    else RefinitivColumnsEnum.BID_SIZE
                )
                ask_col = (
                    RefinitivExtractColumns.ASK_SIZE
                    if RefinitivExtractColumns.ASK_SIZE in order_book_data.keys()
                    else RefinitivColumnsEnum.ASK_SIZE
                )

                levels = record.get(PromptFields.THRESHOLDS_LAYERING_NUMBER_OF_PRICE_LEVELS, 1)
                bid_volume_start, ask_volume_start = get_volumes(
                    before_tick, bid_col, ask_col, levels
                )
                bid_volume_end, ask_volume_end = get_volumes(after_tick, bid_col, ask_col, levels)

                order_book_detail_prompt = order_book_detail_prompt.format(
                    ask_volume_start=ask_volume_start,
                    bid_volume_start=bid_volume_start,
                    threshold_layering_number_of_price_levels=levels,
                    ask_volume_end=ask_volume_end,
                    bid_volume_end=bid_volume_end,
                )

        # populate main prompt
        main_prompt = main_prompt.format(
            additional_fields_top_level_instrument_name=record.get(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_INSTRUMENT_NAME
            )[0],
            additional_fields_top_level_earliest_timestamp=pd.Timestamp(
                record.get(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_EARLIEST_TIMESTAMP)
            )
            .date()
            .strftime(DATE_FORMAT),
            conditional_buy_orders_prompt=buy_order_prompt,
            conditional_sell_orders_prompt=sell_order_prompt,
            threshold_layering_order_percentage=prettify_number(
                record.get(PromptFields.THRESHOLDS_LAYERING_ORDER_PERCENTAGE) * 100
            ),
            threshold_layering_time_window=record.get(PromptFields.THRESHOLDS_LAYERING_TIME_WINDOW),
            threshold_evaluation_type=record.get(PromptFields.THRESHOLDS_EVALUATION_TYPE),
            conditional_venue_prompt=conditional_venue_prompt,
            order_book_detail_prompt=order_book_detail_prompt,
        )

        return main_prompt

    def _get_potam_prompt(self, record):
        """Get GPT prompt for POTAM.

        :param record:
        :return:
        """
        (main_prompt) = self.raw_prompt_getter()

        # Main Prompt
        main_prompt = main_prompt.format(
            additional_fields_top_level_instrument_full_name=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_INSTRUMENT_FULL_NAME, record
            ),
            additional_fields_top_level_order_submitted_ts_local=pd.Timestamp(
                get_field_value(
                    PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_ORDER_SUBMITTED_TS_LOCAL, record
                )
            )
            .date()
            .strftime(DATE_FORMAT),
            additional_fields_top_level_potam_offeree=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_POTAM_OFFEREE, record
            ),
            additional_fields_top_level_potam_offeror=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_POTAM_OFFERROR, record
            ),
            additional_fields_top_level_potam_window_start=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_POTAM_WINDOW_START, record
            ),
            additional_fields_top_level_potam_window_end=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_POTAM_WINDOW_END, record
            ),
            additional_fields_top_level_traded_quantity=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_POTAM_TRADED_QUANTITY, record
            ),
        )
        return main_prompt

    def _get_ramping_prompt(self, record):
        """Get GPT prompt for Ramping/Painting the tape V2.

        :param record:
        :return:
        """

        market_data_eval_type = record.get(PromptFields.THRESHOLDS_MARKET_DATA_EVALUATION_TYPE)
        market_data_eval_type_conditonal_prompt = ""
        if market_data_eval_type == MarketDataEvaluationType.MARKET_DAY_TRADED_VOLUME:
            market_data_eval_type_conditonal_prompt = """Total Market Day Traded Volume: {additional_fields_top_level_comparison_volume}\nvs. Market Day Traded Volume: {additional_fields_top_level_comparison_percentage}"""

        elif market_data_eval_type == MarketDataEvaluationType.MARKET_TIME_WINDOW_TRADED_VOLUME:
            market_data_eval_type_conditonal_prompt = """Total Intraday Traded Volume: {additional_fields_top_level_comparison_volume}\nvs. Intraday Market Day Traded Volume: {additional_fields_top_level_comparison_percentage}"""

        elif market_data_eval_type == MarketDataEvaluationType.MARKET_AVERAGE_DAILY_TRADED_VOLUME:
            market_data_eval_type_conditonal_prompt = """Total Average Market Day Traded Volume: {additional_fields_top_level_comparison_volume}\nvs. Average Market Day Traded Volume: {additional_fields_top_level_comparison_percentage}"""

        market_data_eval_type_conditonal_prompt = market_data_eval_type_conditonal_prompt.format(
            additional_fields_top_level_comparison_volume=record.get(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_COMPARISON_VOLUME
            )[0],
            additional_fields_top_level_comparison_percentage=record.get(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_COMPARISON_PERCENTAGE
            )[0],
        )

        max_time_conditional_prompt = (
            """\nLast Execution: {additional_fields_top_level_max_order_ts_time_window_grouping}"""
        )
        if record.get(PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_MAX_TRADE_TS_TIME_WINDOW_GROUPING):
            max_time_conditional_prompt = max_time_conditional_prompt.format(
                pd.Timestamp(
                    record.get(
                        PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_MAX_TRADE_TS_TIME_WINDOW_GROUPING
                    )[0]
                )
                .date()
                .strftime(TIME_FORMAT)
            )

        else:
            max_time_conditional_prompt = ""

        (main_prompt) = self.raw_prompt_getter()

        # Main Prompt
        main_prompt = main_prompt.format(
            additional_fields_top_level_instrument_name=record.get(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_INSTRUMENT_NAME
            )[0],
            additional_fields_top_level_min_order_ts_time_window_grouping_time_format=pd.Timestamp(
                record.get(
                    PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_MIN_ORDER_TS_TIME_WINDOW_GROUPING
                )[0]
            )
            .date()
            .strftime(DATE_FORMAT),
            additional_fields_top_level_min_order_ts_time_window_grouping_date_format=pd.Timestamp(
                record.get(
                    PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_MIN_ORDER_TS_TIME_WINDOW_GROUPING
                )[0]
            )
            .date()
            .strftime(TIME_FORMAT),
            additional_fields_top_level_count_buys_time_window_grouping=record.get(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_COUNT_BUYS_TIME_WINDOW_GROUPING
            )[0],
            additional_fields_top_level_count_sells_time_window_grouping=record.get(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_COUNT_SELLS_TIME_WINDOW_GROUPING
            )[0],
            thresholds_min_order_count=record.get(PromptFields.THRESHOLDS_MIN_ORDER_COUNT),
            thresholds_evaluation_type=record.get(PromptFields.THRESHOLDS_EVALUATION_TYPE),
            max_time_conditional_prompt=max_time_conditional_prompt,
            additional_fields_top_level_quantity_sum_time_window_grouping=record.get(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_QUANTITY_SUM_TIME_WINDOW_GROUPING
            )[0],
            market_data_eval_type_conditonal_prompt=market_data_eval_type_conditonal_prompt,
            thresholds_percentage_adv=record.get(PromptFields.THRESHOLDS_PERCENTAGE_ADV),
            additional_fields_top_level_price_improvement_percentage=record.get(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_PRICE_IMPROVEMENT_PERCENTAGE
            )[0],
        )
        return main_prompt

    def _get_sloev_prompt(self, record):
        """Get GPT prompt for SUSPICIOUS LARGE ORDER VOLUME V2.

        :param record:
        :return:
        """

        (main_prompt,) = self.raw_prompt_getter()

        flow = record.get(PromptFields.THRESHOLDS_CATEGORY_EVALUATION_TYPE)
        conditional_flow_prompt = ""
        conditional_market_data_evaluation_type_prompt = ""
        conditional_market_impact_percentage_price_prompt = ""

        if flow == CategoryEvaluationTypeValues.INTERNAL_FLOW:
            thresholds_general_evaluation_type = get_field_value(
                PromptFields.THRESHOLDS_GENERAL_EVALUATION_TYPE, record
            )
            conditional_flow_prompt = f"""the historically defined average for the denoted given {thresholds_general_evaluation_type}"""
            conditional_market_data_evaluation_type_prompt = """20-Day Average {thresholds_general_evaluation_type} Traded Volume: {additional_fields_top_level_adv}\nvs. 20-Day Average {thresholds_general_evaluation_type} Traded Volume: {additional_fields_top_level_adv_percentage}"""

        elif flow == CategoryEvaluationTypeValues.MARKET:
            conditional_flow_prompt = (
                """a user-defined percentage of Market Traded Volume figure."""
            )
            market_impact_percentage = record.get(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_MARKET_IMPACT_PERCENTAGE
            )

            if market_impact_percentage:
                conditional_market_impact_percentage_price_prompt = f"\nThe Market Price Impact (Market Mid Price at time of first Order vs. Market Mid Price 10 Minutes thereafter): {prettify_number(market_impact_percentage[0])}"

            market_data_eval_type = record.get(PromptFields.THRESHOLDS_MARKET_DATA_EVALUATION_TYPE)
            if market_data_eval_type == MarketDataEvaluationType.DAY_TRADED_VOLUME:
                conditional_market_data_evaluation_type_prompt = """Market Day Traded Volume: {additional_fields_top_level_adv}\nvs. Market Day Traded Volume: {additional_fields_top_level_adv_percentage}"""

            elif market_data_eval_type == MarketDataEvaluationType.AVERAGE_DAILY_TRADED_VOLUME:
                conditional_market_data_evaluation_type_prompt = """Average Market Day Traded Volume: {additional_fields_top_level_adv}\nvs. Average Market Day Traded Volume: {additional_fields_top_level_adv_percentage}"""

            elif market_data_eval_type == MarketDataEvaluationType.INTRA_DAY_TRADED_VOLUME:
                conditional_market_data_evaluation_type_prompt = """Intraday Traded Volume: {additional_fields_top_level_adv}\nvs. Intraday Traded Volume: {additional_fields_top_level_adv_percentage}"""

        conditional_market_data_evaluation_type_prompt = (
            conditional_market_data_evaluation_type_prompt.format(
                additional_fields_top_level_adv=record.get(
                    PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_ADV
                )[0],
                additional_fields_top_level_adv_percentage=record.get(
                    PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_ADV_PERCENTAGE
                )[0],
                thresholds_general_evaluation_type=get_field_value(
                    PromptFields.THRESHOLDS_GENERAL_EVALUATION_TYPE, record
                ),
            )
        )
        main_prompt = main_prompt.format(
            conditional_flow_prompt=conditional_flow_prompt,
            additional_fields_top_level_instrument_name_list=record.get(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_INSTRUMENT_NAME_LIST
            )[0],
            additional_fields_top_level_earliest_order_timestamp=pd.Timestamp(
                get_field_value(
                    PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_EARLIEST_ORDER_TIMESTAMP, record
                )
            )
            .date()
            .strftime(DATE_FORMAT),
            additional_fields_top_level_orders_detected=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_ORDERS_DETECTED, record
            ),
            thresholds_day_and_order_evaluation_type=get_field_value(
                PromptFields.THRESHOLDS_DAY_AND_ORDER_EVALUATION_TYPE, record
            ),
            additional_fields_top_level_total_order_quantity=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_TOTAL_ORDER_QUANTITY, record
            ),
            conditional_market_data_evaluation_type_prompt=conditional_market_data_evaluation_type_prompt,
            thresholds_percentage_adv=get_field_value(
                PromptFields.THRESHOLDS_PERCENTAGE_ADV, record
            ),
            additional_fields_top_level_price_improvement_percentage=get_field_value(
                PromptFields.ADDITIONAL_FIELDS_TOP_LEVEL_PRICE_IMPROVEMENT_PERCENTAGE, record
            ),
            conditional_market_impact_percentage_price_prompt=conditional_market_impact_percentage_price_prompt,
        )

        return main_prompt

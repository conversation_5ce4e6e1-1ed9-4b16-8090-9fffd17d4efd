# ruff: noqa: E501
import inspect
from se_elastic_schema.static.case import ResolutionCategoryMarEnum, ResolutionSubCategoryMarEnum
from se_elastic_schema.static.surveillance import MarketAbuseReportType
from typing import Tuple

DT_DISPLAY_FMT = "%Y-%m-%d - %H:%M:%S"

DATE_TIME_FORMAT = "%Y-%m-%dT%H:%M:%S.%fZ"

AWS_REGION = "eu-west-1"

UNKNOWN_ID = "UNKNOWN"


class PromptFields:
    # Additinal Fields Top Level
    ADDITIONAL_FIELDS_TOP_LEVEL_BEHAVIOUR_PERIOD = "additionalFields.topLevel.behaviourPeriod"
    ADDITIONAL_FIELDS_TOP_LEVEL_BEHAVIOUR_PERIOD_START = (
        "additionalFields.topLevel.behaviourPeriodStart"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_DATE = "additionalFields.topLevel.date"
    ADDITIONAL_FIELDS_TOP_LEVEL_EXECUTED_BUY_QUANTITY = (
        "additionalFields.topLevel.executedBuyQuantity"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_EXECUTED_SELL_QUANTITY = (
        "additionalFields.topLevel.executedSellQuantity"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_EXECUTION_DETAILS = "additionalFields.topLevel.executionDetails"
    ADDITIONAL_FIELDS_TOP_LEVEL_EVALUATION_TYPE = "additionalFields.topLevel.evaluationType"
    ADDITIONAL_FIELDS_TOP_LEVEL_EVENT_DATE = "additionalFields.topLevel.eventDate"
    ADDITIONAL_FIELDS_TOP_LEVEL_EVENT_DIRECTION = "additionalFields.topLevel.eventDirection"
    ADDITIONAL_FIELDS_TOP_LEVEL_EVENT_TYPE = "additionalFields.topLevel.eventType"
    ADDITIONAL_FIELDS_TOP_LEVEL_IMPLIED_PL = "additionalFields.topLevel.impliedPL"
    ADDITIONAL_FIELDS_TOP_LEVEL_IMPLIED_PL_CURRENCY = "additionalFields.topLevel.impliedPLCurrency"
    ADDITIONAL_FIELDS_TOP_LEVEL_INSTRUMENT_NAME = "additionalFields.topLevel.instrumentName"
    ADDITIONAL_FIELDS_TOP_LEVEL_MARKET_CLOSE_PRICE = "additionalFields.topLevel.marketClosePrice"
    ADDITIONAL_FIELDS_TOP_LEVEL_MARKET_CLOSE_PRICE_VARIATION = (
        "additionalFields.topLevel.marketClosePriceVariation"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_MARKET_PRICE_LIMITS = "additionalFields.topLevel.marketPriceLimits"
    ADDITIONAL_FIELDS_TOP_LEVEL_NEWS_HEADLINES = "additionalFields.topLevel.newsHeadline"
    ADDITIONAL_FIELDS_TOP_LEVEL_OBSERVATION_PERIOD = "additionalFields.topLevel.observationPeriod"
    ADDITIONAL_FIELDS_TOP_LEVEL_OBSERVATION_PERIOD_START = (
        "additionalFields.topLevel.observationPeriodStart"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_PRICE_DIFFERENCE = "additionalFields.topLevel.priceDifference"
    ADDITIONAL_FIELDS_TOP_LEVEL_TIME_DIFFERENCE = "additionalFields.topLevel.timeDifference"
    ADDITIONAL_FIELDS_TOP_LEVEL_VOLUME_DIFFERENCE = "additionalFields.topLevel.volumeDifference"
    ADDITIONAL_FIELDS_TOP_LEVEL_PNL = "additionalFields.topLevel.PNL"
    ADDITIONAL_FIELDS_TOP_LEVEL_CURRENCY = "additionalFields.topLevel.currency"
    ADDITIONAL_FIELDS_TOP_LEVEL_FRONT_RUNNING_ORDERS_TIMESTAMP = (
        "additionalFields.topLevel.frontRunningOrdersTimestamp"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_FRONT_RUN_ORDERS_TIMESTAMP = (
        "additionalFields.topLevel.frontRunOrdersTimestamp"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_FRONT_RUNNING_ORDERS_QUANTITY = (
        "additionalFields.topLevel.frontRunningOrdersQuantity"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_FRONT_RUN_ORDERS_QUANTITY = (
        "additionalFields.topLevel.frontRunOrdersQuantity"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_PRICE_IMPROVEMENT = "additionalFields.topLevel.priceImprovement"
    ADDITIONAL_FIELDS_TOP_LEVEL_FRONT_RUNNING_PRICE_AVERAGE = (
        "additionalFields.topLevel.frontRunningPriceAverage"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_FRONT_RUN_PRICE_AVERAGE = (
        "additionalFields.topLevel.frontRunPriceAverage"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_ASK_PRICE_LEVELS = "additionalFields.topLevel.askPriceLevels"
    ADDITIONAL_FIELDS_TOP_LEVEL_BID_PRICE_LEVELS = "additionalFields.topLevel.bidPriceLevels"
    ADDITIONAL_FIELDS_TOP_LEVEL_BUY_ORDERS_QUANTITY = "additionalFields.topLevel.buyOrdersQuantity"
    ADDITIONAL_FIELDS_TOP_LEVEL_SELL_ORDERS_QUANTITY = (
        "additionalFields.topLevel.sellOrdersQuantity"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_EARLIEST_TIMESTAMP = "additionalFields.topLevel.earliestTimestamp"
    ADDITIONAL_FIELDS_TOP_LEVEL_NUMBER_BUY_ORDERS = "additionalFields.topLevel.numberBuyOrders"
    ADDITIONAL_FIELDS_TOP_LEVEL_NUMBER_SELL_ORDERS = "additionalFields.topLevel.numberSellOrders"
    ADDITIONAL_FIELDS_TOP_LEVEL_ORDER_ID_LIST = "additionalFields.topLevel.orderIdList"
    ADDITIONAL_FIELDS_TOP_LEVEL_VOLUME_LEVEL = "additionalFields.topLevel.volumeLevel"
    ADDITIONAL_FIELDS_TOP_LEVEL_PRICE_LEVEL = "additionalFields.topLevel.priceLevel"
    ADDITIONAL_FIELDS_TOP_LEVEL_PERCENTAGE_LEVEL = "additionalFields.topLevel.percentageLevel"
    ADDITIONAL_FIELDS_TOP_LEVEL_VENUE_LIST = "additionalFields.topLevel.venueList"
    ADDITIONAL_FIELDS_TOP_LEVEL_INSTRUMENT_FULL_NAME = (
        "additionalFields.topLevel.instrumentFullName"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_POTAM_OFFEREE = "additionalFields.topLevel.potamOfferee"
    ADDITIONAL_FIELDS_TOP_LEVEL_POTAM_OFFERROR = "additionalFields.topLevel.potamOfferor"
    ADDITIONAL_FIELDS_TOP_LEVEL_POTAM_WINDOW_START = "additionalFields.topLevel.potamWindowStart"
    ADDITIONAL_FIELDS_TOP_LEVEL_POTAM_WINDOW_END = "additionalFields.topLevel.potamWindowEnd"
    ADDITIONAL_FIELDS_TOP_LEVEL_ORDER_SUBMITTED_TS_LOCAL = (
        "additionalFields.topLevel.orderSubmittedTSLocal"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_POTAM_TRADED_QUANTITY = "additionalFields.topLevel.tradedQuantity"
    ADDITIONAL_FIELDS_TOP_LEVEL_MIN_ORDER_TS_TIME_WINDOW_GROUPING = (
        "additionalFields.topLevel.vMinimumOrderTimestampInTimeWindowGrouping"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_COUNT_BUYS_TIME_WINDOW_GROUPING = (
        "additionalFields.topLevel.vCountOfBuysInTimeWindowGrouping"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_COUNT_SELLS_TIME_WINDOW_GROUPING = (
        "additionalFields.topLevel.vCountOfSellsInTimeWindowGrouping"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_MAX_TRADE_TS_TIME_WINDOW_GROUPING = (
        "additionalFields.topLevel.vMaximumTradeTimestampInTimeWindowGrouping"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_QUANTITY_SUM_TIME_WINDOW_GROUPING = (
        "additionalFields.topLevel.vQuantitySumTimeWindowGrouping"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_PRICE_IMPROVEMENT_PERCENTAGE = (
        "additionalFields.topLevel.vPriceImprovementPercentage"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_COMPARISON_VOLUME = "additionalFields.topLevel.vComparisonVolume"
    ADDITIONAL_FIELDS_TOP_LEVEL_COMPARISON_PERCENTAGE = (
        "additionalFields.topLevel.vComparisonPercentage"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_INSTRUMENT_NAME_LIST = (
        "additionalFields.topLevel.instrumentNameList"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_EARLIEST_ORDER_TIMESTAMP = (
        "additionalFields.topLevel.earliestOrderTimestamp"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_ORDERS_DETECTED = "additionalFields.topLevel.ordersDetected"

    ADDITIONAL_FIELDS_TOP_LEVEL_MARKET_IMPACT_PERCENTAGE = (
        "additionalFields.topLevel.marketImpactPercentage"
    )
    ADDITIONAL_FIELDS_TOP_LEVEL_ADV = "additionalFields.topLevel.adv"
    ADDITIONAL_FIELDS_TOP_LEVEL_ADV_PERCENTAGE = "additionalFields.topLevel.advPercentage"
    ADDITIONAL_FIELDS_TOP_LEVEL_TOTAL_ORDER_QUANTITY = (
        "additionalFields.topLevel.totalOrderQuantity"
    )

    CLIENT_FILE_IDENTIFIER = "clientFileIdentifier"
    COUNTERPARTY_FILE_IDENTIFIER = "counterpartyFileIdentifier"
    INSTRUMENT_FULL_NAME = "instrumentDetails.instrument.instrumentFullName"

    # Records
    RECORDS_EXECUTION = "records.executions"
    RECORDS_ORDERS = "records.orders"
    RECORDS_BEHAVIOUR_PERIOD_EXECUTIONS = "records.behaviourPeriodExecutions"
    RECORDS_OBSERVATION_PERIOD_EXECUTIONS = "records.observationPeriodExecutions"
    RECORDS_FRONT_RUNNING_ORDER = "records.frontRunningOrder"
    RECORDS_FRONT_RUN_ORDERS = "records.frontRunOrders"

    EXECUTION_DETAILS_BUY_SELL_INDICATOR = "executionDetails.buySellIndicator"
    TRADER_FILE_IDENTIFIER = "traderFileIdentifier"
    TRANSACTION_DETAILS_TRADING_DATE_TIME = "transactionDetails.tradingDateTime"
    TRANSACTION_DETAILS_QUANTITY = "transactionDetails.quantity"

    # Thresholds
    THRESHOLDS_ACTIVITY_MINIMUM_PNL = "thresholds.activityMinimumPNL"
    THRESHOLDS_ACTIVITY_MINIMUM_TRADE_AMOUNT = "thresholds.activityMinimumTradeAmount"
    THRESHOLDS_CURRENCY_FILTER = "thresholds.currencyFilter"
    THRESHOLDS_EVENT_CREATION = "thresholds.eventCreation"
    THRESHOLDS_MAX_TIME_WINDOW = "thresholds.maxTimeWindow"
    THRESHOLDS_MAX_VOLUME_DIFFERENCE = "thresholds.maxVolumeDifference"
    THRESHOLDS_MAX_PRICE_DIFFERENCE = "thresholds.maxPriceDifference"
    THRESHOLDS_FLOW = "thresholds.flow"
    THRESHOLDS_TIME_WINDOW_UNIT = "thresholds.timeWindow.unit"
    THRESHOLDS_TIME_WINDOW_VALUE = "thresholds.timeWindow.value"
    THRESHOLDS_EVALUATION_TYPE = "thresholds.evaluationType"
    THRESHOLDS_MARKET_DATA_EVALUATION_TYPE = "thresholds.marketDataEvaluationType"
    THRESHOLDS_LAYERING_NUMBER_OF_PRICE_LEVELS = "thresholds.layeringNumberOfPriceLevels"
    THRESHOLDS_LAYERING_ORDER_PERCENTAGE = "thresholds.layeringOrderPercentage"
    THRESHOLDS_LAYERING_TIME_WINDOW = "thresholds.layeringTimeWindow"
    THRESHOLDS_MIN_ORDER_COUNT = "thresholds.minOrderCount"
    THRESHOLDS_PERCENTAGE_ADV = "thresholds.percentageAdv"
    THRESHOLDS_CATEGORY_EVALUATION_TYPE = "thresholds.categoryEvaluationType"
    THRESHOLDS_DAY_AND_ORDER_EVALUATION_TYPE = "thresholds.dayAndOrderEvaluationType"
    THRESHOLDS_GENERAL_EVALUATION_TYPE = "thresholds.generalEvaluationType"


class DfColumns:
    ORDER_KEY = "key"
    ORDER_ID = "order_id"
    PERCENTAGE_LEVEL = "percentage_level"
    PRICE_LEVEL = "price_level"
    VOLUME_LEVEL = "volume_level"


class FlowValues:
    CLIENT_VS_CLIENT = "Client vs. Client"
    DESK_VS_DESK = "Desk vs. Desk"
    PAD_VS_NON_PAD = "PAD vs. Non Pad"
    PROP_VS_CLIENT = "Prop vs. Client"


class CategoryEvaluationTypeValues:
    INTERNAL_FLOW = "internalFlow"
    MARKET = "market"


class AIResponseCols:
    DESCRIPTION = "alertDescription"
    EXPLANATION = "alertExplanation"
    SCORE = "score"
    SCORE_RATIONALE = "scoreRationale"
    SUGGESTED_RESOLUTION_CATEGORY = "suggestedResolutionCategory"
    SUGGESTED_RESOLUTION_SUB_CATEGORIES = "suggestedResolutionSubCategories"
    SUGGESTED_RESOLUTION_COMMENT = "suggestedResolutionComment"


class CopilotAnalysisSourceFields:
    SOURCE = "source"
    MODEL = "model"
    VERSION = "version"
    ADDITIONAL_INFO = "additionalInfo"


class OpenAIModels:
    ENGINE_GPT_4_O = "gpt-4o"


class MarketDataEvaluationType:
    MARKET_AVERAGE_DAILY_TRADED_VOLUME = "Market Average Daily Traded Volume"
    MARKET_DAY_TRADED_VOLUME = "Market Day Traded Volume"
    MARKET_TIME_WINDOW_TRADED_VOLUME = "Market Time Window Traded Volume"
    DAY_TRADED_VOLUME = "dayTradedVolume"
    AVERAGE_DAILY_TRADED_VOLUME = "averageDailyTradedVolume"
    INTRA_DAY_TRADED_VOLUME = "intraDayTradedVolume"


class RawPromptTSurv:
    def __init__(self, market_abuse_report_type: MarketAbuseReportType):
        self.market_abuse_report_type = market_abuse_report_type

    def get_raw_prompt(self):
        report_type_to_prompt = {
            MarketAbuseReportType.WASH_TRADING: self._get_wash_trade_raw_prompt,
            MarketAbuseReportType.INSIDER_TRADING_V3_REFINITIV: self._get_insider_trading_v3_refinitiv_raw_prompts,
            MarketAbuseReportType.FRONT_RUNNING_V2: self._get_frontrunning_v2_raw_prompts,
            MarketAbuseReportType.LAYERING_V2: self._get_layering_v2_raw_prompts,
            MarketAbuseReportType.POTAM: self._get_potam_prompts,
            MarketAbuseReportType.PAINTING_THE_TAPE_V2: self._get_ramping_prompts,
            MarketAbuseReportType.SUSPICIOUS_LARGE_ORDER_VOLUME_V2: self._get_sloev_prompts,
        }
        return report_type_to_prompt.get(self.market_abuse_report_type)

    @staticmethod
    def _get_wash_trade_raw_prompt() -> str:
        wash_trade_prompt_message = """Alert Type: Wash Trades
        The Alert contains:
        {buy_count} Buy Executions, with a total Executed Quantity of {buy_executed_qty} and {sell_count} Sell Executions with a total Executed Quantity of {sell_executed_qty}
        All Executions were on the same Instrument: {additional_fields_top_level_instrument_name}
        All Executions were on the same Date: {additional_fields_top_level_date}
        The Quantity difference between Buys and Sells was: {additional_fields_top_level_volume_difference}
        (vs. a maximum allowable threshold of {thresholds_max_volumne_difference}%)
        The Execution Price difference between Buys and Sells was: {additional_fields_top_level_price_difference}
        (vs. a maximum allowable threshold of {thresholds_max_price_difference})
        The implied Monetary Value difference between Buys and Sells was: {additional_fields_top_level_implied_pl} {additional_fields_top_level_implied_pl_currency}
        The Time difference between Buys and Sells was: {additional_fields_top_level_time_difference} seconds (
        vs. a maximum allowable threshold of {thresholds_max_time_window} seconds)
        {execution_details_prompt_text}
        """
        return inspect.cleandoc(wash_trade_prompt_message)

    @staticmethod
    def _get_insider_trading_v3_refinitiv_raw_prompts() -> Tuple[str, str, str, str, str]:
        main_prompt = """Alert Type: Insider Trading
        - The Alert contains {record_observation_period_executions} Executions ({buy_count} Buys, {sell_count} Sells) on the Instrument: {instrument_full_name}. Details on the detected execution(s):

        - In all detected Records the {additional_fields_top_level_evaluation_type} is the same.
        - The monetary value of these Executions is {additional_fields_top_level_observation_period} ({thresholds_currency_filter}). The minimum value set to generate an alert was {thresholds_activity_minimum_trade_amount} .
        - The alert was generated because {alert_reason}.
        -- The direction of the Event was {event_direction}. {conditional_news_prompt_text}{conditional_market_event_prompt_text}

        - The implied PnL (difference between the Average Execution Price and the Market Close on the Event Date) was {additional_fields_top_level_pnl} ({additional_fields_top_level_currency}) {conditional_threshold_activity_minimum_pnl}

        - The model looked back to {additional_fields_top_level_behaviour_period_start} for Trading Activity in the same  {instrument_full_name} for the same  {additional_fields_top_level_evaluation_type} and found {records_behaviour_period_executions_count} executions in this range.
        {conditional_behaviour_execution_details}
        """  # noqa: E501
        conditional_news_prompt_text = """
        -- There were {additional_fields_top_level_new_headlines_count} news articles detected. The news articles are summarised below:
        {new_article_prompt_text}
        """  # noqa: E501

        conditional_market_event_prompt_text = """
        -- The Market Close Price on {additional_fields_top_level_event_date} was {additional_fields_top_level_market_close_price}. This was a {additional_fields_top_level_market_close_price_variation} % difference between the date of the Executions {additional_fields_top_level_observation_period_start} and the event date {additional_fields_top_level_event_date}.
        The Algo detection model used a confidence  interval calculation to set the allowable Market Close Price Change limits between {additional_fields_top_level_market_price_limits}
        """  # noqa: E501

        conditional_threshold_activity_minimum_pnl = (
            """the minimum PnL set to generate an alert was {thresholds_activity_minimum_pnl}"""
        )
        conditional_behaviour_execution_details = """
        - Based on this prior activity the model set an allowable trading activity range of {additional_fields_top_level_behaviour_period_0} to {additional_fields_top_level_behaviour_period_1} in order to generate an alert"""  # noqa: E501

        return (
            inspect.cleandoc(main_prompt),
            inspect.cleandoc(conditional_news_prompt_text),
            inspect.cleandoc(conditional_market_event_prompt_text),
            inspect.cleandoc(conditional_threshold_activity_minimum_pnl),
            inspect.cleandoc(conditional_behaviour_execution_details),
        )

    @staticmethod
    def _get_frontrunning_v2_raw_prompts() -> Tuple[str, str, str, str, str]:
        main_prompt = """Alert Type: Frontrunning
        {conditional_flow_prompt}
        All Orders were placed on the same security ({additional_fields_top_level_instrument_name}).
        The alert contains {count_frontrunning_orders} Frontrunning Order/s:
            -- The Order was placed at {frontrunning_orders_timestamp} for a quantity of {frontrunning_orders_quantity}{conditional_frontrunning_price_avg_prompt}
        And {count_frontrun_orders} Frontrun Orders;
            -- The Order was placed at {frontrun_orders_timestamp} for a quantity of {frontrun_orders_quantity}{conditional_frontrun_price_avg_prompt}
        Therefore, between these two groups:
            -- The Time difference was {additional_fields_top_level_time_difference} seconds. The Threshold to generate an Alert is {threshold_time_window_value} {threshold_time_window_unit}.
            -- The relative size of the front running order, compared to the front run order, was {additional_fields_top_level_volume_difference}%
            {conditional_price_improvement_prompt}
        """  # noqa: E501

        conditional_flow_prompt = """
        The alert was raised because {threshold_flow_prompt}"""
        conditional_frontrunning_avg_price_prompt = (
            """and was executed for an avergae price of {frontrunning_price_avg}"""
        )
        conditional_frontrun_avg_price_prompt = (
            """and was executed for an avergae price of {frontrun_price_avg}"""
        )
        conditional_price_improvement_prompt = (
            """-- The Price difference was {additional_fields_top_level_price_improvement}"""
        )

        return (
            inspect.cleandoc(main_prompt),
            inspect.cleandoc(conditional_flow_prompt),
            inspect.cleandoc(conditional_frontrunning_avg_price_prompt),
            inspect.cleandoc(conditional_frontrun_avg_price_prompt),
            inspect.cleandoc(conditional_price_improvement_prompt),
        )

    @staticmethod
    def _get_layering_v2_raw_prompts() -> Tuple[str, str, str, str, str, str]:
        main_prompt = """Summary: Layering Alert for {additional_fields_top_level_instrument_name} on the Instrument {additional_fields_top_level_earliest_timestamp}
        {conditional_buy_orders_prompt}
        {conditional_sell_orders_prompt}
        The Minimum % of Level required to generate an Alert is {threshold_layering_order_percentage}%
        The Maximum Time Window Threshold to group Alerts is {threshold_layering_time_window} Seconds
        {conditional_venue_prompt}
        All Orders were placed under the instruction of the same {threshold_evaluation_type}
        Inspecting the Market Order Book:
        {order_book_detail_prompt}
        """  # noqa: E501

        conditional_buy_orders_prompt = """- Buy Orders: {additional_fields_top_level_number_buy_orders}
        - Buy Order Quantity: {additional_fields_top_level_buy_orders_quantity}
        - Distinct Bid Price Levels Orders Placed at: {additional_fields_top_level_bid_price_levels}. The minimum required number of Price Levels to generate an Alert is {threshold_layering_number_of_price_levels}.
        - Each Buy Order is described below:
        {buy_execution_detail_prompt}
        """
        buy_execution_detail_prompt = """-- Order: {order_id}, Timestamp: {order_submitted}, Quantity: {initial_quantity}, Bid Price Level: {price_level}, Market Volume Available at its Level: {volume_level}, Order Quantity as a % of Level: {percentage_level}%"""

        conditional_sell_orders_prompt = """- Sell Orders: {additional_fields_top_level_number_sell_orders}
        - Sell Order Quantity: {additional_fields_top_level_sell_orders_quantity}
        - Distinct Ask Price Levels Orders Placed at: {additional_fields_top_level_ask_price_levels}. The minimum required number of Price Levels to generate an Alert is {threshold_layering_number_of_price_levels}.
        - Each Sell Order is described below:
        {sell_execution_detail_prompt}
        """
        sell_execution_detail_prompt = """-- Order: {order_id}, Timestamp: {order_submitted}, Quantity: {initial_quantity}, Ask Price Level: {price_level}, Market Volume Available at its Level: {volume_level}, Order Quantity as a % of Level: {percentage_level}%"""

        order_book_detail_prompt = """- Immediately preceeding the First Order Submission there was {ask_volume_start} available on the Ask, and {bid_volume_start} available on the Bid from Levels 1 to {threshold_layering_number_of_price_levels}
        - Immediately after the Last Order Submission there was {ask_volume_end} available on the Ask, and {bid_volume_end} available on the Bid from Levels 1 to {threshold_layering_number_of_price_levels}
        """

        return (
            inspect.cleandoc(main_prompt),
            inspect.cleandoc(conditional_buy_orders_prompt),
            inspect.cleandoc(buy_execution_detail_prompt),
            inspect.cleandoc(conditional_sell_orders_prompt),
            inspect.cleandoc(sell_execution_detail_prompt),
            inspect.cleandoc(order_book_detail_prompt),
        )

    @staticmethod
    def _get_potam_prompts() -> str:
        main_prompt = """Alert Logic Description: This model raises alerts when either an Order or Execution on an Instrument and that same Instrument is also present on the POTAM List.
        The POTAM list is maintained by the Panel on Takeovers and Mergers and is the body that oversees takeover activity in the UK.
        The Algorithm looks for Order Placement either within the POTAM window or around the POTAM window. The POTAM window is defined as the date at which the offer for Takeover or Merger is publicly available until the Takeover or Merger is either completed or withdrawn.
        The Algorithm searches for both securities and derivatives securities of those securities listed on the POTAM list.

        POTAM alert for {additional_fields_top_level_instrument_full_name} on {additional_fields_top_level_order_submitted_ts_local}
        Instrument: {additional_fields_top_level_potam_offeree}
        Named Potential Acquirer of {additional_fields_top_level_potam_offeree}: {additional_fields_top_level_potam_offeror}
        Offer Commences: {additional_fields_top_level_potam_window_start}
        Offeror Identified / Removal from POTAM List: {additional_fields_top_level_potam_window_end}
        Order Date: {additional_fields_top_level_order_submitted_ts_local}
        Trade Direction:
        Total Traded Quantity: {additional_fields_top_level_traded_quantity}
        """

        return inspect.cleandoc(main_prompt)

    @staticmethod
    def _get_ramping_prompts():
        main_prompt = """Summary: Ramping Alert for {additional_fields_top_level_instrument_name} on {additional_fields_top_level_min_order_ts_time_window_grouping_date_format}
        Total Buy Executions: {additional_fields_top_level_count_buys_time_window_grouping}
        Total Sell Executions: {additional_fields_top_level_count_sells_time_window_grouping}
        Minimum Execution Count (to generate an Alert): {thresholds_min_order_count}
        The {thresholds_evaluation_type} is the same on all Records
        Instrument: {additional_fields_top_level_instrument_name}
        First Execution: {additional_fields_top_level_min_order_ts_time_window_grouping_time_format}{max_time_conditional_prompt}
        Total Executed Quantity: {additional_fields_top_level_quantity_sum_time_window_grouping}
        {market_data_eval_type_conditonal_prompt}
        Volume Threshold (to generate an Alert): {thresholds_percentage_adv}
        Price improvement between first and last execution was {additional_fields_top_level_price_improvement_percentage}
        """

        return inspect.cleandoc(main_prompt)

    @staticmethod
    def _get_sloev_prompts():
        main_prompt = """Summary: Alert Logic Description: This model raises alerts when either an Order, or collection of Orders are placed, and their associated quantities are above {conditional_flow_prompt}
        Suspicious Large Order Alert for: {additional_fields_top_level_instrument_name_list} on {additional_fields_top_level_earliest_order_timestamp}
        Instrument: {additional_fields_top_level_instrument_name_list}
        Asset Class: retrieve the asset class of the instrument
        Orders Detected: {additional_fields_top_level_orders_detected}
        Total {thresholds_day_and_order_evaluation_type} Quantity: {additional_fields_top_level_total_order_quantity}
        {conditional_market_data_evaluation_type_prompt}
        % Threshold (to generate an Alert): {thresholds_percentage_adv}{conditional_market_impact_percentage_price_prompt}
        """

        return (inspect.cleandoc(main_prompt),)

    @staticmethod
    def get_execution_details_prompt_text() -> str:
        execution_details_prompt_text = (
            "Execution {count} Side: {side} Timestamp: {timestamp}, "
            "Quantity: {quantity}, Client: {client}, Trader: {trader}, "
            "Counterparty: {counterparty}"
        )
        return inspect.cleandoc(execution_details_prompt_text)


MAX_USER_PROMPT_SIZE = 44000
JSON_REGEX = r"\{[^{}]+\}"

CATEGORY_SUB_CATEGORY_MAP = {
    ResolutionCategoryMarEnum.BREACH: ";".join(
        [
            ResolutionSubCategoryMarEnum.EXCHANGE_BREACH.name,
            ResolutionSubCategoryMarEnum.INTERNAL_POLICY_BREACH.name,
            ResolutionSubCategoryMarEnum.REGULATORY_BREACH.name,
        ]
    ),
    ResolutionCategoryMarEnum.DATA_INTEGRITY: ";".join(
        [
            ResolutionSubCategoryMarEnum.MARKET_DATA.name,
            ResolutionSubCategoryMarEnum.ORDER_TRADE_DATA.name,
        ]
    ),
    ResolutionCategoryMarEnum.FALSE_POSITIVE: ";".join(
        [
            ResolutionSubCategoryMarEnum.CROSS_TRADE.name,
            ResolutionSubCategoryMarEnum.IMMATERIAL_RELEASE_OF_NEWS_ANALYST_RATING_CHANGE.name,
            ResolutionSubCategoryMarEnum.IMMATERIAL_RELEASE_OF_NEWS_GENERAL_NEWS.name,
            ResolutionSubCategoryMarEnum.IMMATERIAL_RELEASE_OF_NEWS_RE_RELEASE.name,
            ResolutionSubCategoryMarEnum.REPO_VS_BOND.name,
            ResolutionSubCategoryMarEnum.THRESHOLDS_SET_TOO_LENIENT.name,
        ]
    ),
    ResolutionCategoryMarEnum.NO_ACTION_REQUIRED: ";".join(
        [
            ResolutionSubCategoryMarEnum.CONSISTENT_WITH_NORMAL_TRADING_ACTIVITY.name,
            ResolutionSubCategoryMarEnum.INSTRUMENT_HIGH_LIQUIDITY_PRECLUDES_IT.name,
            ResolutionSubCategoryMarEnum.MARKET_EVENT.name,
            ResolutionSubCategoryMarEnum.NEAR_MISS.name,
            ResolutionSubCategoryMarEnum.NO_EVIDENCE_OF_IMPROPER_CONDUCT.name,
            ResolutionSubCategoryMarEnum.NO_SIGNIFICANT_PRICE_MOVEMENT_AFTER_NEWS.name,
            ResolutionSubCategoryMarEnum.ORDER_TRADE_VOLUME_NOT_MATERIAL.name,
            ResolutionSubCategoryMarEnum.PRICE_MOVE_IS_CONSISTENT_WITH_THE_MARKET.name,
            ResolutionSubCategoryMarEnum.PRICE_MOVE_IS_CONSISTENT_WITH_THE_SECTOR.name,
            ResolutionSubCategoryMarEnum.PRICE_SENSITIVE_NEWS_FLOW_PRIOR_TO_TRADE.name,
        ]
    ),
    ResolutionCategoryMarEnum.OTHER: "Other",
}


ALERT_TYPE_EXECUTION_FIELD_MAPPING = {
    MarketAbuseReportType.WASH_TRADING: PromptFields.RECORDS_EXECUTION,
    MarketAbuseReportType.INSIDER_TRADING_V3_REFINITIV: PromptFields.RECORDS_OBSERVATION_PERIOD_EXECUTIONS,
    MarketAbuseReportType.LAYERING_V2: PromptFields.RECORDS_ORDERS,
}

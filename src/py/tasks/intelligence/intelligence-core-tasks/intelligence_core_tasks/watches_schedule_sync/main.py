import addict
import logging
import pytz
import time
import urllib3
from aries_config_api_httpschema.conductor_workflow_schedule import (
    ConductorWorkflowInputScheduleTypeEnum,
    ConductorWorkflowScheduleInfo,
    ConductorWorkflowScheduleInterval,
)
from aries_io_event.app_metric import AppMetricFieldSet
from aries_se_api_client.client import AriesApiClient
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from data_platform_config_api_client.conductor_workflow_schedule import ConductorWorkflowScheduleAPI
from data_platform_config_api_client.tenant import Tenant<PERSON>I
from datetime import datetime
from dateutil import tz
from intelligence_core_tasks.watches_schedule_sync.config import TASK_CONFIG
from intelligence_core_tasks.watches_schedule_sync.data_repository import DataRepository
from intelligence_core_tasks.watches_schedule_sync.static import WATCH_QUERY_TYPE_MAP
from se_elastic_schema.components.surveillance.watch_schedule_details import WatchScheduleDetails
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch
from se_elastic_schema.static.surveillance import (
    WatchScheduleMonthlyRecurrence,
    WatchScheduleRecurrence,
    WatchStatusType,
)
from se_es_utils.slim_record_handler import SlimRecordHandler
from typing import Dict

logger = logging.getLogger("schedule_sync")
elastic_logger = logging.getLogger("elasticsearch")
elastic_logger.setLevel(logging.WARNING)  # preventing spam of successful elastic logs
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def get_workflow_name(watch: dict) -> str | None:
    # Attempting to map as csurv2 watch using queryType
    aries_workflow_name = WATCH_QUERY_TYPE_MAP.get(watch.get("queryType", ""))

    if not aries_workflow_name:
        # Mapping mar algos and drop unmapped watches (Mar algos not deployed to aries)
        mar_algo_name = watch["query"].get("marketAbuseReportType")
        aries_workflow_name = TASK_CONFIG.MAR_WORKFLOW_MAP.get(mar_algo_name)
    return aries_workflow_name


def is_schedulable(schedule_details: WatchScheduleDetails):
    if not schedule_details or not schedule_details.recurrence:
        return False

    if not schedule_details.recurrence == WatchScheduleRecurrence.CONTINUOUS:
        if not isinstance(schedule_details.timeOfDay, str):
            return False

        if len(schedule_details.timeOfDay.split(":")) != 2:
            return False

        hour = schedule_details.timeOfDay.split(":")[0]
        minute = schedule_details.timeOfDay.split(":")[1]

        if not hour.isnumeric() or not minute.isnumeric():
            return False
    elif not isinstance(schedule_details.interval, int):
        return False

    return True


def create_cron(schedule_details: WatchScheduleDetails) -> str:
    hour, minute = "0", "0"
    day_of_month = "*"
    month = "*"
    day_of_week = "*"

    time_zone = schedule_details.timeZone if schedule_details.timeZone else "UTC"

    try:
        naive_time = datetime.strptime(schedule_details.timeOfDay, "%H:%M")
        local_tz = pytz.timezone(time_zone)
        local_time = datetime.now(tz=local_tz)
        local_time = local_time.replace(
            hour=naive_time.hour, minute=naive_time.minute, second=0, microsecond=0
        )
        utc_time = local_time.astimezone(tz.UTC)
        hour, minute = str(utc_time.hour), str(utc_time.minute)
    except (ValueError, TypeError):
        pass
    if schedule_details.daysOfWeek:
        daysOfWeek = schedule_details.daysOfWeek.split(",")

        day_name_to_number = {
            "MONDAY": 0,
            "TUESDAY": 1,
            "WEDNESDAY": 2,
            "THURSDAY": 3,
            "FRIDAY": 4,
            "SATURDAY": 5,
            "SUNDAY": 6,
        }
        number_to_day_abbrev = {
            0: "MON",
            1: "TUE",
            2: "WED",
            3: "THU",
            4: "FRI",
            5: "SAT",
            6: "SUN",
        }

        utc_day_of_week = utc_time.weekday()

        # If the day of the week in UTC is different from the local time, adjust the daysOfWeek
        if utc_day_of_week != local_time.weekday():
            if utc_day_of_week > local_time.weekday():
                daysOfWeek = [
                    number_to_day_abbrev[(day_name_to_number[day] + 1) % 7] for day in daysOfWeek
                ]
            else:
                daysOfWeek = [
                    number_to_day_abbrev[(day_name_to_number[day] - 1) % 7] for day in daysOfWeek
                ]
        else:
            daysOfWeek = [number_to_day_abbrev[day_name_to_number[day]] for day in daysOfWeek]

        day_of_week = ",".join(daysOfWeek)

    if schedule_details.recurrence == WatchScheduleRecurrence.DAILY and schedule_details.interval:
        day_of_month = f"*/{schedule_details.interval}"

    if schedule_details.recurrence == WatchScheduleRecurrence.CONTINUOUS:
        interval = schedule_details.interval
        if interval < 60:
            minute = f"*/{interval}"
            hour = "*"
        else:
            hour = f"*/{interval // 60}"

    if schedule_details.recurrence == WatchScheduleRecurrence.MONTHLY:
        monthly_recurrence = schedule_details.monthlyRecurrence
        if monthly_recurrence == WatchScheduleMonthlyRecurrence.ON_DAY:
            day_of_month = schedule_details.dayOfMonth
        elif monthly_recurrence == WatchScheduleMonthlyRecurrence.FIRST_DAY:
            day_of_month = "1"
        elif monthly_recurrence == WatchScheduleMonthlyRecurrence.LAST_DAY:
            day_of_month = "last"

    cron_string = f"{minute} {hour} {day_of_month} {month} {day_of_week}"

    return cron_string


def schedule_watches(watch: dict, cron_str=None, interval=None):
    schedule_details = watch.get("scheduleDetails", {})

    # Defaulting for deleted/paused watches with no schedule
    if (
        watch["status"] in (WatchStatusType.DELETED, WatchStatusType.PAUSED)
        and not schedule_details
    ):
        schedule_details = WatchScheduleDetails(
            recurrence=WatchScheduleRecurrence.MONTHLY.value, timeOfDay="06:00"
        )

    if not is_schedulable(schedule_details):
        raise Exception(f"This schedule {schedule_details} is invalid")

    if schedule_details.recurrence == WatchScheduleRecurrence.CONTINUOUS.value and (
        schedule_details.interval > 720
        or (schedule_details.interval > 60 and schedule_details.interval % 60 != 0)
    ):
        interval = ConductorWorkflowScheduleInterval(minutes=schedule_details.interval)

    if not interval:
        cron_str = create_cron(schedule_details)

    return cron_str, interval


def process_event(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    total_es_watches = 0
    time_processing_es_watches, time_updating_db_watches = 0.0, 0.0
    failed_tenants: Dict = {}
    skipped_tenants: list = []
    config_api_client = AriesApiClient(host=TASK_CONFIG.DATA_PLATFORM_CONFIG_API_URL)
    conductor_workflow_config_api = ConductorWorkflowScheduleAPI(client=config_api_client)
    tenant_config_api = TenantAPI(client=config_api_client)

    stack = TASK_CONFIG.STACK
    tenant_info = tenant_config_api.get_all(stack_name=stack).content
    tenant_list = [tenant["name"] for tenant in tenant_info]

    record_handler = SlimRecordHandler()
    for tenant in tenant_list:
        try:
            logger.info(f"Syncing tenant {tenant}")
            index = record_handler.get_es_alias(model=SurveillanceWatch, tenant=tenant)
            index_exists = record_handler.client.indices.exists(index=index)
            if not index_exists:
                skipped_tenants.append(tenant)
                logger.info(f"Skipping tenant {tenant}, index {index} doesn't exist")
                continue
            # Get ES watches for tenant
            # Only fetching MAR, cSurv2 watches
            es_watches = DataRepository(
                tenant=tenant, record_handler=record_handler
            ).retrieve_watches()
            total_es_watches += len(es_watches)

            start = time.time()
            es_watches_schedule = []
            for watch in es_watches:
                watch_id = None
                try:
                    aries_workflow_name = get_workflow_name(watch)
                    if aries_workflow_name:
                        watch_id = watch[record_handler.meta.id]
                        cron_str, interval = schedule_watches(addict.Dict(watch))

                        # Adding tenant to schedule_id
                        # Avoiding future issues in case of duplicating watches between tenants
                        schedule_id = f"{watch_id}_{tenant}"

                        schedule = {
                            "schedule_type": (
                                ConductorWorkflowInputScheduleTypeEnum.CRON.value
                                if cron_str
                                else ConductorWorkflowInputScheduleTypeEnum.INTERVAL.value
                            ),
                            "workflow_name": aries_workflow_name,
                            "workflow_input": dict(io_param=dict(params=dict(watch_id=watch_id))),
                            "cron": cron_str if cron_str else None,
                            "interval": interval if interval else None,
                            "schedule_id": schedule_id,
                            "deleted": (
                                True if watch["status"] == WatchStatusType.DELETED else False
                            ),
                            "paused": True if watch["status"] == WatchStatusType.PAUSED else False,
                        }

                        # Validating schedule
                        schedule = ConductorWorkflowScheduleInfo.validate(schedule).dict(
                            exclude_unset=True
                        )
                        es_watches_schedule.append(schedule)
                except Exception as e:
                    logger.warning(f"Failed to sync watch {watch_id} due to {e}")
                    failed_tenants[tenant] = failed_tenants.get(tenant, []) + [
                        f"{watch_id} - {type(e)} - {str(e)}"
                    ]

            time_processing_es_watches += time.time() - start

            logger.info(
                f"{len(es_watches)} watches in ES ({len(es_watches_schedule)} for aries workflows)"
            )

            start = time.time()
            if es_watches_schedule:
                logger.info(
                    "Trying to update:"
                    f" {[watch['schedule_id'].split('_')[0] for watch in es_watches_schedule]}"
                )
                json_body = {"schedules": es_watches_schedule}
                response = conductor_workflow_config_api.bulk_upsert_conductor_workflow_schedules(
                    stack_name=aries_task_input.workflow.stack,
                    tenant_name=tenant,
                    json_body=json_body,
                    timeout=TASK_CONFIG.TIMEOUT,
                )
                if response.raw_response.status_code != 200:
                    failed_tenants[tenant] = "bulk upsert failed"
            time_updating_db_watches += time.time() - start

        except Exception as e:
            failed_tenants[tenant] = f"{type(e)} - {str(e)}"

    if failed_tenants:
        for k, v in failed_tenants.items():
            logger.exception(f"Tenant {k} failed sync due to {v}")

        raise Exception(f"Failure syncing tenants: {list(failed_tenants.keys())} ")

    # Create Metrics
    app_metric = AppMetricFieldSet(
        metrics={
            "custom": {
                aries_task_input.task.name: {
                    "es_watches_processed": total_es_watches,
                    "time_processing_es_watches": time_processing_es_watches,
                    "time_updating_db_watches": time_updating_db_watches,
                }
            }
        }
    )

    return AriesTaskResult(app_metric=app_metric)


def run_watches_schedule_sync(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    """This invokes schedule sync task."""

    return process_event(aries_task_input)

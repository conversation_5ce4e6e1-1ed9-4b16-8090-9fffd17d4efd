import datetime
from intelligence_core_tasks.comms_watch_enrich.static import DATE_TIME_FORMAT
from intelligence_core_tasks.comms_watch_enrich.utils import remove_unneeded_meta
from se_elastic_schema.components.surveillance.alert_watch_detail import AlertWatchDetail
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch
from se_elastic_schema.static.surveillance import (
    AlertHitStatus,
    BehaviourQueryKind,
)
from se_schema_meta import M<PERSON><PERSON>, PARENT, UNIQUE_PROPS
from surveillance_utils.utils import is_info_barrier_watch
from typing import List


def create_alerts_from_hits(
    hits: List[dict],
    surveillance_watch: SurveillanceWatch,
    surveillance_watch_execution_id: str,
    project_behaviour_list: list | None = None,
) -> List[dict]:
    """Transforms hits into alert hits, asides from the slug part.

    :param hits: hits obtained from the query
    :param surveillance_watch: SurveillanceWatch model
    :param surveillance_watch_execution_id: SurveillanceWatchExecution id
    :param project_behaviour_list: List of project behaviours

    :return: incomplete alert hits
    """
    project_behaviour_list = project_behaviour_list if project_behaviour_list else []

    alert_watch_data = {
        "createdOn": surveillance_watch.createdOn,
        "frequencyType": surveillance_watch.scheduleDetails.recurrence,
        "watchId": surveillance_watch.id__,
        "watchName": surveillance_watch.name,
        "watchPriority": surveillance_watch.priority,
        "queryKind": (
            surveillance_watch.query.kind if hasattr(surveillance_watch.query, "kind") else None
        ),
        "queryName": surveillance_watch.query.name,
        "queryType": surveillance_watch.queryType,
        "templateType": (
            None
            if surveillance_watch.query.template is None
            else surveillance_watch.query.template.templateType
        ),
    }
    alert_details = AlertWatchDetail.validate(alert_watch_data).dict()

    # data for the CommunicationAlert
    alerts: List = []
    for hit_source in hits:
        alert_hit = {
            PARENT: surveillance_watch_execution_id,
            "detail": alert_details,
            "detected": datetime.datetime.now().strftime(DATE_TIME_FORMAT),
            "hit": _remove_unneeded_lexica_from_hit_source(
                remove_unneeded_meta(record=hit_source), surveillance_watch, project_behaviour_list
            ),
            "hitModel": hit_source[MODEL],
            "workflow": {
                "status": AlertHitStatus.UNRESOLVED.value,
                "assigneeId": surveillance_watch.defaultAssigneeId,
                "assigneeName": surveillance_watch.defaultAssigneeName,
            },
        }

        if UNIQUE_PROPS in hit_source:
            alert_hit[UNIQUE_PROPS] = hit_source.get(UNIQUE_PROPS)

        if hit_source.get("analytics"):
            matched_lexica = {}
            if surveillance_watch.query.kind == BehaviourQueryKind.BEHAVIOUR.value:
                matched_lexica = _generate_lexica_categories_and_terms(
                    surveillance_watch, hit_source
                )
            elif is_info_barrier_watch(surveillance_watch):
                matched_lexica = _generate_info_barrier_lexica(hit_source, project_behaviour_list)

            if any(matched_lexica.values()):
                alert_hit.update(matched_lexica)

        alerts.append(alert_hit)
    return alerts


def _generate_lexica_categories_and_terms(
    surveillance_watch: SurveillanceWatch,
    hit: dict,
) -> dict:
    """Retrieves matched lexica, if present.

    :param surveillance_watch: SurveillanceWatch Record
    :param hit: an alert hit to be checked for the present of matches
    :return: separated categories and terms
    """
    matched_lexica: dict = {
        "matchedLexicaCategories": [],
        "matchedLexica": [],
        "matchedLexicaIds": [],
    }

    surveillance_watch_behaviour = surveillance_watch.query.lexicaBehaviour.name
    surveillance_watch_languages = surveillance_watch.query.lexicaBehaviour.languages

    for lexica in hit.get("analytics", {}).get("lexica", {}):
        if (
            lexica["behaviour"] == surveillance_watch_behaviour
            and lexica["termLanguage"] in surveillance_watch_languages
        ):
            matched_lexica["matchedLexicaCategories"].append(lexica["behaviour"])
            matched_lexica["matchedLexica"].append(lexica["term"])
            matched_lexica["matchedLexicaIds"].append(lexica["termId"])

    return matched_lexica


def _generate_info_barrier_lexica(
    hit: dict,
    project_behaviour_list: list,
) -> dict:
    """Retrieves matched lexica, if present.

    :param hit: an alert hit to be checked for the present of matches
    :param project_behaviour_list: List of project behaviours
    :return: separated categories and terms
    """

    matched_lexica: dict = {
        "matchedLexicaCategories": [],
        "matchedLexica": [],
        "matchedLexicaIds": [],
    }

    for lexica in hit.get("analytics", {}).get("lexica", {}):
        if lexica["behaviour"] in project_behaviour_list:
            matched_lexica["matchedLexicaCategories"].append(lexica["behaviour"])
            matched_lexica["matchedLexica"].append(lexica["term"])
            matched_lexica["matchedLexicaIds"].append(lexica["termId"])

    matched_lexica["infoBarrierProjectIds"] = list(set(matched_lexica["matchedLexicaCategories"]))

    return matched_lexica


def _remove_unneeded_lexica_from_hit_source(
    hit: dict, surveillance_watch: SurveillanceWatch, project_behaviour_list: list
) -> dict:
    def filter_triggers(trigger: dict) -> bool:
        if (
            not surveillance_watch.query.falsePositiveReduction
            or not surveillance_watch.query.falsePositiveReduction.excludedZones
        ):
            return False
        for excluded_class in surveillance_watch.query.falsePositiveReduction.excludedZones:
            if (
                trigger.get("zone_class") == excluded_class.className
                and trigger.get("zone_value", -1) > excluded_class.confidenceScore
            ):
                break
        else:
            return True
        return False

    lexica_hits = hit.get("analytics", {}).get("lexica", [])

    filtered_lexica = []

    for lex_hit in lexica_hits:
        if (
            surveillance_watch.query.lexicaBehaviour is not None
            and lex_hit["behaviourId"] == surveillance_watch.query.lexicaBehaviour.id
            and lex_hit["termLanguage"] in surveillance_watch.query.lexicaBehaviour.languages
        ) or (
            is_info_barrier_watch(surveillance_watch)
            and lex_hit["behaviour"] in project_behaviour_list
        ):
            if "triggers" not in lex_hit:
                filtered_lexica.append(lex_hit)
                continue

            triggers = [trigger for trigger in lex_hit["triggers"] if filter_triggers(trigger)]
            if triggers:
                lex_hit["triggers"] = triggers
                filtered_lexica.append(lex_hit)

    if filtered_lexica:
        hit["analytics"]["lexica"] = filtered_lexica
    return hit

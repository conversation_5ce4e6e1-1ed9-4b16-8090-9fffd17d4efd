# ruff: noqa: E501, W291

DT_DISPLAY_FMT = "%Y-%m-%d - %H:%M:%S"

DATE_TIME_FORMAT = "%Y-%m-%dT%H:%M:%S.%fZ"

AWS_REGION = "eu-west-1"

UNKNOWN_ID = "UNKNOWN"

DISCLAIMER_REGEX = (
    r"BLOOMBERG CREATED NOTE|\)?\s?FIRM DISCLAIMER|\)?\s?PERSONAL DISCLAIMER|\)?\s?DISCLAIMER"
)
CHAT_EVENT_REGEX = (
    r"has left the (room|chat)|"
    r"joined the (chat|room)|"
    r"ended the (chat|room)|"
    r"ended the deal|removed a post|"
    r"\[Bloomberg created note\:|"
    r"has invited the following users"
)


class AIResponseCols:
    NEXT_STEP = "nextStep"
    OTHER_RISKS = "otherRisks"
    SCORE = "score"
    SCORE_RATIONALE = "scoreRationale"
    SUGGESTED_RESOLUTION_CATEGORY = "suggestedResolutionCategory"
    SUGGESTED_RESOLUTION_COMMENT = "suggestedResolutionComment"
    SUGGESTED_RESOLUTION_SUB_CATEGORIES = "suggestedResolutionSubCategories"


JSON_REGEX = r"\{[^{}]+\}"

PROMPT_SYSTEM_MESSAGE = (
    "You are a Compliance Analyst working for a Regulated Financial Markets Firm"
)
PROMPT_SYSTEM_MESSAGE_ATTACHMENTS = (
    "You are designated as a Compliance Analyst at a Regulated Financial Markets Firm. "
    "Your primary role is to analyze communications flagged for potential regulatory issues. "
    "This includes communications where critical content, such as attachments, documents, "
    "or other information necessary for a thorough assessment, cannot be directly reviewed by you"
)

PROMPT_USER_MESSAGE = """Our Comms surveillance system flagged a <communication_type> as it contained the term <matched_term>.
Sender = <fromId>, Detection Policy=<detection_policy>.
Based on the content of the <communication_type> and the potential risks at hand, please analyze the situation and suggest a course of action.
Analyze these communications in the lens of what a compliance officer could deem relevant and worth being surfaced to him. Even if not immediately illegal or an explicit breach, the communication can still pose a risk to the firm or represent noncompliance with internal policies such as reporting mistakes or conflict handling. Such cases should also be flagged for further review. When flagging communications that warrant further investigation, assign a minimum score of 6, with higher scores for more serious concerns that require urgent attention.
The only Available resolution categories and sub-categories are as follows:
- 'POLICY_BREACH':  UNACCEPTABLE_PERSONAL_DISCUSSION, UNACCEPTABLE_INTERNAL_DISCUSSION, UNACCEPTABLE_EXTERNAL_DISCUSSION, REGULATORY_BREACH, REFERRED_TO_ENFORCEMENT, POLICY_BREACH, EXCHANGE_BREACH, OTHER
- 'NEAR_MISS':  ACCEPTABLE_PERSONAL_DISCUSSION, ACCEPTABLE_INTERNAL_DISCUSSION, ACCEPTABLE_EXTERNAL_DISCUSSION, OTHER
- 'FALSE_POSITIVE': LEXICA_REQUIRES_GREATER_CONTEXTUAL_SENSITIVITY, LEXICA_IS_PART_OF_A_DISCLAIMER, COMMUNICATION_IS_SPAM, COMMUNICATION_IS_PART_OF_AN_ALREADY_REVIEWED_EMAIL_THREAD,COMMUNICATION_IS_NOT_DIRECTED_AT_DETECTED_EMPLOYEE, COMMUNICATION_IS_AN_OUT_OF_OFFICE, COMMUNICATION_IS_A_SUPPORT_TICKET,COMMUNICATION_IS_A_RESEARCH_NOTE, COMMUNICATION_IS_A_PRICING_RUN, COMMUNICATION_IS_A_NEWSLETTER, COMMUNICATION_IS_A_TRADE_CONFIRM, OTHER
- 'OTHER': OTHER
Provide your response strictly in RFC8259 compliant JSON format with below fields: score(0-10), scoreRationale, nextStep, suggestedResolutionComment, suggestedResolutionCategory, suggestedResolutionSubCategories and otherRisks."""

PROMPT_USER_MESSAGE_ATTACHMENT = """Our surveillance system has flagged a <communication_type> because it contains the term <matched_term>, identified in the <term_location> of the email.
This was sent by <fromId> and was flagged under the detection policy <detection_policy>.
 Considering the inherent limitations of not being able to analyze all pertinent content directly, especially when key details may reside within inaccessible attachments or documents, please proceed to analyze the best available information based on best effort. If the main content lack all usable details and the flagged term is in an inaccessible attachments or documents, highlight this.
The only Available resolution categories and sub-categories are as follows:
- 'POLICY_BREACH':  UNACCEPTABLE_PERSONAL_DISCUSSION, UNACCEPTABLE_INTERNAL_DISCUSSION, UNACCEPTABLE_EXTERNAL_DISCUSSION, REGULATORY_BREACH, REFERRED_TO_ENFORCEMENT, POLICY_BREACH, EXCHANGE_BREACH, OTHER
- 'NEAR_MISS':  ACCEPTABLE_PERSONAL_DISCUSSION, ACCEPTABLE_INTERNAL_DISCUSSION, ACCEPTABLE_EXTERNAL_DISCUSSION, OTHER
- 'FALSE_POSITIVE': LEXICA_REQUIRES_GREATER_CONTEXTUAL_SENSITIVITY, LEXICA_IS_PART_OF_A_DISCLAIMER, COMMUNICATION_IS_SPAM, COMMUNICATION_IS_PART_OF_AN_ALREADY_REVIEWED_EMAIL_THREAD,COMMUNICATION_IS_NOT_DIRECTED_AT_DETECTED_EMPLOYEE, COMMUNICATION_IS_AN_OUT_OF_OFFICE, COMMUNICATION_IS_A_SUPPORT_TICKET,COMMUNICATION_IS_A_RESEARCH_NOTE, COMMUNICATION_IS_A_PRICING_RUN, COMMUNICATION_IS_A_NEWSLETTER, COMMUNICATION_IS_A_TRADE_CONFIRM, OTHER
- 'OTHER': OTHER
Response strictly in RFC8259 compliant JSON format with below fields:
"score": If the main content lack any usable details based on best effort, and the flagged term is in an inaccessible attachment, set to 5. Otherwise, adjust based on content review where 10 is when there is a definite financial regulatory issue and 0 when there is no financial regulatory issue at all,
"scoreRationale": Given the provided communication type and available content, the analysis is based on best effort including cite example and justification,
"nextStep": If the score is 5 due to attachments or documents limitations, suggest manual review of the attachments or documents by a compliance officer for a comprehensive evaluation. Detail other steps based on the content review,
"suggestedResolutionComment":  If the score is 5 due to attachments or documents limitations, mention constrain and recommend actions. If applicable, based on the review of available information,
"suggestedResolutionCategory": Leave blank if the analysis is totally hindered by the lack of attachment or document content; otherwise, fill based on content review.,
"suggestedResolutionSubCategory":  Leave blank if the analysis is totally hindered by the lack of attachment or document content; otherwise, fill based on content review.,
"otherRisks": Identify potential risks on best effort, noting any that may not be fully assessable without access to the attachment or document content only if important to assessment. Highlight the importance of a manual review where necessary."""


INITIAL_MULTI_PROJECT_INFO_BARRIER = "These people <project_team_names> are involved in multiple internal, confidential projects. The projects are: <project_name>."
PROJECT_PROMPT_MULTI_PROJECT_INFO_BARRIER = "For <project_name>, the project sensitivity is <project_sensitivity>. The project is classified as <project_types>. The project description is: <project_description>. The project started on <project_datetime_start>, and ends on <project_datetime_end>."

INITIAL_SINGLE_PROJECT_INFO_BARRIER = "These people <project_team_names> are involved in an internal, confidential project. The project is called: <project_name>. The project sensitivity is <project_sensitivity>. The project is classified as <project_types>. The project description is: <project_description>. The project started on <project_datetime_start>, and ends on <project_datetime_end>."


PROMPT_SYSTEM_MESSAGE_INFO_BARRIER = "You are the Chief Compliance Officer of a Regulated Financial Markets Trading Firm. Your Communications Surveillance solution captures all Calls/Emails/Chats/Meetings involving monitored individuals in your organisation. You assess these records for risk as defined."

PROMPT_USER_MESSAGE_INFO_BARRIER = """Our Surveillance System has Alerted on the below <communication_type> because it contains a related trigger '<matched_term>'.
- The <communication_type> was sent from: <fromId>.
- The <communication_type> was sent to: <participants_count>.
- It was sent on <comms_date>.
Provide your response strictly in RFC8259 compliant JSON format with below fields. Do not include any other explanation or details:
- "score": Please score the likelihood of this <communication_type> leaking sensitive information about the Project on a scale of 1-10.
- "scoreRationale": Write a concise summary and rationale as to whether you believe the <communication_type> indicates any 'Information Leakage' relating to the Project including cite example and justification."""

PROMPT_SYSTEM_MESSAGE_ATTACHMENT_INFO_BARRIER = "You are the Chief Compliance Officer of a Regulated Financial Markets Trading Firm. Your Communications Surveillance solution captures all Calls/Emails/Chats/Meetings involving monitored individuals in your organisation. This includes communications where critical content, such as attachments, documents, or other related information necessary for a thorough assessment, cannot be directly reviewed by you. You assess these records for risk as defined."

PROMPT_USER_MESSAGE_ATTACHMENT_INFO_BARRIER = """Our Surveillance System has Alerted on the below <communication_type> because it contains a related trigger '<matched_term>', identified in the '<term_location>'. 
- The <communication_type> was sent from: <fromId>.
- The <communication_type> was sent to: <participants_count>.
- It was sent on <comms_date>.
If the main content lack all usable details and the flagged term is in an inaccessible attachments or documents, highlight this, assess these records to best of your ability and if applicable, suggest manual review of the attachments or documents by a compliance officer for a comprehensive evaluation.
Provide your response strictly in RFC8259 compliant JSON format with below fields. Do not include any other explanation or details:
- "score": Please score the likelihood of this <communication_type> leaking sensitive information about the Project on a scale of 1-10. If the main content lack any usable details based on best effort, and the flagged term is in an inaccessible attachment, set score to <default_score>. Otherwise, adjust based on content review where 10 is when there is a definite financial regulatory issue and 0 when there is no financial regulatory issue at all.
- "scoreRationale": Given the provided communication type and available content, write concise summary and rationale as to whether you believe the email indicates any 'Information Leakage' relating to the Project. The analysis is based on best effort including cite example and justification."""

PROMPT_DICT = {
    "llama-3": {
        "PROMPT_SYSTEM_MESSAGE": PROMPT_SYSTEM_MESSAGE,
        "PROMPT_SYSTEM_MESSAGE_ATTACHMENTS": PROMPT_SYSTEM_MESSAGE_ATTACHMENTS,
        "PROMPT_USER_MESSAGE": PROMPT_USER_MESSAGE,
        "PROMPT_USER_MESSAGE_ATTACHMENT": PROMPT_USER_MESSAGE_ATTACHMENT,
        "INITIAL_MULTI_PROJECT_INFO_BARRIER": INITIAL_MULTI_PROJECT_INFO_BARRIER,
        "PROJECT_PROMPT_MULTI_PROJECT_INFO_BARRIER": PROJECT_PROMPT_MULTI_PROJECT_INFO_BARRIER,
        "INITIAL_SINGLE_PROJECT_INFO_BARRIER": INITIAL_SINGLE_PROJECT_INFO_BARRIER,
        "PROMPT_SYSTEM_MESSAGE_INFO_BARRIER": PROMPT_SYSTEM_MESSAGE_INFO_BARRIER,
        "PROMPT_USER_MESSAGE_INFO_BARRIER": PROMPT_USER_MESSAGE_INFO_BARRIER,
        "PROMPT_SYSTEM_MESSAGE_ATTACHMENT_INFO_BARRIER": PROMPT_SYSTEM_MESSAGE_ATTACHMENT_INFO_BARRIER,
        "PROMPT_USER_MESSAGE_ATTACHMENT_INFO_BARRIER": PROMPT_USER_MESSAGE_ATTACHMENT_INFO_BARRIER,
    },
    "llama-4": {
        "PROMPT_SYSTEM_MESSAGE": PROMPT_SYSTEM_MESSAGE,
        "PROMPT_SYSTEM_MESSAGE_ATTACHMENTS": PROMPT_SYSTEM_MESSAGE_ATTACHMENTS,
        "PROMPT_USER_MESSAGE": PROMPT_USER_MESSAGE,
        "PROMPT_USER_MESSAGE_ATTACHMENT": PROMPT_USER_MESSAGE_ATTACHMENT,
        "INITIAL_MULTI_PROJECT_INFO_BARRIER": INITIAL_MULTI_PROJECT_INFO_BARRIER,
        "PROJECT_PROMPT_MULTI_PROJECT_INFO_BARRIER": PROJECT_PROMPT_MULTI_PROJECT_INFO_BARRIER,
        "INITIAL_SINGLE_PROJECT_INFO_BARRIER": INITIAL_SINGLE_PROJECT_INFO_BARRIER,
        "PROMPT_SYSTEM_MESSAGE_INFO_BARRIER": PROMPT_SYSTEM_MESSAGE_INFO_BARRIER,
        "PROMPT_USER_MESSAGE_INFO_BARRIER": PROMPT_USER_MESSAGE_INFO_BARRIER,
        "PROMPT_SYSTEM_MESSAGE_ATTACHMENT_INFO_BARRIER": PROMPT_SYSTEM_MESSAGE_ATTACHMENT_INFO_BARRIER,
        "PROMPT_USER_MESSAGE_ATTACHMENT_INFO_BARRIER": PROMPT_USER_MESSAGE_ATTACHMENT_INFO_BARRIER,
    },
    "gpt-4": {
        "PROMPT_SYSTEM_MESSAGE": PROMPT_SYSTEM_MESSAGE,
        "PROMPT_SYSTEM_MESSAGE_ATTACHMENTS": PROMPT_SYSTEM_MESSAGE_ATTACHMENTS,
        "PROMPT_USER_MESSAGE": PROMPT_USER_MESSAGE,
        "PROMPT_USER_MESSAGE_ATTACHMENT": PROMPT_USER_MESSAGE_ATTACHMENT,
        "INITIAL_MULTI_PROJECT_INFO_BARRIER": INITIAL_MULTI_PROJECT_INFO_BARRIER,
        "PROJECT_PROMPT_MULTI_PROJECT_INFO_BARRIER": PROJECT_PROMPT_MULTI_PROJECT_INFO_BARRIER,
        "INITIAL_SINGLE_PROJECT_INFO_BARRIER": INITIAL_SINGLE_PROJECT_INFO_BARRIER,
        "PROMPT_SYSTEM_MESSAGE_INFO_BARRIER": PROMPT_SYSTEM_MESSAGE_INFO_BARRIER,
        "PROMPT_USER_MESSAGE_INFO_BARRIER": PROMPT_USER_MESSAGE_INFO_BARRIER,
        "PROMPT_SYSTEM_MESSAGE_ATTACHMENT_INFO_BARRIER": PROMPT_SYSTEM_MESSAGE_ATTACHMENT_INFO_BARRIER,
        "PROMPT_USER_MESSAGE_ATTACHMENT_INFO_BARRIER": PROMPT_USER_MESSAGE_ATTACHMENT_INFO_BARRIER,
    },
}


class CopilotAnalysisSourceFields:
    SOURCE = "source"
    MODEL = "model"
    VERSION = "version"
    ADDITIONAL_INFO = "additionalInfo"

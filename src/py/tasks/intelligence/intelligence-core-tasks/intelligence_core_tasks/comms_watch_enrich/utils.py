import copy
import logging
from intelligence_core_tasks.comms_watch_enrich.config import TASK_CONFIG
from intelligence_core_tasks.comms_watch_enrich.schema import CommsWatchEnrichMetrics
from se_elastic_schema.models import TenantConfiguration
from se_elastic_schema.static.surveillance import BehaviourQueryKind
from se_elastic_schema.static.tenant_configuration import FeatureFlags
from se_schema.models.surveillance.watch.model import SurveillanceWatch
from se_schema_meta import HASH, ID, KEY  # pants: no-infer-dep
from surveillance_utils.utils import is_info_barrier_watch
from typing import List

logger = logging.getLogger(__name__)


def remove_unneeded_meta(
    record: dict,
) -> dict:
    """Removes meta information to just keep &id and &key.

    :param record: Elastic Record
    :return: Record _id and _key as meta information
    """
    trimmed = copy.deepcopy(record)
    keep = [ID, KEY, HASH]
    for key in record.keys():
        if key.startswith("&") and key not in keep:
            trimmed.pop(key, None)
    return trimmed


def check_copilot_eligibility(
    tenant: str, watch: SurveillanceWatch, tenant_configuration: TenantConfiguration
) -> bool:
    """checks if tenant/watch should be sent through copilot.

    :param tenant:
    :param watch:
    :return:
    """
    if FeatureFlags.COMPLIANCE_COPILOT_COMMS not in tenant_configuration.featureFlags:
        logger.info(f"Tenant {tenant} not eligible for copilot.")
        return False
    elif not (watch.query.kind == BehaviourQueryKind.BEHAVIOUR or is_info_barrier_watch(watch)):
        logger.info(f"Watch {watch.id__} not eligible for copilot.")
        return False
    return True


def set_default_copilot_fallback(alert: dict, default_alert_score: int, reason: None | str = None):
    """Sets the fallback copilot analysis for an alert.

    :param alert: dict - The alert dictionary that needs to be updated.
    :param default_alert_score: int - The default score to be assigned to the alert.
    :param reason: None|str - Reason to add to scoreRationale.
    """
    score_rationale = (
        reason
        if reason
        else "There was an error while attempting to process this alert. Please contact our support team for further assistance."  # noqa: E501
    )
    alert["copilotAnalysis"] = {"score": default_alert_score, "scoreRationale": score_rationale}


def evaluate_alert_processing_limit(
    surveillance_watch: SurveillanceWatch,
    alerts: List,
    metrics: CommsWatchEnrichMetrics,
    processed_remaining_alerts: bool,
    default_alert_score: int,
):
    """Checks if the number of alerts exceeds the maximum allowed size for co-
    pilot processing. If the limit is reached, determines whether to skip all
    remaining alerts or just a portion of them.

    :param surveillance_watch: SurveillanceWatch Record to be used to create the watch execution
    :param alerts: list - List of alert objects to be processed.
    :param metrics: CommsWatchEnrichMetrics - Worker metrics.
    :param processed_remaining_alerts: bool - A flag indicating whether the
                    remaining alerts have already been processed.
    :param default_alert_score: int - The default score to be assigned to alerts

    :return:
    """
    if (
        is_info_barrier_watch(surveillance_watch)
        and metrics.input_count > TASK_CONFIG.MAX_INFO_BARRIER_ALERTS_SIZE
    ):
        logging.warning(
            msg=f"Number of alerts of watch exceed co-pilot"
            f" limit ({TASK_CONFIG.MAX_INFO_BARRIER_ALERTS_SIZE})"
        )

        if processed_remaining_alerts:
            for alert in alerts:
                set_default_copilot_fallback(
                    alert,
                    default_alert_score,
                    "You've reached your limit for this watch execution. Please contact our support team for further assistance.",  # noqa: E501
                )
            return [], alerts, processed_remaining_alerts
        else:
            processed_remaining_alerts = True
            remaining_alerts = TASK_CONFIG.MAX_INFO_BARRIER_ALERTS_SIZE - (
                metrics.input_count - len(alerts)
            )

            alerts_to_process = alerts[:remaining_alerts]
            alerts_to_skip = alerts[remaining_alerts:]

            for alert in alerts_to_skip:
                set_default_copilot_fallback(
                    alert,
                    default_alert_score,
                    "You've reached your limit for this watch execution. Please contact our support team for further assistance.",  # noqa: E501
                )
            return alerts_to_process, alerts_to_skip, processed_remaining_alerts
    else:
        return alerts, [], processed_remaining_alerts

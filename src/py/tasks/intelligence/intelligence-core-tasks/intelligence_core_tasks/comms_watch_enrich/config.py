from pydantic import BaseSettings, Field


class TaskConfig(BaseSettings):
    """This class is a global settings config for the task."""

    BATCH_SIZE: int = 16
    BATCH_SIZE_CHARS: int = 10**5
    ELASTIC_API_KEY: str
    INFO_BARRIER_COMPLETION_TOKEN_FACTOR: float = Field(
        1.1,
        description="Approximating the completion tokens of prompt total tokens (defaults to 10%).",
    )
    INFO_BARRIER_TOKEN_PROMPT_LIMIT: int = 108000
    MESSAGE_CONTEXT_SEARCH_SIZE: int = Field(20, max=1000)
    MAX_INFO_BARRIER_ALERTS_SIZE: int = Field(
        1000,
        description="Max info barrier alerts to go through co-pilot",
    )
    WATCH_COPILOT_TENANTS: str = ""


TASK_CONFIG = TaskConfig()

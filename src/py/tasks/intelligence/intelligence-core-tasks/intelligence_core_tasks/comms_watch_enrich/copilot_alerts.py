import copy
import json
import logging
import os
import re
from copilot_utils.client import SeOpenApiClient
from copilot_utils.config import OPENAI_CONFIG
from copilot_utils.static import OpenAIResponse
from datetime import datetime
from indict import Indict
from intelligence_core_tasks.comms_watch_enrich.config import TASK_CONFIG
from intelligence_core_tasks.comms_watch_enrich.schema import CommsWatchEnrichMetrics
from intelligence_core_tasks.comms_watch_enrich.static import (
    CHAT_EVENT_REGEX,
    DISCLAIMER_REGEX,
    PROMPT_DICT,
    UNKNOWN_ID,
    AIResponseCols,
    CopilotAnalysisSourceFields,
)
from intelligence_core_tasks.comms_watch_enrich.utils import (
    check_copilot_eligibility,
    set_default_copilot_fallback,
)
from operator import itemgetter
from se_elastic_schema.models import Message
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch
from se_elastic_schema.static.case import (
    ResolutionCategoryCommsEnum,
    ResolutionSubCategoryCommsEnum,
)
from se_elastic_schema.static.comms.analytics import AnalyticsSourceEnum
from se_elastic_schema.static.surveillance import BehaviourQueryKind
from se_es_utils.slim_record_handler import SlimRecordHandler
from se_schema_meta import ID
from surveillance_utils.utils import is_info_barrier_watch
from typing import Any, Dict, List

logger = logging.getLogger(__name__)

MODEL_GROUP_PREFIXES = {
    "llama-3": "llama-3",
    "gpt-4": "gpt-4",
    "llama-4": "llama-4",
}

PROMPT_DICT_KEY = next(
    (
        group
        for prefix, group in MODEL_GROUP_PREFIXES.items()
        if OPENAI_CONFIG.OPENAI_API_MODEL.startswith(prefix)
    ),
    OPENAI_CONFIG.OPENAI_API_MODEL,
)

MODEL_PROMPT_DICT = PROMPT_DICT.get(PROMPT_DICT_KEY)
if MODEL_PROMPT_DICT is None:
    raise ValueError(
        f"No prompt mapping found for the given model: {OPENAI_CONFIG.OPENAI_API_MODEL}"
        f"Must belong to {list(PROMPT_DICT.keys())}"
    )


def alerts_copilot(
    alerts: List[dict],
    tenant: str,
    workflow: str,
    record_handler: SlimRecordHandler,
    surveillance_watch: SurveillanceWatch,
    project_info: dict,
    client: SeOpenApiClient,
    metrics: CommsWatchEnrichMetrics,
    hosted_llm_failed: bool = False,
) -> List[dict]:
    """Run Copilot process to call chatGPT for every behavioural alert for
    eligible tenants. For each alert:

    1. Check if the tenant is eligible for Copilot Processing and alert is of type Behavioural
    2. Construct the prompt
    3. Call Azure OpenAI chatGTP and get the response
    4. Format the response and add it to the field copilotAnalysis of the
    CommunicationAlert model
    """
    default_alert_score = 5
    ai_booster_filter = 0
    final_alerts = []

    try:
        if not check_copilot_eligibility(tenant=tenant, watch=surveillance_watch):
            return alerts

        # Creating Defaults for Info Barrier watches
        if is_info_barrier_watch(surveillance_watch):
            project_info["watch_project_members_value"] = (
                surveillance_watch.query.infoBarrier.involvingProjectMembers
            )
            if surveillance_watch.query.infoBarrier.aiBooster:
                ai_booster_filter = surveillance_watch.query.infoBarrier.aiBooster
                default_alert_score = surveillance_watch.query.infoBarrier.aiBooster

        # run Copilot
        batch_size = int(os.environ.get("COPILOT_BATCH_SIZE", 10))

        if not hosted_llm_failed:
            for i in range(0, len(alerts), batch_size):
                batch = alerts[i : i + batch_size]
                try:
                    batch_result = _copilot_processing(
                        client=client,
                        alerts=copy.deepcopy(batch),
                        tenant=tenant,
                        workflow=workflow,
                        record_handler=record_handler,
                        project_info=project_info,
                        default_alert_score=default_alert_score,
                        watch_kind=surveillance_watch.query.kind,
                    )

                    final_alerts.extend(batch_result)
                except Exception as e:
                    logger.error(f"Error in batch {i}. Copilot failed. Error:{e}")
                    for alert in batch:
                        set_default_copilot_fallback(alert, default_alert_score)

                    final_alerts.extend(batch)
        else:
            for alert in alerts:
                set_default_copilot_fallback(alert, default_alert_score)
            final_alerts = alerts

        # For info barrier alert filtering
        # Default value to score and ai_booster_filter to avoid any unwanted filtering
        if is_info_barrier_watch(surveillance_watch):
            len_total_alerts = len(final_alerts)
            final_alerts = [
                alert
                for alert in final_alerts
                if alert.get("copilotAnalysis", {}).get("score", 10) >= ai_booster_filter
            ]
            if len_total_alerts != len(final_alerts):
                dropped = len_total_alerts - len(final_alerts)
                metrics.dropped_alerts += dropped

        return final_alerts
    except Exception as e:
        logger.error(f"Copilot Processing failed due to error: {e}")
        return alerts


def _copilot_processing(
    client: SeOpenApiClient,
    alerts: List[dict],
    tenant: str,
    workflow: str,
    record_handler: SlimRecordHandler,
    watch_kind: BehaviourQueryKind,
    project_info: dict,
    default_alert_score: int,
) -> List[dict]:
    """For each alert construct the prompt and then call Azure OpenAI chatGPT
    asynchronously.

    :param alerts: List[dict]
    :return: List[dict]
    """
    logger.info("Starting Copilot Processing")
    openai_config = []
    for alert in alerts:
        openai_config.append(
            _construct_prompt(
                client=client,
                alert=alert,
                tenant=tenant,
                record_handler=record_handler,
                watch_kind=watch_kind,
                project_info=project_info,
                default_alert_score=default_alert_score,
            )
        )
    response_list = _call_open_ai(
        client=client,
        openai_config=openai_config,
        default_alert_score=default_alert_score,
        tenant=tenant,
        workflow=workflow,
    )
    return response_list  # type: ignore


def transform_messages(query_results: list[dict[str, Any]]) -> list[str]:
    """Transform query results into formatted message strings, filtering out unwanted content.

    Filters out messages based on:
    - Zoning predictions indicating disclaimers
    - Bloomberg disclaimer regex patterns
    - Bloomberg chat event patterns

    :param query_results: List of flattened message dictionaries from Elasticsearch
    :return: List of formatted message strings for context
    """
    processed_results = []
    if query_results:
        query_results = sorted(query_results, key=itemgetter("timestamps.timestampStart"))
        for data in query_results:
            skip_flag = False
            for zoning_predictions in data.get("analytics.zoning.predictionOffsets", []):
                if zoning_predictions.get("class") == "disclaimer":
                    skip_flag = True
                    break

            # Additional disclaimer check
            if data.get("metadata.source.client") == "bloomberg" and re.match(
                DISCLAIMER_REGEX, data.get("body.text", "")
            ):
                skip_flag = True

            # Chat event check
            if data.get("metadata.source.client") == "bloomberg" and re.match(
                CHAT_EVENT_REGEX, data.get("body.text", "")
            ):
                skip_flag = True

            if not skip_flag:
                required_fields = ["identifiers.fromId", "body.text", "timestamps.timestampStart"]
                if all(field in data for field in required_fields):
                    processed_results.append(
                        (
                            data["identifiers.fromId"]
                            + " said "
                            + data["body.text"]
                            + " at "
                            + data["timestamps.timestampStart"]
                        )
                    )

    return processed_results


def fetch_messages_before_after(
    alert: dict, tenant: str, record_handler: SlimRecordHandler
) -> List[str]:
    """Fetch messages before and after an alert message. The context window is
    set by an env variable.

    :param alert: A dictionary containing details of the alert message.
    :param tenant: The identifier for the tenant.
    :param record_handler: An instance of SlimRecordHandler used for record handling.

    :return: A list containing messages before and after the alert message.
    """
    message_details = alert.get("hit", {})
    message_id = message_details.get("&id")
    message_start_time = message_details.get("timestamps", {}).get("timestampStart")
    message_start_timestamp = int(datetime.fromisoformat(message_start_time).timestamp() * 1000)
    room_id = message_details.get("roomId")

    if not message_id or not room_id or not message_start_time:
        logger.warning(
            f"Could not fetch contextual records for message {message_id} in room {room_id}"
            f"as message_start_time is {message_start_time}"
        )
        return []

    query: Dict[str, Any] = {
        "_source": {
            "includes": [
                "identifiers.fromId",
                "body.text",
                "timestamps.timestampStart",
                "analytics.zoning.predictionOffsets",
                "metadata.source.client",
            ]
        },
        "query": {
            "bool": {
                "must_not": [
                    {"exists": {"field": "&expiry"}},
                    # Adds filters to remove Bloomberg defined disclaimers
                    {
                        "term": {
                            "analytics.zoning.metadata.endpointName": "bloomberg-chat-disclaimer-regex"  # noqa: E501
                        }
                    },
                ],
                "filter": [
                    {"term": {"roomId": room_id}},
                    {"exists": {"field": "body.text"}},
                ],
            }
        },
        "sort": [
            {
                "timestamps.timestampStart": {
                    "order": "",
                    "missing": "_last",
                    "unmapped_type": "long",
                }
            },
            {"&id": {"order": "", "missing": "_last", "unmapped_type": "long"}},
        ],
        "search_after": [message_start_timestamp, message_id],
        "from": 0,
        "size": TASK_CONFIG.MESSAGE_CONTEXT_SEARCH_SIZE,
    }

    # Perform the search before search
    query["sort"][0]["timestamps.timestampStart"]["order"] = "desc"
    query["sort"][1]["&id"]["order"] = "desc"
    search_before_results = record_handler.search_records_by_query(
        model=Message, query=query, tenant=tenant
    )

    search_before_results = [
        Indict(hit.get("_source", {})).flatten().to_dict()
        for hit in search_before_results.get("hits", {}).get("hits", [])
    ]

    search_before_context = transform_messages(query_results=search_before_results)

    # Perform the search before search
    query["sort"][0]["timestamps.timestampStart"]["order"] = "asc"
    query["sort"][1]["&id"]["order"] = "asc"
    search_after_results = record_handler.search_records_by_query(
        model=Message, query=query, tenant=tenant
    )

    search_after_results = [
        Indict(hit.get("_source", {})).flatten().to_dict()
        for hit in search_after_results.get("hits", {}).get("hits", [])
    ]

    search_after_context = transform_messages(query_results=search_after_results)

    # Aggregating contextual message data so that it can be fit to GPT prompt.
    context_data = search_before_context + search_after_context

    return context_data


def _construct_prompt(
    client: SeOpenApiClient,
    alert: dict,
    tenant: str,
    record_handler: SlimRecordHandler,
    watch_kind: BehaviourQueryKind,
    project_info: dict,
    default_alert_score: int,
) -> dict:
    """Constructs the prompt and communication required to call Azure OpenAI
    chatGPT.

    :param alert: List[dict]
    :param record_handler: An instance of SlimRecordHandler used for record handling.
    :param watch_kind: BehaviourQueryKind,
    :param project_info Project info in case of info barrier watch
    :param default_alert_score Default score in case prompt creation fails
    :return: dict
    """
    try:
        if watch_kind == BehaviourQueryKind.BEHAVIOUR:
            prompt, system_prompt, communication, communication_type = _construct_prompt_behaviour(
                alert
            )
        else:
            prompt, system_prompt, communication, communication_type = (
                _construct_prompt_info_barrier(alert, project_info, default_alert_score)
            )

        # Fetch contextual information when the communication type is a Message
        if communication_type == Message.get_reference().name:
            context_messages = fetch_messages_before_after(
                alert=alert, tenant=tenant, record_handler=record_handler
            )
            if context_messages:
                communication += (
                    "\n" + f"The {TASK_CONFIG.MESSAGE_CONTEXT_SEARCH_SIZE} messages before "
                    f"and after the triggered message are provided below: \n"
                )
                communication += "\n".join(context_messages)

        if watch_kind == BehaviourQueryKind.TEMPLATE:
            merged_prompt = [[{"content": system_prompt + prompt}]]
            filtered_prompt = client.filter_prompts_based_on_token_quota(
                merged_prompt,
                TASK_CONFIG.INFO_BARRIER_TOKEN_PROMPT_LIMIT,
                0,
                TASK_CONFIG.INFO_BARRIER_COMPLETION_TOKEN_FACTOR,
            )
            if any(item is None for item in filtered_prompt):
                logger.error(
                    "Prompt size bigger than token size expected"
                    f" ({TASK_CONFIG.INFO_BARRIER_TOKEN_PROMPT_LIMIT}),"
                    f" email id: {alert['hit'][ID]}"
                )

                set_default_copilot_fallback(alert, default_alert_score)
                return {"alert": alert}

        return {
            "alert": alert,
            "prompt": prompt,
            "system_prompt": system_prompt,
            "communication": communication,
        }
    except Exception as e:
        logger.error(f"Error constructing prompt:{e}, email id: {alert['hit'][ID]}")
        set_default_copilot_fallback(alert, default_alert_score)
        return {"alert": alert}


def _construct_prompt_behaviour(alert: dict):
    # Check if any of the lexica hits was generated by content within the attachment.
    # If true can the prompts so that GPT can better assess it.
    if MODEL_PROMPT_DICT is None:
        raise ValueError("Model prompt dict not initialized")

    hit_source_mask = [
        lexica.get("source") == AnalyticsSourceEnum.ATTACHMENTS_TEXT
        for lexica in alert.get("hit", {}).get("analytics", {}).get("lexica", [])
    ]
    if any(hit_source_mask):
        logger.info("Attachment hit detected. Using attachment prompts.")
        user_prompt_template = MODEL_PROMPT_DICT["PROMPT_USER_MESSAGE_ATTACHMENT"]
        system_prompt = MODEL_PROMPT_DICT["PROMPT_SYSTEM_MESSAGE_ATTACHMENTS"]
    else:
        user_prompt_template = MODEL_PROMPT_DICT["PROMPT_USER_MESSAGE"]
        system_prompt = MODEL_PROMPT_DICT["PROMPT_SYSTEM_MESSAGE"]

    subject = alert.get("hit", {}).get("subject")
    from_id = alert.get("hit", {}).get("identifiers", {}).get("fromId", UNKNOWN_ID)
    to_ids = alert.get("hit", {}).get("identifiers", {}).get("toIds", UNKNOWN_ID)
    body = alert.get("hit", {}).get("body", {}).get("text")
    communication_type = alert.get("hitModel", "")
    matched_term = alert.get("matchedLexica")
    detection_policy = alert.get("matchedLexicaCategories")
    prompt = (
        user_prompt_template.replace("<communication_type>", str(communication_type))
        .replace("<matched_term>", str(matched_term))
        .replace("<detection_policy>", str(detection_policy))
        .replace("<fromId>", str(from_id))
        .replace("<toIds>", str(to_ids))
        .replace(
            "<term_location>",
            AnalyticsSourceEnum.ATTACHMENTS_TEXT if any(hit_source_mask) else "None",
        )
    )
    communication = (
        f"Here is the flagged {communication_type}: "
        f"{f'Subject: {subject} ' if subject else ''}\nBody: {body}"
    )
    return prompt, system_prompt, communication, communication_type


def _construct_prompt_info_barrier(alert: dict, project_info: dict, default_alert_score: int):
    # Check if any of the lexica hits was generated by content within the attachment.
    # If true can the prompts so that GPT can better assess it.
    if MODEL_PROMPT_DICT is None:
        raise ValueError("Model prompt dict is not initialized.")
    hit_source_mask = [
        lexica.get("source") == AnalyticsSourceEnum.ATTACHMENTS_TEXT
        for lexica in alert.get("hit", {}).get("analytics", {}).get("lexica", [])
    ]
    if any(hit_source_mask):
        logger.info("Attachment hit detected. Using attachment prompts.")
        user_prompt_template = MODEL_PROMPT_DICT["PROMPT_USER_MESSAGE_ATTACHMENT_INFO_BARRIER"]
        system_prompt = MODEL_PROMPT_DICT["PROMPT_SYSTEM_MESSAGE_ATTACHMENT_INFO_BARRIER"]
    else:
        user_prompt_template = MODEL_PROMPT_DICT["PROMPT_USER_MESSAGE_INFO_BARRIER"]
        system_prompt = MODEL_PROMPT_DICT["PROMPT_SYSTEM_MESSAGE_INFO_BARRIER"]

    projects_envolved = sorted(list(set(alert.get("matchedLexicaCategories", []))))

    project_member = [
        name for behaviour in projects_envolved for name in project_info[behaviour]["employeeIds"]
    ]
    project_name = [project_info[behaviour]["name"] for behaviour in projects_envolved]

    if len(projects_envolved) > 1:
        initial_prompt_template = MODEL_PROMPT_DICT["INITIAL_MULTI_PROJECT_INFO_BARRIER"]

        initial_prompt = initial_prompt_template.replace(
            "<project_team_names>", str(", ".join(project_member))
        ).replace("<project_name>", str(", ".join(project_name)))
        project_complete_prompt = ""
        for behaviour in projects_envolved:
            project_prompt = MODEL_PROMPT_DICT["PROJECT_PROMPT_MULTI_PROJECT_INFO_BARRIER"]
            project_complete_prompt += (
                project_prompt.replace("<project_name>", str(project_info[behaviour]["name"]))
                .replace(
                    "<project_sensitivity>",
                    str(project_info[behaviour].get("sensitivity", "Medium")),
                )
                .replace(
                    "<project_types>", str(", ".join(project_info[behaviour].get("types", [])))
                )
                .replace(
                    "<project_description>", str(project_info[behaviour].get("description", ""))
                )
                .replace(
                    "<project_datetime_start>",
                    str(project_info[behaviour].get("dateTimeStart", "")),
                )
                .replace(
                    "<project_datetime_end>", str(project_info[behaviour].get("dateTimeStart", ""))
                )
            )
        initial_prompt = initial_prompt + project_complete_prompt
    else:
        initial_prompt_template = MODEL_PROMPT_DICT["INITIAL_SINGLE_PROJECT_INFO_BARRIER"]
        project_sensitivity = project_info[projects_envolved[0]].get("sensitivity", "Medium")
        project_types = project_info[projects_envolved[0]].get("types", [])
        project_description = project_info[projects_envolved[0]].get("description", "")
        project_datetime_start = project_info[projects_envolved[0]].get("dateTimeStart", "")
        project_datetime_end = project_info[projects_envolved[0]].get("dateTimeEnd", "")

        initial_prompt = (
            initial_prompt_template.replace("<project_team_names>", str(", ".join(project_member)))
            .replace("<project_name>", str(", ".join(project_name)))
            .replace("<project_sensitivity>", str(project_sensitivity))
            .replace("<project_types>", str(", ".join(project_types)))
            .replace("<project_description>", str(project_description))
            .replace("<project_datetime_start>", str(project_datetime_start))
            .replace("<project_datetime_end>", str(project_datetime_end))
        )

    subject = alert.get("hit", {}).get("subject")
    from_id = alert.get("hit", {}).get("identifiers", {}).get("fromId", UNKNOWN_ID)
    comms_date = alert.get("hit", {}).get("timestamps", {}).get("timestampStart")
    participants_count = alert.get("hit", {}).get("stats", {}).get("participantsCount")
    to_ids = alert.get("hit", {}).get("identifiers", {}).get("toIds", UNKNOWN_ID)
    body = alert.get("hit", {}).get("body", {}).get("text")
    communication_type = alert.get("hitModel", "")
    matched_term = alert.get("matchedLexica", [])
    detection_policy = alert.get("matchedLexicaCategories", [])
    prompt = (
        user_prompt_template.replace("<communication_type>", str(communication_type))
        .replace("<matched_term>", str(", ".join(matched_term)))
        .replace("<detection_policy>", str(", ".join(detection_policy)))
        .replace("<fromId>", str(from_id))
        .replace("<toIds>", str(to_ids))
        .replace("<participants_count>", str(participants_count))
        .replace("<comms_date>", str(comms_date))
        .replace("<default_score>", str(default_alert_score))
        .replace(
            "<term_location>",
            AnalyticsSourceEnum.ATTACHMENTS_TEXT if any(hit_source_mask) else "None",
        )
    )

    prompt = initial_prompt + prompt

    communication = (
        f"Here is the flagged {communication_type}: "
        f"{f'Subject: {subject} ' if subject else ''}\nBody: {body}"
    )
    return prompt, system_prompt, communication, communication_type


def _prepare_prompt(
    client: SeOpenApiClient,
    prompt: str,
    system_prompt: str,
    communication: str,
):
    """
    Constructs a list of prompt dictionaries suitable for the OpenAI API.

    This function takes a user-provided prompt, a system-level prompt, and
    a communication string, and structures them into a list of dictionaries,
    each representing a message with a defined role ("system" or "user").
    It utilizes the `construct_prompt_with_role` method of the provided
    `SeOpenApiClient` to create each prompt dictionary.

    :param client: An instance of the `SeOpenApiClient` used for constructing
                   prompt dictionaries with roles.
    :param prompt: The main user-provided prompt string. This will be assigned
                   the "user" role.
    :param system_prompt: The system-level prompt string. This will be assigned
                          the "system" role, providing context or instructions
                          to the language model.
    :param communication: An additional communication string from the user.
                          This will also be assigned the "user" role.
    :returns: A list of dictionaries, where each dictionary has "role" and
              "content" keys, representing the prepared prompt sequence for
              the OpenAI API. The order of the prompts in the list is:
              system prompt, user prompt, communication.
    """
    return [
        client.construct_prompt_with_role(role="system", prompt_content=system_prompt),
        client.construct_prompt_with_role(role="user", prompt_content=prompt),
        client.construct_prompt_with_role(role="user", prompt_content=communication),
    ]


def _process_llm_response(
    client: SeOpenApiClient,
    llm_response_list: list[OpenAIResponse],
    record_indices: list[int],
    openai_config: list[dict],
    default_alert_score: int,
) -> list[dict]:
    """Call Azure OpenAI ChatGPT with the given prompt and communication and
    gets the json response within the completion response. max_tokens=16000
    because that is the max that the model(specified as engine below) allows.

    :param client: An instance of `SeOpenApiClient` used for parsing the
                   raw LLM responses into their content strings.
    :param llm_response_list: A list of raw `OpenAIResponse` objects, each
                               containing the completion from an LLM call.
    :param record_indices: A list of integers where each integer represents
                           the original index in the `openai_config` list
                           that corresponds to the respective LLM response
                           in `llm_response_list`. This ensures correct
                           mapping of processed results.
    :param openai_config: A list of dictionaries, where each dictionary
                          represents an existing alert record that needs to be
                          enriched with the LLM's analysis. The order corresponds
                          to the `record_indices`.
    :param default_alert_score: The default score (integer) to assign to an
                                alert's 'copilotAnalysis' in case the LLM
                                response is empty, malformed, or its processing
                                encounters an error.
    :returns: A list of dictionaries, which are the original `openai_config`
              dictionaries, now updated and enriched with the 'copilotAnalysis'
              field (either from the LLM response or a fallback), maintaining
              their original order.
    """
    processed_alert_list = []
    completion_list = client.parse_content_from_response(response_list=llm_response_list)
    for idx, completion in zip(record_indices, completion_list):
        alert = openai_config[idx].get("alert", {})
        try:
            if completion:
                result = json.loads(completion)
                alert["copilotAnalysis"] = _validate_and_enrich_result(value=result)
            else:
                logger.error(
                    f"Empty completion from copilot response, email id: {alert['hit'][ID]}"
                )
                set_default_copilot_fallback(alert, default_alert_score)

        except Exception as e:
            logger.error(f"Error while processing copilot response:{e}")
            set_default_copilot_fallback(alert, default_alert_score)

        processed_alert_list.append(alert)

    return processed_alert_list


def _call_open_ai(
    client: SeOpenApiClient,
    openai_config: List[dict],
    default_alert_score: int,
    tenant: str,
    workflow: str,
):
    """Calls asynchronously the method to call OpenAI ChatGPT.

    :param openai_config: List[dict]
    :param default_alert_score Default score in case prompt creation fails
    :param tenant: Tenant for which the task is run
    :param workflow: Workflow to which the task belongs to
    :return: List[dict]
    """
    to_be_processed_records = dict()
    results = []
    for idx, config in enumerate(openai_config):
        system_prompt = config.get("system_prompt", "")
        alert = config.get("alert", {})
        prompt = config.get("prompt", "")

        if not system_prompt:
            set_default_copilot_fallback(alert, default_alert_score)
            results.append(alert)
        else:
            to_be_processed_records[idx] = _prepare_prompt(
                client=client,
                prompt=prompt,
                system_prompt=system_prompt,
                communication=config.get("communication", ""),
            )

    llm_result = client.call_open_ai(
        prompt_list=list(to_be_processed_records.values()),
        tenant=tenant,
        workflow=workflow,
    )

    processed_result = _process_llm_response(
        client=client,
        llm_response_list=llm_result,
        record_indices=list(to_be_processed_records.keys()),
        openai_config=openai_config,
        default_alert_score=default_alert_score,
    )
    results.extend(processed_result)

    return results


def _validate_and_enrich_result(value: dict) -> dict:
    """Validates the source dict to its corresponding schema enums and adds
    additional field 'source'.

    :param value: dict
    :return: dict
    """
    # remove extra columns from AI response
    required_keys = {
        AIResponseCols.SCORE,
        AIResponseCols.SCORE_RATIONALE,
        AIResponseCols.NEXT_STEP,
        AIResponseCols.SUGGESTED_RESOLUTION_COMMENT,
        AIResponseCols.SUGGESTED_RESOLUTION_CATEGORY,
        AIResponseCols.SUGGESTED_RESOLUTION_SUB_CATEGORIES,
        AIResponseCols.OTHER_RISKS,
    }
    value = {key: value for key, value in value.items() if key in required_keys}
    # score is a mandatory column according to schema. So setting default 0
    value.setdefault(AIResponseCols.SCORE, 0)

    conversion_config = [
        {
            "source_field": "suggestedResolutionCategory",
            "schema_enum": ResolutionCategoryCommsEnum,
            "schema_type": "string",
            "default_value": None,
        },
        {
            "source_field": "suggestedResolutionSubCategories",
            "schema_enum": ResolutionSubCategoryCommsEnum,
            "schema_type": "List",
            "default_value": None,
        },
    ]
    value = _str_to_enum(value=value, conversion_config=conversion_config)
    if isinstance(value.get("otherRisks"), list):
        # sometimes response from chatGPT has empty list [] but it is an Optional[str] as per schema
        value["otherRisks"] = value["otherRisks"][0] if value.get("otherRisks") else None
    value["source"] = {
        CopilotAnalysisSourceFields.SOURCE: "AZURE OPENAI",
        CopilotAnalysisSourceFields.MODEL: OPENAI_CONFIG.OPENAI_API_MODEL,
        CopilotAnalysisSourceFields.VERSION: OPENAI_CONFIG.OPENAI_API_VERSION,
    }
    return value


def _str_to_enum(value: dict, conversion_config: List[dict]) -> dict:
    """Validates and converts the string values to schema enums.

    :param value: dict
    :param conversion_config: List[dict]
    :return: dict
    """
    for config in conversion_config:
        suggested_value = value.get(config["source_field"])
        if not suggested_value:
            value[config["source_field"]] = config["default_value"]
        elif config["schema_type"] == "List":
            allowed_values = config["schema_enum"].__dict__["_member_names_"]
            if not isinstance(value[config["source_field"]], list):
                value[config["source_field"]] = [value[config["source_field"]]]
            value[config["source_field"]] = [
                getattr(config["schema_enum"], x).value
                for x in value[config["source_field"]]
                if x in allowed_values
            ]
            if not value[config["source_field"]]:
                # check for empty list
                value[config["source_field"]] = None
        else:
            if isinstance(suggested_value, list):
                suggested_value = suggested_value[0]
            if not hasattr(config["schema_enum"], suggested_value):
                value[config["source_field"]] = config["default_value"]
            else:
                value[config["source_field"]] = getattr(
                    config["schema_enum"], suggested_value
                ).value
    return value

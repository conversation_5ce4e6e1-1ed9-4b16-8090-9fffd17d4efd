# type: ignore
import copy
import datetime
import pytest
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from fsspec.implementations.local import LocalFileSystem
from lexica_matcher.lexica_model_storage import LexicaModelStorage
from lexica_processor.lexica_annotator import LexicaAnnotator
from lexica_processor.main import lexica_processor
from pathlib import Path
from se_elastic_schema.static.comms.analytics import AnalyticsSourceEnum
from surveillance_utils.test_mock_helpers import fake_get_tenant_workflow
from tests_lexica_processor.comms_records_fixtures import load_models
from tests_lexica_processor.comms_records_helpers import comm_ndjson, open_ndjson
from tests_lexica_processor.mock_tika_client import mock_extract
from unittest.mock import MagicMock

assert comm_ndjson
CURRENT_PATH = Path(__file__).parent


orig = copy.deepcopy(LexicaAnnotator.process_comms)


def mock_process_comms(*args, **kwargs):
    from apache_tika_client.api.extractor import Extractor

    setattr(Extractor, "extract", mock_extract)
    return orig(*args, **kwargs)


def initialize_lexica_processor_params(
    file_uri: str, data_model: str, ignore_attachments_size: float = None
) -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="Test",
        stack="stack",
        tenant="test_good_storage",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri=file_uri,
            data_model=data_model,
            ignore_attachments_size=ignore_attachments_size,
        )
    )
    task = TaskFieldSet(name="lexica_processor", version="latest", success=False)
    input = AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
    return input


def test_lexica_processor_task(
    comm_ndjson,
    monkeypatch,
):
    file_uri = f"{CURRENT_PATH}/data/testing_files/comms_test_ndjson.ndjson"
    data_model = "se_elastic_schema.models.tenant.communication.email:Email"

    monkeypatch.setattr(CachedTenantWorkflowAPIClient, "get", fake_get_tenant_workflow)
    monkeypatch.setattr(LexicaAnnotator, "process_comms", mock_process_comms)

    aries_task_input = initialize_lexica_processor_params(file_uri, data_model)

    LexicaModelStorage.load = MagicMock()
    LexicaModelStorage.load.return_value = load_models()

    def wrapped_put_file(self, path1, path2, callback=None, **kwargs):
        self.auto_mkdir = True
        self.cp_file(path1, path2, **kwargs)

    monkeypatch.setattr(LocalFileSystem, "put_file", wrapped_put_file)

    task_output = lexica_processor(aries_task_input=aries_task_input)

    response = open_ndjson(task_output.output_param.params["file_uri"])

    assert len(response) == len(comm_ndjson)

    # Multiple Hits on body
    assert len(response[0]["analytics"]["lexica"]) == 2
    assert response[0]["analytics"]["lexica"][0]["term"] == "its a breach"
    assert response[0]["analytics"]["lexica"][1]["term"] == "maybe a jam"

    # Multiple Hits on subject
    assert len(response[2]["analytics"]["lexica"]) == 2
    assert response[2]["analytics"]["lexica"][0]["source"] == AnalyticsSourceEnum.SUBJECT_TEXT

    # Hits on body_edits and attachments
    assert len(response[3]["analytics"]["lexica"]) == 2
    assert response[3]["analytics"]["lexica"][0]["source"] == AnalyticsSourceEnum.EDITS_TEXT
    assert response[3]["analytics"]["lexica"][1]["source"] == AnalyticsSourceEnum.ATTACHMENTS_TEXT

    # Hits only on attachments
    assert len(response[1]["analytics"]["lexica"]) == 1
    assert response[1]["analytics"]["lexica"][0]["source"] == AnalyticsSourceEnum.ATTACHMENTS_TEXT

    # No hits, no analytics
    assert response[4].get("analytics") is None

    # metric evaluation
    task_metrics = task_output.app_metric.metrics["custom"]["lexica_processor"]
    assert task_metrics["errored_attachments_count"] == 0
    assert task_metrics["errored_batch_count"] == 0
    assert task_metrics["errored_comms_count"] == 0
    assert task_metrics["successful_comms_count"] == 10


def test_task_lexica_processor_invalid_records(
    monkeypatch,
):
    file_uri = f"{CURRENT_PATH}/data/testing_files/comms_test_ndjson.ndjson"
    data_model = "se_elastic_schema.models.tenant.communication.transcript:Transcript"
    monkeypatch.setattr(CachedTenantWorkflowAPIClient, "get", fake_get_tenant_workflow)

    aries_task_input = initialize_lexica_processor_params(file_uri, data_model)

    with pytest.raises(Exception) as e:
        lexica_processor(aries_task_input=aries_task_input)

    assert "all communications must be of type commstype" in str(e).lower()


def test_task_lexica_processor_invalid_input(
    monkeypatch,
):
    file_uri = f"{CURRENT_PATH}/data/testing_files/comms_test_ndjson.ndjson"
    monkeypatch.setattr(CachedTenantWorkflowAPIClient, "get", fake_get_tenant_workflow)

    workflow = WorkflowFieldSet(
        name="Test",
        stack="stack",
        tenant="test_good_storage",
        start_timestamp=datetime.datetime.utcnow(),
    )

    input_param = IOParamFieldSet(params=dict(file_uri=file_uri))

    task = TaskFieldSet(name="lexica_processor", version="latest", success=False)
    wrong_input = AriesTaskInput(workflow=workflow, input_param=input_param, task=task)

    with pytest.raises(ValueError):
        lexica_processor(aries_task_input=wrong_input)


def test_task_lexica_processor_empty_file_uri(
    monkeypatch,
):
    monkeypatch.setattr(CachedTenantWorkflowAPIClient, "get", fake_get_tenant_workflow)

    workflow = WorkflowFieldSet(
        name="Test",
        stack="stack",
        tenant="test_good_storage",
        start_timestamp=datetime.datetime.utcnow(),
    )

    input_param = IOParamFieldSet(params=dict(file_uri=None))

    task = TaskFieldSet(name="lexica_processor", version="latest", success=False)
    wrong_input = AriesTaskInput(workflow=workflow, input_param=input_param, task=task)

    with pytest.raises(ValueError):
        lexica_processor(aries_task_input=wrong_input)


def test_task_lexica_processor_empty_file_uri_with_ignore_empty_file_uri(
    monkeypatch,
):
    monkeypatch.setattr(CachedTenantWorkflowAPIClient, "get", fake_get_tenant_workflow)

    workflow = WorkflowFieldSet(
        name="Test",
        stack="stack",
        tenant="test_good_storage",
        start_timestamp=datetime.datetime.utcnow(),
    )

    file_uri = None
    ignore_empty_file_uri = True
    data_model = "se_elastic_schema.models.tenant.communication.email:Email"
    input_param = IOParamFieldSet(
        params=dict(
            file_uri=file_uri, ignore_empty_file_uri=ignore_empty_file_uri, data_model=data_model
        )
    )
    task = TaskFieldSet(name="lexica_processor", version="latest", success=True)
    aries_task_input = AriesTaskInput(workflow=workflow, input_param=input_param, task=task)

    lexica_processor(aries_task_input)


def test_lexica_processor_task_with_ignore_attachments_size(
    mocker,
    comm_ndjson,
    monkeypatch,
):
    file_uri = f"{CURRENT_PATH}/data/testing_files/comms_test_ndjson_multipl_att.ndjson"
    data_model = "se_elastic_schema.models.tenant.communication.email:Email"

    monkeypatch.setattr(CachedTenantWorkflowAPIClient, "get", fake_get_tenant_workflow)
    monkeypatch.setattr(LexicaAnnotator, "process_comms", mock_process_comms)

    aries_task_input = initialize_lexica_processor_params(file_uri, data_model, 3.6)

    LexicaModelStorage.load = MagicMock()
    LexicaModelStorage.load.return_value = load_models()

    def wrapped_put_file(self, path1, path2, callback=None, **kwargs):
        self.auto_mkdir = True
        self.cp_file(path1, path2, **kwargs)

    monkeypatch.setattr(LocalFileSystem, "put_file", wrapped_put_file)

    mocker.patch("integration_audit.auditor.write_json")

    task_output = lexica_processor(aries_task_input=aries_task_input)

    response = open_ndjson(task_output.output_param.params["file_uri"])

    assert len(response) == 11

    count_has_attachment = 0
    count_does_not_have_attachment = 0

    count_with_attachment_analytics = 0
    count_without_attachment_analytics = 0

    count_skipped_attachment = 0
    count_not_skipped_attachment = 0

    for email in response:
        att = email.get("attachments", [])
        if att:
            count_has_attachment += 1
            for attach in att:
                analytics = attach.get("analytics", {})
                size = attach.get("sizeInBytes", 0)
                if analytics:
                    count_with_attachment_analytics += 1
                else:
                    count_without_attachment_analytics += 1

                if size < 3600000:
                    count_not_skipped_attachment += 1
                else:
                    count_skipped_attachment += 1
        else:
            count_does_not_have_attachment += 1

    assert count_has_attachment == 8
    assert count_with_attachment_analytics == 5
    assert count_without_attachment_analytics == 6
    assert count_does_not_have_attachment == 3
    assert count_skipped_attachment == 6
    assert count_not_skipped_attachment == 5

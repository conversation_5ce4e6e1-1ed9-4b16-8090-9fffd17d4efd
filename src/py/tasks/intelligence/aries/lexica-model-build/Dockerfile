ARG SE_MONO_PY11_BASE_SUPERUSER
FROM $SE_MONO_PY11_BASE_SUPERUSER

# Installing libpq-dev which is required for sqlalchemy postgres DB engine psycopg2
RUN apt update \
    && apt install -y --no-install-recommends \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# switch to non-root user before copying pex
USER app
# ensure that following ENTRYPOINT, COPY, ARG, and ENV statements are always added to the end of dockerfile in this order for optimal caching
ENTRYPOINT ["/bin/app/__main__.py"]
COPY --chown=app:app src.py.tasks.intelligence.aries.lexica-model-build/bin.pex /bin/app
ARG IMAGE_TAG
ENV SE_VERSION=$IMAGE_TAG


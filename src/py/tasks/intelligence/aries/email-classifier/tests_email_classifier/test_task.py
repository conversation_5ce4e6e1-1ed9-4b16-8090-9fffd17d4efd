import addict
import fsspec
import json
import pytest
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_core_tasks.utilities.elasticsearch_utils import get_es_config
from aries_se_core_tasks.utilities.environment_variables import ElasticEnvVars
from aries_task_link.models import AriesTaskInput
from elasticsearch6 import Elasticsearch
from email_classifier.main import run_email_classifier
from fsspec.implementations.local import LocalFileSystem
from pathlib import Path
from se_elasticsearch.repository.elasticsearch6 import ElasticsearchRepository
from se_es_utils.slim_record_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from surveillance_utils.test_mock_helpers import (
    data_lake,
    dump_file_into_datalake,
    fake_get_tenant_workflow,
)

EMAIL_INGRESS_DIR = "./aries/ingest/email/2023/06/12/A_TRACE_ID/B_STACK__email_transform/"


@pytest.fixture
def mock_fsspec_local(monkeypatch):
    def wrapped_put_file(self, path1, path2, callback=None, **kwargs):
        self.auto_mkdir = True
        self.cp_file(path1, path2, **kwargs)

    monkeypatch.setattr(LocalFileSystem, "put_file", wrapped_put_file)


def fake_aries_task_input(input_file: str) -> AriesTaskInput:
    return AriesTaskInput.validate(
        {
            "workflow": {
                "start_timestamp": "**********",
                "name": "email-workflow",
                "stack": "local",
                "tenant": "foxtrot",
            },
            "task": {"name": "email-classifier-task", "version": "", "success": True},
            "input_param": {"params": {"file_uri": input_file, "cloud_provider": "local"}},
        }
    )


@pytest.mark.parametrize(
    "watch_id,expected_hits",
    [("a_watch_filters_spam", 35), ("a_watch_dont_filter", 36), (None, 36)],
)
def test_run_email_classifier(watch_id: str, expected_hits: int, mock_fsspec_local, monkeypatch):
    def fake_es_get(self, index, id):
        watch: addict.Dict = addict.Dict(
            {
                "body": {
                    "_source": {
                        "executedOn": "2023-11-01T10:00:00.000Z",
                        "watch": {
                            "name": "fake_watch",
                            "query": {
                                "name": "fake_query",
                                "description": "fake_description",
                                "kind": "BEHAVIOUR",
                                "lexicaBehaviour": {"name": "oi gato", "languages": ["pt"]},
                            },
                        },
                    }
                }
            }
        )
        if id == "a_watch_filters_spam":
            watch["body"]["_source"]["watch"]["query"]["falsePositiveReduction"] = {
                "excludedClassifications": [{"className": "spam", "confidenceScore": 0.5}]
            }
        return watch

    monkeypatch.setattr(CachedTenantWorkflowAPIClient, "get", fake_get_tenant_workflow)
    monkeypatch.setattr(Elasticsearch, "get", fake_es_get)
    monkeypatch.setattr(Elasticsearch, "ping", lambda self: True)
    monkeypatch.setattr(ElasticEnvVars, "ELASTIC_HOST", "localhost")

    monkeypatch.setattr(
        SlimRecordHandler, "get_es_repo", lambda *a, **k: ElasticsearchRepository(get_es_config())
    )
    from integration_audit import auditor

    monkeypatch.setattr(auditor, "get_cloud_provider_prefix", lambda x: ".")

    test_data_path = str(
        (Path(__file__).parent / "data" / "conductor_test_emails.ndjson").absolute()
    )
    with data_lake():
        input_file = dump_file_into_datalake(EMAIL_INGRESS_DIR, test_data_path)

        aries_task_input = fake_aries_task_input(input_file)
        if watch_id:
            aries_task_input.input_param.params["watch_execution_id"] = watch_id

        output = run_email_classifier(aries_task_input)

        output_file = output.output_param.params["file_uri"]

        records = []
        with fsspec.open(output_file, mode="r") as f:
            for line in f:
                records.append(json.loads(line))

        # Correct number of comms records were processed
        assert len(records) == expected_hits

        # Analytics match expected results (record 31 is supposed to skip)
        for res_comm in records[:31] + records[32:-1]:
            assert res_comm["analytics"]["classifier"]["predictedClasses"] == ["newsletters"]
            assert res_comm["analytics"]["classifier"]["predictions"][2]["value"] == 1.0

        if watch_id != "a_watch_filters_spam":
            # Last record must be spam but not excluded
            assert records[-1]["analytics"]["classifier"]["predictedClasses"] == ["spam"]
            assert records[-1]["analytics"]["classifier"]["predictions"][-3]["value"] == 1.0

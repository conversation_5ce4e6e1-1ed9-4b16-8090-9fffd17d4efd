# ruff: noqa: E501

import addict
import copy
import gc
import logging
import orjson
import tempfile
import time
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_io_event.app_metric import AppMetricFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskR<PERSON>ult
from aries_task_link.task import aries_task
from email_classifier.classifier import predict_batch
from email_classifier.config import TASK_CONFIG
from email_classifier.schemas.email_request import EmailRequest
from email_classifier.schemas.input_params import EmailClassifierParams
from email_classifier.schemas.internal import ClassifierInnerMetrics, ClassifierOutput
from email_classifier.watch_filtering import filter_emails_with_watch
from integration_audit.auditor import (
    AuditorStaticFields,
    get_record_identifier,
    upload_audit,
    upsert_audit,
)
from se_elastic_schema.components.communication.common_analytics import CommonAnalytics
from se_elastic_schema.models.tenant.communication.email import Email
from se_enums.cloud import <PERSON><PERSON><PERSON>ider<PERSON>num
from se_es_utils.slim_record_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from se_io_utils.batching_utils import batching_generator, upload_temporary_file
from se_io_utils.json_utils import append_to_ndjson, write_named_temporary_json
from se_schema_meta import ID
from surveillance_utils.utils import create_output_path
from surveillance_utils.watch_execution_handling import fetch_watch_execution_record
from typing import Any, Dict

logger = logging.getLogger(__name__)
RECORD_HANDLER = None


def pre_flight_email(email: Dict[str, Any]) -> EmailRequest:
    # Handle missing metadata gracefully
    header_from = ""

    header = email.get("metadata", {}).get("header", {})
    if header.get("From"):
        header_from = header["From"]
    elif header.get("from"):
        header_from = header["from"]
    elif email.get("identifiers", {}).get("fromId"):
        header_from = email["identifiers"]["fromId"]

    if not header_from:
        raise ValueError("Email is missing 'sender' in metadata!")

    res = EmailRequest(
        header_from=header_from,
        subject=email.get("subject", ""),
        body_lines=email.get("body", {}).get("text", ""),
        attachment_names=[att.get("fileName", "") for att in email.get("attachments", []) or []],
    )
    return res


def format_classifier_analytics(
    task_name: str, classification: ClassifierOutput
) -> CommonAnalytics:
    analytics = addict.Dict()
    analytics.classifier.metadata.endpointName = task_name
    analytics.classifier.metadata.predictedClassesThreshold = 0.5
    analytics.classifier.predictedClasses = classification.predicted_categories
    analytics.classifier.predictions = [
        {"class_": k, "value": v} for k, v in classification.confidence.items()
    ]
    return CommonAnalytics(**analytics)


def handle_skip(
    skipped_emails: list[Dict[str, Any]],
    streamed: bool,
    cloud_provider: CloudProviderEnum,
    audit_filepath: str,
) -> None:
    # Add audit
    audit_result: Dict[str, dict] = {}
    for email in skipped_emails:
        rec_id = (
            get_record_identifier(
                streamed=streamed,
                record=email,
                data_model=Email,
                cloud_provider=cloud_provider,
                already_unflattened=True,
            )
            or "unknown"
        )
        audit_result[rec_id] = {
            AuditorStaticFields.ERRORED: 1,
            AuditorStaticFields.STATUS: ["Failed to be processed by email-classifier"],
        }
    upsert_audit(
        audit_path=audit_filepath,
        streamed=streamed,
        input_data=audit_result,
        models=[Email],
    )


@aries_task()
def run_email_classifier(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    params = EmailClassifierParams.validate(aries_task_input.input_param.params)
    task_name = aries_task_input.task.name

    # Fetch surveillance_watch
    surveillance_watch = None
    if params.watch_execution_id:
        global RECORD_HANDLER
        if RECORD_HANDLER is None:
            RECORD_HANDLER = SlimRecordHandler()

        surveillance_watch = fetch_watch_execution_record(
            params.watch_execution_id, aries_task_input.workflow.tenant, RECORD_HANDLER
        ).watch

    # Get tenant workflow tenant config from postgres
    cached_tenant_workflow_config = CachedTenantWorkflowAPIClient.get(
        stack_name=aries_task_input.workflow.stack,
        tenant_name=aries_task_input.workflow.tenant,
        workflow_name=aries_task_input.workflow.name,
    )

    cloud_provider = CloudProviderEnum(cached_tenant_workflow_config.tenant.cloud)
    streamed: bool = cached_tenant_workflow_config.workflow.streamed

    output_params = copy.deepcopy(aries_task_input.input_param)
    output_path = create_output_path(
        aries_task_input,
        cloud_provider_enum=cloud_provider,
        file_uri=aries_task_input.input_param.params["file_uri"],
        file_name_diff="_update_alerted",
    )

    output_path_dropped = create_output_path(
        aries_task_input,
        cloud_provider_enum=cloud_provider,
        file_uri=aries_task_input.input_param.params["file_uri"],
        file_name_diff="_update_dropped",
    )

    output_params.params["file_uri"] = output_path
    output_params.params["update_dropped"] = output_path_dropped

    audit_filepath = write_named_temporary_json(content={}, output_filename="audit.json")
    inner_metrics = ClassifierInnerMetrics()
    pre_flight_time = 0.0

    input_count, skipped_count, hit_count = 0, 0, 0

    if params.watch_execution_id:
        dropped_path = tempfile.NamedTemporaryFile(suffix="dropped.ndjson", delete=False).name

    for temp_file_path, email_list_str in batching_generator(
        [params.file_uri],
        output_path,
        TASK_CONFIG.BATCH_SIZE,
        TASK_CONFIG.BATCH_SIZE_CHARS,
    ):
        batch_start = input_count
        input_count += len(email_list_str)
        logger.info(f"Processing records {batch_start} through {input_count}")

        email_list = [orjson.loads(line) for line in email_list_str]
        del email_list_str
        gc.collect()

        try:
            # Extract fields from emails
            start = time.time()
            batch = []
            to_process_emails = []
            skipped_emails = []
            for email in email_list:
                try:
                    batch.append(pre_flight_email(email))
                    to_process_emails.append(email)
                except ValueError as e:
                    logger.warning(
                        f"Error processing email {email.get(ID, 'unknown')}: {type(e)} -- SKIPPED"
                    )
                    skipped_emails.append(email)

            pre_flight_time += time.time() - start

            # Pass through models
            results, inner_metrics = predict_batch(batch, TASK_CONFIG.BATCH_SIZE, inner_metrics)

            # Add analytics to emails in-place
            for email, classification in zip(to_process_emails, results):
                analytics = format_classifier_analytics(task_name, classification)
                email["analytics"] = email.get("analytics", {})
                email["analytics"]["classifier"] = analytics.dict(exclude_none=True)["classifier"]

            if params.watch_execution_id:
                to_process_emails, dropped_emails = filter_emails_with_watch(
                    to_process_emails, surveillance_watch
                )

                dropped_emails = [
                    {ID: email[ID], "analytics": {"classifier": email["analytics"]["classifier"]}}
                    for email in dropped_emails
                ]
                append_to_ndjson(dropped_path, dropped_emails)

        except Exception as e:
            logger.error(f"FAILURE IN BATCH {batch_start} through {input_count} - {type(e)}, {e}")
            skipped_emails = email_list
            to_process_emails = []

        if skipped_emails:
            skipped_count += len(skipped_emails)
            handle_skip(skipped_emails, streamed, cloud_provider, audit_filepath)

        # Write to output file
        append_to_ndjson(temp_file_path, to_process_emails + skipped_emails)

        hit_count += len(email_list)

    if params.watch_execution_id:
        # Upload dropped emails
        upload_temporary_file(temp_file_path=dropped_path, output_path=output_path_dropped)

    # Upload Ingestion Audit
    upload_audit(
        aries_task_input=aries_task_input,
        audit_filepath=audit_filepath,
        cloud_provider=cloud_provider,
    )

    # Create Metrics
    app_metric = update_app_metrics(
        aries_task_input_name=task_name,
        input_count=input_count,
        skipped_count=skipped_count,
        pre_flight_time=pre_flight_time,
        inner_metrics=inner_metrics,
    )

    # Flow path decision
    if params.watch_execution_id:
        if hit_count > 0:
            logger.info(f"{hit_count} hits after classifier filtering")
            output_params.params["flow_path"] = "hits_found"
        else:
            output_params.params["flow_path"] = "no_hits"

    return AriesTaskResult(output_param=output_params, app_metric=app_metric)


def update_app_metrics(
    aries_task_input_name: str,
    input_count: int,
    skipped_count: int,
    pre_flight_time: float,
    inner_metrics: ClassifierInnerMetrics,
) -> AppMetricFieldSet:
    predict_time = round(
        inner_metrics.move_time
        + inner_metrics.bert_time
        + inner_metrics.token_time
        + inner_metrics.head_time,
        5,
    )
    app_metrics = AppMetricFieldSet(
        metrics={
            "custom": {
                aries_task_input_name: {
                    "input_count": input_count,
                    "skipped_count": skipped_count,
                    "classified_count": input_count - skipped_count,
                    "pre_flight_time": round(pre_flight_time, 5),
                    "move_time": round(inner_metrics.move_time, 5),
                    "bert_time": round(inner_metrics.bert_time, 5),
                    "token_time": round(inner_metrics.token_time, 5),
                    "head_time": round(inner_metrics.head_time, 5),
                    "predict_time": predict_time,
                }
            }
        }
    )
    logger.info(orjson.dumps(app_metrics.dict()))
    return app_metrics

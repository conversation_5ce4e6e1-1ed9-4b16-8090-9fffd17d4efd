{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "2b03cb49-6708-09ef-4d5f-31ae181a96f0", "&key": "SurveillanceWatch:2b03cb49-6708-09ef-4d5f-31ae181a96f0:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "wash_trading___test_case_10_1", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"maxPriceDifference\": 5, \"maxTimeWindow\": 86400, \"maxVolumeDifference\": 0.05, \"excludeMatchingTimestamps\": true, \"minimumTradedQuantity\": 100, \"numberOfCounterparties\": \"single\"}", "marketAbuseReportType": "WASH_TRADING", "name": "test_case_10_1", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeblotter.mar.washtrades.10.1.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}
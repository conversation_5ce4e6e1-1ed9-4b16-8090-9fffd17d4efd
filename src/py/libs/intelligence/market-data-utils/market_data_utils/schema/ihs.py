import enum
from market_data_utils.schema.base import BaseColumns


class CDSCreditSingleColumns(BaseColumns):
    RED_CODE = "RedCode"
    TENOR = "Tenor"
    TIER = "Tier"
    CURRENCY = "Currency"
    DOC_CLAUSE = "DocClause"
    DATE = "Date"
    PRIMARY_PRICE_TYPE = "PrimaryPriceType"
    CONV_SPREAD_MID = "ConvSpreadMid"
    UP_FRONT_MID = "UpfrontMid"
    CONV_SPREAD_ASK = "ConvSpreadAsk"
    UP_FRONT_ASK = "UpfrontAsk"
    CONV_SPREAD_BID = "ConvSpreadBid"
    UP_FRONT_BID = "UpfrontBid"
    ESTIMATED_NOTIONAL = "EstimatedNotional"
    PRIMARY_COUPON = "PrimaryCoupon"


class CDSCreditIndexColumns(BaseColumns):
    INDEX_RED_CODE = "IndexRedCode"
    INDEX_TENOR = "IndexTenor"
    CURRENCY = "Currency"
    DATE = "Date"
    COMPOSITE_PRICE_MID = "CompositePriceMid"
    COMPOSITE_PRICE_ASK = "CompositePriceAsk"
    COMPOSITE_PRICE_BID = "CompositePriceBid"
    COMPOSITE_SPREAD_MID = "CompositeSpreadMid"


class CDSCreditTrancheColumns(BaseColumns):
    INDEX_RED_CODE = "IndexRedCode"
    INDEX_TERM = "IndexTerm"
    ATTACHMENT = "Attachment"
    DETACHMENT = "Detachment"
    DATE = "Date"
    TRANCHE_UP_FRONT_MID = "TrancheUpfrontMid"
    TRANCHE_UP_FRONT_ASK = "TrancheUpfrontAsk"
    TRANCHE_UP_FRONT_BID = "TrancheUpfrontBid"


class LoansColumns(BaseColumns):
    LOAN_X_ID = "LoanX ID"
    CLOSE_DATE = "Close Date"
    EVALUATED_PRICE = "Evaluated Price"
    CLOSE_OFFER = "Close Offer"
    CLOSE_BID = "Close Bid"


class LoansDeltaColumns(BaseColumns):
    LOAN_X_ID = "LoanX ID"
    CURRENCY = "Currency"


class ExtractionStatus(enum.Enum):
    RUNNING = "RUNNING"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"

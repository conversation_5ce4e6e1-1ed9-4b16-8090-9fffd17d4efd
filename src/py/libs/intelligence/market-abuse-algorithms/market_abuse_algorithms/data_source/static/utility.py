from market_abuse_algorithms.data_source.static.sdp.order import OrderField


class OrderStateValueColumns:
    ORDER_STATE_VALUE = "orderStateValue"
    OPTIONS_TYPE = "optionsType"
    ORDER_STATE_PNL = "orderStatePNL"
    CONVERTED_ORDER_STATE_VALUE = "convertedOrderStateValue"
    PRICE_QUANTITY_NOTATION = "priceQuantityNotation"
    CONVERTED_PRICE = "convertedPrice"


class PNLColumns:
    ORDER_STATE_EOD_CLOSE_PRICE = "orderStatePNLClosePrice"
    ORDER_STATE_TRADE_DATE = "orderStateTradeDate"
    PNL_VALUE = "PNLValue"
    PNL_VALUE_BY_RECORD = "pnlValueByRecord"
    PNL_VALUE_ORIGINAL = "pnlValueOriginal"


class DFColumns:
    CCY_CONVERSION_DATE = "currencyConversionDate"
    EVENT_DAYS = "event_days"
    INSTRUMENT = "instrument"
    RIC = "RIC"


class InstrumentRegexPatterns:
    NON_DERIVATIVES = "^([DEC][A-Z]{5})|(JES[A-Z]{3})|(IF[A-Z]{4})|(IC[A-Z]{4})|(FCE[A-Z]{3})$"
    DERIVATIVES = "^(FFS[A-Z]{3})|(FED[A-Z]{3})|(O[A-Z]{2}S[A-Z]{2})|(HES[A-Z]{3})|(SE[SB][A-Z]{3})|(R[PSWS][A-Z]{4})$"  # noqa: E501
    POSSIBLE_INSTRUMENTS = "^(FFS[A-Z]{3})|(FED[A-Z]{3})|(O[A-Z]{2}S[A-Z]{2})|(HES[A-Z]{3})|(SE[SB][A-Z]{3})|(R[PSWS][A-Z]{4})|([DEC][A-Z]{5})|(JES[A-Z]{3})|(IC[A-Z]{4})|(IF[A-Z]{4})|(FCE[A-Z]{3})$"  # noqa: E501
    # CDS RegexPatterns must NOT be included in NON_DERIVATIVES and POSSIBLE_INSTRUMENTS/DERIVATIVES
    CDS = "^(SCI[A-Z]{3})|(SCV[A-Z]{3})|(SCU[A-Z]{3})$"


class OrderVolume:
    WEIGHTED_EXECUTION_PRICE = "Order Volume Weighted Execution Price"


class CommonColumns:
    CCY_CONVERSION_RATE = "currencyConversionRate"
    CCY_CONVERSION_DATE = "currencyConversionDate"
    OBSERVATION_PERIOD_AMOUNT = "observationPeriodAmount"
    SEGMENT_AMOUNT_LIST = "segmentAmountList"


ORDER_STATE_CALCULATION_TYPES_MAP = {
    OrderStateValueColumns.OPTIONS_TYPE: [
        OrderField.INST_DERIV_PRICE_MULTIPLIER,
        OrderField.INST_DERIV_STRIKE_PRICE,
        OrderField.INST_CLASSIFICATION,
    ],
    OrderStateValueColumns.PRICE_QUANTITY_NOTATION: [
        OrderField.TRX_DTL_PC_NOTATION,
        OrderField.TRX_DTL_QUANTITY_NOTATION,
        OrderField.PC_FD_PRICE,
    ],
}


class EvaluationTypeThreshold:
    COMPANY = "Company"
    COUNTERPARTY = "Counterparty"
    DESK = "Desk"
    EXECUTING_ENTITY = "ExecutingEntity"
    FUND = "Fund"
    PORTFOLIO_MANAGER = "PortfolioManager"
    TRADER = "Trader"


EVALUATION_TYPE_FIELD_MAPPING = {
    EvaluationTypeThreshold.COMPANY: None,
    EvaluationTypeThreshold.COUNTERPARTY: OrderField.COUNTERPARTY_ID,
    EvaluationTypeThreshold.DESK: OrderField.TRD_ALGO_FIRM_DESKS_ID,
    EvaluationTypeThreshold.EXECUTING_ENTITY: OrderField.RPT_DTL_EXC_ENTITY_FILE_IDENTIFIER,
    EvaluationTypeThreshold.FUND: OrderField.CLIENT_FILE_IDENTIFIER,
    EvaluationTypeThreshold.PORTFOLIO_MANAGER: OrderField.TRD_ALGO_FILE_IDENTIFIER,
    EvaluationTypeThreshold.TRADER: OrderField.TRADER_FILE_IDENTIFIER,
}

EVALUATION_TYPE_FALL_BACK = {
    EvaluationTypeThreshold.COMPANY: None,
    EvaluationTypeThreshold.COUNTERPARTY: None,
    EvaluationTypeThreshold.DESK: OrderField.TRADER_FILE_IDENTIFIER,
    EvaluationTypeThreshold.EXECUTING_ENTITY: None,
    EvaluationTypeThreshold.FUND: OrderField.TRADER_FILE_IDENTIFIER,
    EvaluationTypeThreshold.PORTFOLIO_MANAGER: None,
    EvaluationTypeThreshold.TRADER: None,
}

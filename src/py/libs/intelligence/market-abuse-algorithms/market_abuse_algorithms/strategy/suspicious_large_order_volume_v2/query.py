# ruff: noqa: E501
import addict
import pandas as pd
import time
from datetime import date
from elasticsearch_dsl.query import Exists, Range, Terms
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import OrderExecutionsQuery, OrderQuery
from market_abuse_algorithms.data_source.query.utils import (
    remove_date_range_from_iris_filters,
)
from market_abuse_algorithms.data_source.repository.market_data.client import (
    get_market_client,
)
from market_abuse_algorithms.data_source.repository.market_data.static import (
    StatsColumns,
    TradeStatsColumns,
)
from market_abuse_algorithms.data_source.static.sdp.order import NewColumns, OrderField
from market_abuse_algorithms.mar_audit.mar_audit import AggregatedStepAudit, StepAudit
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.strategy.base.static import QueryAggScripts
from market_abuse_algorithms.strategy.base.strategy import (
    market_abuse_audit_object,
    singleton_audit_object,
)
from market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.static import (
    GENERAL_GROUPING_MAP,
    DFColumns,
    EventType,
    MarketDataEvaluationType,
    NormaliseBehaviour,
    ThresholdsNames,
)
from market_abuse_algorithms.strategy.utils import convert_parent_id_to_parent_key
from market_abuse_algorithms.utils.data import process_numeric_columns
from market_abuse_algorithms.utils.filters import filter_non_business_days
from market_abuse_algorithms.utils.processment import (
    get_query_string_to_audit,
    new_instrument_combination_with_rics,
)
from se_elastic_schema.static.mar_audit import DropReason, StepType
from se_elastic_schema.static.mifid2 import OrderStatus
from se_market_data_utils.schema.parquet import QuoteTickColumns
from se_market_data_utils.schema.refinitiv import RefinitivEventType
from typing import Dict, List, Optional, Tuple, Union


def get_eval_type_required_fields(general_evaluation_type) -> List:
    """Returns the required fields for the queries, excluding possible None
    values.

    :param general_evaluation_type:
    :return: List of required fields
    """
    required_fields = [
        GENERAL_GROUPING_MAP.get(general_evaluation_type),
    ]
    required_fields = [i for i in required_fields if i is not None]

    return required_fields


DROP_NAN_COLUMNS = [
    OrderField.META_KEY,
    OrderField.EXC_DTL_BUY_SELL_IND,
    OrderField.PC_FD_PRICE,
]

INCLUDES_FIELDS = [
    OrderField.CLIENT_IDENT_CLIENT_NAME,
    OrderField.CLIENT_FILE_IDENTIFIER,
    OrderField.COUNTERPARTY_NAME,
    OrderField.COUNTERPARTY_ID,
    OrderField.EXC_DTL_BUY_SELL_IND,
    OrderField.EXC_DTL_ORD_STATUS,
    OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN,
    OrderField.INST_EXT_UNIQUE_IDENT,
    OrderField.INST_ID_CODE,
    OrderField.INST_EXT_ALT_IDENT,
    OrderField.INST_FULL_NAME,
    OrderField.META_ID,
    OrderField.META_KEY,
    OrderField.META_PARENT,
    OrderField.ORD_IDENT_ID_CODE,
    OrderField.PARTICIPANTS,
    OrderField.PC_FD_PRICE,
    OrderField.RPT_DTL_EXC_ENTITY_NAME,
    OrderField.RPT_DTL_EXC_ENTITY_FILE_IDENTIFIER,
    OrderField.TRADER,
    OrderField.TRADER_FILE_IDENTIFIER,
    OrderField.TRD_ALGO_FIRM_DESKS,
    OrderField.TRD_ALGO_FIRM_DESKS_ID,
    OrderField.TRD_ALGO_FIRM_NAME,
    OrderField.TS_ORD_SUBMITTED,
    OrderField.TS_ORD_UPDATED,
    OrderField.TS_TRADING_DATE_TIME,
    OrderField.TRD_ALGO_FILE_IDENTIFIER,
    *OrderField.get_client_fields(),
    *OrderField.get_venue_fields(),
]


class Queries(BaseQuery):
    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)

        self._th_general_evaluation_type = self.context.thresholds.dict().get(
            ThresholdsNames.GENERAL_EVALUATION_TYPE
        )

        self._th_execution_notional_value_currency = self.context.thresholds.dict().get(
            ThresholdsNames.EXECUTION_NOTIONAL_VALUE_CURRENCY
        )
        self._th_normalise_behaviour = self.context.thresholds.dict().get(
            ThresholdsNames.NORMALISE_BEHAVIOUR
        )
        self._market_data_client = get_market_client(tenant=context.tenant)

        self.REQUIRED_FIELDS = [OrderField.META_KEY]
        self.EVAL_REQUIRED_FIELDS = get_eval_type_required_fields(self._th_general_evaluation_type)

    def cases_to_analyse(self, event_type: EventType):
        """Get the cases to apply the algorithm.

        :param event_type: EventType. Enum with orders type
        """

        if event_type == EventType.NEWOS:
            query = self._get_order_query(add_default_conditions=True)

        elif event_type == EventType.EXECUTIONS:
            query = self._get_order_executions_query(add_default_conditions=True)

        else:
            raise ValueError(f"`{event_type}` not valid for this strategy: {event_type}")

        market_abuse_audit_object.query = get_query_string_to_audit(query=query)

        self.inspect_required_fields(query, fields=self.REQUIRED_FIELDS, update_audit_metrics=True)

        instruments_combinations: List[List[str]] = self.get_instruments_combinations(
            query=query,
            agg_script=QueryAggScripts.UNIQUE_IDENTIFIER_PRIORITY_SCRIPT_NON_DERIV,
        )

        instrument_id_ric_mapping: Union[dict, pd.DataFrame] = self._market_data_client.get_ric_map(
            instrument_combinations=instruments_combinations
        )

        new_instrument_combinations_with_ric: List = new_instrument_combination_with_rics(
            instrument_combinations=instruments_combinations,
            instrument_ric_mapping=instrument_id_ric_mapping,
        )
        for instruments_and_rics in new_instrument_combinations_with_ric:
            start_audit, method_id = self.get_start_time_and_unique_id()
            inst_comb = [inst for inst, ric in instruments_and_rics]
            start = time.perf_counter()
            if event_type == EventType.NEWOS:
                result = self._execute_query(
                    order_state_query=False,
                    instr_comb=inst_comb,
                )
                end, method_id = self.get_start_time_and_unique_id()
                if result.empty:
                    singleton_audit_object.write_audit_data_to_local_files(
                        StepAudit(
                            step_id=method_id,
                            list_of_order_ids=[],
                            list_of_instruments=inst_comb,
                            reason=f"No NEWOs were found for the instruments {inst_comb}.",
                        )
                    )
                    singleton_audit_object.write_audit_data_to_local_files(
                        AggregatedStepAudit(
                            aggregated_step_id=method_id,
                            start=start_audit,
                            end=end,
                            number_of_dropped_orders=0,
                            number_of_input_orders=0,
                            number_of_resulting_orders=0,
                            step_name="Initial Data Retrieval",
                            step_type=StepType.FILTERS,
                            drop_reason=DropReason.RECORDS_DROPPED_FETCH_DATA,
                        )
                    )
                    self.GLOBAL_START_TIME = end
                    continue

                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=start_audit,
                        end=end,
                        number_of_dropped_orders=0,
                        number_of_input_orders=len(result),
                        number_of_resulting_orders=len(result),
                        step_name="Initial Data Retrieval",
                        step_type=StepType.FILTERS,
                    )
                )
                self.GLOBAL_START_TIME = end

                result: pd.DataFrame = self.process_result(
                    date_col=OrderField.TS_ORD_SUBMITTED,
                    data=result,
                    event_type=event_type,
                )

                pre_result_keys = result.get(OrderField.META_KEY, pd.Series()).tolist()

                end, method_id = self.get_start_time_and_unique_id()
                if len(result) < len(pre_result_keys):
                    singleton_audit_object.write_audit_data_to_local_files(
                        StepAudit(
                            step_id=method_id,
                            list_of_order_ids=list(
                                set(pre_result_keys)
                                - set(result.get(OrderField.META_KEY, pd.Series()).tolist())
                            ),
                            reason="Dropped records after processing initial data to remove empty fields "
                            "and non business days.",
                        )
                    )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=start_audit,
                        end=end,
                        number_of_dropped_orders=len(pre_result_keys) - len(result),
                        number_of_input_orders=len(pre_result_keys),
                        number_of_resulting_orders=len(result),
                        step_name="Processing of Initial Data",
                        step_type=StepType.FILTERS,
                        drop_reason=DropReason.RECORDS_DROPPED_FETCH_DATA,
                    )
                )
                self.GLOBAL_START_TIME = end

            elif event_type == EventType.EXECUTIONS:
                result = self._execute_query(
                    order_state_query=True,
                    instr_comb=inst_comb,
                )
                end, method_id = self.get_start_time_and_unique_id()
                if result.empty:
                    singleton_audit_object.write_audit_data_to_local_files(
                        StepAudit(
                            step_id=method_id,
                            list_of_order_ids=[],
                            list_of_instruments=inst_comb,
                            reason=f"No executions were found for the instruments {inst_comb}",
                        )
                    )
                    singleton_audit_object.write_audit_data_to_local_files(
                        AggregatedStepAudit(
                            aggregated_step_id=method_id,
                            start=start_audit,
                            end=end,
                            number_of_dropped_orders=0,
                            number_of_input_orders=0,
                            number_of_resulting_orders=0,
                            step_name="Initial Data Retrieval",
                            step_type=StepType.FILTERS,
                            drop_reason=DropReason.RECORDS_DROPPED_FETCH_DATA,
                        )
                    )
                    self.GLOBAL_START_TIME = end
                    continue

                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=start_audit,
                        end=end,
                        number_of_dropped_orders=0,
                        number_of_input_orders=len(result),
                        number_of_resulting_orders=len(result),
                        step_name="Initial Data Retrieval",
                        step_type=StepType.FILTERS,
                    )
                )
                self.GLOBAL_START_TIME = end

                pre_result_keys = result.get(OrderField.META_KEY, pd.Series()).tolist()

                result: pd.DataFrame = self.process_result(
                    date_col=OrderField.TS_TRADING_DATE_TIME,
                    data=result,
                    event_type=event_type,
                )
                end, method_id = self.get_start_time_and_unique_id()
                if len(result) < len(pre_result_keys):
                    singleton_audit_object.write_audit_data_to_local_files(
                        StepAudit(
                            step_id=method_id,
                            list_of_order_ids=list(
                                set(pre_result_keys)
                                - set(result.get(OrderField.META_KEY, pd.Series()).tolist())
                            ),
                            reason="Dropped records after processing initial data to remove empty fields "
                            "and non business days.",
                        )
                    )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=start_audit,
                        end=end,
                        number_of_dropped_orders=len(pre_result_keys) - len(result),
                        number_of_input_orders=len(pre_result_keys),
                        number_of_resulting_orders=len(result),
                        step_name="Processing of Initial Data",
                        step_type=StepType.FILTERS,
                        drop_reason=DropReason.RECORDS_DROPPED_FETCH_DATA,
                    )
                )
                self.GLOBAL_START_TIME = end

            else:
                raise ValueError(f"`{event_type}` not valid for this strategy: {event_type}")

            if result.empty:
                self._logger.info(
                    f"Dataset empty after processing the event_type query result. Skipping instruments {inst_comb}."
                )
                continue

            self._logger.info(
                f"For a instrument combination of size {len(inst_comb)}, it took {time.perf_counter() - start} seconds"
            )

            try:
                result[DFColumns.RIC] = result[OrderField.INST_EXT_UNIQUE_IDENT].map(
                    dict(instruments_and_rics)
                )
            except KeyError:
                self._logger.warning(
                    f"RIC mapping failed because the dataframe does not have the {OrderField.INST_EXT_UNIQUE_IDENT} column. "
                    f"Skipping instruments {inst_comb}."
                )
                continue

            yield result

    def _base_query(
        self,
        query: Union[OrderQuery, OrderExecutionsQuery],
        instr_comb: Optional[List[str]] = None,
        add_default_conditions: bool = False,
    ) -> Union[OrderQuery, OrderExecutionsQuery]:
        """create base query to be used.

        :param query:
        :param instr_comb:
        :param add_default_conditions:

        :return:
        """
        if add_default_conditions:
            self.add_default_conditions_to_query(query)

        ccy_field = (
            OrderField.get_best_exc_trx_ecb_ref_rate_ccy(self._th_execution_notional_value_currency)
            if self._th_execution_notional_value_currency is not None
            else None
        )

        evaluation_type_exists: List = self.get_eval_exist_conditions()

        if evaluation_type_exists:
            query.add_condition(mode="should", conditions=evaluation_type_exists)

        for field in self.REQUIRED_FIELDS:
            existing_filters = query.to_dict().get("query").get("bool").get("filter")
            if existing_filters is not None:
                existing_field = list(
                    filter(
                        lambda item: item is not None,
                        [val.get("exists", {}).get("field", None) for val in existing_filters],
                    )
                )
                if field in existing_field:
                    continue
                else:
                    query.exists(field=field)
            else:
                query.exists(field=field)

        includes_fields = INCLUDES_FIELDS
        if (
            self._th_execution_notional_value_currency
            and ccy_field is not None
            and ccy_field not in INCLUDES_FIELDS
        ):
            includes_fields = INCLUDES_FIELDS + [ccy_field]

        query.includes(includes_fields)

        if instr_comb:
            query.instrument_id(instr_comb)

        return query

    def _get_order_executions_query(
        self,
        instr_comb: Optional[List[str]] = None,
        add_default_conditions: bool = False,
    ) -> OrderExecutionsQuery:
        """Create the query to be executed on the sdp repository.

        :param instr_comb:
        :return: OrderExecutionsQuery. Query to execute.
        """
        q = OrderExecutionsQuery()

        q: OrderExecutionsQuery = self._base_query(
            query=q,
            instr_comb=instr_comb,
            add_default_conditions=add_default_conditions,
        )

        q.add_condition(
            mode="filter",
            conditions=[
                {
                    "terms": {
                        OrderField.EXC_DTL_ORD_STATUS: [
                            OrderStatus.FILL,
                            OrderStatus.PARF,
                        ]
                    }
                },
            ],
        )

        return q

    def _get_order_query(
        self,
        instr_comb: Optional[List[str]] = None,
        add_default_conditions: bool = False,
    ) -> OrderQuery:
        """Create the query to be executed on the sdp repository.

        :param instr_comb:
        :return: OrderQuery. Query to execute.
        """
        return self._base_query(
            query=OrderQuery(),
            instr_comb=instr_comb,
            add_default_conditions=add_default_conditions,
        )

    def _execute_query(
        self,
        order_state_query: bool,
        instr_comb: Optional[List[str]],
    ) -> pd.DataFrame:
        """
        :param order_state_query: bool. True if is to create an orderState Query
        :param instr_comb: instrument combinations

        :return: pd.DataFrame
        """

        query: Union[OrderQuery, OrderExecutionsQuery] = (
            self._get_order_executions_query(instr_comb=instr_comb, add_default_conditions=True)
            if order_state_query
            else self._get_order_query(instr_comb=instr_comb, add_default_conditions=True)
        )

        specific_include_fields = self.query_specific_include_fields(order_state_query)

        query.includes(specific_include_fields)

        result: pd.DataFrame = self._sdp_repository.search_after_query(query=query)

        # drop records with no __instrument_code__
        result = result[~result[NewColumns.INSTRUMENT_CODE].isnull()]

        pre_keys = result[OrderField.META_KEY].tolist()

        if self._th_normalise_behaviour == NormaliseBehaviour.ASSET_CLASS:
            if OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN not in result.columns:
                self._logger.warning(
                    f"The required column {OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN} doesn't "
                    f"exists in the data. Dropping all records for instruments: {instr_comb}"
                )

                result = pd.DataFrame()
            else:
                result = result[result[OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN].notnull()]

        post_keys = result[OrderField.META_KEY].tolist()
        dropped = set(pre_keys) - set(post_keys)

        if len(dropped) != 0:
            start_audit, method_id = self.get_start_time_and_unique_id()
            end, method_id = self.get_start_time_and_unique_id()
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=list(dropped),
                    reason=f"Orders don't have required value of {OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN}.",
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start_audit,
                    end=end,
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=len(pre_keys),
                    number_of_resulting_orders=len(post_keys),
                    step_name="Initial Data Retrieval",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FETCH_DATA,
                )
            )
            self.GLOBAL_START_TIME = end

        return result

    def process_result(
        self, date_col: str, data: pd.DataFrame, event_type: EventType
    ) -> pd.DataFrame:
        """Process data to remove NaN in certain cols and filter by business
        days.

        :param date_col: Str, column name to remove non business days
        :param data: pd.DataFrame. Algorithm data
        :param event_type: EventType. Enum with orders type type
        :return: pd.DataFrame. Data processed
        """
        if date_col == OrderField.TS_ORD_SUBMITTED:
            date_col = (
                OrderField.TS_ORD_SUBMITTED
                if OrderField.TS_ORD_SUBMITTED in data.columns
                else OrderField.TS_ORD_UPDATED
            )

        if date_col not in data:
            self._logger.info(
                f"Date column {date_col}, not present in the dataset, so we can't filtered the non business days"
            )
            return pd.DataFrame()

        data: pd.DataFrame = filter_non_business_days(date_col=date_col, data=data)

        if data.empty:
            self._logger.debug("No data after filtering the Non business days.")
            return pd.DataFrame()

        drop_nan_cols = [
            *DROP_NAN_COLUMNS,
            GENERAL_GROUPING_MAP.get(self._th_general_evaluation_type),
        ]
        if event_type == EventType.NEWOS:
            drop_nan_cols.extend(
                [
                    OrderField.PC_FD_INIT_QTY,
                    OrderField.TS_ORD_SUBMITTED,
                ]
            )

        if event_type == EventType.EXECUTIONS:
            drop_nan_cols.extend(
                [
                    OrderField.META_PARENT,
                    OrderField.PC_FD_TRD_QTY,
                    OrderField.TS_TRADING_DATE_TIME,
                ]
            )

        drop_nan_cols = [col for col in drop_nan_cols if col in data.columns]

        if OrderField.META_PARENT in data.columns:
            parent_ids = data[OrderField.META_PARENT].dropna().tolist()
            if parent_ids:
                order_meta_keys = self.get_order_meta_key(parent_ids=parent_ids)
                data = convert_parent_id_to_parent_key(
                    data=data,
                    parent_meta_keys=order_meta_keys,
                    new_def_column=DFColumns.META_PARENT_META_KEY,
                )
        process_data: pd.DataFrame = data.dropna(subset=drop_nan_cols)

        if process_data.empty:
            self._logger.info(
                f"After removing NaN from the columns {drop_nan_cols} the dataset is empty."
            )
            return pd.DataFrame()

        process_data: pd.DataFrame = process_numeric_columns(data=process_data)
        return process_data

    def get_historical_data(
        self,
        start_date: date,
        end_date: date,
        date_field: str,
        event_type: EventType,
        instr_comb: List[str] = None,
        grouping_field_filters: dict = None,
    ) -> pd.DataFrame:
        """Get historical data to do further analysis based on the evaluation
        types selected.

        :param start_date: date. Start date to filter the data to search by
        :param end_date: date. End date to filter the data to search by
        :param date_field: Str. Date column to apply the date range
        :param event_type: Str. Type of order that the user want
        :param instr_comb: list of Str. Instrument to filter (if applied)
        :param grouping_field_filters: dict. dict with field to filter by
        :return:
        """

        if event_type == EventType.NEWOS:
            query = self._get_order_query(instr_comb=instr_comb, add_default_conditions=False)

        elif event_type == EventType.EXECUTIONS:
            query = self._get_order_executions_query(
                instr_comb=instr_comb, add_default_conditions=False
            )

        else:
            raise ValueError(f"`{event_type}` not valid for this strategy: {event_type}")

        query.add_iris_filters(self.get_iris_filters_without_date_range())

        ts_range = Range(
            **{
                date_field: {
                    "gte": start_date.isoformat(),
                    "lte": end_date.isoformat(),
                }
            }
        )

        query.add_condition(mode="filter", conditions=[ts_range])

        if grouping_field_filters:
            grouping_conditions = []
            instrument_conditions = None
            for g_field_dict in grouping_field_filters:
                if g_field_dict.get(NewColumns.INSTRUMENT_CODE):
                    instr_comb = g_field_dict.get(NewColumns.INSTRUMENT_CODE)
                    if instr_comb:
                        instrument_conditions = [
                            Terms(**{field: instr_comb})
                            for field in OrderField.get_instrument_fields()
                        ]
                        query.instrument_id(instr_comb)

                else:
                    grouping_conditions.append(Terms(**g_field_dict))

            if grouping_conditions:
                query.add_condition(mode="should", conditions=grouping_conditions)

            if instrument_conditions is not None:
                query.add_condition(mode="should", conditions=instrument_conditions)

        specific_include_fields = self.query_specific_include_fields(
            event_type == EventType.EXECUTIONS
        )

        query.includes(specific_include_fields)
        result: pd.DataFrame = self._sdp_repository.search_after_query(query=query)

        if result.empty:
            return result

        date_col = (
            OrderField.TS_ORD_SUBMITTED
            if OrderField.TS_ORD_SUBMITTED in result.columns
            else OrderField.TS_ORD_UPDATED
        )

        historical_data_processed: pd.DataFrame = self.process_result(
            date_col=date_col, data=result, event_type=event_type
        )

        return historical_data_processed

    def query_specific_include_fields(self, order_state_query):
        specific_include_fields = (
            [*INCLUDES_FIELDS, OrderField.PC_FD_TRD_QTY]
            if order_state_query
            else [*INCLUDES_FIELDS, OrderField.PC_FD_INIT_QTY]
        )
        if self._th_execution_notional_value_currency is not None:
            notional_field = OrderField.get_best_exc_trx_ecb_ref_rate_ccy(
                self._th_execution_notional_value_currency
            )
            specific_include_fields.append(notional_field)
        return specific_include_fields

    def get_iris_filters_without_date_range(self) -> Dict:
        """
        Based on the filters we get from the context remove the date range part of it
        :return:
        """
        if self._filters is None:
            iris_filters = {}
        else:
            iris_filters = self._filters.copy()

        filter_modes = ["must", "filter"]

        for mode in filter_modes:
            iris_filters = self.remove_range_terms_from_filters(
                query_filters=iris_filters, mode=mode
            )

        iris_filters = remove_date_range_from_iris_filters(iris_filters)

        return iris_filters

    def get_eval_exist_conditions(self) -> List:
        """
        Get exists conditions for evaluation type
        :return: list of exists conditions
        """
        eval_exist_conditions = []

        for eval_field in self.EVAL_REQUIRED_FIELDS:
            eval_exist_conditions.append(Exists(field=eval_field))

        return eval_exist_conditions

    def get_child_executions(self, parent_ids: List[str]) -> pd.DataFrame:
        """Get the child executions for a given set of NEWOs ids.

        :param parent_ids:
        :return:
        """
        query: OrderExecutionsQuery = OrderExecutionsQuery()

        query = self._add_filter_condition(
            ids=parent_ids,
            order_field=OrderField.META_PARENT,
            max_size=self.MAX_TERMS_SIZE,
            query_to_be_update=query,
        )
        query.order_status([OrderStatus.FILL, OrderStatus.PARF])
        query.includes([OrderField.META_KEY, OrderField.ORD_IDENT_ID_CODE])
        result: pd.DataFrame = self._sdp_repository.search_after_query(query=query)
        return result

    def get_parent_orders(self, child_ids: List[str]) -> pd.DataFrame:
        """Get the PARENT orders for a given set of Executions ids.

        :param child_ids:
        :return:
        """
        query: OrderExecutionsQuery = OrderExecutionsQuery()

        query = self._add_filter_condition(
            ids=child_ids,
            order_field=OrderField.META_KEY,
            max_size=self.MAX_TERMS_SIZE,
            query_to_be_update=query,
        )

        query.includes([OrderField.META_KEY, OrderField.META_PARENT, OrderField.ORD_IDENT_ID_CODE])

        query.order_status([OrderStatus.FILL, OrderStatus.PARF])

        order_executions_result: pd.DataFrame = self._sdp_repository.search_after_query(query=query)

        if OrderField.META_PARENT in order_executions_result.columns:
            meta_parent_ids = order_executions_result.loc[:, OrderField.META_PARENT].tolist()

            order_query: OrderQuery = OrderQuery()

            order_query = self._add_filter_condition(
                ids=meta_parent_ids,
                order_field=OrderField.META_ID,
                max_size=self.MAX_TERMS_SIZE,
                query_to_be_update=order_query,
            )
            order_query.includes([OrderField.META_KEY, OrderField.ORD_IDENT_ID_CODE])

            order_result: pd.DataFrame = self._sdp_repository.search_after_query(query=order_query)

            return order_result

        order_executions_result[OrderField.META_PARENT] = pd.NA
        order_executions_result[OrderField.ORD_IDENT_ID_CODE] = pd.NA

        return order_executions_result

    @staticmethod
    def _add_filter_condition(
        ids: List[str], order_field: OrderField, max_size: int, query_to_be_update
    ):
        """Add filter condition to query.

        :param ids:
        :param order_field:
        :param max_size:
        :param query_to_be_update:
        :return:
        """
        query: Union[OrderQuery, OrderExecutionsQuery] = query_to_be_update

        if len(ids) > max_size:
            ids_chunks = [ids[ix : ix + max_size] for ix in range(0, len(ids), max_size)]
            shoulds = [{"terms": {order_field: chunk}} for chunk in ids_chunks]
            query.add_condition(mode="should", conditions=shoulds)
        else:
            query.add_condition(
                mode="filter",
                conditions=[{"terms": {order_field: ids}}],
            )

        return query

    def get_market_data(
        self,
        rics_inst_ids: List[Tuple[str, str]],
        time_series_col: str,
        look_back_days: int,
        evaluation_type: MarketDataEvaluationType,
    ) -> pd.DataFrame:
        """Based on the instrument unique identifiers retrieved and on the
        market data eval type fetches the market data with/ or without the
        average daily traded volume calculated for the window set in the
        threshold.

        :param rics_inst_ids: List of tuples: (RIC, INSTRUMENT_ID).
        :param time_series_col: str. Datetime column name to set as index. Default is None
        :param look_back_days: int. Number of days to look back
        :param evaluation_type: MarketDataEvaluationType. market data evaluation type

        :return: pd.DataFrame. Market Data
        """

        results = []

        for ric, inst_id in rics_inst_ids:
            if evaluation_type == MarketDataEvaluationType.DAY_TRADED_VOLUME:
                data: pd.DataFrame = self._market_data_client.get_market_data_stats(
                    instrument_ric=ric,
                    start_date=self.look_back_period_ts,
                    end_date=self.market_data_end_date,
                )

                if (
                    data.empty
                    or TradeStatsColumns.TRADE_VOLUME not in data.columns
                    or (
                        TradeStatsColumns.TRADE_VOLUME in data.columns
                        and data[TradeStatsColumns.TRADE_VOLUME].isna().all()
                    )
                ):
                    # [ENG-10440] if the data is empty will return an empty dataframe and if all rows for the trade volume
                    # [ENG-10440] are NULL we should also return an empty dataframe and don't proceed with the calculation
                    trade_vol_nan = False
                    trade_vol_present = TradeStatsColumns.TRADE_VOLUME in data.columns
                    if TradeStatsColumns.TRADE_VOLUME in data.columns:
                        trade_vol_nan = data[TradeStatsColumns.TRADE_VOLUME].isna().all()

                    self._logger.warning(
                        f"Either the EOD data is empty or the trading volume is NaN in all days."
                        f"Is trade volume present in market data: {trade_vol_present}"
                        f"Is trade volume NaN in all days? {trade_vol_nan}."
                        f"Is market data empty? {data.empty}"
                    )
                    data = pd.DataFrame()

                if not data.empty:
                    data[StatsColumns.DATE] = pd.to_datetime(
                        data[StatsColumns.DATE], format="mixed"
                    )

            else:
                data = self._market_data_client.get_avg_daily_trading_volume(
                    instrument_ric=ric,
                    look_back_period=f"{look_back_days}d",
                    adtv_column=DFColumns.MARKET_AVG_DAILY_VOLUME,
                    time_series_col=time_series_col,
                    start_date=self.look_back_period_ts,
                    end_date=self.market_data_end_date,
                )

            if data.empty:
                self._logger.info(
                    f"No market data data found for the instrument RIC {ric}. "
                    f"So moving to the next instrument."
                )
                continue

            data[OrderField.INST_EXT_UNIQUE_IDENT] = inst_id

            results.append(data)

        if not results:
            self._logger.info(f"No market data found for all instruments {rics_inst_ids}.")
            return pd.DataFrame()

        result_mkt_data: pd.DataFrame = pd.concat(results).drop_duplicates()

        return result_mkt_data

    def get_tick_market_data(
        self,
        instrument_rics: List[str],
        dates: List[pd.Timestamp],
        event_type: RefinitivEventType,
    ) -> pd.DataFrame:
        """Based on the instrument unique identifiers retrieved, fetches the
        tick data for a given set of dates.

        :param instrument_rics: List of instrument ric.
        :param dates: list of dates. dates to get the tick data
        :param event_type: RefinitivEventType

        :return: pd.DataFrame. tick market Data
        """

        results = []

        for ric in instrument_rics:
            data: pd.DataFrame = self._market_data_client.get_tick_data(
                instrument_ric=ric,
                dates=dates,
                event_type=event_type,
            )

            if data.empty:
                self._logger.info(
                    f"No tick data found for the instrument RIC {ric}, for the dates {dates}. "
                    f"So moving to the next instrument."
                )
                continue

            results.append(data)

        if not results:
            self._logger.info(
                f"No tick data found for all instruments {instrument_rics}, for the dates {dates}."
            )
            return pd.DataFrame()

        tick_data: pd.DataFrame = pd.concat(results).drop_duplicates()

        return (
            tick_data.reset_index()
            .drop(columns=["index"])
            .sort_values(by=[QuoteTickColumns.DATE_TIME])
        )

    @staticmethod
    def remove_range_terms_from_filters(query_filters: Dict, mode: str = "must") -> Dict:
        """

        :param query_filters:
        :param mode:
        :return:
        """
        query_filters_dict = addict.Dict(query_filters)
        filters_to_check = query_filters_dict.get("bool", {}).get(mode)

        if filters_to_check is None:
            return query_filters_dict

        if isinstance(filters_to_check, dict):
            if "range" not in filters_to_check.keys():
                return query_filters_dict

            query_filters_dict.get("bool").pop(mode)

            return query_filters_dict

        if isinstance(filters_to_check, list):
            index_range = None

            for term in filters_to_check:
                if isinstance(term, dict) and "range" in term:
                    index_range = filters_to_check.index(term)

            if index_range is not None:
                query_filters_dict.get("bool").get(mode).pop(index_range)

        return query_filters_dict

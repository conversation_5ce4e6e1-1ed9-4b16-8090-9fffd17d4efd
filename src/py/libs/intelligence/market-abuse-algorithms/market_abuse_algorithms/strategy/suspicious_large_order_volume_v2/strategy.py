# ruff: noqa: E501
import datetime
import pandas as pd
from market_abuse_algorithms.data_source.repository.market_data.static import (
    TradeStatsColumns,
)
from market_abuse_algorithms.data_source.static.sdp.order import (
    <PERSON><PERSON>ell,
    NewColumns,
    OrderField,
    OrderStatus,
)
from market_abuse_algorithms.mar_audit.mar_audit import (
    DATETIME_FORMAT,
    AggregatedStepAudit,
    StepAudit,
)
from market_abuse_algorithms.strategy.base.errors import StrategyError
from market_abuse_algorithms.strategy.base.models import StrategyContext, TimeUnit
from market_abuse_algorithms.strategy.base.static import StrategyName
from market_abuse_algorithms.strategy.base.strategy import (
    AbstractStrategy,
    market_abuse_audit_object,
    singleton_audit_object,
)
from market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.models import (
    Thresholds,
    TimeWindow,
)
from market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.query import (
    Queries,
)
from market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.scenario import (
    <PERSON><PERSON><PERSON>,
)
from market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.static import (
    GENERAL_GROUPING_MAP,
    CategoryEvaluationType,
    DayAndOrderEvaluationType,
    DFColumns,
    EventType,
    MarketDataEvaluationType,
    NormaliseBehaviour,
    QuantityEvaluationType,
    ThresholdsNames,
)
from market_abuse_algorithms.utils.data import get_earliest_timestamp
from pandas.tseries.offsets import BDay
from se_elastic_schema.static.mar_audit import DropReason, StepType
from se_market_data_utils.schema.parquet import QuoteTickColumns, TradeTickColumns
from se_market_data_utils.schema.refinitiv import RefinitivEventType, RefinitivExtractColumns
from typing import Dict, List, NoReturn, Optional, Tuple, Union


class Strategy(AbstractStrategy):
    """
    Suspicious Large Order Volume - V2
    https://steeleye.atlassian.net/wiki/spaces/PRODUCT/pages/1351122959/Suspicious+Large+Order+Execution+Volume+SLOEV

    Jira tickets for the logic:
        - https://steeleye.atlassian.net/browse/MA-32

    Jira tickets for the front-end:
        - https://steeleye.atlassian.net/browse/PR-2157
    """

    def __init__(self, context: StrategyContext):
        super().__init__(
            context=context,
            strategy_name=StrategyName.SUSPICIOUS_LARGE_ORDER_VOLUME_V2,
            thresholds_class=Thresholds,
            queries_class=Queries,
        )

        self._th_category_evaluation_type = self.context.thresholds.dict().get(
            ThresholdsNames.CATEGORY_EVALUATION_TYPE
        )
        self._th_day_and_order_evaluation_type = self.context.thresholds.dict().get(
            ThresholdsNames.DAY_AND_ORDER_EVALUATION_TYPE
        )

        self._th_execution_notional_value_currency = self.context.thresholds.dict().get(
            ThresholdsNames.EXECUTION_NOTIONAL_VALUE_CURRENCY
        )

        self._th_general_evaluation_type = self.context.thresholds.dict().get(
            ThresholdsNames.GENERAL_EVALUATION_TYPE
        )

        self._th_look_back_period = (
            self.context.thresholds.dict().get(ThresholdsNames.LOOK_BACK_PERIOD)
            if self.context.thresholds.dict().get(ThresholdsNames.LOOK_BACK_PERIOD)
            else TimeWindow(value=20, unit=TimeUnit.DAYS)
        )

        self._th_market_data_evaluation_type = self.context.thresholds.dict().get(
            ThresholdsNames.MARKET_DATA_EVALUATION_TYPE
        )
        self._th_minimum_quantity = self.context.thresholds.dict().get(
            ThresholdsNames.MINIMUM_QUANTITY
        )
        self._th_min_number_of_days_order_flow = self.context.thresholds.dict().get(
            ThresholdsNames.MIN_NUMBER_DAYS_ORDER_FLOW
        )
        self._th_normalise_behaviour = self.context.thresholds.dict().get(
            ThresholdsNames.NORMALISE_BEHAVIOUR
        )
        self._th_percentage_adv = self.context.thresholds.dict().get(ThresholdsNames.PERCENTAGE_ADV)
        self._th_quantity_evaluation_type = self.context.thresholds.dict().get(
            ThresholdsNames.QUANTITY_EVALUATION_TYPE
        )

        self._th_market_price_impact = self.context.thresholds.dict().get(
            ThresholdsNames.MARKET_PRICE_IMPACT
        )

        self._th_event_date = None
        self._th_quantity = None
        self._th_events = None

        self.GLOBAL_START_TIME = datetime.datetime.now(datetime.timezone.utc).strftime(
            DATETIME_FORMAT
        )

        self._variables_configuration()

    def _apply_strategy(self):
        """Apply algorithm strategy based on the cases retrieved when executing
        query."""

        for data in self.queries.cases_to_analyse(event_type=self._th_events):
            market_abuse_audit_object.records_analysed += len(data)
            self._apply_strategy_universal(data=data)

    def _apply_strategy_universal(self, data: pd.DataFrame) -> NoReturn:
        """Execute universal strategy batch.

        :param data: pd.DataFrame. Data retrieved from sdp repository
        """
        group_field: Union[List[OrderField], OrderField] = self._get_universal_grouping_field()

        if group_field is None:
            end, method_id = self.get_start_time_and_unique_id()
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=data.get(OrderField.META_KEY, pd.Series()).tolist(),
                    reason=f"Required grouping fields missing for threshold {ThresholdsNames.DAY_AND_ORDER_EVALUATION_TYPE} as {self._th_day_and_order_evaluation_type}.",
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.GLOBAL_START_TIME,
                    end=end,
                    number_of_dropped_orders=len(data),
                    number_of_input_orders=len(data),
                    number_of_resulting_orders=0,
                    step_name="Creation of groupings per 'Evaluate By Order, Parent Order, or By Day' Threshold",
                    step_type=StepType.GROUPING,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                )
            )
            self.GLOBAL_START_TIME = end
            self._logger.info("No grouping fields")
            return

        group_fields_to_check: List = [group_field] if isinstance(group_field, str) else group_field

        check_grouping_fields: List = [
            (True, g_field) if g_field in data.columns else (False, g_field)
            for g_field in group_fields_to_check
        ]
        fields_not_in_dataframe = [field[1] for field in check_grouping_fields if field[0] is False]

        if fields_not_in_dataframe and list(filter(None, fields_not_in_dataframe)):
            end, method_id = self.get_start_time_and_unique_id()
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=data.get(OrderField.META_KEY, pd.Series()).tolist(),
                    reason=f"The required fields {fields_not_in_dataframe} are missing from the data.",
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.GLOBAL_START_TIME,
                    end=end,
                    number_of_dropped_orders=len(data),
                    number_of_input_orders=len(data),
                    number_of_resulting_orders=0,
                    step_name="Creation of groupings per 'Evaluate By Order, Parent Order, or By Day' Threshold",
                    step_type=StepType.GROUPING,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                )
            )
            self.GLOBAL_START_TIME = end
            self._logger.warning(
                "Not all grouping fields are present in the dataframe. "
                f"These are the fields {fields_not_in_dataframe}"
            )
            return

        end, method_id = self.get_start_time_and_unique_id()
        singleton_audit_object.write_audit_data_to_local_files(
            AggregatedStepAudit(
                aggregated_step_id=method_id,
                start=self.GLOBAL_START_TIME,
                end=end,
                number_of_dropped_orders=0,
                number_of_input_orders=len(data),
                number_of_resulting_orders=len(data),
                step_name="Creation of groupings per 'Evaluate By Order, Parent Order, or By Day' Threshold",
                step_type=StepType.GROUPING,
            )
        )
        self.GLOBAL_START_TIME = end

        if isinstance(group_field, List) and self._th_event_date in group_field:
            group_field, data = self.check_date_time_grouping(
                grouping_fields=group_field, tenant_dataset=data
            )

        if self._th_event_date not in data.columns:
            end, method_id = self.get_start_time_and_unique_id()
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=data.get(OrderField.META_KEY, pd.Series()).tolist(),
                    reason=f"The required column {self._th_event_date} is missing from the data.",
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.GLOBAL_START_TIME,
                    end=end,
                    number_of_dropped_orders=len(data),
                    number_of_input_orders=len(data),
                    number_of_resulting_orders=0,
                    step_name="Creation of groupings per 'Evaluate By Order, Parent Order, or By Day' Threshold",
                    step_type=StepType.GROUPING,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                )
            )
            self.GLOBAL_START_TIME = end
            self._logger.warning(f"Required Col {self._th_event_date} is missing from the data.. ")
            return

        groupings_no = len(data.groupby(group_field))
        groupings_dropped = 0
        for field, group in data.groupby(group_field):
            if self._th_quantity not in group.columns:
                groupings_dropped += 1
                end, method_id = self.get_start_time_and_unique_id()
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=group.get(OrderField.META_KEY, pd.Series()).tolist(),
                        reason=f"The column {self._th_quantity} is not present and it's mandatory",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.GLOBAL_START_TIME,
                        end=end,
                        number_of_dropped_orders=len(group),
                        number_of_input_orders=len(group),
                        number_of_resulting_orders=0,
                        step_name=f"Missing column {self._th_quantity}.",
                        step_type=StepType.GROUPING,
                        drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                        groupings=groupings_no,
                        groupings_dropped=groupings_dropped,
                    )
                )
                self.GLOBAL_START_TIME = end
                continue

            group_quantity_sum: float = group[self._th_quantity].sum()

            end, method_id = self.get_start_time_and_unique_id()

            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.GLOBAL_START_TIME,
                    end=end,
                    number_of_dropped_orders=0,
                    number_of_input_orders=len(group),
                    number_of_resulting_orders=len(group),
                    step_name="Executing Algo for Grouping",
                    step_type=StepType.GROUPING,
                    groupings=groupings_no,
                )
            )
            self.GLOBAL_START_TIME = end

            if self._th_minimum_quantity and not self._check_minimum_quantity_threshold(
                group_quantity_sum=group_quantity_sum
            ):
                groupings_dropped += 1
                end, method_id = self.get_start_time_and_unique_id()
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=group.get(OrderField.META_KEY, pd.Series()).tolist(),
                        reason=f"Group quantity sum {group_quantity_sum} is less than {self._th_minimum_quantity} "
                        f"for threshold {ThresholdsNames.MINIMUM_QUANTITY}.",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.GLOBAL_START_TIME,
                        end=end,
                        number_of_dropped_orders=len(group),
                        number_of_input_orders=len(group),
                        number_of_resulting_orders=0,
                        step_name="Application of 'Minimum Quantity' Threshold",
                        step_type=StepType.GROUPING,
                        drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                        groupings=groupings_no,
                        groupings_dropped=groupings_dropped,
                    )
                )
                self.GLOBAL_START_TIME = end
                continue

            end, method_id = self.get_start_time_and_unique_id()

            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.GLOBAL_START_TIME,
                    end=end,
                    number_of_dropped_orders=0,
                    number_of_input_orders=len(group),
                    number_of_resulting_orders=len(group),
                    step_name="Application of 'Minimum Quantity' Threshold",
                    step_type=StepType.GROUPING,
                    groupings=groupings_no,
                )
            )
            self.GLOBAL_START_TIME = end

            self._run_algo(data=group, quantity_sum=group_quantity_sum)

    def _run_algo(self, data: pd.DataFrame, quantity_sum: float) -> NoReturn:
        """Global method to execute the algorithm and then create algorithm
        scenarios.

        :param data: pd.DataFrame. Data retrieved from sdp repository
        """

        results: pd.DataFrame = pd.DataFrame()

        if self._th_category_evaluation_type == CategoryEvaluationType.MARKET:
            market_data: pd.DataFrame = self._get_market_data(data=data)

            results: pd.DataFrame = self._algo_market_data_logic(
                quantity_sum=quantity_sum, market_data=market_data
            )

        elif self._th_category_evaluation_type == CategoryEvaluationType.INTERNAL_FLOW:
            results: pd.DataFrame = self._algo_internal_flow_logic(
                data=data, quantity_sum=quantity_sum
            )

        if results.empty:
            end, method_id = self.get_start_time_and_unique_id()

            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.GLOBAL_START_TIME,
                    end=end,
                    number_of_dropped_orders=len(data),
                    number_of_input_orders=len(data),
                    number_of_resulting_orders=0,
                    step_name="Creation of Alerts",
                    step_type=StepType.ALERT_CREATION,
                    drop_reason=DropReason.RECORDS_DROPPED_CREATE_ALERTS,
                )
            )
            self.GLOBAL_START_TIME = end
            self._logger.debug(
                f"No results were found after applying {self._th_category_evaluation_type} strategy."
            )
            return

        end, method_id = self.get_start_time_and_unique_id()

        singleton_audit_object.write_audit_data_to_local_files(
            AggregatedStepAudit(
                aggregated_step_id=method_id,
                start=self.GLOBAL_START_TIME,
                end=end,
                number_of_dropped_orders=len(data) - len(results),
                number_of_input_orders=len(data),
                number_of_resulting_orders=len(results),
                step_name="Creation of Alerts",
                step_type=StepType.ALERT_CREATION,
            )
        )
        self.GLOBAL_START_TIME = end

        self._create_scenarios(data=results)

    def _algo_market_data_logic(
        self, quantity_sum: float, market_data: pd.DataFrame
    ) -> pd.DataFrame:
        """Execute algorithm for the market data part.

        :param quantity_sum: float.
        :param market_data: pd.DataFrame. Market Data retrieved
        :return: pd.DataFrame. Data resulted after applying the market data logic
        """
        comparison_volume_col = None

        if market_data.empty:
            self._logger.info("No Market Data available")
            return pd.DataFrame()

        if self._th_market_data_evaluation_type == MarketDataEvaluationType.DAY_TRADED_VOLUME:
            comparison_volume_col = TradeStatsColumns.TRADE_VOLUME

        if (
            self._th_market_data_evaluation_type
            == MarketDataEvaluationType.AVERAGE_DAILY_TRADED_VOLUME
        ):
            comparison_volume_col = DFColumns.MARKET_AVG_DAILY_VOLUME

        if self._th_market_data_evaluation_type == MarketDataEvaluationType.INTRA_DAY_TRADED_VOLUME:
            comparison_volume_col = DFColumns.MARKET_INTRA_DAY_VOLUME

        if (
            comparison_volume_col not in market_data.columns
            or market_data[comparison_volume_col].isnull().all()
            or market_data[comparison_volume_col].sum() == 0.0
        ):
            end, method_id = self.get_start_time_and_unique_id()
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=market_data.get(OrderField.META_KEY, pd.Series()).tolist(),
                    reason=f"The required Comparison Volume column was not selected for {self._th_market_data_evaluation_type}.",
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.GLOBAL_START_TIME,
                    end=end,
                    number_of_dropped_orders=len(market_data),
                    number_of_input_orders=len(market_data),
                    number_of_resulting_orders=0,
                    step_name="Calculating 'ADTV' from Market Data",
                    step_type=StepType.MARKET_DATA,
                    drop_reason=DropReason.RECORDS_DROPPED_MARKET_ADV,
                )
            )
            self.GLOBAL_START_TIME = end
            self._logger.info("No Comparison Volume column selected.")
            return pd.DataFrame()

        market_data = market_data[
            (market_data[comparison_volume_col] > 0) & ~market_data[comparison_volume_col].isnull()
        ]

        end, method_id = self.get_start_time_and_unique_id()

        singleton_audit_object.write_audit_data_to_local_files(
            AggregatedStepAudit(
                aggregated_step_id=method_id,
                start=self.GLOBAL_START_TIME,
                end=end,
                number_of_dropped_orders=0,
                number_of_input_orders=len(market_data),
                number_of_resulting_orders=len(market_data),
                step_name="Calculating 'ADTV' from Market Data",
                step_type=StepType.MARKET_DATA,
            )
        )
        self.GLOBAL_START_TIME = end

        market_data[DFColumns.ADV] = market_data[comparison_volume_col]

        market_data[DFColumns.ADV_PERCENTAGE] = quantity_sum / market_data[DFColumns.ADV]

        prev_orders: List = market_data.get(OrderField.META_KEY, pd.Series()).tolist()

        market_data = market_data.loc[
            market_data[DFColumns.ADV_PERCENTAGE] > self._th_percentage_adv
        ]

        end, method_id = self.get_start_time_and_unique_id()
        if len(market_data) < len(prev_orders):
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=list(
                        set(prev_orders)
                        - set(market_data.get(OrderField.META_KEY, pd.Series()).tolist())
                    ),
                    reason="There is no market data because the ADV percentage is lower than the "
                    f"ADV percentage threshold {self._th_percentage_adv}.",
                )
            )
        singleton_audit_object.write_audit_data_to_local_files(
            AggregatedStepAudit(
                aggregated_step_id=method_id,
                start=self.GLOBAL_START_TIME,
                end=end,
                number_of_dropped_orders=len(prev_orders) - len(market_data),
                number_of_input_orders=len(prev_orders),
                number_of_resulting_orders=len(market_data),
                step_name="Application of 'vs. Volume %' Threshold",
                step_type=StepType.THRESHOLD_CALCULATION,
                drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
            )
        )
        self.GLOBAL_START_TIME = end

        if market_data.empty:
            self._logger.info(
                "After applying the ADV percentage threshold, where the calculated ADV percentage should be lower "
                "then the threshold defined by the user, there is no market data."
            )
            return pd.DataFrame()

        market_data: pd.DataFrame = self.add_market_price_variables(data_to_check=market_data)

        if self._th_market_price_impact:
            if market_data.empty:
                end, method_id = self.get_start_time_and_unique_id()

                self._logger.info("Missing columns from market price in market data dataframe")

                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=market_data.get(
                            OrderField.META_KEY, pd.Series()
                        ).tolist(),
                        reason=f"Records dropped because of missing columns {DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE}"
                        f"or {DFColumns.MARKET_PRICE_IMPROVEMENT}"
                        f"as {self._th_market_price_impact}.",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.GLOBAL_START_TIME,
                        end=end,
                        number_of_dropped_orders=len(
                            market_data.get(OrderField.META_KEY, pd.Series()).tolist()
                        ),
                        number_of_input_orders=len(
                            market_data.get(OrderField.META_KEY, pd.Series()).tolist()
                        ),
                        number_of_resulting_orders=0,
                        step_name="Application of 'Market Price Impact' Threshold",
                        step_type=StepType.THRESHOLD_CALCULATION,
                        drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                    )
                )
                self.GLOBAL_START_TIME = end
                return pd.DataFrame()

            market_price_impact_mask = (
                market_data[DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE_ABS]
                >= self._th_market_price_impact
            ) & (market_data[DFColumns.MARKET_PRICE_IMPROVEMENT])

            dropped = market_data.get(OrderField.META_KEY, pd.Series()).tolist()
            original_len = len(market_data)

            market_data: pd.DataFrame = market_data.loc[market_price_impact_mask]

            dropped = set(dropped) - set(market_data.get(OrderField.META_KEY, pd.Series()).tolist())
            if dropped:
                end, method_id = self.get_start_time_and_unique_id()
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=list(dropped),
                        reason="Records dropped after filtering by the Market Price Impact threshold "
                        f"as {self._th_market_price_impact}.",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.GLOBAL_START_TIME,
                        end=end,
                        number_of_dropped_orders=len(dropped),
                        number_of_input_orders=original_len,
                        number_of_resulting_orders=original_len - len(dropped),
                        step_name="Application of 'Market Price Impact' Threshold",
                        step_type=StepType.THRESHOLD_CALCULATION,
                        drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                    )
                )
                self.GLOBAL_START_TIME = end

            end, method_id = self.get_start_time_and_unique_id()

            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.GLOBAL_START_TIME,
                    end=end,
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=original_len,
                    number_of_resulting_orders=original_len - len(dropped),
                    step_name="Application of 'Market Price Impact' Threshold",
                    step_type=StepType.THRESHOLD_CALCULATION,
                )
            )
            self.GLOBAL_START_TIME = end

            if market_data.empty:
                self._logger.info(
                    "After applying the Market Price impact threshold percentage, there is no data"
                )
                return pd.DataFrame()

        return market_data

    def _algo_internal_flow_logic(self, data: pd.DataFrame, quantity_sum: float) -> pd.DataFrame:
        """Execute algorithm for the internal flow part.

        :param data: pd.DataFrame. Data retrieved from sdp repository after some grouping
        :param quantity_sum: float.
        :return: pd.DataFrame. Data resulted after applying the internal flow logic
        """
        if GENERAL_GROUPING_MAP.get(self._th_general_evaluation_type) not in data.columns:
            end, method_id = self.get_start_time_and_unique_id()

            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=data.get(OrderField.META_KEY, pd.Series()).tolist(),
                    reason=f"The required column for {self._th_general_evaluation_type} does not exists in the data.",
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.GLOBAL_START_TIME,
                    end=end,
                    number_of_dropped_orders=len(data),
                    number_of_input_orders=len(data),
                    number_of_resulting_orders=0,
                    step_name="Getting grouping fields.",
                    step_type=StepType.GROUPING,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_MANDATORY_FIELDS,
                )
            )
            self.GLOBAL_START_TIME = end
            self._logger.info(
                f"The general evaluation type column for {self._th_general_evaluation_type} does not"
                f" exist in the dataframe."
            )
            return pd.DataFrame()

        grouping_fields: Union[OrderField, List[OrderField]] = self._get_grouping_fields()

        if not grouping_fields:
            end, method_id = self.get_start_time_and_unique_id()
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=data.get(OrderField.META_KEY, pd.Series()).tolist(),
                    reason="No possible grouping fields were found in the data",
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.GLOBAL_START_TIME,
                    end=end,
                    number_of_dropped_orders=len(data),
                    number_of_input_orders=len(data),
                    number_of_resulting_orders=0,
                    step_name="Getting grouping fields",
                    step_type=StepType.GROUPING,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_MANDATORY_FIELDS,
                )
            )
            self.GLOBAL_START_TIME = end
            self._logger.info("No grouping fields to be used")
            return pd.DataFrame()

        end, method_id = self.get_start_time_and_unique_id()

        singleton_audit_object.write_audit_data_to_local_files(
            AggregatedStepAudit(
                aggregated_step_id=method_id,
                start=self.GLOBAL_START_TIME,
                end=end,
                number_of_dropped_orders=0,
                number_of_input_orders=len(data),
                number_of_resulting_orders=len(data),
                step_name="Getting grouping fields",
                step_type=StepType.GROUPING,
            )
        )
        self.GLOBAL_START_TIME = end

        sorted_data: pd.DataFrame = self._sort_data_by_event_date(data=data)

        time_intervals: List[tuple] = self.construct_time_window(data=sorted_data)

        end, method_id = self.get_start_time_and_unique_id()

        singleton_audit_object.write_audit_data_to_local_files(
            AggregatedStepAudit(
                aggregated_step_id=method_id,
                start=self.GLOBAL_START_TIME,
                end=end,
                number_of_dropped_orders=0,
                number_of_input_orders=len(sorted_data),
                number_of_resulting_orders=len(sorted_data),
                step_name="Constructing Time Windows",
                step_type=StepType.GROUPING,
            )
        )
        self.GLOBAL_START_TIME = end

        alerts_to_be_checked: List[pd.DataFrame] = self._get_alerts_groups_to_checked(
            time_intervals=time_intervals,
            sorted_data=sorted_data,
            grouping_fields=grouping_fields,
        )

        alerts = self._get_alerts(
            data_sorted=sorted_data,
            grouping_fields=grouping_fields,
            quantity_sum=quantity_sum,
            alerts_to_be_checked=alerts_to_be_checked,
        )
        return alerts

    def _get_alerts(
        self,
        data_sorted: pd.DataFrame,
        grouping_fields: Union[List[OrderField], OrderField],
        quantity_sum: float,
        alerts_to_be_checked: List[pd.DataFrame],
    ) -> pd.DataFrame:
        """Evaluate the several groups and get the ones that should be alerted.

        :param data_sorted: pd.DataFrame
        :param grouping_fields: Union[List[OrderField], OrderField]
        :param quantity_sum: float.
        :param alerts_to_be_checked: List[pd.DataFrame]
        :return: pd.DataFrame. Alerts to be showed in the scenarios
        """
        grouping_no = len(data_sorted.groupby(grouping_fields))
        groupings_dropped = 0
        for group_field, group in data_sorted.groupby(grouping_fields):
            to_check_if_in_alert = (
                list(group_field) if isinstance(group_field, tuple) else [group_field]
            )

            group_original = group.copy()

            for alert_tbd in alerts_to_be_checked:
                if alert_tbd[grouping_fields].isin(to_check_if_in_alert).all().all():
                    group[DFColumns.ADV] = alert_tbd[DFColumns.AVERAGE_DAILY_TRADING_VOLUME].iloc[
                        -1
                    ]

                    group[DFColumns.ADV_PERCENTAGE] = quantity_sum / group[DFColumns.ADV]

                    group = group.loc[group[DFColumns.ADV_PERCENTAGE] > self._th_percentage_adv]

                    if group.empty:
                        groupings_dropped += 1
                        end, method_id = self.get_start_time_and_unique_id()
                        singleton_audit_object.write_audit_data_to_local_files(
                            StepAudit(
                                step_id=method_id,
                                list_of_order_ids=group_original.get(
                                    OrderField.META_KEY, pd.Series()
                                ).tolist(),
                                reason="There is no data to show because the ADV percentage is larger than the "
                                f"ADV percentage threshold {self._th_percentage_adv}.",
                            )
                        )
                        singleton_audit_object.write_audit_data_to_local_files(
                            AggregatedStepAudit(
                                aggregated_step_id=method_id,
                                start=self.GLOBAL_START_TIME,
                                end=end,
                                number_of_dropped_orders=len(group_original),
                                number_of_input_orders=len(group_original),
                                number_of_resulting_orders=0,
                                step_name=f"Applying threshold: {ThresholdsNames.PERCENTAGE_ADV}",
                                step_type=StepType.THRESHOLD_CALCULATION,
                                drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                                groupings=grouping_no,
                                groupings_dropped=groupings_dropped,
                            )
                        )
                        self.GLOBAL_START_TIME = end
                        self._logger.info(
                            "After applying the ADV percentage threshold, where the calculated ADV percentage "
                            "should be bigger then the threshold defined by the user, no data to be showed."
                        )
                        return pd.DataFrame()

                    end, method_id = self.get_start_time_and_unique_id()

                    singleton_audit_object.write_audit_data_to_local_files(
                        AggregatedStepAudit(
                            aggregated_step_id=method_id,
                            start=self.GLOBAL_START_TIME,
                            end=end,
                            number_of_dropped_orders=0,
                            number_of_input_orders=len(group_original),
                            number_of_resulting_orders=len(group_original),
                            step_name=f"Applying threshold: {ThresholdsNames.PERCENTAGE_ADV}.",
                            step_type=StepType.THRESHOLD_CALCULATION,
                            groupings=grouping_no,
                        )
                    )
                    self.GLOBAL_START_TIME = end

                    return group

        return pd.DataFrame()

    def _get_alerts_groups_to_checked(
        self,
        time_intervals: List[tuple],
        sorted_data: pd.DataFrame,
        grouping_fields: Union[OrderField, List[OrderField]],
    ) -> List[pd.DataFrame]:
        """Get the alerts to be checked after based in the quantity_sum &
        last_adtv_val.

        :param time_intervals: List[tuple]
        :param sorted_data: pd.DataFrame
        :param grouping_fields: Union[OrderField, List[OrderField]]
        :return: List[pd.DataFrame]
        """
        alerts_to_be_checked: List[pd.DataFrame] = []

        for time_window in time_intervals:
            start_date = pd.to_datetime(time_window[0], format="mixed").date()
            end_date = pd.to_datetime(time_window[1], format="mixed").date()

            fields_to_filter: List[dict] = self.get_fields_filter(
                data=sorted_data, historical_grouping_fields=grouping_fields
            )

            group_date: pd.DataFrame = self.queries.get_historical_data(
                start_date=start_date,
                end_date=end_date,
                date_field=self._th_event_date,
                event_type=self._th_events,
                grouping_field_filters=fields_to_filter,
            )

            if group_date.empty:
                self._logger.info(
                    f"There is no historical data between {start_date} and {end_date}."
                )
                continue

            general_grouping = GENERAL_GROUPING_MAP.get(self._th_general_evaluation_type)

            if general_grouping not in group_date.columns:
                self._logger.info(
                    f"The general grouping {general_grouping} used as general evaluation type "
                    f"does not exist in the dataframe columns."
                )
                continue

            groups_with_prior_activity: List[pd.DataFrame] = self._check_orders_count_and_filter(
                initial_data=sorted_data,
                grouping_fields=grouping_fields,
                data=group_date,
            )

            for group in groups_with_prior_activity:
                if group.empty:
                    self._logger.info("No data with prior activity.")
                    continue

                if self._th_quantity not in group.columns:
                    # Drop the group if it does not have the column for _th_quantity
                    self._logger.info(f"{self._th_quantity} not in group columns.")
                    continue

                group_with_activity: pd.DataFrame = self.drop_rows_with_zero_activity(
                    data=group, activity_column_to_check=self._th_quantity
                )

                group_with_activity: pd.DataFrame = self._sort_data_by_event_date(
                    data=group_with_activity
                )

                group_with_activity: pd.DataFrame = group_with_activity.reset_index().drop(
                    columns=["index"]
                )

                data_to_be_checked: pd.DataFrame = self._generate_alerts_to_be_checked(
                    data=group_with_activity
                )

                if data_to_be_checked.empty:
                    self._logger.info("No data after applying the ADTV threshold.")
                    continue

                alerts_to_be_checked.append(data_to_be_checked)

        return alerts_to_be_checked

    def _get_grouping_fields(self) -> Union[OrderField, List[OrderField]]:
        """
        Get grouping fields
        :return: Union[OrderField, List[OrderField]], if normalised behaviour is None, only return the OrderField from
        GENERAL_GROUPING_MAP
        """
        grouping_fields = GENERAL_GROUPING_MAP.get(self._th_general_evaluation_type)

        if self._get_normalise_grouping():
            grouping_fields = [grouping_fields, self._get_normalise_grouping()]

        return grouping_fields

    def _generate_alerts_to_be_checked(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate data to be checked after.

        :param data: pd.DataFrame - Data to be analyzed
        :return: pd.DataFrame: data with alerts to be
        """

        data[DFColumns.ADV_DAY] = data[self._th_event_date].apply(lambda x: x.date())

        adtv_days_order_flow: float = data[self._th_event_date].dt.date.nunique()

        if (
            self._th_min_number_of_days_order_flow is not None
            and adtv_days_order_flow < self._th_min_number_of_days_order_flow.value
        ):
            self._logger.info(
                "Dropping records that don't meet the minimum number days of order flow threshold."
            )
            return pd.DataFrame()

        data[DFColumns.AVERAGE_DAILY_TRADING_VOLUME] = pd.NA

        for day, group in data.groupby(DFColumns.ADV_DAY):
            data_until_day: pd.DataFrame = data[data[DFColumns.ADV_DAY] < day]

            previous_days_activity = self.get_count_of_previous_days_with_activity(
                data=data,
                day_to_check_data_before=day,
                max_look_back_period=self._th_look_back_period.value,
            )

            if previous_days_activity == 0 or data_until_day.empty:
                self._logger.info(
                    f"There aren't any previous days with activity before the day - {day} - that we are running now."
                )
                continue

            if len(data_until_day[DFColumns.ADV_DAY].unique().tolist()) != previous_days_activity:
                self._logger.info(
                    f"There is a mismatch between the number of previous days "
                    f"{len(data_until_day[DFColumns.ADV_DAY].unique().tolist())} with "
                    f"activity and the number of different days before the day - {day} - that we "
                    f"are running now."
                )
                continue

            traded_volume_until_day = data_until_day[self._th_quantity].sum()

            internal_adtv_day = traded_volume_until_day / previous_days_activity

            data.loc[data[DFColumns.ADV_DAY] == day, DFColumns.AVERAGE_DAILY_TRADING_VOLUME] = (
                internal_adtv_day
            )

        return data[data.loc[:, DFColumns.AVERAGE_DAILY_TRADING_VOLUME] > 0]

    def _get_universal_grouping_field(self) -> Union[List[OrderField], OrderField]:
        """
        Define the first groups that needs to be done based on the day/order evaluation type
        :return: Union[List[OrderField], OrderField]
        """
        grouping_fields = None

        if self._th_day_and_order_evaluation_type == DayAndOrderEvaluationType.ORDER:
            grouping_fields = OrderField.ORD_IDENT_ID_CODE

        elif self._th_day_and_order_evaluation_type == DayAndOrderEvaluationType.PARENT_ORDER:
            grouping_fields = OrderField.META_PARENT

        elif self._th_day_and_order_evaluation_type == DayAndOrderEvaluationType.DAY:
            grouping_fields = [self._th_event_date]

            if GENERAL_GROUPING_MAP.get(self._th_general_evaluation_type):
                grouping_fields.append(GENERAL_GROUPING_MAP.get(self._th_general_evaluation_type))

            if self._get_normalise_grouping():
                grouping_fields.append(self._get_normalise_grouping())

        return grouping_fields

    def _get_normalise_grouping(self) -> OrderField:
        """
        Check the normalise behaviour selected by the user and return the order field that will be used for further grouping
        :return: OrderField
        """
        if (
            self._th_normalise_behaviour
            and self._th_normalise_behaviour == NormaliseBehaviour.INSTRUMENT
        ) or self._th_category_evaluation_type == CategoryEvaluationType.MARKET:
            return NewColumns.INSTRUMENT_CODE

        if (
            self._th_normalise_behaviour
            and self._th_normalise_behaviour == NormaliseBehaviour.ASSET_CLASS
        ):
            return OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN

    def _get_market_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Get market data based on instrument ric.

        :param data: Data retrieved from sdp repository after some grouping
        :return:
        """

        instrument_rics = []

        earliest_timestamp: pd.DataFrame = get_earliest_timestamp(
            data=data,
            time_field=self._th_event_date,
            time_delta_column=DFColumns.TIMEDELTA,
        )

        start_date_intra_day = earliest_timestamp[self._th_event_date][0]
        end_date_intra_day = pd.to_datetime(
            start_date_intra_day, format="mixed"
        ) + datetime.timedelta(minutes=10)

        if DFColumns.RIC in data.columns:
            instrument_rics = data[DFColumns.RIC].dropna().unique().tolist()
            rics_inst_ids = list(
                data[[DFColumns.RIC, OrderField.INST_EXT_UNIQUE_IDENT]]
                .drop_duplicates()
                .itertuples(index=False, name=None)
            )

        if not instrument_rics:
            end, method_id = self.get_start_time_and_unique_id()

            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=data.get(OrderField.META_KEY, pd.Series()).tolist(),
                    reason="No RICs to search for market data.",
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.GLOBAL_START_TIME,
                    end=end,
                    number_of_dropped_orders=len(data),
                    number_of_input_orders=len(data),
                    number_of_resulting_orders=0,
                    step_name="Getting Market Data based on Instrument Rics.",
                    step_type=StepType.MARKET_DATA_RIC,
                    drop_reason=DropReason.RECORDS_DROPPED_MARKET_DATA,
                )
            )
            self.GLOBAL_START_TIME = end
            self._logger.info("No instrument rics.")
            return pd.DataFrame()

        if self._th_market_data_evaluation_type != MarketDataEvaluationType.INTRA_DAY_TRADED_VOLUME:
            market_data: pd.DataFrame = self.queries.get_market_data(
                rics_inst_ids=rics_inst_ids,
                time_series_col=DFColumns.DATE,
                look_back_days=self._th_look_back_period.value,
                evaluation_type=self._th_market_data_evaluation_type,
            )

            if market_data.empty:
                end, method_id = self.get_start_time_and_unique_id()
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=data.get(OrderField.META_KEY, pd.Series()).tolist(),
                        reason=f"No Market Data available for these RICs {instrument_rics}",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.GLOBAL_START_TIME,
                        end=end,
                        number_of_dropped_orders=len(data),
                        number_of_input_orders=len(data),
                        number_of_resulting_orders=0,
                        step_name="Getting Market Data based on Instrument Rics.",
                        step_type=StepType.MARKET_DATA_RIC,
                        drop_reason=DropReason.RECORDS_DROPPED_MARKET_DATA,
                    )
                )
                self.GLOBAL_START_TIME = end
                self._logger.info("Fetching Market Data")
                return pd.DataFrame()

            end, method_id = self.get_start_time_and_unique_id()

            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.GLOBAL_START_TIME,
                    end=end,
                    number_of_dropped_orders=0,
                    number_of_input_orders=len(data),
                    number_of_resulting_orders=len(data),
                    step_name="Fetching EOD Market Data",
                    step_type=StepType.MARKET_DATA_RIC,
                )
            )
            self.GLOBAL_START_TIME = end

            data.loc[:, "md_ident"] = (
                data[self._th_event_date].dt.strftime("%Y-%m-%d")
                + data[OrderField.INST_EXT_UNIQUE_IDENT]
            )

            market_data["md_ident"] = (
                market_data[TradeStatsColumns.DATE].dt.strftime("%Y-%m-%d")
                + market_data[OrderField.INST_EXT_UNIQUE_IDENT]
            )

            data = data.merge(market_data, how="left", on="md_ident")

        min_event_date: pd.Timestamp = data[self._th_event_date].min()

        max_event_date = None
        if self._th_day_and_order_evaluation_type == DayAndOrderEvaluationType.DAY:
            max_event_date: pd.Timestamp = data[self._th_event_date].max()

        min_date_year_is_less_2022 = min_event_date.year < 2022

        if not min_date_year_is_less_2022:
            dates = [min_event_date, max_event_date] if max_event_date else [min_event_date]
            quote_tick_data: pd.DataFrame = self.queries.get_tick_market_data(
                instrument_rics=instrument_rics,
                dates=dates,
                event_type=RefinitivEventType.QUOTE,
            )

            if not quote_tick_data.empty:
                quote_tick_data[RefinitivExtractColumns.DATE_TIME] = pd.to_datetime(
                    quote_tick_data.loc[:, RefinitivExtractColumns.DATE_TIME]
                )

                end, method_id = self.get_start_time_and_unique_id()
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        reason=f"Successfully fetched quote tick data for security {rics_inst_ids}, for the date {min_event_date.date()}.",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.GLOBAL_START_TIME,
                        end=end,
                        number_of_dropped_orders=0,
                        number_of_input_orders=len(data),
                        number_of_resulting_orders=len(data),
                        step_name="Calculation of 'Market Price Impact' Threshold",
                        step_type=StepType.MARKET_DATA,
                    )
                )
                self.GLOBAL_START_TIME = end

                market_price_impact_variables: Dict = self.calculate_market_impact_percentage_price(
                    tick_data=quote_tick_data,
                    min_event_date=min_event_date,
                    max_event_date=max_event_date,
                )

                data[DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE] = market_price_impact_variables.get(
                    DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE
                )
                data[DFColumns.NEAREST_MID_BEFORE] = market_price_impact_variables.get(
                    DFColumns.NEAREST_MID_BEFORE
                )
                data[DFColumns.NEAREST_MID_AFTER] = market_price_impact_variables.get(
                    DFColumns.NEAREST_MID_AFTER
                )

            if quote_tick_data.empty and self._th_market_price_impact:
                end, method_id = self.get_start_time_and_unique_id()
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=data.get(OrderField.META_KEY, pd.Series()).tolist(),
                        reason=f"No quote tick data for RICs {instrument_rics} for the date {min_event_date.date()}.",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.GLOBAL_START_TIME,
                        end=end,
                        number_of_dropped_orders=len(data),
                        number_of_input_orders=len(data),
                        number_of_resulting_orders=0,
                        step_name=f"Getting Quote Tick Data to calculate {DFColumns.MARKET_INTRA_DAY_VOLUME} and {DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE}.",
                        step_type=StepType.MARKET_DATA_TICKS,
                        drop_reason=DropReason.RECORDS_DROPPED_MARKET_DATA,
                    )
                )
                self.GLOBAL_START_TIME = end
                self._logger.debug(
                    f"No quote tick data for instruments {instrument_rics}, for the date {min_event_date.date()}."
                )
                return pd.DataFrame()

            if (
                self._th_market_data_evaluation_type
                == MarketDataEvaluationType.INTRA_DAY_TRADED_VOLUME
            ):
                trade_tick_data: pd.DataFrame = self.queries.get_tick_market_data(
                    instrument_rics=instrument_rics,
                    dates=[min_event_date],
                    event_type=RefinitivEventType.TRADE,
                )

                if trade_tick_data.empty:
                    end, method_id = self.get_start_time_and_unique_id()
                    singleton_audit_object.write_audit_data_to_local_files(
                        StepAudit(
                            step_id=method_id,
                            list_of_order_ids=data.get(OrderField.META_KEY, pd.Series()).tolist(),
                            reason=f"No trade tick data for RICs {instrument_rics} for the date {min_event_date.date()} "
                            f"to calculate the Market Intra Day Volume.",
                        )
                    )
                    singleton_audit_object.write_audit_data_to_local_files(
                        AggregatedStepAudit(
                            aggregated_step_id=method_id,
                            start=self.GLOBAL_START_TIME,
                            end=end,
                            number_of_dropped_orders=len(data),
                            number_of_input_orders=len(data),
                            number_of_resulting_orders=0,
                            step_name=f"Getting Trade Tick Data to calculate {DFColumns.MARKET_INTRA_DAY_VOLUME}",
                            step_type=StepType.MARKET_DATA_TICKS,
                            drop_reason=DropReason.RECORDS_DROPPED_MARKET_DATA,
                        )
                    )
                    self.GLOBAL_START_TIME = end
                    self._logger.debug(
                        f"No trade tick data for instruments {instrument_rics}, for the date {min_event_date.date()}."
                        f"Can't calculate the Market Intra Day Volume"
                    )

                    return pd.DataFrame()

                end, method_id = self.get_start_time_and_unique_id()
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        reason=f"Successfully fetched trade tick data for security {rics_inst_ids} "
                        f"for the date {min_event_date.date()}.",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.GLOBAL_START_TIME,
                        end=end,
                        number_of_dropped_orders=0,
                        number_of_input_orders=len(data),
                        number_of_resulting_orders=len(data),
                        step_name=f"Successfully fetched trade tick data for calculation of {DFColumns.MARKET_INTRA_DAY_VOLUME}.",
                        step_type=StepType.MARKET_DATA_TICKS,
                    )
                )
                self.GLOBAL_START_TIME = end

                trade_tick_data[RefinitivExtractColumns.DATE_TIME] = pd.to_datetime(
                    trade_tick_data.loc[:, RefinitivExtractColumns.DATE_TIME]
                )

                datetime_mask = (
                    trade_tick_data[RefinitivExtractColumns.DATE_TIME] <= end_date_intra_day
                ) & (trade_tick_data[RefinitivExtractColumns.DATE_TIME] >= start_date_intra_day)

                data[DFColumns.MARKET_INTRA_DAY_VOLUME] = trade_tick_data.loc[datetime_mask][
                    TradeTickColumns.VOLUME
                ].sum()

                data = data[~data[DFColumns.MARKET_INTRA_DAY_VOLUME].isin([0.0])]
                if data.empty:
                    self._logger.warning("No trade tick data available")
                    return pd.DataFrame()

        else:
            self._logger.warning(
                f"No tick data available before 2022. Earliest date is {min_event_date}"
            )

        return data

    def _check_minimum_quantity_threshold(self, group_quantity_sum: float) -> bool:
        """Check if the quantity sum is according with the minimum quantity
        threshold defined by the user.

        :param group_quantity_sum: float. quantity sum calculated for each grouping
        :return:  bool. true if passes, false if is to drop
        """

        return group_quantity_sum >= self._th_minimum_quantity

    def construct_time_window(self, data: pd.DataFrame) -> List[tuple]:
        """Construct list of time ranges to check the executions between that
        time period.

        :param data: pd.DataFrame. data from query
        :return: List[tuple], with date times
        """
        date_ranges: List[tuple] = []

        for date, group_date in data.groupby(self._th_event_date):
            with StrategyError.handle_algo_records_error(audit=self._audit, data=group_date):
                time_window: tuple = (
                    pd.to_datetime(
                        date - BDay(self._th_look_back_period.value), format="mixed"
                    ).date(),
                    # should be the date, so we calculate the ADTV for that day, that will have already an offset of 1 day
                    pd.to_datetime(date, format="mixed").date(),
                )

                date_ranges.append(time_window)

        return list(set(date_ranges))

    def _create_scenarios(self, data: pd.DataFrame) -> NoReturn:
        """Create algorithm scenarios, which is a dictionary.

        :param data: pd.DataFrame. Data that was filtered and grouped
        """

        if self._th_day_and_order_evaluation_type == DayAndOrderEvaluationType.ORDER:
            for order_id, group in data.groupby(OrderField.ORD_IDENT_ID_CODE):
                result = self._create_single_scenario(data=group)
                scenario = Scenario(result=result, context=self.context)
                self.scenarios.append(scenario)
        else:
            result = self._create_single_scenario(data=data)
            scenario = Scenario(result=result, context=self.context)
            self.scenarios.append(scenario)

    def _variables_configuration(self):
        """
        Configure some thresholds that are necessary for the algo
        :return:
        """
        if self._th_quantity_evaluation_type == QuantityEvaluationType.ORDER_QUANTITY:
            self._th_event_date = OrderField.TS_ORD_SUBMITTED
            self._th_quantity = OrderField.PC_FD_INIT_QTY
            self._th_events = EventType.NEWOS

        elif self._th_quantity_evaluation_type == QuantityEvaluationType.EXECUTED_QUANTITY:
            self._th_event_date = OrderField.TS_TRADING_DATE_TIME
            self._th_quantity = OrderField.PC_FD_TRD_QTY
            self._th_events = EventType.EXECUTIONS
        elif self._th_quantity_evaluation_type == QuantityEvaluationType.EXECUTED_NOTIONAL:
            self._th_event_date = OrderField.TS_TRADING_DATE_TIME
            self._th_quantity = OrderField.get_best_exc_trx_ecb_ref_rate_ccy(
                self._th_execution_notional_value_currency
            )
            self._th_events = EventType.EXECUTIONS

    def _convert_timestamp_to_datetime(self, data: pd.DataFrame) -> pd.DataFrame:
        """Convert _th_event_date column to datetime column.

        :param data: pd.DataFrame. algo data to check
        :return: pd.DataFrame. Dataframe with the date column as datetime
        """

        data.loc[:, self._th_event_date] = data[self._th_event_date].dt.date
        return data

    def calculate_market_impact_percentage_price(
        self,
        tick_data: pd.DataFrame,
        min_event_date: pd.Timestamp,
        max_event_date: pd.Timestamp,
    ) -> Dict:
        """Calculate market impact percentage.

        :param min_event_date: minimum date in the tenant data
        :param tick_data: quote tick data
        :return:
        """
        date_col = RefinitivExtractColumns.DATE_TIME
        price_col = QuoteTickColumns.MID_PRICE

        missing_cols = set([date_col, price_col]) - set(tick_data.columns)

        after_event_date = (
            max_event_date
            if max_event_date
            else pd.to_datetime(min_event_date, format="mixed") + datetime.timedelta(minutes=10)
        )

        if missing_cols:
            self._logger.debug(
                f"The column(s) {missing_cols} aren't present in the dataset. "
                f"They are important for the rest of the calculations."
            )
            return {
                DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE: pd.NA,
                DFColumns.NEAREST_MID_BEFORE: pd.NA,
                DFColumns.NEAREST_MID_AFTER: pd.NA,
            }

        if tick_data[date_col].min() > min_event_date:
            self._logger.debug(
                f"The minimum date in the tick data file {tick_data[date_col].min()} is greater than "
                f"the minimum date from the tenant which is {min_event_date}."
                f"So we're using the minimum date from the tick date file."
            )
            min_event_date = tick_data[date_col].min()

        tick_data_before_min_date = tick_data[tick_data[date_col] < min_event_date]

        if tick_data_before_min_date.empty:
            self._logger.debug("No tick data to use before the minimum event date.")
            tick_data_before_min_date = tick_data[tick_data[date_col] == min_event_date]

        nearest_mid_before: float = tick_data_before_min_date.iloc[-1][price_col]

        tick_data_after_event_date: pd.DataFrame = tick_data[tick_data[date_col] > after_event_date]

        nearest_mid_after_event_date: float = (
            tick_data.iloc[-1][price_col]
            if tick_data_after_event_date.empty
            else tick_data_after_event_date.iloc[0][price_col]
        )

        market_impact_percentage_price = (
            nearest_mid_after_event_date - nearest_mid_before
        ) / nearest_mid_before

        return {
            DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE: market_impact_percentage_price,
            DFColumns.NEAREST_MID_BEFORE: nearest_mid_before,
            DFColumns.NEAREST_MID_AFTER: nearest_mid_after_event_date,
        }

    def _check_orders_count_and_filter(
        self, initial_data: pd.DataFrame, grouping_fields: list, data: pd.DataFrame
    ) -> List[pd.DataFrame]:
        """Check if there are more vEvents (orders/executions) with a given
        grouping combination of GeneralEvaluationType & NormaliseGrouping
        within a certain time frame.

        :param initial_data: pd.DataFrame. Initial data from the first query executed in the algo
        :param grouping_fields: List[]. fields to group by the data
        :param data: pd.DataFrame. Algo data within a certain time frame
        :return: bool. True if there are more than one order/execution, False if not
        """
        initial_val_gnr_gpr: List[str] = (
            initial_data[GENERAL_GROUPING_MAP.get(self._th_general_evaluation_type)]
            .unique()
            .tolist()
        )

        initial_val_nrm_gpr: List[str] = (
            initial_data[self._get_normalise_grouping()].unique().tolist()
            if self._get_normalise_grouping() is not None
            else []
        )

        if GENERAL_GROUPING_MAP.get(self._th_general_evaluation_type) not in data.columns or (
            self._get_normalise_grouping() is not None
            and self._get_normalise_grouping() not in data.columns
        ):
            return [pd.DataFrame()]

        if initial_val_nrm_gpr:
            filtered_data = data[
                data[GENERAL_GROUPING_MAP.get(self._th_general_evaluation_type)].isin(
                    initial_val_gnr_gpr
                )
                & data[self._get_normalise_grouping()].isin(initial_val_nrm_gpr)
            ]
        else:
            filtered_data = data[
                data[GENERAL_GROUPING_MAP.get(self._th_general_evaluation_type)].isin(
                    initial_val_gnr_gpr
                )
            ]

        return [grp for info, grp in filtered_data.groupby(grouping_fields) if len(grp) > 1]

    def _sort_data_by_event_date(self, data) -> pd.DataFrame:
        """sort the algo data based on the event datetime.

        :param data: pd.DataFrame. algo data to check
        :return: pd.DataFrame. sorted dataframe
        """
        df: pd.DataFrame = data.sort_values(by=[self._th_event_date], ascending=True)

        return df

    def _create_single_scenario(self, data: pd.DataFrame) -> dict:
        """Create single scenario.

        :param data: pd.DataFrame. Final data to
        :return: dict with data to scenario
        """

        result = {}

        earliest_order = data.iloc[0]

        if OrderField.META_PARENT in data.columns and self._th_events == EventType.EXECUTIONS:
            if DFColumns.META_PARENT_META_KEY in data.columns:
                parent_meta_key = data[DFColumns.META_PARENT_META_KEY].dropna().unique().tolist()

                if parent_meta_key:
                    result[DFColumns.ORDERS_KEYS] = parent_meta_key
                    result[DFColumns.ORDER_DETECTED] = len(parent_meta_key)

        if OrderField.META_KEY in data.columns:
            result[DFColumns.ORDER_STATES_KEYS] = (
                data.loc[
                    data[OrderField.EXC_DTL_ORD_STATUS].isin([OrderStatus.PARF, OrderStatus.FILL]),
                    OrderField.META_KEY,
                ]
                .unique()
                .tolist()
            )

            result[DFColumns.EXECUTIONS_DETECTED] = data.loc[
                data[OrderField.EXC_DTL_ORD_STATUS].isin([OrderStatus.PARF, OrderStatus.FILL]),
                OrderField.ORD_IDENT_ID_CODE,
            ].nunique()

            if DFColumns.ORDERS_KEYS not in result.keys():
                result[DFColumns.ORDERS_KEYS] = (
                    data.loc[
                        data[OrderField.EXC_DTL_ORD_STATUS] == OrderStatus.NEWO,
                        OrderField.META_KEY,
                    ]
                    .unique()
                    .tolist()
                )

                result[DFColumns.ORDER_DETECTED] = data.loc[
                    data[OrderField.EXC_DTL_ORD_STATUS] == OrderStatus.NEWO,
                    OrderField.ORD_IDENT_ID_CODE,
                ].nunique()

        result = self.check_parent_and_child_orders(data_result=result, dataset=data)

        if OrderField.TS_ORD_SUBMITTED in data.columns:
            result[DFColumns.EARLIEST_TIMESTAMP] = earliest_order[OrderField.TS_ORD_SUBMITTED]

        if OrderField.INST_FULL_NAME in data.columns:
            result[DFColumns.INSTRUMENT_NAME_LIST] = (
                data[OrderField.INST_FULL_NAME].unique().tolist()
            )
            result[DFColumns.INSTRUMENT_DETECTED] = data[OrderField.INST_FULL_NAME].nunique()

        if OrderField.PC_FD_INIT_QTY in data.columns:
            result[DFColumns.INITIAL_QUANTITY] = data[OrderField.PC_FD_INIT_QTY].sum()

        if OrderField.PC_FD_TRD_QTY in data.columns:
            result[DFColumns.TRADED_QUANTITY] = data[OrderField.PC_FD_TRD_QTY].sum()

        if OrderField.CLIENT_FILE_IDENTIFIER in data.columns:
            result[DFColumns.CLIENT_NAME] = (
                data[OrderField.CLIENT_FILE_IDENTIFIER].unique().tolist()
            )

        if OrderField.COUNTERPARTY_ID in data.columns:
            result[DFColumns.COUNTERPARTY_NAME_LIST] = (
                data[OrderField.COUNTERPARTY_ID].unique().tolist()
            )

        if OrderField.TRADER_FILE_IDENTIFIER in data.columns:
            result[DFColumns.TRADER_NAME_LIST] = (
                data[OrderField.TRADER_FILE_IDENTIFIER].unique().tolist()
            )

        if OrderField.TRD_ALGO_FIRM_DESKS_NAME in data.columns:
            result[DFColumns.DESK_NAME_LIST] = (
                data[OrderField.TRD_ALGO_FIRM_DESKS_NAME].unique().tolist()
            )

        if DFColumns.ADV_PERCENTAGE in data.columns:
            result[DFColumns.ADV_PERCENTAGE] = data[DFColumns.ADV_PERCENTAGE].unique().tolist()

        if DFColumns.ADV in data.columns:
            result[DFColumns.ADV] = data[DFColumns.ADV].unique().tolist()

        if self._th_general_evaluation_type:
            result[DFColumns.EVALUATION_TYPE] = self._th_general_evaluation_type.value

            evaluation_col_to_check = GENERAL_GROUPING_MAP.get(self._th_general_evaluation_type)

            if evaluation_col_to_check in data.columns:
                result[DFColumns.EVALUATION_ID] = data[evaluation_col_to_check].unique().tolist()

        result[DFColumns.MARKET_PRICE_IMPROVEMENT] = (
            data[DFColumns.MARKET_PRICE_IMPROVEMENT].unique().tolist()
            if DFColumns.MARKET_PRICE_IMPROVEMENT in data.columns
            else None
        )

        result[DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE] = (
            data[DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE].unique().tolist()
            if DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE in data.columns
            else None
        )

        result[DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE_ABS] = (
            data[DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE_ABS].unique().tolist()
            if DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE_ABS in data.columns
            else None
        )

        result[DFColumns.NEAREST_MID_BEFORE] = (
            data[DFColumns.NEAREST_MID_BEFORE].unique().tolist()
            if DFColumns.NEAREST_MID_BEFORE in data.columns
            else None
        )
        result[DFColumns.NEAREST_MID_AFTER] = (
            data[DFColumns.NEAREST_MID_AFTER].unique().tolist()
            if DFColumns.NEAREST_MID_AFTER in data.columns
            else None
        )

        result[DFColumns.RIC.lower()] = None
        if DFColumns.RIC in data.columns:
            ric_values = data[DFColumns.RIC].dropna().unique()
            result[DFColumns.RIC.lower()] = ric_values[0] if len(ric_values) > 0 else None

        return result

    def check_parent_and_child_orders(self, data_result: dict, dataset: pd.DataFrame) -> dict:
        """Get the parent and the child IDs.

        :param data_result: dict, with the alert to be showed to the user
        :param dataset: pd.DataFrame. algo data to check
        :return: updated dict
        """
        data_updated: dict = data_result.copy()

        if data_result.get(DFColumns.ORDERS_KEYS) and not data_result.get(
            DFColumns.ORDER_STATES_KEYS
        ):
            parent_ids = dataset.loc[
                dataset[OrderField.EXC_DTL_ORD_STATUS] == OrderStatus.NEWO,
                OrderField.META_ID,
            ].tolist()

            child_executions: pd.DataFrame = self.queries.get_child_executions(
                parent_ids=parent_ids
            )
            if not child_executions.empty:
                data_updated[DFColumns.ORDER_STATES_KEYS] = (
                    child_executions.loc[:, OrderField.META_KEY].unique().tolist()
                )
                data_updated[DFColumns.EXECUTIONS_DETECTED] = child_executions.loc[
                    :, OrderField.ORD_IDENT_ID_CODE
                ].nunique()

        if data_result.get(DFColumns.ORDER_STATES_KEYS) and not data_result.get(
            DFColumns.ORDERS_KEYS
        ):
            child_executions: pd.DataFrame = self.queries.get_parent_orders(
                child_ids=data_result.get(DFColumns.ORDER_STATES_KEYS)
            )
            if (
                not child_executions.empty
                and not child_executions.loc[:, OrderField.ORD_IDENT_ID_CODE].isna().all()
            ):
                data_updated[DFColumns.ORDERS_KEYS] = (
                    child_executions.loc[:, OrderField.META_KEY].unique().tolist()
                )
                data_updated[DFColumns.ORDER_DETECTED] = child_executions.loc[
                    :, OrderField.ORD_IDENT_ID_CODE
                ].nunique()

        return data_updated

    def add_market_price_variables(self, data_to_check: pd.DataFrame):
        """Add market price percentage abs & market price improvement
        variables.

        :param data_to_check:
        :return: dataframe w more 2 columns or not
        """
        missing_columns = set(
            [
                OrderField.EXC_DTL_BUY_SELL_IND,
                DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE,
            ]
        ) - set(data_to_check.columns)

        if missing_columns:
            if self._th_market_price_impact:
                end, method_id = self.get_start_time_and_unique_id()
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=data_to_check.get(
                            OrderField.META_KEY, pd.Series()
                        ).tolist(),
                        reason=f"The columns {missing_columns} required to calculate the Market Price Impact "
                        "are missing from the data.",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.GLOBAL_START_TIME,
                        end=end,
                        number_of_dropped_orders=len(data_to_check),
                        number_of_input_orders=len(data_to_check),
                        number_of_resulting_orders=0,
                        step_name="Calculation of 'Market Price Impact' Threshold",
                        step_type=StepType.THRESHOLD_CALCULATION,
                        drop_reason=DropReason.RECORDS_DROPPED_FILTER_MANDATORY_FIELDS,
                    )
                )
                self.GLOBAL_START_TIME = end
                self._logger.debug(
                    f"Columns {missing_columns} are not present in the dataset, so the we can't proceed with the "
                    f"Market Price impact analysis, which is mandatory for this execution."
                )
                return pd.DataFrame()

            return data_to_check
        else:
            end, method_id = self.get_start_time_and_unique_id()
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.GLOBAL_START_TIME,
                    end=end,
                    number_of_dropped_orders=0,
                    number_of_input_orders=len(data_to_check),
                    number_of_resulting_orders=len(data_to_check),
                    step_name="Calculation of 'Market Price Impact' Threshold",
                    step_type=StepType.THRESHOLD_CALCULATION,
                )
            )
            self.GLOBAL_START_TIME = end

        data_to_check[DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE_ABS] = abs(
            data_to_check[DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE]
        )

        data_to_check[DFColumns.MARKET_PRICE_IMPROVEMENT] = data_to_check.apply(
            self.check_if_exists_market_impact, axis=1
        )

        return data_to_check

    def get_fields_filter(
        self,
        data: pd.DataFrame,
        historical_grouping_fields: Union[OrderField, List[OrderField]],
    ) -> List[dict]:
        """
        Returns a list of instruments if instrument_code is present in grouping_fields otherwise returns None
        method applied after a groupby, can have multiple values of same instrument
        data: pd.DataFrame,
        :param data: pd.DataFrame
        :param historical_grouping_fields: List[OrderField] , grouping fields only for internal flow logic
        :return: returns a List of instrument codes or None
        """

        universal_grouping_fields: Union[List[OrderField], OrderField] = (
            self._get_universal_grouping_field()
        )

        historical_filter_fields_list: List[dict] = self.check_historical_universal_filters(
            grouping_fields=historical_grouping_fields,
            data=data,
            grouping_fields_filter_list=[],
        )

        filter_fields_list: List[dict] = self.check_historical_universal_filters(
            grouping_fields=universal_grouping_fields,
            data=data,
            grouping_fields_filter_list=historical_filter_fields_list,
            exclude_fields_to_filter=[
                OrderField.ORD_IDENT_ID_CODE,
                OrderField.META_PARENT,
                OrderField.TS_ORD_SUBMITTED,
                OrderField.TS_ORD_UPDATED,
                OrderField.TS_TRADING_DATE_TIME,
            ],
        )

        if self._th_general_evaluation_type:
            general_eval_type: str = GENERAL_GROUPING_MAP.get(self._th_general_evaluation_type)

            filter_dict = self._assign_single_filter(column_field=general_eval_type, data=data)

            if filter_dict and filter_dict not in filter_fields_list:
                filter_fields_list.append(filter_dict)

        return filter_fields_list

    def check_historical_universal_filters(
        self,
        grouping_fields: Union[OrderField, List[OrderField]],
        data: pd.DataFrame,
        grouping_fields_filter_list: List,
        exclude_fields_to_filter: List[OrderField] = None,
    ) -> List[dict]:
        """check the filter fields from both historical & universal fields.

        :param exclude_fields_to_filter:
        :param grouping_fields_filter_list:
        :param grouping_fields:
        :param data:
        :return:
        """
        if isinstance(grouping_fields, str):
            if exclude_fields_to_filter and grouping_fields in exclude_fields_to_filter:
                return grouping_fields_filter_list

            filter_dict = self._assign_single_filter(column_field=grouping_fields, data=data)
            if filter_dict and filter_dict not in grouping_fields_filter_list:
                grouping_fields_filter_list.append(filter_dict)

        elif isinstance(grouping_fields, list):
            for filter_col in grouping_fields:
                if exclude_fields_to_filter and filter_col in exclude_fields_to_filter:
                    continue

                filter_dict = self._assign_single_filter(column_field=filter_col, data=data)
                if filter_dict and filter_dict not in grouping_fields_filter_list:
                    grouping_fields_filter_list.append(filter_dict)

        return grouping_fields_filter_list

    @staticmethod
    def _assign_single_filter(column_field: str, data: pd.DataFrame) -> dict:
        """check if column field is in data columns, if yes, create dict to
        filter historical data.

        :param column_field:
        :param data:
        :return:
        """
        filter_dict = {}
        if column_field in data.columns:
            filter_dict[column_field] = list(data[column_field].unique().tolist())
        return filter_dict

    def check_date_time_grouping(
        self, grouping_fields: List[OrderField], tenant_dataset: pd.DataFrame
    ) -> Tuple[List[OrderField], pd.DataFrame]:
        """Check if either order submitted or the trading date time are present
        in the groupings If yes:

        1. remove the one that is present from the groupings list;
        2. add a new column called date_col_grouping
        3. populate the column date_col_grouping with date (dd-mm-yyyy) of either order submitted or
        the trading date time instead of timestamp
        :return: Tuple with list of groupings and the new dataframe with the date column
        """
        grouping_fields.remove(self._th_event_date)
        grouping_fields.append("date_col_grouping")
        tenant_dataset["date_col_grouping"] = tenant_dataset[self._th_event_date].apply(
            lambda x: x.date()
        )

        return grouping_fields, tenant_dataset

    @staticmethod
    def refactor_date_column(
        data: pd.DataFrame,
        window: Union[int, str],
        time_series_column: str,
        data_column: str,
        date_format: Optional[str] = "%Y-%m-%d",
    ) -> pd.Series:
        """Refactor the date column: set the date column as index after set it
        to ISO format and then sort the dataframe by the index (that should be
        a date column)

        :param data: Pandas DataFrame. It has all market data
        :param window: str or int. Type of window to use
        :param time_series_column: str. Datetime column name to set as index. Default is None
        :param data_column: str. Needs to be a column defined on the scope
        :param date_format: bool. Boolean to set the datetime to isoformat. Default is True

        :return: Pandas Dataframe with the Date column in datetime type
        """
        if isinstance(window, str):
            if date_format:
                data[time_series_column] = pd.to_datetime(
                    data[time_series_column], format=date_format
                )
            else:
                data[time_series_column] = pd.to_datetime(data[time_series_column], format="mixed")

            data = data.set_index(time_series_column)

            data = data.sort_index()

        data_window: pd.Series = data[data_column]
        return data_window

    @staticmethod
    def check_if_exists_market_impact(data: pd.Series) -> bool:
        """check if there was market impact.

        :param data: pd.Dataframe, tenant data
        :return: pd.Series of Bool values, True if there is market impact, False if not
        """
        if pd.isna(data.get(DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE)):
            return False

        if (
            data.get(OrderField.EXC_DTL_BUY_SELL_IND) == BuySell.BUY
            and data.get(DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE) > 0
        ) or (
            data.get(OrderField.EXC_DTL_BUY_SELL_IND) == BuySell.SELL
            and data.get(DFColumns.MARKET_IMPACT_PERCENTAGE_PRICE) < 0
        ):
            return True

        return False

    @staticmethod
    def drop_rows_with_zero_activity(
        data: pd.DataFrame, activity_column_to_check: str
    ) -> pd.DataFrame:
        """Drop th_activity rows that have zero values.

        :param data: dataframe; historical data to be check
        :param activity_column_to_check: str; with the activiy column to check
        :return: dataframe where the activity col doesn't have zero values
        """

        data[activity_column_to_check] = data[activity_column_to_check].replace(0, pd.NA)
        data_with_activity: pd.DataFrame = data.dropna(subset=[activity_column_to_check])

        return data_with_activity

    @staticmethod
    def get_count_of_previous_days_with_activity(
        data: pd.DataFrame,
        day_to_check_data_before: datetime.date,
        max_look_back_period: int,
    ) -> int:
        """Get the historical data and create a column with the count of the
        previous days that have activity.

        :param data: DataFrame, with historical data
        :param max_look_back_period: if the previous days w activity reaches the max look back period,
        :param day_to_check_data_before: datetime.date, you want to check how many unique days exist in the dataset before this day
        :return: int, number of previous days w activity
        """

        list_of_dates = (
            data[data[DFColumns.ADV_DAY] < day_to_check_data_before][DFColumns.ADV_DAY]
            .unique()
            .tolist()
        )
        if len(list_of_dates) >= max_look_back_period:
            return max_look_back_period

        return len(list_of_dates)

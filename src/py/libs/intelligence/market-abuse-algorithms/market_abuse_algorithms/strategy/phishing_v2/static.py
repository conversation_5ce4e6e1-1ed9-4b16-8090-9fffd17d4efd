from enum import Enum


class ThresholdsNames(str, Enum):
    TIME_WINDOW = "timeWindow"
    PERCENTAGE_QUANTITY_OF_LARGE_ORDER = "percentageQuantityOfLargeOrder"
    MIN_NUMBER_OF_SMALL_ORDERS = "minNumberOfSmallOrders"
    NUMBER_OF_COUNTERPARTIES_INVOLVED = "numberOfCounterpartiesInvolved"
    ORDERS_IN_OPPOSITE_DIRECTION = "ordersInOppositeDirection"
    MERGED_ALERTS = "mergedAlerts"


class DFColumns:
    DATE = "date"
    INST_EXT_BEST_EX_ASSET_CLASS_MAIN = "bestExAssetClassMain"
    INST_FULL_NAME = "instrumentFullName"
    INST_ID_CODE = "instrumentId"
    MERGED_GROUPS = "mergedGroups"
    NUMBER_OF_ORDERS = "numberOfOrders"
    NUMBER_OF_SMALL_ORDERS = "numberOfSmallOrders"
    ORDERS = "orders"
    ORDERS_IN_OPPOSITE_DIRECTION = "ordersInOppositeDirection"
    SMALL_ORDERS = "smallOrders"
    SMALL_ORDERS_LIMIT = "smallOrderLimit"
    SMALL_ORDER_QUANTITY = "smallOrderQuantity"

    PREVIOUS_ALERTS = "previousAlerts"
    TS_MINUS_TIME_WINDOW = "tsMinusTimeWindow"

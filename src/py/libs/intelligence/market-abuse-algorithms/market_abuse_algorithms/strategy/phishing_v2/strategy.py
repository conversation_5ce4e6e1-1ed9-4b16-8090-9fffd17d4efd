import pandas as pd
from itertools import chain
from market_abuse_algorithms.data_source.static.sdp.order import NewColumns, OrderField
from market_abuse_algorithms.strategy.base.errors import StrategyError
from market_abuse_algorithms.strategy.base.models import NumberOfCounterparties, StrategyContext
from market_abuse_algorithms.strategy.base.static import StrategyLog, StrategyName
from market_abuse_algorithms.strategy.base.strategy import AbstractStrategy
from market_abuse_algorithms.strategy.phishing_v2.models import Thresholds
from market_abuse_algorithms.strategy.phishing_v2.query import Queries
from market_abuse_algorithms.strategy.phishing_v2.scenario import Scenario
from market_abuse_algorithms.strategy.phishing_v2.static import DFColumns, ThresholdsNames


class Strategy(AbstractStrategy):
    """Phishing V2.

    Jira tickets for the logic:
        - https://steeleye.atlassian.net/browse/PR-980
    """

    def __init__(self, context: StrategyContext):
        super().__init__(
            context=context,
            strategy_name=StrategyName.PHISHING_V2,
            thresholds_class=Thresholds,
            queries_class=Queries,
        )
        self._th_time_window = self.context.thresholds.dict().get(ThresholdsNames.TIME_WINDOW)
        self._th_percentage_quantity_of_large_order = self.context.thresholds.dict().get(
            ThresholdsNames.PERCENTAGE_QUANTITY_OF_LARGE_ORDER
        )
        self._th_min_number_of_small_orders = self.context.thresholds.dict().get(
            ThresholdsNames.MIN_NUMBER_OF_SMALL_ORDERS
        )

        self._th_number_of_counterparties_involved = self.context.thresholds.dict().get(
            ThresholdsNames.NUMBER_OF_COUNTERPARTIES_INVOLVED
        )

        self._th_orders_in_opposite_direction = self.context.thresholds.dict().get(
            ThresholdsNames.ORDERS_IN_OPPOSITE_DIRECTION
        )
        self._th_merged_alerts = self.context.thresholds.dict().get(ThresholdsNames.MERGED_ALERTS)

        self._set_time_window_threshold()

    def _set_time_window_threshold(self):
        """
        This method converts time window thresholds to pd.Timedelta
        :return:
        """

        self._th_time_window = pd.Timedelta(
            value=self._th_time_window.value, unit=self._th_time_window.unit.value
        )

    def _apply_strategy(self):
        for data in self.queries.cases_to_analyse():
            self._apply_strategy_mini_batch(df=data)

    def _apply_strategy_mini_batch(self, df: pd.DataFrame):
        cols = [NewColumns.INSTRUMENT_CODE, df[OrderField.TS_ORD_SUBMITTED].dt.date]

        if self._th_number_of_counterparties_involved == NumberOfCounterparties.SINGLE:
            cols.append(OrderField.COUNTERPARTY_ID)

        for group_id, group in df.groupby(cols):
            with StrategyError.handle_algo_records_error(audit=self._audit, data=df):
                self._logger.debug(StrategyLog.TOP_LEVEL.format(info=f"Group: {group_id}"))
                self._run_algo(df=group)

    def _run_algo(self, df: pd.DataFrame):
        results = self._algo(df=df)

        if results.empty:
            return

        self._create_scenarios(df=results)

    def _algo(self, df: pd.DataFrame) -> pd.DataFrame:
        df = df.set_index(OrderField.TS_ORD_SUBMITTED, drop=False).sort_index()
        result = df.copy()

        # Small quantity limit
        result[DFColumns.SMALL_ORDER_QUANTITY] = (
            result[OrderField.PC_FD_INIT_QTY] * self._th_percentage_quantity_of_large_order
        )

        # Min time window
        result[DFColumns.TS_MINUS_TIME_WINDOW] = (
            result[OrderField.TS_ORD_SUBMITTED] - self._th_time_window
        )

        # Scenario small orders within time window
        result[DFColumns.SMALL_ORDERS] = result[
            [
                DFColumns.TS_MINUS_TIME_WINDOW,
                DFColumns.SMALL_ORDER_QUANTITY,
                OrderField.TS_ORD_SUBMITTED,
            ]
        ].apply(
            lambda x: df.loc[
                (df[OrderField.TS_ORD_SUBMITTED] >= x[DFColumns.TS_MINUS_TIME_WINDOW])
                & (df[OrderField.TS_ORD_SUBMITTED] <= x[OrderField.TS_ORD_SUBMITTED])
                & (df[OrderField.PC_FD_INIT_QTY] <= x[DFColumns.SMALL_ORDER_QUANTITY]),
                OrderField.META_KEY,
            ].tolist(),
            axis=1,
        )

        # Number of small orders
        min_small_orders_mask = (
            result[DFColumns.SMALL_ORDERS].apply(len) > self._th_min_number_of_small_orders
        )

        result = result.loc[min_small_orders_mask]

        if result.empty:
            return pd.DataFrame()

        # Side
        if self._th_orders_in_opposite_direction:
            result[DFColumns.ORDERS_IN_OPPOSITE_DIRECTION] = result[
                [OrderField.EXC_DTL_BUY_SELL_IND, DFColumns.SMALL_ORDERS]
            ].apply(
                lambda x: df.loc[
                    (df[OrderField.META_KEY].isin(x[DFColumns.SMALL_ORDERS]))
                    & (df[OrderField.EXC_DTL_BUY_SELL_IND] != x[OrderField.EXC_DTL_BUY_SELL_IND]),
                    OrderField.META_KEY,
                ].tolist(),
                axis=1,
            )

            side_mask = result[DFColumns.ORDERS_IN_OPPOSITE_DIRECTION].apply(len)
            result = result.loc[side_mask > 0]

            if result.empty:
                return pd.DataFrame()

        if self._th_merged_alerts:
            result[DFColumns.SMALL_ORDERS] = result[DFColumns.SMALL_ORDERS].apply(set)

        return result

    def _create_scenarios(self, df: pd.DataFrame):
        keys = df[OrderField.META_KEY].unique().tolist()

        additional_fields = self.queries.get_additional_fields_for_scenarios(keys=keys)

        df = df.set_index(OrderField.META_KEY, drop=False)

        df = pd.concat([df, additional_fields], axis=1)

        df[DFColumns.NUMBER_OF_SMALL_ORDERS] = df[DFColumns.SMALL_ORDERS].map(len)

        df[DFColumns.DATE] = df[OrderField.TS_ORD_SUBMITTED].dt.date

        fields_to_scenario_map = {
            OrderField.DATE: DFColumns.DATE,
            OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN: DFColumns.INST_EXT_BEST_EX_ASSET_CLASS_MAIN,  # noqa: E501
            OrderField.INST_FULL_NAME: DFColumns.INST_FULL_NAME,
            OrderField.INST_ID_CODE: DFColumns.INST_ID_CODE,
            OrderField.META_KEY: DFColumns.ORDERS,
            DFColumns.NUMBER_OF_SMALL_ORDERS: DFColumns.NUMBER_OF_SMALL_ORDERS,
            DFColumns.SMALL_ORDER_QUANTITY: DFColumns.SMALL_ORDERS_LIMIT,
            DFColumns.SMALL_ORDERS: DFColumns.SMALL_ORDERS,
        }

        df = df.loc[:, df.columns.isin(fields_to_scenario_map.keys())]

        if self._th_merged_alerts:

            def check_small_orders(small_orders, df_orders, current_index):
                res = []
                for index, element in df_orders.items():
                    if (
                        index != current_index
                        and len(element.intersection(small_orders))
                        >= self._th_min_number_of_small_orders
                    ):
                        res.append(True)
                    else:
                        res.append(False)

                return res

            df[DFColumns.MERGED_GROUPS] = df.apply(
                lambda x: df.loc[
                    check_small_orders(
                        x[DFColumns.SMALL_ORDERS], df[DFColumns.SMALL_ORDERS], x.name
                    ),
                    OrderField.META_KEY,
                ].tolist(),
                axis=1,
            )
            df[DFColumns.MERGED_GROUPS] = df[DFColumns.MERGED_GROUPS].apply(lambda x: tuple(x))

        df[OrderField.META_KEY] = df[OrderField.META_KEY].apply(lambda x: [x])

        if self._th_merged_alerts:
            df = self.merge_alerts(df)
            df = df.drop(labels=DFColumns.MERGED_GROUPS, axis=1)

        df.columns = df.columns.map(fields_to_scenario_map)

        df[DFColumns.NUMBER_OF_ORDERS] = df[DFColumns.ORDERS] + df[DFColumns.SMALL_ORDERS]

        df[DFColumns.NUMBER_OF_ORDERS] = df[DFColumns.NUMBER_OF_ORDERS].apply(lambda x: len(set(x)))  # noqa: E501

        df[DFColumns.MERGED] = df[DFColumns.ORDERS].str.len() > 1

        results = df.to_dict(orient="records")

        for result in results:
            scenario = Scenario(result=result, context=self.context)
            self.scenarios.append(scenario)

    def merge_alerts(self, df: pd.DataFrame) -> pd.DataFrame:
        final_df = df.copy()
        for ix, row in df.iterrows():
            if not row[DFColumns.MERGED_GROUPS]:
                continue
            final_df.at[ix, OrderField.META_KEY].extend(row[DFColumns.MERGED_GROUPS])
            temp_df = final_df.loc[final_df.loc[ix, OrderField.META_KEY]]
            final_df.loc[ix, DFColumns.SMALL_ORDER_QUANTITY] = temp_df[
                DFColumns.SMALL_ORDER_QUANTITY
            ].max()

            small_orders = set(chain.from_iterable(temp_df[DFColumns.SMALL_ORDERS]))
            final_df.at[ix, DFColumns.SMALL_ORDERS] = small_orders
            final_df.at[ix, DFColumns.NUMBER_OF_SMALL_ORDERS] = len(small_orders)

        final_df[DFColumns.SMALL_ORDERS] = final_df[DFColumns.SMALL_ORDERS].apply(
            lambda x: frozenset(x)
        )
        final_df[OrderField.META_KEY] = final_df[OrderField.META_KEY].apply(lambda x: frozenset(x))
        final_df = final_df.drop_duplicates(OrderField.META_KEY)

        final_df[OrderField.META_KEY] = final_df[OrderField.META_KEY].apply(lambda x: list(x))
        final_df[DFColumns.SMALL_ORDERS] = final_df[DFColumns.SMALL_ORDERS].apply(lambda x: list(x))  # noqa: E501

        return final_df

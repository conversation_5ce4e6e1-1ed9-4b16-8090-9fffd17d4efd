import addict
import copy
import json
import logging
import os
import sys
from csurv_utils.utils import determine_date_range
from datetime import datetime
from flang.generators import BadFilterValue, NotSupported  # pants: no-infer-dep
from se_core_tasks.io.read.fetch_tenant_configuration import run_fetch_tenant_configuration
from se_elastic_schema.components.surveillance.false_positive_reduction import PredictedClass
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8
from se_elastic_schema.models.tenant.surveillance.communication_alert import CommunicationAlert
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch
from se_elastic_schema.static.surveillance import BehaviourQueryKind, TemplateType, WatchQueryType
from se_es_utils.search_after import search_after_query_yield
from se_es_utils.slim_record_handler import SlimRecordHandler
from se_schema_meta import ID
from surveillance_query_builder.alerts_query_builder.builder import AlertsQueryBuilder
from surveillance_query_builder.comms_query_builder.builder import WatchCommsQueryBuilder
from surveillance_utils.deduplication import AlertDeduplication
from typing import Iterator, List, Tuple, Union

logger = logging.getLogger(__name__)


def get_watch_builder_model(
    watch_type: WatchQueryType,
) -> Union[WatchCommsQueryBuilder, AlertsQueryBuilder]:
    if watch_type == WatchQueryType.COMMUNICATIONS:
        return WatchCommsQueryBuilder
    else:
        return AlertsQueryBuilder


def run_query(
    alert_de_dup: AlertDeduplication,
    tenant: str,
    surveillance_watch: SurveillanceWatch,
    upper_bound_date: datetime,
    index_list: List[SteelEyeSchemaBaseModelES8],
    record_handler: SlimRecordHandler,
    watch_type: Union[WatchQueryType.COMMUNICATIONS, WatchQueryType.ALERT_SAMPLING],
) -> Iterator[List[dict]]:
    """Extracts the query from the SurveillanceWatch and runs it to check if
    there is a need to generate new Alerts.

    :param tenant: name of the tenant
    :param surveillance_watch:  SurveillanceWatch Record
    :param record_handler: RecordHandler
    :param index_list: List of indices to search
    :return: list of new hits, if there are any and upper bound date limit from query
    """

    logger.info("Beginning of the RunQuery Task")

    if surveillance_watch.queryType != watch_type:
        raise Exception(
            f"Running a watch from type {surveillance_watch.queryType.value} as {watch_type.value}"
        )

    try:
        # Patch for EU-8711
        if len(surveillance_watch.query.filter.chunks) > 300:
            sys.setrecursionlimit(1500)

    except Exception:
        pass
    builder = get_watch_builder_model(watch_type)
    try:
        query = builder(surveillance_watch.to_dict(exclude_none=True)).build_query()
    except (BadFilterValue, NotSupported):
        raise Exception(
            f"The following query is not supported by flang, {surveillance_watch.query}"
        )

    hits_for_new_alerts = _retrieve_hits(
        alert_de_dup=alert_de_dup,
        query=query,
        tenant=tenant,
        surveillance_watch=surveillance_watch,
        upper_bound_date=upper_bound_date,
        index_list=index_list,
        record_handler=record_handler,
        watch_type=watch_type,
    )
    return hits_for_new_alerts


def _retrieve_hits(
    alert_de_dup: AlertDeduplication,
    query: addict.Dict,
    tenant: str,
    surveillance_watch: SurveillanceWatch,
    upper_bound_date: datetime,
    index_list: List[SteelEyeSchemaBaseModelES8],
    record_handler: SlimRecordHandler,
    watch_type: Union[WatchCommsQueryBuilder, AlertsQueryBuilder],
) -> Iterator[List[dict]]:
    """Retrieves hits from the different types of records.

    This method searches in the index_list for a given tenant.
    If there are any existing previously analyzed hits (with alerts), there are excluded.
    Retrieves hits for whose will be created alerts.

    :param query: Query to be used to search for hits to create alerts
    :param tenant: name of the tenant to be searched
    :param surveillance_watch:  SurveillanceWatch Record
    :param record_handler: RecordHandler
    :param index_list: List of indices to search
    :return: hits to be used on new alerts
    """
    query = copy.deepcopy(query)
    request_timeout = int(os.environ.get("REQUEST_TIMEOUT_SECONDS", 600))
    indices: str = ",".join([index.get_elastic_index_alias(tenant=tenant) for index in index_list])
    watch_query_dict = surveillance_watch.query.dict()

    if watch_query_dict.get("lexicaBehaviour"):
        for i in query.get("query", {}).get("bool", {}).get("must", {}):
            # Needed for legacy compatibility (cSurv1 watches used analytics.lexica.category)
            if "{'analytics.lexica.category':" in str(i):
                behaviour = i["nested"]["query"]["bool"]["must"]["term"].pop(
                    "analytics.lexica.category"
                )
                i["nested"]["query"]["bool"]["must"]["term"]["analytics.lexica.behaviour"] = (
                    behaviour
                )

            if "{'analytics.lexica.behaviour':" in str(i):
                languages = [
                    index
                    for index in surveillance_watch.to_dict(exclude_none=True)["query"][
                        "lexicaBehaviour"
                    ]["languages"]
                ]
                must_behaviour = i.get("nested").get("query").get("bool").pop("must")
                must_with_languages = [
                    {"terms": {"analytics.lexica.termLanguage": languages}}
                ] + must_behaviour
                i["nested"]["query"]["bool"]["must"] = must_with_languages

    expiry_str = "{'exists': {'field': '___expiry___'}}"
    expiry_str = expiry_str.replace("___expiry___", record_handler.meta.expiry)
    if expiry_str not in str(query.query.bool.must_not):
        query.query.bool.must_not.append({"exists": {"field": record_handler.meta.expiry}})

    # Replace must with filter
    if query.query.bool.must:
        must_clause = copy.deepcopy(query.query.bool.must)
        query.query.bool.pop("must")
        query.query.bool.filter = must_clause

    # Add date range to query
    date_range_from = determine_date_range(surveillance_watch=surveillance_watch)
    query_range = {
        "range": {
            record_handler.meta.timestamp: {
                "gt": int(date_range_from.date_from.timestamp() * 1000),  # type: ignore[union-attr]
                "lte": int(upper_bound_date.timestamp() * 1000),
            }
        }
    }

    filter = query.query.bool.get("filter", [])
    query.query.bool.filter = filter + [query_range]

    # Handle Template Queries
    if (
        surveillance_watch.query.kind == BehaviourQueryKind.TEMPLATE
        and surveillance_watch.query.template.templateType != TemplateType.DATA_LEAKAGE_INFO_BARRIER
    ):
        query, total_records = _add_template_filter(
            query, surveillance_watch, record_handler, indices, request_timeout
        )
        if total_records == 0:
            logger.info(f"Against {indices}, executing: {json.dumps(query, default=str)}")
            logger.warning("Stopping execution, no records found")
            return iter([])
        query_total_hits = int(query.get("size", 5000) or 5000)
        query["size"] = min(query_total_hits, 250)
    else:
        query_total_hits = -1
        query["size"] = 250

    # Handle False Positive Reduction
    if (
        watch_type == WatchQueryType.COMMUNICATIONS
        and surveillance_watch.query.falsePositiveReduction
    ):
        # Handle Zoning
        excluded_zones = surveillance_watch.query.falsePositiveReduction.excludedZones
        if excluded_zones:
            query = _add_zoning_filter(query, excluded_zones)

        # Handle Classification
        excluded_classes = surveillance_watch.query.falsePositiveReduction.excludedClassifications
        if excluded_classes:
            query = _add_classifier_filter(query, excluded_classes)

    query = alert_de_dup.update_query(query, models=[CommunicationAlert])

    if watch_type == WatchQueryType.ALERT_SAMPLING:
        # We don't want Alerts of Random Sampling Alerts

        must_not = query.query.function_score.query.bool.get("must_not", [])
        query.query.function_score.query.bool.must_not = must_not + [
            {"exists": {"field": "underlyingAlertId"}}
        ]

    if watch_query_dict.get("infoBarrier"):
        tenant_configuration = run_fetch_tenant_configuration(
            es_client=record_handler.es_repo, tenant=tenant
        )
        infoBarrierMaxRecipients = tenant_configuration.get("commsSurveillanceSettings", {}).get(
            "infoBarrierMaxRecipients", 0
        )
        if infoBarrierMaxRecipients:
            filter = query.query.bool.get("filter", [])
            max_recipients = {
                "range": {"stats.participantsCount": {"lte": infoBarrierMaxRecipients}}
            }
            query.query.bool.filter = filter + [max_recipients]
            logger.info(f"Setting the max of stats.participantsCount as {infoBarrierMaxRecipients}")

    # Run the query
    logger.info(f"Against {indices}, executing: {json.dumps(query, default=str)}")
    hit_generator = search_after_query_yield(
        query_dict=query,
        index=indices,
        es_client=record_handler.es_repo,
        query_total_hits=query_total_hits,
        request_timeout=request_timeout,
    )

    return hit_generator


def _add_template_filter(
    query: dict,
    surveillance_watch: SurveillanceWatch,
    record_handler: SlimRecordHandler,
    indices: str,
    request_timeout: int,
) -> Tuple[dict, int]:
    """Adding size filter for Random Sampling watches.

    :param query: ElasticSearch query
    :param surveillance_watch:  SurveillanceWatch Record
    :param record_handler: RecordHandler
    :param indices: ElasticSearch indices
    :param request_timeout: ElasticSearch request timeout
    :return:
    """
    # Count the total number of records that match the query
    count_query = copy.deepcopy(query)
    count_query.pop("size", None)
    count_query.pop("sort", None)
    count_query.pop("_source", None)
    total_records = record_handler.client.count(
        body=count_query,
        index=indices,
        request_timeout=request_timeout,
    )["count"]

    query = {
        "query": {
            "function_score": {
                "query": query["query"],
                "random_score": {"seed": int(datetime.utcnow().timestamp() * 1000), "field": ID},
                "boost_mode": "replace",
            }
        }
    }

    sample_size = surveillance_watch.query.template.thresholds.sampleSize
    sample_percentage = surveillance_watch.query.template.thresholds.samplePercentage

    try:
        count_by_absolute = int(sample_size)
    except Exception:
        count_by_absolute = 5000

    try:
        count_by_percentage = int(round(total_records * sample_percentage / 100)) or 1
    except Exception:
        count_by_percentage = 5000

    query["size"] = min((count_by_absolute, count_by_percentage, 5000))

    logger.info(f"Randomly sampling {query['size']} out of {total_records} records")
    return query, total_records


def _add_zoning_filter(query: dict, excluded_zones: List[PredictedClass]) -> dict:
    """Adds the zoning filter.

    :param query: ElasticSearch query
    :param excluded_zones: List of zones to exclude
    :return: ElasticSearch query
    """
    # The matching hit must have at least one trigger NOT in the excluded zones
    # This means that hits with triggers with no zone are NOT excluded
    zone_should_filter = []
    for excluded_class in excluded_zones:
        zone_should_filter.append(
            {
                "bool": {
                    "should": [
                        {
                            "bool": {
                                "must_not": [
                                    {
                                        "term": {
                                            "analytics.lexica.triggers.zone_class": excluded_class.className  # noqa: E501
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            "range": {
                                "analytics.lexica.triggers.zone_value": {
                                    "lte": excluded_class.confidenceScore
                                }
                            }
                        },
                    ]
                }
            }
        )
    sub_query = addict.Dict()
    sub_query.nested.path = "analytics.lexica.triggers"
    sub_query.nested.query.bool.must = zone_should_filter

    for query_part in query["query"]["bool"]["filter"]:
        if "{'analytics.lexica.behaviour':" in str(query_part):
            try:
                query_part["nested"]["query"]["bool"]["must"].append(sub_query.to_dict())
            except KeyError:
                # we are in the presence of a info barrier watch
                for should_query in query_part["bool"]["should"]:
                    should_query["bool"]["filter"].append(sub_query.to_dict())

    return query


def _add_classifier_filter(query: dict, excluded_classes: List[PredictedClass]) -> dict:
    """Adds the classifier filter.

    :param query: ElasticSearch query
    :param excluded_classes: List of classes to exclude
    :return: ElasticSearch query
    """
    # Records either don't have the classifier field or they conform
    # to the excluded classifications
    # Meaning Records that were not classified are NOT excluded

    query["query"]["bool"]["minimum_should_match"] = 1
    query["query"]["bool"]["should"] = [
        {"bool": {"must_not": [{"exists": {"field": "analytics.classifier"}}]}}
    ]
    nested_class_filter = []
    for excluded_class in excluded_classes:
        sub_query = addict.Dict()
        sub_query.nested.path = "analytics.classifier.predictions"
        sub_query.nested.query.bool.must = [
            {"term": {"analytics.classifier.predictions.class": excluded_class.className}},
            {
                "range": {
                    "analytics.classifier.predictions.value": {"gt": excluded_class.confidenceScore}
                }
            },
        ]
        nested_class_filter.append(sub_query.to_dict())
    query["query"]["bool"]["should"].append({"bool": {"must_not": nested_class_filter}})

    return query

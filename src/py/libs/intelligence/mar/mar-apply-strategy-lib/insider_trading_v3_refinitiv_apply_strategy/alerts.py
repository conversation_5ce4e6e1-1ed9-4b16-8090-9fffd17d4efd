import addict
import pandas as pd
from insider_trading_v3_refinitiv_apply_strategy.static import (
    AlertColumnsEnum,
    EventDayDetailsEnum,
    EventDirection,
    RefinitivNewsColumns,
)
from market_abuse_algorithms.strategy.base.scenario import (  # type: ignore[attr-defined]
    AbstractScenario,
    TradeColumns,
)
from market_abuse_algorithms.strategy.base.static import ScenarioFields
from typing import Dict, List, Tuple, Union


class InsiderTradingV3RefinitivScenario(AbstractScenario):  # type: ignore
    def _trade_columns(self) -> TradeColumns:
        return TradeColumns(
            single=[],
            multiple=[
                "behaviourPeriodExecutions",
                "observationPeriodExecutions",
                "observationPeriodExecutionsPostEvent",
            ],
        )

    def _build_scenario(self) -> pd.Series:
        records = {
            "behaviourPeriodExecutions": self._result.pop(
                AlertColumnsEnum.BEHAVIOUR_PERIOD_EXECUTIONS
            ),
            "observationPeriodExecutions": self._result.pop(
                AlertColumnsEnum.OBSERVATION_PERIOD_EXECUTIONS
            ),
            "observationPeriodExecutionsPostEvent": self._result.pop(
                AlertColumnsEnum.OBSERVATION_PERIOD_EXECUTIONS_POST_EVENT
            ),
        }

        alert: addict.Dict = addict.Dict(
            thresholds=self._thresholds,
            records=records,
            additionalFields={ScenarioFields.TOP_LEVEL: self._result},
        )

        scenario: pd.Series = pd.Series(alert.to_dict())

        return scenario


def create_alert_news_feed_events_fields(
    alert: Dict[str, str],
    event_day_details: Dict[str, Union[str, float]],
    instrument: str,
    news_feed_events: List,
    observation_amount: float,
) -> Tuple[Dict[str, str], Dict[str, str | float]]:
    """populates news feed events fields in alert.

    :param news_feed_events: list of new feed events
    :param event_day_details: event day details dictionary
    :param alert: dictionary with fields required for the alert
    :param instrument: instrument for which the alert is being created
    :return: alert with news feed events field populated
    """
    news_feed_event = [event for event in news_feed_events if event.get_instrument() == instrument]

    if len(news_feed_event) > 1:
        event_direction = EventDirection.UP if observation_amount >= 0 else EventDirection.DOWN
        news_feed_event = [
            event for event in news_feed_event if event.get_direction() == event_direction
        ]

    if len(news_feed_event) == 0:
        return alert, event_day_details

    news_feed_dataframe: pd.DataFrame = news_feed_event[0].get_news()

    alert[AlertColumnsEnum.NEWS_HEADLINE] = news_feed_dataframe.loc[  # type: ignore
        :,
        [
            RefinitivNewsColumns.TITLE,
            RefinitivNewsColumns.STORY_CREATED,
            RefinitivNewsColumns.SOURCES,
            RefinitivNewsColumns.SENTIMENT_POSITIVE,
            RefinitivNewsColumns.SENTIMENT_NEGATIVE,
            RefinitivNewsColumns.SENTIMENT_NEUTRAL,
            RefinitivNewsColumns.STORY_ID,
            RefinitivNewsColumns.STORY_SUBJECT_RELEVANCE,
            RefinitivNewsColumns.SUBJECTS,
            RefinitivNewsColumns.CONTENT_URI,
        ],
    ].to_dict(orient="records")

    alert[AlertColumnsEnum.EVENT_DIRECTION] = news_feed_event[0].get_direction()

    event_day_details[EventDayDetailsEnum.NEWS_HEADLINE] = news_feed_dataframe.loc[  # type: ignore
        :, RefinitivNewsColumns.TITLE
    ].tolist()

    return alert, event_day_details


def create_alert_market_data_events_fields(
    alert: Dict[str, str],
    event_day_details: Dict[str, str],
    instrument: str,
    market_data_events: List,
) -> Tuple[Dict[str, str], Dict[str, str]]:
    """populates market data events fields in alert.

    :param market_data_events: list of market data events events
    :param event_day_details: event day details dictionary
    :param alert: dictionary with fields required for the alert
    :param instrument: instrument for which the alert is being created
    :return: alert with market data events field populated
    """
    # ToDo: Remove the instrument check once strategy module from maa is deleted
    market_data_event = [
        event for event in market_data_events if event.get_instrument() == instrument
    ]

    if len(market_data_event) == 0:
        return alert, event_day_details

    alert[AlertColumnsEnum.MARKET_CLOSE_PRICE_VARIATION] = market_data_event[
        0
    ].get_price_variation()
    alert[AlertColumnsEnum.MARKET_PRICE_LIMITS] = market_data_event[0].get_price_range()
    alert[AlertColumnsEnum.EVENT_DIRECTION] = market_data_event[0].get_direction()

    event_day_details[EventDayDetailsEnum.CLOSE_PRICE_VARIATION] = market_data_event[
        0
    ].get_price_variation()
    event_day_details[EventDayDetailsEnum.ALLOWED_PRICE_VARIATION_RANGE] = market_data_event[
        0
    ].get_price_range()

    return alert, event_day_details

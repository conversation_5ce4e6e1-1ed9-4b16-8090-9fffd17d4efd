# type: ignore
import logging
import pandas as pd
import polars as pl
from mar_utils.abstract.abstract_mar_apply_strategy import AbstractMarApplyStrategy
from mar_utils.auditor.strategy_audits.wash_trading_audit import (
    WashTradingAudit,
    WashTradingAuditName,
)
from market_abuse_algorithms.data_source.repository.market_data.static import EODStatsDataColumns
from market_abuse_algorithms.data_source.static.sdp.order import BuySell, OrderField
from market_abuse_algorithms.utils.formulas import calculate_percentage_difference  # type: ignore
from typing import Any, Optional
from wash_trading_apply_strategy.alerts import Scenario  # type: ignore
from wash_trading_apply_strategy.models import (
    EVALUATION_TYPE_FIELD_MAPPING,
    Thresholds,
)
from wash_trading_apply_strategy.query import Queries  # type: ignore
from wash_trading_apply_strategy.static import (
    DAY_TRADED_NOTIONAL_MAP,
    NOTIONAL_OPERATOR_MAP,
    DFColumns,
    NumberOfCounterparties,
)

log = logging.getLogger(__name__)


class ApplyStrategy(AbstractMarApplyStrategy):
    def __init__(self, **kwargs):
        super().__init__(
            queries_cls=Queries,
            thresholds_cls=Thresholds,
            scenario_cls=Scenario,
            mar_auditor_cls=WashTradingAudit,
            **kwargs,
        )

        self._thresholds_max_price_diff = None
        if self.thresholds.maxPriceDifference is not None:
            self._thresholds_max_price_diff = self.thresholds.maxPriceDifference / 100 / 100

        if self.thresholds.dayTradedNotionalCurrency is not None:
            self._thresholds_day_traded_notional_currency_col = DAY_TRADED_NOTIONAL_MAP.get(
                self.thresholds.dayTradedNotionalCurrency
            ).value

    def _apply_strategy(self):
        group_data = self.get_group().to_dicts()[0]
        log.debug("Applying strategy on group: %s", group_data)

        # Get the orders for every instrument one by one
        for instrument_ric in group_data["instrument"]:
            instrument = instrument_ric["instrument"]
            ric = instrument_ric["ric"]
            data = []
            for result in self._queries.get_instrument_orders(
                instrument_id=instrument, search_size=5000
            ):
                data.extend(result)

            data_df = pl.json_normalize(data)

            data_df = data_df.with_columns(
                pl.col(OrderField.TS_TRADING_DATE_TIME)
                .str.strptime(pl.Datetime, strict=False)
                .cast(pl.Datetime("ns"))
            ).sort(OrderField.TS_TRADING_DATE_TIME)

            data_df = data_df.with_columns(
                pl.col(OrderField.TS_TRADING_DATE_TIME)
                .dt.truncate("1s")
                .alias(DFColumns.TS_TRADE_TIME_SEC),
                (
                    pl.col(OrderField.TS_TRADING_DATE_TIME)
                    + pl.duration(seconds=self.thresholds.maxTimeWindow)
                ).alias(DFColumns.TS_TRADE_TIME_AFTER),
            )

            if self.thresholds.numberOfCounterparties == NumberOfCounterparties.SINGLE:
                grouping_columns = [OrderField.COUNTERPARTY_ID]
            else:
                grouping_columns = []

            if self.thresholds.evaluationType:
                grouping_columns.append(
                    EVALUATION_TYPE_FIELD_MAPPING[self.thresholds.evaluationType]
                )

            results = []
            for _, group in data_df.group_by(grouping_columns or None):
                # Verifies records have daily traded notional to what is defined in threshold
                if self.thresholds.dayTradedNotional is not None:
                    group: pl.DataFrame = self.filter_day_traded_notional(data=group, ric=ric)
                    if group.is_empty():
                        log.info("No data remaining after applying day traded notional threshold")
                        continue

                for execution in group.iter_rows(named=True):
                    result = self._run_algo(instrument, execution, group)
                    if result:
                        results.append(result)

            if not results:
                continue

            # NOTE: The following code is done in pandas because for some reason, polars
            # sort differs from pandas sort, so to not bring any rgression.
            results_df = pd.DataFrame(results).sort_values(by=OrderField.TS_TRADING_DATE_TIME)

            # Filter window executions already found
            wash_executions = results_df[DFColumns.EXECUTIONS_KEYS].apply(set)

            wash_executions_diff = (wash_executions - wash_executions.shift()).fillna(
                wash_executions
            )

            mask = wash_executions_diff.apply(len) > 0

            results_df = results_df[mask]
            self._prepare_scenarios(results_df)

    def _prepare_scenarios(self, results_df: pd.DataFrame):
        if results_df.empty:
            return

        additional_fields = self.queries.get_additional_fields_for_results(
            results_df[[OrderField.META_KEY, DFColumns.EXECUTIONS_KEYS]]
        )

        results = results_df.merge(additional_fields, how="left", on=OrderField.META_KEY)
        self._write_result_to_ndjson(results_df=results)

    def _write_result_to_ndjson(self, **kwargs):
        result_df = kwargs["results_df"]
        with open(self.result_local_file_path, "a") as output_alerts:
            output_alerts.write(result_df.to_json(date_format="iso", orient="records", lines=True))

    def _run_algo(self, instrument: str, execution: dict[str, Any], group: pl.DataFrame):
        upper_mask = (
            group[OrderField.TS_TRADING_DATE_TIME] <= execution[DFColumns.TS_TRADE_TIME_AFTER]
        )
        lower_mask = (
            group[OrderField.TS_TRADING_DATE_TIME] >= execution[OrderField.TS_TRADING_DATE_TIME]
        )

        group_window = group.filter(upper_mask & lower_mask)

        if group_window.is_empty():
            self.auditor.step_audit(
                audit_key=WashTradingAuditName.EMPTY_TIME_WINDOW_GROUP,
                audit_data={"instrument": instrument, "execution": execution[OrderField.META_KEY]},
                list_of_order_ids=[execution[OrderField.META_KEY]],
                number_of_input_orders=1,
                number_of_resulting_orders=0,
            )
            return

        self.auditor.step_audit(
            audit_key=WashTradingAuditName.CREATE_TIME_WINDOW_GROUP,
            audit_data={"instrument": instrument, "execution": execution[OrderField.META_KEY]},
            number_of_input_orders=1,
            number_of_resulting_orders=1,
        )

        # Calculate the price difference
        if self._thresholds_max_price_diff is not None:
            lower_bound = (
                execution[OrderField.PC_FD_PRICE]
                - execution[OrderField.PC_FD_PRICE] * self._thresholds_max_price_diff
            )
            upper_bound = (
                execution[OrderField.PC_FD_PRICE]
                + execution[OrderField.PC_FD_PRICE] * self._thresholds_max_price_diff
            )
            group_window = group_window.filter(
                pl.col(OrderField.PC_FD_PRICE).is_between(lower_bound, upper_bound)
            )

        if any(
            (
                group_window.is_empty(),
                group_window.n_unique(subset=OrderField.EXC_DTL_BUY_SELL_IND) != 2,
                self.thresholds.excludeMatchingTimestamps
                and group_window.n_unique(subset=DFColumns.TS_TRADE_TIME_SEC) == 1,
            )
        ):
            self.auditor.step_audit(
                audit_key=WashTradingAuditName.EMPTY_MAX_PRICE_DIFFERENCE,
                audit_data={"execution": execution[OrderField.META_KEY]},
                list_of_order_ids=[execution[OrderField.META_KEY]],
                number_of_input_orders=1,
                number_of_resulting_orders=0,
            )

            return

        if self._thresholds_max_price_diff is not None:
            self.auditor.step_audit(
                audit_key=WashTradingAuditName.FILTERED_MAX_PRICE_DIFFERENCE,
                audit_data={"execution": execution[OrderField.META_KEY]},
                number_of_input_orders=1,
                number_of_resulting_orders=1,
            )

        # Apply notional calculation
        if self.thresholds.minimumNotionalValueCurrency:
            currency_field = OrderField.get_best_exc_trx_ecb_ref_rate_ccy(
                currency=self.thresholds.minimumNotionalValueCurrency
            )

            currency_total_sum = (
                group_window.select(pl.col(currency_field).sum().alias("total_sum"))
                .to_dicts()[0]
                .get("total_sum", 0)
            )

            if currency_total_sum < self.thresholds.minimumNotionalValue:
                self.auditor.step_audit(
                    audit_key=WashTradingAuditName.DROPPED_MINIMUM_NOTIONAL_THRESHOLD,
                    audit_data={
                        "execution": execution[OrderField.META_KEY],
                        "notional_sum": currency_total_sum,
                        "minimum_threshold": self.thresholds.minimumNotionalValue,
                    },
                    list_of_order_ids=[execution[OrderField.META_KEY]],
                    number_of_input_orders=1,
                    number_of_resulting_orders=0,
                )

                return

            self.auditor.step_audit(
                audit_key=WashTradingAuditName.APPLIED_MINIMUM_NOTIONAL_THRESHOLD,
                audit_data={
                    "execution": execution[OrderField.META_KEY],
                    "notional_sum": currency_total_sum,
                    "minimum_threshold": self.thresholds.minimumNotionalValue,
                },
                number_of_input_orders=1,
                number_of_resulting_orders=1,
            )

        volumes = group_window.group_by(OrderField.EXC_DTL_BUY_SELL_IND).agg(
            pl.sum(OrderField.PC_FD_TRD_QTY).alias("total_qty")
        )
        volumes = dict(zip(volumes[OrderField.EXC_DTL_BUY_SELL_IND], volumes["total_qty"]))  # type: ignore

        if (
            self.thresholds.minimumTradedQuantity is not None
            and volumes[BuySell.BUY] < self.thresholds.minimumTradedQuantity
            and volumes[BuySell.SELL] < self.thresholds.minimumTradedQuantity
        ):
            self.auditor.step_audit(
                audit_key=WashTradingAuditName.DROPPED_MINIMUM_TRADED_QUANTITY_THRESHOLD,
                audit_data={
                    "execution": execution[OrderField.META_KEY],
                    "buys_volume": volumes[BuySell.BUY],
                    "sells_volume": volumes[BuySell.SELL],
                    "volume_threshold": self.thresholds.minimumTradedQuantity,
                },
                list_of_order_ids=[execution[OrderField.META_KEY]],
                number_of_input_orders=1,
                number_of_resulting_orders=0,
            )
            return

        if self.thresholds.minimumTradedQuantity is not None:
            self.auditor.step_audit(
                audit_key=WashTradingAuditName.APPLIED_MINIMUM_TRADED_QUANTITY_THRESHOLD,
                audit_data={
                    "execution": execution[OrderField.META_KEY],
                    "buys_volume": volumes[BuySell.BUY],
                    "sells_volume": volumes[BuySell.SELL],
                    "volume_threshold": self.thresholds.minimumTradedQuantity,
                },
                number_of_input_orders=1,
                number_of_resulting_orders=1,
            )

        vol_pct_diff = calculate_percentage_difference(
            volumes[BuySell.BUY], volumes[BuySell.SELL], as_percentage=False
        )
        if vol_pct_diff is None or vol_pct_diff > self.thresholds.maxVolumeDifference:
            self.auditor.step_audit(
                audit_key=WashTradingAuditName.DROPPED_MAX_VOLUME_DIFFERENCE_THRESHOLD,
                audit_data={
                    "execution": execution[OrderField.META_KEY],
                    "volume_difference": vol_pct_diff,
                    "volume_threshold": self.thresholds.maxVolumeDifference,
                },
                list_of_order_ids=[execution[OrderField.META_KEY]],
                number_of_input_orders=1,
                number_of_resulting_orders=0,
            )
            return

        self.auditor.step_audit(
            audit_key=WashTradingAuditName.APPLIED_MAX_VOLUME_DIFFERENCE_THRESHOLD,
            audit_data={
                "execution": execution[OrderField.META_KEY],
                "volume_difference": vol_pct_diff,
                "volume_threshold": self.thresholds.maxVolumeDifference,
            },
            number_of_input_orders=1,
            number_of_resulting_orders=1,
        )

        buys_price_avg = (
            group_window.filter(pl.col(OrderField.EXC_DTL_BUY_SELL_IND) == BuySell.BUY)
            .select(pl.col(OrderField.PC_FD_PRICE).mean().round(5))
            .item()
        )

        sells_price_avg = (
            group_window.filter(pl.col(OrderField.EXC_DTL_BUY_SELL_IND) == "SELL")
            .select(pl.col(OrderField.PC_FD_PRICE).mean().round(5))
            .item()
        )

        implied_pl = self.calculate_implied_pl(data=group_window)

        result = {
            OrderField.META_KEY: execution[OrderField.META_KEY],
            OrderField.TS_TRADING_DATE_TIME: execution[OrderField.TS_TRADING_DATE_TIME],
            DFColumns.INSTRUMENT_ID: instrument,
            DFColumns.VOLUME_PERCENTAGE_DIFFERENCE: vol_pct_diff,
            DFColumns.EXECUTIONS_KEYS: sorted(group_window[OrderField.META_KEY].unique().to_list()),
            DFColumns.TIME_DIFFERENCE: (
                group_window[OrderField.TS_TRADING_DATE_TIME].max()  # type: ignore
                - group_window[OrderField.TS_TRADING_DATE_TIME].min()
            ).total_seconds(),
            DFColumns.MAX_PRICE_DIFFERENCE: abs(
                execution[OrderField.PC_FD_PRICE] - group_window[OrderField.PC_FD_PRICE].max()
            ),
            DFColumns.EXECUTED_BUY_QUANTITY: volumes[BuySell.BUY],
            DFColumns.EXECUTED_SELL_QUANTITY: volumes[BuySell.SELL],
            DFColumns.PRICE_DIFFERENCE: buys_price_avg - sells_price_avg,
            DFColumns.IMPLIED_PL: implied_pl,
        }
        return result

    def filter_day_traded_notional(self, data: pl.DataFrame, ric: Optional[str]) -> pl.DataFrame:
        """Filters records with market day traded notional below or above the
        value defined in day traded notional threshold.

        :param data: group of records to be analysed
        :return: pandas dataframe with filtered records
        """

        if not isinstance(ric, str):
            return pl.DataFrame()

        earliest_timestamp = (
            data.select(pl.col(OrderField.TS_TRADING_DATE_TIME)).min().to_series()[0]
        )

        market_data: pd.DataFrame() = self.queries.get_market_data(
            ric=ric, start_timestamp=earliest_timestamp
        )

        market_data = pl.from_pandas(market_data)

        record_meta_keys = data.get_column(OrderField.META_KEY).to_list()

        if market_data.is_empty():
            self.auditor.step_audit(
                audit_key=WashTradingAuditName.MISSING_MARKET_DATA,
                audit_data={"ric": ric},
                list_of_order_ids=record_meta_keys,
                number_of_input_orders=len(data),
                number_of_resulting_orders=0,
            )
            log.info(f"No market data found for ric: {ric}")
            return pl.DataFrame()

        self.auditor.step_audit(
            audit_key=WashTradingAuditName.FETCHED_MARKET_DATA,
            audit_data={"ric": ric},
            number_of_input_orders=len(data),
            number_of_resulting_orders=len(data),
        )

        if EODStatsDataColumns.DAILY_TRADED_NOTIONAL.value not in market_data.columns:
            self.auditor.step_audit(
                audit_key=WashTradingAuditName.MISSING_DAY_TRADED_NOTIONAL_COLUMN,
                audit_data={"ric": ric},
                list_of_order_ids=record_meta_keys,
                number_of_input_orders=len(data),
                number_of_resulting_orders=0,
            )
            log.info("No Day traded notional available in market data")
            return pl.DataFrame()

        self.auditor.step_audit(
            audit_key=WashTradingAuditName.CHECKED_DAY_TRADED_NOTIONAL_COLUMN,
            audit_data={"ric": ric},
            number_of_input_orders=len(data),
            number_of_resulting_orders=len(data),
        )

        # Matching market data to each record according to day
        data = data.with_columns(
            pl.col(OrderField.TS_TRADING_DATE_TIME).dt.date().alias("trade_date")
        )
        market_data = market_data.with_columns(
            pl.col(EODStatsDataColumns.DATE.value).dt.date().alias("market_date")
        )

        data = data.join(market_data, left_on="trade_date", right_on="market_date", how="left")

        data_with_notional = data.drop_nulls(
            subset=[EODStatsDataColumns.DAILY_TRADED_NOTIONAL.value]
        )

        if data_with_notional.is_empty():
            self.auditor.step_audit(
                audit_key=WashTradingAuditName.MISSING_DAY_TRADED_NOTIONAL_VALUE,
                audit_data={"ric": ric},
                list_of_order_ids=record_meta_keys,
                number_of_input_orders=len(data),
                number_of_resulting_orders=0,
            )
            log.info("Records don't have matching market day traded notional")
            return pl.DataFrame()

        record_meta_keys = data.get_column(OrderField.META_KEY).to_list()
        output_record_meta_keys = data_with_notional.get_column(OrderField.META_KEY).to_list()
        self.auditor.step_audit(
            audit_key=WashTradingAuditName.MISSING_DAY_TRADED_NOTIONAL_VALUE,
            audit_data={"ric": ric},
            list_of_order_ids=list(set(record_meta_keys) - set(output_record_meta_keys)),
            number_of_input_orders=len(data),
            number_of_resulting_orders=len(data_with_notional),
        )

        # Applying daily traded notional value
        operator = NOTIONAL_OPERATOR_MAP.get(self.thresholds.dayTradedNotionalOperator)

        operator_mask = operator(
            data_with_notional[self._thresholds_day_traded_notional_currency_col],
            self.thresholds.dayTradedNotional,
        )

        filtered_data = data_with_notional.filter(operator_mask)

        notional_value = data_with_notional[
            self._thresholds_day_traded_notional_currency_col
        ].to_list()[0]

        record_meta_keys = data_with_notional.get_column(OrderField.META_KEY).to_list()
        output_record_meta_keys = data_with_notional.get_column(OrderField.META_KEY).to_list()

        if filtered_data.is_empty():
            self.auditor.step_audit(
                audit_key=WashTradingAuditName.DROPPED_DAY_TRADED_NOTIONAL_THRESHOLD,
                audit_data={
                    "ric": ric,
                    "notional_value": notional_value,
                    "threshold_value": self.thresholds.dayTradedNotional,
                },
                list_of_order_ids=record_meta_keys,
                number_of_input_orders=len(data_with_notional),
                number_of_resulting_orders=0,
            )

            log.info("All records were dropped after applying the daily notional value threshold")
            return pl.DataFrame()

        self.auditor.step_audit(
            audit_key=WashTradingAuditName.APPLIED_DAY_TRADED_NOTIONAL_THRESHOLD,
            audit_data={
                "ric": ric,
                "notional_value": notional_value,
                "threshold_value": self.thresholds.dayTradedNotional,
            },
            list_of_order_ids=list(set(record_meta_keys) - set(output_record_meta_keys)),
            number_of_input_orders=len(data_with_notional),
            number_of_resulting_orders=len(filtered_data),
        )

        return filtered_data

    @staticmethod
    def calculate_implied_pl(data: pl.DataFrame):
        if OrderField.BEST_EXC_DATA_TRX_VOL_NATIVE in data.columns:
            buys_volume = (
                data.filter(pl.col(OrderField.EXC_DTL_BUY_SELL_IND) == BuySell.BUY)
                .select(pl.col(OrderField.BEST_EXC_DATA_TRX_VOL_NATIVE).sum())
                .item()
            )

            sells_volume = (
                data.filter(pl.col(OrderField.EXC_DTL_BUY_SELL_IND) == BuySell.SELL)
                .select(pl.col(OrderField.BEST_EXC_DATA_TRX_VOL_NATIVE).sum())
                .item()
            )

            return sells_volume - buys_volume
        return None

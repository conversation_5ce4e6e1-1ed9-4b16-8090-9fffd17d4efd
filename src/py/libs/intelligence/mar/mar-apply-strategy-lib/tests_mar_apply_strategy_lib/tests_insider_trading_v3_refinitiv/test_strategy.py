# ruff: noqa: E501
# type: ignore
import pandas as pd
import pytest
from insider_trading_v3_refinitiv_apply_strategy.apply_strategy import ApplyStrategy
from insider_trading_v3_refinitiv_apply_strategy.events import NewsFeedEvent
from insider_trading_v3_refinitiv_apply_strategy.static import (
    EventDirection,
)
from insider_trading_v3_refinitiv_apply_strategy.utils.evaluate_thresholds import (
    calculate_and_check_pnl,
    check_direction_condition,
    check_net_trade_amount_threshold,
    check_net_trade_out_of_allowed_range,
    global_pnl_evaluation,
)
from market_abuse_algorithms.data_source.query.static import DateRangeParameters
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.data_source.static.utility import DFColumns, PNLColumns
from market_abuse_algorithms.utils.formulas import (
    calculate_final_pnl,
    close_variation_formula,
    get_event_day_close_price_using_market_data_events,
    get_market_data_currency_using_market_data_events,
)
from pathlib import Path
from tests_mar_apply_strategy_lib.tests_insider_trading_v3_refinitiv.fakers import (
    FakeMarketClient,
    FakeRefinitivNewsClient,
    fake_get_eod_stats,
)
from tests_mar_apply_strategy_lib.tests_insider_trading_v3_refinitiv.mock_data import (
    mock_alert_market_data_events,
    mock_alert_market_data_events_with_result,
    mock_alert_news_feed_events,
    mock_alert_news_feed_events_with_result,
)
from tests_mar_apply_strategy_lib.tests_insider_trading_v3_refinitiv.thresholds_use_cases import (
    THRESHOLDS_USE_CASE_6,
)
from unittest.mock import MagicMock

TEST_DATA = Path(__file__).parent.joinpath("test_data")
TEST_DATA_MARKET_EVENTS = Path(__file__).parent.joinpath("test_data/create_market_data_events")


def custom_date_parser():
    return lambda x: pd.to_datetime(x)


@pytest.fixture
def mock_context(helpers):
    thresholds = {
        "evaluationType": "Trader",
        "eventCreation": "Any",
        "marketDataEventPriceVariation": 0.95,
        "newsFeedEventNewsRelevance": "high",
        "sentiment": 0.7,
        "activityObservationPeriod": 5,
        "activityBehaviourPeriod": 2,
        "activityAllowedRangeFromProfile": 0.8,
        "activityMinimumPNL": 10000,
        "activityMinimumTradeAmount": 2000,
        "currencyFilter": "EUR",
    }

    context = helpers.get_context(thresholds=thresholds, filters={})
    return context


@pytest.fixture
def mock_context_for_event_type(
    helpers,
    mock_apply_strategy_kwargs,
):
    thresholds = {
        "evaluationType": "Trader",
        "eventCreation": "Any",
        "marketDataEventPriceVariation": 0.95,
        "newsFeedEventNewsRelevance": "high",
        "sentiment": 0.7,
        "activityObservationPeriod": 1,
        "activityBehaviourPeriod": 2,
        "activityAllowedRangeFromProfile": 0.95,
        "activityMinimumPNL": 1000,
        "activityMinimumTradeAmount": 10,
        "currencyFilter": "USD",
        "minimumPercentagePriceVariation": 0.05,
    }

    filters = {
        "bool": {
            "must": {
                "script": {
                    "script": {
                        "lang": "painless",
                        "params": {"end": 1677196800000, "start": 1675209600000},
                        "inline": "def model = doc['&model'].value; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'].value : doc['timestamps.orderSubmitted'].value; rawDt >= params.start && rawDt <= params.end",
                    }
                }
            }
        }
    }

    import insider_trading_v3_refinitiv_apply_strategy.query
    import market_abuse_algorithms.data_source.repository.market_data.utils

    market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
    market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = (
        fake_get_eod_stats()
    )
    insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
    insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
        FakeMarketClient()
    )

    insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
    insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
        FakeRefinitivNewsClient(relevance="high")
    )

    mock_apply_strategy_kwargs["context"] = helpers.get_context(
        thresholds=thresholds, filters=filters
    )
    strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

    data = pd.read_csv(TEST_DATA.joinpath("test_event_type_any.csv"), index_col=0)

    segment_amount_list = [63861989.81646368, 51069048.97663352]
    observation_period_amount = -557105.**********
    behaviour_confidence_interval = check_net_trade_amount_threshold(
        segment_amount_list=segment_amount_list,
        observation_period_amount=observation_period_amount,
        activity_allowed_range_from_profile_th=strategy.thresholds.activityAllowedRangeFromProfile,
        list_of_order_ids=data[OrderField.META_KEY].tolist(),
        list_of_instruments=data[OrderField.INST_ID_CODE].unique().tolist(),
    )

    return strategy, data, behaviour_confidence_interval


@pytest.fixture
def mock_context_test_case(helpers):
    filters = {
        "bool": {
            "must": {
                "script": {
                    "script": {
                        "lang": "painless",
                        "params": {"end": 1655423999000, "start": 1655337600000},
                        "inline": "def model = doc['&model'].value; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'].value : doc['timestamps.orderSubmitted'].value; rawDt >= params.start && rawDt <= params.end",
                    }
                },
                "terms": {
                    "sourceKey": [
                        "s3://benjamin.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/Insider_Trading_V3_Split_UC6v2_CADEUR.csv"
                    ]
                },
            }
        }
    }

    context = helpers.get_context(thresholds=THRESHOLDS_USE_CASE_6, filters=filters)
    return context


@pytest.fixture
def mock_empty_filters():
    return {}


@pytest.fixture
def mock_market_data_alerts(monkeypatch, *args, **kwargs):
    mock_alert_market_data_events(monkeypatch)


@pytest.fixture
def mock_news_feed_alerts(monkeypatch, *args, **kwargs):
    mock_alert_news_feed_events(monkeypatch)


@pytest.fixture
def mock_market_data_alerts_with_result(monkeypatch, *args, **kwargs):
    mock_alert_market_data_events_with_result(monkeypatch)


@pytest.fixture
def mock_news_feed_alerts_with_result(monkeypatch, *args, **kwargs):
    mock_alert_news_feed_events_with_result(monkeypatch)


class TestInsiderTradingV3RefinitivStrategy:
    """Test Insider trading V3 strategy."""

    def test_close_variation_formula(
        self,
        mock_context,
        mock_empty_filters,
    ):
        """test to verify close_variation formula."""
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        close_price = pd.Series([2, 1, 12, 2, 4, 6])
        close_price_minus_1 = pd.Series([1, 2, 2, 4, 2, 1])
        close_variation = close_variation_formula(
            close_price=close_price, close_price_minus_1=close_price_minus_1
        )
        assert close_variation.equals(pd.Series([1.0, -0.5, 5.0, -0.5, 1.0, 5.0]))

    def test_no_events_created(
        self,
        mock_context_test_case,
        mock_apply_strategy_kwargs,
    ):
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        instrument_ric = ("CA4702731031", "XPTO")
        event_day = pd.Timestamp("2022-06-15")
        mock_apply_strategy_kwargs["context"] = mock_context_test_case
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.DataFrame()
        insider_strategy.queries.fetch_refinitiv_news = MagicMock()
        insider_strategy.queries.fetch_refinitiv_news.return_value = (
            pd.DataFrame(),
            None,
            None,
        )

        instruments = insider_strategy.create_events_and_get_respective_instruments(
            instrument_ric=instrument_ric,
            event_day=event_day,
            order_states_data=pd.DataFrame(),
        )
        assert not instruments

    def test_currency_in_alert(
        self,
        mock_context,
        mock_empty_filters,
        mock_market_data_alerts,
        mock_news_feed_alerts,
        mock_apply_strategy_kwargs,
    ):
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        data = pd.read_csv(TEST_DATA.joinpath("test_currency_in_alert.csv"), index_col=0)
        mock_context.filters = {
            "bool": {
                "must": [
                    {
                        "script": {
                            "script": {
                                "lang": "painless",
                                "params": {
                                    "end": 1666137600000,
                                    "start": 1665964800000,
                                },
                                "inline": "def model = doc['&model'].value; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'].value : doc['timestamps.orderSubmitted'].value; rawDt >= params.start && rawDt <= params.end",
                            }
                        }
                    }
                ]
            }
        }
        mock_context.thresholds = {
            "evaluationType": "Trader",
            "eventCreation": "Any",
            "marketDataEventPriceVariation": 0.8,
            "newsFeedEventNewsRelevance": "low",
            "sentiment": 0.7,
            "activityObservationPeriod": 2,
            "activityBehaviourPeriod": 2,
            "activityAllowedRangeFromProfile": 0.8,
            "activityMinimumPNL": 0,
            "activityMinimumTradeAmount": 0,
            "currencyFilter": "USD",
        }
        mock_apply_strategy_kwargs["context"] = mock_context
        strategy = ApplyStrategy(
            **mock_apply_strategy_kwargs,
        )

        segment_amount_list = [63861989.81646368, 51069048.97663352]
        observation_period_amount = ********.********
        behaviour_confidence_interval = check_net_trade_amount_threshold(
            segment_amount_list=segment_amount_list,
            observation_period_amount=observation_period_amount,
            activity_allowed_range_from_profile_th=strategy.thresholds.activityAllowedRangeFromProfile,
            list_of_order_ids=data[OrderField.META_KEY].tolist(),
            list_of_instruments=data[OrderField.INST_ID_CODE].unique().tolist(),
        )

        alert = strategy.create_alert(
            alert_data=data,
            event_day=pd.Timestamp("2022-10-17 00:00:00"),
            event_day_close_price=142.41,
            event_day_close_price_converted=142.41,
            market_close_currency="USD",
            instrument="US0378331005",
            behaviour_confidence_interval=behaviour_confidence_interval,
            event_type="Both",
            evaluation_id="account:neil.gooderham",
            observation_period_net_amount=********.********,
            pnl=1764617.**********,
        )
        assert alert.get("currency") == "USD"
        assert alert.get("marketCloseCurrency") == "USD"

    def test_any_event_type_both(
        self,
        mock_context_for_event_type,
        mock_market_data_alerts_with_result,
        mock_news_feed_alerts_with_result,
    ):
        strategy, data, behaviour_confidence_interval = mock_context_for_event_type
        alert = strategy.create_alert(
            alert_data=data,
            event_day=pd.Timestamp("2023-02-14 00:00:00"),
            event_day_close_price=6.312,
            event_day_close_price_converted=6.312,
            market_close_currency="EUR",
            instrument="'DE0007500001'",
            behaviour_confidence_interval=behaviour_confidence_interval,
            event_type="Any",
            evaluation_id="account:neil.gooderham",
            observation_period_net_amount=-557105.**********,
            pnl=59638.**********,
        )
        assert alert.get("eventType") == "Both"

    def test_any_event_type_news(
        self,
        mock_context_for_event_type,
        mock_market_data_alerts,
        mock_news_feed_alerts_with_result,
    ):
        strategy, data, behaviour_confidence_interval = mock_context_for_event_type
        alert = strategy.create_alert(
            alert_data=data,
            event_day=pd.Timestamp("2023-02-14 00:00:00"),
            event_day_close_price=6.312,
            event_day_close_price_converted=6.312,
            market_close_currency="EUR",
            instrument="'DE0007500001'",
            behaviour_confidence_interval=behaviour_confidence_interval,
            event_type="Any",
            evaluation_id="account:neil.gooderham",
            observation_period_net_amount=-557105.**********,
            pnl=59638.**********,
        )
        assert alert.get("eventType") == "News Feed"

    def test_any_event_type_market(
        self,
        mock_context_for_event_type,
        mock_market_data_alerts_with_result,
        mock_news_feed_alerts,
    ):
        strategy, data, behaviour_confidence_interval = mock_context_for_event_type
        alert = strategy.create_alert(
            alert_data=data,
            event_day=pd.Timestamp("2023-02-14 00:00:00"),
            event_day_close_price=6.312,
            event_day_close_price_converted=6.312,
            market_close_currency="EUR",
            instrument="'DE0007500001'",
            behaviour_confidence_interval=behaviour_confidence_interval,
            event_type="Any",
            evaluation_id="account:neil.gooderham",
            observation_period_net_amount=-557105.**********,
            pnl=59638.**********,
        )
        assert alert.get("eventType") == "Market Data"

    def test_pnl_threshold(self, mock_context, mock_empty_filters, mock_apply_strategy_kwargs):
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        observation_data = pd.read_csv(TEST_DATA.joinpath("test_pnl_threshold.csv"), index_col=0)
        mock_context.thresholds = {
            "evaluationType": "Trader",
            "eventCreation": "Any",
            "marketDataEventPriceVariation": 0.8,
            "newsFeedEventNewsRelevance": "low",
            "sentiment": 0.7,
            "activityObservationPeriod": 2,
            "activityBehaviourPeriod": 2,
            "activityAllowedRangeFromProfile": 0.8,
            "activityMinimumPNL": 0,
            "activityMinimumTradeAmount": 0,
            "currencyFilter": "USD",
            "eventDay": [pd.Timestamp("2022-11-04 00:00:00")],
        }

        market_data = (
            pd.read_csv(
                TEST_DATA.joinpath("test_pnl_threshold_mrk_data.csv"),
                index_col=0,
                parse_dates=["Date"],
                date_parser=custom_date_parser(),
            )
            .reset_index()
            .drop(columns=["index"])
        )
        mock_apply_strategy_kwargs["context"] = mock_context
        strategy = ApplyStrategy(
            **mock_apply_strategy_kwargs,
        )

        data_to_analyse = pd.read_csv(
            TEST_DATA.joinpath("test_pnl_threshold_data_to_analyse.csv"), index_col=0
        )
        strategy.queries.fetch_refinitiv_news = MagicMock()
        strategy.queries.fetch_refinitiv_news.return_value = (
            pd.DataFrame(),
            None,
            None,
        )

        strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        strategy.queries.get_market_data_by_instrument_or_ric.return_value = market_data

        instrument_ric_map = list(
            data_to_analyse[[DFColumns.INSTRUMENT, DFColumns.RIC]].apply(tuple, axis=1)
        )

        assert instrument_ric_map == [("GB00BH4HKS39", "VOD.L")]

        # Data added just to the conversion rate being ignored
        fake_data = pd.read_json(TEST_DATA.joinpath("fake_data_to_be_used.ndjson"))
        fake_data["instrument"] = "GB00BH4HKS39"
        fake_data[OrderField.TRX_DTL_PC_CCY] = "GBP"

        filtered_instruments = strategy.create_events_and_filters_instruments_with_events(
            data_to_analyse=data_to_analyse,
            event_day=pd.Timestamp("2022-11-04 00:00:00"),
            order_states_data=fake_data,
        )

        assert set(filtered_instruments) == {"GB00BH4HKS39", "GB00BH4HKS39GBPXLON"}

        close_price: tuple = get_event_day_close_price_using_market_data_events(
            instrument_id="GB00BH4HKS39",
            event_day=pd.Timestamp("2022-11-04 00:00:00"),
            market_data_events=strategy.market_data_events,
        )

        assert close_price == (1.0476, 1.0476)

        mrk_currency: str = get_market_data_currency_using_market_data_events(  # type: ignore
            instrument_id="GB00BH4HKS39",
            event_day=pd.Timestamp("2022-11-04 00:00:00"),
            market_data_events=strategy.market_data_events,
        )
        assert mrk_currency == "GBP"

        observation_data_pnl, audit_key, audit_data = calculate_and_check_pnl(
            observation_data=observation_data,
            event_day_close_price=close_price[0],
            filter_currency=strategy.thresholds.currencyFilter,
            pnl_threshold=strategy.thresholds.activityMinimumPNL,
            data_currency=mrk_currency,
        )

        assert observation_data_pnl.empty
        assert audit_key == "pnl_below_threshold"
        assert audit_data == {"pnl": -2204184.664175485, "threshold": 0}

        pnl_result, audit_key, audit_data = calculate_final_pnl(
            observation_data=observation_data,
            event_day_close_price=close_price[0],
            filter_currency=strategy.thresholds.currencyFilter,
            data_currency=mrk_currency,
        )

        pnl_value = pnl_result[PNLColumns.PNL_VALUE].unique().tolist()[0]

        assert pnl_value < 0
        assert audit_key is None
        assert audit_data is None

    def test_no_pnl_threshold_rl_1413(
        self,
    ):
        data_in_analyse = pd.read_csv(
            TEST_DATA.joinpath("test_pnl_threshold_data_to_analyse.csv"), index_col=0
        )

        data_in_analyse[OrderField.TRX_DTL_PC_CCY] = "EUR"

        data_dict, audit_key, audit_data = global_pnl_evaluation(
            data_in_analyse=data_in_analyse,
            event_day_close_price=154.4,
            event_day_close_price_converted=154.4,
            observation_period_range={},
            data_in_obs_period=data_in_analyse,
            filter_currency="EUR",
            market_data_currency="EUR",
            pnl_threshold=None,
            event_day=pd.Timestamp("2024-07-12 00:00:00"),
        )

        pnl_result: pd.DataFrame = data_dict.get("pnl")  # type: ignore

        pnl_value = pnl_result[PNLColumns.PNL_VALUE].unique().tolist()[0]

        pnl_value_original = pnl_result[PNLColumns.PNL_VALUE_ORIGINAL].unique().tolist()[0]

        assert pd.isna(pnl_value)
        assert pd.isna(pnl_value_original)
        assert audit_key is None
        assert audit_data is None

    def test_direction_condition(self):
        confidence_interval = {
            DateRangeParameters.START: -0.52,
            DateRangeParameters.END: 0.45,
        }

        assert check_direction_condition(
            direction=EventDirection.UP,
            price_variation=0.71,
            interval=confidence_interval,
            instrument="test",
        )
        assert check_direction_condition(
            direction=EventDirection.DOWN,
            price_variation=-0.61,
            interval=confidence_interval,
            instrument="test",
        )
        assert not check_direction_condition(
            direction=EventDirection.UP,
            price_variation=0.44,
            interval=confidence_interval,
            instrument="test",
        )
        assert not check_direction_condition(
            direction=EventDirection.DOWN,
            price_variation=-0.51,
            interval=confidence_interval,
            instrument="test",
        )

    @pytest.mark.parametrize(
        "behaviour_confidence_interval, observation_period_amount, expected",
        [
            (
                {
                    DateRangeParameters.START: 37779207.68893925,
                    DateRangeParameters.END: 77151831.10415795,
                },
                ********.********,
                True,
            ),
            (
                {
                    DateRangeParameters.START: 37779207.68893925,
                    DateRangeParameters.END: 77151831.10415795,
                },
                -15505654.********,
                True,
            ),
            (
                {
                    DateRangeParameters.START: 37779207.68893925,
                    DateRangeParameters.END: 77151831.10415795,
                },
                55505654.********,
                False,
            ),
            (
                {
                    DateRangeParameters.START: -77151831.10415795,
                    DateRangeParameters.END: -37779207.68893925,
                },
                25505654.********,
                True,
            ),
            (
                {
                    DateRangeParameters.START: 37779207.68893925,
                    DateRangeParameters.END: 77151831.10415795,
                },
                25505654.********,
                False,
            ),
            (
                {
                    DateRangeParameters.START: pd.NA,
                    DateRangeParameters.END: pd.NA,
                },
                25505654.********,
                True,
            ),
            (
                {
                    DateRangeParameters.START: pd.NA,
                    DateRangeParameters.END: 37779207.68893925,
                },
                25505654.********,
                True,
            ),
            (
                {
                    DateRangeParameters.START: 37779207.68893925,
                    DateRangeParameters.END: pd.NA,
                },
                25505654.********,
                True,
            ),
        ],
    )
    def test_check_net_trade_out_of_allowed_range(
        self,
        behaviour_confidence_interval,
        observation_period_amount,
        expected,
    ):
        net_trade_allowed_range = check_net_trade_out_of_allowed_range(
            behaviour_confidence_interval=behaviour_confidence_interval,
            observation_period_amount=observation_period_amount,
        )

        assert net_trade_allowed_range is expected

    def test_conversion_rate_value_using_best_ex(self, mock_context, mock_apply_strategy_kwargs):
        mock_apply_strategy_kwargs["context"] = mock_context
        strategy = ApplyStrategy(
            **mock_apply_strategy_kwargs,
        )

        group = pd.read_csv(
            TEST_DATA.joinpath("test_currency_conversion_pnl_bestex.csv"), index_col=0
        )

        conversion_rate = strategy.get_conversion_rate(
            group=group,
            market_data_currency="GBP",
            event_day=pd.Timestamp("2024-04-09 00:00:00"),
        )

        assert conversion_rate == 1.1655690891077568

    def test_conversion_rate_value_using_additional_ric(
        self, mock_context, mock_apply_strategy_kwargs
    ):
        mock_apply_strategy_kwargs["context"] = mock_context
        strategy = ApplyStrategy(
            **mock_apply_strategy_kwargs,
        )

        group = pd.read_csv(
            TEST_DATA.joinpath("test_currency_conversion_pnl_bestex.csv"), index_col=0
        )

        market_data_add_ric = pd.read_csv(
            TEST_DATA.joinpath("test_currency_conversion_pnl_bestex_mrk_data.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        strategy.queries.get_market_data_by_instrument_or_ric.return_value = market_data_add_ric

        conversion_rate = strategy.get_conversion_rate(
            group=group,
            market_data_currency="FAKE",
            event_day=pd.Timestamp("2024-04-09 00:00:00"),
        )

        assert conversion_rate == 1.1673

    def test_conversion_rate_value_using_additional_ric_no_mrk(
        self, mock_context, mock_apply_strategy_kwargs
    ):
        mock_apply_strategy_kwargs["context"] = mock_context
        strategy = ApplyStrategy(
            **mock_apply_strategy_kwargs,
        )

        group = pd.read_csv(
            TEST_DATA.joinpath("test_currency_conversion_pnl_bestex.csv"), index_col=0
        )

        market_data_add_ric = pd.DataFrame(
            columns=[
                "Close Ask Price",
                "Close Bid Price",
                "Close Price",
                "Currency",
                "Date",
                "Exchange Code",
                "High Ask Price",
                "High Bid Price",
                "High Price",
                "Low Price",
                "Low Ask Price",
                "Low Bid Price",
                "Open Ask Price",
                "Open Bid Price",
                "Open Interest",
                "Open Price",
                "#RIC",
                "Trade Volume",
                "VWAP",
                "Traded Volume 20 Day EMA",
            ]
        )

        strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        strategy.queries.get_market_data_by_instrument_or_ric.return_value = market_data_add_ric

        conversion_rate = strategy.get_conversion_rate(
            group=group,
            market_data_currency="FAKE",
            event_day=pd.Timestamp("2024-04-09 00:00:00"),
        )

        assert pd.isna(conversion_rate)

    def test_conversion_rate_value_using_bestex_miss_bestex_field(
        self, mock_context, mock_apply_strategy_kwargs
    ):
        mock_apply_strategy_kwargs["context"] = mock_context
        strategy = ApplyStrategy(
            **mock_apply_strategy_kwargs,
        )

        group = pd.read_csv(
            TEST_DATA.joinpath("test_currency_conversion_pnl_bestex.csv"), index_col=0
        )

        conversion_rate = strategy.get_conversion_rate(
            group=group,
            market_data_currency="JPY",
            event_day=pd.Timestamp("2024-04-09 00:00:00"),
        )

        assert pd.isna(conversion_rate)

    def test_conversion_rate_value_failed_market_data_fetch(
        self, mock_context, mock_apply_strategy_kwargs
    ):
        mock_apply_strategy_kwargs["context"] = mock_context
        strategy = ApplyStrategy(
            **mock_apply_strategy_kwargs,
        )
        group = pd.read_csv(
            TEST_DATA.joinpath("test_currency_conversion_pnl_bestex.csv"), index_col=0
        )

        strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.DataFrame()

        conversion_rate = strategy.get_conversion_rate(
            group=group,
            market_data_currency="DOES_NOT_EXIST",
            event_day=pd.Timestamp("2024-04-09 00:00:00"),
        )

        assert pd.isna(conversion_rate)

    def test_calculate_transaction_volume_put_option(
        self, mock_context, mock_apply_strategy_kwargs
    ):
        """Test which verifies an opposite value is calculated when the records
        are PUT options test case contains single record for a BUY of a PUT
        option:

        according to what is expected the value should be negative
        """
        mock_apply_strategy_kwargs["context"] = mock_context
        strategy = ApplyStrategy(
            **mock_apply_strategy_kwargs,
        )

        data = pd.read_csv(TEST_DATA.joinpath("mc_140_transaction_volume.csv"))

        currency = "EUR"
        expected_output = -17080.0

        output = strategy.calculate_transaction_volume(data=data, currency=currency)

        assert output == expected_output


def test_create_alert_post_event_intraday_alert(
    mock_context, mock_empty_filters, mock_apply_strategy_kwargs
):
    """Test for alert with records after news event when intra day is
    activated."""
    import insider_trading_v3_refinitiv_apply_strategy.query
    import market_abuse_algorithms.data_source.repository.market_data.utils

    market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
    market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = (
        fake_get_eod_stats()
    )
    insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
    insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
        FakeMarketClient()
    )

    insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
    insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
        FakeRefinitivNewsClient(relevance="high")
    )
    data = pd.read_csv(TEST_DATA.joinpath("intraday_create_alert_data.csv"))
    mock_context.filters = {}
    mock_context.thresholds = {
        "evaluationType": "Trader",
        "eventCreation": "News Feed",
        "newsFeedEventNewsRelevance": "high",
        "intraDaySurveillance": True,
        "activityObservationPeriod": 1,
        "activityBehaviourPeriod": 1,
        "activityAllowedRangeFromProfile": 0.8,
        "activityMinimumTradeAmount": 1,
        "currencyFilter": "EUR",
        "sentiment": 0.81,
        "eventDay": [pd.Timestamp("2024-07-17 00:00:00")],
    }
    mock_apply_strategy_kwargs["context"] = mock_context
    strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

    news_df = pd.read_csv(TEST_DATA.joinpath("news_data_intra_day.csv"))
    news_event = [
        NewsFeedEvent(
            date=pd.Timestamp("2024-07-17 00:00:00"),
            instrument="NL0010273215",
            ric="ASML.AS",
            news_feed=news_df,
            direction=EventDirection.DOWN,
        )
    ]
    strategy.news_feed_events = news_event

    alert = strategy.create_alert(
        alert_data=data,
        event_day=pd.Timestamp("2024-07-17 00:00:00"),
        event_day_close_price=870.9,
        event_day_close_price_converted=pd.NA,
        market_close_currency="EUR",
        instrument="NL0010273215",
        behaviour_confidence_interval={
            DateRangeParameters.START: pd.NA,
            DateRangeParameters.END: pd.NA,
        },
        event_type="News Feed",
        evaluation_id="account:trader1",
        observation_period_net_amount=-********.0,
        pnl=-2818000.0,
        news_feed_events=news_event,
    )

    assert alert["behaviourPeriodExecutions"] == []
    assert alert["observationPeriodExecutions"] == []
    assert alert["observationPeriodExecutionsPostEvent"] == [
        "Order:ORD|ASML|EUR|3.2:2:ORDASMLEUR32TRDASMLEUR3220240717SELL:FILL:2024-07-17T12:10:00Z:0.0:*************"
    ]

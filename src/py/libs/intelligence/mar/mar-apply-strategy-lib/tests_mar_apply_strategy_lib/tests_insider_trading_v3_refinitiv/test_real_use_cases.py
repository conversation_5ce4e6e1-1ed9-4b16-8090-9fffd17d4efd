# ruff: noqa: E501
import json
import nanoid
import pandas as pd
import polars as pl
import pytest
import pytz
import shutil
from insider_trading_v3_refinitiv_apply_strategy.alerts import (
    InsiderTradingV3RefinitivScenario,
)
from insider_trading_v3_refinitiv_apply_strategy.apply_strategy import ApplyStrategy
from insider_trading_v3_refinitiv_apply_strategy.static import (
    EventResultDict,
)
from insider_trading_v3_refinitiv_apply_strategy.utils import (
    evaluate_thresholds,
)
from insider_trading_v3_refinitiv_apply_strategy.utils.evaluate_thresholds import (
    check_net_trade_amount_threshold,
)
from market_abuse_algorithms.data_source.query.static import DateRangeParameters
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.data_source.static.utility import CommonColumns, OrderStateValueColumns
from market_abuse_algorithms.strategy.base.strategy import singleton_audit_object
from market_abuse_algorithms.utils import formulas
from market_abuse_algorithms.utils.data import filter_df_by_timestamps
from market_abuse_algorithms.utils.dates import (
    determine_behavior_periods,
    determine_observation_period,
    get_order_state_date_range,
)
from market_abuse_algorithms.utils.formulas import (
    calculate_confidence_interval,
    calculate_net_amount,
    calculate_net_trade_amount_periods,
    calculate_order_states_values,
    order_states_convert_price,
)
from market_abuse_algorithms.utils.processment import (
    filter_instruments,
    get_grouping_columns,
)
from pathlib import Path
from tests_mar_apply_strategy_lib.tests_insider_trading_v3_refinitiv.fakers import (
    FakeMarketClient,
    FakeRefinitivNewsClient,
    fake_get_eod_stats,
)
from tests_mar_apply_strategy_lib.tests_insider_trading_v3_refinitiv.thresholds_use_cases import (
    THRESHOLDS_USE_CASE_6,
    THRESHOLDS_USE_CASE_11,
    THRESHOLDS_USE_CASE_PNL_9051,
)
from unittest.mock import MagicMock

TEST_DATA = Path(__file__).parent.joinpath("test_data")


@pytest.fixture
def mock_context_test_case_6(helpers):
    filters = {
        "bool": {
            "must": {
                "script": {
                    "script": {
                        "lang": "painless",
                        "params": {"end": 1655423999000, "start": 1655337600000},
                        "inline": "def model = doc['&model'].value; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'].value : doc['timestamps.orderSubmitted'].value; rawDt >= params.start && rawDt <= params.end",
                    }
                },
                "terms": {
                    "sourceKey": [
                        "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/Insider_Trading_V3_Split_UC6v2_CADEUR.csv"
                    ]
                },
            }
        }
    }

    context = helpers.get_context(thresholds=THRESHOLDS_USE_CASE_6, filters=filters)
    return context


@pytest.fixture
def mock_context_test_case_11(helpers):
    filters = {
        "bool": {
            "must": [
                {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.insiderTradingV3.Refinitiv.2.csv"
                        ]
                    }
                },
                {
                    "script": {
                        "script": {
                            "lang": "painless",
                            "params": {"end": 1697803200000, "start": 1697803200000},
                            "inline": "def model = doc['&model'].value; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'].value : doc['timestamps.orderSubmitted'].value; rawDt >= params.start && rawDt <= params.end",
                        }
                    }
                },
            ]
        }
    }

    context = helpers.get_context(thresholds=THRESHOLDS_USE_CASE_11, filters=filters)
    return context


@pytest.fixture
def mock_context_test_case_pnl_9051(helpers):
    filters = {
        "bool": {
            "should": [
                {
                    "bool": {
                        "filter": [
                            {"terms": {"executionDetails.orderStatus": ["NEWO"]}},
                            {
                                "range": {
                                    "timestamps.orderSubmitted": {
                                        "gte": 1712534400000,
                                        "lt": 1712966399999,
                                    }
                                }
                            },
                        ]
                    }
                },
                {
                    "bool": {
                        "filter": [
                            {
                                "range": {
                                    "timestamps.tradingDateTime": {
                                        "gte": 1712534400000,
                                        "lt": 1712966399999,
                                    }
                                }
                            }
                        ],
                        "must_not": [{"terms": {"executionDetails.orderStatus": ["NEWO"]}}],
                    }
                },
            ],
            "minimum_should_match": 1,
            "filter": [
                {
                    "terms": {
                        "sourceKey": [
                            "s3://mares8.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_blotter/TC4_ITv3_TestFile_RIC_20240411.csv"
                        ]
                    }
                },
                {"range": {"&timestamp": {"gte": "2022-07-22T15:59:34.370987Z"}}},
            ],
        }
    }

    context = helpers.get_context(thresholds=THRESHOLDS_USE_CASE_PNL_9051, filters=filters)
    return context


def custom_date_parser():
    return lambda x: pd.to_datetime(x)


class TestInsiderTradingV3RefinitivUseCases:
    """Test Insider trading V3 strategy."""

    def test_use_case_6(
        self, mock_context_test_case_6, mock_apply_strategy_kwargs, folder="use_case_6"
    ):
        """Test use case 6 Run Date: 16th of June Event Day: 15th of June
        Executions date: 15th of June.

        Expected Result: Market data UP event || 19 executions from the
        inserted file
        """
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        mock_apply_strategy_kwargs["context"] = mock_context_test_case_6
        insider_strategy = ApplyStrategy(
            **mock_apply_strategy_kwargs,
        )

        insider_strategy.get_and_check_position_record = MagicMock()
        insider_strategy.get_and_check_position_record.return_value = (
            pd.NA,
            pd.NA,
            pd.NA,
        )

        instruments_df = pl.read_csv(TEST_DATA.joinpath(f"{folder}/cases_to_analyse_case_6.csv"))
        instruments_df = instruments_df.with_columns(
            pl.col("event_days")
            .str.split(",")  # Split into a list (though only one item here)
            .list.eval(
                pl.col("").str.strptime(pl.Datetime, "%Y-%m-%d %H:%M:%S %Z")
            )  # Convert to datetime
        )
        insider_strategy.queries.get_group = MagicMock()
        insider_strategy.queries.get_group.return_value = instruments_df
        event_days_col = [
            day.replace(tzinfo=pytz.UTC) for day in instruments_df["event_days"].explode().to_list()
        ]
        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/market_data_use_case_6.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        insider_strategy.queries.get_order_states = MagicMock()
        insider_strategy.queries.get_order_states.return_value = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/order_states_df_use_case_6.csv"),
            index_col=0,
            parse_dates=["timestamps.tradingDateTime"],
            date_parser=custom_date_parser(),
        )

        expected_instrument = ["CA4702731031"]

        ric_instrument_list = ("CA4702731031", "JBR.CD")

        expected_behavior = [
            {
                "start": pd.Timestamp("2022-06-01 00:00:00", tz="UTC"),
                "end": pd.Timestamp("2022-06-07 23:59:59"),
            },
            {
                "start": pd.Timestamp("2022-05-25 00:00:00", tz="UTC"),
                "end": pd.Timestamp("2022-05-31 23:59:59"),
            },
            {
                "start": pd.Timestamp("2022-05-18 00:00:00", tz="UTC"),
                "end": pd.Timestamp("2022-05-24 23:59:59"),
            },
        ]

        expected_order_state_range = {
            "start": pd.Timestamp("2022-05-18 00:00:00", tz="UTC"),
            "end": pd.Timestamp("2022-06-14 23:59:59"),
        }

        alerts = []

        order_states_df = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/order_states_df_use_case_6.csv"),
            index_col=0,
            parse_dates=["timestamps.tradingDateTime"],
            date_parser=custom_date_parser(),
        )

        for event_date in event_days_col:
            observation_period = determine_observation_period(
                event_day=event_date,
                observation_period=insider_strategy.thresholds.activityObservationPeriod,
            )
            assert (
                observation_period.get(DateRangeParameters.START).replace(tzinfo=None).isoformat()
                == "2022-06-08T00:00:00"
            )
            assert (
                observation_period.get(DateRangeParameters.END).isoformat() == "2022-06-14T23:59:59"
            )

            datasets = [
                pd.read_csv(
                    TEST_DATA.joinpath(f"{folder}/cases_to_analyse_case_6.csv"),
                    index_col=0,
                )
            ]

            for dataset in datasets:
                obtained_instruments = (
                    insider_strategy.create_events_and_get_respective_instruments(
                        instrument_ric=ric_instrument_list,
                        event_day=event_date,
                        order_states_data=order_states_df,
                    )
                )

                assert set(obtained_instruments) == set(expected_instrument)

                assert insider_strategy.instruments_with_news_feed_events == set()
                assert insider_strategy.instruments_with_market_data_events == set(
                    expected_instrument
                )

                filtered_instruments = filter_instruments(
                    list_of_instruments=expected_instrument, data_to_be_analysed=dataset
                )

                assert filtered_instruments == expected_instrument

                behaviour_period_segments = determine_behavior_periods(
                    date_range=observation_period,
                    observation_period=insider_strategy.thresholds.activityObservationPeriod,
                    behaviour_period=insider_strategy.thresholds.activityBehaviourPeriod,
                )

                assert len(behaviour_period_segments) == 3
                assert expected_behavior == behaviour_period_segments

                orders_state_date_range = get_order_state_date_range(
                    event_day=event_date,
                    observation_period=insider_strategy.thresholds.activityObservationPeriod,
                    behaviour_period=insider_strategy.thresholds.activityBehaviourPeriod,
                )

                assert orders_state_date_range == expected_order_state_range

                skipped_records, order_states_df_converted, _, _ = order_states_convert_price(
                    order_states=order_states_df,
                    filter_currency=insider_strategy.thresholds.currencyFilter,
                )

                order_states_w_order_st_value = calculate_order_states_values(
                    order_states_df=order_states_df_converted,
                )

                order_states_w_order_st_value = order_states_w_order_st_value.reset_index().drop(
                    columns=["index"]
                )

                order_states_w_order_st_value.loc[
                    :, OrderStateValueColumns.CONVERTED_ORDER_STATE_VALUE
                ] = (
                    order_states_w_order_st_value.loc[:, OrderStateValueColumns.ORDER_STATE_VALUE]
                    * order_states_w_order_st_value.loc[:, CommonColumns.CCY_CONVERSION_RATE]
                )

                expected_order_states_df_converted = pd.read_csv(
                    TEST_DATA.joinpath(f"{folder}/order_states_df_converted_use_case_6.csv"),
                    index_col=0,
                    parse_dates=["timestamps.tradingDateTime"],
                    date_parser=custom_date_parser(),
                )

                expected_order_states_w_order_st_value = pd.read_csv(
                    TEST_DATA.joinpath(
                        f"{folder}/order_states_df_converted_state_value_use_case_6.csv"
                    ),
                    index_col=0,
                    parse_dates=["timestamps.tradingDateTime"],
                    date_parser=custom_date_parser(),
                )

                obtained_converted_price = order_states_df_converted["convertedPrice"].apply(
                    lambda x: round(x, 9)
                )

                expected_converted_price = expected_order_states_df_converted[
                    "convertedPrice"
                ].apply(lambda x: round(x, 9))

                assert all(obtained_converted_price == expected_converted_price)
                assert all(
                    expected_order_states_w_order_st_value["orderStateValue"]
                    == order_states_w_order_st_value["orderStateValue"]
                )

                grouping_columns = get_grouping_columns(
                    evaluation_type=insider_strategy.thresholds.evaluationType,
                    data=order_states_w_order_st_value,
                )
                expected_grouping_values = ["instrument", "traderFileIdentifier"]

                assert set(grouping_columns.values()) == set(expected_grouping_values)

                groupby_groups = order_states_w_order_st_value.groupby(expected_grouping_values)

                assert groupby_groups.ngroups == 1

                for group_values, group in groupby_groups:
                    assert isinstance(group_values, tuple)

                    instrument_value, evaluation_grp_val = group_values

                    amount_dict = calculate_net_trade_amount_periods(
                        order_states_data=group,
                        observation_date_range=observation_period,
                        behaviour_period_segments=behaviour_period_segments,
                    )

                    observation_data: pd.DataFrame = filter_df_by_timestamps(
                        data=group,
                        timestamp_column=OrderField.TS_TRADING_DATE_TIME,
                        date_range=observation_period,
                    )
                    assert observation_data.shape[0] == 5
                    assert observation_data[
                        "timestamps.tradingDateTime"
                    ].min() >= orders_state_date_range.get(DateRangeParameters.START).replace(
                        tzinfo=None
                    )
                    assert observation_data[
                        "timestamps.tradingDateTime"
                    ].max() <= orders_state_date_range.get(DateRangeParameters.END)

                    observation_net_amount: float = calculate_net_amount(
                        data=observation_data,
                        column_name=OrderStateValueColumns.CONVERTED_ORDER_STATE_VALUE,
                    )

                    observation_period_amount = amount_dict.get("observationPeriodAmount")
                    segment_amount_list = amount_dict.get("segmentAmountList")

                    assert observation_net_amount == observation_period_amount
                    assert observation_period_amount == 15954.**********
                    assert segment_amount_list == [
                        4407.8805030717995,
                        4936.7148877866,
                        -8765.6195662544,
                    ]

                    assert (
                        observation_period_amount
                        > insider_strategy.thresholds.activityMinimumTradeAmount
                    )

                    behaviour_confidence_interval = calculate_confidence_interval(
                        value_series=pd.Series(segment_amount_list),
                        alpha=insider_strategy.thresholds.activityAllowedRangeFromProfile,
                    )

                    assert not (
                        behaviour_confidence_interval.get(DateRangeParameters.START)
                        <= observation_period_amount
                        <= behaviour_confidence_interval.get(DateRangeParameters.END)
                    )

                    check_net_trade_amount = check_net_trade_amount_threshold(
                        segment_amount_list=segment_amount_list,
                        observation_period_amount=observation_period_amount,
                        activity_allowed_range_from_profile_th=insider_strategy.thresholds.activityAllowedRangeFromProfile,
                        list_of_order_ids=group[OrderField.META_KEY].tolist(),
                        list_of_instruments=[instrument_value],
                    )

                    assert check_net_trade_amount == {
                        "start": -12894.110295651166,
                        "end": 13280.09417872049,
                    }

                    transaction_volume = insider_strategy.calculate_transaction_volume(
                        data=group, currency=insider_strategy.thresholds.currencyFilter
                    )

                    check_events_result, audit_key, audit_data = (
                        insider_strategy.check_events_order_states_direction(
                            transaction_volume=transaction_volume,
                            instrument=instrument_value,
                            observation_amount=observation_period_amount,
                            behaviour_series=pd.Series(segment_amount_list),
                        )
                    )

                    assert check_events_result[EventResultDict.CREATE_ALERT] is True
                    assert audit_key is None
                    assert audit_data is None
                    assert check_events_result[EventResultDict.EVENT_DIRECTION].lower() == "up"
                    assert check_events_result[EventResultDict.MESSAGE] == "Alert created"

                alert = insider_strategy._run_insider_trading_v3_refinitiv(
                    instruments_from_obs_period=dataset,
                    event_day=event_date,
                    observation_period=observation_period,
                )

                alerts.append(alert)

        dropped_orders, _, alerts_total_counter = (
            alerts[0].get("dropped_orders"),
            alerts[0].get("input_orders"),
            alerts[0].get("alerts_total_counter"),
        )

        assert alerts_total_counter == 1
        assert dropped_orders == 0
        with open(insider_strategy.result_local_file_path) as alert_output:
            alert_result = alert_output.readlines()
        alert_result = [json.loads(result) for result in alert_result]
        expected_behavior_period_executions = [
            "OrderState:UC6_ITv3_CashEQ_Behaviour_10.2:1:UC6ITV3CASHEQBEHAVIOUR102UC6ITV3CASHEQBEHAVIOUR102T2:PARF:2022-05-26T09:44:53Z:0.0:1658246631140",
            "OrderState:UC6_ITv3_CashEQ_Behaviour_11.2:1:UC6ITV3CASHEQBEHAVIOUR112UC6ITV3CASHEQBEHAVIOUR112T2:PARF:2022-05-25T09:45:53Z:0.0:1658246631140",
            "OrderState:UC6_ITv3_CashEQ_Behaviour_12.2:1:UC6ITV3CASHEQBEHAVIOUR122UC6ITV3CASHEQBEHAVIOUR122T2:PARF:2022-05-24T09:46:53Z:0.0:1658246631140",
            "OrderState:UC6_ITv3_CashEQ_Behaviour_13.2:1:UC6ITV3CASHEQBEHAVIOUR132UC6ITV3CASHEQBEHAVIOUR132T2:PARF:2022-05-23T09:47:53Z:0.0:1658246631140",
            "OrderState:UC6_ITv3_CashEQ_Behaviour_14.2:2:UC6ITV3CASHEQBEHAVIOUR142UC6ITV3CASHEQBEHAVIOUR142T2:PARF:2022-05-20T09:48:53Z:0.0:1658246631140",
            "OrderState:UC6_ITv3_CashEQ_Behaviour_15.2:2:UC6ITV3CASHEQBEHAVIOUR152UC6ITV3CASHEQBEHAVIOUR152T2:PARF:2022-05-19T09:49:53Z:0.0:1658246631140",
            "OrderState:UC6_ITv3_CashEQ_Behaviour_2.2:1:UC6ITV3CASHEQBEHAVIOUR22UC6ITV3CASHEQBEHAVIOUR22T202:PARF:2022-06-07T09:36:53Z:0.0:1658246631140",
            "OrderState:UC6_ITv3_CashEQ_Behaviour_3.2:1:UC6ITV3CASHEQBEHAVIOUR32UC6ITV3CASHEQBEHAVIOUR32T202:PARF:2022-06-06T09:37:53Z:0.0:1658246631140",
            "OrderState:UC6_ITv3_CashEQ_Behaviour_4.2:1:UC6ITV3CASHEQBEHAVIOUR42UC6ITV3CASHEQBEHAVIOUR42T202:PARF:2022-06-03T09:38:53Z:0.0:1658246631140",
            "OrderState:UC6_ITv3_CashEQ_Behaviour_5.2:1:UC6ITV3CASHEQBEHAVIOUR52UC6ITV3CASHEQBEHAVIOUR52T202:PARF:2022-06-02T09:39:53Z:0.0:1658246631140",
            "OrderState:UC6_ITv3_CashEQ_Behaviour_6.2:1:UC6ITV3CASHEQBEHAVIOUR62UC6ITV3CASHEQBEHAVIOUR62T202:PARF:2022-06-01T09:40:53Z:0.0:1658246631140",
            "OrderState:UC6_ITv3_CashEQ_Behaviour_7.2:1:UC6ITV3CASHEQBEHAVIOUR72UC6ITV3CASHEQBEHAVIOUR72T202:PARF:2022-05-31T09:41:53Z:0.0:1658246631140",
            "OrderState:UC6_ITv3_CashEQ_Behaviour_8.2:1:UC6ITV3CASHEQBEHAVIOUR82UC6ITV3CASHEQBEHAVIOUR82T202:PARF:2022-05-30T09:42:53Z:0.0:1658246631140",
            "OrderState:UC6_ITv3_CashEQ_Behaviour_9.2:1:UC6ITV3CASHEQBEHAVIOUR92UC6ITV3CASHEQBEHAVIOUR92T202:PARF:2022-05-27T09:43:53Z:0.0:1658246631140",
        ]
        expected_observation_period_executions = [
            "OrderState:UC6_ITv3_CashEQ_Behaviour_1.2:1:UC6ITV3CASHEQBEHAVIOUR12UC6ITV3CASHEQBEHAVIOUR12T202:PARF:2022-06-08T09:35:53Z:0.0:1658246631140",
            "OrderState:UC6_ITv3_CashEQ_Obs2.2:1:UC6ITV3CASHEQOBS22UC6ITV3CASHEQOBS22T20220614BUYI:PARF:2022-06-14T09:31:53Z:0.0:1658246631140",
            "OrderState:UC6_ITv3_CashEQ_Obs3.2:1:UC6ITV3CASHEQOBS32UC6ITV3CASHEQOBS32T20220613BUYI:PARF:2022-06-13T09:32:53Z:0.0:1658246631140",
            "OrderState:UC6_ITv3_CashEQ_Obs4.2:1:UC6ITV3CASHEQOBS42UC6ITV3CASHEQOBS42T20220610BUYI:PARF:2022-06-10T09:33:53Z:0.0:1658246631140",
            "OrderState:UC6_ITv3_CashEQ_Obs5.2:1:UC6ITV3CASHEQOBS52UC6ITV3CASHEQOBS52T20220609BUYI:PARF:2022-06-09T09:34:53Z:0.0:1658246631140",
        ]

        for alert in alert_result:
            scenario = InsiderTradingV3RefinitivScenario(
                result=alert, context=mock_context_test_case_6
            )

            assert set(
                scenario._scenario.get("records", {}).get("behaviourPeriodExecutions")
            ) == set(expected_behavior_period_executions)

            assert set(
                scenario._scenario.get("records", {}).get("observationPeriodExecutions")
            ) == set(expected_observation_period_executions)

            top_level_fields = scenario._scenario.get("additionalFields", {}).get("topLevel")

            assert top_level_fields
            assert top_level_fields.get("PNL") == 7987.**********
            assert top_level_fields.get("observationPeriod") == 15954.**********
            assert top_level_fields.get("evaluationID") == "account:jp01"
            assert top_level_fields.get("eventDirection") == "up"
            assert "underlyingISIN" in top_level_fields.keys()
            assert top_level_fields.get("underlyingISIN") == "CA4702731031"
            assert top_level_fields.get("eventType") == "Market Data"
        shutil.rmtree(insider_strategy.auditor._local_base_audit_path, ignore_errors=True)

    def test_use_case_7(
        self, mock_context_test_case_6, mock_apply_strategy_kwargs, folder="use_case_7"
    ):
        """No ECR rates, so all skipped records."""
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        mock_apply_strategy_kwargs["context"] = mock_context_test_case_6
        insider_strategy = ApplyStrategy(
            **mock_apply_strategy_kwargs,
        )
        instruments_df = pl.read_csv(TEST_DATA.joinpath(f"{folder}/cases_to_analyse_case_7.csv"))
        instruments_df = instruments_df.with_columns(
            pl.col("event_days")
            .str.split(",")  # Split into a list (though only one item here)
            .list.eval(
                pl.col("").str.strptime(pl.Datetime, "%Y-%m-%d %H:%M:%S %Z")
            )  # Convert to datetime
        )
        insider_strategy.queries.get_group = MagicMock()
        insider_strategy.queries.get_group.return_value = instruments_df
        event_days_col = [
            day.replace(tzinfo=pytz.UTC) for day in instruments_df["event_days"].explode().to_list()
        ]

        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/market_data_use_case_7.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        insider_strategy.queries.get_order_states = MagicMock()
        insider_strategy.queries.get_order_states.return_value = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/order_states_df_use_case_7.csv"),
            index_col=0,
            parse_dates=["timestamps.tradingDateTime"],
            date_parser=custom_date_parser(),
        )

        expected_instrument = ["CA4702731031"]

        ric_instrument_list = ("CA4702731031", "JBR.CD")

        expected_behavior = [
            {
                "start": pd.Timestamp("2022-06-01 00:00:00", tz="UTC"),
                "end": pd.Timestamp("2022-06-07 23:59:59"),
            },
            {
                "start": pd.Timestamp("2022-05-25 00:00:00", tz="UTC"),
                "end": pd.Timestamp("2022-05-31 23:59:59"),
            },
            {
                "start": pd.Timestamp("2022-05-18 00:00:00", tz="UTC"),
                "end": pd.Timestamp("2022-05-24 23:59:59"),
            },
        ]

        expected_order_state_range = {
            "start": pd.Timestamp("2022-05-18 00:00:00", tz="UTC"),
            "end": pd.Timestamp("2022-06-14 23:59:59"),
        }

        alerts = []

        order_states_df = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/order_states_df_use_case_7.csv"),
            index_col=0,
            parse_dates=["timestamps.tradingDateTime"],
            date_parser=custom_date_parser(),
        )

        for event_date in event_days_col:
            observation_period = determine_observation_period(
                event_day=event_date,
                observation_period=insider_strategy.thresholds.activityObservationPeriod,
            )
            assert (
                observation_period.get(DateRangeParameters.START).replace(tzinfo=None).isoformat()
                == "2022-06-08T00:00:00"
            )
            assert (
                observation_period.get(DateRangeParameters.END).isoformat() == "2022-06-14T23:59:59"
            )

            datasets = [
                pd.read_csv(
                    TEST_DATA.joinpath(f"{folder}/cases_to_analyse_case_7.csv"),
                    index_col=0,
                )
            ]

            for dataset in datasets:
                obtained_instruments = (
                    insider_strategy.create_events_and_get_respective_instruments(
                        instrument_ric=ric_instrument_list,
                        event_day=event_date,
                        order_states_data=order_states_df,
                    )
                )

                assert set(obtained_instruments) == set(expected_instrument)

                assert insider_strategy.instruments_with_news_feed_events == set()
                assert insider_strategy.instruments_with_market_data_events == set(
                    expected_instrument
                )

                filtered_instruments = filter_instruments(
                    list_of_instruments=expected_instrument, data_to_be_analysed=dataset
                )

                assert filtered_instruments == expected_instrument

                behaviour_period_segments = determine_behavior_periods(
                    date_range=observation_period,
                    observation_period=insider_strategy.thresholds.activityObservationPeriod,
                    behaviour_period=insider_strategy.thresholds.activityBehaviourPeriod,
                )

                assert len(behaviour_period_segments) == 3
                assert expected_behavior == behaviour_period_segments

                orders_state_date_range = get_order_state_date_range(
                    event_day=event_date,
                    observation_period=insider_strategy.thresholds.activityObservationPeriod,
                    behaviour_period=insider_strategy.thresholds.activityBehaviourPeriod,
                )

                assert orders_state_date_range == expected_order_state_range

                skipped_records, order_states_df_converted, _, _ = order_states_convert_price(
                    order_states=order_states_df,
                    filter_currency=insider_strategy.thresholds.currencyFilter,
                )

                assert order_states_df_converted.empty
                assert skipped_records.shape == order_states_df.shape

                alert = insider_strategy._run_insider_trading_v3_refinitiv(
                    instruments_from_obs_period=dataset,
                    event_day=event_date,
                    observation_period=observation_period,
                )

                alerts.append(alert)

        assert len(alerts) == 1
        assert alerts[0]["alerts_total_counter"] == 0
        assert alerts[0]["dropped_orders"] == alerts[0]["input_orders"]
        shutil.rmtree(insider_strategy.auditor._local_base_audit_path, ignore_errors=True)

    def test_use_case_11(
        self, mock_context_test_case_11, mock_apply_strategy_kwargs, folder="use_case_11"
    ):
        """1 hit on story ID: nL1N3BP27V."""
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        mock_apply_strategy_kwargs["context"] = mock_context_test_case_11
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        insider_strategy.get_and_check_position_record = MagicMock()
        insider_strategy.get_and_check_position_record.return_value = (
            pd.NA,
            pd.NA,
            pd.NA,
        )

        insider_strategy.queries.get_cases_to_analyse = MagicMock()
        insider_strategy.queries.get_cases_to_analyse.return_value = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/cases_to_analyse_case_11.csv"),
            index_col=0,
        )

        insider_strategy.queries.fetch_refinitiv_news = MagicMock()
        insider_strategy.queries.fetch_refinitiv_news.return_value = (
            pd.read_csv(
                TEST_DATA.joinpath(f"{folder}/refinitiv_news_use_case_11.csv"),
                index_col=0,
                parse_dates=["storyCreated"],
                date_parser=custom_date_parser(),
            ),
            None,
            None,
        )

        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/market_data_use_case_11.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        insider_strategy.queries.get_order_states = MagicMock()
        insider_strategy.queries.get_order_states.return_value = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/order_states_df_use_case_11.csv"),
            index_col=0,
            parse_dates=["timestamps.tradingDateTime"],
            date_parser=custom_date_parser(),
        )

        expected_instrument = ["US30303M1027"]

        instrument_ric = ("US30303M1027", "META.OQ")

        expected_behavior = [
            {
                "start": pd.Timestamp("2023-10-17 00:00:00", tz="UTC"),
                "end": pd.Timestamp("2023-10-17 23:59:59"),
            },
            {
                "start": pd.Timestamp("2023-10-16 00:00:00", tz="UTC"),
                "end": pd.Timestamp("2023-10-16 23:59:59"),
            },
            {
                "start": pd.Timestamp("2023-10-13 00:00:00", tz="UTC"),
                "end": pd.Timestamp("2023-10-13 23:59:59"),
            },
        ]

        expected_order_state_range = {
            "start": pd.Timestamp("2023-10-13 00:00:00", tz="UTC"),
            "end": pd.Timestamp("2023-10-18 23:59:59"),
        }

        alerts = []

        order_states_df = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/order_states_df_use_case_11.csv"),
            index_col=0,
            parse_dates=["timestamps.tradingDateTime"],
            date_parser=custom_date_parser(),
        )

        for event_date in mock_context_test_case_11.thresholds.eventDay:
            observation_period = determine_observation_period(
                event_day=event_date,
                observation_period=insider_strategy.thresholds.activityObservationPeriod,
            )
            assert (
                observation_period.get(DateRangeParameters.START).replace(tzinfo=None).isoformat()
                == "2023-10-18T00:00:00"
            )
            assert (
                observation_period.get(DateRangeParameters.END).isoformat() == "2023-10-18T23:59:59"
            )

            datasets = [
                pd.read_csv(
                    TEST_DATA.joinpath(f"{folder}/cases_to_analyse_case_11.csv"),
                    index_col=0,
                )
            ]

            for dataset in datasets:
                obtained_instruments = (
                    insider_strategy.create_events_and_get_respective_instruments(
                        instrument_ric=instrument_ric,
                        event_day=event_date,
                        order_states_data=pd.DataFrame(),
                    )
                )

                assert set(obtained_instruments) == set(expected_instrument)

                assert insider_strategy.instruments_with_news_feed_events == set(
                    expected_instrument
                )
                assert insider_strategy.instruments_with_market_data_events == set()

                filtered_instruments = filter_instruments(
                    list_of_instruments=expected_instrument, data_to_be_analysed=dataset
                )

                assert set(filtered_instruments) == {"US30303M1027", "US30303M1027USDARCX"}

                behaviour_period_segments = determine_behavior_periods(
                    date_range=observation_period,
                    observation_period=insider_strategy.thresholds.activityObservationPeriod,
                    behaviour_period=insider_strategy.thresholds.activityBehaviourPeriod,
                )

                assert len(behaviour_period_segments) == 3
                assert expected_behavior == behaviour_period_segments

                orders_state_date_range = get_order_state_date_range(
                    event_day=event_date,
                    observation_period=insider_strategy.thresholds.activityObservationPeriod,
                    behaviour_period=insider_strategy.thresholds.activityBehaviourPeriod,
                )

                assert orders_state_date_range == expected_order_state_range

                skipped_records, order_states_df_converted, _, _ = order_states_convert_price(
                    order_states=order_states_df,
                    filter_currency=insider_strategy.thresholds.currencyFilter,
                )

                assert skipped_records.empty
                assert order_states_df.shape == (4, 61)

                alert = insider_strategy._run_insider_trading_v3_refinitiv(
                    instruments_from_obs_period=dataset,
                    event_day=event_date,
                    observation_period=observation_period,
                )

                alerts.append(alert)

        assert alerts[0]["alerts_total_counter"] == 1
        assert alerts[0]["dropped_orders"] == 0
        shutil.rmtree(insider_strategy.auditor._local_base_audit_path, ignore_errors=True)

    def test_check_minimum_trade_amount(self, helpers, monkeypatch):
        singleton_audit_object.delete_local_audit_files()

        def fake_calculate_net_trade_amount_periods_none(
            order_states_data=None,
            observation_date_range=None,
            behaviour_period_segments=None,
        ):
            return {
                CommonColumns.OBSERVATION_PERIOD_AMOUNT: None,
                CommonColumns.SEGMENT_AMOUNT_LIST: None,
            }

        monkeypatch.setattr(
            formulas,
            "calculate_net_trade_amount_periods",
            fake_calculate_net_trade_amount_periods_none,
        )

        _, audit_key, audit_data = evaluate_thresholds.check_minimum_trade_amount(
            pd.DataFrame(), dict(), [], 0
        )

        assert audit_key == "observation_period_trade_amount"

        def fake_calculate_net_trade_amount_periods_val(
            order_states_data=None,
            observation_date_range=None,
            behaviour_period_segments=None,
        ):
            return {
                CommonColumns.OBSERVATION_PERIOD_AMOUNT: 1,
                CommonColumns.SEGMENT_AMOUNT_LIST: [0],
            }

        monkeypatch.setattr(
            formulas,
            "calculate_net_trade_amount_periods",
            fake_calculate_net_trade_amount_periods_val,
        )
        _, audit_key, audit_data = evaluate_thresholds.check_minimum_trade_amount(
            pd.DataFrame(),
            dict(),
            [],
            2,
        )
        assert audit_key == "observation_period_trade_below_threshold"

        def fake_calculate_net_trade_amount_periods_val1(
            order_states_data=None,
            observation_date_range=None,
            behaviour_period_segments=None,
        ):
            return {
                CommonColumns.OBSERVATION_PERIOD_AMOUNT: 1,
                CommonColumns.SEGMENT_AMOUNT_LIST: [],
            }

        monkeypatch.setattr(
            formulas,
            "calculate_net_trade_amount_periods",
            fake_calculate_net_trade_amount_periods_val1,
        )
        _, audit_key, audit_data = evaluate_thresholds.check_minimum_trade_amount(
            pd.DataFrame(),
            dict(),
            [0],
            0,
        )

        assert audit_key == "behaviour_data_missing"

    def test_order_states_convert_price(self, helpers, folder="use_case_1"):
        singleton_audit_object.delete_local_audit_files()
        _, _, audit_key, audit_data = formulas.order_states_convert_price(
            pd.DataFrame(), "USD", nanoid.generate()
        )
        assert audit_key == "currency_field_missing"

        order_states = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/order_states_df_use_case_1.csv"),
            index_col=0,
            parse_dates=["timestamps.tradingDateTime"],
            date_parser=custom_date_parser(),
        )
        order_states[OrderField.BEST_EXC_DATA_TRX_VOL_NATIVE_CURRENCY] = "USD"
        _, _, audit_key, audit_data = formulas.order_states_convert_price(
            order_states, "USD", nanoid.generate()
        )
        assert audit_key == "price_currency_misaligned"

        singleton_audit_object.delete_local_audit_files()

    def test_calculate_final_pnl(self, helpers):
        singleton_audit_object.delete_local_audit_files()
        _, audit_key, audit_data = formulas.calculate_final_pnl(
            pd.DataFrame(),
            0,
            "test",
            data_currency="USD",
        )
        assert audit_key == "pnl_calculation_column_missing"
        singleton_audit_object.delete_local_audit_files()

    def test_create_market_data_event_no_ric(
        self, helpers, mock_context_test_case_6, mock_apply_strategy_kwargs
    ):
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        singleton_audit_object.delete_local_audit_files()
        mock_apply_strategy_kwargs["context"] = mock_context_test_case_6
        insider_strategy = ApplyStrategy(
            **mock_apply_strategy_kwargs,
        )
        insider_strategy.create_market_data_events(
            ("ISIN1", None),
            pd.Timestamp.now(),
            order_states_data=pd.DataFrame(),
        )
        counter = 0
        for audit_key, step_audit_file in insider_strategy.auditor._audits_key_files.items():
            if step_audit_file.exists():
                counter += 1
        # If RIC is none, it should be audited in create group directly
        assert counter == 0

        shutil.rmtree(insider_strategy.auditor._local_base_audit_path, ignore_errors=True)

    def test__run_insider_trading_v3_audit(
        self, mock_context_test_case_6, mock_apply_strategy_kwargs, folder="use_case_6"
    ):
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )

        singleton_audit_object.delete_local_audit_files()
        mock_apply_strategy_kwargs["context"] = mock_context_test_case_6
        insider_strategy = ApplyStrategy(
            **mock_apply_strategy_kwargs,
        )

        insider_strategy.queries.get_cases_to_analyse = MagicMock()
        insider_strategy.queries.get_cases_to_analyse.return_value = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/cases_to_analyse_case_6.csv"),
            index_col=0,
        )

        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/market_data_use_case_6.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        insider_strategy.queries.get_order_states = MagicMock()
        insider_strategy.queries.get_order_states.return_value = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/order_states_df_use_case_6.csv"),
            index_col=0,
            parse_dates=["timestamps.tradingDateTime"],
            date_parser=custom_date_parser(),
        ).drop(OrderField.EXC_DTL_BUY_SELL_IND, axis=1)

        instrument_ric = ("CA4702731031", "JBR.CD")

        order_states_df = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/order_states_df_use_case_6.csv"),
            index_col=0,
            parse_dates=["timestamps.tradingDateTime"],
            date_parser=custom_date_parser(),
        )

        alerts = []
        for event_date in mock_context_test_case_6.thresholds.eventDay:
            observation_period = determine_observation_period(
                event_day=event_date,
                observation_period=insider_strategy.thresholds.activityObservationPeriod,
            )

            datasets = [
                pd.read_csv(
                    TEST_DATA.joinpath(f"{folder}/cases_to_analyse_case_6.csv"),
                    index_col=0,
                )
            ]

            for dataset in datasets:
                insider_strategy.create_events_and_get_respective_instruments(
                    instrument_ric=instrument_ric,
                    event_day=event_date,
                    order_states_data=order_states_df,
                )

                alert = insider_strategy._run_insider_trading_v3_refinitiv(
                    instruments_from_obs_period=dataset,
                    event_day=event_date,
                    observation_period=observation_period,
                )

                alerts.extend(alert)
            counter = 0
            for audit_key, step_audit_file in insider_strategy.auditor._audits_key_files.items():
                if step_audit_file.exists():
                    counter += 1
            # Assert 18 step audits were written
            assert counter == 4

        shutil.rmtree(insider_strategy.auditor._local_base_audit_path, ignore_errors=True)

    def test__run_insider_trading_pnl_9051(
        self,
        mock_context_test_case_pnl_9051,
        mock_apply_strategy_kwargs,
        folder="use_case_9051",
    ):
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        mock_apply_strategy_kwargs["context"] = mock_context_test_case_pnl_9051
        insider_strategy = ApplyStrategy(
            **mock_apply_strategy_kwargs,
        )

        insider_strategy.queries.get_cases_to_analyse = MagicMock()
        insider_strategy.queries.get_cases_to_analyse.return_value = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/cases_to_analyse_case_9051.csv"),
            index_col=0,
        )

        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/market_data_use_case_9051.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        insider_strategy.queries.get_order_states = MagicMock()
        insider_strategy.queries.get_order_states.return_value = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/order_states_df_use_case_9051.csv"),
            index_col=0,
            parse_dates=["timestamps.tradingDateTime"],
            date_parser=custom_date_parser(),
        )

        ric_instrument = ("GB00BH4HKS39", "VOD.L")

        order_states_df = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/order_states_df_use_case_9051.csv"),
            index_col=0,
            parse_dates=["timestamps.tradingDateTime"],
            date_parser=custom_date_parser(),
        )

        for event_date in mock_context_test_case_pnl_9051.thresholds.eventDay:
            observation_period = determine_observation_period(
                event_day=event_date,
                observation_period=insider_strategy.thresholds.activityObservationPeriod,
            )

            dataset = pd.read_csv(
                TEST_DATA.joinpath(f"{folder}/cases_to_analyse_case_9051.csv"),
                index_col=0,
            )

            insider_strategy.create_events_and_get_respective_instruments(
                instrument_ric=ric_instrument,
                event_day=event_date,
                order_states_data=order_states_df,
            )

            alert = insider_strategy._run_insider_trading_v3_refinitiv(
                instruments_from_obs_period=dataset,
                event_day=event_date,
                observation_period=observation_period,
            )
        assert alert["alerts_total_counter"] == 1
        result = pd.read_json(insider_strategy.result_local_file_path, lines=True).iloc[0]
        assert result["PNL"].__round__(2) == 63.42
        assert result["originalPNL"].__round__(2) == 58.60
        assert result["marketCloseCurrency"] == "GBP"
        assert result["observationPeriod"].__round__(3) == -487.035
        assert result["orderStateValue"].__round__(3) == -487.035
        assert result["originalOrderStateValue"].__round__(3) == -450.00
        shutil.rmtree(insider_strategy.auditor._local_base_audit_path, ignore_errors=True)

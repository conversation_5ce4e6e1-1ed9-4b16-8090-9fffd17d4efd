# ruff: noqa: E501
# type: ignore
import datetime
import pandas as pd
import pytest
import pytz
from insider_trading_v3_refinitiv_apply_strategy import alerts
from insider_trading_v3_refinitiv_apply_strategy.apply_strategy import ApplyStrategy
from insider_trading_v3_refinitiv_apply_strategy.events import MarketDataEvent, NewsFeedEvent
from insider_trading_v3_refinitiv_apply_strategy.static import (
    EventDayDetailsEnum,
    EventDirection,
)
from insider_trading_v3_refinitiv_apply_strategy.utils import (
    evaluate_thresholds,
)
from insider_trading_v3_refinitiv_apply_strategy.utils.evaluate_thresholds import (
    check_confidence_interval,
    check_event_daily_variation,
)
from market_abuse_algorithms.data_source.query.static import DateRangeParameters
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.utils import dates
from market_abuse_algorithms.utils.dates import (
    determine_behavior_periods,
    determine_observation_period,
)
from market_abuse_algorithms.utils.filters import discard_invalid_market_data
from market_abuse_algorithms.utils.formulas import (
    calc_close_price_variation,
    calc_price_variation_for_11_days,
)
from market_abuse_algorithms.utils.processment import (
    get_close_price_to_analyse,
    get_market_data_for_day,
)
from pandas.tseries.offsets import BDay
from pathlib import Path
from se_market_data_utils.schema.parquet import EoDStatsColumns
from tests_mar_apply_strategy_lib.tests_insider_trading_v3_refinitiv.fakers import (
    FakeMarketClient,
    FakeRefinitivNewsClient,
    fake_get_eod_stats,
)
from tests_mar_apply_strategy_lib.tests_insider_trading_v3_refinitiv.mock_data import (
    mock_evaluate_thresholds,
)
from tests_mar_apply_strategy_lib.tests_insider_trading_v3_refinitiv.thresholds_use_cases import (
    THRESHOLDS_USE_CASE_6,
    THRESHOLDS_USE_CASE_11,
)
from unittest.mock import MagicMock

TEST_DATA = Path(__file__).parent.joinpath("test_data")
TEST_DATA_MARKET_EVENTS = Path(__file__).parent.joinpath("test_data/create_market_data_events")


def custom_date_parser():
    return lambda x: pd.to_datetime(x)


@pytest.fixture
def mock_context(helpers):
    thresholds = {
        "evaluationType": "Trader",
        "eventCreation": "Any",
        "marketDataEventPriceVariation": 0.95,
        "newsFeedEventNewsRelevance": "high",
        "sentiment": 0.7,
        "activityObservationPeriod": 5,
        "activityBehaviourPeriod": 2,
        "activityAllowedRangeFromProfile": 0.8,
        "activityMinimumPNL": 10000,
        "activityMinimumTradeAmount": 2000,
        "currencyFilter": "EUR",
    }

    context = helpers.get_context(thresholds=thresholds, filters={})
    return context


@pytest.fixture
def mock_context_with_daily_volume_variation(helpers):
    thresholds = {
        "evaluationType": "Trader",
        "eventCreation": "Any",
        "marketDataEventPriceVariation": 0.95,
        "marketDataEventDailyVolumeVariation": 0.99,
        "newsFeedEventNewsRelevance": "high",
        "sentiment": 0.7,
        "activityObservationPeriod": 5,
        "activityBehaviourPeriod": 2,
        "activityAllowedRangeFromProfile": 0.8,
        "activityMinimumPNL": 10000,
        "activityMinimumTradeAmount": 2000,
        "currencyFilter": "EUR",
    }

    context = helpers.get_context(thresholds=thresholds, filters={})
    return context


@pytest.fixture
def mock_context_test_case(helpers):
    filters = {
        "bool": {
            "must": {
                "script": {
                    "script": {
                        "lang": "painless",
                        "params": {"end": 1655423999000, "start": 1655337600000},
                        "inline": "def model = doc['&model'].value; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'].value : doc['timestamps.orderSubmitted'].value; rawDt >= params.start && rawDt <= params.end",
                    }
                },
                "terms": {
                    "sourceKey": [
                        "s3://benjamin.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/Insider_Trading_V3_Split_UC6v2_CADEUR.csv"
                    ]
                },
            }
        }
    }

    context = helpers.get_context(thresholds=THRESHOLDS_USE_CASE_6, filters=filters)
    return context


@pytest.fixture
def mock_context_test_case_11(helpers):
    filters = {
        "bool": {
            "must": [
                {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.insiderTradingV3.Refinitiv.2.csv"
                        ]
                    }
                },
                {
                    "script": {
                        "script": {
                            "lang": "painless",
                            "params": {"end": 1697803200000, "start": 1697803200000},
                            "inline": "def model = doc['&model'].value; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'].value : doc['timestamps.orderSubmitted'].value; rawDt >= params.start && rawDt <= params.end",
                        }
                    }
                },
            ]
        }
    }

    context = helpers.get_context(thresholds=THRESHOLDS_USE_CASE_11, filters=filters)
    return context


@pytest.fixture
def mock_empty_filters():
    return {}


@pytest.fixture
def mock_thresholds_evaluation(monkeypatch, *args, **kwargs):
    mock_evaluate_thresholds(monkeypatch)


class TestNewsFeedEventsRefinitiv:
    def test_create_news_events_uc11(
        self, mock_context_test_case_11, mock_apply_strategy_kwargs, folder="use_case_11"
    ):
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()

        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        # ToDo: move this to fixture
        mock_apply_strategy_kwargs["context"] = mock_context_test_case_11
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        insider_strategy.queries.fetch_refinitiv_news = MagicMock()
        insider_strategy.queries.fetch_refinitiv_news.return_value = (
            pd.read_csv(
                TEST_DATA.joinpath(f"{folder}/refinitiv_news_use_case_11.csv"),
                index_col=0,
                parse_dates=["storyCreated"],
                date_parser=custom_date_parser(),
            ),
            None,
            None,
        )

        instrument_ric = ("US30303M1027", "META.OQ")
        event_day = pd.Timestamp("2023-10-19")

        insider_strategy.create_news_feeds_events(
            instrument_ric=instrument_ric,
            event_day=event_day,
            order_states_data=pd.DataFrame(),
        )

        news_events = insider_strategy.news_feed_events

        assert len(news_events) == 1
        assert news_events[0].get_direction() == EventDirection.UP


class TestCreateMarketDataEvensRefinitiv:
    def test_create_market_data_event_uc6(
        self, mock_context_test_case, mock_apply_strategy_kwargs, folder="use_case_6"
    ):
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        instrument_ric = ("CA4702731031", "JBR.CD")
        event_day = pd.Timestamp("2022-06-15")
        mock_apply_strategy_kwargs["context"] = mock_context_test_case
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)
        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.read_csv(
            TEST_DATA.joinpath(f"{folder}/market_data_use_case_6.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        market_data = insider_strategy.queries.get_market_data_by_instrument_or_ric(
            instrument="CA4702731031"
        )

        market_data = discard_invalid_market_data(market_data=market_data)

        event_day_df = get_market_data_for_day(market_data=market_data, day_of_event=event_day)

        event_day_minus_1_df = get_market_data_for_day(
            market_data=market_data, day_of_event=event_day - BDay(1)
        )

        assert not event_day_df.empty
        assert not event_day_minus_1_df.empty

        close_price_event_day: float = get_close_price_to_analyse(data_to_fetch_from=event_day_df)
        close_price_event_day_minus_1: float = get_close_price_to_analyse(
            data_to_fetch_from=event_day_minus_1_df
        )

        close_price_variation = calc_close_price_variation(
            close_price_event_day, close_price_event_day_minus_1
        )

        assert pd.notna(close_price_variation)
        assert close_price_variation == 0.7142857142857141

        assert insider_strategy.thresholds.marketDataEventDailyVolumeVariation is not None

        check_vol_variation = check_event_daily_variation(
            market_data_day=event_day_df,
            market_data_day_minus_one=event_day_minus_1_df,
            market_data_event_daily_vol_variation_th=insider_strategy.thresholds.marketDataEventDailyVolumeVariation,
            orders_data_per_instrument=[],
        )
        assert check_vol_variation is True

        index_event_day: int = event_day_df.index.values[0]

        market_data_12_days_df: pd.DataFrame = market_data.iloc[
            index_event_day - 11 : index_event_day + 1, :
        ]

        price_variations_11_days = calc_price_variation_for_11_days(
            market_data=market_data_12_days_df
        )
        expected_price_variation = [
            0.0,
            0.0,
            0.0,
            0.0,
            -0.055555555555555455,
            -0.23529411764705885,
            -0.07692307692307698,
            0.0,
            -0.4166666666666666,
            0.0,
        ]

        assert set(price_variations_11_days.tolist()) == set(expected_price_variation)

        confidence_interval = check_confidence_interval(
            price_variations=price_variations_11_days,
            close_price_variation=close_price_variation,
            price_variation_mkt_data=insider_strategy.thresholds.marketDataEventPriceVariation,
        )

        assert confidence_interval == {
            DateRangeParameters.START: -0.17860625406388703,
            DateRangeParameters.END: 0.02171837070541545,
        }

        assert round(confidence_interval[DateRangeParameters.START], 7) == -0.1786063
        assert round(confidence_interval[DateRangeParameters.END], 7) == 0.0217184

        # Data added just to the conversion rate being ignored
        market_currency = market_data_12_days_df.loc[:, EoDStatsColumns.CURRENCY].unique()[0]

        # Data added just to the conversion rate being ignored
        fake_data = pd.read_json(TEST_DATA.joinpath("fake_data_to_be_used.ndjson"))
        fake_data[OrderField.TRX_DTL_PC_CCY] = market_currency
        fake_data["instrument"] = "CA4702731031"

        insider_strategy.create_market_data_events(
            instrument_ric=instrument_ric,
            event_day=event_day,
            order_states_data=fake_data,
        )

        assert len(insider_strategy.market_data_events) == 1
        assert insider_strategy.market_data_events[0].get_direction() == EventDirection.UP
        assert insider_strategy.market_data_events[0].get_close_price() == close_price_event_day
        assert insider_strategy.market_data_events[0].get_ric() == "JBR.CD"
        assert insider_strategy.market_data_events[0].get_price_variation() == close_price_variation

        assert insider_strategy.market_data_events[0].get_price_range() == confidence_interval

    def test_create_market_data_missing_event_day_minus_1(
        self, mock_context_test_case, mock_apply_strategy_kwargs
    ):
        """Missing market data for event_day and fallback on event_day Return
        market_data_event."""
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        instrument_ric = ("CA4702731031", "JBR.CD")
        event_day = pd.Timestamp("2022-06-15", tzinfo=pytz.UTC)
        mock_apply_strategy_kwargs["context"] = mock_context_test_case
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)
        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.read_csv(
            TEST_DATA_MARKET_EVENTS.joinpath("market_data_for_5bd_logic.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        market_data = insider_strategy.queries.get_market_data_by_instrument_or_ric(
            instrument="CA4702731031"
        )

        # Data added just to the conversion rate being ignored
        market_currency = market_data.loc[:, EoDStatsColumns.CURRENCY].unique()[0]

        # Data added just to the conversion rate being ignored
        fake_data = pd.read_json(TEST_DATA.joinpath("fake_data_to_be_used.ndjson"))
        fake_data[OrderField.TRX_DTL_PC_CCY] = market_currency
        fake_data["instrument"] = "CA4702731031"

        insider_strategy.create_market_data_events(
            instrument_ric=instrument_ric,
            event_day=event_day,
            order_states_data=fake_data,
        )

        assert len(insider_strategy.market_data_events) == 1

    def test_create_market_data_missing_event_day(
        self, mock_context_test_case, mock_apply_strategy_kwargs
    ):
        """Missing market data for event_day and fallback on event_day Return
        market_data_event."""
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        instrument_ric = ("CA4702731031", "JBR.CD")
        event_day = pd.Timestamp("2022-06-14", tzinfo=pytz.UTC)
        mock_apply_strategy_kwargs["context"] = mock_context_test_case
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.read_csv(
            TEST_DATA_MARKET_EVENTS.joinpath("market_data_for_5bd_logic.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        market_data = insider_strategy.queries.get_market_data_by_instrument_or_ric(
            instrument="CA4702731031"
        )

        # Data added just to the conversion rate being ignored
        market_currency = market_data.loc[:, EoDStatsColumns.CURRENCY].unique()[0]

        # Data added just to the conversion rate being ignored
        fake_data = pd.read_json(TEST_DATA.joinpath("fake_data_to_be_used.ndjson"))
        fake_data[OrderField.TRX_DTL_PC_CCY] = market_currency
        fake_data["instrument"] = "CA4702731031"

        insider_strategy.create_market_data_events(
            instrument_ric=instrument_ric,
            event_day=event_day,
            order_states_data=fake_data,
        )

        assert len(insider_strategy.market_data_events) == 0

    def test_create_market_data_missing_both(
        self, mock_context_test_case, mock_apply_strategy_kwargs
    ):
        """Missing market data for both and fallback on event_day and
        event_day_minus_1 Return market_data_event."""
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        instrument_ric = ("CA4702731031", "JBR.CD")
        event_day = pd.Timestamp("2022-03-07", tzinfo=pytz.UTC)
        mock_apply_strategy_kwargs["context"] = mock_context_test_case
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.read_csv(
            TEST_DATA_MARKET_EVENTS.joinpath("market_data_for_5bd_logic.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        market_data = insider_strategy.queries.get_market_data_by_instrument_or_ric(
            instrument="CA4702731031"
        )

        # Data added just to the conversion rate being ignored
        market_currency = market_data.loc[:, EoDStatsColumns.CURRENCY].unique()[0]

        # Data added just to the conversion rate being ignored
        fake_data = pd.read_json(TEST_DATA.joinpath("fake_data_to_be_used.ndjson"))
        fake_data[OrderField.TRX_DTL_PC_CCY] = market_currency
        fake_data["instrument"] = "CA4702731031"

        insider_strategy.create_market_data_events(
            instrument_ric=instrument_ric,
            event_day=event_day,
            order_states_data=fake_data,
        )

        assert len(insider_strategy.market_data_events) == 0

    def test_create_market_data_events_more_than_5bdays_difference(
        self, mock_context, mock_empty_filters, mock_apply_strategy_kwargs
    ):
        """test create_market_data_events gets market data but fails because
        more than 5bdays in the remaining 12 days market data.

        :param mock_context: mock context with sample thresholds and filters
        :param mock_empty_filters: empty dictionary of filters
        """
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        mock_context.filters = mock_empty_filters
        mock_apply_strategy_kwargs["context"] = mock_context
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        market_data = pd.read_csv(
            TEST_DATA_MARKET_EVENTS.joinpath("market_data_for_5bd_logic.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        day_mask = market_data.loc[:, "Date"] == pd.Timestamp(2022, 6, 15, 0, 0, 0)
        day_index = market_data.loc[day_mask].index

        market_data.drop(day_index, inplace=True)

        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = market_data

        insider_strategy.create_market_data_events(
            instrument_ric=("CA4702731031", "JBR.CD"),
            event_day=pd.Timestamp(2022, 5, 19, 0, 0, 0),
            order_states_data=pd.DataFrame(),
        )
        assert not insider_strategy.market_data_events

    def test_create_market_data_events_fallback_more_than_5bdays_difference(
        self, mock_context, mock_empty_filters, mock_apply_strategy_kwargs
    ):
        """test create_market_data_events event_day_df fallback gets market
        data but fails because more than 5bdays between event_day_df and
        event_day_df_minus_1.

        :param mock_context: mock context with sample thresholds and filters
        :param mock_empty_filters: empty dictionary of filters
        """
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        mock_context.filters = mock_empty_filters
        mock_apply_strategy_kwargs["context"] = mock_context
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        market_data = pd.read_csv(
            TEST_DATA_MARKET_EVENTS.joinpath("market_data_for_5bd_logic.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        day_mask = market_data.loc[:, "Date"] == pd.Timestamp(2022, 6, 15, 0, 0, 0)
        day_index = market_data.loc[day_mask].index

        market_data.drop(day_index, inplace=True)

        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = market_data

        insider_strategy.create_market_data_events(
            instrument_ric=("CA4702731031", "JBR.CD"),
            event_day=pd.Timestamp(2021, 9, 13, 0, 0, 0, tzinfo=pytz.UTC),
            order_states_data=pd.DataFrame(),
        )
        assert not insider_strategy.market_data_events

    def test_create_market_data_events_cant_fallback(
        self, mock_context, mock_empty_filters, mock_apply_strategy_kwargs
    ):
        """test create_market_data_events event_day_df fallback gets market
        data but fails because more than 5bdays between event_day_df and
        event_day_df_minus_1.

        :param mock_context: mock context with sample thresholds and filters
        :param mock_empty_filters: empty dictionary of filters
        """
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        mock_context.filters = mock_empty_filters
        mock_apply_strategy_kwargs["context"] = mock_context
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        market_data = pd.read_csv(
            TEST_DATA_MARKET_EVENTS.joinpath("market_data_for_5bd_logic.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        day_mask = market_data.loc[:, "Date"] == pd.Timestamp(2022, 6, 15, 0, 0, 0)
        day_index = market_data.loc[day_mask].index

        market_data.drop(day_index, inplace=True)

        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = market_data

        # event day minus 1
        insider_strategy.create_market_data_events(
            instrument_ric=("CA4702731031", "JBR.CD"),
            event_day=pd.Timestamp(2021, 8, 24, 0, 0, 0, tzinfo=pytz.UTC),
            order_states_data=pd.DataFrame(),
        )
        # event day
        insider_strategy.create_market_data_events(
            instrument_ric=("CA4702731031", "JBR.CD"),
            event_day=pd.Timestamp(2021, 8, 17, 0, 0, 0, tzinfo=pytz.UTC),
            order_states_data=pd.DataFrame(),
        )
        assert not insider_strategy.market_data_events

    def test_create_market_data_events_no_market_data(
        self, mock_context, mock_empty_filters, mock_apply_strategy_kwargs
    ):
        """test create_market_data_events with missing market data.

        :param mock_context: mock context with sample thresholds and filters
        :param mock_empty_filters: empty dictionary of filters
        """
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        mock_context.filters = mock_empty_filters
        mock_apply_strategy_kwargs["context"] = mock_context
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()

        # Test when no market data is returned
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.DataFrame()

        insider_strategy.create_market_data_events(
            instrument_ric=("CA4702731031", "JBR.CD"),
            event_day=pd.Timestamp(2022, 6, 15, 0, 0, 0),
            order_states_data=pd.DataFrame(),
        )

        assert not insider_strategy.market_data_events

    def test_create_market_data_events_no_event_day_data(
        self, mock_context, mock_empty_filters, mock_apply_strategy_kwargs
    ):
        """test create_market_data_events with missing market data for the
        event day.

        :param mock_context: mock context with sample thresholds and filters
        :param mock_empty_filters: empty dictionary of filters
        """
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        mock_context.filters = mock_empty_filters
        mock_apply_strategy_kwargs["context"] = mock_context
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        market_data = pd.read_csv(
            TEST_DATA_MARKET_EVENTS.joinpath("market_data.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        day_mask = market_data.loc[:, "Date"] == pd.Timestamp(2022, 6, 15, 0, 0, 0)
        day_index = market_data.loc[day_mask].index

        market_data.drop(day_index, inplace=True)

        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = market_data

        insider_strategy.create_market_data_events(
            instrument_ric=("CA4702731031", "JBR.CD"),
            event_day=pd.Timestamp(2022, 7, 20, 0, 0, 0),
            order_states_data=pd.DataFrame(),
        )
        assert not insider_strategy.market_data_events

    def test_create_market_data_events_no_market_data_12_days(
        self, mock_context, mock_empty_filters, mock_apply_strategy_kwargs
    ):
        """test create_market_data_events with missing market data for 12 days
        required.

        :param mock_context: mock context with sample thresholds and filters
        :param mock_empty_filters: empty dictionary of filters
        """
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        mock_context.filters = mock_empty_filters
        mock_apply_strategy_kwargs["context"] = mock_context
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.read_csv(
            TEST_DATA_MARKET_EVENTS.joinpath("market_data.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        insider_strategy.create_market_data_events(
            instrument_ric=("CA4702731031", "JBR.CD"),
            event_day=pd.Timestamp(2021, 1, 5, 0, 0, 0),
            order_states_data=pd.DataFrame(),
        )
        assert not insider_strategy.market_data_events

    def test_create_market_data_events_invalid_close_variation(
        self, mock_context, mock_empty_filters, mock_apply_strategy_kwargs
    ):
        """test create_market_data_events with missing close price for event
        day.

        :param mock_context: mock context with sample thresholds and filters
        :param mock_empty_filters: empty dictionary of filters
        """
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        mock_context.filters = mock_empty_filters
        mock_apply_strategy_kwargs["context"] = mock_context
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.read_csv(
            TEST_DATA_MARKET_EVENTS.joinpath("market_data.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        # Data added just to the conversion rate being ignored
        fake_data = pd.read_json(TEST_DATA.joinpath("fake_data_to_be_used.ndjson"))
        fake_data["instrument"] = "CA4702731031"

        insider_strategy.create_market_data_events(
            instrument_ric=("CA4702731031", "JBR.CD"),
            event_day=pd.Timestamp(2021, 2, 9, 0, 0, 0),
            order_states_data=fake_data,
        )
        assert not insider_strategy.market_data_events

    def test_create_market_data_events_failed_daily_volume_variation(
        self,
        mock_context_with_daily_volume_variation,
        mock_empty_filters,
        mock_context,
        mock_apply_strategy_kwargs,
    ):
        """test create_market_data_events with failing in the daily volume
        variation threshold.

        :param mock_context_with_daily_volume_variation: mock context with sample thresholds with daily volume variation and filters
        :param mock_empty_filters: empty dictionary of filters
        """
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        mock_context.filters = mock_empty_filters
        mock_apply_strategy_kwargs["context"] = mock_context_with_daily_volume_variation
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)
        # insider_strategy = Strategy(context=mock_context_with_daily_volume_variation)

        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.read_csv(
            TEST_DATA_MARKET_EVENTS.joinpath("market_data.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        # Data added just to the conversion rate being ignored
        fake_data = pd.read_json(TEST_DATA.joinpath("fake_data_to_be_used.ndjson"))
        fake_data["instrument"] = "CA4702731031"

        insider_strategy.create_market_data_events(
            instrument_ric=("CA4702731031", "JBR.CD"),
            event_day=pd.Timestamp(2022, 6, 15, 0, 0, 0),
            order_states_data=fake_data,
        )
        assert not insider_strategy.market_data_events

    def test_create_market_data_events_failed_confidence_interval(
        self,
        mock_context,
        mock_empty_filters,
        mock_thresholds_evaluation,
        mock_apply_strategy_kwargs,
    ):
        """test create_market_data_events failing in the confidence_interval
        threshold.

        :param mock_empty_filters: empty dictionary of filters
        """
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        mock_context.filters = mock_empty_filters
        mock_apply_strategy_kwargs["context"] = mock_context
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.read_csv(
            TEST_DATA_MARKET_EVENTS.joinpath("market_data.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        # Data added just to the conversion rate being ignored
        fake_data = pd.read_json(TEST_DATA.joinpath("fake_data_to_be_used.ndjson"))
        fake_data["instrument"] = "CA4702731031"

        insider_strategy.create_market_data_events(
            instrument_ric=("CA4702731031", "JBR.CD"),
            event_day=pd.Timestamp(2022, 6, 15, 0, 0, 0),
            order_states_data=fake_data,
        )

        assert not insider_strategy.market_data_events

    def test_create_market_data_events_failed_direction_orientation(
        self,
        mock_context,
        mock_empty_filters,
        mock_thresholds_evaluation,
        mock_apply_strategy_kwargs,
    ):
        """test create_market_data_events failing in the
        check_direction_condition.

        :param mock_empty_filters: empty dictionary of filters
        """
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        mock_context.filters = mock_empty_filters
        mock_apply_strategy_kwargs["context"] = mock_context
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.read_csv(
            TEST_DATA_MARKET_EVENTS.joinpath("market_data.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.read_csv(
            TEST_DATA_MARKET_EVENTS.joinpath("market_data.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        evaluate_thresholds.check_confidence_interval = MagicMock()
        evaluate_thresholds.check_confidence_interval.return_value = {
            DateRangeParameters.START: 0,
            DateRangeParameters.END: 1,
        }

        # Data added just to the conversion rate being ignored
        fake_data = pd.read_json(TEST_DATA.joinpath("fake_data_to_be_used.ndjson"))
        fake_data["instrument"] = "CA4702731031"

        insider_strategy.create_market_data_events(
            instrument_ric=("CA4702731031", "JBR.CD"),
            event_day=pd.Timestamp(2022, 6, 15, 0, 0, 0),
            order_states_data=fake_data,
        )

        assert not insider_strategy.market_data_events

    def test_determine_observation_and_behaviour_periods(
        self, mock_context, mock_empty_filters, mock_apply_strategy_kwargs
    ):
        """test determine_observation_period and determine_behavior_periods.

        :param mock_context: mock context with thresholds
        :param mock_empty_filters: empty filters
        :param : mock for market data client
        :return:
        """
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        mock_context.filters = mock_empty_filters
        mock_apply_strategy_kwargs["context"] = mock_context
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        observation_period = determine_observation_period(
            event_day=pd.Timestamp(2022, 5, 5, 0, 0, 0),
            observation_period=insider_strategy.thresholds.activityObservationPeriod,
        )
        assert observation_period.get(DateRangeParameters.START) == pd.Timestamp(
            2022, 4, 28, 0, 0, 0
        )
        assert observation_period.get(DateRangeParameters.END) == pd.Timestamp(
            2022, 5, 4, 23, 59, 59
        )

        behaviour_period = determine_behavior_periods(
            date_range=observation_period,
            observation_period=insider_strategy.thresholds.activityObservationPeriod,
            behaviour_period=insider_strategy.thresholds.activityBehaviourPeriod,
        )

        assert len(behaviour_period) == 2
        assert behaviour_period[0].get(DateRangeParameters.START) == pd.Timestamp(
            2022, 4, 21, 0, 0, 0
        )
        assert behaviour_period[0].get(DateRangeParameters.END) == pd.Timestamp(
            2022, 4, 27, 23, 59, 59
        )
        assert behaviour_period[1].get(DateRangeParameters.START) == pd.Timestamp(
            2022, 4, 14, 0, 0, 0
        )
        assert behaviour_period[1].get(DateRangeParameters.END) == pd.Timestamp(
            2022, 4, 20, 23, 59, 59
        )

    def test_minimum_price_variation(self, mock_context, mock_apply_strategy_kwargs):
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        mock_context.filters = {
            "bool": {
                "must": [
                    {
                        "script": {
                            "script": {
                                "lang": "painless",
                                "params": {
                                    "end": 1666137600000,
                                    "start": 1665964800000,
                                },
                                "inline": "def model = doc['&model'].value; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'].value : doc['timestamps.orderSubmitted'].value; rawDt >= params.start && rawDt <= params.end",
                            }
                        }
                    }
                ]
            }
        }
        mock_context.thresholds = {
            "evaluationType": "Trader",
            "eventCreation": "Any",
            "marketDataEventPriceVariation": 0.8,
            "newsFeedEventNewsRelevance": "low",
            "sentiment": 0.7,
            "activityObservationPeriod": 2,
            "activityBehaviourPeriod": 2,
            "activityAllowedRangeFromProfile": 0.8,
            "activityMinimumPNL": 0,
            "activityMinimumTradeAmount": 0,
            "currencyFilter": "USD",
            "minimumPercentagePriceVariation": 0.01,
        }
        mock_apply_strategy_kwargs["context"] = mock_context
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.read_csv(
            TEST_DATA.joinpath("test_minimum_price_variation.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        # Data added just to the conversion rate being ignored
        fake_data = pd.read_json(TEST_DATA.joinpath("fake_data_to_be_used.ndjson"))
        fake_data["instrument"] = "US0378331005"

        insider_strategy.create_market_data_events(
            instrument_ric=("US0378331005", "AAPL.OQ"),
            event_day=pd.Timestamp(2022, 10, 17, 0, 0, 0),
            order_states_data=fake_data,
        )

        assert len(insider_strategy.market_data_events) == 1
        assert insider_strategy.market_data_events[0].get_price_variation() == 0.029122705593293836

    def test_minimum_price_variation_1(self, mock_context, mock_apply_strategy_kwargs):
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        mock_context.filters = {
            "bool": {
                "must": [
                    {
                        "script": {
                            "script": {
                                "lang": "painless",
                                "params": {
                                    "end": 1666137600000,
                                    "start": 1665964800000,
                                },
                                "inline": "def model = doc['&model'].value; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'].value : doc['timestamps.orderSubmitted'].value; rawDt >= params.start && rawDt <= params.end",
                            }
                        }
                    }
                ]
            }
        }
        mock_context.thresholds = {
            "evaluationType": "Trader",
            "eventCreation": "Any",
            "marketDataEventPriceVariation": 0.8,
            "newsFeedEventNewsRelevance": "low",
            "sentiment": 0.7,
            "activityObservationPeriod": 2,
            "activityBehaviourPeriod": 2,
            "activityAllowedRangeFromProfile": 0.8,
            "activityMinimumPNL": 0,
            "activityMinimumTradeAmount": 0,
            "currencyFilter": "USD",
            "minimumPercentagePriceVariation": 0.03,
        }
        mock_apply_strategy_kwargs["context"] = mock_context
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)
        insider_strategy.queries.get_market_data_by_instrument_or_ric = MagicMock()
        insider_strategy.queries.get_market_data_by_instrument_or_ric.return_value = pd.read_csv(
            TEST_DATA.joinpath("test_minimum_price_variation.csv"),
            index_col=0,
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        # Data added just to the conversion rate being ignored
        fake_data = pd.read_json(TEST_DATA.joinpath("fake_data_to_be_used.ndjson"))
        fake_data["instrument"] = "US0378331005"

        insider_strategy.create_market_data_events(
            instrument_ric=("US0378331005", "AAPL.OQ"),
            event_day=pd.Timestamp(2022, 10, 17, 0, 0, 0),
            order_states_data=fake_data,
        )

        assert len(insider_strategy.market_data_events) == 0

    def test_get_and_check_position_record(self, helpers, monkeypatch, mock_apply_strategy_kwargs):
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.base
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        filters = {
            "bool": {
                "must": {
                    "script": {
                        "script": {
                            "lang": "painless",
                            "params": {"end": 1655423999000, "start": 1655337600000},
                            "inline": "def model = doc['&model'].value; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'].value : doc['timestamps.orderSubmitted'].value; rawDt >= params.start && rawDt <= params.end",
                        }
                    },
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/Insider_Trading_V3_Split_UC6v2_CADEUR.csv"
                        ]
                    },
                }
            }
        }
        thresholds = {
            "evaluationType": "Trader",
            "eventCreation": "Market Data",
            "marketDataEventPriceVariation": 0.95,
            "marketDataEventDailyVolumeVariation": 0.05,
            "activityObservationPeriod": 5,
            "activityBehaviourPeriod": 3,
            "activityAllowedRangeFromProfile": 0.90,
            "activityMinimumPNL": 1,
            "activityMinimumTradeAmount": 1,
            "currencyFilter": "EUR",
            "marketDataEventSectorIndexComparison": False,
            "ordersExclusion": False,
            "relativeActivity": 2,
        }
        context = helpers.get_context(thresholds=thresholds, filters=filters)
        mock_apply_strategy_kwargs["context"] = context
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        alert_data = pd.read_csv(
            TEST_DATA.joinpath("test_get_and_check_position_record_alert_data.csv"),
            index_col=0,
        )

        class FakeQuery:
            def __init__(self):
                self._call = 0

            def fake_search_after_query(
                self,
                query=None,
                query_total_hits=None,
                request_timeout=None,
                batch_size=None,
            ):
                if self._call == 0:
                    self._call += 1
                    return pd.read_csv(
                        TEST_DATA.joinpath("test_positions_record.csv"),
                        index_col=0,
                    )
                return pd.DataFrame()

        monkeypatch.setattr(
            insider_strategy.queries.sdp_repository,
            "search_after_query",
            FakeQuery().fake_search_after_query,
        )

        observ_period = {
            "start": pd.Timestamp("2022-06-08 00:00:00"),
            "end": pd.Timestamp("2022-06-14 23:59:59"),
        }

        behaviour_period_segments = dates.determine_behavior_periods(
            date_range=observ_period,
            observation_period=insider_strategy.thresholds.activityObservationPeriod,
            behaviour_period=insider_strategy.thresholds.activityBehaviourPeriod,
        )

        observation_period_net_amounts = [
            (16950, (2.1768211559432937, 7786.583639966034, "2022-12-20")),
            (1695, None),
        ]

        for (
            observation_period_net_amount,
            expected_alert_res,
        ) in observation_period_net_amounts:
            position_check = insider_strategy.get_and_check_position_record(
                alert_data_to_check=alert_data,
                grouping_columns_dict={
                    "instrumentGrouping": "instrument",
                    "evaluationGrouping": "traderFileIdentifier",
                },
                eval_grp_val="trader 1",
                behaviour_date_range=dates.get_full_behaviour_period(
                    list_of_behaviour_periods=behaviour_period_segments
                ),
                observation_period_net_amount=observation_period_net_amount,
            )
            if position_check:
                assert position_check[0] == expected_alert_res

    def test_get_and_check_position_record_empty(
        self, helpers, monkeypatch, mock_apply_strategy_kwargs
    ):
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.base
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        filters = {
            "bool": {
                "must": {
                    "script": {
                        "script": {
                            "lang": "painless",
                            "params": {"end": 1655423999000, "start": 1655337600000},
                            "inline": "def model = doc['&model'].value; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'].value : doc['timestamps.orderSubmitted'].value; rawDt >= params.start && rawDt <= params.end",
                        }
                    },
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/Insider_Trading_V3_Split_UC6v2_CADEUR.csv"
                        ]
                    },
                }
            }
        }
        thresholds = {
            "evaluationType": "Trader",
            "eventCreation": "Market Data",
            "marketDataEventPriceVariation": 0.95,
            "marketDataEventDailyVolumeVariation": 0.05,
            "activityObservationPeriod": 5,
            "activityBehaviourPeriod": 3,
            "activityAllowedRangeFromProfile": 0.90,
            "activityMinimumPNL": 1,
            "activityMinimumTradeAmount": 1,
            "currencyFilter": "EUR",
            "marketDataEventSectorIndexComparison": False,
            "ordersExclusion": False,
            "relativeActivity": 2,
        }
        context = helpers.get_context(thresholds=thresholds, filters=filters)
        mock_apply_strategy_kwargs["context"] = context
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)
        # insider_strategy = Strategy(context=context)

        alert_data = pd.read_csv(
            TEST_DATA.joinpath("test_get_and_check_position_record_alert_data.csv"),
            index_col=0,
        )

        class FakeQuery:
            def __init__(self):
                self._call = 0

            def fake_search_after_query(
                self,
                query=None,
                query_total_hits=None,
                request_timeout=None,
                batch_size=None,
            ):
                return pd.DataFrame()

        monkeypatch.setattr(
            insider_strategy.queries.sdp_repository,
            "search_after_query",
            FakeQuery().fake_search_after_query,
        )

        observ_period = {
            "start": pd.Timestamp("2022-06-08 00:00:00"),
            "end": pd.Timestamp("2022-06-14 23:59:59"),
        }

        behaviour_period_segments = dates.determine_behavior_periods(
            date_range=observ_period,
            observation_period=insider_strategy.thresholds.activityObservationPeriod,
            behaviour_period=insider_strategy.thresholds.activityBehaviourPeriod,
        )

        position_check, audit_key, audit_data = insider_strategy.get_and_check_position_record(
            alert_data_to_check=alert_data,
            grouping_columns_dict={
                "instrumentGrouping": "instrument",
                "evaluationGrouping": "traderFileIdentifier",
            },
            eval_grp_val="trader 1",
            behaviour_date_range=dates.get_full_behaviour_period(
                list_of_behaviour_periods=behaviour_period_segments
            ),
            observation_period_net_amount=169500,
        )
        assert position_check is None
        assert audit_key == "no_positions_data"
        assert audit_data == {
            "isin": "CA4702731031",
            "currency": "USD",
            "event_day": None,
            "evaluation": "trader 1",
        }

    def test_get_and_check_position_record_no_direction(
        self, monkeypatch, helpers, mock_apply_strategy_kwargs
    ):
        """Tests position threshold when no currency is available, it should
        pass and use only the previous thresholds."""
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.base
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        filters = {
            "bool": {
                "must": {
                    "script": {
                        "script": {
                            "lang": "painless",
                            "params": {"end": 1655423999000, "start": 1655337600000},
                            "inline": "def model = doc['&model'].value; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'].value : doc['timestamps.orderSubmitted'].value; rawDt >= params.start && rawDt <= params.end",
                        }
                    },
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/Insider_Trading_V3_Split_UC6v2_CADEUR.csv"
                        ]
                    },
                }
            }
        }
        thresholds = {
            "evaluationType": "Trader",
            "eventCreation": "Market Data",
            "marketDataEventPriceVariation": 0.95,
            "marketDataEventDailyVolumeVariation": 0.05,
            "activityObservationPeriod": 5,
            "activityBehaviourPeriod": 3,
            "activityAllowedRangeFromProfile": 0.90,
            "activityMinimumPNL": 1,
            "activityMinimumTradeAmount": 1,
            "currencyFilter": "EUR",
            "marketDataEventSectorIndexComparison": False,
            "ordersExclusion": False,
        }
        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        alert_data = pd.read_csv(
            TEST_DATA.joinpath("test_get_and_check_position_record_alert_data.csv"),
            index_col=0,
        )

        class FakeQuery:
            def __init__(self):
                self._call = 0

            def fake_search_after_query(
                self,
                query=None,
                query_total_hits=None,
                request_timeout=None,
                batch_size=None,
            ):
                return pd.read_csv(
                    TEST_DATA.joinpath("test_positions_record_no_direction.csv"),
                    index_col=0,
                )

        monkeypatch.setattr(
            insider_strategy.queries.sdp_repository,
            "search_after_query",
            FakeQuery().fake_search_after_query,
        )

        observ_period = {
            "start": pd.Timestamp("2022-06-08 00:00:00"),
            "end": pd.Timestamp("2022-06-14 23:59:59"),
        }

        behaviour_period_segments = dates.determine_behavior_periods(
            date_range=observ_period,
            observation_period=insider_strategy.thresholds.activityObservationPeriod,
            behaviour_period=insider_strategy.thresholds.activityBehaviourPeriod,
        )

        position_check, _, _ = insider_strategy.get_and_check_position_record(
            alert_data_to_check=alert_data,
            grouping_columns_dict={
                "instrumentGrouping": "instrument",
                "evaluationGrouping": "traderFileIdentifier",
            },
            eval_grp_val="trader 1",
            behaviour_date_range=dates.get_full_behaviour_period(
                list_of_behaviour_periods=behaviour_period_segments
            ),
            observation_period_net_amount=169500,
        )

        assert position_check == (pd.NA, pd.NA, pd.NA)

    def test_get_and_check_position_record_no_currency(
        self, monkeypatch, helpers, mock_apply_strategy_kwargs
    ):
        """Tests position threshold when no currency is available, it should
        pass and use only the previous thresholds."""
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.base
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        filters = {
            "bool": {
                "must": {
                    "script": {
                        "script": {
                            "lang": "painless",
                            "params": {"end": 1655423999000, "start": 1655337600000},
                            "inline": "def model = doc['&model'].value; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'].value : doc['timestamps.orderSubmitted'].value; rawDt >= params.start && rawDt <= params.end",
                        }
                    },
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/Insider_Trading_V3_Split_UC6v2_CADEUR.csv"
                        ]
                    },
                }
            }
        }
        thresholds = {
            "evaluationType": "Trader",
            "eventCreation": "Market Data",
            "marketDataEventPriceVariation": 0.95,
            "marketDataEventDailyVolumeVariation": 0.05,
            "activityObservationPeriod": 5,
            "activityBehaviourPeriod": 3,
            "activityAllowedRangeFromProfile": 0.90,
            "activityMinimumPNL": 1,
            "activityMinimumTradeAmount": 1,
            "currencyFilter": "EUR",
            "marketDataEventSectorIndexComparison": False,
            "ordersExclusion": False,
        }
        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        insider_strategy = ApplyStrategy(**mock_apply_strategy_kwargs)
        # insider_strategy = Strategy(context=context)

        alert_data = pd.read_csv(
            TEST_DATA.joinpath("test_get_and_check_position_record_alert_data.csv"),
            index_col=0,
        )

        class FakeQuery:
            def __init__(self):
                self._call = 0

            def fake_search_after_query(
                self,
                query=None,
                query_total_hits=None,
                request_timeout=None,
                batch_size=None,
            ):
                return pd.read_csv(
                    TEST_DATA.joinpath("test_positions_record_no_currency.csv"),
                    index_col=0,
                )

        monkeypatch.setattr(
            insider_strategy.queries.sdp_repository,
            "search_after_query",
            FakeQuery().fake_search_after_query,
        )

        observ_period = {
            "start": pd.Timestamp("2022-06-08 00:00:00"),
            "end": pd.Timestamp("2022-06-14 23:59:59"),
        }

        behaviour_period_segments = dates.determine_behavior_periods(
            date_range=observ_period,
            observation_period=insider_strategy.thresholds.activityObservationPeriod,
            behaviour_period=insider_strategy.thresholds.activityBehaviourPeriod,
        )

        position_check, audit_key, audit_key = insider_strategy.get_and_check_position_record(
            alert_data_to_check=alert_data,
            grouping_columns_dict={
                "instrumentGrouping": "instrument",
                "evaluationGrouping": "traderFileIdentifier",
            },
            eval_grp_val="trader 1",
            behaviour_date_range=dates.get_full_behaviour_period(
                list_of_behaviour_periods=behaviour_period_segments
            ),
            observation_period_net_amount=169500,
        )

        assert position_check == (pd.NA, pd.NA, pd.NA)

    def test_create_alert_events_fields(self, helpers, mock_apply_strategy_kwargs):
        """Added test which verifies that the alert is not affected when there
        is no event for the specific instrument."""

        alert = {}
        news_feed_events = []
        market_data_events = []
        event_day_details = {
            EventDayDetailsEnum.DATE: datetime.date.today(),
            EventDayDetailsEnum.PRICE_OF_EVENT_DAY: 100,
        }

        (
            news_feed_events_alert,
            news_feed_event_day_details,
        ) = alerts.create_alert_news_feed_events_fields(
            alert=alert,
            event_day_details=event_day_details,
            instrument="foo",
            news_feed_events=news_feed_events,
        )

        assert alert == news_feed_events_alert
        assert event_day_details == news_feed_event_day_details

        (
            market_feed_events_alert,
            market_event_day_details,
        ) = alerts.create_alert_market_data_events_fields(
            alert=alert,
            event_day_details=event_day_details,
            instrument="foo",
            market_data_events=market_data_events,
        )

        assert alert == market_feed_events_alert
        assert event_day_details == market_event_day_details

    def test_check_events_order_states_direction_transaction_volume_zero(
        self, helpers, mock_apply_strategy_kwargs
    ):
        """Test the check_events_order_states_direction_transaction when
        transaction volume is zero."""
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        thresholds = {
            "evaluationType": "Trader",
            "eventCreation": "Both",
            "marketDataEventPriceVariation": 0.95,
            "newsFeedEventNewsRelevance": "medium",
            "activityObservationPeriod": 5,
            "activityBehaviourPeriod": 2,
            "activityAllowedRangeFromProfile": 0.95,
            "activityMinimumPNL": 1000,
            "activityMinimumTradeAmount": 1000,
            "currencyFilter": "USD",
            "minimumPercentagePriceVariation": 0.05,
            "sentiment": 0.6,
            "eventDay": [
                pd.Timestamp("2024-01-23 00:00:00"),
            ],
        }
        filters = {}

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        strategy.instruments_with_market_data_events = set(["US23344D1081"])
        strategy.instruments_with_news_feed_events = set(["US23344D1081", "DK0010272632"])

        obtained_events_result, audit_key, audit_data = (
            strategy.check_events_order_states_direction(
                transaction_volume=0,
                instrument="DK0010272632",
                observation_amount=-89005.77545386799,
                behaviour_series=pd.Series([0.0, 0.0]),
            )
        )

        assert obtained_events_result == {
            "alertCreation": False,
            "eventDirection": None,
            "msg": "Event creation threshold was not met.",
        }
        assert audit_key == "news_sentiment_below_threshold"
        assert audit_data == {
            "instrument": "DK0010272632",
            "sentiment_threshold": 0.6,
            "event_day": None,
        }

    def test_check_events_order_states_direction_no_event_direction(
        self, helpers, mock_apply_strategy_kwargs
    ):
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        thresholds = {
            "evaluationType": "Trader",
            "eventCreation": "Both",
            "marketDataEventPriceVariation": 0.95,
            "newsFeedEventNewsRelevance": "medium",
            "activityObservationPeriod": 5,
            "activityBehaviourPeriod": 2,
            "activityAllowedRangeFromProfile": 0.95,
            "activityMinimumPNL": 1000,
            "activityMinimumTradeAmount": 1000,
            "currencyFilter": "USD",
            "minimumPercentagePriceVariation": 0.05,
            "sentiment": 0.03,
            "eventDay": [
                pd.Timestamp("2024-01-23 00:00:00"),
            ],
        }
        filters = {}

        def mock_refinitiv_news_subject():
            return {
                "all_subjects": True,
                "subjects": [
                    "Agriculture",
                    "Analyst Ratings",
                    "Author",
                    "Business",
                    "Business Sectors",
                    "Central Banks",
                    "Commodities",
                    "Corporate Actions",
                    "Corporate Earnings",
                    "country",
                    "Country",
                    "Crypto",
                    "Debt/Credit",
                    "Earnings",
                    "Energy",
                    "Equity - Buybacks",
                    "Europe",
                    "Financial News",
                    "Financial NEws",
                    "Forex",
                    "Journalists columns",
                    "Macroeconomics",
                    "Merger Acquisition",
                    "News Language",
                    "News Source",
                    "Other",
                    "Regulation",
                ],
            }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        strategy.market_data_events = [
            MarketDataEvent(
                date=pd.Timestamp("2024-01-23 00:00:00"),
                direction="up",
                instrument="DK0010272632",
                event_day_close_price=2.18,
                event_day_close_price_converted=2.18,
                ric="DADA.OQ",
                market_data=pd.read_csv(
                    TEST_DATA.joinpath(
                        "test_check_events_order_states_direction_no_event_direction_mrk_data.csv"
                    ),
                    index_col=0,
                    parse_dates=["Date"],
                    date_parser=custom_date_parser(),
                ),
                close_price_variation=0.09547738693467345,
                allowed_price_range={
                    "start": -0.15017378269604392,
                    "end": 0.0950550188190303,
                },
            )
        ]

        strategy.news_feed_events = [
            NewsFeedEvent(
                date=pd.Timestamp("2024-01-23 00:00:00"),
                instrument="US23344D1081",
                news_feed=pd.read_csv(
                    TEST_DATA.joinpath(
                        "test_check_events_order_states_direction_no_event_direction_news_feed.csv"
                    ),
                    index_col=0,
                    parse_dates=["createdDateTime", "updatedDateTime"],
                    date_parser=custom_date_parser(),
                ),
                ric="DADA.OQ",
            )
        ]

        strategy.instruments_with_market_data_events = set(["US23344D1081", "DK0010272632"])
        strategy.instruments_with_news_feed_events = set(["US23344D1081", "DK0010272632"])

        obtained_events_result, audit_key, audit_data = (
            strategy.check_events_order_states_direction(
                transaction_volume=-609681.7999999999,
                instrument="US23344D1081",
                observation_amount=-89005.77545386799,
                behaviour_series=pd.Series([0.0, 0.0]),
            )
        )

        assert obtained_events_result == {
            "alertCreation": False,
            "eventDirection": None,
            "msg": "No event direction.",
        }
        assert audit_key == "null_event_direction"
        assert audit_data == {"event_day": None, "instrument": "US23344D1081"}

    def test_check_events_order_states_direction_no_alerts_event_direction(
        self, helpers, mock_apply_strategy_kwargs
    ):
        import insider_trading_v3_refinitiv_apply_strategy.query
        import market_abuse_algorithms.data_source.repository.market_data.utils

        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.get_eod_stats.return_value = fake_get_eod_stats()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_market_client.return_value = (
            FakeMarketClient()
        )

        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client = MagicMock()
        insider_trading_v3_refinitiv_apply_strategy.query.get_refinitiv_client.return_value = (
            FakeRefinitivNewsClient(relevance="high")
        )
        thresholds = {
            "evaluationType": "Trader",
            "eventCreation": "Both",
            "marketDataEventPriceVariation": 0.95,
            "newsFeedEventNewsRelevance": "medium",
            "activityObservationPeriod": 5,
            "activityBehaviourPeriod": 2,
            "activityAllowedRangeFromProfile": 0.95,
            "activityMinimumPNL": 1000,
            "activityMinimumTradeAmount": 1000,
            "currencyFilter": "USD",
            "minimumPercentagePriceVariation": 0.05,
            "sentiment": 0.03,
            "eventDay": [
                pd.Timestamp("2024-01-23 00:00:00"),
            ],
        }
        filters = {}

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        strategy.market_data_events = [
            MarketDataEvent(
                date=pd.Timestamp("2024-01-23 00:00:00"),
                direction="up",
                instrument="DK0010272632",
                event_day_close_price=2.18,
                event_day_close_price_converted=2.18,
                ric="DADA.OQ",
                market_data=pd.read_csv(
                    TEST_DATA.joinpath(
                        "test_check_events_order_states_direction_no_event_direction_mrk_data.csv"
                    ),
                    index_col=0,
                    parse_dates=["Date"],
                    date_parser=custom_date_parser(),
                ),
                close_price_variation=0.09547738693467345,
                allowed_price_range={
                    "start": -0.15017378269604392,
                    "end": 0.0950550188190303,
                },
            )
        ]

        strategy.news_feed_events = [
            NewsFeedEvent(
                date=pd.Timestamp("2024-01-23 00:00:00"),
                instrument="US23344D1081",
                news_feed=pd.read_csv(
                    TEST_DATA.joinpath(
                        "test_check_events_order_states_direction_no_event_direction_news_feed.csv"
                    ),
                    index_col=0,
                    parse_dates=["createdDateTime", "updatedDateTime"],
                    date_parser=custom_date_parser(),
                ),
                ric="DADA.OQ",
                direction="down",
            )
        ]

        strategy.instruments_with_market_data_events = set(["US23344D1081", "DK0010272632"])
        strategy.instruments_with_news_feed_events = set(["US23344D1081", "DK0010272632"])

        obtained_events_result, audit_key, audit_data = (
            strategy.check_events_order_states_direction(
                transaction_volume=-609681.7999999999,
                instrument="US23344D1081",
                observation_amount=89005.77545386799,
                behaviour_series=pd.Series([0.0, 0.0]),
            )
        )

        assert obtained_events_result == {
            "alertCreation": False,
            "eventDirection": "down",
            "msg": "Observation amount must be negative when event impact is negative.",
            "market_data_events": [],
            "news_feed_events": strategy.news_feed_events,
        }
        assert audit_key == "event_direction_analysis"
        assert audit_data == {
            "analysis_msg": "Observation amount must be negative when event impact is negative.",
            "observation_amount": 89005.77545386799,
            "direction": "down",
            "behaviour_mean": 0.0,
            "event_day": None,
        }

import addict
import fsspec
import hashlib
import json
import os
import shutil
import tempfile
from contextlib import contextmanager
from fsspec.core import get_fs_token_paths
from pathlib import Path
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8
from se_elasticsearch.repository.helpers import Meta
from typing import Any, List, Optional


class FakeIndex:
    def __init__(self, index: str) -> None:
        self.index = index

    def exists(self, index):
        return index == self.index


class FakeCreateClient:
    def __init__(self, index: FakeIndex, count: int, client_get_path: Optional[str] = None) -> None:
        self.indices = index
        self.path = client_get_path
        self.internal_storage: dict = {}
        self.count_value = count
        self.update_call_count = 0
        self.update_list: list = []

    def create(self, index, id, body, **kwargs):
        existing_docs = self.internal_storage.get(index, {})
        existing_docs[id] = body
        self.internal_storage[index] = existing_docs

    def update(self, **kwargs):
        self.update_call_count += 1
        self.update_list.append(kwargs)
        return addict.Dict({"body": {"result": "updated"}})

    def delete(self, **kwargs):
        return

    def count(self, **kwargs):
        return addict.Dict({"count": self.count_value})

    def update_by_query(self, **kwargs):
        return addict.Dict({"updated": 20})

    def get(self, **kwargs):
        watch = json.loads(Path(self.path).read_text())  # type: ignore[arg-type]
        es_response = addict.Dict({"body": {"_source": watch}})
        return es_response


class FakeEsRepo:
    def __init__(
        self,
        version: int = 8,
        search_repo_path: Optional[List[str]] = None,
        client_get_path: Optional[str] = None,
        fake_index: str = "fake-tenant_surv_exclude_by_id",
        count: int = 20,
    ):
        self.version = version
        self.meta = Meta()
        self.client = FakeCreateClient(
            index=FakeIndex(fake_index), client_get_path=client_get_path, count=count
        )
        self.paths = search_repo_path if search_repo_path is not None else []
        self.call_count = 0

    def get_elasticsearch_version(self):
        return self.version

    def search(self, **kwargs):
        if not self.paths:
            raise ValueError("search_repo_path is not set or is empty")

        # Determine the index to use based on the call count
        path_index = self.call_count % len(self.paths)
        self.call_count += 1
        if self.paths[path_index].endswith(".json"):
            watch = json.loads(Path(self.paths[path_index]).read_text())
            es_response = addict.Dict({"hits": {"hits": [{"_source": watch}], "total": 1}})
        else:
            responses = [
                json.loads(line) for line in Path(self.paths[path_index]).read_text().split("\n")
            ]
            es_response = addict.Dict(
                {
                    "hits": {
                        "hits": [{"_source": response} for response in responses],
                        "total": len(responses),
                    }
                }
            )
        return es_response


class FakeSlimRecordHandler:
    def __init__(
        self,
        version: int = 8,
        search_repo_path: Optional[List[str]] = None,
        client_get_path: Optional[str] = None,
    ):
        self.es_version = version
        self.es_repo = FakeEsRepo(version, search_repo_path, client_get_path)
        self.meta = self.es_repo.meta
        self.client = self.es_repo.client

    def get_es_alias(self, model: SteelEyeSchemaBaseModelES8, tenant: str | None = None) -> str:
        return model.get_elastic_index_alias(tenant=tenant)  # type: ignore


class FakeSequenceService:
    def __init__(self, *args, **kwargs):
        self.number = 0

    def next(self, *args, **kwargs):
        self.number += 1
        return self.number


@contextmanager
def data_lake():
    """Create datalake file structure inside tempfile.

    See:
    github.com/intake/filesystem_spec/blob/master/fsspec/implementations/tests/test_local.py
    :yield: Yields the path to the temporary directory
    :rtype: str
    """

    odir = os.getcwd()
    dirname = tempfile.mkdtemp()
    os.chdir(dirname)

    yield dirname

    # cleanup
    shutil.rmtree(dirname)
    os.chdir(odir)


def dump_file_into_datalake(
    base_path: str,
    file_path: str,
    file_extension: str = ".ndjson",
) -> str:
    file_dest = f"{hashlib.sha256(file_path.encode()).hexdigest()}______transformed{file_extension}"
    file_dest = base_path + file_dest

    # copy files into temp lake
    os.makedirs(base_path, exist_ok=True)
    fs, _, _ = get_fs_token_paths(os.getcwd())
    fs.cp(file_path, file_dest)

    return file_dest


def dump_files_into_datalake(base_path: str, file_paths: List[str]) -> List[str]:
    # copy files into temp lake
    os.makedirs(base_path)
    fs, _, _ = get_fs_token_paths(os.getcwd())

    output_paths = []
    for file_path in file_paths:
        file_dest = f"{hashlib.sha256(file_path.encode()).hexdigest()}______analytics.ndjson"
        file_dest = base_path + file_dest
        fs.cp(file_path, file_dest)
        output_paths.append(file_dest)

    return output_paths


def dump_list_into_datalake(base_path: str, json_list: list) -> str:
    file_dest = f"{hashlib.sha256('a_hash'.encode()).hexdigest()}______transformed.ndjson"
    file_dest = base_path + file_dest

    # copy files into temp lake
    os.makedirs(base_path)
    fs, _, _ = get_fs_token_paths(os.getcwd())
    with fs.open(file_dest, mode="w") as f:
        for json_obj in json_list:
            f.write(json.dumps(json_obj) + "\n")

    return file_dest


def fake_get_tenant_workflow(*args, **kwargs):
    return addict.Dict(
        {
            "tenant": {
                "cloud": "local",
                "lake_prefix": "./",
            },
            "workflow": {"streamed": True},
        },
    )


def load_ndjson_into_list(path: str) -> List[Any]:
    records = []
    output_file = fsspec.open(path, mode="r")
    with output_file as f:
        for line in f:
            records.append(json.loads(line))

    return records

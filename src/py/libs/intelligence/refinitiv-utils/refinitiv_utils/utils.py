import backoff
import httpx
import logging
import re
import requests
import shutil
import time
from contextlib import contextmanager
from datetime import datetime
from http import HTTPStatus
from httpx import Client, HTTPStatusError
from refinitiv_utils.endpoints import RefinitivAPEndpoints
from refinitiv_utils.refinitiv_settings import refinitiv_config
from refinitiv_utils.static import MappingIdentifierType, RefinitivProduct
from requests.auth import HTT<PERSON><PERSON>asicAuth
from typing import Any, Iterator, List, Optional

LEVEL_1_USER = refinitiv_config.REFINITIV_1_USERNAME
LEVEL_1_PASS = refinitiv_config.REFINITIV_1_PASSWORD

LEVEL_2_USER = refinitiv_config.REFINITIV_2_USERNAME
LEVEL_2_PASS = refinitiv_config.REFINITIV_2_PASSWORD

LEVEL_3_USER = refinitiv_config.REFINITIV_3_USERNAME
LEVEL_3_PASS = refinitiv_config.REFINITIV_3_PASSWORD

REFINITIV_API_TIMEOUT = refinitiv_config.REFINITIV_API_TIMEOUT
MAX_POLL_COUNT = refinitiv_config.REFINITIV_API_MAX_POLL_COUNT
POLL_SLEEP_SECS = refinitiv_config.REFINITIV_API_POLL_SLEEP_SECS


class RefinitivClient:
    httpx_refinitiv_client: Client

    def __init__(self, level_1: bool = True, eod: bool = True, logger=logging.getLogger(__name__)):
        user = (
            LEVEL_1_USER
            if level_1 and not eod
            else LEVEL_2_USER
            if not eod and not level_1
            else LEVEL_3_USER
        )
        password = (
            LEVEL_1_PASS
            if level_1 and not eod
            else LEVEL_2_PASS
            if not eod and not level_1
            else LEVEL_3_PASS
        )
        self.auth = HTTPBasicAuth(username=user, password=password)
        self.httpx_refinitiv_client = httpx.Client(
            base_url=RefinitivAPEndpoints.BASE_URL,
            auth=(user, password),
            timeout=REFINITIV_API_TIMEOUT,
            follow_redirects=(not level_1),
        )
        self.logger = logger

    def close(self):
        self.httpx_refinitiv_client.close()

    @backoff.on_exception(backoff.expo, httpx.RequestError, max_tries=3)
    def request_notes_file_id(self, report_extraction_id: str) -> str:
        """Request extraction notes file using a report_extraction_id."""
        url = RefinitivAPEndpoints.report_extraction_notes_file_url(report_extraction_id)
        response = self.httpx_refinitiv_client.get(url)
        response.raise_for_status()
        return str(response.json().get("ExtractedFileId"))

    def request_market_depth_raw(
        self,
        rics: List[str],
        start_date: datetime,
        end_date: datetime,
        levels: int = 10,
        retries: Optional[int] = 5,
        retry_sleep: float = 60.0,
    ) -> Any:
        # compose extract raw url
        extract_raw_url = RefinitivAPEndpoints.extract_raw_url()
        # compose extract raw request body
        extract_raw_payload = RefinitivAPEndpoints.extract_order_book_depth_raw_request_body(
            rics=rics, query_start_date=start_date, query_end_date=end_date, levels=levels
        )
        while retries:
            try:
                extract_raw_response = self.httpx_refinitiv_client.post(
                    url=extract_raw_url,
                    json=extract_raw_payload,
                    timeout=60 * 5,
                    headers={"Prefer": "respond-async"},
                )
                extract_raw_response.raise_for_status()

                # loop for the JobId in the GET of Location
                poll_location = extract_raw_response.headers.get("Location")

                # while the status_code is 202, the request is being processed.
                counter = 0
                while (
                    extract_raw_response.status_code == httpx.codes.ACCEPTED
                    and counter <= MAX_POLL_COUNT
                ):
                    time.sleep(POLL_SLEEP_SECS)  # waiting between poll requests
                    extract_raw_response = self.httpx_refinitiv_client.get(
                        poll_location,
                        timeout=60 * 5,
                        headers={"Prefer": "respond-async"},
                    )
                    counter += 1

                extract_raw_response.raise_for_status()
                if vali_errors := extract_raw_response.json().get("IdentifierValidationErrors"):
                    self.logger.warning(f"Identifier Validation errors: {vali_errors}")

                # extract job id from response
                job_id = extract_raw_response.json().get("JobId")
                return job_id

            except HTTPStatusError as e:
                if not retries:
                    raise e

                time.sleep(retry_sleep)
                retries -= 1

        raise Exception("Failed to initiate back-load")

    @contextmanager
    def download_instrument_list_file(self, report_extraction_id: str) -> Iterator[httpx.Response]:
        # compose raw extraction results url
        url = RefinitivAPEndpoints.instrument_list_full_download_url(
            report_extraction_id=report_extraction_id
        )

        with self.httpx_refinitiv_client.stream(
            "GET", url=url, headers={"X-Direct-Download": "true"}, follow_redirects=True
        ) as response:
            response.raise_for_status()
            yield response

    def download_market_depth_raw(self, job_id: str) -> Iterator[Any]:
        # compose raw extraction results url
        raw_extraction_results_url = RefinitivAPEndpoints.raw_extraction_results_url(job_id=job_id)
        raw_extraction_response = requests.get(
            url=raw_extraction_results_url,
            auth=self.auth,
            stream=True,
            headers={"X-Direct-Download": "true", "Prefer": "respond-async"},
        )
        raw_extraction_response.raise_for_status()

        return raw_extraction_response.iter_content(chunk_size=100 * 1000)

    @backoff.on_exception(backoff.expo, httpx.RequestError, max_tries=5)
    def get_extraction_notes(self, file_id: str) -> str:
        """Get raw notes text for a given extraction file_id."""
        # compose raw extraction results url
        raw_extraction_results_url = RefinitivAPEndpoints.raw_extraction_file_url(job_id=file_id)
        response = self.httpx_refinitiv_client.get(url=raw_extraction_results_url)
        response.raise_for_status()
        return response.text

    @backoff.on_exception(backoff.expo, httpx.RequestError, max_tries=3)
    def extract_raw(
        self,
        rics: List[str],
        start_date: datetime,
        end_date: datetime,
        identifier_type: MappingIdentifierType = MappingIdentifierType.RIC,
    ) -> Iterator[Any]:
        request_headers = {
            "X-Direct-Download": "true",
            "Prefer": "respond-async",
            "Accept-Encoding": "gzip",
        }

        # compose extract raw url
        extract_raw_url = RefinitivAPEndpoints.extract_raw_url()

        # compose extract raw request body
        extract_raw_payload = RefinitivAPEndpoints.extract_raw_request_body(
            rics=rics,
            query_start_date=start_date,
            query_end_date=end_date,
            identifier_type=identifier_type,
        )

        poll_location = None
        for attempt in range(3):
            extract_raw_response = self.httpx_refinitiv_client.post(
                url=extract_raw_url,
                json=extract_raw_payload,
                headers=request_headers,
                timeout=60 * 5,
            )
            extract_raw_response.raise_for_status()
            poll_location = extract_raw_response.headers.get("Location")
            if poll_location:
                break
            self.logger.warning(f"Poll location not found on attempt {attempt + 1}, retrying...")
            time.sleep(5)
        if not poll_location:
            raise AttributeError("Poll location not present after retries")
        # while the status_code is 202, the request is being processed.
        counter = 0
        while extract_raw_response.status_code == HTTPStatus.ACCEPTED and counter <= MAX_POLL_COUNT:
            time.sleep(POLL_SLEEP_SECS)  # waiting between poll requests
            extract_raw_response = self.httpx_refinitiv_client.get(
                poll_location,
                headers=request_headers,
                timeout=60 * 5,
            )
            counter += 1

        extract_raw_response.raise_for_status()
        if vali_errors := extract_raw_response.json().get("IdentifierValidationErrors"):
            self.logger.warning(f"Identifier Validation errors: {vali_errors}")

        # extract job id from response
        job_id = extract_raw_response.json().get("JobId")

        # compose raw extraction results url
        raw_extraction_results_url = RefinitivAPEndpoints.raw_extraction_results_url(job_id=job_id)

        time.sleep(10)  # waiting between poll requests

        with self.httpx_refinitiv_client.stream(
            "GET", url=raw_extraction_results_url, headers=request_headers, follow_redirects=True
        ) as raw_extraction_response:
            raw_extraction_response.raise_for_status()
            # buffer the data to avoid stream closure errors
            yield from raw_extraction_response.iter_bytes(chunk_size=100_000)

    def extract_eod(
        self,
        rics: List[str],
        query_start_date: datetime,
        query_end_date: datetime,
        refinitiv_product: RefinitivProduct = RefinitivProduct.ELEKTRON,
        identifier_type: MappingIdentifierType = MappingIdentifierType.RIC,
    ) -> Iterator[Any]:
        request_headers = {
            "X-Direct-Download": "true",
            "Prefer": "respond-async",
            "Accept-Encoding": "gzip",
        }

        # compose extract raw url
        extract_raw_url = RefinitivAPEndpoints.extract_raw_url()

        # compose extract raw request body
        extract_raw_payload = RefinitivAPEndpoints.extract_elektron_eod_request_body(
            rics=rics,
            query_start_date=query_start_date,
            query_end_date=query_end_date,
            refinitiv_product=refinitiv_product,
            identifier_type=identifier_type,
        )

        extract_raw_response = requests.post(
            url=extract_raw_url,
            json=extract_raw_payload,
            auth=self.auth,
            timeout=60 * 90,
            headers=request_headers,
        )

        try:
            response_json = extract_raw_response.json()
        except ValueError:
            response_json = None

        if response_json is not None:
            # Log validation errors if they exist
            vali_errors = response_json.get("IdentifierValidationErrors")
            if vali_errors:
                self.logger.warning(
                    f"Identifier Validation errors: {vali_errors} \n"
                    f"RICs: {rics} \nRefinitiv Product: {refinitiv_product}"
                )

            job_id = response_json.get("JobId")
            if not job_id:
                raise ValueError("JobId not found in the response JSON.")
        else:
            location = extract_raw_response.headers.get("Location", "")
            job_id_match = re.search("'(.*?)'", location)
            if job_id_match:
                job_id = job_id_match.group(1)
            else:
                raise ValueError(f"No job ID found in the Location header: {location}")

        request_headers = {
            "Prefer": "respond-async",
            "X-Direct-Download": "true",
            "Accept-Encoding": "gzip",
        }
        # Downloads instrument data files with given id
        url = RefinitivAPEndpoints.raw_extraction_results_url(job_id)
        ready = False
        while not ready:
            time.sleep(5)
            response = requests.get(url, headers=request_headers, stream=True, auth=self.auth)
            ready = response.status_code == 200

        response.raise_for_status()

        # create generator for response with chunk size of 100kB
        itr = response.iter_content(chunk_size=100_000)

        return itr

    @backoff.on_exception(backoff.expo, httpx.RequestError, max_tries=3)
    def extract_schedule(
        self,
        schedule_id: str,
        start_date: datetime,
        end_date: datetime,
    ) -> Any:
        # Get all extraction ids for instrument between two dates
        schedule_url = RefinitivAPEndpoints.raw_extraction_schedule_url(
            schedule_id=schedule_id,
            query_start_date=start_date,
            query_end_date=end_date,
        )
        extract_raw_response = self.httpx_refinitiv_client.get(url=schedule_url)
        extract_raw_response.raise_for_status()

        return extract_raw_response.json()

    @backoff.on_exception(backoff.expo, httpx.RequestError, max_tries=3)
    def extract_file_id(self, report_id: str) -> Any:
        # Retrieves file_ids needed to download instrument data
        file_url = RefinitivAPEndpoints.raw_extraction_file_id(report_id=report_id)
        extract_raw_response = requests.get(url=file_url, auth=self.auth)
        extract_raw_response.raise_for_status()

        return extract_raw_response.json()

    @backoff.on_exception(backoff.expo, httpx.RequestError, max_tries=3)
    def download_file(self, job_id: str, file_path: str) -> requests.models.Response:
        request_headers = {
            "Prefer": "respond-async",
            "Content-Type": "text/plain",
            "Accept-Encoding": "gzip",
            "X-Direct-Download": "true",
        }
        # Downloads instrument data files with given id
        url = RefinitivAPEndpoints.raw_extraction_file_url(job_id)

        response = requests.get(url, headers=request_headers, stream=True, auth=self.auth)
        response.raise_for_status()

        response.raw.decode_content = False
        file_name = file_path + job_id + ".csv.gz"
        chunk_size = 1024
        rr = response.raw
        with open(file_name, "wb") as fd:
            shutil.copyfileobj(rr, fd, chunk_size)

        return response

    @backoff.on_exception(backoff.expo, httpx.RequestError, max_tries=3)
    def get_ric_code(self, instrument_identifiers_list: List[dict]) -> requests.models.Response:
        """get ric code for instruments.

        :param instrument_identifiers_list: Instrument Identifiers data as per refinitiv API format
                Example 1: [
                            {
                                "Identifier": "IE00BF3N7219",
                                "IdentifierType": "Isin"
                            }
                        ]
                Example 2: [
                            {
                                "Identifier": "CH0513825809",
                                "IdentifierType": "Isin"
                            },
                            {
                                "Identifier": "IE00BF3N7219",
                                "IdentifierType": "Isin",
                                "Source": "DEU"
                            }
                           ]
        :param retries: Number of times to retry the API
        :param retry_sleep_time: Amount of time in seconds before each retry attempt
        :return: requests response object
        """

        url = RefinitivAPEndpoints.get_ric_code_url()
        payload = RefinitivAPEndpoints.get_ric_code_payload(instrument_identifiers_list)

        response = requests.post(url=url, json=payload, auth=self.auth)
        response.raise_for_status()
        if response.status_code == 200:
            # check status code as occasionally
            # refinitiv returns 201/202 status without ric data in response
            return response
        else:
            raise httpx.RequestError(
                f"Refinitiv returned an invalid status code: {response.status_code}"
            )

    @backoff.on_exception(backoff.expo, httpx.RequestError, max_tries=3)
    def get_rics_in_instrument_list(self, list_id: str) -> requests.models.Response:
        url = RefinitivAPEndpoints.get_rics_in_instrument_list_url(list_id)
        response = requests.get(
            url=url,
            auth=self.auth,
        )
        response.raise_for_status()

        return response

    @backoff.on_exception(backoff.expo, httpx.RequestError, max_tries=3)
    def get_instrument_list_id_by_name(self, instrument_list_name: str) -> Any:
        url = RefinitivAPEndpoints.instrument_list_by_name_url(instrument_list_name)
        self.logger.info(f"Getting instrument list by name {instrument_list_name}")
        response = self.httpx_refinitiv_client.get(url)
        response.raise_for_status()
        return response.json().get("ListId")

    @backoff.on_exception(backoff.expo, httpx.RequestError, max_tries=3)
    def add_ric_to_il(self, ric: str, refinitv_list_id: str) -> requests.models.Response:
        self.logger.info(f"add {ric} to {refinitv_list_id}")

        add_ric_url = RefinitivAPEndpoints.add_ric_to_il_url(refinitv_list_id)
        add_ric_payload = RefinitivAPEndpoints.add_ric_to_il_payload(ric)

        add_ric_response = requests.post(
            url=add_ric_url,
            json=add_ric_payload,
            auth=self.auth,
        )
        add_ric_response.raise_for_status()

        return add_ric_response

    def is_valid_ric(self, ric: str) -> bool:
        validate_ric_url = RefinitivAPEndpoints.validate_ric_url()
        validate_ric_payload = RefinitivAPEndpoints.validate_ric_payload(ric)
        validate_ric_response = requests.post(
            url=validate_ric_url,
            json=validate_ric_payload,
            auth=self.auth,
        )
        validate_ric_response.raise_for_status()
        data = validate_ric_response.json()["value"]
        if not (len(data) > 0 and data[0]["Identifier"] == ric):
            # invalid ric
            return False
        return True

    def bulk_is_valid_ric(self, rics: List[str], custom_options: dict = {}) -> List[str]:
        """Validation for RICs with custom options. Please see the payload to
        see what options u want to override.

        :param rics:
        :param custom_options:
        :return: list of valid rics
        """
        validate_rics_url = RefinitivAPEndpoints.bulk_validate_ric_url()
        validate_rics_payload = RefinitivAPEndpoints.validate_bulk_ric_payload(
            rics=rics, options=custom_options
        )
        validate_rics_response = requests.post(
            url=validate_rics_url,
            json=validate_rics_payload,
            auth=self.auth,
        )
        validate_rics_response.raise_for_status()
        invalid_rics = self.extract_invalid_rics(data=validate_rics_response.json())

        return list(set(rics) - set(invalid_rics))

    def extract_invalid_rics(self, data: dict):
        """Extracts invalid RICs from refinitiv
        InstrumentListValidateIdentifiersWithOptions response.

        :param response:
        :return:
        """
        invalid_rics = []
        messages = data.get("ValidationResult", {}).get("Messages", [])
        for message in messages:
            message_text = message.get("Message", "")
            severity = message.get("Severity")
            if severity in ["Error", "Warning", "Info"]:
                if "RIC" in message_text and "(not found)" in message_text:
                    invalid_ric = message_text.split(", ")[1].split(" (")[0]
                    invalid_rics.append(invalid_ric)

        return invalid_rics

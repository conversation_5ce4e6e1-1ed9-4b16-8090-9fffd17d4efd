# type: ignore
import logging
from api_sdk.auth import Tenancy
from api_sdk.es_dsl.base import TermFilter
from api_sdk.exceptions import AlreadyExists
from api_sdk.repository.syncronous.request_bound import BaseRepository
from api_sdk.search_models.orders import ExecutionsSearch, OrdersSearch
from api_sdk.utils.utils import nested_dict_get as rget
from case_bulk_workflow.base import BaseTask, CaseAttachSpec
from case_bulk_workflow.schemas.case_bulk_in import BulkImportType, CaseBulkOutput, RecordInIdOrKey
from collections import defaultdict, namedtuple
from se_api_svc.repository.comms.common import CommsSearchBase
from se_api_svc.schemas.comms import Call, Email, Message, Text
from se_api_svc.schemas.comms.meeting import Meeting
from se_api_svc.schemas.order import Order
from se_db_utils.database import Database
from se_elastic_schema.static.surveillance import AlertHitStatus

log = logging.getLogger(__name__)

COMMS_MODELS = [Call, Email, Message, Meeting, Text]
ORDER_MODELS = [Order]

MAX_SEARCH_SIZE = 10_000


RunError = namedtuple("RunError", ["id", "critical", "message"])


def run_error(id, message, *args, critical=True) -> RunError:
    """A convenient function to create RunErrors."""
    return RunError(id=id, critical=critical, message=message.format(*args))


class TaskError(Exception):
    def __init__(self, errors):
        self._errors = errors

    def _format_error(self, error):
        return "{}: {}".format(error.id, error.message)

    def __str__(self):
        return ", ".join(self.to_list())

    def to_list(self):
        return [self._format_error(e) for e in self._errors]


class CaseAttachRecordsByIdsTask(BaseTask):
    """If alerts are added to a case, then case record models needs to be
    created and the workflow field of alerts needs to be updated.

    If communications are added to a case, then only case record models
    need to be created.
    """

    def __init__(self, tenancy: Tenancy, record_handler: BaseRepository, db: Database, owner=None):
        super().__init__(tenancy=tenancy, record_handler=record_handler, owner=owner, db=db)

        self.attachable_models = []
        if self.has_tsurv:
            self.attachable_models.extend(ORDER_MODELS)
        if self.has_csurv:
            self.attachable_models.extend(COMMS_MODELS)

    def get_case_records(self, case, task_spec, with_errors=True):
        """Retrieves case records and returns a dictionary with keys as Case
        Record models and values as lists of corresponding model records."""
        errors = []
        case_records = defaultdict(list)

        for record in task_spec.data:
            record_in = task_spec.record_in(
                rget(record, "_source.&key"), rget(record, "_source.&id")
            )
            record_in.data = data = record["_source"]

            # Mark the record so skipped records can be accounted for later.
            task_spec.mark_processed(record_in.identifier)

            try:
                model_type = data.get("&model")
                domain = self.domains.get(model_type)
                log.info(
                    "Fetching record %s (%s)",
                    record_in.identifier,
                    model_type,
                )
                case_record = domain.get_case_record(case.id_, record_in)
                case_records[case_record.model_].append(case_record.to_dict(strip_meta=True))

            except AlreadyExists:
                log.info(
                    "Record %s already linked to the case %s - skipping",
                    record_in.identifier,
                    case.id_,
                )
                errors.append(
                    run_error(
                        record_in.identifier,
                        "Record already exists - skipping",
                        critical=False,
                    )
                )
            except RuntimeError as e:
                log.error("Failed to attach record to case: %s", str(e))
                log.debug(e, exc_info=True)
                errors.append(
                    run_error(
                        record_in.identifier,
                        "Failed to fetch case record: {}",
                        str(e),
                    )
                )
            except Exception as e:
                log.debug(e, exc_info=True)
                errors.append(
                    run_error(
                        record_in.identifier,
                        "Invalid data while fetching record",
                    )
                )

        if with_errors:
            return case_records, errors
        return case_records

    def run(self, bulk_request) -> CaseBulkOutput:
        case_id = bulk_request.case_id
        records_in = bulk_request.records

        log.info(f"Fetching case {case_id}")
        case = self.case_record_repo.get_one_by_id_or_slug(case_id)

        log.info("Fetching the attachable records with the ids from RecordIn")
        task_spec = CaseAttachSpec(records_in=records_in, keep_back_links=True)
        task_spec.data = self.get_records(task_spec, models=self.attachable_models)

        if not task_spec.data:
            log.error("No records were found matching supplied RecordIn")
            record_model = ""
        else:
            record_model = task_spec.data[0]["_source"]["&model"]

        if record_model == Order.Config.model_name:
            # Attach the respective OrderStates' also to case
            _, order_ids = task_spec.get_ids(keys_to_ids=True)
            task_spec.default_filters = [{"terms": {"&parent": order_ids}}]
            order_states = self.get_records(
                task_spec, models=self.attachable_models, use_task_filters=True
            )

            order_states_task_spec = CaseAttachSpec(
                records_in=[RecordInIdOrKey(**{"id_": r["_id"]}) for r in order_states]
            )

            task_spec.data.extend(order_states)
            task_spec.records.update(order_states_task_spec.records)

        case_records, errors = self.get_case_records(case, task_spec)
        missing = (r for r in task_spec.iter_records() if r.identifier not in task_spec.processed)
        errors = errors + [
            run_error(r.identifier, "Record not found", critical=False) for r in missing
        ]

        alert_records = {}
        if task_spec.alerts or task_spec.scenarios:
            alert_filters = []
            if task_spec.alerts:
                alert_filters.append(
                    TermFilter(name="&id", value=task_spec.alerts).to_dict(meta_fields={})
                )
            if task_spec.scenarios:
                alert_filters.append(
                    TermFilter(name="scenarioId", value=task_spec.scenarios).to_dict(meta_fields={})
                )

            workflow = {
                "status": AlertHitStatus.UNDER_INVESTIGATION,
                "caseId": case.id_,
                "caseSlug": case.slug,
            }

            # back-linking case to alert
            alert_records = self.get_back_link_records(
                task_spec=task_spec, filters=alert_filters, workflow=workflow
            )

        if errors:
            logging.info(f"Errors found during e: \n{TaskError(errors).to_list()}")

        return CaseBulkOutput(case_records=case_records, alert_records=alert_records)


class CaseAttachRecordsByFilterTask(BaseTask):
    def __init__(
        self,
        tenancy: Tenancy,
        record_handler: BaseRepository,
        db: Database,
    ):
        super().__init__(tenancy=tenancy, record_handler=record_handler, db=db)

    def run(self, bulk_request, raise_errors=False) -> CaseBulkOutput:
        filter_def = bulk_request.filter
        case_id = bulk_request.case_id

        log.info(f"Fetching case {case_id}")
        case = self.case_record_repo.get_one_by_id_or_slug(case_id)

        if filter_def.type == BulkImportType.COMMS:
            log.info("For the given filter, searching for communication records")
            results = self.scan_records(
                index=self.comms_repo.index_for_record_model(COMMS_MODELS),
                search_model=CommsSearchBase(**filter_def.to_params().as_search_kwargs()),
            )
        else:
            # Note: This logic should align with the process used to populate the Orders table on the UI.  # noqa: E501

            # For the given filter, searching for Order state records to get the order id's
            executions_search_model = ExecutionsSearch(**filter_def.to_params().as_search_kwargs())
            executions = self.orders_repo.repo.get_aggs(
                record_model=[Order],
                search_model=executions_search_model,
                aggs={
                    "ORDER_IDS": {
                        "terms": {
                            "field": "&parent",
                            "size": MAX_SEARCH_SIZE,
                        }
                    }
                },
            )
            order_ids = [
                execution["key"] for execution in executions.iter_raw_bucket_agg("ORDER_IDS")
            ] or None
            log.info("For the given filter, searching for Order records")
            orders = self.scan_records(
                index=self.comms_repo.index_for_record_model(ORDER_MODELS),
                search_model=OrdersSearch(
                    **filter_def.to_params().as_search_kwargs(), order_ids=order_ids
                ),
            )
            order_ids = [order["&id"] for order in orders] or None

            log.info("For the retrieved orders, searching for corresponding order state records")
            order_states = self.scan_records(
                index=self.comms_repo.index_for_record_model(ORDER_MODELS),
                search_model=ExecutionsSearch(order_ids=order_ids),
            )
            results = orders + order_states

        case_records = defaultdict(list)
        for data in results:
            try:
                domain = self.domains.get(data.get("&model"))
                log.info(
                    "Fetching record %s (%s)",
                    data.get("&id"),
                    data.get("&model"),
                )
                case_record = domain.get_case_record(case.id_, RecordInIdOrKey(id_=data.get("&id")))
                case_records[case_record.model_].append(case_record.to_dict())

            except AlreadyExists:
                log.warning("Record %s already linked to case - skipping", data.get("&id"))
            except RuntimeError as e:
                log.error("Failed to fetch record", str(e))
                log.debug(e, exc_info=True)

        return CaseBulkOutput(case_records=case_records)

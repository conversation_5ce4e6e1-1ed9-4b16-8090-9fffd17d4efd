# type: ignore
import json
import logging
from api_sdk.auth import Tenancy
from api_sdk.repository.syncronous.request_bound import BoundRepository, TenantConfiguration
from case_bulk_workflow.case_records_domain import (
    CaseCallsDomain,
    CaseEmailsDomain,
    CaseMeetingsDomain,
    CaseMessagesDomain,
    CaseOrdersDomain,
    CaseTextsDomain,
)
from case_bulk_workflow.repository.case_records import CaseRecordRepository
from case_bulk_workflow.schemas.case_bulk_in import RecordInIdOrKey
from case_bulk_workflow.schemas.cases import FakeAttachment
from case_bulk_workflow.schemas.common import model_map
from collections import defaultdict
from se_api_svc.repository.cases.cases import CasesRepository
from se_api_svc.repository.comms import (
    CallsRepository,
    EmailsRepository,
    MessagesRepository,
    TextsRepository,
)
from se_api_svc.repository.comms.common import CommsRepository
from se_api_svc.repository.comms.repos import MeetingsRepository
from se_api_svc.repository.order.orders import OrdersRepository
from se_api_svc.repository.scenarios import ScenariosRepository
from se_api_svc.repository.surveillance.alerts import AlertsRepository
from se_db_utils.database import Database
from se_elastic_schema.models.tenant.surveillance.communication_alert import (
    CommunicationAlert,
)
from se_elastic_schema.models.tenant.surveillance.market_abuse_alert import (
    MarketAbuseAlert,
)
from se_elastic_schema.models.tenant.surveillance.market_abuse_scenario_tag import (
    MarketAbuseScenarioTag,
)
from se_elastic_schema.models.tenant.surveillance.order_alert import OrderAlert
from se_elastic_schema.static.reference import Module
from starlette.requests import Request
from typing import Dict, List, Optional, Union

log = logging.getLogger(__name__)

MIN_ITEMS_FOR_SCROLL = 250


class CaseAttachSpec:
    def __init__(self, records_in, keep_back_links=False):
        self.keep_back_links = keep_back_links
        records = [self.normalise_record(r) for r in records_in]
        self.alerts = list(
            set([r.alert_id for r in records if r is not None and r.alert_id is not None])
        )
        self.scenarios = list(
            set([r.scenario_id for r in records if r is not None and r.scenario_id is not None])
        )
        self.records = {r.identifier: self.strip(r) for r in records if r is not None}
        self.data = None
        self.processed = []
        self.default_filters = []

    def normalise_record(self, record: Union[RecordInIdOrKey, Dict]) -> Optional[RecordInIdOrKey]:
        if type(record) is dict:  # noqa: E721
            try:
                return RecordInIdOrKey(**record)
            except Exception:
                log.warning("Invalid RecordIn: '%s'", json.dumps(record))
                return None
        return record

    def iter_records(self):
        return list(self.records.values())

    def record_in(self, key, id):
        return self.records.get(key, self.records.get(id))

    def strip(self, record: RecordInIdOrKey) -> RecordInIdOrKey:
        if self.keep_back_links:
            return record
        record.alert_id = None
        record.scenario_id = None
        return record

    def __len__(self):
        return len(self.records)

    def mark_processed(self, identifier: str):
        self.processed.append(identifier)

    def get_ids(self, keys_to_ids=False):
        ids, filter_key = [], ""
        for r in self.iter_records():
            if r.uses_key():
                filter_key = "&key"
                if keys_to_ids:
                    id = r.identifier.split(":", 1)[1].rsplit(":", 1)[0]
                    ids.append(id)
                else:
                    ids.append(r.identifier)
            else:
                filter_key = "&id"
                ids.append(r.identifier)

        return filter_key, ids


class BaseTask:
    def __init__(
        self,
        tenancy: Tenancy,
        record_handler: BoundRepository,
        db: Database,
        owner: Optional[Request] = None,
    ):
        self.tenancy = tenancy
        self.owner = owner
        self.es_repo = record_handler.es_repo
        self.db = db
        # Using subscribedModules of tenant_config, such that indices and models can be used appropriately. # noqa: E501
        tenant_config = record_handler.get_one(TenantConfiguration)
        self.has_csurv = (
            Module.COMMS_SURVEILLANCE in tenant_config.subscribedModules
            or Module.COMMUNICATIONS in tenant_config.subscribedModules
        )
        self.has_tsurv = (
            Module.TRADE_SURVEILLANCE in tenant_config.subscribedModules
            or (
                Module.BEST_EXECUTION in tenant_config.subscribedModules
                and Module.ORDERS in tenant_config.subscribedModules
            )
            or Module.ORDERS in tenant_config.subscribedModules
        )

        self.case_record_repo = CaseRecordRepository(
            tenancy=tenancy, es_repo=record_handler.es_repo, owner=self.owner, db=self.db
        )
        self.cases_repo = CasesRepository(
            case_record_repo=self.case_record_repo,
            tenancy=tenancy,
            es_repo=self.es_repo,
            attachments=FakeAttachment(),
            api_config=None,
            owner=self.owner,
            db=self.db,
        )
        self.comms_repo = CommsRepository(repo=record_handler)
        self.orders_repo = OrdersRepository(repo=record_handler)
        self.alerts_repo = AlertsRepository(repo=record_handler)
        self.scenarios_repo = ScenariosRepository(repo=record_handler)

        common_params = {
            "record_handler": record_handler,
            "cases_repo": self.cases_repo,
            "scenario_repo": self.scenarios_repo,
            "alerts_repo": self.alerts_repo,
            "tenancy": self.tenancy,
            "es_repo": self.es_repo,
            "db": self.db,
        }

        self.domains = {
            "Order": CaseOrdersDomain(
                orders_repo=self.orders_repo, owner=self.owner, **common_params
            ),
            "Call": CaseCallsDomain(
                calls_repo=CallsRepository(repo=record_handler), owner=self.owner, **common_params
            ),
            "Message": CaseMessagesDomain(
                messages_repo=MessagesRepository(repo=record_handler),
                owner=self.owner,
                **common_params,
            ),
            "Email": CaseEmailsDomain(
                emails_repo=EmailsRepository(repo=record_handler), owner=self.owner, **common_params
            ),
            "Text": CaseTextsDomain(
                texts_repo=TextsRepository(repo=record_handler), owner=self.owner, **common_params
            ),
            "Meeting": CaseMeetingsDomain(
                meeting_repo=MeetingsRepository(repo=record_handler),
                owner=self.owner,
                **common_params,
            ),
        }

    async def run(self, **kwargs):
        """Executes the task."""
        raise NotImplementedError

    @staticmethod
    def custom_deserializer(hit):
        model_name = hit["_source"]["&model"]
        src = hit["_source"]

        model = model_map.get(model_name, None)
        if model:
            return model(**src)

        return src

    def scan_records(self, index, search_model, **params):
        query = search_model.build(
            index=index, meta_fields=self.es_repo.meta_fields, paginate=False
        ).to_dict()
        log.info(f"Against {index}, executing query for scroll: {json.dumps(query, default=str)}")

        return self.es_repo.scroll_query(query=query, index=index)

    def get_records(self, task_spec: CaseAttachSpec, models, use_task_filters=False):
        """Retrieves attachable records using the ids provided."""
        terms = [{"terms": {"&model": [model.__name__ for model in models]}}]

        # consider only not expired records
        terms.append({"bool": {"must_not": {"exists": {"field": "&expiry"}}}})

        if not use_task_filters:
            filter_key, ids = task_spec.get_ids()
            if ids:
                terms.append({"terms": {filter_key: ids}})
        else:
            terms.append(*task_spec.default_filters)

        query = {"sort": [{"&model": "desc"}], "query": {"bool": {"filter": terms}}}
        indices = self.comms_repo.repo.index_for_record_model(models)
        logging.info(f"Against index {indices}; Executing query: {json.dumps(query)}")

        return [hit for hit in self.es_repo.scan(query, indices)]

    def get_alert_records(self, filters: List[Dict], models):
        """Retrieves alert records based on specified id filter and models."""
        query = {
            "query": {
                "bool": {
                    "must": [{"terms": {"&model": [model.__name__ for model in models]}}, *filters],
                    "must_not": [{"exists": {"field": "&expiry"}}],
                }
            }
        }

        # Comms repo is just used to get indices
        indices = self.comms_repo.repo.index_for_record_model(models)
        logging.info(f"Against index {indices}; Executing query: {json.dumps(query)}")

        yield from self.es_repo.scan(query, indices)

    def get_back_link_records(
        self, task_spec, filters: List[Dict], workflow: Dict, all_alert_models=False
    ):
        """Fetches the alert records using the ids provided and the updates the
        workflow of alert records."""
        ids = task_spec.alerts or task_spec.scenarios
        if not ids:
            return {}

        back_link_models = []
        if task_spec.alerts:
            # alert ids provided in payload can be of either of communication or non-market-abuse alerts # noqa: E501
            if self.has_csurv:
                back_link_models.append(CommunicationAlert)
            if self.has_tsurv:
                back_link_models.append(OrderAlert)

        if task_spec.scenarios:
            back_link_models.extend([MarketAbuseAlert, MarketAbuseScenarioTag])

        if all_alert_models:
            back_link_models = [
                CommunicationAlert,
                OrderAlert,
                MarketAbuseAlert,
                MarketAbuseScenarioTag,
            ]

        alert_records = defaultdict(list)
        for record in self.get_alert_records(filters=filters, models=back_link_models):
            record = record["_source"]

            workflow["updatedBy"] = self.comms_repo.repo.tenancy.principal
            record["workflow"].update(workflow)

            alert_records[record["&model"]].append(record)

        return alert_records

import numpy as np
import pandas as pd
import pytest
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.mapping_overrides.example.order_aladdin_v2_schroders_market_side_mappings import (  # noqa: E501
    OrderAladdinV2SchrodersMarketSideMappings,
)
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.order_aladdin_v2_base_mappings import (  # noqa: E501
    OrderAladdinV2BaseMappings,
)
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.order_aladdin_v2_client_side_mappings import (  # noqa: E501
    OrderAladdinV2ClientSideMappings,
)
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.order_aladdin_v2_market_side_mappings import (  # noqa: E501
    OrderAladdinV2MarketSideMappings,
)
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.static import (
    DevColumns,
    FileTypes,
    FillSourceColumns,
    OrderDetailSourceColumns,
    OrderSourceColumns,
    PlacementSourceColumns,
    SecGroupValues,
    SecTypeValues,
    TransactionSourceColumns,
)
from aries_se_trades_tasks.order.transformations.order_transform_maps import (
    order_aladdin_v2_client_side_map,
    order_aladdin_v2_market_side_map,
)
from freezegun import freeze_time
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_test_utils.aws_helpers.s3_test_helpers import create_and_add_objects_to_s3_bucket
from moto import mock_aws
from pathlib import Path
from se_elastic_schema.static.mifid2 import OptionType
from se_trades_tasks.order.static import OrderColumns, add_prefix
from se_trades_tasks.order_and_tr.static import AssetClass
from typing import List, Type

BUCKET_NAME = "test.dev.steeleye.co"
SCRIPT_PATH: Path = Path(__file__).parent
LOCAL_BUCKET_PATH = Path(__file__).parent.joinpath("data/buckets", BUCKET_NAME)

mock_aiobotocore_convert_to_response_dict()


class MockESClient:
    def __init__(self, mock_es_scroll: pd.DataFrame):
        self.mock_es_scroll = mock_es_scroll

    def scroll(self, **kwargs):
        return self.mock_es_scroll


@mock_aws
@freeze_time(time_to_freeze="2024-02-10 14:20:00.000000+00:00")
class TestOrderAladdinV2BaseMappings:
    @staticmethod
    def upload_mock_s3_data():
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME, source_path=LOCAL_BUCKET_PATH)

    def test_instrument_name_fallback_from_cache(self):
        self.upload_mock_s3_data()
        source_frame = pd.DataFrame(
            {
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID): [
                    "order_id_1",
                    "order_id_4",
                    "order_id_5",
                ],
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    "orig_order_id_z",
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "status",
                ]
                * 3,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 3,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                ]
                * 3,
            }
        )

        target_frame = pd.DataFrame(
            {
                DevColumns.INSTRUMENT_FULL_NAME: [pd.NA, "instrument name", pd.NA],
            }
        )

        class_instance = OrderAladdinV2BaseMappings(
            source_file_uri="foo",
            file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
            "foo_FI.20241103_market_side_orders_batch_0.csv",
            tenant="test",
            es_client=MockESClient(mock_es_scroll=pd.DataFrame()),
            order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/random_id/file_splitter_by_criteria/"
            "instrument_cache.json",
            source_frame=source_frame,
        )

        class_instance.target_df = target_frame
        class_instance._instrument_name_fallback_from_cache()

        assert class_instance.target_df[DevColumns.INSTRUMENT_FULL_NAME].tolist() == [
            "instrument full name 1",  # was mapped from the cache
            "instrument name",  # was already present in the target frame
            np.nan,  # was not present in the target frame and was not mapped from the cache
        ]

    def test_instrument_name_fallback_from_empty_cache(self):
        self.upload_mock_s3_data()
        source_frame = pd.DataFrame(
            {
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID): [
                    "order_id_3",
                    "order_id_4",
                    "order_id_5",
                ],
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    "orig_order_id_z",
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "status",
                ]
                * 3,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 3,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                ]
                * 3,
            }
        )

        target_frame = pd.DataFrame(
            {
                DevColumns.INSTRUMENT_FULL_NAME: [pd.NA] * 3,
            }
        )

        class_instance = OrderAladdinV2BaseMappings(
            source_file_uri="foo",
            file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
            "foo_FI.20241103_market_side_orders_batch_0.csv",
            tenant="test",
            es_client=MockESClient(mock_es_scroll=pd.DataFrame()),
            order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/random_id/file_splitter_by_criteria/"
            "empty_instrument_cache.json",
            source_frame=source_frame,
        )

        class_instance.target_df = target_frame
        class_instance._instrument_name_fallback_from_cache()

        assert (
            class_instance.target_df[DevColumns.INSTRUMENT_FULL_NAME].tolist()
            == [
                pd.NA,
            ]
            * 3
        )

    def test_expiry_date(self):
        self.upload_mock_s3_data()
        source_frame = pd.DataFrame(
            {
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.MATURITY): [
                    "10/15/2024",  # M-D-Y
                    "10/05/2024",  # M-D-Y
                    "11/24/2025",  # M-D-Y
                    pd.NA,
                ],
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    "orig_order_id_z",
                    "orig_order_id_q",
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "status",
                ]
                * 4,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 4,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                ]
                * 4,
            }
        )

        class_instance = OrderAladdinV2BaseMappings(
            source_file_uri="foo",
            file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
            "foo_FI.20241103_market_side_orders_batch_0.csv",
            tenant="test",
            es_client=MockESClient(mock_es_scroll=pd.DataFrame()),
            order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/random_id/file_splitter_by_criteria/"
            "instrument_cache.json",
            source_frame=source_frame,
        )

        result = class_instance._get_expiry_date()

        assert result.tolist() == [
            "2024-10-15",
            "2024-10-05",
            "2025-11-24",
            pd.NA,
        ]

    def test_get_instrument_classification(self):
        self.upload_mock_s3_data()
        sec_group_col = add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP)
        sec_type_col = add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE)

        source_frame = pd.DataFrame(
            {
                sec_group_col: [
                    SecGroupValues.CASH,
                    SecGroupValues.FUND,
                    pd.NA,
                    SecGroupValues.IBND,
                    SecGroupValues.IBND,
                    SecGroupValues.SYNTH,
                    pd.NA,
                    pd.NA,
                ],
                sec_type_col: [
                    pd.NA,
                    SecTypeValues.CLOSED_END,
                    SecTypeValues.CLOSED_END,
                    SecTypeValues.GOVT,
                    pd.NA,
                    SecTypeValues.SWAPTION,
                    SecTypeValues.SWAPTION,
                    pd.NA,
                ],
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    "orig_order_id_z",
                    "orig_order_id_q",
                    "orig_order_id_w",
                    "orig_order_id_e",
                    "orig_order_id_r",
                    "orig_order_id_t",
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "status",
                ]
                * 8,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 8,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                ]
                * 8,
            }
        )

        class_instance = OrderAladdinV2BaseMappings(
            source_file_uri="foo",
            file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
            "foo_FI.20241103_market_side_orders_batch_0.csv",
            tenant="test",
            es_client=MockESClient(mock_es_scroll=pd.DataFrame()),
            order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/random_id/file_splitter_by_criteria/"
            "instrument_cache.json",
            source_frame=source_frame,
        )

        result = class_instance._get_instrument_classification()

        assert result.tolist() == [
            "DYXXXX",
            "CIOXXX",
            pd.NA,
            "DBXTXX",
            pd.NA,
            "SCICXX",
            pd.NA,
            pd.NA,
        ]

    def test_get_option_strike_price(self):
        self.upload_mock_s3_data()
        source_frame = pd.DataFrame(
            {
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.TRADE_COUPON): [
                    0,  # strike price can't be 0, hence is set to pd.NA
                    0,
                    8,  # the strike price gets mapped to 8.0
                    8,  # the strike price is set to pd.NA because this is not an Option
                    9.23,
                    9.23,  # the strike price is set to pd.NA because this is not an Option
                    pd.NA,  # assert nulls are handled correctly
                    pd.NA,
                    pd.NA,  # assert nulls are handled correctly even when the SecGroup is null
                ],
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP): [
                    SecGroupValues.OPTION,
                    SecGroupValues.FUTURE,
                    SecGroupValues.OPTION,
                    SecGroupValues.FUTURE,
                    SecGroupValues.OPTION,
                    SecGroupValues.FUTURE,
                    SecGroupValues.OPTION,
                    SecGroupValues.FUTURE,
                    pd.NA,
                ],
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    "orig_order_id_z",
                    "orig_order_id_q",
                    "orig_order_id_w",
                    "orig_order_id_e",
                    "orig_order_id_r",
                    "orig_order_id_t",
                    "orig_order_id_y",
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "status",
                ]
                * 9,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 9,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                ]
                * 9,
            }
        )
        expected_result = [
            pd.NA,
            pd.NA,
            8.0,
            pd.NA,
            9.23,
            pd.NA,
            pd.NA,
            pd.NA,
            pd.NA,
        ]

        class_instance = OrderAladdinV2BaseMappings(
            source_file_uri="foo",
            file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
            "foo_FI.20241103_market_side_orders_batch_0.csv",
            tenant="test",
            es_client=MockESClient(mock_es_scroll=pd.DataFrame()),
            order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/random_id/file_splitter_by_criteria/"
            "instrument_cache.json",
            source_frame=source_frame,
        )

        result = class_instance._get_option_strike_price()
        assert result.tolist() == expected_result

    @pytest.mark.parametrize(
        "asset_class,expected_result",
        [
            ("EQ", ["EUR", "EUR", pd.NA, "CNY"]),  # non-fx case
            ("FX", ["JPY", "JPY", pd.NA, "CNY"]),  # fx case
        ],
    )
    def test_get_currency(self, asset_class, expected_result):
        self.upload_mock_s3_data()
        source_frame = pd.DataFrame(
            {
                add_prefix(FileTypes.ORDER, OrderSourceColumns.PRICE_CCY): [
                    "EUR",
                    "EUR",
                    pd.NA,
                    "CNH",
                ],
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.FX_BASE_CCY): [
                    "JPY",
                    "JPY",
                    pd.NA,
                    "CNH",
                ],
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    "orig_order_id_z",
                    "orig_order_id_q",
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "status",
                ]
                * 4,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 4,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                ]
                * 4,
            }
        )

        class_instance = OrderAladdinV2BaseMappings(
            source_file_uri="foo",
            file_uri=f"s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            f"2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
            f"foo_{asset_class}.20241103_market_side_orders_batch_0.csv",
            tenant="test",
            es_client=MockESClient(mock_es_scroll=pd.DataFrame()),
            order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/random_id/file_splitter_by_criteria/"
            "instrument_cache.json",
            source_frame=source_frame,
        )

        result = class_instance._get_currency()
        assert result.tolist() == expected_result

    @pytest.mark.parametrize(
        "asset_class,expected_result",
        [
            ("EQ", ["EUR", "EUR", pd.NA, "CNY"]),  # non-fx case
            ("FX", ["JPY", "JPY", pd.NA, "CNY"]),  # fx case
        ],
    )
    def test_get_notional_currency_2(self, asset_class, expected_result):
        self.upload_mock_s3_data()
        source_frame = pd.DataFrame(
            {
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1): [
                    "JPY/EUR",
                    "JPY/EUR",
                    pd.NA,
                    "JPY/CNH",
                ],
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.FX_TERM_CCY): [
                    "JPY",
                    "JPY",
                    pd.NA,
                    "CNH",
                ],
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    "orig_order_id_z",
                    "orig_order_id_q",
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "status",
                ]
                * 4,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 4,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                ]
                * 4,
            }
        )

        class_instance = OrderAladdinV2BaseMappings(
            source_file_uri="foo",
            file_uri=f"s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            f"2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
            f"foo_{asset_class}.20241103_market_side_orders_batch_0.csv",
            tenant="test",
            es_client=MockESClient(mock_es_scroll=pd.DataFrame()),
            order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/random_id/file_splitter_by_criteria/"
            "instrument_cache.json",
            source_frame=source_frame,
        )

        result = class_instance._get_notional_currency_2()
        assert result.tolist() == expected_result

    def test_order_identifiers_order_routing_code(self):
        self.upload_mock_s3_data()
        source_frame = pd.DataFrame(
            {
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    "orig_order_id_z",
                    "orig_order_id_q",
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "status",
                ]
                * 4,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 4,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                ]
                * 4,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.TRADE_NUM): [
                    "1257",
                    "A642",
                    "69.64",
                    pd.NA,
                ],
            }
        )
        expected_result = [
            "1257",
            "A642",
            "69.64",
            pd.NA,
        ]

        class_instance = OrderAladdinV2BaseMappings(
            source_file_uri="foo",
            file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
            "foo_EQ.20241103_market_side_orders_batch_0.csv",
            tenant="test",
            es_client=MockESClient(mock_es_scroll=pd.DataFrame()),
            order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/random_id/file_splitter_by_criteria/"
            "instrument_cache.json",
            source_frame=source_frame,
        )

        class_instance.order_identifiers_order_routing_code()
        assert (
            class_instance.target_df[OrderColumns.ORDER_IDENTIFIERS_ORDER_ROUTING_CODE].tolist()
            == expected_result
        )

    def test_schroders_override_skip_logic(self, mocker):
        self.upload_mock_s3_data()

        # data needed for the class constructor
        source_frame = pd.DataFrame(
            {
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    "orig_order_id_z",
                    "orig_order_id_q",
                    "orig_order_id_w",
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "status",
                ]
                * 5,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 5,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                ]
                * 5,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP): [
                    SecGroupValues.BND,
                    SecGroupValues.FX,
                    SecGroupValues.FUTURE,
                    SecGroupValues.FX,
                    SecGroupValues.CASH,
                ],
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE): [
                    pd.NA,
                    SecTypeValues.CSWAP,
                    SecTypeValues.CLOSED_END,
                    SecTypeValues.SPOT,
                    SecTypeValues.BA,
                ],
                add_prefix(FileTypes.FILL, FillSourceColumns.FILL_ID): [
                    "fill_id_1",
                    "fill_id_2",
                    "fill_id_3",
                    "fill_id_4",
                    "fill_id_5",
                ],
                add_prefix(FileTypes.FILL, FillSourceColumns.PLACEMENT_ID): [
                    "placement_id_1",
                    "placement_id_2",
                    "placement_id_3",
                    "placement_id_4",
                    "placement_id_5",
                ],
            }
        )

        # Note: the market-side rows are sorted by `fill.fillId`
        for child_class, expected_index in zip(
            [
                order_aladdin_v2_client_side_map.map["schroders"],
                order_aladdin_v2_market_side_map.map["schroders"],
            ],
            [[0, 1, 2, 4], [4, 2, 1, 0]],
        ):
            class_instance = child_class(
                source_file_uri="foo",
                file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
                "2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
                "foo_FX.20241103_market_side_orders_batch_0.csv",
                tenant="test",
                es_client=MockESClient(mock_es_scroll=pd.DataFrame()),
                order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
                "2025/02/10/random_id/file_splitter_by_criteria/"
                "instrument_cache.json",
                source_frame=source_frame,
            )

            # FI skip logic does not apply to FX input files and vice versa
            # https://steeleye.atlassian.net/browse/ON-4586?focusedCommentId=194305
            assert class_instance.source_frame.index.tolist() == expected_index

    def test_schroders_override_skip_logic_consecutive_asset_class_check(self, mocker):
        self.upload_mock_s3_data()

        # data needed for the class constructor
        source_frame = pd.DataFrame(
            {
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    "orig_order_id_z",
                    "orig_order_id_q",
                    "orig_order_id_w",
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "status",
                ]
                * 5,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 5,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                ]
                * 5,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP): [
                    SecGroupValues.BND,
                    SecGroupValues.FX,
                    SecGroupValues.FUTURE,
                    SecGroupValues.FX,
                    SecGroupValues.CASH,
                ],
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE): [
                    pd.NA,
                    SecTypeValues.CSWAP,
                    SecTypeValues.CLOSED_END,
                    SecTypeValues.SPOT,
                    SecTypeValues.BA,
                ],
                add_prefix(FileTypes.FILL, FillSourceColumns.FILL_ID): [
                    "fill_id_1",
                    "fill_id_2",
                    "fill_id_3",
                    "fill_id_4",
                    "fill_id_5",
                ],
                add_prefix(FileTypes.FILL, FillSourceColumns.PLACEMENT_ID): [
                    "placement_id_1",
                    "placement_id_2",
                    "placement_id_3",
                    "placement_id_4",
                    "placement_id_5",
                ],
            }
        )

        # Note: the market-side rows are sorted by `fill.fillId`
        for child_class, expected_index in zip(
            [
                order_aladdin_v2_client_side_map.map["schroders"],
                order_aladdin_v2_market_side_map.map["schroders"],
            ],
            [[0, 1, 2, 3], [3, 2, 1, 0]],
        ):
            class_instance = child_class(
                source_file_uri="foo",
                file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
                "2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
                "foo_FI.20241103_market_side_orders_batch_0.csv",
                tenant="test",
                es_client=MockESClient(mock_es_scroll=pd.DataFrame()),
                order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
                "2025/02/10/random_id/file_splitter_by_criteria/"
                "instrument_cache.json",
                source_frame=source_frame,
            )

            # FX skip logic does not apply to FI input files and vice versa
            assert class_instance.source_frame.index.tolist() == expected_index

    def test_get_option_type(self):
        self.upload_mock_s3_data()
        source_frame = pd.DataFrame(
            {
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1): [
                    "DEC24 ASML NA C @ 860.2962",
                    "DEC24 ASML NA P @ 860.2962",
                    "abc",
                    pd.NA,
                    "foo",
                ],
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    "orig_order_id_z",
                    "orig_order_id_q",
                    "orig_order_id_q",
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "status",
                ]
                * 5,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 5,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                ]
                * 5,
            }
        )
        expected_result = [
            OptionType.CALL,
            OptionType.PUTO,
            OptionType.OTHR,
            pd.NA,
            pd.NA,  # this is not an Option
        ]

        class_instance = OrderAladdinV2BaseMappings(
            source_file_uri="foo",
            file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
            "foo_FI.20241103_market_side_orders_batch_0.csv",
            tenant="test",
            es_client=MockESClient(mock_es_scroll=pd.DataFrame()),
            order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/random_id/file_splitter_by_criteria/"
            "instrument_cache.json",
            source_frame=source_frame,
        )

        class_instance.pre_process_df = pd.DataFrame(
            {
                DevColumns.ASSET_CLASS: [
                    AssetClass.OPTION,
                    AssetClass.OPTION,
                    AssetClass.OPTION,
                    AssetClass.FUTURE,
                    AssetClass.FUTURE,
                ]
            }
        )

        result = class_instance._get_option_type()
        assert result.tolist() == expected_result

    def test_get_buy_sell(self):
        self.upload_mock_s3_data()
        source_frame = pd.DataFrame(
            {
                add_prefix(FileTypes.ORDER, OrderSourceColumns.TRAN_TYPE): [
                    "BUY",  # is mapped directly by the parent class's method
                    "bar",  # has positive quantity, hence is mapped to "1" (buy)
                    pd.NA,  # has negative quantity, hence is mapped to "2" (sell)
                    "foo",  # has positive quantity, hence is mapped to "1" (buy)
                    "abc",  # has positive quantity, hence is mapped to "1" (buy)
                ],
                add_prefix(
                    FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_DETAIL_QUANTITY
                ): [
                    1000.0,
                    50.0,
                    -20.0,
                    3,
                    23.6,
                ],
                add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_QUANTITY): [
                    1000.0,
                    50.0,
                    0,  # is 0, hence for MarketSide Orders we need to use placementQuantityFilled
                    0,  # is 0, hence for MarketSide Orders we need to use placementQuantityFilled
                    23.6,
                ],
                add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_QUANTITY_FILLED): [
                    1000.0,
                    50.0,
                    -3.3,
                    5.0,
                    23.6,
                ],
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    "orig_order_id_z",
                    "orig_order_id_q",
                    "orig_order_id_w",
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "status",
                ]
                * 5,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 5,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                ]
                * 5,
            }
        )
        expected_result = [
            "1",
            "1",
            "2",
            "1",
            "1",
        ]

        for klass in [OrderAladdinV2ClientSideMappings, OrderAladdinV2MarketSideMappings]:
            class_instance = klass(
                source_file_uri="foo",
                file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
                "2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
                "foo_FI.20241103_market_side_orders_batch_0.csv",
                tenant="test",
                es_client=MockESClient(mock_es_scroll=pd.DataFrame()),
                order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
                "2025/02/10/random_id/file_splitter_by_criteria/"
                "instrument_cache.json",
                source_frame=source_frame,
            )

            result = class_instance._get_buy_sell()
            assert result.tolist() == expected_result

    def test_timestamp_cache(self):
        self.upload_mock_s3_data()
        source_frame = pd.DataFrame(
            {
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ACTIVATED_TIMESTAMP_UTC): [
                    "2024-10-22T13:21:33.700Z",
                    pd.NA,  # missing timestamp, must get it from the cache by orderDetail.orderId
                    "2024-10-22T13:22:33.700Z",
                    "2024-10-22T13:20:33.700Z",
                    "2024-10-22T13:23:33.700Z",
                ],
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    "orig_order_id_z",
                    "orig_order_id_q",
                    "orig_order_id_w",
                ],
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID): [
                    "order_id_x",
                    "order_id_y",
                    "order_id_z",
                    "order_id_q",
                    "order_id_w",
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "status",
                ]
                * 5,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 5,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                ]
                * 5,
            }
        )
        date_expected_result = [
            "2024-10-22",
            "2024-09-22",
            "2024-10-22",
            "2024-10-22",
            "2024-10-22",
        ]
        timestamp_expected_result = [
            "2024-10-22T13:21:33.700000Z",
            "2024-09-22T13:21:33.700000Z",
            "2024-10-22T13:22:33.700000Z",
            "2024-10-22T13:20:33.700000Z",
            "2024-10-22T13:23:33.700000Z",
        ]

        class_instance = OrderAladdinV2MarketSideMappings(
            source_file_uri="foo",
            file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
            "foo_FI.20241103_market_side_orders_batch_0.csv",
            tenant="test",
            es_client=MockESClient(mock_es_scroll=pd.DataFrame()),
            order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/random_id/file_splitter_by_criteria/"
            "instrument_cache.json",
            source_frame=source_frame,
        )

        class_instance.date()
        class_instance.timestamps_order_received()
        assert class_instance.target_df[OrderColumns.DATE].tolist() == date_expected_result
        assert (
            class_instance.target_df[OrderColumns.TIMESTAMPS_ORDER_RECEIVED].tolist()
            == timestamp_expected_result
        )

    @pytest.mark.parametrize(
        "klass,expected_results",
        [
            (
                OrderAladdinV2SchrodersMarketSideMappings,
                [
                    30.4,
                    40.2,
                    40.3,
                    40.5,
                    40.6,
                ],
            ),  # flattening case
            (
                OrderAladdinV2MarketSideMappings,
                [
                    0,
                    30.3,
                    50.2,
                    50.4,
                    50.5,
                ],
            ),  # default case
        ],
    )
    def test_price_forming_data_price(
        self, mocker, klass: Type[OrderAladdinV2BaseMappings], expected_results: List
    ):
        self.upload_mock_s3_data()

        # data needed for the class constructor
        source_frame = pd.DataFrame(
            {
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    "orig_order_id_z",
                    "orig_order_id_q",
                    "orig_order_id_w",
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "status",
                ]
                * 5,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 5,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                ]
                * 5,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP): [pd.NA] * 5,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE): [pd.NA] * 5,
                add_prefix(FileTypes.FILL, FillSourceColumns.FILL_ID): [
                    "fill_id_1",
                    "fill_id_2",
                    "fill_id_3",
                    "fill_id_4",
                    "fill_id_5",
                ],
                add_prefix(FileTypes.FILL, FillSourceColumns.PLACEMENT_ID): [
                    "placement_id_1",
                    "placement_id_2",
                    "placement_id_3",
                    "placement_id_4",
                    "placement_id_5",
                ],
                add_prefix(FileTypes.FILL, FillSourceColumns.EXECUTED_PRICE): [
                    50.2,
                    pd.NA,  # fallback to order.avgPrice
                    50.4,
                    50.5,
                    pd.NA,  # fallback to order.avgPrice (pd.NA as well)
                ],
                add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.AVG_PRICE): [
                    40.2,
                    40.3,
                    pd.NA,
                    40.5,
                    40.6,
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.AVG_PRICE): [
                    30.2,
                    30.3,
                    30.4,
                    30.5,
                    pd.NA,
                ],
            }
        )

        class_instance = klass(
            source_file_uri="foo",
            file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
            "foo_FI.20241103_market_side_orders_batch_0.csv",
            tenant="test",
            es_client=MockESClient(mock_es_scroll=pd.DataFrame()),
            order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/random_id/file_splitter_by_criteria/"
            "instrument_cache.json",
            source_frame=source_frame,
        )

        class_instance.price_forming_data_price()
        results = sorted(
            class_instance.target_df[OrderColumns.PRICE_FORMING_DATA_PRICE].fillna(0).to_list()
        )

        assert results == expected_results

from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.order_aladdin_v2_market_side_mappings import (  # noqa: E501
    OrderAladdinV2MarketSideMappings,
)
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.static import (
    FileTypeAssetClass,
    SecGroupValues,
    SecTypeValues,
)


class OrderAladdinV2SchrodersMarketSideMappings(OrderAladdinV2MarketSideMappings):
    def __init__(self, **kwargs):
        """
        Schroders have requested to not ingest any Order where the Asset Class is FX Spot
        This override is filtering out certain asset classes and then
        calling the parent class's constructor.
        This changes the shape of the source_frame, but that is not
        a problem as this is the first step of the
        transformation/mappings, hence all downstream operations will be executed
        against a DataFrame with a consistent shape/index.
        """
        input_file_type_asset_class = self._get_file_type_asset_class(file_uri=kwargs["file_uri"])

        kwargs["source_frame"] = self.skip_records_by_asset_class(
            source_frame=kwargs["source_frame"],
            input_file_type_asset_class=input_file_type_asset_class,
            target_file_type_asset_class=FileTypeAssetClass.FX,
            sec_group_and_type_skip_list=[
                # https://steeleye.atlassian.net/browse/ON-4586?focusedCommentId=194305
                # (SecGroupValues.FX, SecTypeValues.CSWAP),
                (SecGroupValues.FX, SecTypeValues.SPOT),
            ],
        )
        kwargs["source_frame"] = self.skip_records_by_asset_class(
            source_frame=kwargs["source_frame"],
            input_file_type_asset_class=input_file_type_asset_class,
            target_file_type_asset_class=FileTypeAssetClass.FI,
            sec_group_and_type_skip_list=[
                (SecGroupValues.CASH, SecTypeValues.BA),
                (SecGroupValues.CASH, SecTypeValues.BB),
                (SecGroupValues.CASH, SecTypeValues.CASH),
                (SecGroupValues.CASH, SecTypeValues.CD),
                (SecGroupValues.CASH, SecTypeValues.PHYSICAL_CP),
                (SecGroupValues.CASH, SecTypeValues.T_BILL),
                (SecGroupValues.CASH, SecTypeValues.TD),
                (SecGroupValues.CASH, SecTypeValues.TPREPO),
                (SecGroupValues.LOAN, SecTypeValues.AMORT),
                (SecGroupValues.LOAN, SecTypeValues.BANK),
                (SecGroupValues.LOAN, SecTypeValues.COMFEE),
                (SecGroupValues.LOAN, SecTypeValues.LEVPRIN),
                (SecGroupValues.LOAN, SecTypeValues.REVOLVE),
                (SecGroupValues.LOAN, SecTypeValues.TERM),
            ],
        )
        kwargs["source_frame"] = self.flatten_parfs_by_placement_id(
            source_frame=kwargs["source_frame"],
        )
        kwargs["should_flatten_market_side_executions"] = True

        super().__init__(**kwargs)

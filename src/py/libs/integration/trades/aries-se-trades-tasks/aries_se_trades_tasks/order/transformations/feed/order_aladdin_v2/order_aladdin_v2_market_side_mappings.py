import pandas as pd
from aries_se_core_tasks.datetime.convert_datetime import (  # type: ignore[attr-defined] # noqa: E501
    run_convert_datetime,
)
from aries_se_core_tasks.transform.map.map_conditional import (  # type: ignore[attr-defined] # noqa: E501
    run_map_conditional,
)
from aries_se_core_tasks.transform.map.map_value import run_map_value
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.order_aladdin_v2_base_mappings import (  # noqa: E501
    OrderAladdinV2BaseMappings,
)
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.static import (
    DATE_TIME_FORMAT,
    DevColumns,
    FileTypes,
    FillSourceColumns,
    OrderDetailSourceColumns,
    OrderSourceColumns,
    PlacementSourceColumns,
    TransactionSourceColumns,
)
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.datetime.convert_datetime import Params as ParamsConvertDatetime
from se_core_tasks.map.map_conditional import Params as ParamsMapConditional
from se_core_tasks.map.map_value import Params as ParamsMapValue
from se_elastic_schema.static.mifid2 import OrderStatus, TradingCapacity
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.order.party.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from se_trades_tasks.order.party.generic_order_party_identifiers import (
    run_generic_order_party_identifiers,
)
from se_trades_tasks.order.static import (
    ModelPrefix,
    OrderColumns,
    add_prefix,
)
from se_trades_tasks.order_and_tr.static import PartyPrefix, Venue


class OrderAladdinV2MarketSideMappings(OrderAladdinV2BaseMappings):
    def _date(self) -> pd.DataFrame:
        result: pd.DataFrame = run_convert_datetime(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=add_prefix(
                    FileTypes.ORDER, OrderSourceColumns.ACTIVATED_TIMESTAMP_UTC
                ),
                source_attribute_format=DATE_TIME_FORMAT,
                target_attribute=OrderColumns.DATE,
                convert_to=ConvertTo.DATE,
            ),
            skip_serializer=True,
        )

        null_mask = result[OrderColumns.DATE].isnull()

        # Inherit the timestamp from the Order cache by merged order id.
        if null_mask.any() and self.order_id_cache_dict:
            temp_col_name = "__temp_timestamp__"
            temp_result = pd.DataFrame(
                data=[pd.NA] * self.source_frame.shape[0],
                index=self.source_frame.index,
                columns=[temp_col_name],
            )
            temp_result.loc[null_mask, temp_col_name] = (
                self.source_frame.loc[
                    null_mask,
                    add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID),
                ]
                .astype("string")
                .map(self.order_id_cache_dict)
                .str.get("timestamp")
            )

            result.loc[null_mask, OrderColumns.DATE] = run_convert_datetime(
                temp_result.loc[null_mask, [temp_col_name]],
                params=ParamsConvertDatetime(
                    source_attribute=temp_col_name,
                    source_attribute_format=DATE_TIME_FORMAT,
                    target_attribute=temp_col_name,
                    convert_to=ConvertTo.DATE,
                ),
                skip_serializer=True,
            )[temp_col_name]

        return result

    def _execution_details_order_status(self) -> pd.DataFrame:
        # For each input row, we attempt to create a synthetic NEWO and a PARF order.
        # The logic is very simple compared to the client-side orders.
        # However, if the feature to flatten market side executions is enabled,
        # we will create FILLs instead of PARFs.
        return pd.concat(
            [
                pd.DataFrame(
                    data=[OrderStatus.NEWO.value] * self.source_frame.shape[0],
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(ModelPrefix.ORDER, OrderColumns.EXECUTION_DETAILS_ORDER_STATUS)
                    ],
                ),
                pd.DataFrame(
                    data=[
                        OrderStatus.FILL.value
                        if self.should_flatten_market_side_executions
                        else OrderStatus.PARF.value
                    ]
                    * self.source_frame.shape[0],
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            ModelPrefix.ORDER_STATE,
                            OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        value_map = {
            "A": TradingCapacity.AOTC.value,
            "O": TradingCapacity.AOTC.value,
            "P": TradingCapacity.DEAL.value,
            "R": TradingCapacity.AOTC.value,
            "M": TradingCapacity.AOTC.value,
            "I": TradingCapacity.DEAL.value,
            "S": TradingCapacity.DEAL.value,
            "L": TradingCapacity.DEAL.value,
            "C": TradingCapacity.DEAL.value,
        }
        return run_map_value(
            source_frame=self.source_frame.loc[
                :, [add_prefix(FileTypes.FILL, FillSourceColumns.DEALING_CAPACITY)]
            ],
            params=ParamsMapValue(
                source_attribute=add_prefix(FileTypes.FILL, FillSourceColumns.DEALING_CAPACITY),
                target_attribute=OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY,
                case_insensitive=True,
                value_map=value_map,
                default_value=TradingCapacity.AOTC.value,
            ),
            skip_serializer=True,
        )

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _id(self) -> pd.DataFrame:
        newo_series = (
            "M|"
            + self.source_frame.loc[
                :, add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_ID)
            ]
        )
        parf_series = self.source_frame.loc[
            :, add_prefix(FileTypes.FILL, FillSourceColumns.FILL_ID)
        ]

        return pd.concat(
            [
                pd.DataFrame(
                    data=newo_series.values,
                    index=self.source_frame.index,
                    columns=[add_prefix(ModelPrefix.ORDER, OrderColumns.ID)],
                ),
                pd.DataFrame(
                    data=parf_series.values,
                    index=self.source_frame.index,
                    columns=[add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID)],
                ),
            ],
            axis=1,
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=(
                "M|"
                + self.source_frame.loc[
                    :, add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_ID)
                ]
            ).values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        newo_series = (
            self.source_frame.loc[
                :, add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_ID)
            ]
            + "|"
            + self.target_df.loc[:, OrderColumns.SOURCE_INDEX].astype("string")
        )
        parf_series = self.source_frame.loc[
            :, add_prefix(FileTypes.FILL, FillSourceColumns.FILL_ID)
        ]

        return pd.concat(
            [
                pd.DataFrame(
                    data=newo_series.values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            ModelPrefix.ORDER, OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO
                        )
                    ],
                ),
                pd.DataFrame(
                    data=parf_series.values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            ModelPrefix.ORDER_STATE, OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.target_df.loc[
                        :,
                        add_prefix(
                            ModelPrefix.ORDER, OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO
                        ),
                    ].values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            ModelPrefix.ORDER, OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO
                        )
                    ],
                ),
                pd.DataFrame(
                    data=self.target_df.loc[
                        :,
                        add_prefix(
                            ModelPrefix.ORDER_STATE, OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO
                        ),
                    ].values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            ModelPrefix.ORDER_STATE,
                            OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        result: pd.DataFrame = pd.DataFrame(
            data=(
                self.source_frame.loc[
                    :, add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_QUANTITY)
                ]
            ).values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY],
        ).abs()

        zero_mask = result.loc[:, OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY] == 0
        result.loc[zero_mask, OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY] = (
            self.source_frame.loc[
                zero_mask,
                add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_QUANTITY_FILLED),
            ].abs()
        )

        return result

    def _price_forming_data_price(self) -> pd.DataFrame:
        source_column = (
            add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.AVG_PRICE)
            if self.should_flatten_market_side_executions
            else (add_prefix(FileTypes.FILL, FillSourceColumns.EXECUTED_PRICE))
        )  # noqa: E501

        cases = [
            {
                "query": f"`{source_column}`.notnull()",  # noqa: E501
                "attribute": f"{source_column}",
            },
            {
                "query": f"`{source_column}`.isnull()",  # noqa: E501
                "attribute": f"{add_prefix(FileTypes.ORDER, OrderSourceColumns.AVG_PRICE)}",
            },
        ]
        result: pd.DataFrame = run_map_conditional(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                cases=cases,
            ),
            skip_serializer=True,
        )
        return result

    def _transaction_details_price(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.PRICE_FORMING_DATA_PRICE].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE],
        )

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=(
                self.source_frame.loc[
                    :,
                    add_prefix(
                        FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_QUANTITY_FILLED
                    )
                    if self.should_flatten_market_side_executions
                    else add_prefix(FileTypes.FILL, FillSourceColumns.EXECUTED_QUANTITY),
                ]
            ).values,
            index=self.source_frame.index,
            columns=[
                add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY)
            ],
        ).abs()

    def _transaction_details_quantity(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=(
                self.target_df.loc[
                    :,
                    add_prefix(
                        ModelPrefix.ORDER_STATE, OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
                    ),
                ]
            ).values,
            index=self.source_frame.index,
            columns=[
                add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.TRANSACTION_DETAILS_QUANTITY)
            ],
        )

    def _source_index(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[:, add_prefix(FileTypes.FILL, "__swarm_raw_index__")].values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        result: pd.DataFrame = run_convert_datetime(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=add_prefix(
                    FileTypes.PLACEMENT, PlacementSourceColumns.SEND_TIME_UTC
                ),
                source_attribute_format=DATE_TIME_FORMAT,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                convert_to=ConvertTo.DATETIME,
            ),
            skip_serializer=True,
        )
        return result

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.TIMESTAMPS_ORDER_SUBMITTED].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TIMESTAMPS_INTERNAL_ORDER_SUBMITTED],
        )

    def _timestamps_order_received(self) -> pd.DataFrame:
        result: pd.DataFrame = run_convert_datetime(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=add_prefix(
                    FileTypes.ORDER, OrderSourceColumns.ACTIVATED_TIMESTAMP_UTC
                ),
                source_attribute_format=DATE_TIME_FORMAT,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                convert_to=ConvertTo.DATETIME,
            ),
            skip_serializer=True,
        )

        null_mask = result[OrderColumns.TIMESTAMPS_ORDER_RECEIVED].isnull()

        # Inherit the timestamp from the Order cache by merged order id.
        if null_mask.any() and self.order_id_cache_dict:
            temp_col_name = "__temp_timestamp__"
            temp_result = pd.DataFrame(
                data=[pd.NA] * self.source_frame.shape[0],
                index=self.source_frame.index,
                columns=[temp_col_name],
            )
            temp_result.loc[null_mask, temp_col_name] = (
                self.source_frame.loc[
                    null_mask,
                    add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID),
                ]
                .astype("string")
                .map(self.order_id_cache_dict)
                .str.get("timestamp")
            )

            result.loc[null_mask, OrderColumns.TIMESTAMPS_ORDER_RECEIVED] = run_convert_datetime(
                temp_result.loc[null_mask, [temp_col_name]],
                params=ParamsConvertDatetime(
                    source_attribute=temp_col_name,
                    source_attribute_format=DATE_TIME_FORMAT,
                    target_attribute=temp_col_name,
                    convert_to=ConvertTo.DATETIME,
                ),
                skip_serializer=True,
            )[temp_col_name]

        return result

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.TIMESTAMPS_ORDER_RECEIVED].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TIMESTAMPS_INTERNAL_ORDER_RECEIVED],
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        newo_order_status_updated = pd.DataFrame(
            data=pd.NA,  # type: ignore[call-overload]
            index=self.target_df.index,
            columns=[add_prefix(ModelPrefix.ORDER, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED)],
        )

        order_state_order_status_updated: pd.DataFrame = run_convert_datetime(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=add_prefix(
                    FileTypes.PLACEMENT, PlacementSourceColumns.FINISH_TIME_UTC
                )
                if self.should_flatten_market_side_executions
                else add_prefix(FileTypes.FILL, FillSourceColumns.EXECUTED_TIMESTAMP_UTC),
                source_attribute_format=DATE_TIME_FORMAT,
                target_attribute=add_prefix(
                    ModelPrefix.ORDER_STATE, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED
                ),
                convert_to=ConvertTo.DATETIME,
            ),
            skip_serializer=True,
        )

        return pd.concat([newo_order_status_updated, order_state_order_status_updated], axis=1)

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[
                :, add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED)
            ].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    ModelPrefix.ORDER_STATE, OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME
                )
            ],
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[
                :, add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED)
            ].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.TIMESTAMPS_TRADING_DATE_TIME)
            ],
        )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=OrderRecordType.MARKET_SIDE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE],
        )

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        # Map EUX to XEUR
        # Map 11 or GIFT to INSE
        value_map = {
            "EUX": "XEUR",
            "11": "INSE",
            "GIFT": "INSE",
        }
        return run_map_value(
            source_frame=self.source_frame.loc[
                :, [add_prefix(FileTypes.FILL, FillSourceColumns.EXCHANGE)]
            ],
            params=ParamsMapValue(
                source_attribute=add_prefix(FileTypes.FILL, FillSourceColumns.EXCHANGE),
                target_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                case_insensitive=True,
                value_map=value_map,
                preserve_original=True,
            ),
            skip_serializer=True,
        ).fillna(Venue.XOFF)

    def _transaction_details_venue(self) -> pd.DataFrame:
        # Map EUX to XEUR
        # Map 11 to INSE
        value_map = {
            "EUX": "XEUR",
            "11": "INSE",
        }
        return run_map_value(
            source_frame=self.source_frame.loc[
                :, [add_prefix(FileTypes.FILL, FillSourceColumns.EXCHANGE)]
            ],
            params=ParamsMapValue(
                source_attribute=add_prefix(FileTypes.FILL, FillSourceColumns.EXCHANGE),
                target_attribute=OrderColumns.TRANSACTION_DETAILS_VENUE,
                case_insensitive=True,
                value_map=value_map,
                preserve_original=True,
            ),
            skip_serializer=True,
        ).fillna(Venue.XOFF)

    def _market_identifiers_parties(self) -> pd.DataFrame:
        parties_source_frame = pd.concat(
            [
                self.source_frame.loc[:, DevColumns.EXECUTING_ENTITY_WITH_LEI],
                self._get_trader(),
                self._get_counterparty(),
                self._get_investment_decision_within_firm(),
                self.target_df.loc[:, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR],
            ],
            axis=1,
        )

        result: pd.DataFrame = run_generic_order_party_identifiers(
            source_frame=parties_source_frame,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=DevColumns.EXECUTING_ENTITY_WITH_LEI,
                client_identifier=DevColumns.EXECUTING_ENTITY_WITH_LEI,
                counterparty_identifier=DevColumns.COUNTERPARTY,
                buyer_identifier=DevColumns.EXECUTING_ENTITY_WITH_LEI,
                seller_identifier=DevColumns.COUNTERPARTY,
                trader_identifier=DevColumns.TRADER,
                investment_decision_within_firm_identifier=DevColumns.INVESTMENT_DECISION_WITHIN_FIRM,
                execution_within_firm_identifier=DevColumns.TRADER,
                buy_sell_side_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller=True,
                create_fallback_fields=True,
            ),
            skip_serializer=True,
        )
        return result

    def _order_class(self):
        """
        Hardcode: “iCross”
        when Transaction.EXECCPTYTYPE = ICROSS
        """
        result = pd.Series(
            data=[pd.NA] * self.target_df.shape[0],
            index=self.target_df.index,
            name=OrderColumns.ORDER_CLASS,
        )
        is_icross_mask = (
            self.source_frame.loc[
                :, add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.EXEC_CPCY_TYPE)
            ]
            .astype("string")
            .str.fullmatch("ICROSS", case=False, na=False)
        )
        result[is_icross_mask] = "iCross"

        return result

    def _get_counterparty(self) -> pd.Series:
        result: pd.Series = pd.Series(
            data=(
                PartyPrefix.ID
                + self.source_frame.loc[
                    :,
                    add_prefix(
                        FileTypes.TRANSACTION,
                        TransactionSourceColumns.EXEC_CPCY_ID,
                    ),
                ].astype("string")
            ).values,
            name=DevColumns.COUNTERPARTY,
            index=self.source_frame.index,
        )
        return result

    def _get_buy_sell(self) -> pd.Series:
        result: pd.Series = super()._get_buy_sell()
        null_mask = result.isnull()
        if null_mask.any():
            # If we cannot map the buySell, we fall back on the quantity:
            # - if it is positive -> it is a buy (1)
            # - if it is negative -> it is a sell (2)
            # - if it is 0 -> we fall back on the filled quantity
            result.loc[null_mask] = (
                self.source_frame.loc[
                    null_mask,
                    add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_QUANTITY),
                ]
                .astype("float")
                .apply(
                    lambda x: pd.NA if pd.isnull(x) else "1" if x > 0 else "2" if x < 0 else pd.NA,  # noqa: E501
                )
            )

        null_mask = result.isnull()
        if null_mask.any():
            # Fall back to PLACEMENT_QUANTITY_FILLED instead of PLACEMENT_QUANTITY
            result.loc[null_mask] = (
                self.source_frame.loc[
                    null_mask,
                    add_prefix(
                        FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_QUANTITY_FILLED
                    ),
                ]
                .astype("float")
                .apply(
                    lambda x: pd.NA if pd.isnull(x) else "1" if x > 0 else "2",
                )
            )

        return result

    @staticmethod
    def flatten_parfs_by_placement_id(source_frame: pd.DataFrame) -> pd.DataFrame:
        # sort by fill.fillId and keep the most recent PARF of each fill.placementId
        source_frame = source_frame.sort_values(
            by=add_prefix(FileTypes.FILL, FillSourceColumns.FILL_ID), ascending=False
        )
        source_frame = source_frame.drop_duplicates(
            [add_prefix(FileTypes.FILL, FillSourceColumns.PLACEMENT_ID)], keep="first"
        )
        return source_frame

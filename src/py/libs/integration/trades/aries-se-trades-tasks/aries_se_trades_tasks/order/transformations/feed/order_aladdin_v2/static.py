from se_trades_tasks.order.static import add_prefix


class FileTypes:
    FILL = "fill"
    ORDER = "order"
    ORDER_DETAILS = "orderdetail"
    PLACEMENT = "placement"
    TRANSACTION = "transaction"


class FileTypeAssetClass:
    DERIV = "DERIV"
    EQ = "EQ"
    FI = "FI"
    FX = "FX"


class FillSourceColumns:
    # If you add a new column here do not forget to set the type in INPUT_COLUMNS_TYPE_MAP
    DEALING_CAPACITY = "dealingCapacity"
    EXCHANGE = "exchange"
    EXECUTED_PRICE = "executedPrice"
    EXECUTED_QUANTITY = "executedQuantity"
    EXECUTED_TIMESTAMP_UTC = "executedTimestampUtc"
    FILL_ID = "fillId"
    PLACEMENT_ID = "placementId"


class OrderDetailSourceColumns:
    # If you add a new column here do not forget to set the type in INPUT_COLUMNS_TYPE_MAP
    ORDER_DETAIL_QUANTITY = "orderDetailQuantity"
    ORDER_DETAIL_QUANTITY_BOOKED = "orderDetailQuantityBooked"
    ORDER_ID = "orderId"
    ORIG_ORDER_ID = "origOrderId"
    PORTFOLIO_ID = "portfolioId"


class OrderSourceColumns:
    # If you add a new column here do not forget to set the type in INPUT_COLUMNS_TYPE_MAP
    ACTIVATED_TIMESTAMP_UTC = "activatedTimestampUtc"
    AUTHORIZED_TIMESTAMP_UTC = "authorizedTimestampUtc"
    AVG_PRICE = "avgPrice"
    BASKET_ID = "basketId"
    CREATED_TIMESTAMP_UTC = "createdTimestampUtc"
    GEN_COMMENTS = "genComments"
    ISIN = "isin"
    ORDER_ID = "orderId"
    ORDER_STATUS = "orderStatus"
    ORDER_TYPE = "orderType"
    OWNER_TYPE = "ownerType"
    PM_INITIALS = "pmInitials"
    PRICE_CCY = "priceCcy"
    RIC = "ric"
    TIME_IN_FORCE = "timeInForce"
    TRADER = "trader"
    TRAN_TYPE = "tranType"


class PlacementSourceColumns:
    # If you add a new column here do not forget to set the type in INPUT_COLUMNS_TYPE_MAP
    AVG_PRICE = "avgPrice"
    CREATED_TIMESTAMP_UTC = "createdTimestampUtc"
    FINISH_TIME_UTC = "finishTimeUtc"
    LIMIT_VALUE = "limitValue"
    ORDER_ID = "orderId"
    PLACEMENT_ID = "placementId"
    PLACEMENT_QUANTITY = "placementQuantity"
    PLACEMENT_QUANTITY_FILLED = "placementQuantityFilled"
    SEND_TIME_UTC = "sendTimeUtc"
    STOP_VALUE = "stopValue"


class TransactionSourceColumns:
    # If you add a new column here, remember to set the type in INPUT_COLUMNS_TYPE_MAP
    ASSET_ID = "assetId"
    AUTHORIZED_TIMESTAMP_UTC = "authorizedTimestampUtc"
    CPTY_ID = "cptyId"
    CREATED_TIMESTAMP_UTC = "createdTimestampUtc"
    DEALING_CAPACITY = "dealingCapacity"
    EXEC_CPCY_ID = "execCptyId"
    EXEC_CPCY_TYPE = "execCptyType"
    FX_BASE_CCY = "fxBaseCcy"
    FX_TERM_CCY = "fxTermCcy"
    ISIN = "isin"
    MATURITY = "maturity"
    MODIFIED_TIMESTAMP_UTC = "modifiedTimestampUtc"
    ORDER_ID = "orderId"
    PLACEMENT_ID = "placementId"
    PORTFOLIO_ID = "portfolioId"
    PORTFOLIO_TICKER = "portfolioTicker"
    RIC = "ric"
    SEC_DESC_1 = "secDesc1"
    SEC_GROUP = "secGroup"
    SEC_TICKER = "secTicker"
    SEC_TYPE = "secType"
    TRADE_COUPON = "tradeCoupon"
    TRADE_DATE = "tradeDate"
    TRADE_NUM = "tradeNum"


INPUT_COLUMNS_TYPE_MAP = {
    add_prefix(FileTypes.FILL, FillSourceColumns.DEALING_CAPACITY): "string",
    add_prefix(FileTypes.FILL, FillSourceColumns.EXCHANGE): "string",
    add_prefix(FileTypes.FILL, FillSourceColumns.EXECUTED_PRICE): "float",
    add_prefix(FileTypes.FILL, FillSourceColumns.EXECUTED_QUANTITY): "float",
    add_prefix(FileTypes.FILL, FillSourceColumns.EXECUTED_TIMESTAMP_UTC): "string",
    add_prefix(FileTypes.FILL, FillSourceColumns.FILL_ID): "string",
    add_prefix(FileTypes.FILL, FillSourceColumns.PLACEMENT_ID): "string",
    add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_DETAIL_QUANTITY): "float",
    add_prefix(
        FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_DETAIL_QUANTITY_BOOKED
    ): "float",
    add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID): "string",
    add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): "string",
    add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.PORTFOLIO_ID): "string",
    add_prefix(FileTypes.ORDER, OrderSourceColumns.ACTIVATED_TIMESTAMP_UTC): "string",
    add_prefix(FileTypes.ORDER, OrderSourceColumns.AUTHORIZED_TIMESTAMP_UTC): "string",
    add_prefix(FileTypes.ORDER, OrderSourceColumns.AVG_PRICE): "float",
    add_prefix(FileTypes.ORDER, OrderSourceColumns.BASKET_ID): "string",
    add_prefix(FileTypes.ORDER, OrderSourceColumns.CREATED_TIMESTAMP_UTC): "string",
    add_prefix(FileTypes.ORDER, OrderSourceColumns.GEN_COMMENTS): "string",
    add_prefix(FileTypes.ORDER, OrderSourceColumns.ISIN): "string",
    add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_ID): "string",
    add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): "string",
    add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_TYPE): "string",
    add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): "string",
    add_prefix(FileTypes.ORDER, OrderSourceColumns.PM_INITIALS): "string",
    add_prefix(FileTypes.ORDER, OrderSourceColumns.PRICE_CCY): "string",
    add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC): "string",
    add_prefix(FileTypes.ORDER, OrderSourceColumns.TIME_IN_FORCE): "string",
    add_prefix(FileTypes.ORDER, OrderSourceColumns.TRADER): "string",
    add_prefix(FileTypes.ORDER, OrderSourceColumns.TRAN_TYPE): "string",
    add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.AVG_PRICE): "float",
    add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.CREATED_TIMESTAMP_UTC): "string",
    add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.FINISH_TIME_UTC): "string",
    add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.LIMIT_VALUE): "string",
    add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.ORDER_ID): "string",
    add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_ID): "string",
    add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_QUANTITY): "float",
    add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_QUANTITY_FILLED): "float",
    add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.SEND_TIME_UTC): "string",
    add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.STOP_VALUE): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.ASSET_ID): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.AUTHORIZED_TIMESTAMP_UTC): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.CPTY_ID): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.CREATED_TIMESTAMP_UTC): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.DEALING_CAPACITY): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.EXEC_CPCY_ID): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.EXEC_CPCY_TYPE): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.FX_BASE_CCY): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.FX_TERM_CCY): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.ISIN): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.MATURITY): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.MODIFIED_TIMESTAMP_UTC): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.ORDER_ID): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.PLACEMENT_ID): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.PORTFOLIO_ID): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.PORTFOLIO_TICKER): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.RIC): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.TRADE_COUPON): "float",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.TRADE_DATE): "string",
    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.TRADE_NUM): "string",
    add_prefix(FileTypes.ORDER, "__swarm_raw_index__"): "string",
    add_prefix(FileTypes.ORDER_DETAILS, "__swarm_raw_index__"): "string",
    add_prefix(FileTypes.PLACEMENT, "__swarm_raw_index__"): "string",
    add_prefix(FileTypes.TRANSACTION, "__swarm_raw_index__"): "string",
    add_prefix(FileTypes.FILL, "__swarm_raw_index__"): "string",
}


class CustomOrdType:
    CLIENT = "C"
    MARKET = "M"


class SecGroupValues:
    ABS = "ABS"
    BND = "BND"
    CASH = "CASH"
    CDS = "CDS"
    CDSWAP = "CDSWAP"
    CMBS = "CMBS"
    CMO = "CMO"
    EQUITY = "EQUITY"
    FUND = "FUND"
    FUTURE = "FUTURE"
    FX = "FX"
    IBND = "IBND"
    LOAN = "LOAN"
    MBS = "MBS"
    OPTION = "OPTION"
    SWAP = "SWAP"
    SYNTH = "SYNTH"


class SecTypeValues:
    AMORT = "AMORT"
    BA = "BA"
    BANK = "BANK"
    BB = "BB"
    CASH = "CASH"
    CD = "CD"
    CDSWAP = "CDSWAP"
    CLOSED_END = "CLOSED_END"
    COMFEE = "COMFEE"
    CSWAP = "CSWAP"
    FWRD = "FWRD"
    GOVT = "GOVT"
    LEVPRIN = "LEVPRIN"
    OPT = "OPT"
    PHYSICAL_CP = "PHYSICALCP"
    REVOLVE = "REVOLVE"
    SPOT = "SPOT"
    SWAPTION = "SWAPTION"
    T_BILL = "TBILL"
    TD = "TD"
    TERM = "TERM"
    TPREPO = "TPREPO"


class DevColumns:
    ASSET_CLASS = "__asset_class__"
    ASSET_ID = "__asset_id__"
    BEST_EX_ASSET_CLASS_MAIN = "__best_ex_asset_class_main__"
    BUY_SELL = "__buy_sell__"
    CFI_CATEGORY = "__cfi_category__"
    CFI_GROUP = "__cfi_group__"
    CLIENT = "__client__"
    COUNTERPARTY = "__counterparty__"
    CURRENCY = "__currency__"
    DEALING_CAPACITY = "__dealing_capacity__"
    EXECUTING_ENTITY_WITH_LEI = "__executing_entity_with_lei__"
    EXPIRY_DATE = "__expiry_date__"
    FILE_TYPE_ASSET_CLASS = "__file_type_asset_class__"
    ID = "__id__"
    INSTRUMENT_CLASSIFICATION = "__instrument_classification__"
    INSTRUMENT_CREATED_THROUGH_FB = "__instrument_created_through_fb__"
    INSTRUMENT_FULL_NAME = "__instrument_full_name__"
    INSTRUMENT_UNIQUE_IDENTIFIER = "__instrument_unique_identifier__"
    INVESTMENT_DECISION_WITHIN_FIRM = "__investment_decision_within_firm__"
    ISIN = "__isin__"
    NEWO_ID = "__newo_id__"
    NEWO_IN_FILE = "__newo_in_file__"
    NOTIONAL_CURRENCY_2 = "__notional_currency_2__"
    OPTION_TYPE = "__option_type__"
    ORDER_DETAIL_MERGE_ID = "__order_detail_merge_id__"
    ORDER_STATUS_UPDATED = "__order_status_updated__"
    ORDER_TYPE = "__order_type__"
    PRICING_REFERENCE_LXID = "__pricing_reference_lxid__"
    PRICING_REFERENCE_REDCODE = "__pricing_reference_redcode__"
    RIC = "__ric__"
    STRIKE_PRICE = "__strike_price__"
    STRIKE_PRICE_CURRENCY = "__strike_price_currency__"
    SYMBOL = "__symbol__"
    SYMBOL_AND_EXPIRY_CODE = "__symbol_and_expiry_code__"
    TEMP_ASSET_CLASS = "__temp_asset_class__"
    TIMESTAMPS_VALIDITY_PERIOD = "__timestamps_validity_period__"
    TRADER = "__trader__"
    TRADING_CAPACITY = "__trading_capacity__"
    UNDERLYING_INDEX_TERM = "__underlying_index_term__"


ALADDIN_FILES_PATTERN = ".+[._](?P<asset_class>DERIV|EQ|FX|FI)[.].*"
ORDER_ALADDIN_V2_CLIENT_SIDE_FEED_NAME = "client_side_order_aladdin_v2"
ORDER_ALADDIN_V2_MARKET_SIDE_FEED_NAME = "market_side_order_aladdin_v2"
DATE_FORMAT = "%Y-%m-%d"
DATE_TIME_FORMAT = "%Y-%m-%dT%H:%M:%S.%fZ"

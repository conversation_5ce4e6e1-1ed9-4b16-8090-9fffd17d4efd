from typing import Dict


class PreNormalisedSourceColumns:
    AMOUNT = "Amount"
    FILL_AS_OF_DATE = "Fill As Of Date"
    FILL_AS_OF_TIME = "Fill As Of Time"
    ORDER_NUMBER = "Order Number"
    ORIGINAL_FILL_DATE = "ORIGINAL_FILL_DATE"
    ROUTE_AVG_PRICE = "Route Avg Price"
    ROUTE_FILLED_AMOUNT = "Route Filled Amount"
    ROUTED_AMOUNT = "Routed Amount"
    STATUS = "Status"
    TIF = "TIF"
    TRADER_NOTES = "Trader Notes"


class SourceColumns:
    ACCOUNT = "ACCOUNT"
    AMOUNT = "AMOUNT"
    AS_OF_DATE = "ASOFDATE"
    AS_OF_TIME = "ASOFTIME"
    ASSET_CLASS = "ASSE CLASS"
    AVERAGE_PRICE = "AVERAGEPRICE"
    BASKET_NAME = "BASKETNAME"
    BBGID = "BBGID"
    BLOCK_ID = "BLOCKID"
    BROKER = "BROKER"
    CFD_FLAG = "CFDFLAG"
    CONTRACT_EXPIRATION = "CONTRACTEXPIRATION"
    CREATE_DATE = "CREATEDATE"
    CREATE_TIME = "CREATETIME"
    CURRENCY = "CURRENCY"
    CUSIP = "CUSIP"
    CUSTOM_NOTE_1 = "CUSTOMNOTE1"
    CUSTOM_NOTE_2 = "CUSTOMNOTE2"
    CUSTOM_NOTE_3 = "CUSTOMNOTE3"
    CUSTOM_NOTE_4 = "CUSTOMNOTE4"
    CUSTOM_NOTE_5 = "CUSTOMNOTE5"
    DAY_AVERAGE_PRICE = "DAYAVERAGEPRICE"
    DAY_FILLED_AMOUNT = "DAYFILLEDAMOUNT"
    EXCHANGE = "EXCHANGE"
    EXEC_INSTRUCTIONS = "EXECINSTRUCTIONS"
    FILL_AS_OF_DATE = "FILLASOFDATE"
    FILL_AS_OF_TIME = "FILLASOFTIME"
    FILLED_AMOUNT = "FILLEDAMOUNT"
    GTD_DATE = "GTDDATE"
    HANDLING_INSTR = "HANDLINGINSTR"
    INDIA_BSE_AVG_PRICE = "INDIABSEAVGPRICE"
    INDIA_BSE_FILLED = "INDIABSEFILLED"
    INDIA_MCX_AVG_PRICE = "INDIAMCXAVGPRICE"
    INDIA_MCX_FILLED = "INDIAMCXFILLED"
    INDIA_NSE_AVG_PRICE = "INDIANSEAVGPRICE"
    INDIA_NSE_FILLED = "INDIANSEFILLED"
    INSTRUCTIONS = "INSTRUCTIONS"
    INVESTOR_ID = "INVESTORID"
    ISIN = "ISIN"
    LAST_FILL_DATE = "LASTFILLDATE"
    LIMIT_PRICE = "LIMITPRICE"
    LONG_FUTURE_NAME = "LONGFUTURENAME"
    OCC_SYMBOL = "OCC_SYMBOL"
    ORDER = "ORDER"
    ORDER_NUMBER = "ORDERNUMBER"
    ORDER_ORIGIN = "ORDERORIGIN"
    ORDER_REF_ID = "ORDERREFID"
    ORDER_TYPE = "ORDERTYPE"
    PARSEKEY = "PARSEKEY"
    POSITION = "POSITION"
    ROUTED_AMOUNT = "ROUTEDAMOUNT"
    ROUTE_AVG_PRICE = "ROUTEAVGPRICE"
    ROUTE_FILLED_AMOUNT = "ROUTEFILLEDAMOUNT"
    SECURITY_NAME = "SECURITYNAME"
    SEDOL = "SEDOL"
    SETTLEMENT_CURRENCY = "SETTLEMENTCURRENCY"
    SETTLEMENT_DATE = "SETTLEMENTDATE"
    SETTLEMENT_TYPE = "SETTLEMENTTYPE"
    SIDE = "SIDE"
    STATUS = "STATUS"
    STOP_PRICE = "STOPPRICE"
    TICKER = "TICKER"
    TICKER_EXCHANGE = "TICKER+EXCHANGE"
    TIF = "TIF"
    TRADER_NAME = "TRADERNAME"
    TRADER_NOTES = "TRADERNOTES"
    TRADER_UUID = "TRADERUUID"
    WORKING_AMOUNT = "WORKINGAMOUNT"
    YELLOW_KEY = "YELLOWKEY"


class TempColumns:
    CONVERTED_DATETIME = "__converted_datetime__"
    CUSTOM_TRADER = "__custom_trader__"
    FILL_AS_OF_TIME_CUTOFF = "__fill_as_of_time_cutoff__"
    INSTRUMENT_IS_CREATED_THROUGH_FALLBACK = "__instr_is_created_through_fallback__"
    TENANT_LEI = "__tenant_lei__"
    TRADER = "__trader__"
    TRADER_NOTES_TRADER = "__trader_notes_trader__"
    TRAN_REF_DATE = "__tran_ref_date__"

    FIRST_SELLER = "__first_seller__"
    FIRST_BUYER = "__first_buyer__"


class PreProcessingColumns:
    FILL_AS_OF_DATETIME = "__FILL_AS_OF_DATETIME__"
    PROCESSING_REASON = "__PROCESSING_REASON__"
    RAW_INDEX = "__RAW_INDEX__"


class ProcessingReason:
    CANCELLED = "Cancelled"
    DAY_ORDER = "DAY Order"
    EU_DESK = "EU Desk"
    FILLED = "Filled"
    FRIDAY = "Friday"


DATA_SOURCE_NAME: str = "Bloomberg EMSX"
WORKFLOW_NAME: str = "tr_bbg_emsi_orders"

JOIN_DATE_AND_TIME_FORMAT: str = "%d/%m/%Y%H:%M:%S"
TIME_FORMAT: str = "%H:%M:%S"
DATE_FORMAT: str = "%d/%m/%Y"

DEFAULT_TIMEZONE: str = "Europe/London"

SOURCE_SCHEMA: Dict[str, str] = {
    PreNormalisedSourceColumns.ORIGINAL_FILL_DATE: "string",
    PreProcessingColumns.FILL_AS_OF_DATETIME: "string",
    PreProcessingColumns.PROCESSING_REASON: "string",
    PreProcessingColumns.RAW_INDEX: "string",
    SourceColumns.ACCOUNT: "string",
    SourceColumns.AVERAGE_PRICE: "float",
    SourceColumns.BROKER: "string",
    SourceColumns.CURRENCY: "string",
    SourceColumns.FILL_AS_OF_DATE: "string",
    SourceColumns.FILL_AS_OF_TIME: "string",
    SourceColumns.FILLED_AMOUNT: "float",
    SourceColumns.ISIN: "string",
    SourceColumns.ORDER_NUMBER: "string",
    SourceColumns.ROUTED_AMOUNT: "float",
    SourceColumns.ROUTE_AVG_PRICE: "float",
    SourceColumns.ROUTE_FILLED_AMOUNT: "float",
    SourceColumns.SECURITY_NAME: "string",
    SourceColumns.SIDE: "string",
    SourceColumns.STATUS: "string",
    SourceColumns.TRADER_NAME: "string",
    SourceColumns.TRADER_NOTES: "string",
    SourceColumns.TRADER_UUID: "string",
}


class FetchedMarketCounterpartyColumns:
    DETAILS_ORG_TYPE = "details.orgType"
    FIRM_IDENTIFIERS_MIC = "firmIdentifiers.mic"

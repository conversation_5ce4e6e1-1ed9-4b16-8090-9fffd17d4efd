import pandas as pd
from aries_se_core_tasks.currency.convert_minor_to_major import (  # type: ignore[attr-defined]
    Params as ConvertMtMParams,
)
from aries_se_core_tasks.currency.convert_minor_to_major import (  # type: ignore[attr-defined]
    run_convert_minor_to_major,
)
from aries_se_core_tasks.datetime.convert_datetime import (  # type: ignore[attr-defined]
    Params as ConvertDatetimeParams,
)
from aries_se_core_tasks.datetime.convert_datetime import (  # type: ignore[attr-defined]
    run_convert_datetime,
)
from aries_se_core_tasks.datetime.join_data_and_time_format import (  # type: ignore[attr-defined]
    Params as JoinDateTimeParams,
)
from aries_se_core_tasks.datetime.join_data_and_time_format import (
    run_join_date_and_time_format,
)
from aries_se_core_tasks.feeds.generic.get_tenant_lei import (  # type: ignore[attr-defined]
    Params as GetTenantLEIParams,
)
from aries_se_core_tasks.feeds.generic.get_tenant_lei import (  # type: ignore[attr-defined]
    run_get_tenant_lei,
)
from aries_se_core_tasks.transform.dataframe.concat_attributes import (  # type: ignore[attr-defined]
    Params as ParamsConcatAttributes,
)
from aries_se_core_tasks.transform.dataframe.concat_attributes import (  # type: ignore[attr-defined]
    run_concat_attributes,
)
from aries_se_core_tasks.transform.map.map_conditional import (  # type: ignore[attr-defined]
    Params as MapConditionalParams,
)
from aries_se_core_tasks.transform.map.map_conditional import (  # type: ignore[attr-defined]
    run_map_conditional,
)
from aries_se_core_tasks.transform.map.map_value import (  # type: ignore[attr-defined]
    Params as MapValueParams,
)
from aries_se_core_tasks.transform.map.map_value import run_map_value
from aries_se_trades_tasks.instrument.instrument_identifiers import (  # type: ignore[attr-defined]
    Params as InstrumentIdentifiersParams,
)
from aries_se_trades_tasks.instrument.instrument_identifiers import run_instrument_identifiers
from aries_se_trades_tasks.orders_and_tr.identifiers.merge_market_identifiers import (  # type: ignore[attr-defined]
    Params as MergeIdentifiersParams,
)
from aries_se_trades_tasks.orders_and_tr.identifiers.merge_market_identifiers import (  # type: ignore[attr-defined]
    run_merge_market_identifiers,
)
from aries_se_trades_tasks.tr.party.identifiers.generic_tr_party_identifiers import (  # type: ignore[attr-defined]
    Params as PartyIdentifiersParams,
)
from aries_se_trades_tasks.tr.party.identifiers.generic_tr_party_identifiers import (  # type: ignore[attr-defined]
    run_generic_tr_party_identifiers,
)
from aries_se_trades_tasks.tr.transformations.bbg.emsi.orders.static import (
    DATA_SOURCE_NAME,
    DEFAULT_TIMEZONE,
    JOIN_DATE_AND_TIME_FORMAT,
    TIME_FORMAT,
    PreNormalisedSourceColumns,
    PreProcessingColumns,
    SourceColumns,
    TempColumns,
)
from datetime import datetime
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.mifid2 import (
    BuySellIndicator,
    PriceNotation,
    QuantityNotation,
    ReportStatus,
    ShortSellingIndicator,
    TradingCapacity,
)
from se_trades_tasks.abstractions.abstract_rts22_transaction_transformations import (
    AbstractRTS22TransactionsTransformations,
)
from se_trades_tasks.order_and_tr.static import PartyPrefix, Venue
from se_trades_tasks.tr.static import RTS22TransactionColumns


class TrBBGEMSIOrdersTransformations(AbstractRTS22TransactionsTransformations):  # type: ignore[misc]
    def __init__(
        self,
        source_file_uri: str,
        tenant: str,
        es_client,
        **kwargs,
    ):
        # This logical block must be executed before the super().__init__()
        # as we want to ensure that all the dataframes reliant on the source_frame
        # have the same shape i.e. we cannot mutate the source_frame after the super().__init__()
        # and then manually change the shape of the pre_process_df, target_df, etc.
        self.eu_desk_mask = (
            kwargs["source_frame"]
            .loc[:, SourceColumns.TRADER_NOTES]
            .astype(str)
            .str.split("-")
            .str.get(0)
            .str.strip()
            .str.fullmatch("CESF", case=False, na=False)
        )
        route_filled_amount_greater_than_zero_mask = (
            kwargs["source_frame"].loc[:, SourceColumns.ROUTE_FILLED_AMOUNT].astype(float).fillna(0)
            > 0
        )
        discardable_routes_mask = self.eu_desk_mask & ~route_filled_amount_greater_than_zero_mask
        # Routes identified as belonging to the EU Desk with filled amount <= 0 must be discarded
        kwargs["source_frame"] = kwargs["source_frame"].loc[~discardable_routes_mask, :]

        super().__init__(**kwargs)
        self.es_client = es_client
        self.tenant = tenant
        self.source_file_uri: str = source_file_uri

        self.pre_process_df: pd.DataFrame
        self.source_frame: pd.DataFrame
        self.target_df: pd.DataFrame

    def _pre_process(self):
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self.get_trader_notes_trader(),
                self.get_parties_tenant_lei(),
                self.get_converted_datetime(),
            ],
            axis=1,
        )

        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self.get_parties_trader(),  # needs trader notes trader
            ],
            axis=1,
        )

    def process(self) -> pd.DataFrame:
        self.pre_process()

        self.data_source_name()
        self.date()

        self.report_details_report_status()

        self.source_index()
        self.source_key()

        self.traders_algos_waivers_indicators_securities_financing_txn_indicator()

        self.transaction_details_buy_sell_indicator()
        self.traders_algos_waivers_indicators_short_selling_indicator()
        self.transaction_details_price()
        self.transaction_details_price_currency()
        self.transaction_details_price_notation()
        self.transaction_details_quantity()
        self.transaction_details_quantity_notation()
        self.transaction_details_trading_capacity()
        self.transaction_details_trading_date_time()
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()
        self.report_details_transaction_ref_no()

        self.transmission_details_order_transmission_indicator()

        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()

        self.post_process()

        return self.target_df

    def _post_process(self):
        self.target_df = pd.concat(
            [
                self.target_df,
                pd.Series(
                    data=[True] * self.source_frame.shape[0],
                    index=self.source_frame.index,
                    name=TempColumns.INSTRUMENT_IS_CREATED_THROUGH_FALLBACK,
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=[DATA_SOURCE_NAME] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        return run_convert_datetime(  # type: ignore[no-any-return]
            source_frame=self.pre_process_df,
            params=ConvertDatetimeParams(
                source_attribute=TempColumns.CONVERTED_DATETIME,
                target_attribute=RTS22TransactionColumns.DATE,
                convert_to=ConvertTo.DATE,
            ),
            skip_serializer=True,
        )

    def _market_identifiers(self) -> pd.DataFrame:
        return run_merge_market_identifiers(  # type: ignore[no-any-return]
            source_frame=self.target_df,
            params=MergeIdentifiersParams(
                identifiers_path=RTS22TransactionColumns.MARKET_IDENTIFIERS,
                parties_path=RTS22TransactionColumns.MARKET_IDENTIFIERS_PARTIES,
                instrument_path=RTS22TransactionColumns.MARKET_IDENTIFIERS_INSTRUMENT,
            ),
            skip_serializer=True,
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        return run_instrument_identifiers(  # type: ignore[no-any-return]
            source_frame=pd.concat(
                [
                    self.target_df.loc[
                        :, RTS22TransactionColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
                    ],
                    self.source_frame.loc[:, SourceColumns.ISIN],
                ],
                axis=1,
            ),
            params=InstrumentIdentifiersParams(
                currency_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                isin_attribute=SourceColumns.ISIN,
                retain_task_inputs=True,
            ),
            skip_serializer=True,
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        party_df = pd.concat(
            [
                self.pre_process_df.loc[:, TempColumns.TENANT_LEI],
                self.target_df.loc[
                    :, RTS22TransactionColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
                ],
                PartyPrefix.ID + self.pre_process_df.loc[:, TempColumns.TRADER],
                PartyPrefix.ID + self.pre_process_df.loc[:, TempColumns.TRADER_NOTES_TRADER],
                PartyPrefix.ID + self.source_frame.loc[:, SourceColumns.BROKER],
            ],
            axis=1,
        )

        return run_generic_tr_party_identifiers(  # type: ignore[no-any-return]
            source_frame=party_df,
            params=PartyIdentifiersParams(
                target_attribute=RTS22TransactionColumns.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=TempColumns.TENANT_LEI,
                investment_decision_within_firm_identifier=TempColumns.TRADER,
                execution_within_firm_identifier=TempColumns.TRADER,
                use_buy_mask_for_buyer_seller=True,
                use_buy_mask_for_buyer_seller_decision_maker=True,
                buy_sell_side_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                buyer_identifier=TempColumns.TRADER_NOTES_TRADER,
                buyer_decision_maker_identifier=TempColumns.TENANT_LEI,
                seller_identifier=SourceColumns.BROKER,
            ),
            skip_serializer=True,
        )

    def _report_details_report_status(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=[ReportStatus.NEWT.value] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.REPORT_DETAILS_REPORT_STATUS],
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        return run_concat_attributes(  # type: ignore[no-any-return]
            source_frame=pd.concat(
                [
                    self.source_frame.loc[
                        :, [SourceColumns.ORDER_NUMBER, SourceColumns.FILL_AS_OF_DATE]
                    ],
                    self.target_df.loc[
                        :, [RTS22TransactionColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR]
                    ],
                    run_convert_datetime(
                        source_frame=self.source_frame,
                        params=ConvertDatetimeParams(
                            source_attribute=SourceColumns.FILL_AS_OF_DATE,
                            target_attribute=TempColumns.TRAN_REF_DATE,
                            target_datetime_format="%Y%m%d",
                        ),
                        skip_serializer=True,
                    ),
                ],
                axis=1,
            ),
            params=ParamsConcatAttributes(
                source_attributes=[
                    SourceColumns.ORDER_NUMBER,
                    TempColumns.TRAN_REF_DATE,
                    TempColumns.TRAN_REF_DATE,
                    RTS22TransactionColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                ],
                delimiter="",
                target_attribute=RTS22TransactionColumns.REPORT_DETAILS_TRANSACTION_REF_NO,
            ),
            skip_serializer=True,
        )

    def _source_index(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame[PreProcessingColumns.RAW_INDEX].values,
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.SOURCE_INDEX],
        )

    def _source_key(self):
        return pd.DataFrame(
            data=[self.source_file_uri] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.SOURCE_KEY],
        )

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=[False] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.TRADERS_ALGOS_WAIVERS_INDICATORS_SECRT_FNC_TXN_IND],
        )

    def _traders_algos_waivers_indicators_short_selling_indicator(self):
        """Needs RTS22TransactionColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        to be populated."""
        return run_map_conditional(
            source_frame=self.target_df.loc[
                :, [RTS22TransactionColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR]
            ],
            params=MapConditionalParams(
                target_attribute=RTS22TransactionColumns.TRADERS_ALGOS_WAIVERS_INDICATORS_SHORT_SELLING_INDICATOR,
                cases=[
                    Case(
                        query=f"`{RTS22TransactionColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR}`.str."
                        f"fullmatch('{BuySellIndicator.SELL.value}', case=False, na=False)",
                        value=ShortSellingIndicator.SELL.value,
                    )
                ],
            ),
            skip_serializer=True,
        )

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        return run_map_value(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.SIDE,
                case_insensitive=True,
                default_value=pd.NA,
                target_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                value_map={
                    "B": BuySellIndicator.BUYI.value,
                    "BY": BuySellIndicator.BUYI.value,
                    "S": BuySellIndicator.SELL.value,
                    "SL": BuySellIndicator.SELL.value,
                    "SS": BuySellIndicator.SELL.value,
                    "SX": BuySellIndicator.SELL.value,
                },
            ),
            skip_serializer=True,
        )

    def _transaction_details_price(self) -> pd.DataFrame:
        result = pd.DataFrame(
            data=pd.NA,
            index=self.target_df.index,
            columns=[RTS22TransactionColumns.TRANSACTION_DETAILS_PRICE],
        )

        if not self.eu_desk_mask.all():
            result.loc[~self.eu_desk_mask, :] = run_convert_minor_to_major(  # type: ignore[no-any-return]
                source_frame=self.source_frame.loc[
                    ~self.eu_desk_mask, [SourceColumns.CURRENCY, SourceColumns.AVERAGE_PRICE]
                ],
                params=ConvertMtMParams(
                    source_ccy_attribute=SourceColumns.CURRENCY,
                    source_price_attribute=SourceColumns.AVERAGE_PRICE,
                    target_price_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_PRICE,
                ),
                skip_serializer=True,
            )

        if self.eu_desk_mask.any():
            result.loc[self.eu_desk_mask, RTS22TransactionColumns.TRANSACTION_DETAILS_PRICE] = (
                run_convert_minor_to_major(  # type: ignore[no-any-return]
                    source_frame=self.source_frame.loc[
                        self.eu_desk_mask, [SourceColumns.CURRENCY, SourceColumns.ROUTE_AVG_PRICE]
                    ],
                    params=ConvertMtMParams(
                        source_ccy_attribute=SourceColumns.CURRENCY,
                        source_price_attribute=SourceColumns.ROUTE_AVG_PRICE,
                        target_price_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_PRICE,
                    ),
                    skip_serializer=True,
                )[RTS22TransactionColumns.TRANSACTION_DETAILS_PRICE]
            )

        return result

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        return run_convert_minor_to_major(  # type: ignore[no-any-return]
            source_frame=self.source_frame,
            params=ConvertMtMParams(
                source_ccy_attribute=SourceColumns.CURRENCY,
                target_ccy_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
            ),
            skip_serializer=True,
        )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=[PriceNotation.MONE.value] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
        )

    def _transaction_details_quantity(self) -> pd.DataFrame:
        result: pd.DataFrame = pd.DataFrame(
            data=self.source_frame[SourceColumns.FILLED_AMOUNT].values,
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.TRANSACTION_DETAILS_QUANTITY],
        )

        if self.eu_desk_mask.any():
            result.loc[self.eu_desk_mask, RTS22TransactionColumns.TRANSACTION_DETAILS_QUANTITY] = (
                self.source_frame.loc[self.eu_desk_mask, SourceColumns.ROUTE_FILLED_AMOUNT]
            )

        return result

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=[QuantityNotation.UNIT.value] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION],
        )

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=[TradingCapacity.AOTC.value] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.pre_process_df[TempColumns.CONVERTED_DATETIME].values,
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME],
        )

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=[Venue.XOFF] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE],
        )

    def _transaction_details_venue(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=[Venue.XOFF] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.TRANSACTION_DETAILS_VENUE],
        )

    def _transmission_details_order_transmission_indicator(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=[True] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.TRANSMISSION_DETAILS_ORDER_TRANSMISSION_INDICATOR],
        )

    # Temp Fields
    def get_converted_datetime(self) -> pd.DataFrame:
        """Use Fill As Of Date + Fill As Of Time.

        If Fill As Of Time is lower than 16:35, change to 16:35

        If either the orig date is before the current fill date or if
        orig date = fill date, but time < cutoff time,
        we have to set the time to 16:35

        Use London timezone as default
        """
        cutoff_times = pd.Series(
            data=[pd.NA] * self.source_frame.shape[0],
            index=self.source_frame.index,
            name=TempColumns.FILL_AS_OF_TIME_CUTOFF,
        )
        four_thirty_five = "16:35:00"
        cutoff_time = datetime.strptime(four_thirty_five, TIME_FORMAT).time()
        date_as_datetimes = pd.to_datetime(
            self.source_frame.loc[:, SourceColumns.FILL_AS_OF_TIME],
            format=TIME_FORMAT,
        )
        time_mask = date_as_datetimes.dt.time < cutoff_time

        if not self.eu_desk_mask.all():
            # NOTE: We are not filtering for the UK Desk in this logical block specifically.
            # We are operating against the whole source_frame, and then we might
            # potentially overwrite the results for EU Desk in the next condition.
            # The rationale is that this compute is quite basic and the cost
            # of maintaining the combined logic of EU and UK Desk trades is not worth it.

            # Compare the original date with the fill as of date (which is updated to
            # the date of Friday's files from the file name if it is a Friday)
            date_mask = pd.to_datetime(
                self.source_frame.loc[:, PreNormalisedSourceColumns.ORIGINAL_FILL_DATE],
            ) < pd.to_datetime(
                self.source_frame.loc[:, SourceColumns.FILL_AS_OF_DATE],
            )
            status_mask = self.source_frame.loc[:, SourceColumns.STATUS].isin(
                ["Part-filled", "Working"]
            )
            time_or_previous_day_mask = time_mask | date_mask
            four_thirty_five_mask = status_mask & time_or_previous_day_mask
            cutoff_times[four_thirty_five_mask] = four_thirty_five

            # When we use ~four_thirty_five_mask, i.e., ~(status_mask & (time_mask | date_mask))
            # we implicitly check if date is >= orig date. However, as date cannot be > orig date
            # (Friday or non-Friday), this essentially will check if date is = orig date, along
            # with the time and status.
            cutoff_times[~four_thirty_five_mask] = self.source_frame.loc[
                ~four_thirty_five_mask, SourceColumns.FILL_AS_OF_TIME
            ]

        if self.eu_desk_mask.any():
            # For the EU Desk trades, the logic is slightly different.
            # If the ROUTED_AMOUNT is equal to the ROUTE_FILLED_AMOUNT,
            # it means the whole route is filled.
            # Hence, we always take the time of last fill.
            route_filled_mask = (
                self.source_frame.loc[:, SourceColumns.ROUTED_AMOUNT]
                == self.source_frame.loc[:, SourceColumns.ROUTE_FILLED_AMOUNT]
            )
            cutoff_times[self.eu_desk_mask & route_filled_mask] = self.source_frame.loc[
                self.eu_desk_mask & route_filled_mask, SourceColumns.FILL_AS_OF_TIME
            ]

            # If the Route is not filled, we check the time.
            # If the time is before 16:35, we set it to 16:35.
            # Otherwise, we use the time of the last partial fill.
            cutoff_times[self.eu_desk_mask & ~route_filled_mask & time_mask] = four_thirty_five
            cutoff_times[self.eu_desk_mask & ~route_filled_mask & ~time_mask] = (
                self.source_frame.loc[
                    self.eu_desk_mask & ~route_filled_mask & ~time_mask,
                    SourceColumns.FILL_AS_OF_TIME,
                ]
            )

        return run_join_date_and_time_format(
            source_frame=pd.concat(
                [cutoff_times, self.source_frame.loc[:, SourceColumns.FILL_AS_OF_DATE]], axis=1
            ),
            params=JoinDateTimeParams(
                source_date_attribute=SourceColumns.FILL_AS_OF_DATE,
                source_time_attribute=TempColumns.FILL_AS_OF_TIME_CUTOFF,
                source_format=JOIN_DATE_AND_TIME_FORMAT,
                timezone_info=DEFAULT_TIMEZONE,
                target_attribute=TempColumns.CONVERTED_DATETIME,
            ),
            skip_serializer=True,
        )

    # Party Fields
    def get_trader_notes_trader(self) -> pd.Series:
        """Get the first element of the string, either before " " or "-".

        e.g. "CEIF 175k" will return "CEIF" "CESF1" will return "CESF1"
        "TIC-DUE" will return "TIC"
        """
        not_null_account = self.source_frame.loc[:, SourceColumns.ACCOUNT].notnull()

        not_null_trader_notes = self.source_frame.loc[:, SourceColumns.TRADER_NOTES].notnull()

        trader = pd.Series(
            data=[pd.NA] * self.source_frame.shape[0],
            index=self.source_frame.index,
            name=TempColumns.TRADER_NOTES_TRADER,
        )

        trader[not_null_account] = self.source_frame.loc[not_null_account, SourceColumns.ACCOUNT]
        trader[~not_null_account & not_null_trader_notes] = (
            self.source_frame.loc[
                ~not_null_account & not_null_trader_notes, SourceColumns.TRADER_NOTES
            ]
            .astype(str)
            .str.split(" |-", regex=True)
            .str[0]
        )

        return trader

    def get_parties_tenant_lei(self) -> pd.DataFrame:
        return run_get_tenant_lei(  # type: ignore[no-any-return]
            source_frame=self.source_frame,
            params=GetTenantLEIParams(
                target_lei_column=TempColumns.TENANT_LEI,
                target_column_prefix=PartyPrefix.LEI,
            ),
            tenant=self.tenant,
            es_client=self.es_client,
            skip_serializer=True,
        )

    def get_parties_trader(self) -> pd.DataFrame:
        """Map as follows:

        If not Trader Name: Trader UUID
        else: Trader Name

        this method has an override for Chelverton
        """

        return run_map_conditional(  # type: ignore[no-any-return]
            source_frame=self.source_frame.loc[
                :, [SourceColumns.TRADER_UUID, SourceColumns.TRADER_NAME, SourceColumns.ACCOUNT]
            ],
            params=MapConditionalParams(
                target_attribute=TempColumns.TRADER,
                cases=[
                    Case(
                        query=f"`{SourceColumns.TRADER_NAME}`.isnull()",
                        attribute=SourceColumns.TRADER_UUID,
                    ),
                    Case(
                        query=f"`{SourceColumns.TRADER_NAME}`.notnull()",
                        attribute=SourceColumns.TRADER_NAME,
                    ),
                    Case(
                        query=f"`{SourceColumns.ACCOUNT}`.notnull()",
                        attribute=SourceColumns.ACCOUNT,
                    ),
                ],
            ),
            skip_serializer=True,
        )

    # Unused methods
    def _meta_model(self):
        pass

    def _report_details_investment_firm_covered_directive(self):
        pass

    def _report_details_trading_venue_transaction_id_code(self):
        pass

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(self):
        pass

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(self):
        pass

    def _traders_algos_waivers_indicators_waiver_indicator(self):
        pass

    def _transaction_details_branch_membership_country(self):
        pass

    def _transaction_details_complex_trade_component_id(self):
        pass

    def _transaction_details_cross_indicator(self):
        pass

    def _transaction_details_derivative_notional_change(self):
        pass

    def _transaction_details_net_amount(self):
        pass

    def _transaction_details_outgoing_order_addl_info(self):
        pass

    def _transaction_details_position_effect(self):
        pass

    def _transaction_details_position_id(self):
        pass

    def _transaction_details_price_not_applicable(self):
        pass

    def _transaction_details_price_pending(self):
        pass

    def _transaction_details_quantity_currency(self):
        pass

    def _transaction_details_record_type(self):
        pass

    def _transaction_details_settlement_amount(self):
        pass

    def _transaction_details_settlement_amount_currency(self):
        pass

    def _transaction_details_settlement_date(self):
        pass

    def _transaction_details_swap_directionalities(self):
        pass

    def _transaction_details_traded_quantity(self):
        pass

    def _transaction_details_trail_id(self):
        pass

    def _transaction_details_upfront_payment(self):
        pass

    def _transaction_details_upfront_payment_currency(self):
        pass

import itertools
import logging
import pandas as pd
from aries_se_core_tasks.core.integration_task import IntegrationTask
from aries_se_core_tasks.static import MetaModel
from enum import Enum
from mymarket_tasks.feeds.person.universal_steeleye_person.static import TempColumns
from mymarket_tasks.static import PersonColumns
from se_elastic_schema.models import Account<PERSON>erson, Market<PERSON>erson
from se_elasticsearch.repository.elasticsearch6 import ElasticsearchRepository
from typing import List, Optional, Tuple

logger = logging.getLogger(__name__)


class ActionType(str, Enum):
    CREATE = "create"
    UPDATE = "update"
    ES_DATA = "elastic_data"
    IGNORE = "ignore"  # TODO: If match with 2+ records on ES ignores the update


class SplitUniversalDuplicatedPerson(IntegrationTask):
    """This task receives a dataframe containing Account<PERSON>erson and Market<PERSON>erson
    data.

    It looks up the emails and names corresponding to the records in
    Elasticsearch for both models and split the dataframe: the rows not
    found is send to create and the rows found is send to update.
    """

    def _run(
        self,
        source_frame: pd.Data<PERSON>rame,
        tenant: str,
        es_client: ElasticsearchRepository,
        **kwargs,
    ) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """This task receives a dataframe containing AccountPerson and
        MarketPerson data. It looks up the emails and names corresponding to
        the records in Elasticsearch for both models and split the dataframe:
        the rows not found is send to create and the rows found is send to
        update.

        :params source_frame: DataFrame received from the feed to proccess
        :params es_client: ElasticsearchRepository instance
        :params logger: Logger for logging messages

        :returns: A Tuple containing three DataFrames: a df to create (with no match in ES),
            a df to update (with a match in ES) and a df with the corresponding rows fetched
            from the elastic.
        """
        if source_frame.empty or PersonColumns.META_MODEL not in source_frame.columns:
            logger.info(
                "Source frame empty or required columns not present, returning empty DataFrame"
            )
            return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

        df = source_frame.copy()

        # Split df into 2: one for AccountPerson and one for MarketPerson
        account_person_mask = df.loc[:, PersonColumns.META_MODEL] == MetaModel.ACCOUNT_PERSON
        market_person_mask = df.loc[:, PersonColumns.META_MODEL] == MetaModel.MARKET_PERSON
        account_person_df = df.loc[account_person_mask]
        market_person_df = df.loc[market_person_mask]

        feed_account_person_df, es_account_person_df = self._match_records_with_elastic(
            tenant=tenant,
            df=account_person_df,
            es_client=es_client,
            model=MetaModel.ACCOUNT_PERSON,
        )
        feed_market_person_df, es_market_person_df = self._match_records_with_elastic(
            tenant=tenant,
            df=market_person_df,
            es_client=es_client,
            model=MetaModel.MARKET_PERSON,
        )

        feed_person_df = pd.concat([feed_account_person_df, feed_market_person_df])
        create_person_df = feed_person_df[feed_person_df[TempColumns.ACTION] != ActionType.UPDATE]
        if not create_person_df.empty:
            create_person_df = create_person_df.drop(
                columns=[
                    TempColumns.EMAIL,
                    TempColumns.GROUP_BY_INDEX,
                    TempColumns.DUPLICATE_ACTION,
                    TempColumns.SOURCE_INDEX,
                ]
            )

        es_person_df = pd.concat([es_account_person_df, es_market_person_df])
        update_person_df = feed_person_df[feed_person_df[TempColumns.ACTION] == ActionType.UPDATE]
        if not es_person_df.empty:
            update_person_df = update_person_df.drop(
                columns=[
                    TempColumns.EMAIL,
                    TempColumns.ACTION,
                    TempColumns.SOURCE_INDEX,
                ]
            )
            es_person_df = es_person_df.drop(columns=[TempColumns.EMAIL])

        return create_person_df.reset_index(drop=True), update_person_df, es_person_df

    @classmethod
    def _match_records_with_elastic(
        cls,
        df: pd.DataFrame,
        tenant: str,
        es_client: ElasticsearchRepository,
        model: MetaModel,
    ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """All emails and names presented in the feed DataFrame are used to
        look for person records in elastic. The ACTION column from the feed
        DataFrame are set to 'update' for those that have a match. The feed and
        the matched elastic records are returned with temporary columns named
        'GROUP_BY_INDEX' and 'MATCHED_FEED_INDEX', respectively, that create
        the correspondence.

        :param df: DataFrame received from the feed that is the base of the lookup
        :param es_client: ElasticsearchRepository instance
        :param model: The model class to search in elastic
        :param logger: Logger for logging messages

        :returns: A tuple with two DataFrames. The feed and the elastic DataFrame,
            both updated with the relational column.
        """
        df[TempColumns.ACTION] = pd.NA

        # Create a column to group the dataframe
        df[TempColumns.GROUP_BY_INDEX] = df.index
        name_list = df.loc[:, PersonColumns.NAME].dropna().unique().tolist()

        # Explode dataframe by email in a new column, preparing the match with the elastic result
        df[TempColumns.EMAIL] = df[PersonColumns.COMMUNICATIONS_EMAILS]
        exploded_df = df.explode(TempColumns.EMAIL)
        email_list = exploded_df[TempColumns.EMAIL].dropna().unique().tolist()

        # Fetch all data from elastic that match email OR name with the data from the feed
        fetched_person_records = cls._fetch_person_from_es(
            tenant=tenant,
            field_names=[PersonColumns.COMMUNICATIONS_EMAILS, PersonColumns.NAME],
            ids=email_list + name_list,
            es_client=es_client,
            model=model,
        )

        if fetched_person_records.empty:
            logger.info(
                f"No records found in Elastic with the following names: {name_list} or emails:"
                f" {email_list}"
            )
            return cls._eliminate_duplicated_rows(
                feed_df=df,
                elastic_df=fetched_person_records,
                es_client=es_client,
            )

        # Explode elastic rows by emails, preparing the match with the feed datafrmame
        if PersonColumns.COMMUNICATIONS_EMAILS not in fetched_person_records.columns:
            fetched_person_records[PersonColumns.COMMUNICATIONS_EMAILS] = (
                fetched_person_records.apply(lambda _: [], axis=1)
            )

        fetched_person_records[TempColumns.EMAIL] = fetched_person_records[
            PersonColumns.COMMUNICATIONS_EMAILS
        ].astype(object)

        fetched_person_records = fetched_person_records.explode(TempColumns.EMAIL).reset_index(
            drop=True
        )
        # Create column MATCHED_FEED_INDEX in elastic df. A pd.NA value means it does
        # not have a match with any feed row.
        fetched_person_records[TempColumns.MATCHED_FEED_INDEX] = pd.NA

        # Match the data from the feed with elastic result. It follows the priority:
        # 1 - Only email
        # 2 - Only name
        feed_df, elastic_df = cls._match_rows_by_columns(
            exploded_df,
            fetched_person_records,
            columns=[TempColumns.EMAIL],
            es_client=es_client,
        )
        feed_df, elastic_df = cls._match_rows_by_columns(
            feed_df,
            elastic_df,
            columns=[PersonColumns.NAME],
            es_client=es_client,
            row_filter=feed_df[
                TempColumns.EMAIL
            ].isna(),  # SPI-1711: Only match by name when email is not available
        )

        # Clean elastic_df for non-matched records where MATCHED_FEED_INDEX still pd.NA
        elastic_df = elastic_df[~pd.isna(elastic_df[TempColumns.MATCHED_FEED_INDEX])]

        return cls._eliminate_duplicated_rows(
            feed_df=feed_df,
            elastic_df=elastic_df,
            es_client=es_client,
        )

    @classmethod
    def _fetch_person_from_es(
        cls,
        tenant: str,
        field_names: List[str],
        ids: List[str],
        es_client: ElasticsearchRepository,
        model: MetaModel,
    ) -> pd.DataFrame:
        """Fetch person records from elastic based in a list of values.

        :params field_name: Field to lookup
        :params ids: List of values used in the search
        :params es_client: DataFrame created fetched rom the elastic
        :params model: Model name to lookup
        :params logger: Logger for logging messages

        :returns: DataFrame containing all results with a match
        """

        logger.info(f"Fetching {len(ids)} {field_names} from {model}")
        query = cls._get_terms_query_multiple_lookup_fields(
            ids=ids,
            es_client=es_client,
            lookup_fields=field_names,
            model_field=model,
        )

        if model == MetaModel.MARKET_PERSON:
            alias = MarketPerson.get_elastic_index_alias(tenant=tenant)
        else:
            alias = AccountPerson.get_elastic_index_alias(tenant=tenant)

        logger.info(f"Scrolling {alias}")
        df: pd.DataFrame = es_client.scroll(query=query, index=alias)

        if df.empty:
            logger.info(f"No records found in Elastic with the following {field_names}s: {ids}.")
            df[es_client.meta.id] = pd.NA

        return df

    @classmethod
    def _match_rows_by_columns(
        cls,
        feed_df: pd.DataFrame,
        elastic_df: pd.DataFrame,
        columns: List[str],
        es_client: ElasticsearchRepository,
        row_filter: Optional[pd.Series] = None,
    ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Match rows from the feed and the elastic df based in the columns
        received as arguments. Any row that has a match on the feed will
        receive an 'update' value on the UPDATE column, and its GROUP_BY_INDEX
        value will be copied to the correspondant MATCHED_FEED_INDEX in the
        elastic df.

        :params feed_df: DataFrame created from the feed
        :params elastic_df: DataFrame created fetched rom the elastic
        :params columns: Columns used as the base to the match

        :returns: A tuple with two DataFrames. The feed DataFrame and the elastic DataFrame,
            both updated with the relational column.
        """
        # Filter the feed_df for non-matched rows
        filtered_feed_df = feed_df[feed_df[TempColumns.ACTION] != ActionType.UPDATE]
        if row_filter is not None:
            filtered_feed_df = filtered_feed_df[row_filter]
        # Filter the elastic data for non-matched rows (MATCHED_FEED_INDEX == pd.NA)
        filtered_elastic_df = elastic_df[elastic_df[TempColumns.MATCHED_FEED_INDEX].isna()]
        if filtered_feed_df.empty or filtered_elastic_df.empty:
            return feed_df, elastic_df

        # Create a temporary df only with matched data (inner) from feed and elastic
        merged_df = pd.merge(
            filtered_feed_df,
            filtered_elastic_df,
            how="inner",
            on=columns,
        )
        # Create a mask to identify each row from the feed has a match in elastic
        matched_mask = feed_df[TempColumns.GROUP_BY_INDEX].isin(
            merged_df[TempColumns.GROUP_BY_INDEX]
        )
        # Update the ACTION column from feed_df for rows with a match in elastic
        feed_df.loc[matched_mask, TempColumns.ACTION] = ActionType.UPDATE
        # Drop duplicates to avoid m:m merge
        merged_df = merged_df.drop_duplicates(subset=[es_client.meta.id], keep="first")
        # Prepare a df to help update MATCHED_FEED_INDEX in elastic with the
        # GROUP_BY_INDEX from the feed_df
        temp_elastic_df = pd.merge(
            elastic_df,
            merged_df,
            how="left",
            on=es_client.meta.id,
        )
        # Update MATCHED_FEED_INDEX from the elastic_df with the GROUP_BY_INDEX from the
        # feed_df, for each row that has a match
        elastic_df[TempColumns.MATCHED_FEED_INDEX] = elastic_df[
            TempColumns.MATCHED_FEED_INDEX
        ].combine_first(temp_elastic_df[TempColumns.GROUP_BY_INDEX])
        # If a row in a feed group should be updated (TempColumn.ACTION == "update"),
        # all elements in the group are marked to update
        grouped_feed_df = feed_df.groupby(TempColumns.GROUP_BY_INDEX)
        feed_df = grouped_feed_df.apply(cls._update_group_to_the_same_action_type)
        # If a row in an elastic group has a match with the feed (MATCHED_FEED_INDEX !=
        # pd.NA), keep only the rows with a match and drop the others
        grouped_elastic_df = elastic_df.groupby(es_client.meta.id)
        elastic_df = grouped_elastic_df.apply(cls._merge_duplicated_rows_from_elastic)
        return feed_df.reset_index(drop=True), elastic_df.reset_index(drop=True)

    @classmethod
    def _update_group_to_the_same_action_type(cls, group):
        """If there is at least one row to update in the group, eliminate all
        other rows.

        Each row in a group represents the same record, if we have a
        match we can ignore the others.
        """
        if (group[TempColumns.ACTION] == ActionType.UPDATE).any():
            return group[group[TempColumns.ACTION] == ActionType.UPDATE]

        return group

    @classmethod
    def _merge_duplicated_rows_from_elastic(cls, group):
        """Any element in the group that has a TempColumns.MATCHED_FEED_INDEX
        matches with an element in the feed.

        This row is eliminated from the group to avoid new match with
        different parameters.
        """
        if (group[TempColumns.MATCHED_FEED_INDEX] != pd.NA).any():
            return group[group[TempColumns.MATCHED_FEED_INDEX] != pd.NA]

        return group

    @classmethod
    def _eliminate_duplicated_rows(
        cls,
        feed_df: pd.DataFrame,
        elastic_df: pd.DataFrame,
        es_client: ElasticsearchRepository,
    ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Eliminate duplicate rows created with explode().

        Duplicated rows are identified: by the same
        TempColumn.GROUP_BY_INDEX for the feed DataFrame or by the same
        es_client.meta.id for the elastic DataFrame.
        """
        feed_df = feed_df.drop_duplicates(subset=TempColumns.GROUP_BY_INDEX, keep="first")
        elastic_df = elastic_df.drop_duplicates(subset=es_client.meta.id, keep="first")
        return feed_df.reset_index(drop=True), elastic_df.reset_index(drop=True)

    @classmethod
    def _get_terms_query_multiple_lookup_fields(
        cls,
        ids: List[str],
        es_client: ElasticsearchRepository,
        lookup_fields: List[str],
        model_field: str,
        source_field: Optional[str | list] = None,
    ) -> dict:
        """Returns a valid Elasticsearch terms query using the list of ids and
        multiple lookup fields. It takes the max terms size into account and
        splits the query accordingly. It also takes an optional source field to
        restrict the results, and uses the schema model in the query as well.

        :param ids: List of ids which are used in the query
        :param es_client: ElasticsearchRepository
        :param source_field: Optional source field(s) used to restrict the results
        :param lookup_fields: list of fields which needs to be looked up
        :param model_field: the schema model
        :returns: Valid elasticsearch query which fetches MarketPerson and/or
                AccountPerson records for the given ids.
        :rtype: dict
        """
        query = {
            "query": {
                "bool": {
                    "filter": [{"term": {"&model": model_field}}],
                    "minimum_should_match": 1,
                    "must_not": {"exists": {"field": "&expiry"}},
                }
            }
        }

        shoulds = []
        if len(ids) > es_client.MAX_TERMS_SIZE:
            ids_chunks = [
                ids[ix : ix + es_client.MAX_TERMS_SIZE]
                for ix in range(0, len(ids), es_client.MAX_TERMS_SIZE)
            ]
            for chunk, field in itertools.product(ids_chunks, lookup_fields):
                shoulds.append({"terms": {field: chunk}})
        else:
            shoulds.extend({"terms": {field: ids}} for field in lookup_fields)
        query["query"]["bool"]["should"] = shoulds
        if source_field:
            query["_source"] = source_field  # type: ignore[assignment]
        return query


def run_split_universal_duplicated_person(
    source_frame: pd.DataFrame,
    tenant: str,
    es_client: ElasticsearchRepository,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    **kwargs,
):
    task = SplitUniversalDuplicatedPerson(app_metrics_path=app_metrics_path, audit_path=audit_path)
    return task.run(source_frame=source_frame, tenant=tenant, es_client=es_client, **kwargs)

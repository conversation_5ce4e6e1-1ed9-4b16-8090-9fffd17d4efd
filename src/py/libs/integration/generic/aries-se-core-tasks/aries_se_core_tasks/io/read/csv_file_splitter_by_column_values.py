import csv
import fsspec
import logging
import pandas as pd
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.core.integration_task import IntegrationTask
from aries_se_core_tasks.core.params import TaskParams
from aries_se_core_tasks.io.read.utils import unquote_list_of_strings
from chardet import UniversalDetector
from dataclasses import dataclass
from inspect import signature
from pathlib import Path
from pydantic import Field, root_validator
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from typing import Any, Callable, Dict, List, Optional, Tuple

logger_ = logging.getLogger(__name__)


@dataclass
class HeaderInfo:
    csv_delimiter: str
    target_indexes: Dict[str, int]


@dataclass
class LinesPerGroup:
    lines_per_group_values: Dict[Tuple, List[int]]
    total_number_of_lines: int


@dataclass
class LinesPerBatch:
    batch_indexes: Dict[int, List[int]]
    batch_skiprows_dict: Dict[int, List[int]]


class PreProcessFunc(TaskParams):
    func: Callable = Field(
        description="Function to be called on each chunk of the dataframe "
        "before it is written to disk"
    )
    func_kwargs: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Keyword arguments to be passed to the function",
    )

    @root_validator
    def func_must_have_arg_df(cls, values):
        func = values.get("func")
        func_args = signature(func).parameters

        if "df" not in func_args:
            raise ValueError(
                f"The `df` argument is mandatory in the pre-process function: {func.__name__}. "
            )
        return values


class CsvFileSplitterByColumnValues(IntegrationTask):
    """This task generates FileSplitterResults by taking in a CSV, and a list
    of target_columns. In the case of there being multiple header lines, it is
    assumed that the target columns are in the same index on all.

    For these target columns, each unique combination of characters is saved
    in a tuple, which will then be used as the key for a dictionary where the
    values are the row indexes for each occurrence of that combination of values.

    Those lists of values are then aggregated to not split up while avoiding
    going over the batch size when possible. They are then converted to lists of
    rows to skip, so that we can pass that to read_csv to create a FileSplitterResult
    for each batch created.

    Example:
        CSV:
            col1, col2, col3, col4, col5
            A, Blue, 10, Dog, 11
            A, Blue, 20, Cat, 22
            A, Red, 30, Dog, 33
            A, Red, 40, Cat, 44
            B, Red, 50, Dog, 55
            B, Red, 60, Cat, 66
            A, Red, 70, Cat, 77

        Using as target [col1, col4] it will generate the dictionary:
        {
            (A, Dog): [1,3],
            (A, Cat): [2,4,7],
            (B, Dog): [5],
            (B, Cat): [6],
        }

        With batch_size = 2 will generate the following batches:
        1:
            __swarm_raw_index__, col1, col2, col3, col4, col5
            1, A, Blue, 10, Dog, 11
            3, A, Red, 30, Dog, 33
        2:
            __swarm_raw_index__, col1, col2, col3, col4, col5
            2, A, Blue, 20, Cat, 22
            4, A, Red, 40, Cat, 44
            7, A, Red, 70, Cat, 77
        3:
            __swarm_raw_index__, col1, col2, col3, col4, col5
            5, B, Red, 50, Dog, 55
            6, B, Red, 60, Cat, 66
    """

    def _run(
        self,
        csv_path: Path,
        target_columns: List[str],
        batch_size: int,
        skiprows: List[int],
        number_of_headers: int,
        index_name: str,
        delimiter: str | None,
        update_metrics_input_count: bool,
        pre_process_func: PreProcessFunc | None,
        propagate_index: bool,
        unquote_header: bool,
        **kwargs,
    ) -> List[FileSplitterResult]:
        header_lines = self.get_header_lines(skiprows=skiprows, number_of_headers=number_of_headers)

        file_encoding = self.get_encoding_from_file(csv_path=csv_path)

        header_info = self.get_info_from_header(
            csv_path=csv_path,
            header_lines=header_lines,
            encoding=file_encoding,
            delimiter=delimiter,
            target_columns=target_columns,
            unquote_header=unquote_header,
        )

        try:
            lines_per_group = self.get_lines_per_group_values(
                csv_path=csv_path,
                delimiter=header_info.csv_delimiter,
                header_lines=header_lines,
                encoding=file_encoding,
                skiprows=skiprows,
                target_indexes=header_info.target_indexes,
            )

        except UnicodeDecodeError:
            lines_per_group = self.get_lines_per_group_values(
                csv_path=csv_path,
                delimiter=header_info.csv_delimiter,
                header_lines=header_lines,
                encoding="UTF-8",
                skiprows=skiprows,
                target_indexes=header_info.target_indexes,
            )

        lines_per_batch = self.get_lines_per_batch(
            lines_per_values=lines_per_group.lines_per_group_values,
            batch_size=batch_size,
            total_number_of_lines=lines_per_group.total_number_of_lines,
            header_lines=header_lines,
            skiprows=skiprows,
        )

        input_total_count: int = (
            lines_per_group.total_number_of_lines - len(skiprows) - len(header_lines)
        )

        try:
            file_splitter_results = self.create_file_splitter_results(
                csv_path=csv_path,
                skiprows_per_batch=lines_per_batch.batch_skiprows_dict,
                batch_indexes=lines_per_batch.batch_indexes,
                index_name=index_name,
                encoding=file_encoding,
                delimiter=header_info.csv_delimiter,
                input_total_count=input_total_count,
                pre_process_func=pre_process_func,
                propagate_index=propagate_index,
            )
        except UnicodeDecodeError:
            file_splitter_results = self.create_file_splitter_results(
                csv_path=csv_path,
                skiprows_per_batch=lines_per_batch.batch_skiprows_dict,
                batch_indexes=lines_per_batch.batch_indexes,
                index_name=index_name,
                encoding="UTF-8",
                delimiter=header_info.csv_delimiter,
                input_total_count=input_total_count,
                pre_process_func=pre_process_func,
                propagate_index=propagate_index,
            )

        if update_metrics_input_count:
            self.update_app_metrics(
                field=GenericAppMetricsEnum.INPUT_COUNT,
                value=input_total_count,
            )

        return file_splitter_results

    @staticmethod
    def get_header_lines(skiprows: List[int], number_of_headers: int) -> List[int]:
        """Assumes csv header is on line 0, else, gets earliest line excluding
        skiprows lines.

        Assumes header lines are all in a row

        :param skiprows: lines to skip when reading csv
        :param number_of_headers: number of header lines
        :return: header lines
        """
        header_line = 0
        if skiprows:
            while header_line in skiprows:
                header_line += 1

        header_lines = list(range(header_line, number_of_headers + header_line))

        return header_lines

    @staticmethod
    def get_encoding_from_file(csv_path: Path) -> str:
        """Reads the file in chunks and detects the encoding for each. Outputs
        the most common detected encoding.

        :param csv_path: file path
        :return: encoding
        """

        with fsspec.open(csv_path.as_posix(), mode="rb") as csv_file:
            # implemented according to https://chardet.readthedocs.io/en/latest/usage.html#advanced-usage

            detector = UniversalDetector()
            for line in csv_file.readlines():
                detector.feed(line)
                if detector.done:
                    break
            detector.close()

        chardet_result = detector.result.get("encoding")

        if chardet_result:
            return chardet_result

        return "utf-8"

    @staticmethod
    def get_delimiter(line_text: str) -> str:
        """Finds delimiter from header line.

        :param line_text: text from csv line
        :return: delimiter
        """
        sniffer = csv.Sniffer()
        csv_delimiter = sniffer.sniff(line_text).delimiter
        return str(csv_delimiter)

    def get_info_from_header(
        self,
        csv_path: Path,
        header_lines: List[int],
        encoding: str,
        delimiter: str | None,
        target_columns: List[str],
        unquote_header: bool,
    ) -> HeaderInfo:
        """Reads the header line of the file and outputs needed info to perform
        the batching.

        :param csv_path: csv file path
        :param header_lines: header lines
        :param encoding: csv encoding
        :param delimiter: csv delimiter
        :param target_columns: task target columns
        :param unquote_header: Removes double quote characters from the header as well as \r
        :return: encoding, delimiter and target indexes dictionary
        """

        with fsspec.open(csv_path.as_posix(), mode="rb") as csv_file:
            for line_number, line_bytes in enumerate(csv_file):
                header_bytes: bytes = line_bytes
                if line_number == header_lines[0]:
                    break

        header_text = header_bytes.decode(encoding=encoding)

        csv_delimiter = delimiter if delimiter else self.get_delimiter(line_text=header_text)

        header_text_split: List[str] = header_text.strip().split(csv_delimiter)
        if unquote_header:
            header_text_split = unquote_list_of_strings(list_str=header_text_split)

        target_indexes = {
            col: header_text_split.index(col) for col in target_columns if col in header_text_split
        }

        return HeaderInfo(csv_delimiter=csv_delimiter, target_indexes=target_indexes)

    @staticmethod
    def get_lines_per_group_values(
        csv_path: Path,
        delimiter: str,
        encoding: str,
        header_lines: List[int],
        skiprows: List[int],
        target_indexes: Dict[str, int],
    ) -> LinesPerGroup:
        """Reads the file line by line and creates a dict where the keys are
        the unique groups of values for the target rows and the values are
        lists of the rows where those values are present Only keeps group
        values with multiple lines.

        :param csv_path: csv path
        :param delimiter: csv delimiter
        :param encoding: csv encoding
        :param header_lines: header lines
        :param skiprows: skiprows lines
        :param target_indexes: indexes of the target columns
        :return: lines per group of values, and total number of lines
        """
        lines_per_values: Dict[Tuple, List[int]] = {}
        total_number_of_lines: int = 0

        with fsspec.open(csv_path.as_posix(), mode="r", encoding=encoding) as csv_file:
            csv_reader = csv.reader(csv_file, delimiter=delimiter)

            for line_number, line_text in enumerate(csv_reader):
                total_number_of_lines += 1

                if line_number in skiprows + header_lines:
                    continue

                group_values = tuple([line_text[idx] for idx in list(target_indexes.values())])

                if not any(group_values):
                    continue

                if group_values in lines_per_values:
                    lines_per_values[group_values].append(line_number)
                else:
                    lines_per_values[group_values] = [line_number]

        lines_per_values = {k: v for k, v in lines_per_values.items() if len(v) > 1}

        return LinesPerGroup(
            lines_per_group_values=lines_per_values, total_number_of_lines=total_number_of_lines
        )

    @staticmethod
    def get_lines_per_batch(
        lines_per_values: Dict[Tuple, List[int]],
        batch_size: int,
        total_number_of_lines: int,
        header_lines: List[int],
        skiprows: List[int],
    ) -> LinesPerBatch:
        """Given the lists of indexes grouped by column values, it creates
        batches while never separating these lines, and only going over the
        batch size if a single group is bigger than the size.

        After having the lists of indexes per batch, it creates the reverse,
        the lists of indexes to skip, to be passed to pd.read_csv in order
        to create the batches csv downstream

        :param lines_per_values: lines per group of values
        :param batch_size: batch size
        :param total_number_of_lines: total number of lines in the file
        :param header_lines: line of the header
        :param skiprows: rows to skip
        :return: indexes and skiprows per batch
        """
        all_group_indexes: List[int] = [
            index for list_of_indexes in lines_per_values.values() for index in list_of_indexes
        ]
        non_group_indexes: List[int] = list(
            set(range(total_number_of_lines)) - set(header_lines + skiprows + all_group_indexes)
        )

        current_batch: int = 0
        batch_indexes: Dict[int, List[int]] = {current_batch: []}

        # group together rows based on their size
        for list_of_indexes in lines_per_values.values():
            current_batch_not_empty = len(batch_indexes[current_batch]) > 0

            group_too_big_for_current_batch = (
                len(batch_indexes[current_batch]) + len(list_of_indexes)
            ) > batch_size

            current_batch_not_empty_and_group_too_big_for_current_batch = (
                current_batch_not_empty and group_too_big_for_current_batch
            )

            current_group_bigger_than_max_size_and_not_first_batch = (
                len(list_of_indexes) > batch_size
            ) and current_batch > 0

            if (
                current_batch_not_empty_and_group_too_big_for_current_batch
                or current_group_bigger_than_max_size_and_not_first_batch
            ):
                current_batch += 1
                batch_indexes[current_batch] = []

            batch_indexes[current_batch].extend(list_of_indexes)

        # add non grouped rows to the existing batches
        for batch_values in batch_indexes.values():
            batch_to_max_difference = batch_size - len(batch_values)
            if batch_to_max_difference > 0 and len(non_group_indexes) > 0:
                batch_values.extend(non_group_indexes[:batch_to_max_difference])
                non_group_indexes = non_group_indexes[batch_to_max_difference:]

        # add any leftover rows
        while non_group_indexes:
            batch_indexes[len(batch_indexes)] = non_group_indexes[:batch_size]
            non_group_indexes = non_group_indexes[batch_size:]

        # convert batch indexes into indexes to skip per batch
        batch_skiprows_dict: Dict[int, List[int]] = {}
        for batch, list_of_indexes in batch_indexes.items():
            batch_skiprows: List[int] = list(
                set(range(total_number_of_lines)) - set(list_of_indexes + header_lines)
            )
            batch_skiprows_dict[batch] = batch_skiprows

        for i in range(len(batch_indexes)):
            batch_indexes[i] = header_lines[1:] + batch_indexes[i]

        return LinesPerBatch(batch_indexes=batch_indexes, batch_skiprows_dict=batch_skiprows_dict)

    @staticmethod
    def create_file_splitter_results(
        csv_path: Path,
        skiprows_per_batch: Dict[int, List[int]],
        batch_indexes: Dict[int, List[int]],
        index_name: str,
        encoding: str,
        delimiter: str,
        input_total_count: int,
        pre_process_func: PreProcessFunc | None,
        propagate_index: bool,
    ) -> List[FileSplitterResult]:
        """For each defined batch, create a FileSplitterResult object.

        :param csv_path: csv path
        :param skiprows_per_batch: lines to skip per batch
        :param batch_indexes: batch index
        :param index_name: index name
        :param encoding: csv encoding
        :param delimiter: csv delimiter
        :param input_total_count: total number of input rows
        :param pre_process_func: Pre-processing function to be applied to each chunk
        :param propagate_index: Propagates the index to the output CSV file if True
        :return: list of FileSplitterResults
        """

        files_splitter_result_list = []

        for batch_index, skiprows in skiprows_per_batch.items():
            batch_path = csv_path.parent.joinpath(
                csv_path.stem + f"_batch_{batch_index}" + csv_path.suffix
            )

            df_chunk = pd.read_csv(
                filepath_or_buffer=csv_path.as_posix(),
                encoding=encoding,
                delimiter=delimiter,
                skiprows=sorted(skiprows),
                dtype=str,
            )

            df_chunk.index = pd.Index(sorted(batch_indexes[batch_index]))
            df_chunk.index.name = index_name

            if pre_process_func:
                pre_process_func_kwargs = pre_process_func.func_kwargs

                logger_.info(
                    f"Applying pre-process function: {pre_process_func.func.__name__} with kwargs: "
                    f"{pre_process_func_kwargs} "
                    f"against batch {csv_path}"
                )

                df_chunk = pre_process_func.func(df_chunk, **pre_process_func_kwargs)

            df_chunk.to_csv(
                path_or_buf=batch_path,
                encoding=encoding,
                quoting=csv.QUOTE_ALL,
                index=propagate_index,
            )

            files_splitter_result_list.append(
                FileSplitterResult(
                    path=batch_path,
                    batch_index=batch_index,
                    input_total_count=input_total_count,
                    encoding=encoding,
                )
            )

        return files_splitter_result_list


def run_csv_file_splitter_by_column_values(
    csv_path: Path,
    target_columns: List[str],
    batch_size: int,
    skiprows: List[int] | None = None,
    number_of_headers: int = 1,
    index_name: str = "__swarm_raw_index__",
    delimiter: str | None = None,
    update_metrics_input_count: bool = True,
    pre_process_func: PreProcessFunc | None = None,
    propagate_index: bool = True,
    unquote_header: bool = True,
    **kwargs,
):
    if skiprows is None:
        skiprows = []

    task = CsvFileSplitterByColumnValues()

    return task.run(
        csv_path=csv_path,
        target_columns=target_columns,
        batch_size=batch_size,
        skiprows=skiprows,
        number_of_headers=number_of_headers,
        index_name=index_name,
        delimiter=delimiter,
        update_metrics_input_count=update_metrics_input_count,
        pre_process_func=pre_process_func,
        propagate_index=propagate_index,
        unquote_header=unquote_header,
        **kwargs,
    )

import os
import pandas as pd
import pytest
from aries_se_core_tasks.io.read.csv_file_splitter_by_column_values import (
    PreProcessFunc,
    run_csv_file_splitter_by_column_values,
)
from pathlib import Path

SCRIPT_PATH = Path(__file__).parent
DATA_PATH = SCRIPT_PATH.joinpath("data", "csv")
INPUT_CSV_PATH = DATA_PATH.joinpath("csv_fs_by_column_values.csv")
INPUT_CSV_QUOTED_PATH = DATA_PATH.joinpath("csv_fs_by_column_values_quoted.csv")
TEST_FILE_ISO_ENCODING_CHARACTERS = DATA_PATH.joinpath(
    r"csv_fs_by_column_values_unknown_characters_iso.csv"
)
EXPECTED_CSV_BATCH_0_PATH = DATA_PATH.joinpath("csv_fs_by_column_values_expected_batch_0.csv")
EXPECTED_CSV_BATCH_0_PRE_PROCESS_PATH = DATA_PATH.joinpath(
    "csv_fs_by_column_values_expected_batch_pre_process_0.csv"
)
EXPECTED_CSV_BATCH_1_PRE_PROCESS_PATH = DATA_PATH.joinpath(
    "csv_fs_by_column_values_expected_batch_pre_process_1.csv"
)
EXPECTED_CSV_BATCH_1_PATH = DATA_PATH.joinpath("csv_fs_by_column_values_expected_batch_1.csv")
EXPECTED_CSV_ISO_BATCH_0_PATH = DATA_PATH.joinpath(
    "csv_fs_by_column_values_iso_expected_batch_0.csv"
)
EXPECTED_CSV_ISO_BATCH_1_PATH = DATA_PATH.joinpath(
    "csv_fs_by_column_values_iso_expected_batch_1.csv"
)
INPUT_MULTIPLE_HEADER_CSV_PATH = DATA_PATH.joinpath("csv_fs_by_column_values_multi_header.csv")
EXPECTED_MULTIPLE_HEADER_CSV_BATCH_0_PATH = DATA_PATH.joinpath(
    "csv_fs_by_column_values_expected_batch_0_multi_header.csv"
)
EXPECTED_MULTIPLE_HEADER_CSV_BATCH_1_PATH = DATA_PATH.joinpath(
    "csv_fs_by_column_values_expected_batch_1_multi_header.csv"
)


class TestCsvFileSplitterByColumnValues:
    # test that the Task supports quoted and unquoted headers
    @pytest.mark.parametrize(
        "csv_path",
        [
            (INPUT_CSV_PATH),
            (INPUT_CSV_QUOTED_PATH),
        ],
    )
    def test_batching_result(self, csv_path: Path):
        result = run_csv_file_splitter_by_column_values(
            csv_path=csv_path,
            target_columns=["column_1"],
            batch_size=5,
            skiprows=[0],
        )

        assert len(result) == 2
        assert result[0].input_total_count == 9

        result_df_1 = pd.read_csv(result[0].path)
        result_df_2 = pd.read_csv(result[1].path)

        # result_df_1 batch contains 5 rows:
        # 4 rows with `column_1` value `id1_0` and 1 row with `column_1` value `pd.NA`
        # note that the `pd.NA` row is included in the first batch because the second
        # occurring `column_1` group is `id1_1` which cannot be part of the first batch
        # as it would exceed the batch size of 5
        assert result_df_1.shape[0] == 5
        assert result_df_2.shape[0] == 4

        pd.testing.assert_frame_equal(
            result_df_1,
            pd.read_csv(EXPECTED_CSV_BATCH_0_PATH.as_posix()),
        )
        pd.testing.assert_frame_equal(
            result_df_2,
            pd.read_csv(EXPECTED_CSV_BATCH_1_PATH.as_posix()),
        )

        for file_splitter in result:
            os.unlink(file_splitter.path)

    def test_batching_result_with_pre_process_func(self):
        def filter_rows_by_prefix(df: pd.DataFrame, prefix: str, col_name: str):
            # Filter out rows where the specified column starts with the given prefix
            prefix_mask = df.loc[:, col_name].str.startswith(prefix)
            return df.loc[~prefix_mask]

        result = run_csv_file_splitter_by_column_values(
            csv_path=INPUT_CSV_PATH,
            target_columns=["column_1"],
            batch_size=5,
            skiprows=[0],
            pre_process_func=PreProcessFunc(
                func=filter_rows_by_prefix,
                func_kwargs={"prefix": "q", "col_name": "column_3"},
            ),
        )

        assert len(result) == 2
        assert result[0].input_total_count == 9

        result_df_1 = pd.read_csv(result[0].path)
        result_df_2 = pd.read_csv(result[1].path)

        # result_df_1 batch contains 4 rows instead of 5 due to the pre-processing function
        # which filtered out the row `id1_0,id2_1,qwe`
        assert result_df_1.shape[0] == 4
        assert result_df_2.shape[0] == 4

        pd.testing.assert_frame_equal(
            result_df_1,
            pd.read_csv(EXPECTED_CSV_BATCH_0_PRE_PROCESS_PATH.as_posix()),
        )
        pd.testing.assert_frame_equal(
            result_df_2,
            pd.read_csv(EXPECTED_CSV_BATCH_1_PRE_PROCESS_PATH.as_posix()),
        )

        for file_splitter in result:
            os.unlink(file_splitter.path)

    def test_multiple_header_file(self):
        result = run_csv_file_splitter_by_column_values(
            csv_path=INPUT_MULTIPLE_HEADER_CSV_PATH,
            target_columns=["column_1"],
            batch_size=5,
            skiprows=[0],
            number_of_headers=3,
        )

        assert len(result) == 2
        assert result[0].input_total_count == 9

        pd.testing.assert_frame_equal(
            pd.read_csv(result[0].path),
            pd.read_csv(EXPECTED_MULTIPLE_HEADER_CSV_BATCH_0_PATH.as_posix()),
        )
        pd.testing.assert_frame_equal(
            pd.read_csv(result[1].path),
            pd.read_csv(EXPECTED_MULTIPLE_HEADER_CSV_BATCH_1_PATH.as_posix()),
        )

        for file_splitter in result:
            os.unlink(file_splitter.path)

    @pytest.mark.parametrize(
        "target_columns, batch_size, final_batches_result",
        [
            (["column_1"], 5, 2),
            (["column_1"], 10, 1),
            (["column_1"], 2, 3),
            (["column_1", "column_2"], 2, 5),
            (["column_3"], 3, 3),
            (["column_3"], 1, 9),
        ],
    )
    def test_batching_process(self, target_columns, batch_size, final_batches_result):
        result = run_csv_file_splitter_by_column_values(
            csv_path=INPUT_CSV_PATH,
            target_columns=target_columns,
            batch_size=batch_size,
            skiprows=[0],
        )

        assert len(result) == final_batches_result
        assert result[0].input_total_count == 9

        for file_splitter in result:
            os.unlink(file_splitter.path)

    def test_result_with_iso_8859_1_encoding(self):
        """
        Test for the case where the detected encoding is ISO-8859-1
        """
        result = run_csv_file_splitter_by_column_values(
            csv_path=TEST_FILE_ISO_ENCODING_CHARACTERS,
            target_columns=["Column1"],
            batch_size=2,
        )

        assert len(result) == 2
        assert result[0].input_total_count == 3

        pd.testing.assert_frame_equal(
            pd.read_csv(result[0].path),
            pd.read_csv(EXPECTED_CSV_ISO_BATCH_0_PATH.as_posix()),
        )
        pd.testing.assert_frame_equal(
            pd.read_csv(result[1].path),
            pd.read_csv(EXPECTED_CSV_ISO_BATCH_1_PATH.as_posix()),
        )

        for file_splitter in result:
            os.unlink(file_splitter.path)

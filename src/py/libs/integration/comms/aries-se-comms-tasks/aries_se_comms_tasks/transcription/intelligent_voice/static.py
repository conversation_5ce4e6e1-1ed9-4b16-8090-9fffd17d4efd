from aries_se_core_tasks.utilities.data_utils import BaseColumns
from se_elastic_schema.static.transcript import (
    LanguageCodeEnum,
    LanguageEnum,
    TranscriptSentimentEnum,
)
from se_enums.core import BaseStrEnum

# Create the Redis Debouncing key, this needs to be used with format() to
# insert the tenant
REDIS_KEY = "{tenant}_intelligent_voice_workflow"

IV_TRANSCRIPTION_WORKFLOW_NAME = "iv_transcription"
MARKER_FILE_PATH_COLUMN = "marker_file_path"

SENTIMENT_THRESHOLD = 25
IV_TRANSFORM_TRANSCRIPT_FLOW_NAME = "iv_transform_transcript"


class IntelligentVoiceTranscriptFields:
    ALL_TEXT = "allText"
    ALL_TEXT_PER_SPEAKER = "allTextPerSpeaker"
    DOC_ID = "docID"
    DURATION = "duration"
    ERROR_CODE = "error_code"
    ERROR_MESSAGE = "error_message"
    ITEM_ID = "item_id"
    LANGUAGE = "language"
    PROCESSING_END = "processing_end"
    PROCESSING_MODELS = "processingModels"
    PROCESSING_START = "processing_start"
    SPEAKER_SENTIMENT_LIST = "speakerSentimentList"
    SRTS = "srts"
    SUMMARY = "summary"
    TAGS = "tags"
    TRANSLATED_TCU = "translatedTCU"
    TRANSLATED_TEXT = "translatedText"
    TURN_CONSTRUCTION_UNITS = "turnConstructionUnits"


class IVGetTranscriptsTargetFields:
    CALL_DURATION = "callDuration"
    IS_EMPTY_TRANSCRIPT = "isEmptyTranscript"
    MODEL_IDS = "modelIds"
    MODEL_NAMES = "modelNames"
    PROCESSING_END_TIME = "processingEndTime"
    PROCESSING_START_TIME = "processingStartTime"
    TRANSCRIPT_ITEM_ID = "transcriptItemId"
    RECORDING_SOURCE_KEY = "recordingSourceKey"
    SOURCE_AUDIO_LANGUAGES = "sourceAudioLanguages"
    TARGET_LANGUAGE = "targetLanguage"
    TRANSCRIPT_SOURCE_KEY = "transcriptSourceKey"
    TRANSCRIPTION_STATUS = "transcriptionStatus"
    TRANSCRIPTION_STATUS_DESCRIPTION = "transcriptionStatusDescription"


class IntelligentVoiceSingleLanguageEnum(BaseStrEnum):
    """List of ISO codes here:
    https://www.loc.gov/standards/iso639-2/php/code_list.php.

    NOTE: The following fields are sorted by the values rather than
    the variables names.
    """

    AFRIKAANS = "af"
    ARABIC = "ar-001"
    CATALAN = "ca"
    DANISH = "da"
    GERMAN = "de-001"
    GREEK = "el"
    ENGLISH = "en-001"
    SPANISH = "es-001"
    FINNISH = "fi"
    FRENCH = "fr-001"
    HEBREW = "he"
    HINDI = "hi"
    CROATIAN = "hr"
    INDONESIAN = "id"
    ITALIAN = "it"
    JAPANESE = "ja"
    JAVANESE = "jv"
    KOREAN = "ko"
    NORWEGIAN = "nb"
    DUTCH = "nl-001"
    POLISH = "pl"
    PORTUGUESE = "pt-001"
    RUSSIAN = "ru"
    SWEDISH = "sv"
    THAI = "th"
    TAGALOG = "tl"
    TURKISH = "tr"
    CHINESE = "zh-001"

    # SECONDARY_LANGUAGES
    AMHARIC = "am"
    MAPUDUNGUN = "arn"
    MOROCCAN_ARABIC = "ary"
    ASSAMESE = "as"
    AZERBAIJANI = "az"
    BASHKIR = "ba"
    BELARUSSIAN = "be"
    BULGARIAN = "bg"
    BENGALI = "bn"
    TIBETAN = "bo"
    BRETON = "br"
    BOSNIAN = "bs"
    CORSICAN = "co"
    CZECH = "cs"
    WELSH = "cy"
    LOWER_SORBIAN = "dsb"
    DIVEHI = "dv"
    ESTONIAN = "et"
    BASQUE = "eu"
    PERSIAN = "fa"
    FILIPINO = "fil"
    FAROESE = "fo"
    FRISIAN = "fy"
    IRISH_GAELIC = "ga"
    SCOTTISH_GAELIC = "gd"
    GALICIAN = "gl"
    SWISS_GERMAN = "gsw"
    GUJARATI = "gu"
    HAUSA = "ha"
    SERBO_CROATIAN = "hrv"
    UPPER_SORBIAN = "hsb"
    HUNGARIAN = "hu"
    ARMENIAN = "hy"
    IGBO = "ig"
    YI = "ii"
    ICELANDIC = "is"
    INUKTITUT = "iu"
    GEORGIAN = "ka"
    KURDI = "kb"
    KAZAKH = "kk"
    GREENLANDIC = "kl"
    KHMER = "km"
    KANNADA = "kn"
    KONKANI = "kok"
    KYRGYZ = "ky"
    LUXEMBOURGISH = "lb"
    LAO = "lo"
    LITHUANIAN = "lt"
    LATVIAN = "lv"
    MAORI = "mi"
    MACEDONIAN = "mk"
    MALAYALAM = "ml"
    MONGOLIAN = "mn"
    MOHAWK = "moh"
    MARATHI = "mr"
    MALAYSIAN = "ms"
    MALTESE = "mt"
    BURMESE = "my"
    NORWEGIAN_NORSK = "nn"
    OCCITAN = "oc"
    ODIA = "or"
    PUNJABI = "pa"
    DARI_PERSIAN = "prs"
    PASHTO = "ps"
    K_ICHE = "quc"
    QUECHUA = "qu"
    ROMANSH = "rm"
    ROMANIAN = "ro"
    RWANDAN = "rw"
    SANSKRIT = "sa"
    YAKUT = "sah"
    NORTHERN_SAMI = "se"
    SINHALA = "si"
    SLOVAK = "sk"
    SLOVENIAN = "sl"
    SOUTHERN_SAMI = "sma"
    SAMI_LULE = "smj"
    SAMI_INARI = "smn"
    SAMI_SKOLT = "sms"
    ALBANIAN = "sq"
    SERBIAN = "sr"
    SESOTHO = "st"
    KISWAHILI = "sw"
    SYRIAC = "syc"
    TAMIL = "ta"
    TELUGU = "te"
    TAJIK = "tg"
    TURKMEN = "tk"
    TSWANA = "tn"
    TATAR = "tt"
    TAMAZIGHT = "tzm"
    UYGHUR = "ug"
    UKRAINIAN = "uk"
    URDU = "ur"
    UZBEK = "uz"
    VIETNAMESE = "vi"
    WOLOF = "wo"
    XHOSA = "xh"
    YORUBA = "yo"
    ZULU = "zu"

    @classmethod
    def primary_loop_languages(cls):
        """Create a list of primary languages that IV support.

        This will be looped through to get the language from the model
        names in get_lang_from_model_name_ignore_suffix.
        """
        return [
            cls.ARABIC,
            cls.AFRIKAANS,
            cls.CATALAN,
            cls.DANISH,
            cls.GERMAN,
            cls.GREEK,
            cls.ENGLISH,
            cls.SPANISH,
            cls.FINNISH,
            cls.FRENCH,
            cls.HEBREW,
            cls.HINDI,
            cls.CROATIAN,
            cls.INDONESIAN,
            cls.ITALIAN,
            cls.JAPANESE,
            cls.JAVANESE,
            cls.KOREAN,
            cls.NORWEGIAN,
            cls.DUTCH,
            cls.POLISH,
            cls.PORTUGUESE,
            cls.RUSSIAN,
            cls.SWEDISH,
            cls.THAI,
            cls.TAGALOG,
            cls.TURKISH,
            cls.CHINESE,
        ]

    @classmethod
    def secondary_loop_languages(cls):
        """Create a list of secondary languages that IV may support in the
        future.

        This will be looped through to get the language from the model
        names in get_lang_from_model_name_ignore_suffix ONLY if none of
        the PRIMARY_LANGUAGES are present. This is done ONLY for
        performance reasons.
        """
        return [
            cls.AMHARIC,
            cls.MAPUDUNGUN,
            cls.MOROCCAN_ARABIC,
            cls.ASSAMESE,
            cls.AZERBAIJANI,
            cls.BASHKIR,
            cls.BELARUSSIAN,
            cls.BULGARIAN,
            cls.BENGALI,
            cls.BENGALI,
            cls.TIBETAN,
            cls.BRETON,
            cls.BOSNIAN,
            cls.CORSICAN,
            cls.CZECH,
            cls.WELSH,
            cls.LOWER_SORBIAN,
            cls.DIVEHI,
            cls.ESTONIAN,
            cls.BASQUE,
            cls.PERSIAN,
            cls.FILIPINO,
            cls.FAROESE,
            cls.FRISIAN,
            cls.SCOTTISH_GAELIC,
            cls.IRISH_GAELIC,
            cls.GALICIAN,
            cls.SWISS_GERMAN,
            cls.GUJARATI,
            cls.HAUSA,
            cls.SERBO_CROATIAN,
            cls.UPPER_SORBIAN,
            cls.HUNGARIAN,
            cls.ARMENIAN,
            cls.IGBO,
            cls.YI,
            cls.ICELANDIC,
            cls.INUKTITUT,
            cls.GEORGIAN,
            cls.KURDI,
            cls.KAZAKH,
            cls.GREENLANDIC,
            cls.KHMER,
            cls.KANNADA,
            cls.KONKANI,
            cls.KYRGYZ,
            cls.LUXEMBOURGISH,
            cls.LAO,
            cls.LITHUANIAN,
            cls.LATVIAN,
            cls.MAORI,
            cls.MACEDONIAN,
            cls.MALAYALAM,
            cls.MONGOLIAN,
            cls.MOHAWK,
            cls.MARATHI,
            cls.MALAYSIAN,
            cls.MALTESE,
            cls.BURMESE,
            cls.NORWEGIAN_NORSK,
            cls.OCCITAN,
            cls.ODIA,
            cls.PUNJABI,
            cls.DARI_PERSIAN,
            cls.PASHTO,
            cls.K_ICHE,
            cls.QUECHUA,
            cls.ROMANSH,
            cls.ROMANIAN,
            cls.RWANDAN,
            cls.SANSKRIT,
            cls.YAKUT,
            cls.NORTHERN_SAMI,
            cls.SINHALA,
            cls.SLOVAK,
            cls.SLOVENIAN,
            cls.SOUTHERN_SAMI,
            cls.SAMI_LULE,
            cls.SAMI_INARI,
            cls.SAMI_SKOLT,
            cls.ALBANIAN,
            cls.SERBIAN,
            cls.SESOTHO,
            cls.KISWAHILI,
            cls.SYRIAC,
            cls.TAMIL,
            cls.TELUGU,
            cls.TAJIK,
            cls.TURKMEN,
            cls.TSWANA,
            cls.TATAR,
            cls.TAMAZIGHT,
            cls.UYGHUR,
            cls.UKRAINIAN,
            cls.URDU,
            cls.UZBEK,
            cls.VIETNAMESE,
            cls.WOLOF,
            cls.XHOSA,
            cls.YORUBA,
            cls.ZULU,
        ]

    @staticmethod
    def get_lang_from_model_name(model_name: str):
        """This function gets the language from a model full name. If
        ignore_suffix is True, it will match 'fr' and 'fr-001'.

        E.g. IntelligentVoice_fr_16kHz_1024_general_V1_NASRv3.1": None
        IntelligentVoice_en-001_16kHz_128_general_V0_NASRv4: en-001
        IntelligentVoice_x-languageid_16kHz_3_en.ja.fr_V4.1_NASRv2: None
        """
        for lang in IntelligentVoiceSingleLanguageEnum.primary_loop_languages():
            if f"_{lang}_" in model_name:
                return lang
        # Only loop through secondary languages if no primary languages are found
        for lang in IntelligentVoiceSingleLanguageEnum.secondary_loop_languages():
            if f"_{lang}_" in model_name:
                return lang

    @staticmethod
    def get_lang_from_model_name_ignore_suffix(model_name: str):
        """This function gets the language from a model full name. If
        ignore_suffix is True, it will match 'fr' and 'fr-001'.

        E.g. IntelligentVoice_fr_16kHz_1024_general_V1_NASRv3.1": None
        IntelligentVoice_en-001_16kHz_128_general_V0_NASRv4: en-001
        IntelligentVoice_x-languageid_16kHz_3_en.ja.fr_V4.1_NASRv2: None
        """
        for lang in IntelligentVoiceSingleLanguageEnum.primary_loop_languages():
            if f"_{lang}_" in model_name or f"_{lang.split('-')[0]}_" in model_name:
                return lang
        # Only loop through secondary languages if no primary languages are found
        for lang in IntelligentVoiceSingleLanguageEnum.secondary_loop_languages():
            if f"_{lang}_" in model_name or f"_{lang.split('-')[0]}_" in model_name:
                return lang


INTELLIGENT_VOICE_LANGUAGE_TO_SCHEMA_LANGUAGE_MAPPING = {
    IntelligentVoiceSingleLanguageEnum.AFRIKAANS.value: LanguageCodeEnum.AFRIKAANS.value,
    IntelligentVoiceSingleLanguageEnum.ARABIC.value: LanguageCodeEnum.ARABIC.value,
    IntelligentVoiceSingleLanguageEnum.CATALAN.value: LanguageCodeEnum.CATALAN.value,
    IntelligentVoiceSingleLanguageEnum.DANISH.value: LanguageCodeEnum.DANISH.value,
    IntelligentVoiceSingleLanguageEnum.GERMAN.value: LanguageCodeEnum.GERMAN.value,
    IntelligentVoiceSingleLanguageEnum.GREEK.value: LanguageCodeEnum.GREEK.value,
    IntelligentVoiceSingleLanguageEnum.ENGLISH.value: LanguageCodeEnum.ENGLISH.value,
    IntelligentVoiceSingleLanguageEnum.SPANISH.value: LanguageCodeEnum.SPANISH.value,
    IntelligentVoiceSingleLanguageEnum.FINNISH.value: LanguageCodeEnum.FINNISH.value,
    IntelligentVoiceSingleLanguageEnum.FRENCH.value: LanguageCodeEnum.FRENCH.value,
    IntelligentVoiceSingleLanguageEnum.HEBREW.value: LanguageCodeEnum.HEBREW.value,
    IntelligentVoiceSingleLanguageEnum.HINDI.value: LanguageCodeEnum.HINDI.value,
    IntelligentVoiceSingleLanguageEnum.CROATIAN.value: LanguageCodeEnum.CROATIAN.value,
    IntelligentVoiceSingleLanguageEnum.INDONESIAN.value: LanguageCodeEnum.INDONESIAN.value,
    IntelligentVoiceSingleLanguageEnum.ITALIAN.value: LanguageCodeEnum.ITALIAN.value,
    IntelligentVoiceSingleLanguageEnum.JAPANESE.value: LanguageCodeEnum.JAPANESE.value,
    IntelligentVoiceSingleLanguageEnum.JAVANESE.value: LanguageCodeEnum.JAVANESE.value,
    IntelligentVoiceSingleLanguageEnum.KOREAN.value: LanguageCodeEnum.KOREAN.value,
    IntelligentVoiceSingleLanguageEnum.NORWEGIAN.value: LanguageCodeEnum.NORWEGIAN.value,
    IntelligentVoiceSingleLanguageEnum.DUTCH.value: LanguageCodeEnum.DUTCH.value,
    IntelligentVoiceSingleLanguageEnum.POLISH.value: LanguageCodeEnum.POLISH.value,
    IntelligentVoiceSingleLanguageEnum.PORTUGUESE.value: LanguageCodeEnum.PORTUGUESE.value,
    IntelligentVoiceSingleLanguageEnum.RUSSIAN.value: LanguageCodeEnum.RUSSIAN.value,
    IntelligentVoiceSingleLanguageEnum.SWEDISH.value: LanguageCodeEnum.SWEDISH.value,
    IntelligentVoiceSingleLanguageEnum.THAI.value: LanguageCodeEnum.THAI.value,
    IntelligentVoiceSingleLanguageEnum.TAGALOG.value: LanguageCodeEnum.TAGALOG.value,
    IntelligentVoiceSingleLanguageEnum.TURKISH.value: LanguageCodeEnum.TURKISH.value,
    IntelligentVoiceSingleLanguageEnum.CHINESE.value: LanguageCodeEnum.CHINESE.value,
    IntelligentVoiceSingleLanguageEnum.AMHARIC.value: LanguageCodeEnum.AMHARIC.value,
    IntelligentVoiceSingleLanguageEnum.MAPUDUNGUN.value: LanguageCodeEnum.MAPUDUNGUN.value,
    IntelligentVoiceSingleLanguageEnum.MOROCCAN_ARABIC.value: LanguageCodeEnum.ARABIC_MOROCCAN.value,  # noqa E501
    IntelligentVoiceSingleLanguageEnum.ASSAMESE.value: LanguageCodeEnum.ASSAMESE.value,
    IntelligentVoiceSingleLanguageEnum.AZERBAIJANI.value: LanguageCodeEnum.AZERBAIJANI.value,
    IntelligentVoiceSingleLanguageEnum.BASHKIR.value: LanguageCodeEnum.BASHKIR.value,
    IntelligentVoiceSingleLanguageEnum.BELARUSSIAN.value: LanguageCodeEnum.BELARUSSIAN.value,
    IntelligentVoiceSingleLanguageEnum.BULGARIAN.value: LanguageCodeEnum.BULGARIAN.value,
    IntelligentVoiceSingleLanguageEnum.BENGALI.value: LanguageCodeEnum.BENGALI.value,
    IntelligentVoiceSingleLanguageEnum.TIBETAN.value: LanguageCodeEnum.TIBETAN.value,
    IntelligentVoiceSingleLanguageEnum.BRETON.value: LanguageCodeEnum.BRETON.value,
    IntelligentVoiceSingleLanguageEnum.BOSNIAN.value: LanguageCodeEnum.BOSNIAN.value,
    IntelligentVoiceSingleLanguageEnum.CORSICAN.value: LanguageCodeEnum.CORSICAN.value,
    IntelligentVoiceSingleLanguageEnum.CZECH.value: LanguageCodeEnum.CZECH.value,
    IntelligentVoiceSingleLanguageEnum.WELSH.value: LanguageCodeEnum.WELSH.value,
    IntelligentVoiceSingleLanguageEnum.LOWER_SORBIAN.value: LanguageCodeEnum.LOWER_SORBIAN.value,
    IntelligentVoiceSingleLanguageEnum.DIVEHI.value: LanguageCodeEnum.DIVEHI.value,
    IntelligentVoiceSingleLanguageEnum.ESTONIAN.value: LanguageCodeEnum.ESTONIAN.value,
    IntelligentVoiceSingleLanguageEnum.BASQUE.value: LanguageCodeEnum.BASQUE.value,
    IntelligentVoiceSingleLanguageEnum.PERSIAN.value: LanguageCodeEnum.PERSIAN.value,
    IntelligentVoiceSingleLanguageEnum.FILIPINO.value: LanguageCodeEnum.TAGALOG_FILIPINO.value,
    IntelligentVoiceSingleLanguageEnum.FAROESE.value: LanguageCodeEnum.FAROESE.value,
    IntelligentVoiceSingleLanguageEnum.FRISIAN.value: LanguageCodeEnum.FRISIAN.value,
    IntelligentVoiceSingleLanguageEnum.IRISH_GAELIC.value: LanguageCodeEnum.GAELIC_IRISH.value,
    IntelligentVoiceSingleLanguageEnum.SCOTTISH_GAELIC.value: LanguageCodeEnum.GAELIC_SCOTTISH.value,  # noqa E501
    IntelligentVoiceSingleLanguageEnum.GALICIAN.value: LanguageCodeEnum.GALICIAN.value,
    IntelligentVoiceSingleLanguageEnum.SWISS_GERMAN.value: LanguageCodeEnum.SWISS_GERMAN.value,
    IntelligentVoiceSingleLanguageEnum.GUJARATI.value: LanguageCodeEnum.GUJARATI.value,
    IntelligentVoiceSingleLanguageEnum.HAUSA.value: LanguageCodeEnum.HAUSA.value,
    IntelligentVoiceSingleLanguageEnum.UPPER_SORBIAN.value: LanguageCodeEnum.UPPER_SORBIAN.value,
    IntelligentVoiceSingleLanguageEnum.HUNGARIAN.value: LanguageCodeEnum.HUNGARIAN.value,
    IntelligentVoiceSingleLanguageEnum.ARMENIAN.value: LanguageCodeEnum.ARMENIAN.value,
    IntelligentVoiceSingleLanguageEnum.IGBO.value: LanguageCodeEnum.IGBO.value,
    IntelligentVoiceSingleLanguageEnum.YI.value: LanguageCodeEnum.YI.value,
    IntelligentVoiceSingleLanguageEnum.ICELANDIC.value: LanguageCodeEnum.ICELANDIC.value,
    IntelligentVoiceSingleLanguageEnum.INUKTITUT.value: LanguageCodeEnum.INUKTITUT.value,
    IntelligentVoiceSingleLanguageEnum.GEORGIAN.value: LanguageCodeEnum.GEORGIAN.value,
    IntelligentVoiceSingleLanguageEnum.KURDI.value: LanguageCodeEnum.KURDI.value,
    IntelligentVoiceSingleLanguageEnum.KAZAKH.value: LanguageCodeEnum.KAZAKH.value,
    IntelligentVoiceSingleLanguageEnum.GREENLANDIC.value: LanguageCodeEnum.GREENLANDIC.value,
    IntelligentVoiceSingleLanguageEnum.KHMER.value: LanguageCodeEnum.KHMER.value,
    IntelligentVoiceSingleLanguageEnum.KANNADA.value: LanguageCodeEnum.KANNADA.value,
    IntelligentVoiceSingleLanguageEnum.KONKANI.value: LanguageCodeEnum.KONKANI.value,
    IntelligentVoiceSingleLanguageEnum.KYRGYZ.value: LanguageCodeEnum.KYRGYZ.value,
    IntelligentVoiceSingleLanguageEnum.LUXEMBOURGISH.value: LanguageCodeEnum.LUXEMBOURGISH.value,
    IntelligentVoiceSingleLanguageEnum.LAO.value: LanguageCodeEnum.LAO.value,
    IntelligentVoiceSingleLanguageEnum.LITHUANIAN.value: LanguageCodeEnum.LITHUANIAN.value,
    IntelligentVoiceSingleLanguageEnum.LATVIAN.value: LanguageCodeEnum.LATVIAN.value,
    IntelligentVoiceSingleLanguageEnum.MAORI.value: LanguageCodeEnum.MAORI.value,
    IntelligentVoiceSingleLanguageEnum.MACEDONIAN.value: LanguageCodeEnum.MACEDONIAN.value,
    IntelligentVoiceSingleLanguageEnum.MALAYALAM.value: LanguageCodeEnum.MALAYALAM.value,
    IntelligentVoiceSingleLanguageEnum.MONGOLIAN.value: LanguageCodeEnum.MONGOLIAN.value,
    IntelligentVoiceSingleLanguageEnum.MOHAWK.value: LanguageCodeEnum.MOHAWK.value,
    IntelligentVoiceSingleLanguageEnum.MARATHI.value: LanguageCodeEnum.MARATHI.value,
    IntelligentVoiceSingleLanguageEnum.MALAYSIAN.value: LanguageCodeEnum.MALAYSIAN.value,
    IntelligentVoiceSingleLanguageEnum.MALTESE.value: LanguageCodeEnum.MALTESE.value,
    IntelligentVoiceSingleLanguageEnum.BURMESE.value: LanguageCodeEnum.BURMESE.value,
    IntelligentVoiceSingleLanguageEnum.NORWEGIAN_NORSK.value: LanguageCodeEnum.NORWEGIAN_NYNORSK.value,  # noqa E501
    IntelligentVoiceSingleLanguageEnum.OCCITAN.value: LanguageCodeEnum.OCCITAN.value,
    IntelligentVoiceSingleLanguageEnum.ODIA.value: LanguageCodeEnum.ODIA.value,
    IntelligentVoiceSingleLanguageEnum.PUNJABI.value: LanguageCodeEnum.PUNJABI.value,
    IntelligentVoiceSingleLanguageEnum.DARI_PERSIAN.value: LanguageCodeEnum.DARI_PERSIAN.value,
    IntelligentVoiceSingleLanguageEnum.PASHTO.value: LanguageCodeEnum.PASHTO.value,
    IntelligentVoiceSingleLanguageEnum.K_ICHE.value: LanguageCodeEnum.K_ICHE.value,
    IntelligentVoiceSingleLanguageEnum.QUECHUA.value: LanguageCodeEnum.QUECHUA.value,
    IntelligentVoiceSingleLanguageEnum.ROMANSH.value: LanguageCodeEnum.ROMANSH.value,
    IntelligentVoiceSingleLanguageEnum.ROMANIAN.value: LanguageCodeEnum.ROMANIAN.value,
    IntelligentVoiceSingleLanguageEnum.RWANDAN.value: LanguageCodeEnum.RWANDAN.value,
    IntelligentVoiceSingleLanguageEnum.SANSKRIT.value: LanguageCodeEnum.SANSKRIT.value,
    IntelligentVoiceSingleLanguageEnum.YAKUT.value: LanguageCodeEnum.YAKUT.value,
    IntelligentVoiceSingleLanguageEnum.NORTHERN_SAMI.value: LanguageCodeEnum.NORTHERN_SAMI.value,
    IntelligentVoiceSingleLanguageEnum.SINHALA.value: LanguageCodeEnum.SINHALA.value,
    IntelligentVoiceSingleLanguageEnum.SLOVAK.value: LanguageCodeEnum.SLOVAK.value,
    IntelligentVoiceSingleLanguageEnum.SLOVENIAN.value: LanguageCodeEnum.SLOVENIAN.value,
    IntelligentVoiceSingleLanguageEnum.SAMI_LULE.value: LanguageCodeEnum.SAMI_LULE.value,
    IntelligentVoiceSingleLanguageEnum.SAMI_INARI.value: LanguageCodeEnum.SAMI_INARI.value,
    IntelligentVoiceSingleLanguageEnum.SAMI_SKOLT.value: LanguageCodeEnum.SAMI_SKOLT.value,
    IntelligentVoiceSingleLanguageEnum.SOUTHERN_SAMI.value: LanguageCodeEnum.SOUTHERN_SAMI.value,
    IntelligentVoiceSingleLanguageEnum.ALBANIAN.value: LanguageCodeEnum.ALBANIAN.value,
    IntelligentVoiceSingleLanguageEnum.SERBIAN.value: LanguageCodeEnum.SERBIAN.value,
    IntelligentVoiceSingleLanguageEnum.SERBO_CROATIAN.value: LanguageCodeEnum.SERBO_CROARIAN.value,
    IntelligentVoiceSingleLanguageEnum.SESOTHO.value: LanguageCodeEnum.SESOTHO.value,
    IntelligentVoiceSingleLanguageEnum.KISWAHILI.value: LanguageCodeEnum.KISWAHILI.value,
    IntelligentVoiceSingleLanguageEnum.SYRIAC.value: LanguageCodeEnum.SYRIAC.value,
    IntelligentVoiceSingleLanguageEnum.TAMIL.value: LanguageCodeEnum.TAMIL.value,
    IntelligentVoiceSingleLanguageEnum.TELUGU.value: LanguageCodeEnum.TELUGU.value,
    IntelligentVoiceSingleLanguageEnum.TAJIK.value: LanguageCodeEnum.TAJIK.value,
    IntelligentVoiceSingleLanguageEnum.TURKMEN.value: LanguageCodeEnum.TURKMEN.value,
    IntelligentVoiceSingleLanguageEnum.TSWANA.value: LanguageCodeEnum.TSWANA.value,
    IntelligentVoiceSingleLanguageEnum.TATAR.value: LanguageCodeEnum.TATAR.value,
    IntelligentVoiceSingleLanguageEnum.TAMAZIGHT.value: LanguageCodeEnum.TAMAZIGHT.value,
    IntelligentVoiceSingleLanguageEnum.UYGHUR.value: LanguageCodeEnum.UYGHUR.value,
    IntelligentVoiceSingleLanguageEnum.UKRAINIAN.value: LanguageCodeEnum.UKRAINIAN.value,
    IntelligentVoiceSingleLanguageEnum.URDU.value: LanguageCodeEnum.URDU.value,
    IntelligentVoiceSingleLanguageEnum.UZBEK.value: LanguageCodeEnum.UZBEK.value,
    IntelligentVoiceSingleLanguageEnum.VIETNAMESE.value: LanguageCodeEnum.VIETNAMESE.value,
    IntelligentVoiceSingleLanguageEnum.WOLOF.value: LanguageCodeEnum.WOLOF.value,
    IntelligentVoiceSingleLanguageEnum.XHOSA.value: LanguageCodeEnum.XHOSA.value,
    IntelligentVoiceSingleLanguageEnum.YORUBA.value: LanguageCodeEnum.YORUBA.value,
    IntelligentVoiceSingleLanguageEnum.ZULU.value: LanguageCodeEnum.ZULU.value,
}


class IVSourceColumns(BaseColumns):
    ALL_TEXT = IntelligentVoiceTranscriptFields.ALL_TEXT
    ALL_TEXT_PER_SPEAKER = IntelligentVoiceTranscriptFields.ALL_TEXT_PER_SPEAKER
    CONFIDENCE = "confidence"
    DOC_ID = IntelligentVoiceTranscriptFields.DOC_ID
    DURATION = IntelligentVoiceTranscriptFields.DURATION
    ID = "id"
    IMPORT_ID = "importId"
    MODEL_IDS = IVGetTranscriptsTargetFields.MODEL_IDS
    MODEL_NAMES = IVGetTranscriptsTargetFields.MODEL_NAMES
    PROCESSING_END_TIME = IVGetTranscriptsTargetFields.PROCESSING_END_TIME
    PROCESSING_START_TIME = IVGetTranscriptsTargetFields.PROCESSING_START_TIME
    SOURCE_AUDIO_LANGUAGES = IVGetTranscriptsTargetFields.SOURCE_AUDIO_LANGUAGES
    SRTS = IntelligentVoiceTranscriptFields.SRTS
    SUMMARY = IntelligentVoiceTranscriptFields.SUMMARY
    TAGS = IntelligentVoiceTranscriptFields.TAGS
    TARGET_LANGUAGE = IVGetTranscriptsTargetFields.TARGET_LANGUAGE
    TURN_CONSTRUCTION_UNITS = IntelligentVoiceTranscriptFields.TURN_CONSTRUCTION_UNITS
    TRANSCRIPT_ITEM_ID = IVGetTranscriptsTargetFields.TRANSCRIPT_ITEM_ID
    TRANSCRIPTION_STATUS = IVGetTranscriptsTargetFields.TRANSCRIPTION_STATUS
    TRANSCRIPTION_STATUS_DESCRIPTION = IVGetTranscriptsTargetFields.TRANSCRIPTION_STATUS_DESCRIPTION
    TRANSLATED_TEXT = IntelligentVoiceTranscriptFields.TRANSLATED_TEXT

    # IV Direct-specific fields: When IV FTP transcripts to us, they send us some
    # additional fields (which we otherwise get from the import_item API when we
    # call the API endpoints ourselves).
    LANGUAGE_MODELS_IV_DIRECT = "language_models"
    PROCESSING_END_TIME_IV_DIRECT = "processing_end"
    PROCESSING_START_TIME_IV_DIRECT = "processing_start"


class IVSourceSRT:
    LANGUAGE = "language"
    LENGTH = "length"
    SCORE = "score"
    SPEAKER = "speaker"
    TIMESTAMP = "timestamp"
    WORD = "word"


class IVSourceTCU:
    AGGREGATED_SENTIMENT = "aggregatedSentiment"
    END_TIME = "endTime"
    ID = "id"
    SENTIMENT_NEGATIVE_PERCENTAGE = "sentimentNegativePercentage"
    SENTIMENT_NEUTRAL_PERCENTAGE = "sentimentNeutralPercentage"
    SENTIMENT_POSITIVE_PERCENTAGE = "sentimentPositivePercentage"
    START_TIME = "startTime"
    TEXT = "text"
    TRANSLATED_TCU = "translatedTCU"


class IVSourceSRTSpeaker:
    ID = "id"
    NAME = "name"
    NO = "no"


class IVSourceTags:
    OFFSET = "offset"
    POSITION = "position"
    TAG = "tag"
    TIMESTAMP = "timestamp"


class TempCols:
    TCU_DURATION = "__TCU_DURATION__"
    TCU_TOTAL_DURATION = "__TCU_TOTAL_DURATION__"
    TRANSCRIPTION_END_MINUS_START = "__TRANSCRIPTION_END_MINUS_START"
    WEIGHTED_DURATION = "__WEIGHTED_DURATION__"
    WEIGHTED_NEGATIVE_SENTIMENT = "__WEIGHTED_NEGATIVE_SENTIMENT__"
    WEIGHTED_NEUTRAL_SENTIMENT = "__WEIGHTED_NEUTRAL_SENTIMENT__"
    WEIGHTED_POSITIVE_SENTIMENT = "__WEIGHTED_POSITIVE_SENTIMENT__"


sentiment_threshold_mapping = {
    IVSourceTCU.SENTIMENT_POSITIVE_PERCENTAGE: TranscriptSentimentEnum.POSITIVE.value,
    IVSourceTCU.SENTIMENT_NEGATIVE_PERCENTAGE: TranscriptSentimentEnum.NEGATIVE.value,
    IVSourceTCU.SENTIMENT_NEUTRAL_PERCENTAGE: TranscriptSentimentEnum.NEUTRAL.value,
}

IV_TRANSFORM_TRANSCRIPT_SOURCE_SCHEMA = {
    "&id": "string",
    "&hash": "string",
    "&model": "string",
    IVGetTranscriptsTargetFields.RECORDING_SOURCE_KEY: "string",
    IVGetTranscriptsTargetFields.TRANSCRIPT_SOURCE_KEY: "string",
    IVGetTranscriptsTargetFields.TRANSCRIPTION_STATUS: "string",
    IVGetTranscriptsTargetFields.TRANSCRIPTION_STATUS_DESCRIPTION: "string",
    IVGetTranscriptsTargetFields.PROCESSING_START_TIME: "string",
    IVGetTranscriptsTargetFields.PROCESSING_END_TIME: "string",
    IVGetTranscriptsTargetFields.TRANSCRIPT_ITEM_ID: "string",
    IVGetTranscriptsTargetFields.MODEL_IDS: "string",
    IVGetTranscriptsTargetFields.MODEL_NAMES: "string",
    IVGetTranscriptsTargetFields.SOURCE_AUDIO_LANGUAGES: "string",
    IVGetTranscriptsTargetFields.TARGET_LANGUAGE: "string",
}

IV_MODEL_LANG_MAPPING = {
    LanguageCodeEnum.AFRIKAANS.value: LanguageEnum.AF.value,
    LanguageCodeEnum.ARABIC.value: LanguageEnum.AR.value,
    LanguageCodeEnum.CATALAN.value: LanguageEnum.CA.value,
    LanguageCodeEnum.DANISH.value: LanguageEnum.DA.value,
    LanguageCodeEnum.GERMAN.value: LanguageEnum.DE.value,
    LanguageCodeEnum.GREEK.value: LanguageEnum.EL.value,
    LanguageCodeEnum.ENGLISH.value: LanguageEnum.EN.value,
    LanguageCodeEnum.SPANISH.value: LanguageEnum.ES.value,
    LanguageCodeEnum.FINNISH.value: LanguageEnum.FI.value,
    LanguageCodeEnum.FRENCH.value: LanguageEnum.FR.value,
    LanguageCodeEnum.HEBREW.value: LanguageEnum.HE.value,
    LanguageCodeEnum.HINDI.value: LanguageEnum.HI.value,
    LanguageCodeEnum.CROATIAN.value: LanguageEnum.HR.value,
    LanguageCodeEnum.INDONESIAN.value: LanguageEnum.ID.value,
    LanguageCodeEnum.ITALIAN.value: LanguageEnum.IT.value,
    LanguageCodeEnum.JAPANESE.value: LanguageEnum.JA.value,
    LanguageCodeEnum.KOREAN.value: LanguageEnum.KO.value,
    LanguageCodeEnum.NORWEGIAN.value: LanguageEnum.NO.value,
    LanguageCodeEnum.DUTCH.value: LanguageEnum.NL.value,
    LanguageCodeEnum.POLISH.value: LanguageEnum.PL.value,
    LanguageCodeEnum.PORTUGUESE.value: LanguageEnum.PT.value,
    LanguageCodeEnum.RUSSIAN.value: LanguageEnum.RU.value,
    LanguageCodeEnum.SWEDISH.value: LanguageEnum.SV.value,
    LanguageCodeEnum.THAI.value: LanguageEnum.TH.value,
    LanguageCodeEnum.TAGALOG.value: LanguageEnum.TL.value,
    LanguageCodeEnum.TURKISH.value: LanguageEnum.TR.value,
    LanguageCodeEnum.CHINESE.value: LanguageEnum.ZH.value,
    LanguageCodeEnum.AMHARIC.value: LanguageEnum.AM.value,
    LanguageCodeEnum.MAPUDUNGUN.value: LanguageEnum.ARN.value,
    LanguageCodeEnum.ARABIC_MOROCCAN.value: LanguageEnum.ARY.value,
    LanguageCodeEnum.ASSAMESE.value: LanguageEnum.AS.value,
    LanguageCodeEnum.AZERBAIJANI.value: LanguageEnum.AZ.value,
    LanguageCodeEnum.BASHKIR.value: LanguageEnum.BA.value,
    LanguageCodeEnum.BELARUSSIAN.value: LanguageEnum.BE.value,
    LanguageCodeEnum.BULGARIAN.value: LanguageEnum.BG.value,
    LanguageCodeEnum.BENGALI.value: LanguageEnum.BN.value,
    LanguageCodeEnum.TIBETAN.value: LanguageEnum.BO.value,
    LanguageCodeEnum.BRETON.value: LanguageEnum.BR.value,
    LanguageCodeEnum.BOSNIAN.value: LanguageEnum.BS.value,
    LanguageCodeEnum.CORSICAN.value: LanguageEnum.CO.value,
    LanguageCodeEnum.CZECH.value: LanguageEnum.CS.value,
    LanguageCodeEnum.WELSH.value: LanguageEnum.CY.value,
    LanguageCodeEnum.LOWER_SORBIAN.value: LanguageEnum.DSB.value,
    LanguageCodeEnum.DIVEHI.value: LanguageEnum.DV.value,
    LanguageCodeEnum.ESTONIAN.value: LanguageEnum.ET.value,
    LanguageCodeEnum.BASQUE.value: LanguageEnum.EU.value,
    LanguageCodeEnum.PERSIAN.value: LanguageEnum.FA.value,
    LanguageCodeEnum.TAGALOG_FILIPINO.value: LanguageEnum.FIL.value,
    LanguageCodeEnum.FAROESE.value: LanguageEnum.FO.value,
    LanguageCodeEnum.FRISIAN.value: LanguageEnum.FY.value,
    LanguageCodeEnum.GAELIC_IRISH.value: LanguageEnum.GA.value,
    LanguageCodeEnum.GAELIC_SCOTTISH.value: LanguageEnum.GD.value,
    LanguageCodeEnum.GALICIAN.value: LanguageEnum.GL.value,
    LanguageCodeEnum.SWISS_GERMAN.value: LanguageEnum.GSW.value,
    LanguageCodeEnum.GUJARATI.value: LanguageEnum.GU.value,
    LanguageCodeEnum.HAUSA.value: LanguageEnum.HA.value,
    LanguageCodeEnum.UPPER_SORBIAN.value: LanguageEnum.HSB.value,
    LanguageCodeEnum.HUNGARIAN.value: LanguageEnum.HU.value,
    LanguageCodeEnum.ARMENIAN.value: LanguageEnum.HY.value,
    LanguageCodeEnum.IGBO.value: LanguageEnum.IG.value,
    LanguageCodeEnum.YI.value: LanguageEnum.II.value,
    LanguageCodeEnum.ICELANDIC.value: LanguageEnum.IS.value,
    LanguageCodeEnum.INUKTITUT.value: LanguageEnum.IU.value,
    LanguageCodeEnum.GEORGIAN.value: LanguageEnum.KA.value,
    LanguageCodeEnum.KURDI.value: LanguageEnum.KB.value,
    LanguageCodeEnum.KAZAKH.value: LanguageEnum.KK.value,
    LanguageCodeEnum.GREENLANDIC.value: LanguageEnum.KL.value,
    LanguageCodeEnum.KHMER.value: LanguageEnum.KM.value,
    LanguageCodeEnum.KANNADA.value: LanguageEnum.KN.value,
    LanguageCodeEnum.KONKANI.value: LanguageEnum.KOK.value,
    LanguageCodeEnum.KYRGYZ.value: LanguageEnum.KY.value,
    LanguageCodeEnum.LUXEMBOURGISH.value: LanguageEnum.LB.value,
    LanguageCodeEnum.LAO.value: LanguageEnum.LO.value,
    LanguageCodeEnum.LITHUANIAN.value: LanguageEnum.LT.value,
    LanguageCodeEnum.LATVIAN.value: LanguageEnum.LV.value,
    LanguageCodeEnum.MAORI.value: LanguageEnum.MI.value,
    LanguageCodeEnum.MACEDONIAN.value: LanguageEnum.MK.value,
    LanguageCodeEnum.MALAYALAM.value: LanguageEnum.ML.value,
    LanguageCodeEnum.MONGOLIAN.value: LanguageEnum.MN.value,
    LanguageCodeEnum.MOHAWK.value: LanguageEnum.MOH.value,
    LanguageCodeEnum.MARATHI.value: LanguageEnum.MR.value,
    LanguageCodeEnum.MALAYSIAN.value: LanguageEnum.MS.value,
    LanguageCodeEnum.MALTESE.value: LanguageEnum.MT.value,
    LanguageCodeEnum.BURMESE.value: LanguageEnum.MY.value,
    LanguageCodeEnum.NORWEGIAN_NYNORSK.value: LanguageEnum.NN.value,
    LanguageCodeEnum.OCCITAN.value: LanguageEnum.OC.value,
    LanguageCodeEnum.ODIA.value: LanguageEnum.OR.value,
    LanguageCodeEnum.PUNJABI.value: LanguageEnum.PA.value,
    LanguageCodeEnum.DARI_PERSIAN.value: LanguageEnum.PRS.value,
    LanguageCodeEnum.PASHTO.value: LanguageEnum.PS.value,
    LanguageCodeEnum.K_ICHE.value: LanguageEnum.QUC.value,
    LanguageCodeEnum.QUECHUA.value: LanguageEnum.QU.value,
    LanguageCodeEnum.ROMANSH.value: LanguageEnum.RM.value,
    LanguageCodeEnum.ROMANIAN.value: LanguageEnum.RO.value,
    LanguageCodeEnum.RWANDAN.value: LanguageEnum.RW.value,
    LanguageCodeEnum.SANSKRIT.value: LanguageEnum.SA.value,
    LanguageCodeEnum.YAKUT.value: LanguageEnum.SAH.value,
    LanguageCodeEnum.NORTHERN_SAMI.value: LanguageEnum.SE.value,
    LanguageCodeEnum.SINHALA.value: LanguageEnum.SI.value,
    LanguageCodeEnum.SLOVAK.value: LanguageEnum.SK.value,
    LanguageCodeEnum.SLOVENIAN.value: LanguageEnum.SL.value,
    LanguageCodeEnum.SAMI_LULE.value: LanguageEnum.SMJ.value,
    LanguageCodeEnum.SAMI_INARI.value: LanguageEnum.SMN.value,
    LanguageCodeEnum.SAMI_SKOLT.value: LanguageEnum.SMS.value,
    LanguageCodeEnum.SOUTHERN_SAMI.value: LanguageEnum.SMA.value,
    LanguageCodeEnum.ALBANIAN.value: LanguageEnum.SQ.value,
    LanguageCodeEnum.SERBIAN.value: LanguageEnum.SR.value,
    LanguageCodeEnum.SERBO_CROARIAN.value: LanguageEnum.HRV.value,
    LanguageCodeEnum.SESOTHO.value: LanguageEnum.ST.value,
    LanguageCodeEnum.KISWAHILI.value: LanguageEnum.SW.value,
    LanguageCodeEnum.SYRIAC.value: LanguageEnum.SYC.value,
    LanguageCodeEnum.TAMIL.value: LanguageEnum.TA.value,
    LanguageCodeEnum.TELUGU.value: LanguageEnum.TE.value,
    LanguageCodeEnum.TAJIK.value: LanguageEnum.TG.value,
    LanguageCodeEnum.TURKMEN.value: LanguageEnum.TK.value,
    LanguageCodeEnum.TSWANA.value: LanguageEnum.TN.value,
    LanguageCodeEnum.TATAR.value: LanguageEnum.TT.value,
    LanguageCodeEnum.TAMAZIGHT.value: LanguageEnum.TZM.value,
    LanguageCodeEnum.UYGHUR.value: LanguageEnum.UG.value,
    LanguageCodeEnum.UKRAINIAN.value: LanguageEnum.UK.value,
    LanguageCodeEnum.URDU.value: LanguageEnum.UR.value,
    LanguageCodeEnum.UZBEK.value: LanguageEnum.UZ.value,
    LanguageCodeEnum.VIETNAMESE.value: LanguageEnum.VI.value,
    LanguageCodeEnum.WOLOF.value: LanguageEnum.WO.value,
    LanguageCodeEnum.XHOSA.value: LanguageEnum.XH.value,
    LanguageCodeEnum.YORUBA.value: LanguageEnum.YO.value,
    LanguageCodeEnum.ZULU.value: LanguageEnum.ZU.value,
}

from dataclasses import dataclass
from enum import Enum
from typing import Dict, List

DEEPGRAM_FEED_FLOW_NAME = "deepgram_feed"


class DeepgramModelEnum(str, Enum):
    GENERAL = "general"
    GENERAL_ENHANCED = "general-enhanced"
    NOVA_2_GENERAL = "nova-2-general"
    STEELEYE = "steeleye"


class DeepGramLanguageEnum(str, Enum):
    CHINESE = "zh"
    CHINESE_CHINA = "zh-CN"
    CHINESE_TAIWAN = "zh-TW"
    DANISH = "da"
    DUTCH = "nl"
    ENGLISH = "en"
    ENGLISH_AUSTRALIA = "en-AU"
    ENGLISH_INDIA = "en-IN"
    ENGLISH_NEW_ZEALAND = "en-NZ"
    ENGLISH_UNITED_KINGDOM = "en-GB"
    ENGLISH_UNITED_STATES = "en-US"
    FLEMISH = "nl"
    FRENCH = "fr"
    FRENCH_CANADA = "fr-CA"
    GERMAN = "de"
    HINDI = "hi"
    HINDI_ROMAN_SCRIPT = "hi-Latn"
    INDONESIAN = "id"
    ITALIAN = "it"
    JAPANESE = "ja"
    KOREAN = "ko"
    NORWEGIAN = "no"
    POLISH = "pl"
    PORTUGUESE = "pt"
    PORTUGUESE_BRAZIL = "pt-BR"
    PORTUGUESE_PORTUGAL = "pt-PT"
    RUSSIAN = "ru"
    SPANISH = "es"
    SPANISH_LATIN_AMERICA = "es-419"
    SWEDISH = "sv"
    TAMIL = "ta"
    TURKISH = "tr"
    UKRAINIAN = "uk"


class StaticTargetFields:
    CALL_DURATION_SECONDS = "callDurationSeconds"
    DATE_TIME_TRANSCRIPTION_START = "dateTime.transcriptionStart"
    JOB_ID = "jobId"
    MODEL_ID = "model.id"
    RECORDING_SOURCE_KEY = "recordingSourceKey"
    SOURCE_AUDIO_LANGUAGE = "sourceAudioLanguage"
    TARGET_LANGUAGE = "targetLanguage"
    TRANSCRIPTION_CONFIDENCE = "transcriptionConfidence"
    TRANSCRIPT_SOURCE_KEY = "transcriptSourceKey"
    TRANSCRIPTION_STATUS_DEEPGRAM = "transcriptionStatus"
    TRANSCRIPTION_STATUS_DESCRIPTION = "transcriptionStatus.description"


@dataclass
class DeepGramCoreData:
    call_has_been_transcribed_before: bool
    call_has_monitored_participants: bool
    call_has_valid_duration: bool
    usage_file_path: str
    audio_file_location: str
    audio_file_duration_in_seconds: int


class TranscriptionParams:
    AUDIO_FILE_LOCATION_ATTRIBUTE = "audio_file_location_attribute"
    CALL_DURATION_ATTRIBUTE = "call_duration_attribute"
    DETECT_LANGUAGE = "detect_language"
    MODEL = "model"
    SOURCE_AUDIO_LANGUAGE = "source_audio_language"
    SOURCE_AUDIO_LANGUAGE_ATTRIBUTE = "source_audio_language_attribute"
    SOURCE_FILE_ATTRIBUTE = "source_file_attribute"
    TARGET_TRANSLATION_LANGUAGE = "target_translation_language"
    TIER = "tier"


class TempColumns:
    HASH_FILE_PATH = "__hash_file_path__"
    RECORDING_FILE_PATH = "__recording_file_path__"
    CALL_DURATION_IN_SECONDS = "__call_duration_in_seconds__"


@dataclass
class TranscriptionConfiguration:
    configuration: List[Dict]
    description: str


class DeepgramApiDefaultParams:
    CALL_DURATION_ATTRIBUTE = "call_duration_in_seconds"
    AUDIO_FILE_LOCATION_ATTRIBUTE = "voiceFile.fileInfo.location.key"
    LANGUAGE = DeepGramLanguageEnum.ENGLISH_UNITED_STATES
    TIER = "enhanced"


DEEPGRAM_FEED_SOURCE_SCHEMA = {
    "&id": "string",
    "&model": "string",
    "&hash": "string",
    "recordingSourceKey": "string",
    "transcriptSourceKey": "string",
    "transcriptionStatus": "string",
    "transcriptionStatus.description": "string",
    "dateTime.transcriptionStart": "string",
    "model.id": "string",
    "sourceAudioLanguage": "string",
    "targetLanguage": "string",
}

DEEPGRAM_ENDPOINT = {
    "dev": "https://deepgram.nonprod-eu-ie-1.steeleye.co/v1/",
    "shared": "https://deepgram.nonprod-eu-ie-1.steeleye.co/v1/",
}

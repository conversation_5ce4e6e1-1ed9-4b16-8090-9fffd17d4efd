import logging
from enum import auto
from se_enums.core import BaseStrEnum

logger_ = logging.getLogger(__name__)


class TranscriptionAppMetricsEnum(BaseStrEnum):
    """App metrics for Transcription."""

    ALREADY_TRANSCRIBED_COUNT = auto()
    COMPLETED_SUCCESSFULLY_COUNT = auto()
    EMPTY_TRANSCRIPT_COUNT = auto()
    INVALID_TRANSCRIPTION_RESPONSE_COUNT = auto()
    NOT_MONITORED_PARTICIPANT_COUNT = auto()
    NOT_COMPLETED_EXCEEDING_CONTRACTUAL_LIMIT_COUNT = auto()
    RETRANSCRIBED_COUNT = auto()
    TOO_MANY_CONCURRENT_ATTEMPTS_COUNT = auto()
    UNEXPECTED_TRANSCRIPTION_FAILURE_COUNT = auto()
    DURATION_OUT_OF_RANGE_COUNT = auto()


class IVTranscriptionMetricsEnum(BaseStrEnum):
    """App metrics for IV Transcription."""

    JOBS_SUBMITTED_COUNT = auto()
    TRANSCRIPTION_PENDING_COUNT = auto()
    TRANSCRIPTS_OBTAINED_COUNT = auto()


class TranslationAppMetricsEnum(BaseStrEnum):
    """App metrics for Transcription."""

    TRANSLATION_FAILED_COUNT = auto()
    TRANSLATION_SKIPPED_AS_ALREADY_TRANSLATED_COUNT = auto()

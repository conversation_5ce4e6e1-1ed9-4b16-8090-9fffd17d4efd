# mypy: disable-error-code="arg-type, return"
import itertools
import json
import logging
import pandas as pd
import pydantic
import re

# from aries_se_comms_tasks.transcription.intelligent_voice.utils import (
#     fetch_segments,
#     fetch_sentiment,
# )
from aries_se_comms_tasks.transcription.static import TranscriptionFields, TranscriptModelFields
from aries_se_comms_tasks.transcription.transcript_copilot.static import (
    AIResponseClassifier,
    AIResponseCols,
    StaticText,
    TempCols,
)
from aries_se_comms_tasks.voice.static import CallColumns, SemanticModelFields
from aries_se_core_tasks.core.exception import TaskException
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.core.integration_task import IntegrationTask
from copilot_utils.client import SeOpenApiClient
from copilot_utils.config import OPENAI_CONFIG
from indict import Indict
from omegaconf import DictConfig, ListConfig
from se_elastic_schema.components.communication.common_analytics import CommonAnalytics
from se_elastic_schema.components.communication.semantic import Semantic
from se_elastic_schema.models import Transcript
from typing import List, Optional

from integration_audio_comms_tasks.transcription.transcription_copilot.static import TranscriptionTempColumns
logger = logging.getLogger(__name__)


class SkipIfSourceFrameEmpty(TaskException):
    pass


class TranscriptCopilot(IntegrationTask):
    """This task take the transformed transcript data as input to call OpenAI GPT-4 model to
    derive the following columns and returns them as a dataframe
    1.SemanticModelFields.SUMMARY
    2.SemanticModelFields.TOPICS
    3.SemanticModelFields.ENTITIES
    4.SemanticModelFields.QUESTIONS
    5.SemanticModelFields.SENTIMENT
    6.TranscriptModelFields.ANALYTICS
    7.SemanticModelFields.COMPLEXITY

    The prompts for fetching these fields are present in the path
    transcription_copilot/config.yml
    """

    # The following columns are required for downstream processing.
    REQUIRED_COLUMNS = [
        CallColumns.ANALYTICS_COPILOT_ANALYTICS_METADATA,
        CallColumns.ANALYTICS_COPILOT_ANALYTICS_RISKS,
        TranscriptModelFields.ANALYTICS,
    ] + SemanticModelFields.list()

    def _run(
        self,
        source_frame: pd.DataFrame,
        config: DictConfig | ListConfig,
        tenant: str,
        workflow: str,
        tenant_configuration: Optional[dict] = None,
        **kwargs,
    ) -> pd.DataFrame:
        if source_frame.empty:
            raise SkipIfSourceFrameEmpty("Source DataFrame is empty, skipping")

        # Update input count
        self.update_app_metrics(field=GenericAppMetricsEnum.INPUT_COUNT, value=len(source_frame))

        self.target_df = pd.DataFrame(index=source_frame.index)
        try:
            return self.process(
                source_frame=source_frame,
                config=config,
                tenant=tenant,
                workflow=workflow,
                tenant_configuration=tenant_configuration,
            )
        except Exception as e:
            logger.error(f"Transcript Copilot failed due to error {e}")
            for col in self.REQUIRED_COLUMNS:
                if col not in self.target_df.columns:
                    self.target_df[col] = pd.NA

            # Dropping not required columns to be extra safe
            self.target_df = self.target_df.drop(
                columns=list(set(self.target_df.columns) - set(self.REQUIRED_COLUMNS)),
                errors="ignore",
            )
            return self.target_df

    def process(
        self,
        source_frame: pd.DataFrame,
        config: DictConfig | ListConfig,
        tenant: str,
        workflow: str,
        tenant_configuration: Optional[dict] = None,
    ) -> pd.DataFrame:
        # Check for SKIP_COPILOT flag
        skip_copilot_col = TranscriptionTempColumns.SKIP_COPILOT
        has_skip_flag = skip_copilot_col in source_frame.columns

        if has_skip_flag:
            skip_mask = source_frame[skip_copilot_col] == True
            if skip_mask.any():
                logger.info(f"Skipping AI processing for {skip_mask.sum()} records with SKIP_COPILOT=True, processing {(~skip_mask).sum()} records")
        else:
            skip_mask = pd.Series(False, index=source_frame.index)

        self.target_df = pd.DataFrame(index=source_frame.index)

        # Only process records that don't have SKIP_COPILOT=True
        records_to_process = source_frame.loc[~skip_mask]

        if not records_to_process.empty:
            df = pd.DataFrame(index=records_to_process.index)
            # create transcript data to be used as prompt
            df.loc[:, TempCols.TRANSCRIPT_DATA] = self._transcript_data(data=records_to_process.copy())
            df.loc[:, TempCols.TRANSCRIPT_CHAR_SIZE] = df.loc[:, TempCols.TRANSCRIPT_DATA].str.len()

            # transcript_copilot processing
            ai_completion_df = self.copilot_processing(
                df=df,
                logger=logger,
                config=config,
                tenant=tenant,
                workflow=workflow,
                tenant_configuration=tenant_configuration,
            )
            ai_completion_df = self._add_mandatory_cols(ai_completion_df=ai_completion_df)
        else:
            # No records to process, create empty AI completion dataframe
            ai_completion_df = pd.DataFrame(index=pd.Index([]))

        # populate target columns from AI response
        # summary
        self.target_df.loc[:, SemanticModelFields.SUMMARY.value] = ai_completion_df.loc[
            :, AIResponseCols.TRANSCRIPTION_SUMMARY
        ]

        # topics
        self.target_df.loc[:, SemanticModelFields.TOPICS.value] = ai_completion_df.loc[
            :, AIResponseCols.TOPICS
        ]

        # entities
        self.target_df.loc[:, SemanticModelFields.ENTITIES.value] = ai_completion_df.loc[
            :, AIResponseCols.ENTITIES
        ]

        # questions
        self.target_df.loc[:, SemanticModelFields.QUESTIONS.value] = ai_completion_df.loc[
            :, AIResponseCols.QUESTIONS
        ]

        # TODO Experimenting sentiments and segments form IV
        # Will remove below commented code based on the test results
        # # sentiments
        # sentiments_not_null_mask = ai_completion_df[AIResponseCols.SENTIMENTS].notnull()
        # self.target_df.loc[
        #     sentiments_not_null_mask, TranscriptModelFields.SENTIMENT
        # ] = np.vectorize(fetch_sentiment)(
        #     tcus=ai_completion_df.loc[sentiments_not_null_mask, AIResponseCols.SENTIMENTS]
        # )
        #
        # # segments
        # self.target_df.loc[sentiments_not_null_mask, TranscriptModelFields.SEGMENTS] =
        # np.vectorize(
        #     fetch_segments
        # )(
        #     tcus=ai_completion_df.loc[sentiments_not_null_mask, AIResponseCols.SENTIMENTS],
        #     target_language=None,
        # )

        # analytics.classifier
        classifier_not_null_mask = ai_completion_df[AIResponseCols.CLASSIFIER].notnull()
        self.target_df.loc[classifier_not_null_mask, TranscriptModelFields.ANALYTICS] = (
            ai_completion_df.loc[classifier_not_null_mask, AIResponseCols.CLASSIFIER].apply(
                lambda x: self._fetch_analytics(classifier=x)
            )
        )

        # complexity.score
        self.target_df.loc[:, SemanticModelFields.COMPLEXITY_SCORE.value] = self._complexity_score(
            df=ai_completion_df
        )

        # complexity.method
        self.target_df.loc[:, SemanticModelFields.COMPLEXITY_METHOD.value] = (
            self._complexity_method(df=ai_completion_df)
        )

        # post-processing: add columns which will be used in downstream tasks

        # analytics.copilotAnalytics.risks
        self.target_df.loc[:, CallColumns.ANALYTICS_COPILOT_ANALYTICS_RISKS] = ai_completion_df.loc[
            :, AIResponseCols.RISKS
        ]
        # replace empty lists with pd.NA
        non_empty_risks = (
            self.target_df[CallColumns.ANALYTICS_COPILOT_ANALYTICS_RISKS].str.len() > 0
        )
        self.target_df[CallColumns.ANALYTICS_COPILOT_ANALYTICS_RISKS] = self.target_df[
            CallColumns.ANALYTICS_COPILOT_ANALYTICS_RISKS
        ].where(non_empty_risks, pd.NA)

        # analytics.copilotAnalytics.metadata
        self.target_df.loc[:, CallColumns.ANALYTICS_COPILOT_ANALYTICS_METADATA] = pd.NA
        risks_not_null_mask = self.target_df.loc[
            :, CallColumns.ANALYTICS_COPILOT_ANALYTICS_RISKS
        ].notnull()
        if risks_not_null_mask.any():
            metadata = self._copilot_analytics_metadata()
            self.target_df.loc[
                risks_not_null_mask, CallColumns.ANALYTICS_COPILOT_ANALYTICS_METADATA
            ] = pd.Series(
                [metadata] * len(self.target_df[risks_not_null_mask]),
                index=self.target_df[risks_not_null_mask].index,
            )

        # validate copilot result against schema
        self.target_df.loc[:, TempCols.VALIDATION] = self.target_df.apply(
            lambda x: self._validate_against_schema(data=x), axis=1
        )
        invalid_mask = ~self.target_df[TempCols.VALIDATION]
        if invalid_mask.any():
            for col in self.REQUIRED_COLUMNS:
                self.target_df.loc[invalid_mask, col] = pd.NA

        # drop temp cols and return target_df
        temp_cols = [TempCols.VALIDATION, TempCols.TRANSCRIPT_DATA, TempCols.TRANSCRIPT_CHAR_SIZE]
        temp_cols_mask = self.target_df.columns.isin(temp_cols)
        return self.target_df.loc[:, ~temp_cols_mask]

    @staticmethod
    def _transcript_data(data: pd.DataFrame) -> pd.Series:
        """Extract transcript data from tokens in the format like below e.g.:

        # Speaker 1: Hello, how are you today? start_time(seconds):1.2
        end_time(seconds): 2.4 # Speaker 2: I am fine, thanks.
        start_time(seconds): 2.4 end_time(seconds):3.6 # Speaker 1: So
        lets start then. start_time(seconds):3.6 end_time(seconds): 4.0
        # .... and so on
        """
        temp_df = pd.DataFrame()
        # explode the input tokens column which is a List of dicts(tokens) into a dataframe
        exploded_df = data[TranscriptModelFields.TOKENS].explode()
        # extract required columns from exploded_df
        temp_df[TempCols.TOKEN] = exploded_df.str.get(TranscriptModelFields.TOKEN)
        temp_df[TempCols.START] = exploded_df.str.get(TranscriptionFields.TIME).str.get(
            TranscriptionFields.START
        )
        temp_df[TempCols.END] = exploded_df.str.get(TranscriptionFields.TIME).str.get(
            TranscriptionFields.END
        )
        temp_df[TempCols.SPEAKER_ID] = exploded_df.str.get(TranscriptModelFields.SPEAKER_ID)

        # add a change marker to mark the change of speaker
        # we group by index because rows with same index are tokens of a single transcript
        temp_df[TempCols.CHANGE_MARKER] = (
            temp_df[TempCols.SPEAKER_ID]
            != temp_df.groupby(temp_df.index)[TempCols.SPEAKER_ID].shift()
        ).astype(int)

        # Use the above change marker and then assign an incremental value for each speaker change
        # under each group(i.e. each transcript)
        temp_df[TempCols.GROUP] = (
            temp_df.groupby(temp_df.index)[TempCols.CHANGE_MARKER].cumsum().astype(int)
        )

        # Concatenate all tokens under each group
        temp_df[TempCols.TOKENS_AGG] = temp_df.groupby([temp_df.index, TempCols.GROUP])[
            TempCols.TOKEN
        ].transform(lambda x: " ".join(x))
        # Aggregate Speaker Id
        temp_df[TempCols.SPEAKER_AGG] = temp_df.groupby([temp_df.index, TempCols.GROUP])[
            TempCols.SPEAKER_ID
        ].transform("first")
        # Aggregate Start time
        temp_df[TempCols.START_AGG] = temp_df.groupby([temp_df.index, TempCols.GROUP])[
            TempCols.START
        ].transform("first")
        # Aggregate End time
        temp_df[TempCols.END_AGG] = temp_df.groupby([temp_df.index, TempCols.GROUP])[
            TempCols.END
        ].transform("last")
        temp_df[TempCols.INDEX_COL] = temp_df.index
        temp_df = temp_df.drop_duplicates(subset=[TempCols.INDEX_COL, TempCols.GROUP])

        # Create a summary transcript data which looks like below:
        # Speaker 1: Hello, how are you today? start_time(seconds):1.2 end_time(seconds): 2.4
        # Speaker 2: I am fine, thanks. start_time(seconds): 2.4 end_time(seconds):3.6
        # Speaker 1: So lets start then. start_time(seconds):3.6 end_time(seconds): 4.0
        # .... and so on
        temp_df[TempCols.TRANSCRIPT_DATA] = (
            StaticText.SPEAKER
            + temp_df[TempCols.SPEAKER_AGG].astype(str)
            + StaticText.COLON
            + temp_df[TempCols.TOKENS_AGG].astype(str)
            + StaticText.START_TIME
            + temp_df[TempCols.START_AGG].astype(str)
            + StaticText.END_TIME
            + temp_df[TempCols.END_AGG].astype(str)
        )
        data.loc[:, TempCols.TRANSCRIPT_DATA] = (
            temp_df.groupby(TempCols.INDEX_COL)[TempCols.TRANSCRIPT_DATA]
            .agg(lambda x: " -- ".join(map(str, x)))
            .reset_index()[TempCols.TRANSCRIPT_DATA]
        )
        return data.loc[:, TempCols.TRANSCRIPT_DATA]

    def copilot_processing(
        self,
        df: pd.DataFrame,
        logger: logging.Logger,
        tenant: str,
        workflow: str,
        config: DictConfig | ListConfig,
        tenant_configuration: Optional[dict] = None,
    ) -> pd.DataFrame:
        """
        :param df: pd.DataFrame
        :param logger: logging.Logger
        :return: pd.Dataframe
        Call OpenAI API using the system and user prompt in controlled batches to avoid hitting the
        ratelimit
        """
        logger.info("Starting Copilot Processing")
        system_prompt = config.prompt.system_prompt

        client = SeOpenApiClient()
        system_message = client.construct_prompt_with_role(
            role="system", prompt_content=system_prompt
        )
        user_message = client.construct_prompt_with_role(
            role="user", prompt_content=df[TempCols.TRANSCRIPT_DATA].values.tolist()
        )
        prompt_list = list(itertools.product([system_message], user_message))
        responses_list = client.call_open_ai(
            prompt_list=prompt_list,
            tenant=tenant,
            workflow=workflow,
            tenant_configuration=tenant_configuration,
        )
        parsed_response = client.parse_content_from_response(responses_list)

        ai_result_json = self._extract_json_response(aggregated_ai_result=parsed_response)
        result_series = pd.Series(ai_result_json)

        # Update LLM metrics
        for metric_name, metric_value in client.metrics.dict().items():
            self.update_app_metrics(field=metric_name, value=metric_value)

        return result_series.apply(pd.Series)

    @staticmethod
    def _extract_json_response(aggregated_ai_result: list[str]) -> List[dict]:
        json_list = []
        for completion in aggregated_ai_result:
            try:
                # New responses from GPT 4o is formatted as a json markdown
                completion = re.sub(r"(```json|```)", "", completion)
                json_list.append(json.loads(completion))
            except Exception:
                json_list.append(None)
        return json_list

    @staticmethod
    def _add_mandatory_cols(ai_completion_df: pd.DataFrame):
        """
        :param ai_completion_df: pd.DataFrame
        :return: ai_completion_df: pd.DataFrame
        Adds mandatory columns to source dataframe and returns it
        """
        required_cols = AIResponseCols().all()
        for col in required_cols:
            if col not in ai_completion_df.columns:
                ai_completion_df.loc[:, col] = pd.NA
        return ai_completion_df

    @staticmethod
    def _fetch_analytics(classifier: dict) -> Optional[dict]:
        """
        :param classifier: dict
        :return: dict
        """
        try:
            confidence_score = classifier.get(AIResponseClassifier.CONFIDENCE_SCORE, 0)
            classification = classifier.get(AIResponseClassifier.CLASSIFICATION, 0)
            analytics = {
                TranscriptModelFields.CLASSIFIER: {
                    TranscriptModelFields.PREDICTIONS: [
                        {
                            TranscriptModelFields.CLASS: classification,
                            TranscriptModelFields.VALUE: confidence_score,
                        }
                    ],
                    TranscriptModelFields.PREDICTED_CLASSES: (
                        [classification] if confidence_score > 0.5 else None
                    ),
                }
            }
            return analytics
        except Exception as e:
            logger.error(f"Error in _fetch_analytics:{e}")
            logger.error(f"For data:{classifier}")

    @staticmethod
    def _complexity_score(df: pd.DataFrame) -> Optional[float]:
        """
        :param df: pd.DataFrame
        :return: float
        """
        try:
            return df[AIResponseCols.COMPLEXITY].str.get(AIResponseCols.SCORE)  # type: ignore[return-value]
        except Exception as e:
            logger.error(f"Error in _complexity_score:{e}")
            logger.error(f"For data:{df[AIResponseCols.COMPLEXITY]}")

    @staticmethod
    def _complexity_method(df: pd.DataFrame) -> pd.Series:
        """
        :param df: pd.DataFrame
        :return: result: pd.Series
        """
        result: pd.Series = pd.Series(data=pd.NA, index=df.index)
        try:
            result.loc[df[AIResponseCols.COMPLEXITY].str.get(AIResponseCols.SCORE) > 0.5] = "TTR"  # type: ignore[operator]
            return result
        except Exception as e:
            logger.error(f"Error in _complexity_method:{e}")
            logger.error(f"For data:{df[AIResponseCols.COMPLEXITY]}")

    def _copilot_analytics_metadata(self):
        return {
            "source": "AZURE OPENAI",
            "model": OPENAI_CONFIG.OPENAI_API_MODEL,
            "version": OPENAI_CONFIG.OPENAI_API_VERSION,
            "promptProvider": "SteelEye",
            "promptRevisionID": 0,
        }

    def _validate_against_schema(self, data: pd.Series) -> bool:
        # TODO This is a quick hack fix for Santander POC.
        # we will revisit it when we have time later
        try:
            data_dict = data.to_dict()
            data_dict = {
                k: v
                for k, v in data_dict.items()
                if k
                not in (
                    [
                        CallColumns.ANALYTICS_COPILOT_ANALYTICS_RISKS,
                        CallColumns.ANALYTICS_COPILOT_ANALYTICS_METADATA,
                    ]
                    + SemanticModelFields.list()
                )
            }
            parsed_transcript_record = Indict(obj=data_dict).unflatten().remove_empty().to_dict()
            parsed_transcript_record["id"] = "sample-id"
            parsed_transcript_record["recordingSourceKey"] = "sample-key"
            parsed_transcript_record["isTranscribedBySteelEye"] = True
            parsed_transcript_record["model"] = {"vendor": "DeepGram"}
            parsed_transcript_record["transcriptSourceKey"] = "sample-key"
            parsed_transcript_record["text"] = "sample-text"
            Transcript(**parsed_transcript_record)

            data_dict = data.to_dict()
            call_dict = {
                k.replace("analytics.", ""): v
                for k, v in data_dict.items()
                if k
                in [
                    CallColumns.ANALYTICS_COPILOT_ANALYTICS_RISKS,
                    CallColumns.ANALYTICS_COPILOT_ANALYTICS_METADATA,
                ]
            }
            parsed_call_record = Indict(obj=call_dict).unflatten().remove_empty().to_dict()
            CommonAnalytics(**parsed_call_record)

            data_dict = data.to_dict()
            transcript_link_dict = {
                k.replace(SemanticModelFields.field_prefix() + ".", ""): v
                for k, v in data_dict.items()
                if k in SemanticModelFields.list()
            }
            parsed_call_semantic_record = (
                Indict(obj=transcript_link_dict).unflatten().remove_empty().to_dict()
            )
            Semantic(**parsed_call_semantic_record)
            return True
        except pydantic.ValidationError as e:
            logger.error(f"Copilot result failed schema validation. So skipping the record:{e}")
            return False


def run_transcript_copilot(
    source_frame: pd.DataFrame,
    config: DictConfig | ListConfig,
    tenant: str,
    workflow: str,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    tenant_configuration: Optional[dict] = None,
    **kwargs,
) -> pd.DataFrame:
    task = TranscriptCopilot(app_metrics_path=app_metrics_path, audit_path=audit_path)
    return task.run(  # type: ignore[no-any-return]
        source_frame=source_frame,
        config=config,
        tenant=tenant,
        workflow=workflow,
        tenant_configuration=tenant_configuration,
        **kwargs,
    )

from aries_se_core_tasks.utilities.data_utils import BaseColumns
from typing import Dict


class SourceColumns(BaseColumns):
    ATTACHMENTS_ATTACHMENT_DESCRIPTION: str = "log.record.attachments.attachment.description"
    ATTACHMENTS_ATTACHMENT_FILENAME: str = "log.record.attachments.attachment.filename"
    ATTACHMENTS_ATTACHMENT_FILETYPE: str = "log.record.attachments.attachment.filetype"
    CONVERSATION_INFO_CONTENT: str = "log.record.conversation_info.content"
    CONVERSATION_INFO_CONTENT_TYPE: str = "log.record.conversation_info.content_type"
    CONVERSATION_INFO_ENCODING: str = "log.record.conversation_info.encoding"
    CONVERSATION_INFO_SUBJECT: str = "log.record.conversation_info.subject"
    CREATION_DATE_DATE_STRING: str = "log.record.creation_date.date_string"
    CREATION_DATE_UTC: str = "log.record.creation_date.utc"
    ID: str = "log.record.@id"
    INTERACTION_ID: str = "log.record.interaction_id"
    ON_BEHALF_OF_EMAIL: str = "log.record.onbehalfof.email"
    ON_BEHALF_OF_USER_ID: str = "log.record.onbehalfof.recipient.user_id"
    RECIPIENTS: str = "log.record.recipients"
    RECIPIENTS_RECIPIENT: str = "log.record.recipients.recipient"
    RECIPIENTS_RECIPIENT_COMPANY: str = "log.record.recipients.recipient.company"
    RECIPIENTS_RECIPIENT_TYPE: str = "log.record.recipients.recipient.@type"
    RECIPIENTS_RECIPIENT_USER_ID: str = "log.record.recipients.recipient.user_id"
    RECIPIENTS_RECIPIENT_USER_INFO_EMAIL: str = "log.record.recipients.recipient.user_info_email"
    RECIPIENTS_RECIPIENT_USER_INFO_FIRST_NAME: str = (
        "log.record.recipients.recipient.user_info_first_name"
    )
    RECIPIENTS_RECIPIENT_USER_INFO_LAST_NAME: str = (
        "log.record.recipients.recipient.user_info_last_name"
    )
    RECIPIENTS_RECIPIENT_USER_INFO_PHONE: str = "log.record.recipients.recipient.user_info_phone"
    SENDER_CHANNEL: str = "log.record.sender.channel"
    SENDER_COMPANY: str = "log.record.sender.company"
    SENDER_USER_ID: str = "log.record.sender.user_id"
    SENDER_USER_INFO_EMAIL: str = "log.record.sender.user_info_email"
    SENDER_USER_INFO_FIRST_NAME: str = "log.record.sender.user_info_first_name"
    SENDER_USER_INFO_LAST_NAME: str = "log.record.sender.user_info_last_name"
    SENDER_USER_INFO_PHONE: str = "log.record.sender.user_info_phone"
    TYPE: str = "log.record.@type"


class TempColumns:
    FROM_ID: str = "__recipient_from_id__"
    ON_BEHALF_OF_ID: str = "__on_behalf_of_id__"
    RECIPIENT_TO_IDS: str = "__recipient_to_ids__"
    TIMESTAMP: str = "__timestamp__"
    TO_USER_IDS: str = "__to_user_ids__"


class ChatEventValues:
    JOINED: str = "Joined"
    LEFT: str = "Left"
    STARTED_CONVO: str = "Client has started conversation."

    ALL = [JOINED, LEFT, STARTED_CONVO]


class Channels:
    IMESSAGE: str = "iMessage"
    LEAP: str = "Leap"
    TELEGRAM: str = "Telegram"
    WECHAT: str = "WeChat"
    WHATSAPP: str = "WhatsApp"
    WECOM: str = "WeCom"

    VALUE_MAP: Dict[str, str] = {
        "leap": LEAP,
        "whatsapp": WHATSAPP,
        "whatsapparchived": WHATSAPP,
        "whatsappNative": WHATSAPP,
        "wechat-miniapp": WECHAT,
        "telegramNative": TELEGRAM,
        "telegrampassive": TELEGRAM,
        "imessagearchived": IMESSAGE,
        "wechatwecom": WECHAT,
        "wechatwecompassive": WECHAT,
        "wecom": WECOM,
    }


class RecipientsKeys:
    PHONE: str = "user_info_phone"
    EMAIL: str = "user_info_email"
    ID: str = "user_id"


CHAT_EVENTS_QUERY: str = f"`{SourceColumns.CONVERSATION_INFO_CONTENT}`.isin({ChatEventValues.ALL})"

MESSAGE_MAPPINGS_NAME: str = "leapxpert_message_mappings"

CHAT_EVENT_MAPPINGS_NAME: str = "leapxpert_chat_event_mappings"

SOURCE_SCHEMA: Dict[str, str] = {x: "string" for x in SourceColumns.all()}

UTC_DATETIME_FORMAT = "%Y-%m-%dT%H:%M:%S.%fZ"

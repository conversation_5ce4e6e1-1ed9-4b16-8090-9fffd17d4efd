from aries_se_comms_tasks.feeds.meeting.zoom import zoom_meeting_transcription_mappings
from aries_se_comms_tasks.feeds.voice.zoom_phone import zoom_phone_transcript_mappings
from aries_se_comms_tasks.transcription.deepgram import deepgram_feed_transcript_mappings
from aries_se_comms_tasks.transcription.fano import fano_transcript_mappings
from aries_se_comms_tasks.transcription.intelligent_voice import (
    intelligence_voice_feed_transcript_mappings,
)
from se_core_tasks.abstractions.transformations.transform_map import TransformMap

deepgram_feed_transform_map = TransformMap(
    default=deepgram_feed_transcript_mappings.DeepgramFeedTranscriptMappings,
    map={},
)
fano_feed_transform_map = TransformMap(
    default=fano_transcript_mappings.FanoTranscriptMappings,
    map={},
)

intelligent_voice_feed_transform_map = TransformMap(
    default=intelligence_voice_feed_transcript_mappings.IntelligenceVoiceFeedTranscriptMappings,
    map={},
)

zoom_meetings_transcript_transform_map = TransformMap(
    default=zoom_meeting_transcription_mappings.ZoomMeetingTranscriptMappings,
    map={},
)

zoom_phone_voice_transcript_transform_map = TransformMap(
    default=zoom_phone_transcript_mappings.ZoomTranscriptMappings, map={}
)

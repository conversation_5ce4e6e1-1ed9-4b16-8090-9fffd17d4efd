# type: ignore
import copy
import fsspec
import json
import logging
import omegaconf
import os
import pandas as pd
import pytest
from copilot_utils.static import OpenAIResponse
from openai.types import CompletionUsage
from openai.types.chat import ChatCompletion, ChatCompletionMessage
from openai.types.chat.chat_completion import Choice
from pathlib import Path
from unittest import mock

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath("expected_result.pkl")
DEFAULT_OPENAI_VALUES = {
    "OPENAI_API_BASE": "",
    "OPENAI_API_KEY": "",
    "OPENAI_API_MODEL": "gpt-4",
    "OPENAI_API_VERSION": "2023-03-15-preview",
}


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    with fsspec.open(TEST_FILES_DIR.joinpath("sample_input.ndjson")) as f:
        df = pd.read_json(f, lines=True)
    return df


@pytest.fixture()
def expected_output_frame() -> pd.DataFrame:
    return pd.read_pickle(EXPECTED_FILE_PATH)


@pytest.fixture()
def log():
    logger = logging.getLogger("dummy")
    logger.setLevel(logging.INFO)
    return logger


@pytest.fixture()
def config():
    return omegaconf.DictConfig({"prompt": {"system_prompt": "sample-prompt"}})


@pytest.fixture()
def mock_gpt_response():
    with open(TEST_FILES_DIR.joinpath("mock_completion_1.json")) as f:
        content_1 = json.dumps(json.load(f))
    with open(TEST_FILES_DIR.joinpath("mock_completion_2.json")) as f:
        content_2 = json.dumps(json.load(f))

    openai_response_template_1 = ChatCompletion(
        id="abc",
        created=1727699717,
        model="sample_model",
        object="chat.completion",
        usage=CompletionUsage(
            total_tokens=100,
            # Leaving these as zero as they are not relevant to the tests
            prompt_tokens=0,
            completion_tokens=0,
        ),
        choices=[
            Choice(
                message=ChatCompletionMessage(content=content_1, role="assistant"),
                index=0,
                finish_reason="stop",
            )
        ],
    )
    openai_response_template_2 = copy.deepcopy(openai_response_template_1)
    openai_response_template_2.choices[0].message.content = content_2
    return [
        OpenAIResponse(index=1, response=openai_response_template_1),
        OpenAIResponse(index=2, response=openai_response_template_2),
    ]


class TestTranscriptCopilot:
    @mock.patch.dict(os.environ, DEFAULT_OPENAI_VALUES)
    def test_end_to_end_mappings(
        self, source_frame, expected_output_frame, log, mocker, mock_gpt_response, config
    ):
        from aries_se_comms_tasks.transcription.transcript_copilot.transcript_copilot import (
            run_transcript_copilot,
        )
        from copilot_utils.client import SeOpenApiClient

        mock_completion = mocker.patch.object(
            SeOpenApiClient,
            "call_open_ai",
        )
        mock_completion.return_value = mock_gpt_response
        result = run_transcript_copilot(
            source_frame=source_frame,
            config=config,
            skip_serializer=True,
            tenant="abc",
            workflow="xyz",
        )
        pd.testing.assert_frame_equal(result, expected_output_frame, check_dtype=False)

    @mock.patch.dict(os.environ, DEFAULT_OPENAI_VALUES)
    def test_end_to_end_mappings_when_no_response_from_gpt(
        self, source_frame, expected_output_frame, log, mocker, config
    ):
        from aries_se_comms_tasks.transcription.transcript_copilot.transcript_copilot import (
            run_transcript_copilot,
        )
        from copilot_utils.client import SeOpenApiClient

        mock_completion = mocker.patch.object(
            SeOpenApiClient,
            "call_open_ai",
        )
        mock_completion.side_effect = []
        result = run_transcript_copilot(
            source_frame=source_frame,
            config=config,
            skip_serializer=True,
            tenant="abc",
            workflow="xyz",
        )
        # All columns will be pd.NA when there is no response from GPT
        for col in expected_output_frame.columns:
            expected_output_frame[col] = pd.NA

        pd.testing.assert_frame_equal(
            result.sort_index(axis=1), expected_output_frame.sort_index(axis=1), check_dtype=False
        )

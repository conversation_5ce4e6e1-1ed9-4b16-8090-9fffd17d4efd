# type: ignore
import json
import os
import pandas as pd
import pytest
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.transcription.deepgram import deepgram_run_transcription
from aries_se_comms_tasks.transcription.deepgram.deepgram_retranscribe import (
    DeepgramRetranscribe,
    run_deepgram_retranscribe,
)
from aries_se_comms_tasks.transcription.deepgram.deepgram_run_transcription import (
    DeepgramRunTranscription,
)
from aries_se_comms_tasks.transcription.static import TRANSCRIPTION_USAGE_MAP
from freezegun import freeze_time
from integration_wrapper.static import IntegrationAriesTaskVariables
from pathlib import Path
from se_elastic_schema.static.communication import TranscriptionProviderEnum
from se_elastic_schema.static.transcript import LanguageEnum
from se_elasticsearch.repository import ResourceConfig
from se_elasticsearch.repository.static import MetaPrefix
from se_enums.cloud import CloudProviderEnum
from shutil import rmtree

PATH = Path(__file__).parent
SERIALIZER_TMP_DIR = PATH.joinpath("serializer_tmp_dir")

SERIALIZER_TMP_DIR.mkdir(parents=True, exist_ok=True)
os.environ[IntegrationAriesTaskVariables.SERIALIZER_TMP_DIR] = SERIALIZER_TMP_DIR.as_posix()


@pytest.fixture(scope="session", autouse=True)
def cleanup(request):
    def _end():
        rmtree(SERIALIZER_TMP_DIR)

    request.addfinalizer(_end)


@pytest.fixture()
def source_frame_mixed_confidence() -> pd.DataFrame:
    """
    Source frame with already-transcribed data
    :return:
    """
    return pd.DataFrame(
        [
            {
                "callDurationSeconds": "286.0",
                "&hash": "bf17d944f963d3ff0d8d5837b279f6c007b68811ba3749efc18d3d3df41f90e6",
                "&id": "5a1f5e00-73d4-40f1-9dbd-fcba11a04ee7",
                "&model": "Call",
                "should_be_transcribed": True,
                "should_be_transcribed_by_duration": True,
                "recordingSourceKey": "lake/ingress/landing/communications/voice/"
                "veritas/ms_teams/recordings/5a1f5e00/5a1f5e00-73d4-40f1-9dbd-fcba11a04ee7.wav",
                "transcriptSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/"
                "transcription/deepgram/deepgram_api/2023/07/12/test1/"
                "8484c8d1932fb9b274b6d156cfb43239.auto_language_detect.deepgram.json",
                "transcriptionStatus": "Transcription completed successfully",
                "transcriptionStatus.description": pd.NA,
                "dateTime.transcriptionStart": "2023-07-12T13:01:49.737Z",
                "model.id": "125125fb-e391-458e-a227-a60d6426f5d6:general-enhanced:2022-05-18.0:",
                "sourceAudioLanguage": LanguageEnum.EN_IE,
                "targetLanguage": pd.NA,
                "transcriptionConfidence": 0.99538535,
            },
            {
                "callDurationSeconds": "11.0",
                "&hash": "b8a1bce248a43dd9e1de7050e1305532e68aafa0f26bab8215f2d74da30850aa",
                "&id": "4e1f6100-7a31-4491-baf3-0dde2b65b82d",
                "&model": "Call",
                "should_be_transcribed": True,
                "should_be_transcribed_by_duration": True,
                "recordingSourceKey": "lake/ingress/landing/communications/voice/"
                "veritas/ms_teams/recordings/4e1f6100/4e1f6100-7a31-4491-baf3-0dde2b65b82d.wav",
                "transcriptSourceKey": "s3://test.dev.steeleye.co/aries/ingress/"
                "depository/transcription/deepgram/deepgram_api/2023/07/12/test1/"
                "f81d62798e672300120e3b9b14b855af.auto_language_detect.deepgram.json",
                "transcriptionStatus": "Transcription completed successfully",
                "transcriptionStatus.description": pd.NA,
                "dateTime.transcriptionStart": "2023-07-12T16:17:23.744Z",
                "model.id": "6ef45878-a6c3-4dcc-b0d7-652d280354f4:general-enhanced:"
                "2022-12-08.27689:",
                "sourceAudioLanguage": LanguageEnum.DE_DE,
                "targetLanguage": pd.NA,
                "transcriptionConfidence": 0.25072932,
            },
            {
                "callDurationSeconds": "1184.0",
                "&hash": "ba53828b8c7148fd69b2c21005acb4a3078f931294d6b550c7b96f4f5db08cb0",
                "&id": "4b1f6100-2130-4185-8ead-8ca5599b78a1",
                "&model": "Call",
                "should_be_transcribed": True,
                "should_be_transcribed_by_duration": True,
                "recordingSourceKey": "lake/ingress/landing/communications/voice/"
                "veritas/ms_teams/recordings/4b1f6100/4b1f6100-2130-4185-8ead-8ca5599b78a1.wav",
                "transcriptSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/"
                "transcription/deepgram/deepgram_api/2023/07/12/test1/"
                "ffd029d1bb9499554940f2fb7e25c000.auto_language_detect.deepgram.json",
                "transcriptionStatus": "Transcription completed successfully",
                "transcriptionStatus.description": pd.NA,
                "dateTime.transcriptionStart": "2023-07-12T16:18:23.957Z",
                "model.id": "6ef45878-a6c3-4dcc-b0d7-652d280354f4:general-enhanced:"
                "2022-12-08.27689:",
                "sourceAudioLanguage": LanguageEnum.FR_FR,
                "targetLanguage": pd.NA,
                "transcriptionConfidence": 0.36171833,
            },
            {
                "callDurationSeconds": "1184.0",
                "&hash": "c775cae4c7103a457ff751132db7bec007e5d5972017b0fd818086b8e07147b7",
                "&id": "451f6100-79d6-4bb5-99fe-d8122dcd7595",
                "&model": "Call",
                "should_be_transcribed": True,
                "should_be_transcribed_by_duration": True,
                "recordingSourceKey": "lake/ingress/landing/communications/voice/"
                "veritas/ms_teams/recordings/451f6100/451f6100-79d6-4bb5-99fe-d8122dcd7595.wav",
                "transcriptSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/"
                "transcription/deepgram/deepgram_api/2023/07/12/test1/"
                "929f8c4ffb6f3ba0f197869828df0f1b.auto_language_detect.deepgram.json",
                "transcriptionStatus": "Transcription completed successfully",
                "transcriptionStatus.description": pd.NA,
                "dateTime.transcriptionStart": "2023-07-12T16:19:27.210Z",
                "model.id": "6ef45878-a6c3-4dcc-b0d7-652d280354f4:general-enhanced:"
                "2022-12-08.27689:",
                "sourceAudioLanguage": LanguageEnum.PT_PT,
                "targetLanguage": pd.NA,
                "transcriptionConfidence": 0.8764816,
            },
        ]
    )


@pytest.fixture()
def tenant_configuration() -> addict.Dict:
    return addict.Dict({"contractualLimits": {"volume": {"voice": {"transcription": 1000}}}})


@pytest.fixture()
def expected_result_df_mixed_confidence() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "callDurationSeconds": "286.0",
                "&hash": "bf17d944f963d3ff0d8d5837b279f6c007b68811ba3749efc18d3d3df41f90e6",
                "&id": "5a1f5e00-73d4-40f1-9dbd-fcba11a04ee7",
                "&model": "Call",
                "should_be_transcribed": True,
                "recordingSourceKey": "lake/ingress/landing/communications/voice/"
                "veritas/ms_teams/recordings/5a1f5e00/5a1f5e00-73d4-40f1-9dbd-fcba11a04ee7.wav",
                "transcriptSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/"
                "transcription/deepgram/deepgram_api/2023/07/12/test1/"
                "8484c8d1932fb9b274b6d156cfb43239.auto_language_detect.deepgram.json",
                "transcriptionStatus": "Transcription completed successfully",
                "transcriptionStatus.description": pd.NA,
                "dateTime.transcriptionStart": "2023-07-12T13:01:49.737Z",
                "model.id": "125125fb-e391-458e-a227-a60d6426f5d6:general-enhanced:2022-05-18.0:",
                "sourceAudioLanguage": LanguageEnum.EN_IE,
                "targetLanguage": pd.NA,
                "transcriptionConfidence": 0.99538535,
                "should_be_transcribed_by_duration": True,
            },
            {
                "callDurationSeconds": "11.0",
                "&hash": "b8a1bce248a43dd9e1de7050e1305532e68aafa0f26bab8215f2d74da30850aa",
                "&id": "4e1f6100-7a31-4491-baf3-0dde2b65b82d",
                "&model": "Call",
                "should_be_transcribed": True,
                "recordingSourceKey": "lake/ingress/landing/communications/voice/"
                "veritas/ms_teams/recordings/4e1f6100/4e1f6100-7a31-4491-baf3-0dde2b65b82d.wav",
                "transcriptSourceKey": "s3://test.dev.steeleye.co/custom-transcript-upload-path/another-dir/file1_hash.en-US.deepgram.json",  # noqa: E501
                "transcriptionStatus": "Transcription completed successfully",
                "transcriptionStatus.description": pd.NA,
                "dateTime.transcriptionStart": "2023-07-13T06:59:38.911459Z",
                "model.id": "4899aa60-f723-4517-9815-2042acc12a82:general:2022-01-18.0:base",
                "sourceAudioLanguage": LanguageEnum.EN_US,
                "targetLanguage": pd.NA,
                "transcriptionConfidence": 0.9978787,
                "should_be_transcribed_by_duration": True,
            },
            {
                "callDurationSeconds": "1184.0",
                "&hash": "ba53828b8c7148fd69b2c21005acb4a3078f931294d6b550c7b96f4f5db08cb0",
                "&id": "4b1f6100-2130-4185-8ead-8ca5599b78a1",
                "&model": "Call",
                "should_be_transcribed": True,
                "recordingSourceKey": "lake/ingress/landing/communications/voice/veritas/"
                "ms_teams/recordings/4b1f6100/4b1f6100-2130-4185-8ead-8ca5599b78a1.wav",
                "transcriptSourceKey": "s3://test.dev.steeleye.co/custom-transcript-upload-path/another-dir/file2_hash.en-US.deepgram.json",  # noqa: E501
                "transcriptionStatus": "Transcription completed successfully",
                "transcriptionStatus.description": pd.NA,
                "dateTime.transcriptionStart": "2023-07-13T06:59:38.911459Z",
                "model.id": "4899aa60-f723-4517-9815-2042acc12a82:general:2022-01-18.0:base",
                "sourceAudioLanguage": LanguageEnum.EN_US,
                "targetLanguage": pd.NA,
                "transcriptionConfidence": 0.9978787,
                "should_be_transcribed_by_duration": True,
            },
            {
                "callDurationSeconds": "1184.0",
                "&hash": "c775cae4c7103a457ff751132db7bec007e5d5972017b0fd818086b8e07147b7",
                "&id": "451f6100-79d6-4bb5-99fe-d8122dcd7595",
                "&model": "Call",
                "should_be_transcribed": True,
                "recordingSourceKey": "lake/ingress/landing/communications/voice/veritas/"
                "ms_teams/recordings/451f6100/451f6100-79d6-4bb5-99fe-d8122dcd7595.wav",
                "transcriptSourceKey": "s3://test.dev.steeleye.co/aries/ingress/"
                "depository/transcription/deepgram/deepgram_api/2023/07/12/test1/"
                "929f8c4ffb6f3ba0f197869828df0f1b.auto_language_detect.deepgram.json",
                "transcriptionStatus": "Transcription completed successfully",
                "transcriptionStatus.description": pd.NA,
                "dateTime.transcriptionStart": "2023-07-12T16:19:27.210Z",
                "model.id": "6ef45878-a6c3-4dcc-b0d7-652d280354f4:general-enhanced:"
                "2022-12-08.27689:",
                "sourceAudioLanguage": LanguageEnum.PT_PT,
                "targetLanguage": pd.NA,
                "transcriptionConfidence": 0.8764816,
                "should_be_transcribed_by_duration": True,
            },
        ]
    )


@pytest.fixture()
def source_frame_nothing_to_transcribe() -> pd.DataFrame:
    """
    Source frame with already-transcribed data
    :return:
    """
    return pd.DataFrame(
        [
            {
                # Not retranscribed because it's in English already
                "callDurationSeconds": "286.0",
                "&hash": "bf17d944f963d3ff0d8d5837b279f6c007b68811ba3749efc18d3d3df41f90e6",
                "&id": "5a1f5e00-73d4-40f1-9dbd-fcba11a04ee7",
                "&model": "Call",
                "should_be_transcribed": True,
                "should_be_transcribed_by_duration": True,
                "recordingSourceKey": "lake/ingress/landing/communications/voice/veritas/"
                "ms_teams/recordings/5a1f5e00/5a1f5e00-73d4-40f1-9dbd-fcba11a04ee7.wav",
                "transcriptSourceKey": "s3://test.dev.steeleye.co/transcription/deepgram/"
                "deepgram_api/2023/07/12/test1/"
                "8484c8d1932fb9b274b6d156cfb43239.auto_language_detect.deepgram.json",
                "transcriptionStatus": "Transcription completed successfully",
                "transcriptionStatus.description": pd.NA,
                "dateTime.transcriptionStart": "2023-07-12T13:01:49.737Z",
                "model.id": "125125fb-e391-458e-a227-a60d6426f5d6:general-enhanced:2022-05-18.0:",
                "sourceAudioLanguage": LanguageEnum.EN_IE,
                "targetLanguage": pd.NA,
                "transcriptionConfidence": 0.39538535,
            },  # Not retranscribed because of high confidence
            {
                "callDurationSeconds": "1184.0",
                "&hash": "c775cae4c7103a457ff751132db7bec007e5d5972017b0fd818086b8e07147b7",
                "&id": "451f6100-79d6-4bb5-99fe-d8122dcd7595",
                "&model": "Call",
                "should_be_transcribed": True,
                "should_be_transcribed_by_duration": True,
                "recordingSourceKey": "lake/ingress/landing/communications/voice/veritas/"
                "ms_teams/recordings/451f6100/451f6100-79d6-4bb5-99fe-d8122dcd7595.wav",
                "transcriptSourceKey": "s3://test.dev.steeleye.co/transcription/deepgram/"
                "deepgram_api/2023/07/12/test1"
                "/929f8c4ffb6f3ba0f197869828df0f1b.auto_language_detect.deepgram.json",
                "transcriptionStatus": "Transcription completed successfully",
                "transcriptionStatus.description": pd.NA,
                "dateTime.transcriptionStart": "2023-07-12T16:19:27.210Z",
                "model.id": "6ef45878-a6c3-4dcc-b0d7-652d280354f4:"
                "general-enhanced:2022-12-08.27689:",
                "sourceAudioLanguage": LanguageEnum.PT_PT,
                "targetLanguage": pd.NA,
                "transcriptionConfidence": 0.8764816,
            },
            # Not retranscribed becuase the language was not auto-detected
            {
                "callDurationSeconds": "1184.0",
                "&hash": "c775cae4c7103a457ff751132db7bec007e5d5972017b0fd818086b8e07147b7",
                "&id": "451f6100-79d6-4bb5-99fe-d8122dcd7595",
                "&model": "Call",
                "should_be_transcribed": True,
                "should_be_transcribed_by_duration": True,
                "recordingSourceKey": "lake/ingress/landing/communications/voice/"
                "veritas/ms_teams/recordings/451f6100/"
                "451f6100-79d6-4bb5-99fe-d8122dcd7595.wav",
                "transcriptSourceKey": "s3://test.dev.steeleye.co/transcription/"
                "deepgram/deepgram_api/2023/07/12/test1/file1_hash.fr-FR.deepgram.json",
                "transcriptionStatus": "Transcription completed successfully",
                "transcriptionStatus.description": pd.NA,
                "dateTime.transcriptionStart": "2023-07-12T16:19:27.210Z",
                "model.id": "6ef45878-a6c3-4dcc-b0d7-652d280354f4:"
                "general-enhanced:2022-12-08.27689:",
                "sourceAudioLanguage": LanguageEnum.FR_FR,
                "targetLanguage": pd.NA,
                "transcriptionConfidence": 0.8764816,
            },
        ]
    )


@freeze_time(time_to_freeze="2023-07-13 06:59:38.911459+00:00")
class TestDeepgramRetranscribe:
    """Test suite for DeepgramRetranscribe."""

    def test_source_frame_empty(self, tenant_configuration: dict):
        result = run_deepgram_retranscribe(
            source_frame=pd.DataFrame(),
            tenant_configuration=tenant_configuration,
            bucket="test.dev.steeleye.co",
            stack="dev-testing",
            streamed=False,
            cloud_provider=CloudProviderEnum.AWS,
            transcript_upload_path="custom-transcript-upload-path/another-dir",
            cloud_provider_prefix="s3://",
            skip_serializer=True,
        )

        assert result.empty

    def test_retranscribe_low_confidence_calls(
        self,
        source_frame_mixed_confidence: pd.DataFrame,
        tenant_configuration: dict,
        expected_result_df_mixed_confidence: pd.DataFrame,
        mocker,
    ):
        """Test for end-to-end re-transcription.

        Here, 2 rows are re-transcribed, and 2 rows are not
        """
        self.init_task_with_secrets_and_mocks(mocker=mocker)

        mocker.patch(
            "aries_se_comms_tasks.transcription.deepgram.deepgram_run_transcription.get_transcription_usage",  # noqa: E501
            return_value=TRANSCRIPTION_USAGE_MAP,
        )

        result = run_deepgram_retranscribe(
            source_frame=source_frame_mixed_confidence,
            tenant_configuration=tenant_configuration,
            bucket="test.dev.steeleye.co",
            stack="dev-testing",
            streamed=False,
            transcript_upload_path="custom-transcript-upload-path/another-dir/",
            skip_serializer=True,
            cloud_provider=CloudProviderEnum.AWS,
            cloud_provider_prefix="s3://",
        )

        pd.testing.assert_frame_equal(
            left=result.sort_index(axis=1),
            right=expected_result_df_mixed_confidence.sort_index(axis=1),
            check_dtype=False,
        )

    def test_it_re_transcribe_low_confidence_calls_when_transcription_limit_have_been_reached(
        self,
        source_frame_mixed_confidence: pd.DataFrame,
        tenant_configuration: dict,
        expected_result_df_mixed_confidence: pd.DataFrame,
        mocker,
    ):
        """This test ensure the re-transcription is done when the transcription
        limit has been reached and the transcription confidence is low, which
        means the transcription will be re-transcribed."""
        self.init_task_with_secrets_and_mocks(mocker=mocker)

        mocker.patch(
            "aries_se_comms_tasks.transcription.deepgram.deepgram_run_transcription.get_transcription_usage",  # noqa: E501
            return_value={
                TranscriptionProviderEnum.DEEP_GRAM.value: 5,
            },
        )

        mock__transcription_is_not_successful = mocker.patch.object(
            DeepgramRunTranscription, "_transcription_is_not_successful"
        )

        mock__transcription_is_not_successful.assert_not_called()

        tenant_configuration["contractualLimits"]["volume"]["voice"]["transcription"] = 2

        result = run_deepgram_retranscribe(
            source_frame=source_frame_mixed_confidence,
            tenant_configuration=tenant_configuration,
            bucket="test.dev.steeleye.co",
            stack="dev-testing",
            streamed=False,
            transcript_upload_path="custom-transcript-upload-path/another-dir/",
            skip_serializer=True,
            cloud_provider=CloudProviderEnum.AWS,
            cloud_provider_prefix="s3://",
        )

        pd.testing.assert_frame_equal(
            left=result.sort_index(axis=1),
            right=expected_result_df_mixed_confidence.sort_index(axis=1),
            check_dtype=False,
        )

    def test_nothing_to_retranscribe(
        self,
        source_frame_nothing_to_transcribe: pd.DataFrame,
        tenant_configuration: dict,
        mocker,
    ):
        """Test for the case where 3 calls as follows (i) Call 1 has high
        transcription confidence (ii) Call 2 is in English already (iii) Call 3
        was transcribed without auto detection, i.e., with a specified
        language.

        Nothing is re-transcribed in this case, and the source frame is
        returned
        """
        self.init_task_with_secrets_and_mocks(mocker=mocker)

        mocker.patch(
            "aries_se_comms_tasks.transcription.deepgram.deepgram_run_transcription.get_transcription_usage",  # noqa: E501
            return_value=TRANSCRIPTION_USAGE_MAP,
        )

        mock_audit_and_app_metrics = mocker.patch.object(
            DeepgramRetranscribe,
            "audit_and_app_metrics",
        )

        mock_audit_and_app_metrics.assert_not_called()

        result = run_deepgram_retranscribe(
            source_frame=source_frame_nothing_to_transcribe,
            tenant_configuration=tenant_configuration,
            bucket="test.dev.steeleye.co",
            stack="dev-testing",
            streamed=False,
            transcript_upload_path="custom-transcript-upload-path/another-dir",
            skip_serializer=True,
            cloud_provider=CloudProviderEnum.AWS,
            cloud_provider_prefix="s3://",
        )

        pd.testing.assert_frame_equal(left=result, right=source_frame_nothing_to_transcribe)

    def init_task_with_secrets_and_mocks(
        self,
        mocker,
    ) -> None:
        mock_elastic_es_config = mocker.patch.object(deepgram_run_transcription, "get_es_config")
        mock_elastic_es_config.return_value = ResourceConfig(
            host="localhost",
            port="9200",
            scheme="http",
            meta_prefix=MetaPrefix.AMPERSAND,
        )

        mock_elastic = mocker.patch.object(
            deepgram_run_transcription, "get_repository_by_cluster_version"
        )
        es_obj = mock_elastic.return_value
        es_obj.MAX_TERMS_SIZE = 1024
        es_obj.meta.prefix = MetaPrefix.AMPERSAND
        es_obj.meta.hash = "&hash"
        es_obj.meta.id = "&id"
        es_obj.meta.model = "&model"

        mocker.patch.object(
            DeepgramRunTranscription,
            "_is_file_in_cloud",
            side_effect=[
                False,
                False,
            ],
        )

        mock_transcription_response = mocker.patch.object(
            DeepgramRunTranscription,
            "_send_audio_file_in_transcription_request",
        )

        transcription_response_content = (
            b'{"metadata":{"model_info":{"4899aa60-f723-4517-9815-2042acc12a82":'
            b'{"name":"general","version":"2022-01-18.0","tier":"base"}}, '
            b'"created": "2022-07-19T13:55:02.965Z"},'
            b'"results":{"channels":[{"alternatives":[{"transcript":"Hello",'
            b' "confidence": 0.9978787}],"detected_language":"en"}]}}'
        )

        mock_transcription_response.return_value = addict.Dict(
            {
                "ok": True,
                "content": transcription_response_content,
            }
        )

        mocker.patch(
            "aries_se_comms_tasks.transcription.deepgram.deepgram_run_transcription.md5_encode",
            side_effect=[
                "file1_hash",
                "file2_hash",
            ],
        )
        mocker.patch(
            "aries_se_comms_tasks.transcription.deepgram.deepgram_run_transcription.write_json",
            return_value=None,
        )

        mock_load_transcript = mocker.patch.object(
            DeepgramRunTranscription,
            "_load_preexisting_transcript",
        )
        mock_load_transcript.return_value = json.loads(transcription_response_content)

        mock_update_transcription_usage = mocker.patch.object(
            DeepgramRunTranscription,
            "update_transcription_usage",
        )

        # ensure that the usage is not updated
        mock_update_transcription_usage.assert_not_called()

        mocker.patch.object(
            target=CachedTenantWorkflowAPIClient,
            attribute="get",
            return_value=addict.Dict(
                {
                    "tenant": {
                        "lake_prefix": "s3://pinafore.dev.steeleye.co",
                    },
                    "workflow": {"streamed": False},
                },
            ),
        )

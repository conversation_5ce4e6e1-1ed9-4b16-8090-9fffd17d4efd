import pandas as pd
import pandas.testing
from aries_se_comms_tasks.feeds.message.leapxpert_chat.leapxpert_chat_event_mappings import (
    LeapXpertChatEventMappings,
)
from aries_se_comms_tasks.message.static import MessageColumns
from aries_se_core_tasks.utilities.helpers_for_tests import (  # type: ignore[attr-defined] # noqa: E501
    sort_list_columns,
)
from freezegun import freeze_time


class TestGetLeapXpertChatEventMappings:
    @freeze_time(time_to_freeze="2023-03-31 14:40:00.000000+00:00")
    def test_end_to_end(self, input_chat_event_df, metadata_file_info, expected_chat_event_df):
        task = LeapXpertChatEventMappings(
            name="TestChatEventMappings",
            source_frame=input_chat_event_df,
            metadata_file_info=metadata_file_info,
            source_file_uri="s3://jose.dev.steeleye.co/lake/ingress/landing/communications/chat/leapxpert/whatsapp/sample_file.zip",
        )

        result = task.process()

        # Sort list columns
        sort_list_columns(
            list_of_list_columns=[
                MessageColumns.IDENTIFIERS_ALL_IDS,
                MessageColumns.IDENTIFIERS_ALL_COUNTRY_CODES,
            ],
            result_df=result,
            expected_result_df=expected_chat_event_df,
        )

        pandas.testing.assert_frame_equal(left=result, right=expected_chat_event_df)

    @freeze_time(time_to_freeze="2023-03-31 14:40:00.000000+00:00")
    def test_metadata_source_client(self, metadata_file_info):
        source_frame = pd.DataFrame(
            {
                "log.record.sender.channel": [
                    "leap",
                    "whatsapp",
                    "whatsapparchived",
                    "whatsappNative",
                    "wechat-miniapp",
                    "telegramNative",
                    "telegrampassive",
                    "imessagearchived",
                    "wechatwecom",
                    "wechatwecompassive",
                    "wecom",
                    "otherstuff",
                    pd.NA,
                ]
            }
        )
        task = LeapXpertChatEventMappings(
            name="TestChatEventMappings",
            source_frame=source_frame,
            metadata_file_info=metadata_file_info,
            source_file_uri="s3://jose.dev.steeleye.co/lake/ingress/landing/communications/chat/leapxpert/whatsapp/sample_file.zip",
        )

        task.metadata_source_client()

        assert task.target_df["metadata.source.client"].tolist() == [
            "Leap",
            "WhatsApp",
            "WhatsApp",
            "WhatsApp",
            "WeChat",
            "Telegram",
            "Telegram",
            "iMessage",
            "WeChat",
            "WeChat",
            "WeCom",
            pd.NA,
            pd.NA,
        ]

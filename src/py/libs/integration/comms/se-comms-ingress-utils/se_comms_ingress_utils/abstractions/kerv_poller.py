import logging
import os
import pathlib
import pytz
from abc import ABC, abstractmethod
from aries_config_api_compatible_client.tenant_workflow import CompatibleTenantWorkflowAPIClient
from aries_io_event.app_metric import AppMetricFieldSet
from aries_se_api_client.client import AriesApiClient
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from azure.core.exceptions import AzureError
from botocore.exceptions import ClientError
from data_platform_config_api_client.tenant_workflow import TenantWorkflowAPI
from datetime import datetime, timedelta
from paramiko.sftp import SFTPError
from pydantic import BaseModel, Field
from se_comms_ingress_utils.common_util import get_from_to_dates, has_date_range_in_event
from se_data_lake.lake_path import get_streamed_poller_file_path
from se_fsspec_utils.file_system import get_filesystem
from se_fsspec_utils.file_utils import get_file_data, is_exist, list_folder, write
from se_fsspec_utils.sftp_utils import get_sftp_fs_from_addict, get_sftp_lookup_path
from se_secrets_client.utils import get_secrets, secrets_client

logger = logging.getLogger("AbstractKervPoller")

KERV_REMOTE_DIR = "/recordings/"
KERV_ROOT_REMOTE_DIR = "/"
MAX_BATCH_SIZE = 100


class InputParams(BaseModel):
    force_pull: bool = False
    custom_lake_path: str | None = Field(None, min_length=1, max_length=255)
    should_event: bool = False
    look_back_days: int = Field(gt=0, le=7, default=7)
    max_batch_size: int | None = Field(description="file size for transform flow")


class AbstractKervPoller(ABC):
    def __init__(self, transform_workflow_name: str, aries_task_input: AriesTaskInput, config):
        self._config = config
        self._aries_task_input = aries_task_input
        self._tenant_name = aries_task_input.workflow.tenant
        self._workflow_name = aries_task_input.workflow.name
        self._initialize_input_params(aries_task_input)
        self._app_metric = AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})
        self._timestamp_now = datetime.now(tz=pytz.UTC)
        self._workflow_trace_id = aries_task_input.workflow.trace_id

        # get sftp secrets
        self._sftp_secrets = get_secrets(
            secret_client=secrets_client(self._config.vault, self._config),
            tenant_name=self._tenant_name,
            workflow_name=self._workflow_name,
            poller_type="sftp",
        )

        # add proxy host and proxy port to sftp secrets if applicable
        self._sftp_secrets.proxy_port = (
            self._config.proxy.port and int(self._config.proxy.port)
        ) or None
        self._sftp_secrets.proxy_host = self._config.proxy.host

        # get tenant workflow configuration for destination workflow
        self._config_api_client = AriesApiClient(host=config.data_platform_config_api_url)
        self._tenant_workflow_api = TenantWorkflowAPI(self._config_api_client)
        self._transform_workflow_tw_config = CompatibleTenantWorkflowAPIClient.get(
            tenant_workflow_api=self._tenant_workflow_api,
            tenant_name=self._tenant_name,
            workflow_name=transform_workflow_name,
        )

        self._batch_size = (
            self._input_params.max_batch_size
            or self._transform_workflow_tw_config.max_batch_size
            or MAX_BATCH_SIZE
        )
        # get filesystem
        self._destination_fs = get_filesystem(
            cloud=self._transform_workflow_tw_config.tenant.cloud,
            lake_prefix=self._transform_workflow_tw_config.tenant.lake_prefix,
        )

        # get sftp fs
        self._sftp_fs = get_sftp_fs_from_addict(self._sftp_secrets)
        self._failed_files: list = []

    def _process_files(self, date_wise_files, poller_workflow_name):
        # iterate within the date_wise_files that consists of date as
        # the key and the list of files for that date as the value

        for date_, date_files in date_wise_files.items():
            streamed_file_path = get_streamed_poller_file_path(
                workflow_name=poller_workflow_name,
                date=date_,
                custom_path=self._input_params.custom_lake_path,
                is_evented=True,
            )
            # sample sftp path in prod for kerv-voice-poller:
            # /recordings/
            # 1106-bec15430-c316-11ed-b1ad-0954178b5e10-447442778398-
            # 447484261701-1678873798-ee-mt-.zip

            # sample mock-sftp/proxy-sftp path for kerv-voice-poller:
            # data/pinafore/kerv_voice_poll/recordings
            # /1106-bec15430-c316-11ed-b1ad-0954178b5e10-447442778398-
            # 447484261701-1678873798-ee-mt-.zip

            # sample sftp path in prod for kerv-text-poller:
            # /recordings/
            # 1146-447974419117-447514929680-1719415994-ee-sms.zip

            # sample mock-sftp/proxy-sftp path for kerv-text-poller:
            # data/pinafore/kerv_text_poll/recordings/
            # 1146-447974419117-447514929680-1719415994-ee-sms.zip

            logger.info(f"Files available from sftp for date: `{date_}` are: `{date_files}`")

            for each_zip_file in date_files:
                # sample target path for the zip_file for kerv-voice-poller is
                # s3://$bucket/aries/ingress/streamed/evented/kerv_voice_poll
                # /2023/04/06/1106-bec15430-c316-11ed-b1ad-0954178b5e10-
                # 447442778398-447484261701-1678873798-ee-mt-.zip

                # sample target path for the zip file for kerv-text-poller is
                # s3://$bucket/aries/ingress/streamed/evented/kerv_text_poll
                # /2023/04/06/1146-447974419117-447514929680-1719415994-ee-sms.zip

                zip_file_name = pathlib.Path(each_zip_file["name"]).name
                target_file_path = os.path.join(
                    self._transform_workflow_tw_config.tenant.lake_prefix.rstrip("/"),
                    streamed_file_path.rstrip("/"),
                    zip_file_name,
                )

                if not self._input_params.force_pull and is_exist(
                    self._destination_fs, target_file_path
                ):
                    logger.info(
                        f"[SKIPPING] {each_zip_file} as it already exists at {target_file_path}"
                    )
                    continue

                logger.info(f"[PROCESSING] {each_zip_file}")
                try:
                    file_content = get_file_data(
                        fs=self._sftp_fs,
                        file_path=each_zip_file["name"],
                    )
                    write(
                        fs=self._destination_fs,
                        target_path=target_file_path,
                        file_content=file_content,
                    )
                    logger.info(
                        f"Successfully [DOWNLOADED] `{each_zip_file['name']}`"
                        f" and [UPLOADED] to `{target_file_path}`"
                    )
                except (SFTPError, ClientError, AzureError, OSError) as exc:
                    # SFTP, S3(botocore) should be fatal failures
                    raise exc
                except Exception:
                    self._failed_files.append(each_zip_file["name"])
                    logger.exception(
                        f"[FAILED] Error Count: "
                        f"{self._app_metric.metrics['generic']['errored_count']}"
                        f". Error to fetch sftp file {each_zip_file['name']}"
                    )

    def _get_from_and_to_date(self):
        if not has_date_range_in_event(self._aries_task_input):
            logger.info(
                f"IDENTIFIED NORMAL RUN with LOOKBACK DAYS: {self._input_params.look_back_days}"
            )
            poll_from_date = datetime.strftime(
                (self._timestamp_now - timedelta(days=self._input_params.look_back_days)),
                "%Y-%m-%d",
            )
            poll_to_date = datetime.strftime(self._timestamp_now, "%Y-%m-%d")

        # back-fill
        else:
            poll_from_date, poll_to_date = get_from_to_dates(  # type: ignore
                event=self._aries_task_input.input_param.params
            )
            logger.info(f"IDENTIFIED BACK-FILL from {poll_from_date} to {poll_to_date}")

        return poll_from_date, poll_to_date

    def _list_all_sftp_files(self):
        """check poll dates and list all the sftp files."""
        # get sftp path to download files from:
        sftp_path = get_sftp_lookup_path(
            remote_directory=KERV_REMOTE_DIR,
            aries_task_input=self._aries_task_input,
        )

        logger.info(f"[LISTING] files from sftp from remote sftp path: `{sftp_path}`")
        try:
            all_sftp_files = list_folder(fs=self._sftp_fs, remote_dir=sftp_path)
            logger.info(f"SFTP folders in `{sftp_path}`:  `{all_sftp_files}`")
        except IOError:
            logger.exception(
                f"[FAILED] Error Count: {self._app_metric.metrics['generic']['errored_count']}."
                f" No Files available in sftp for path `{sftp_path}`"
            )
            raise
        return all_sftp_files

    def _initialize_input_params(self, aries_task_input):
        input_params = InputParams.validate(aries_task_input.input_param.params)

        # Access the validated input parameters
        self._input_params = input_params

    @abstractmethod
    def run_poller(
        self,
    ) -> AriesTaskResult:
        raise NotImplementedError

    @abstractmethod
    def _get_date_wise_files(self, all_sftp_files, poll_from_date, poll_to_date) -> AriesTaskResult:
        raise NotImplementedError

import datetime
import fsspec
import json
import logging
import os
import random
import shutil
import time
from copilot_utils.client import SeOpenApiClient
from fsspec.core import url_to_fs
from intelligence_core_tasks.comms_neural.neural_assessment import process_batch
from intelligence_core_tasks.comms_neural.schema import CommsNeuralMetrics
from neural_kit.models import NeuralRunConfig
from neural_kit.process_results import evaluate_neural_run
from pathlib import Path
from se_elastic_schema.components.surveillance.neural_thresholds import NeuralThresholds
from se_elastic_schema.models.tenant.communication.email import Email

random.seed(42)
logs = logging.getLogger("neural_kit")


def prepare_folders(config: NeuralRunConfig):
    """Prepare output folders"""
    path = f"{config.OUTPUT_FOLDER}/{config.RUN_NAME}"
    if os.path.exists(path):
        inp = input("Folder already exists. Enter d to delete and continue: ")
        if inp == "d":
            shutil.rmtree(path)
        else:
            raise Exception(f"Folder '{path}' already exists")
    Path(path).mkdir(parents=True, exist_ok=True)
    with open(path + "/config.json", "w") as f:
        json.dump(config.dict(), f)


def read_data(config: NeuralRunConfig) -> list[dict]:
    """Read in data from input file"""

    with open(config.INPUT_FILE, "r") as f:
        data = f.read()
    batch: list[dict] = []
    for line in data.split("\n"):
        if not line:
            break
        rec = json.loads(line)
        rec["body"] = {"text": rec["body.text"]}
        del rec["body.text"]
        batch.append(rec)

    assert config.SAMPLE_SIZE <= len(batch), (
        f"Sample size {config.SAMPLE_SIZE} is greater than batch size {len(batch)}"
    )
    if config.SAMPLE_SIZE > 0:
        batch = random.sample(batch, config.SAMPLE_SIZE)
    return batch


def process_data(config: NeuralRunConfig, dataset: list[dict]):
    base_run_path = f"{config.OUTPUT_FOLDER}/{config.RUN_NAME}/remote_folder"

    total_tokens = 0
    for i in range(config.REPS_START, config.REPS_END):
        logs.info(f"\n\n{'#' * 50}\n Processing run {i}\n")

        for folder in ["", "failed_assessments", "prompt", "response"]:
            os.mkdir(base_run_path + "/" + folder)

        # Initialize variables
        id_2_hash_map: dict[str, str] = {}
        metrics = CommsNeuralMetrics()
        llm_client = SeOpenApiClient(force_override=True)
        neuralThresholds = NeuralThresholds(l1Score=5, l2Score=6, l3Score=6)
        fs: fsspec.AbstractFileSystem = url_to_fs("/")[0]
        start = time.time()
        max_threshold_dict: dict[str, dict[str, float | int]] = {
            "l1Score": {
                "MEDIUM": 5,
                "CRITICAL": 9,
            },
            "l2Score": {
                "MEDIUM": 6,
                "CRITICAL": 9,
            },
            "l3Score": {
                "MEDIUM": 6,
                "CRITICAL": 9,
            },
        }

        # Process batch
        _, neural_token_usage = process_batch(
            llm_client=llm_client,
            comms_batch=dataset,
            upper_bound_date=datetime.datetime.strptime("2025-04-01T00:00:00", "%Y-%m-%dT%H:%M:%S"),
            remote_folder=base_run_path,
            fs=fs,
            id_2_hash_map=id_2_hash_map,
            model=Email,
            metrics=metrics,
            neural_limit=50_000_000,
            neural_token_usage=0,
            neural_watch_thresholds=neuralThresholds,
            max_threshold_dict=max_threshold_dict,
        )
        total_tokens += neural_token_usage

        with open(Path(base_run_path).parent / "metrics.json", "w") as f:
            f.write(metrics.json())

        logs.info(
            f"\n{'#' * 50}\n"
            f"Token usage {neural_token_usage}\n"
            f"Time taken  {round(time.time() - start, 1)}\n"
            f"{'#' * 50}"
        )
        logs.info(f"\n{'#' * 50}\n \n{'#' * 50}")

        shutil.move(base_run_path + "/", base_run_path + f"_{i}/")

    logs.info(f"\n\n{'#' * 50}\n Total token usage {total_tokens}\n{'#' * 50}")


def main(config: NeuralRunConfig):
    prepare_folders(config)
    dataset = read_data(config)
    process_data(config, dataset)
    evaluate_neural_run(config)


if __name__ == "__main__":
    config = NeuralRunConfig(
        MODEL="us.anthropic.claude-3-5-haiku-20241022-v1:0",
        REPS_START=0,
        REPS_END=1,
        SAMPLE_SIZE=10,
        RUN_NAME="NEW_CLAUD35_LABELED_EMAILS_SMALL",
        OUTPUT_FOLDER="./NEURAL_BENCHMARK/RESULTS",
        INPUT_FILE="./NEURAL_BENCHMARK/DATA/ALL_LABELED_EMAILS.NDJSON",
        COMMIT="cb2a5753b426b19f52afa480a5e773d1249e60ab",
    )
    main(config)

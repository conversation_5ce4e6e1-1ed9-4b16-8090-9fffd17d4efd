import fsspec
import json
import logging
import pandas as pd
import tempfile
from aries_se_core_tasks.core.exception import TaskException
from httpx import Headers
from se_elastic_schema.elastic_schema.core.base import SteelEyeBaseModel
from typing import Any, Dict, List, Optional, Type

logger_ = logging.getLogger(__name__)


class SkipIfEmptyNdjson(TaskException):
    pass


class BaseModelJsonEncoder(json.JSONEncoder):
    def default(self, o: Any) -> Any:
        if isinstance(o, Headers):
            return dict(o)

        for obj_type, encoder_func in SteelEyeBaseModel.Config.json_encoders.items():
            if isinstance(o, obj_type):
                return encoder_func(o)

        return super().default(o)


def write_named_temporary_json(content: Dict[str, int], output_filename: Optional[str]) -> str:
    """Create a temporary JSON file with the name `output_filename` and
    contents `content`

    :param content: Dictionary with data that will be stored in the JSON file
    :param output_filename: Name of the resulting JSON file
    :return: Absolute filepath of the JSON file as string
    """
    local_path = tempfile.NamedTemporaryFile(suffix=output_filename, delete=False).name
    with fsspec.open(local_path, "w") as file:
        json.dump(content, file)

    return local_path


def read_json(path: str) -> Any:
    """Read local JSON file.

    :param path: Absolute filepath of the JSON file as string
    :return: Dictionary with the JSON contents
    """
    with fsspec.open(path, "r") as file:
        return json.load(file)


def write_json(path: str, content: Dict[str, Any]):
    """Write JSON file.

    :param path: Absolute filepath of the JSON file as string
    :param content: JSON content
    """
    logger_.info(f"Writing JSON file to {path}")

    with fsspec.open(path, "w") as file:
        file.write(json.dumps(content))


def append_to_ndjson(
    path: str, records: List[dict], json_encoder_cls: Optional[Type[json.JSONEncoder]] = None
):
    """Appends the records to a file.

    :param path: Absolute filepath of the NDJSON file as string
    :type path: str
    :param records: records to append to the file
    :type records: List[dict]
    :param json_encoder_cls: JSON encoder class, defaults to None
    :type json_encoder_cls: Optional[json.JSONEncoder], optional
    """
    with fsspec.open(path, mode="a") as file:
        for i, record in enumerate(records):
            json.dump(record, file, cls=json_encoder_cls)
            file.write("\n")


def ndjson_to_flat_dataframe(
    ndjson_file_path: str, skip_on_emtpy_frame: bool = False, skip_message: Optional[str] = None
) -> pd.DataFrame:
    """Auxiliary file-system-agnostic function to read an NDJSON file using
    FSSPEC. Returns a DataFrame where each row is a record in the NDJSON file,
    and each column is one of the flattened fields of each record.

    :param ndjson_file_path: Absolute filepath of an NDJSON file
    :param skip_on_emtpy_frame: Raises a Skip exception whenever the data frame is empty,
    if this param is True,
    :param skip_message: If non-None, allows you to provide a custom skip message
    :return: Pandas DataFrame with flattened NDJSON data
    """
    records = []  # type: ignore
    with fsspec.open(ndjson_file_path, mode="r") as f:
        records.extend(json.loads(line) for line in f)
    result = pd.json_normalize(records)
    if result.empty and skip_on_emtpy_frame:
        skip_message = skip_message or "NDJSON empty, empty dataframe returned"
        raise SkipIfEmptyNdjson(skip_message)
    return result

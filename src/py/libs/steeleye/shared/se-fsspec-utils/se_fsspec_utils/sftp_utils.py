import addict
import backoff
import fsspec
import io
import logging
import os
import paramiko
from aries_task_link.models import AriesTaskInput
from cachetools import TTLCache, cached
from distutils.util import strtobool
from fsspec.implementations.sftp import SFTPFileSystem
from paramiko import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PKey, RSAKey, SSHException
from typing import Any, List, Optional, Type, Union

logger = logging.getLogger(__name__)

MAX_ATTEMPTS = 3
MAX_TIME = 30


def should_use_mock_path(aries_task_input: AriesTaskInput) -> bool:
    """determine if we need to use mock sftp path or not
    Parameters:
        aries_task_input (AriesTaskInput): The input object containing information about the path
    Returns:
        bool: True if the mock SFTP path should be used, False otherwise.
    """
    should_use_mock = False

    if aries_task_input.workflow.stack and aries_task_input.workflow.stack.startswith("dev"):
        should_use_mock = True

    elif "use_mock_sftp" in aries_task_input.input_param.params:
        use_mock_sftp = aries_task_input.input_param.params.get("use_mock_sftp")
        if isinstance(use_mock_sftp, str):
            use_mock_sftp = strtobool(use_mock_sftp)
        if use_mock_sftp:
            should_use_mock = True

    return should_use_mock


def get_mock_sftp_prefix(
    tenant: Optional[Union[str, None]], workflow: Optional[Union[str, None]]
) -> str:
    """proxy/mock sftp is used for dev testing since its single sftp host,
    following convention is used to store files in proxy/mock_sftp.

    Parameters:
        tenant (Optional[Union[str, None]]): The name of the tenant.
        workflow (Optional[Union[str, None]]): The name of the workflow.

    Returns:
        str: The prefix for proxy/mock SFTP.
    Raises:
        ValueError: If tenant or workflow details are missing.
    """
    if not tenant or not workflow:
        raise ValueError("missing tenant and workflow details")
    return f"data/{tenant}/{workflow}"


def get_sftp_lookup_path(remote_directory: str, aries_task_input: AriesTaskInput) -> str:
    """mock sftp lookup path."""
    if should_use_mock_path(aries_task_input=aries_task_input):
        mock_path = get_mock_sftp_prefix(
            tenant=aries_task_input.workflow.tenant, workflow=aries_task_input.workflow.name
        )
        remote_directory = mock_path + remote_directory
    return remote_directory


@backoff.on_exception(
    backoff.expo,
    paramiko.ssh_exception.AuthenticationException,
    max_tries=MAX_ATTEMPTS,
    max_time=MAX_TIME,
)
@cached(cache=TTLCache(maxsize=4, ttl=300))
def get_sftp_fs(
    host: str,
    port: int,
    username: str,
    private_key_ascii: str | None = None,
    password: str | None = None,
    proxy_port: int | None = None,
    proxy_host: str | None = None,
    use_legacy_auth: bool = False,
) -> SFTPFileSystem:
    """Returns a fsspec filesystem object for SFTP.

    Caches upto 4 connections for 5 minutes.
    """
    authentication_kwargs: dict[str, Any] = build_authentication_kwargs(
        password=password, private_key_ascii=private_key_ascii
    )

    if proxy_host and proxy_port:
        if os.environ.get("LOCAL_RUN"):
            proxy_command = f"corkscrew {proxy_host} {proxy_port} %s %d" % (
                host,
                port,
            )  # to be used on Mac. noqa: E501
        else:
            proxy_command = f"nc -X connect -x {proxy_host}:{proxy_port} %s %d" % (
                host,
                port,
            )  # to test from inside a pod

        proxy = paramiko.proxy.ProxyCommand(proxy_command)
        authentication_kwargs["sock"] = proxy
        if use_legacy_auth:
            authentication_kwargs["disabled_algorithms"] = {
                "keys": ["rsa-sha2-256", "rsa-sha2-512"]
            }

    if not authentication_kwargs:
        raise Exception("Missing credentials: must provide either id_path or password")

    get_sftp_fs.cache_clear()  # Clear the TTL cache

    logger.info("Creating ffspec SFTPFileSystem")
    return fsspec.filesystem(
        "sftp",
        host=host,
        port=port,
        username=username,
        **authentication_kwargs,
        skip_instance_cache=True,
    )


def build_authentication_kwargs(
    password: str | None, private_key_ascii: str | None
) -> dict[str, Any]:
    """Builds the authentication kwargs for the SFTP connection. Supports
    password or SSH key authentication. Supports multiple SSH key types.

    :param password: Auth password as string
    :param private_key_ascii: Auth SSH private key as string

    :return: A Dictionary containing the authentication kwargs to be used in Paramiko
    """

    authentication_kwargs: dict[str, Any] = dict()

    if password:
        authentication_kwargs["password"] = password

    elif private_key_ascii:
        supported_key_types: List[Type[PKey]] = [RSAKey, Ed25519Key]
        for key_type in supported_key_types:
            try:
                pkey = key_type.from_private_key(io.StringIO(private_key_ascii))
            except SSHException:  # wrong key type, continue to the next one
                continue
            else:  # exception was not raised, thus we have a valid key and key type -> break loop
                break

        else:  # else clause will only be triggered if the for loop completes without breaking
            raise Exception(
                f"Invalid private key provided. Supported key types: {[x.__name__ for x in supported_key_types]}"  # noqa: E501
            )

        authentication_kwargs["pkey"] = pkey

    else:
        raise Exception("No suitable authentication strategy found for sftp")

    return authentication_kwargs


def get_sftp_fs_from_addict(
    sftp_secrets: addict.Dict, use_legacy_auth: bool = False
) -> SFTPFileSystem:
    """Wrapper on top of get_sftp_fs method in sftp_utils."""
    return get_sftp_fs(
        sftp_secrets.get("host"),
        int(sftp_secrets.get("port")),
        sftp_secrets.get("username"),
        sftp_secrets.get("private_key_ascii"),
        sftp_secrets.get("password"),
        sftp_secrets.get("proxy_port"),
        sftp_secrets.get("proxy_host"),
        use_legacy_auth=use_legacy_auth,
    )

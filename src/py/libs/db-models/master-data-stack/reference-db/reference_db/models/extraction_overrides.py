from reference_db.declarative_base import Base
from se_db_utils.psql_utils import utcnow
from sqlalchemy import Column, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import text
from sqlalchemy.sql.sqltypes import Boolean, DateTime, String, Text


class ExtractionOverrides(Base):
    __tablename__ = "ExtractionOverrides"
    __table_args__ = (Index("eof_unique_key", "ric", "type", unique=True),)
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        server_default=text("gen_random_uuid()"),
    )
    _type = Column(String(9), nullable=False, comment='["tick", "eod_stats", "obd"]', name="type")  # type: ignore
    ric = Column(ForeignKey("RefinitivNews.Ric.ric"), nullable=False)  # type: ignore
    currencyNormOverride = Column(Boolean, nullable=True)
    createdDateTime = Column(DateTime, nullable=False, server_default=utcnow())
    createdBy = Column(Text, nullable=False, server_default=text("'UNKNOWN'"))
    updatedDateTime = Column(DateTime, nullable=True, onupdate=utcnow())
    updatedBy = Column(Text, nullable=True, onupdate=text("'UNKNOWN'"))

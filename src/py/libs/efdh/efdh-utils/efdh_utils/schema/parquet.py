import enum
import polars as pl
import pyarrow as pa
from efdh_utils.schema.base import BaseColumns
from efdh_utils.schema.refinitiv import (
    RefinitivEventType,
    RefinitivExtractColumns,
    RefinitivOrderBookDepthColumns,
)

SEQ_NO_COLUMN_MAP = {RefinitivExtractColumns.SEQ_NO: "Sequence Number"}


class FileNameTemplates:
    EVENT_RESAMPLED_FILENAME_TPL = "{ric}__{event_type}__{resolution}__{date}.parquet"
    STATS_FILENAME_TPL = "{ric}__{event_type}__STATS__DAY__{date}.parquet"
    ROLLING_YEAR = "ROLLING_YEAR"
    ROLLING_MONTH = "ROLLING_MONTH"
    DAILY_RESAMPLED_FILENAME_TPL = "{ric}__{event_type}__{resolution}__{year}{month}{day}.parquet"
    EOD_STATS_FILENAME_TPL = "{ric}__EOD_ROLLING_STATS.parquet"
    EOD_STATS_HISTORICAL_FILENAME_TPL = "{ric}__{year}__EOD_ROLLING_STATS.parquet"
    EOD_STATS_PATH_TPL = "lake/ingress/ric/curated/{ric}/" + EOD_STATS_FILENAME_TPL
    EOD_STATS_HISTORICAL_PATH_TPL = (
        "lake/ingress/ric/curated/{ric}/" + EOD_STATS_HISTORICAL_FILENAME_TPL
    )

    ROLLING_TICK_PTH_TPL = (
        "{ric}/{ric}__{event_type}__{rolling_resolution}__{rolling_period}.parquet"
    )


class RollingPeriod(enum.StrEnum):
    MONTH = "ROLLING_MONTH"
    YEAR = "ROLLING_YEAR"


class ResolutionLevel2(enum.StrEnum):
    TICK = "TICK"
    SECOND = "SECOND"
    MINUTE = "MINUTE"
    MILLISECOND = "MILLISECOND"


class ResolutionLevel1(enum.StrEnum):
    TICK = "TICK"
    MINUTE = "MINUTE"
    HOUR = "HOUR"


class RollingResolution(enum.StrEnum):
    MINUTE = "MINUTE"
    HOUR = "HOUR"


class NewQuoteColumns:
    QUOTE_PRICE_CURRENCY = "Quote Price Currency"


class NewAuctionColumns:
    AUCTION_PRICE_CURRENCY = "Auction Price Currency"


class QuoteTickInputColumns(BaseColumns):
    EXCH_TIME = RefinitivExtractColumns.EXCH_TIME
    ACCUMULATED_ASK_ORDER = RefinitivExtractColumns.ACCUMULATED_ASK_ORDER
    ACCUMULATED_BID_ORDER = RefinitivExtractColumns.ACCUMULATED_BID_ORDER
    ASK_PRICE = RefinitivExtractColumns.ASK_PRICE
    ASK_SIZE = RefinitivExtractColumns.ASK_SIZE
    BID_PRICE = RefinitivExtractColumns.BID_PRICE
    BID_SIZE = RefinitivExtractColumns.BID_SIZE
    DATE_TIME = RefinitivExtractColumns.DATE_TIME
    GMT_OFFSET = RefinitivExtractColumns.GMT_OFFSET
    MID_PRICE = RefinitivExtractColumns.MID_PRICE
    RIC = RefinitivExtractColumns.RIC


class QuoteTickColumns(QuoteTickInputColumns):
    QUOTE_PRICE_CURRENCY = NewQuoteColumns.QUOTE_PRICE_CURRENCY


class TradeTickInputColumns(BaseColumns):
    EXCH_TIME = RefinitivExtractColumns.EXCH_TIME
    DATE_TIME = RefinitivExtractColumns.DATE_TIME
    GMT_OFFSET = RefinitivExtractColumns.GMT_OFFSET
    HIGH = RefinitivExtractColumns.HIGH
    LOW = RefinitivExtractColumns.LOW
    MARKET_VWAP = RefinitivExtractColumns.MARKET_VWAP
    OPEN = RefinitivExtractColumns.OPEN
    PRICE = RefinitivExtractColumns.PRICE
    RIC = RefinitivExtractColumns.RIC
    SEQ_NO = RefinitivExtractColumns.SEQ_NO
    TRADE_PRICE_CURRENCY = RefinitivExtractColumns.TRADE_PRICE_CURRENCY
    VOLUME = RefinitivExtractColumns.VOLUME


class TradeTickColumns(TradeTickInputColumns):
    SEQ_NO = SEQ_NO_COLUMN_MAP[RefinitivExtractColumns.SEQ_NO]


class AuctionTickInputColumns(BaseColumns):
    EXCH_TIME = RefinitivExtractColumns.EXCH_TIME
    DATE_TIME = RefinitivExtractColumns.DATE_TIME
    GMT_OFFSET = RefinitivExtractColumns.GMT_OFFSET
    RIC = RefinitivExtractColumns.RIC
    IND_AUCTION_PRICE = RefinitivExtractColumns.IND_AUCTION_PRICE
    IND_AUCTION_VOLUME = RefinitivExtractColumns.IND_AUCTION_VOLUME
    QUALIFIERS = RefinitivExtractColumns.QUALIFIERS
    SEQ_NO = RefinitivExtractColumns.SEQ_NO
    PRICE = RefinitivExtractColumns.PRICE
    VOLUME = RefinitivExtractColumns.VOLUME


class AuctionTickColumns(AuctionTickInputColumns):
    SEQ_NO = SEQ_NO_COLUMN_MAP[RefinitivExtractColumns.SEQ_NO]
    AUCTION_PRICE_CURRENCY = NewAuctionColumns.AUCTION_PRICE_CURRENCY


class EoDStatsColumns(BaseColumns):
    CLOSE_ASK_PRICE = "Close Ask Price"
    CLOSE_BID_PRICE = "Close Bid Price"
    CLOSE_PRICE = "Close Price"
    CURRENCY = "Currency"
    DATE = "Date"
    EXCHANGE_CODE = "Exchange Code"
    HIGH_ASK_PRICE = "High Ask Price"
    HIGH_BID_PRICE = "High Bid Price"
    HIGH_PRICE = "High Price"
    MARKET_VWAP = "Market VWAP"
    VWAP = "VWAP"
    LOW_PRICE = "Low Price"
    LOW_ASK_PRICE = "Low Ask Price"
    LOW_BID_PRICE = "Low Bid Price"
    OPEN_ASK_PRICE = "Open Ask Price"
    OPEN_BID_PRICE = "Open Bid Price"
    OPEN_INTEREST = "Open Interest"
    OPEN_PRICE = "Open Price"
    RIC = "#RIC"
    TRADE_VOLUME = "Trade Volume"
    TRADED_VOLUME_20D_EMA = "Traded Volume 20 Day EMA"

    # Calculated Columns
    PRICE_VOLATILITY = "Close Price 10 Day Volatility"
    VOLUME_VOLATILITY = "Trade Volume 10 Day Volatility"
    VOLUME_EMA = "Traded Volume 20 Day EMA"
    DAILY_TRADED_NOTIONAL = "Daily Traded Notional"
    VENUE = "Venue"


class EoDStatsPriceColumns(BaseColumns):
    CLOSE_ASK_PRICE = "Close Ask Price"
    CLOSE_BID_PRICE = "Close Bid Price"
    CLOSE_PRICE = "Close Price"
    HIGH_ASK_PRICE = "High Ask Price"
    HIGH_BID_PRICE = "High Bid Price"
    HIGH_PRICE = "High Price"
    MARKET_VWAP = "Market VWAP"
    VWAP = "VWAP"
    LOW_PRICE = "Low Price"
    LOW_ASK_PRICE = "Low Ask Price"
    LOW_BID_PRICE = "Low Bid Price"
    OPEN_ASK_PRICE = "Open Ask Price"
    OPEN_BID_PRICE = "Open Bid Price"
    OPEN_PRICE = "Open Price"


class ParquetCDSColumns(BaseColumns):
    RIC = "#RIC"
    RED_CODE = "RedCode"
    SENIORITY = "Seniority"
    TENOR = "Tenor"
    CURRENCY = "Currency"
    CLOSE_PRICE = "Close Price"
    CLOSE_ASK_PRICE = "Close Ask Price"
    CLOSE_BID_PRICE = "Close Bid Price"
    DATE = "Date"


class ParquetLoansColumns(BaseColumns):
    RIC = "#RIC"
    DATE = "Date"
    CLOSE_PRICE = "Close Price"
    CLOSE_ASK_PRICE = "Close Ask Price"
    CLOSE_BID_PRICE = "Close Bid Price"
    CURRENCY = "Currency"


class LoansDeltaColumnsRicLookup(BaseColumns):
    INSTRUMENT_UNIQUE_IDENTIFIER = "instrumentUniqueIdentifier"
    RIC = "ric"
    PREFERRED_RIC = "preferredRic"
    PREFERRED_RIC_CURRENCY_RAW = "preferredRicCurrencyRaw"
    PREFERRED_RIC_CURRENCY = "preferredRicCurrency"
    PRIMARY_TRADING_RIC = "primaryTradingRic"
    PREFERRED_RIC_VENUE_REFINITIV = "preferredRicVenueRefinitiv"
    INSTRUMENT_LIST_ID = "instrumentListId"
    PREFERRED_RIC_VENUE = "preferredRicVenue"
    CFI_CODE = "cfi.code"
    CFI_ATTRIBUTE1 = "cfi.attribute1"
    CFI_ATTRIBUTE2 = "cfi.attribute2"
    CFI_ATTRIBUTE3 = "cfi.attribute3"
    CFI_ATTRIBUTE4 = "cfi.attribute4"
    CFI_CATEGORY = "cfi.category"
    CFI_GROUP = "cfi.group"
    COMPOSITE_RIC = "compositeRic"
    REFINITIV_EXCHANGE_CODE = "refinitivExchangeCode"
    TIMESTAMPS_COVERAGE_FROM = "timestamps.coverageFrom"
    TIMESTAMPS_COVERAGE_TO = "timestamps.coverageTo"


class QuoteCurrencyColumn:
    QUOTE_PRICE_CURRENCY = "Quote Price Currency"


class AuctionCurrencyColumn:
    AUCTION_PRICE_CURRENCY = "Auction Price Currency"


EVENT_TYPE_COLUMN_MAP = {
    RefinitivEventType.AUCTION: AuctionTickColumns(),
    RefinitivEventType.QUOTE: QuoteTickColumns(),
    RefinitivEventType.TRADE: TradeTickColumns(),
}


class RicColumns:
    RIC = "ric"
    CURRENCY = "currency"
    CURRENCY_RAW = "currencyRaw"


class TickDtypes:
    TRADES = {
        TradeTickColumns.DATE_TIME: pl.Int64,
        TradeTickColumns.GMT_OFFSET: pl.Int64,
        TradeTickColumns.HIGH: pl.Float64,
        TradeTickColumns.LOW: pl.Float64,
        TradeTickColumns.MARKET_VWAP: pl.Float64,
        TradeTickColumns.OPEN: pl.Float64,
        TradeTickColumns.PRICE: pl.Float64,
        TradeTickColumns.RIC: pl.Utf8,
        TradeTickColumns.TRADE_PRICE_CURRENCY: pl.Utf8,
        TradeTickColumns.VOLUME: pl.Int64,
        TradeTickColumns.EXCH_TIME: pl.Utf8,
        TradeTickColumns.SEQ_NO: pl.Int64,
    }

    QUOTES = {
        QuoteTickColumns.ACCUMULATED_ASK_ORDER: pl.Float64,
        QuoteTickColumns.ACCUMULATED_BID_ORDER: pl.Float64,
        QuoteTickColumns.ASK_PRICE: pl.Float64,
        QuoteTickColumns.ASK_SIZE: pl.Float64,
        QuoteTickColumns.BID_PRICE: pl.Float64,
        QuoteTickColumns.BID_SIZE: pl.Float64,
        QuoteTickColumns.DATE_TIME: pl.Int64,
        QuoteTickColumns.GMT_OFFSET: pl.Int64,
        QuoteTickColumns.MID_PRICE: pl.Float64,
        QuoteTickColumns.RIC: pl.Utf8,
    }

    AUCTIONS = {
        AuctionTickColumns.EXCH_TIME: pl.Utf8,
        AuctionTickColumns.DATE_TIME: pl.Int64,
        AuctionTickColumns.GMT_OFFSET: pl.Float64,
        AuctionTickColumns.RIC: pl.Utf8,
        AuctionTickColumns.IND_AUCTION_PRICE: pl.Float64,
        AuctionTickColumns.IND_AUCTION_VOLUME: pl.Float64,
        AuctionTickColumns.QUALIFIERS: pl.Utf8,
        AuctionTickColumns.PRICE: pl.Float64,
        AuctionTickColumns.VOLUME: pl.Int64,
    }


MARKET_COLUMN_DTYPE = {
    RefinitivEventType.TRADE: pa.schema(
        {
            TradeTickColumns.EXCH_TIME: pa.string(),
            TradeTickColumns.DATE_TIME: pa.int64(),
            TradeTickColumns.GMT_OFFSET: pa.int64(),
            TradeTickColumns.HIGH: pa.float64(),
            TradeTickColumns.LOW: pa.float64(),
            TradeTickColumns.MARKET_VWAP: pa.float64(),
            TradeTickColumns.OPEN: pa.float64(),
            TradeTickColumns.PRICE: pa.float64(),
            TradeTickColumns.RIC: pa.string(),
            TradeTickColumns.SEQ_NO: pa.int64(),
            TradeTickColumns.TRADE_PRICE_CURRENCY: pa.string(),
            TradeTickColumns.VOLUME: pa.int64(),
        }
    ),
    RefinitivEventType.QUOTE: pa.schema(
        {
            QuoteTickColumns.EXCH_TIME: pa.string(),
            QuoteTickColumns.DATE_TIME: pa.int64(),
            QuoteTickColumns.GMT_OFFSET: pa.int64(),
            QuoteTickColumns.RIC: pa.string(),
            QuoteCurrencyColumn.QUOTE_PRICE_CURRENCY: pa.string(),
            QuoteTickColumns.ACCUMULATED_ASK_ORDER: pa.float64(),
            QuoteTickColumns.ACCUMULATED_BID_ORDER: pa.float64(),
            QuoteTickColumns.ASK_PRICE: pa.float64(),
            QuoteTickColumns.ASK_SIZE: pa.float64(),
            QuoteTickColumns.BID_PRICE: pa.float64(),
            QuoteTickColumns.BID_SIZE: pa.float64(),
            QuoteTickColumns.MID_PRICE: pa.float64(),
        }
    ),
    RefinitivEventType.ELEKTRON: pa.schema(
        {
            EoDStatsColumns.CLOSE_ASK_PRICE: pa.float64(),
            EoDStatsColumns.CLOSE_BID_PRICE: pa.float64(),
            EoDStatsColumns.CLOSE_PRICE: pa.float64(),
            EoDStatsColumns.CURRENCY: pa.string(),
            EoDStatsColumns.DATE: pa.string(),
            EoDStatsColumns.EXCHANGE_CODE: pa.string(),
            EoDStatsColumns.HIGH_ASK_PRICE: pa.float64(),
            EoDStatsColumns.HIGH_BID_PRICE: pa.float64(),
            EoDStatsColumns.HIGH_PRICE: pa.float64(),
            EoDStatsColumns.MARKET_VWAP: pa.float64(),
            EoDStatsColumns.LOW_PRICE: pa.float64(),
            EoDStatsColumns.LOW_ASK_PRICE: pa.float64(),
            EoDStatsColumns.LOW_BID_PRICE: pa.float64(),
            EoDStatsColumns.OPEN_ASK_PRICE: pa.float64(),
            EoDStatsColumns.OPEN_BID_PRICE: pa.float64(),
            EoDStatsColumns.OPEN_INTEREST: pa.float64(),
            EoDStatsColumns.OPEN_PRICE: pa.float64(),
            EoDStatsColumns.RIC: pa.string(),
            EoDStatsColumns.TRADE_VOLUME: pa.float64(),
            EoDStatsColumns.TRADED_VOLUME_20D_EMA: pa.float64(),
        }
    ),
    RefinitivEventType.OBD: pa.schema(
        {
            RefinitivOrderBookDepthColumns.RIC: pa.string(),
            RefinitivOrderBookDepthColumns.DATE_TIME: pa.int64(),
            RefinitivOrderBookDepthColumns.DOMAIN: pa.string(),
            RefinitivOrderBookDepthColumns.EXCH_TIME: pa.string(),
            RefinitivOrderBookDepthColumns.GMT_OFFSET: pa.int64(),
            RefinitivOrderBookDepthColumns.TYPE: pa.string(),
            RefinitivOrderBookDepthColumns.COUNT: pa.int64(),
            RefinitivOrderBookDepthColumns.DATE_TIME_RESAMPLED: pa.int64(),
            RefinitivOrderBookDepthColumns.L1_BID_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L1_BID_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L1_BUY_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L1_ASK_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L1_ASK_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L1_SELL_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L2_BID_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L2_BID_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L2_BUY_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L2_ASK_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L2_ASK_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L2_SELL_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L3_BID_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L3_BID_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L3_BUY_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L3_ASK_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L3_ASK_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L3_SELL_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L4_BID_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L4_BID_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L4_BUY_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L4_ASK_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L4_ASK_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L4_SELL_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L5_BID_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L5_BID_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L5_BUY_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L5_ASK_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L5_ASK_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L5_SELL_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L6_BID_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L6_BID_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L6_BUY_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L6_ASK_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L6_ASK_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L6_SELL_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L7_BID_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L7_BID_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L7_BUY_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L7_ASK_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L7_ASK_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L7_SELL_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L8_BID_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L8_BID_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L8_BUY_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L8_ASK_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L8_ASK_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L8_SELL_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L9_BID_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L9_BID_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L9_BUY_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L9_ASK_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L9_ASK_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L9_SELL_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L10_BID_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L10_BID_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L10_BUY_NO: pa.float64(),
            RefinitivOrderBookDepthColumns.L10_ASK_PRICE: pa.float64(),
            RefinitivOrderBookDepthColumns.L10_ASK_SIZE: pa.float64(),
            RefinitivOrderBookDepthColumns.L10_SELL_NO: pa.float64(),
            QuoteCurrencyColumn.QUOTE_PRICE_CURRENCY: pa.string(),
        }
    ),
}

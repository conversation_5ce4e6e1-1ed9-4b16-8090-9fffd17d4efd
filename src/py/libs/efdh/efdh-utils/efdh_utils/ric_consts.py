class RicCSVInputColumns:
    INSTRUMENT_UNIQUE_IDENTIFIER = "instrumentUniqueIdentifier"
    PREFERRED_RIC = "preferredRic"
    RIC = "ric"
    PRIMARY_TRADING_RIC = "primaryTradingRic"
    PREFERRED_RIC_CURRENCY_RAW = "preferredRicCurrencyRaw"
    PREFERRED_RIC_CURRENCY = "preferredRicCurrency"
    PREFERRED_RIC_VENUE_REFINITIV = "preferredRicVenueRefinitiv"
    INSTRUMENT_LIST_ID = "instrumentListId"
    PREFERRED_RIC_VENUE = "preferredRicVenue"
    CFI_CODE = "cfi.code"
    CFI_ATTRIB_1 = "cfi.attribute1"
    CFI_ATTRIB_2 = "cfi.attribute2"
    CFI_ATTRIB_3 = "cfi.attribute3"
    CFI_ATTRIB_4 = "cfi.attribute4"
    CFI_CATEGORY = "cfi.category"
    CFI_GROUP = "cfi.group"
    COMPOSITE_RIC = "compositeRic"
    REFINITIV_EXCHANGE_CODE = "refinitivExchangeCode"
    COVERAGE_FROM = "timestamps.coverageFrom"
    COVERAGE_TO = "timestamps.coverageTo"
    INSTRUMENT_MAPPING_TYPE = "_enriched.instrumentIdentifierType"
    PERM_ID = "permId"
    MARKET_IDENTIFIER_TYPE = "marketIdentifierType"
    INSTRUMENT_ID_CODE = "instrumentIdCode"

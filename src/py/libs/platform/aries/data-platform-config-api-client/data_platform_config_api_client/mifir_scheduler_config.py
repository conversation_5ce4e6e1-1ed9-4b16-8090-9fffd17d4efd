import addict
import logging
from aries_se_api_client.base import EndPoint
from aries_se_api_client.client import AriesAbstractApiClient
from aries_se_api_client.response_parsers import AddictResponseParser, ListAddictResponseParser
from data_platform_config_api_client.base import DataPlatformConfigAPI
from typing import Any, ClassVar, Dict

log = logging.getLogger(__name__)


class MifirSchedulerConfigAPI(DataPlatformConfigAPI):
    PREFIX: ClassVar[str] = "/stacks/{stack_name}/tenants/{tenant_name}/mifir_schedules"
    GET_CONFIGS: ClassVar[str] = "/"
    GET_CONFIG: ClassVar[str] = "/schedule_type/{schedule_type}"
    BULK_UPSERT_CONFIGS: ClassVar[str] = "/"
    UPSERT_CONFIG: ClassVar[str] = "/schedule_type/{schedule_type}"

    def get_prefix(self) -> str:
        return super().get_prefix() + MifirSchedulerConfigAPI.PREFIX

    def __init__(self, client: AriesAbstractApiClient):
        self._client = client

        self._get_config: EndPoint[list[addict.Dict]] = EndPoint(
            path=MifirSchedulerConfigAPI.GET_CONFIG,
            http_verb="GET",
            response_parser=AddictResponseParser(),
        )

        self._get_configs: EndPoint[addict.Dict] = EndPoint(
            path=MifirSchedulerConfigAPI.GET_CONFIGS,
            http_verb="GET",
            response_parser=ListAddictResponseParser(),
        )
        self._bulk_upsert_configs_by_tenant: EndPoint[addict.Dict] = EndPoint(
            path=MifirSchedulerConfigAPI.BULK_UPSERT_CONFIGS,
            http_verb="PUT",
            response_parser=AddictResponseParser(),
        )
        self._upsert_config_by_tenant_and_schedule_type: EndPoint[addict.Dict] = EndPoint(
            path=MifirSchedulerConfigAPI.UPSERT_CONFIG,
            http_verb="PUT",
            response_parser=AddictResponseParser(),
        )

    def get_configs(
        self,
        stack_name: str,
        tenant_name: str,
        enabled: bool | None,
    ):
        query_param: Dict[str, Any] = {"enabled": enabled}
        return self._client.call_api(
            api=self,
            endpoint=self._get_configs,
            path_params={"stack_name": stack_name, "tenant_name": tenant_name},
            query_param=query_param,
        )

    def get_config(
        self,
        stack_name: str,
        tenant_name: str,
        schedule_type: str,
        enabled: bool | None,
    ):
        query_param: Dict[str, Any] = {"enabled": enabled}
        return self._client.call_api(
            api=self,
            endpoint=self._get_config,
            path_params={
                "stack_name": stack_name,
                "tenant_name": tenant_name,
                "schedule_type": schedule_type,
            },
            query_param=query_param,
        )

    def bulk_upsert_configs_by_tenant(
        self,
        json_body: dict[str, Any],
        stack_name: str,
        tenant_name: str,
    ):
        return self._client.call_api(
            api=self,
            endpoint=self._bulk_upsert_configs_by_tenant,
            json_body=json_body,
            path_params={"stack_name": stack_name, "tenant_name": tenant_name},
        )

    def upsert_config_by_tenant_and_schedule_type(
        self,
        json_body: dict[str, str],
        stack_name: str,
        tenant_name: str,
        schedule_type: str,
    ):
        return self._client.call_api(
            api=self,
            endpoint=self._upsert_config_by_tenant_and_schedule_type,
            json_body=json_body,
            path_params={
                "stack_name": stack_name,
                "tenant_name": tenant_name,
                "schedule_type": schedule_type,
            },
        )

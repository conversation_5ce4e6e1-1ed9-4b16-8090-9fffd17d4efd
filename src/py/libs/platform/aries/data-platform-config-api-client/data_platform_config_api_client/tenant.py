import addict
import logging
from aries_se_api_client.base import EndPoint
from aries_se_api_client.client import AriesAbstractApiClient
from aries_se_api_client.response_parsers import AddictResponseParser, ListAddictResponseParser
from data_platform_config_api_client.base import DataPlatformConfigAPI
from typing import ClassVar

log = logging.getLogger(__name__)


class TenantAPI(DataPlatformConfigAPI):
    PREFIX: ClassVar[str] = "/stacks/{stack_name}/tenants"
    GET_ALL: ClassVar[str] = "/"
    GET: ClassVar[str] = "/{tenant_name}"
    ADD: ClassVar[str] = "/{tenant_name}"

    def get_prefix(self) -> str:
        return super().get_prefix() + TenantAPI.PREFIX

    def __init__(self, client: AriesAbstractApiClient):
        self._client = client
        self._get_all: EndPoint[addict.Dict] = EndPoint(
            path=TenantAPI.GET_ALL,
            http_verb="GET",
            response_parser=ListAddictResponseParser(),
        )
        self._get: EndPoint[addict.Dict] = EndPoint(
            path=TenantAPI.GET,
            http_verb="GET",
            response_parser=AddictResponseParser(),
        )
        self._add: EndPoint[list[addict.Dict]] = EndPoint(
            path=TenantAPI.ADD,
            http_verb="POST",
            response_parser=AddictResponseParser(),
        )

    def get_all(
        self,
        stack_name: str,
    ):
        return self._client.call_api(
            api=self,
            endpoint=self._get_all,
            path_params={
                "stack_name": stack_name,
            },
        )

    def get(
        self,
        stack_name: str,
        tenant_name: str,
    ):
        return self._client.call_api(
            api=self,
            endpoint=self._get,
            path_params={
                "stack_name": stack_name,
                "tenant_name": tenant_name,
            },
        )

    def add(self, json_body: dict[str, str], stack_name: str, tenant_name: str):
        return self._client.call_api(
            api=self,
            endpoint=self._add,
            path_params={"stack_name": stack_name, "tenant_name": tenant_name},
            json_body=json_body,
        )

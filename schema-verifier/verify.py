import functools
import io
import json
import re

import boto3
from deepdiff import DeepDiff
from elasticsearch6 import Elasticsearch
from semver import compare
from semver import parse

SCHEMA_BUCKET = "schema.steeleye.co"


class S3ObjectInterator(io.RawIOBase):
    """
    Stream a s3 file into a python variable, either total or as stream chunks

    data = S3ObjectInterator(bucket, key).read()

    or

    for data_chunk in S3ObjectInterator(bucket, key).read(500):
        print(data_chunk)

    ref: https://stackoverflow.com/a/40854612
    """

    def __init__(self, bucket, key):
        """Initialize with S3 bucket and key names"""
        self.s3c = boto3.client("s3")
        the_object = self.s3c.get_object(Bucket=bucket, Key=key)
        self.obj_stream = self.s3c.get_object(Bucket=bucket, Key=key)["Body"]

    def read(self, n=-1):
        """Read from the stream"""
        return self.obj_stream.read() if n == -1 else self.obj_stream.read(n)


def get_mapping_from_schema(schema_mappings):
    keys = schema_mappings["properties"]["_meta"]["properties"].keys()
    mapping = {}

    for key in keys:
        mapping[f"&{key}"] = schema_mappings["properties"]["_meta"]["properties"][key]

    keys = schema_mappings["properties"].keys()
    for key in keys:
        if key == "_meta":
            continue
        mapping[key] = schema_mappings["properties"][key]

    return mapping


def get_schema(schema_version):

    key = f"elasticsearch/{schema_version}/sdp/schema.json"

    try:
        data = S3ObjectInterator(bucket=SCHEMA_BUCKET, key=key).read()
    except:
        raise SystemError(f"File couldn't be read: s3://{SCHEMA_BUCKET}/{key}")

    data = data.decode("utf8").replace("_meta.", "&")

    schema = json.loads(data)

    mappings = {}
    keys = list(schema["schemaDict"].keys())
    for key in keys:
        model = key.replace("{realm}:", "")
        mappings[model] = get_mapping_from_schema(schema["schemaDict"][key]["mappings"])

    return mappings


def main():
    with open("schema.json") as fp:
        data = fp.read()

    data = data.replace("_meta.", "&")

    schema = json.loads(data)

    mappings = {}
    keys = list(schema["schemaDict"].keys())
    for key in keys:
        mappings[key.replace("{realm}:", "")] = schema["schemaDict"][key]["mappings"]

    account_firm = get_mapping_from_schema(mappings, "AccountFirm")
    account_firm_2 = pinafore_account_firm[list(pinafore_account_firm.keys())[0]][
        "mappings"
    ]["AccountFirm"]["properties"]

    diff = DeepDiff(
        account_firm,
        account_firm_2,
        ignore_order=False,
        verbose_level=1,
        ignore_string_case=True,
    )
    print("")
    print("Added")
    print(diff["dictionary_item_added"])
    print("")
    print("Removed")
    print(diff["dictionary_item_removed"])
    print("")
    print("Changed values")
    print(diff["values_changed"])
    print("")


def get_schema_diff(schema_version_1, schema_version_2, model=None):
    """
    Get the diff of two schema versions:

        get_schema_diff("4.1.45", "5.1.33")

        - It always compares oldest with newest
        - Uses the published schema as present in the s3 bucket schema.steeleye.co
    """
    if compare(schema_version_1, schema2) > 0:
        tmp = schema_version_1
        schema_version_1 = schema_version_2
        schema_version_2 = tmp

    schema1 = get_schema(schema_version_1)
    schema2 = get_schema(schema_version_2)

    if not model:
        schema_diff = DeepDiff(
            schema1,
            schema2,
            ignore_order=False,
            verbose_level=1,
            ignore_string_case=True,
        )
    else:
        schema_diff = DeepDiff(
            schema1[model],
            schema2[model],
            ignore_order=False,
            verbose_level=1,
            ignore_string_case=True,
        )
    return schema_diff


def camel2snake(camel_case):
    """
    Returns a_string for a given AString

    ref: https://stackoverflow.com/a/1176023
    """

    if not hasattr(camel2snake, "pattern"):
        camel2snake.pattern = re.compile(r"(?<!^)(?=[A-Z])")
    return camel2snake.pattern.sub("_", camel_case).lower()


def snake2camel(snake_case):
    """
    Returns AString for a given a_string

    ref: https://stackoverflow.com/a/1176023
    """
    return "".join(word.title() for word in snake_case.split("_"))


def get_tenant_schema_version(tenant):
    es = Elasticsearch()
    index = ".abaci"
    versions = es.search(index=index, body={})


def get_abaci_version(tenant):
    es = Elasticsearch()

    index = ".abaci"
    query = {
        "size": 1,
        "_source": ["version"],
        "query": {
            "bool": {
                "must_not": {"exists": {"field": "&expiry"}},
                "filter": [
                    {"term": {"&model": "Schema"}},
                    {"term": {"&id": tenant}},
                ],
            }
        },
    }
    response = es.search(index=index, body=query)
    try:
        version = response["hits"]["hits"][0]["_source"]["version"]
    except KeyError:
        version = None
    return version


def get_tenant_models(tenant):

    es = Elasticsearch()

    # As we use multi-type on index level with ES5, there is no direct mapping
    # between index and mapping, so we need to query ES for the index name first
    indices = es.search(
        f"{tenant}:*",
        body={
            "size": 0,
            "aggs": {"Models": {"terms": {"field": "&model", "size": 10000}}},
        },
    )
    return [model["key"] for model in indices["aggregations"]["Models"]["buckets"]]


def get_tenant_schema(tenant, model):

    es = Elasticsearch()

    # As we use multi-type on index level with ES5, there is no direct mapping
    # between index and mapping, so we need to query ES for the index name first
    indices = es.search(
        f"{tenant}:*",
        body={
            "size": 0,
            "query": {"bool": {"filter": [{"term": {"&model": model}}]}},
            "aggs": {"Indices": {"terms": {"field": "_index", "size": 10000}}},
        },
    )

    if indices["hits"]["total"] == 0:
        raise ValueError(f"Invalid model for tenant: {tenant}:{model}")

    index = indices["aggregations"]["Indices"]["buckets"][0]["key"]

    raw_data = es.indices.get_mapping(index)

    return raw_data[list(raw_data.keys())[0]]["mappings"][model]["properties"]


def schema_versions(non_dev=False):
    """
    Gets all the schema versions that are published on the s3

    Accepts a parameter to filter out any version with dev/rc versions out
    """
    client = boto3.client("s3")

    paginator = client.get_paginator("list_objects_v2")
    response_iterator = paginator.paginate(
        Bucket=SCHEMA_BUCKET,
        Prefix="elasticsearch/",
        PaginationConfig={"PageSize": 1000},
    )
    items = [j["Key"] for i in response_iterator for j in i["Contents"]]
    versions = set()
    for item in items:
        value = item.split("/")
        try:
            v = parse(value[1])
        except ValueError as e:
            continue
        versions.add(value[1])

    if non_dev:
        versions = (
            version
            for version in versions
            if "dev" not in version and "rc" not in version
        )

    return sorted(versions, key=functools.cmp_to_key(compare))


if __name__ == "__main__":

    tenant = "pinafore"

    schemas = schema_versions(non_dev=True)

    # diff = get_schema_diff(schemas[len(schemas) - 2], schemas[len(schemas) - 1])

    # print("")
    # print("Added")
    # print(diff["dictionary_item_added"])
    # print("")
    # print("Removed")
    # print(diff["dictionary_item_removed"])
    # print("")
    # print("Changed values")
    # print(diff["values_changed"])
    # print("")

    all_models = get_tenant_models(tenant)

    tenant_schema_version = get_abaci_version(tenant)

    if not tenant_schema_version:
        raise ValueError(f"Schema version not found for tenant {tenant}")

    es = Elasticsearch()

    needle = schemas.index(tenant_schema_version)
    versions = schemas  # [needle - 10 : needle + 10]

    the_diff = None

    for version in versions:

        try:
            schema_account_firm = get_schema(tenant_schema_version)["AccountFirm"]
        except:
            print(f"Reading schema {version} failed")
            continue

        print(f"Check between pinafore and schema {version} for model AccountFirm")
        pinafore_account_firm = get_tenant_schema(tenant, "AccountFirm")

        diff = DeepDiff(
            pinafore_account_firm,
            schema_account_firm,
            ignore_order=False,
            verbose_level=1,
            ignore_string_case=True,
        )

        change = DeepDiff(the_diff, diff)

        print("")
        print("Added")
        print(diff["dictionary_item_added"])
        print("")
        print("Removed")
        print(diff["dictionary_item_removed"])
        print("")
        print("Changed values")
        print(diff["values_changed"])
        print("")

    pass

# Fill by Fill is turned off for caxton. We aggregate all FILLs and PARFs to a single record.
taskOverrides:
  all_environments:
    - name: FillByFillCheck
      params:
        fill_by_fill_flag: false
        initial_quantity: priceFormingData.initialQuantity
        remaining_quantity: _orderState.priceFormingData.remainingQuantity
        fill_quantity: _orderState.priceFormingData.tradedQuantity
        order_id: _order.id
        execution_price: __last_px__
        order_status: _orderState.executionDetails.orderStatus
        trading_date_time: _orderState.transactionDetails.tradingDateTime
        route_id: _order.id

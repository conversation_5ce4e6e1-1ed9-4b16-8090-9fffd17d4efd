id: order-feed-eze-eclipse
name: Order Feed EZE Eclipse
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  mapped: true
  merge: false
tasks:
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.nested_json_file_splitter:NestedJsonFileSplitter
  name: NestedJsonFileSplitter
  params:
    chunksize: 1000
    unstacked_columns_name_format: "fully_qualified_no_symbols"
    drop_source_index: false
    columns_to_unstack:
      - Routes
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: extractor_result
- path: swarm_tasks.io.read.batch_producer:BatchProducer
  name: BatchProducer
  params:
    source_schema:
      "Allocations": string
      "AssetClass": string
      "AssetType": string
      "BloombergID": string
      "CreateDateTime": string
      "CreateDateTime_x": string
      "CounterpartyCode": string
      "DestinationDisplayName": string
      "Executions": string
      "ExecutionPrice": float
      "ExpirationDate": string
      "ID": string
      "ID_x": string
      "ID_y": string
      "ISIN": string
      "Issuer": string
      "LastModifiedDateTime": string
      "LastModifiedDateTime_x": string
      "Limit": string
      "Limit_x": string
      "LimitPrice": float
      "LimitPrice_x": float
      "LimitPrice_y": float
      "LocalCurrency": string
      "Note": string
      "OccCode": string
      "OriginalOrderID": string
      "PriceMultiplier": float
      "Quantity": float
      "Quantity_x": float
      "Routes": string
      "SecurityName": string
      "Side": string
      "StrikePrice": float
      "Symbol": string
      "Trader": string
      "UnderlyingISIN": string
      "UnderlyingSymbol": string
  upstreamTasks:
    - taskName: NestedJsonFileSplitter
      mapped: true
      flatten: true
      key: file_splitter_result
   #  Primary transformations
- path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
  name: PrimaryTransformations
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
# Link Parties
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkParties
  params:
    identifiers_path: marketIdentifiers.parties
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: result
# Parties Fallback
- path: swarm_tasks.order.generic.parties_fallback:PartiesFallback
  name: PartiesFallback
  resources:
    es_client_key: tenant-data
  params:
    executing_entity_attribute: __TENANT_LEI__
    trader_attribute: __AGGREGATED_TRADER__
    client_attribute: __AGGREGATED_PORTFOLIO_NAME__
    counterparty_attribute: __COUNTERPARTY__
    investment_decision_maker_attribute: __AGGREGATED_INVESTMENT_DECISION_WITHIN_FIRM__
    execution_within_firm_attribute: __EXECUTION_WITHIN_FIRM__
    use_buy_mask_for_buyer_seller: true
    buy_sell_side_attribute: transactionDetails.buySellIndicator
    buyer_attribute: __TENANT_LEI__
    seller_attribute: __COUNTERPARTY__
  upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: result
    - taskName: LinkParties
      mapped: true
      key: link_parties_result
# Link Instrument
- path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
  name: LinkInstrument
  params:
    identifiers_path: marketIdentifiers.instrument
    venue_attribute: transactionDetails.ultimateVenue
    currency_attribute: transactionDetails.priceCurrency
  resources:
    ref_data_key: reference-data
    tenant_data_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: result
# Instrument Fallback
- path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
  name: InstrumentFallback
  params:
    instrument_fields_map:
      # Derivative
      - source_field: transactionDetails.priceCurrency
        target_field: derivative.strikePriceCurrency
      - source_field: PriceMultiplier
        target_field: derivative.priceMultiplier
      # Ext
      - source_field: __INST_FB_EXT_BEST_EX_ASSET_CLS_MAIN__
        target_field: ext.bestExAssetClassMain
      - source_field: __INST_FB_EXT_BEST_EX_ASSET_CLS_SUB__
        target_field: ext.bestExAssetClassSub
      - source_field: __INST_FB_EXCH_ROOT_SYMBOL__
        target_field: ext.exchangeSymbolRoot
      - source_field: __INST_FB_INST_UNIQUE_ID__
        target_field: ext.alternativeInstrumentIdentifier
      - source_field: __INST_FB_INST_UNIQUE_ID__
        target_field: ext.instrumentUniqueIdentifier
      # Top-level fields
      - source_field: __INST_FB_INST_ID_CODE__
        target_field: instrumentIdCode
      - source_field: __INST_FB_INST_FULL_NAME__
        target_field: instrumentFullName
      - source_field: __INST_FB_IS_CREATED_THRU_FB__
        target_field: isCreatedThroughFallback
      - source_field: transactionDetails.priceCurrency
        target_field: notionalCurrency1
    str_to_bool_dict:
      "on": True
      "off": False
  upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: result
    - taskName: LinkInstrument
      mapped: true
      key: link_instrument
# Link each OrderState record to the parent Order record
- path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
  name: AssignMetaParent
  params:
      parent_model_attribute: _order.__meta_model__
      parent_attributes_prefix: _order.
      target_attribute: _orderState.__meta_parent__
  upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: result

# Auxiliary frame concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: AuxiliaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - marketIdentifiers.instrument
      - marketIdentifiers.parties
      - PriceMultiplier
      - __AGGREGATED_DESTINATION_DISPLAY_NAME_WITH_PREFIX__
      - __AGGREGATED_DESTINATION_DISPLAY_NAME__
      - __AGGREGATED_PORTFOLIO_NAME_WITH_PREFIX__
      - __AGGREGATED_PORTFOLIO_NAME__
      - __AGGREGATED_QTY_EXECUTIONS__
      - __AGGREGATED_TRADER_WITH_PREFIX__
      - __AGGREGATED_TRADER__
      - __AGGREGATED_INVESTMENT_DECISION_WITHIN_FIRM__
      - __AGGREGATED_INVESTMENT_DECISION_WITHIN_FIRM_WITH_PREFIX__
      - __ASSET_CLASS__
      - __CREATE_DATETIME__
      - __COUNTERPARTY__
      - __EXEC_DATE_TIME__
      - __EXEC_VENUE__
      - __EXPIRY_DATE_EXP_DATE__
      - __EXPIRY_DATE_OCC_CODE__
      - __EXPIRY_DATE_SYMBOL__
      - __EXPIRY_DATE__
      - __INST_FB_EXCH_ROOT_SYMBOL__
      - __INST_FB_EXT_BEST_EX_ASSET_CLS_MAIN__
      - __INST_FB_EXT_BEST_EX_ASSET_CLS_SUB__
      - __INST_FB_INST_FULL_NAME__
      - __INST_FB_IS_CREATED_THRU_FB__
      - __OCC_CODE__
      - __OPTION_TYPE__
      - __STRIKE_PRICE_OCC_CODE__
      - __STRIKE_PRICE__
      - __TENANT_LEI__
      - __UNDERLYING_SYMBOL__
      - __EXECUTION_WITHIN_FIRM__
      - __INST_FB_INST_UNIQUE_ID__
      - __LIMIT_PRICE__
      - __ORDER_SUBMITTED_DT__
      - __INST_FB_INST_ID_CODE__
      - asset_class_attribute
      - bbg_figi_id_attribute
      - venue_attribute
      - currency_attribute
      - isin_attribute
      - expiry_date_attribute
      - option_strike_price_attribute
      - option_type_attribute
      - underlying_symbol_attribute
      - notional_currency_2_attribute
      - isin_attribute
      - currency_attribute
  upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: primary_transformations
    - taskName: PartiesFallback
      mapped: true
      key: party_fallback
    - taskName: InstrumentFallback
      mapped: true
      key: instrument_fallback
    - taskName: AssignMetaParent
      mapped: true
      key: parent_id

# Filter only Order records into a data frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderRecords
  params:
    except_prefix: _orderState.
    strip_prefix: true
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenator
    mapped: true
    key: result
# Filter only OrderState records into a data frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderStateRecords
  params:
    except_prefix: _order.
    strip_prefix: true
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenator
    mapped: true
    key: result
# Strip prefix `_order.` from the column names of OrderRecords frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrder
  params:
    action: strip
    prefix: _order.
  upstreamTasks:
  - taskName: OrderRecords
    mapped: true
    key: result
# Strip prefix `_orderState.` from the column names of OrderState Records frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrderState
  params:
    action: strip
    prefix: _orderState.
  upstreamTasks:
  - taskName: OrderStateRecords
    mapped: true
    key: result
# Vertically concatenate Order and OrderState Record frames created above.
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: VerticalConcatenator
  params:
    orient: vertical
    reset_index: true
    drop_index: true
  upstreamTasks:
  - taskName: StripPrefixOrder
    mapped: true
    key: order_records
  - taskName: StripPrefixOrderState
    mapped: true
    key: order_state_records

# Remove InvalidOrderStates
# Removes orderStates
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: RemoveInvalidOrderStates
  params:
    query: "`executionDetails.orderStatus`.str.fullmatch('NEWO', case=False, na=False) | ((`executionDetails.orderStatus`.str.fullmatch('PARF', case=False, na=False)) & (`__ONLY_NEWO__`.astype('str').str.fullmatch('False', case=False, na=False)))"
  upstreamTasks:
  - taskName: VerticalConcatenator
    mapped: true
    key: result
# Remove duplicate new orders, remove synthetic new orders already in Elastic
- path: swarm_tasks.order.generic.remove_duplicate_newo:RemoveDuplicateNEWO
  name: RemoveDupNEWO
  params:
    newo_in_file_col: __NEW_O_COL_IN_FILE__
    drop_newo_in_file_col: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: RemoveInvalidOrderStates
    mapped: true
    key: result

#Best Execution
- path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
  name: BestExecution
  resources:
    es_client_key: reference-data
  upstreamTasks:
    - taskName: RemoveDupNEWO
      mapped: true
      key: result
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: BestExecutionConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __ONLY_NEWO__
  upstreamTasks:
    - taskName: RemoveDupNEWO
      mapped: true
      key: result
    - taskName: BestExecution
      mapped: true
      key: best_ex_result
# Assign Meta
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: AssignMeta
  params:
    model_attribute: __meta_model__
    parent_attribute: __meta_parent__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result

# Post-meta concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PostMetaConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
      - __meta_parent__
  upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
    - taskName: AssignMeta
      mapped: true
      key: meta

- path: swarm_tasks.generic.extract_batch_from_frame_producer_result:ExtractBatchFromFrameProducerResult
  name: ExtractBatchFromFrameProducerResult
  params:
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: frame_producer_result

- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: ExtractBatchFromFrameProducerResult
      mapped: true
      key: batch_index
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsent
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: ElasticBulkTransformer
      mapped: true
      key: result
# Instrument Mapper
- path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
  name: InstrumentMapper
  upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: source_frame
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: ExtractBatchFromFrameProducerResult
      mapped: true
      key: batch_index
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsent
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: QuarantinedElasticBulkTransformer
      mapped: true
      key: result
- path: swarm_tasks.control_flow.noop:Noop
  name: Noop

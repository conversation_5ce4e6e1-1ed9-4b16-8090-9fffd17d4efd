id: order-feed-aladdin-controller
name: Order Feed Aladdin Controller
infra:
  - name: tenant-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
tasks:
  - path: swarm_tasks.control_flow.multiple_files_input_controller:MultipleFilesInputController
    name: MultipleFilesInputController
    params:
      list_of_files_regex:
        - ".*((FI|DERIV|EQ|FX)\\.OrderDetail\\.).*"
        - ".*((FI|DERIV|EQ|FX)\\.Order\\.).*"
        - ".*((FI|DERIV|EQ|FX)\\.Transaction\\.).*"
        - ".*((FI|DERIV|EQ|FX)\\.Placement\\.).*"
        - ".*((FI|DERIV|EQ|FX)\\.Fill\\.).*"
      unique_identifier_regex:
        - "\\d{4}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])" # i.e "20220226 - YYYYMMDD"
        - {"regex": ".*(FI|DERIV|EQ|FX)", "start_index": 3, "stop_index": 19}  # Matched Asset Class like EQ, FX, FI, DERIV
      list_of_files_regex_for_which_empty_frame_returned:
        - ".*\\_BETA\\..*"  # Ignore and skip files with "_BETA." in the name.
        - ".*\\.Employee\\..*"
        - ".*\\.Broker\\..*"
        - ".*\\.PlacementAlgo\\..*"
        - ".*\\.Portfolio\\..*"
        - ".*\\.Quote\\..*"
        - ".*SMFPriceMultiplier\\..*"
        - ".*\\.OrderCustom\\..*"  # Ignore and skip files with ".OrderCustom"
        - ".*\\.TradeCustom\\..*"  # Ignore and skip files with ".TradeCustom"
      file_links_in_output: true
    upstreamTasks:
      - taskName: file_url
        key: file_url

  # Pre Process data by reading all the files present in S3 for the flow run
  - path: swarm_tasks.io.read.aws.s3_download_multiple_files:S3DownloadMultipleFiles
    name: DownloadAllFiles
    params:
      suffix_identifier_regex: s3_[a-z]+.(.*)._file_url
    upstreamTasks:
      - taskName: MultipleFilesInputController
        mapped: false
        key: producer_result

  # Merge the different frames for aladdin orders
  - path: swarm_tasks.order.feed.aladdin.aladdin_order_frame_merger:AladdinOrderFrameMerger
    name: AladdinOrderFrameMerger
    upstreamTasks:
      - taskName: DownloadAllFiles
        mapped: false
        key: pre_process_result
      - taskName: file_url
        key: file_url

  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvFileSplitter
    params:
      chunksize: 25000
      include_file_name_in_batches: true
    upstreamTasks:
      - taskName: AladdinOrderFrameMerger
        mapped: false
        key: extractor_result

  - path: swarm_tasks.transform.extract_path.s3_file_list_from_frame_splitter_result_list:S3FileListFileSplitterResultList
    name: S3FileListFileSplitterResultList
    params:
      cloud_key_prefix: "flows/order-feed-aladdin-processor"
    upstreamTasks:
      - taskName: CsvFileSplitter
        mapped: false
        key: file_splitter_result_list

  - path: swarm_tasks.io.write.aws.s3_upload_file:S3UploadFile
    name: S3UploadFile
    upstreamTasks:
      - taskName: S3FileListFileSplitterResultList
        mapped: true
        key: upload_target
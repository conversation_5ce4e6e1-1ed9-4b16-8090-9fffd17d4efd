import os


class InstrumentModels:
    ANNA_DSB_INSTRUMENT = "AnnaDsbInstrument"
    CFD_INSTRUMENT = "CfdInstrument"
    FCA_FIRDS_INSTRUMENT = "FcaFirdsInstrument"
    FIRDS_INSTRUMENT = "FirdsInstrument"
    INSTRUMENT = "Instrument"
    SEDOL_INSTRUMENT = "SedolInstrument"
    STEELEYE_INSTRUMENT = "SteelEyeInstrument"
    VENUE_DIRECT_INSTRUMENT = "VenueDirectInstrument"
    VENUE_INSTRUMENT = "VenueInstrument"


MODELS_PREFERENCE_RANK = [
    InstrumentModels.FCA_FIRDS_INSTRUMENT,
    InstrumentModels.VENUE_DIRECT_INSTRUMENT,
    InstrumentModels.VENUE_INSTRUMENT,
    InstrumentModels.FIRDS_INSTRUMENT,
    InstrumentModels.ANNA_DSB_INSTRUMENT,
    InstrumentModels.SEDOL_INSTRUMENT,
    InstrumentModels.CFD_INSTRUMENT,
    InstrumentModels.STEELEYE_INSTRUMENT,
    InstrumentModels.INSTRUMENT,
]
VENUE_PREFERENCE_RANK = [
    "XLON",
    "ARCX",
    "FRAB",
    "BATS",
    "XPAR",
    "360T",
    "BGCO",
    "FXRQ",
    "GFBO",
]
INSTRUMENT_INDEX = "instrument"

INSTRUMENT_CACHE_EXPIRY = int(os.environ.get("INSTRUMENT_CACHE_EXPIRY", 2592000))
COGNITO_CLIENT_ID = os.environ.get("COGNITO_CLIENT_ID")
COGNITO_CLIENT_SECRET = os.environ.get("COGNITO_CLIENT_SECRET")
COGNITO_AUTH_URL = os.environ.get("COGNITO_AUTH_URL")


class InstrumentField:
    CFI_ATTRIBUTE_1 = "cfiAttribute1"
    CFI_ATTRIBUTE_2 = "cfiAttribute2"
    CFI_ATTRIBUTE_3 = "cfiAttribute3"
    CFI_ATTRIBUTE_4 = "cfiAttribute4"
    DERIVATIVE_IS_USER_DEFINED_SPREAD = "derivative.isUserDefinedSpread"
    INSTRUMENT_CLASSIFICATION = "instrumentClassification"
    INSTRUMENT_ID_CODE = "instrumentIdCode"
    NOTIONAL_CURRENCY_1 = "notionalCurrency1"
    TRADING_VENUE = "venue.tradingVenue"


class CfiAttribute1:
    SPOT = "Spot"
    SPOT_FORWARD_SWAP = "Spot-Forward swap"


class CfiAttribute2:
    CONSTANT = "Constant"
    EUROPEAN_PUT = "European Put"
    EUROPEAN_CHOOSER = "European Chooser"
    EUROPEAN_CALL = "European Call"


class CfiAttribute3:
    VANILLA = "Vanilla"


class CfiAttribute4:
    AUCTION = "Auction"
    CASH = "Cash"
    PHYSICAL = "Physical"


class DFColumns:
    CACHE_INDEX = "cache_index"

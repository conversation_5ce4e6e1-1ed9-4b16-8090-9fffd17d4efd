from pathlib import Path

import pandas as pd

from se_trades_tasks.order.party.generic_order_party_identifiers import (
    run_generic_order_party_identifiers,
    Params as ParamsGenericOrderPartyIdentifiers,
)
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.transformations.universal.steeleye_universal_order_blotter_transformations import (
    SteeleyeUniversalOrderBlotterTransformations,
)
from se_trades_tasks.order.transformations.universal import steeleye_universal_order_blotter_schema as col
from se_core_tasks.feeds.generic.get_tenant_lei import run_get_tenant_lei
from se_core_tasks.feeds.generic.get_tenant_lei import Params as ParamsGetTenantLEI

from se_trades_tasks.order_and_tr.static import PartyPrefix


class ThornbridgeTempColumns:
    ACCOUNT_FIRM_LEI_WITH_PREFIX = "__account_firm_with_lei__"
    CLIENT_ID_TRADTHO = "__client_id_tradtho__"


class ThornbridgeSteeleyeUniversalOrderBlotterTransformations(SteeleyeUniversalOrderBlotterTransformations):
    """Override SteeleyeUniversalOrderBlotterTransformations class to add Thornbrige-specific
    logic.
    This also includes the 'Universum DMA' feed's logic, where there is a separate logic based on
    the file name.
    """

    def _data_source_name(self) -> pd.DataFrame:
        if not Path(self.input_file_path).name.lower().startswith("dma"):
            # As this is not a DMA file, call the main Order blotter class's
            # _market_identifiers_parties
            return super()._data_source_name()

        return pd.DataFrame(
            data="Thornbridge DMA",
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """
        Must run after "transactionDetails.buySellIndicator" and "transactionDetails.tradingCapacity"
        """

        if not Path(self.input_file_path).name.lower().startswith("dma"):
            # As this is not a DMA file, call the main Order blotter class's
            # _market_identifiers_parties
            return super()._market_identifiers_parties()

        # For Thornbridge DMA files, we have different mappings for parties

        # Get the part before / for the client id. E.g. for 59700/10047USD3,
        # and append TRADTHO to it (with a slash in between).
        # For this example, the final value should be 59700/TRADTHO
        client_id_tradtho_df: pd.Series = (
            self.source_frame.loc[:, col.CLIENT_ID.normalized_column_name]
            .astype(str)
            .str.split("/")
            .str[0]
            .rename(ThornbridgeTempColumns.CLIENT_ID_TRADTHO)
        )
        client_id_tradtho_df = client_id_tradtho_df + "/TRADTHO"

        return run_generic_order_party_identifiers(
            source_frame=pd.concat(
                [
                    client_id_tradtho_df.to_frame(),
                    self.source_frame.loc[
                        :,
                        [
                            col.TRADER_ID.normalized_column_name,
                            col.CLIENT_ID.normalized_column_name,
                        ],
                    ],
                    run_get_tenant_lei(
                        source_frame=self.source_frame,
                        params=ParamsGetTenantLEI(
                            target_lei_column=ThornbridgeTempColumns.ACCOUNT_FIRM_LEI_WITH_PREFIX,
                            target_column_prefix=PartyPrefix.LEI,
                        ),
                        tenant=self.tenant,
                        es_client=self.es_client,
                    ),
                    self.target_df.loc[:, [OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR]],
                ],
                axis=1,
            ),
            params=ParamsGenericOrderPartyIdentifiers(
                executing_entity_identifier=ThornbridgeTempColumns.ACCOUNT_FIRM_LEI_WITH_PREFIX,
                trader_identifier=col.TRADER_ID.normalized_column_name,
                client_identifier=col.CLIENT_ID.normalized_column_name,
                counterparty_identifier=ThornbridgeTempColumns.CLIENT_ID_TRADTHO,
                buyer_identifier=col.CLIENT_ID.normalized_column_name,
                seller_identifier=ThornbridgeTempColumns.CLIENT_ID_TRADTHO,
                buyer_decision_maker_identifier=ThornbridgeTempColumns.ACCOUNT_FIRM_LEI_WITH_PREFIX,
                buy_sell_side_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller=True,
                use_buy_mask_for_buyer_seller_decision_maker=True,
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                create_fallback_fields=True,
            ),
        )

#!/usr/bin/env python3
"""
Test script to verify the dynamic directory selection functionality in KervPoller.
This script demonstrates how the _get_remote_directory method works with different configurations.
"""

import sys
import os
from unittest.mock import Mock, patch
from datetime import datetime

# Add the path to import the modules
sys.path.insert(0, 'src/py/libs/integration/comms/se-comms-ingress-utils')
sys.path.insert(0, 'src/py/tasks/integration/integration-poller-tasks')

from se_comms_ingress_utils.abstractions.kerv_poller import AbstractKervPoller, InputParams, KERV_REMOTE_DIR, KERV_ROOT_REMOTE_DIR
from aries_task_link.models import AriesTaskInput
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet


class TestKervPoller(AbstractKervPoller):
    """Test implementation of AbstractKervPoller for testing purposes."""
    
    def run_poller(self):
        return None
    
    def _get_date_wise_files(self, all_sftp_files, poll_from_date, poll_to_date):
        return {}


def create_test_input(remote_directory_type=None):
    """Create a test AriesTaskInput with optional remote_directory_type."""
    workflow = WorkflowFieldSet(
        name="test_kerv_poll",
        stack="dev-test",
        tenant="test_tenant",
        start_timestamp=datetime.now(),
    )
    
    params = {
        "look_back_days": 3,
        "force_pull": False,
    }
    
    if remote_directory_type is not None:
        params["remote_directory_type"] = remote_directory_type
    
    input_param = IOParamFieldSet(params=params)
    task = TaskFieldSet(name="test", version="latest", success=False)
    
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


def test_directory_selection():
    """Test the directory selection logic using map-based implementation."""
    print("Testing KervPoller dynamic directory selection (map-based)...")

    # Mock the config and other dependencies
    mock_config = Mock()
    
    # Test 1: Default behavior (no remote_directory_type specified)
    print("\n1. Testing default behavior (no remote_directory_type):")
    test_input = create_test_input()
    
    with patch('se_comms_ingress_utils.abstractions.kerv_poller.get_secrets'), \
         patch('se_comms_ingress_utils.abstractions.kerv_poller.secrets_client'), \
         patch('se_comms_ingress_utils.abstractions.kerv_poller.CompatibleTenantWorkflowAPIClient'), \
         patch('se_comms_ingress_utils.abstractions.kerv_poller.get_filesystem'), \
         patch('se_comms_ingress_utils.abstractions.kerv_poller.get_sftp_fs_from_addict'):
        
        poller = TestKervPoller("test_workflow", test_input, mock_config)
        remote_dir = poller._get_remote_directory()
        print(f"   Expected: {KERV_REMOTE_DIR}")
        print(f"   Actual: {remote_dir}")
        print(f"   ✓ PASS" if remote_dir == KERV_REMOTE_DIR else f"   ✗ FAIL")
    
    # Test 2: Explicitly set to "recordings"
    print("\n2. Testing remote_directory_type='recordings':")
    test_input = create_test_input("recordings")
    
    with patch('se_comms_ingress_utils.abstractions.kerv_poller.get_secrets'), \
         patch('se_comms_ingress_utils.abstractions.kerv_poller.secrets_client'), \
         patch('se_comms_ingress_utils.abstractions.kerv_poller.CompatibleTenantWorkflowAPIClient'), \
         patch('se_comms_ingress_utils.abstractions.kerv_poller.get_filesystem'), \
         patch('se_comms_ingress_utils.abstractions.kerv_poller.get_sftp_fs_from_addict'):
        
        poller = TestKervPoller("test_workflow", test_input, mock_config)
        remote_dir = poller._get_remote_directory()
        print(f"   Expected: {KERV_REMOTE_DIR}")
        print(f"   Actual: {remote_dir}")
        print(f"   ✓ PASS" if remote_dir == KERV_REMOTE_DIR else f"   ✗ FAIL")
    
    # Test 3: Set to "root"
    print("\n3. Testing remote_directory_type='root':")
    test_input = create_test_input("root")
    
    with patch('se_comms_ingress_utils.abstractions.kerv_poller.get_secrets'), \
         patch('se_comms_ingress_utils.abstractions.kerv_poller.secrets_client'), \
         patch('se_comms_ingress_utils.abstractions.kerv_poller.CompatibleTenantWorkflowAPIClient'), \
         patch('se_comms_ingress_utils.abstractions.kerv_poller.get_filesystem'), \
         patch('se_comms_ingress_utils.abstractions.kerv_poller.get_sftp_fs_from_addict'):
        
        poller = TestKervPoller("test_workflow", test_input, mock_config)
        remote_dir = poller._get_remote_directory()
        print(f"   Expected: {KERV_ROOT_REMOTE_DIR}")
        print(f"   Actual: {remote_dir}")
        print(f"   ✓ PASS" if remote_dir == KERV_ROOT_REMOTE_DIR else f"   ✗ FAIL")
    
    print("\n4. Testing InputParams validation:")
    # Test that InputParams accepts the new field
    try:
        params1 = InputParams(remote_directory_type="recordings")
        params2 = InputParams(remote_directory_type="root")
        params3 = InputParams()  # Should work without the field
        print("   ✓ PASS - InputParams validation works correctly")
    except Exception as e:
        print(f"   ✗ FAIL - InputParams validation failed: {e}")
    
    print("\nTesting complete!")


if __name__ == "__main__":
    test_directory_selection()

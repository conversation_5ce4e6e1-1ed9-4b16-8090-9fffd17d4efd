PREFECT__FLOWS__CHECKPOINTING=true

STACK=uat-shared-steeleye
DEV=true
DEBUG=false
MAX_PREFECT_TASKS=4
AWS_PROFILE=nonprod_power
KAFKA_SECURITY_PROTOCOL=PLAINTEXT
ELASTIC_HOST=elasticsearch.uat-shared-steeleye.steeleye.co
ELASTIC_PORT=443
ELASTIC_SCHEME=https
ELASTIC_API_KEY=akEyZUs1QUIyenJaWEo4MV9oX1Y6LXhwdU5lVlZSLW1rQWk4LVVBQnVSQQ==

SWARM_FILE_URL=/Users/<USER>/Downloads/1721727550_378053.csv
SWARM_LOCAL_FLOW_ID=ben.uat.steeleye.co:order-feed-tt-fix

OBD_DATA_STORAGE_BUCKET=s3://master-data.eu-west-1.steeleye.co
COGNITO_AUTH_URL=https://prod-master-data.auth.eu-west-1.amazoncognito.com/oauth2/token
COGNITO_CLIENT_ID=1c7hapqudk6ab5npoubfttc0ma
COGNITO_CLIENT_SECRET=1lakej5lmkro0nv2nohuibnv83ki38qij2vrldlsv2enl4r85v3s
MARKET_DATA_API_URL=https://api.market-data.steeleye.co
MASTER_DATA_API_HOST=https://api.master-data.steeleye.co
MASTER_DATA_HOST=https://api.master-data.steeleye.co
INSTRUMENT_STORE_BUCKET=uat-srp.uat.steeleye.co
DEV_REGISTRY_PORT: '443'
SWARM_LOCAL_PORTS: '{elasticsearch.srp.steeleye.co: 9200}'
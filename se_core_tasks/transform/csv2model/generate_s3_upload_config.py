import logging
import os
import uuid
from enum import Enum
from pathlib import Path
from typing import Any
from typing import List
from typing import Optional

from pydantic import Field
from se_elastic_schema import models

from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.core.core_dataclasses import S3Action
from se_core_tasks.core.core_dataclasses import S3File
from se_core_tasks.core.exception import TaskException
from se_core_tasks.core.params import TaskParams
from se_core_tasks.core.task import Task
from se_core_tasks.utils.task_utils import match_enum_value

_logger = logging.getLogger(__name__)


class Params(TaskParams):
    file_suffix: Optional[str] = Field(
        None,
        description="File suffix used to extract a subset of the ExtractPathResult list. The resulting"
        "S3 file list will only contain this subset of files. ",
    )
    s3_key_prefix: str = Field(
        ..., description="S3 Prefix where the file will be uploaded to"
    )
    bucket_name: str = Field(
        ..., description="Bucket where the file will be uploaded to"
    )


class SkipIfExtractPathResultEmpty(TaskException):
    pass


class FailIfModelNotResolved(TaskException):
    pass


class GenerateCsv2ModelS3UploadConfig(Task):
    """
    Creates a list of S3 Files from a list of ExtractPathResult. There is an optional file
    extension parameter, which filters the ExtractPathResult before creating the S3 file
    list. The resultant s3 key will have the batch no prefixed.
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        extract_path_result_list: List[ExtractPathResult] = None,
        file_url: str = None,
        **kwargs,
    ) -> Optional[List[S3File]]:
        return self.process(
            params=params,
            extract_path_result_list=extract_path_result_list,
            file_url=file_url,
        )

    @classmethod
    def process(
        cls,
        params: Optional[Params] = None,
        extract_path_result_list: List[ExtractPathResult] = None,
        file_url: str = None,
        **kwargs,
    ):
        if not extract_path_result_list:
            raise SkipIfExtractPathResultEmpty(
                "No ExtractPathResult elements in extract_path_result_list"
            )

        # Resolving the model name from file_url.
        # Expectation is that the source files will be uploaded to individual Model folders
        # Removing 'flows' substring from file_url as otherwise it will match to Flow model in all_schema
        file_immediate_folder_path = str(Path(file_url).parent).replace("flows", "")
        all_models_enum = Enum(
            "all_models_enum",
            {
                k: k
                for k, v in models.__dict__.items()
                if not k.startswith("__")
                and k
                not in [
                    "se_elastic_schema",
                    "find_model",
                    "importlib",
                    "reference",
                    "tenant",
                ]
            },
        )
        resolved_model = match_enum_value(file_immediate_folder_path, all_models_enum)
        if not resolved_model:
            raise FailIfModelNotResolved(
                f"Model not found / is not supported."
                f"\nDebug info:\n Path: {file_url} \n resolved model: {resolved_model}"
            )

        if not isinstance(extract_path_result_list, list):
            extract_path_result_list = [extract_path_result_list]

        required_file_list = list()
        for extract_result_path in extract_path_result_list:
            # If path in the ExtractPathResult obj is a directory, process all files within
            # it which matches the file_suffix
            if extract_result_path.path.is_dir():
                for root, directory_names, file_names in os.walk(
                    extract_result_path.path
                ):
                    for f in file_names:
                        if (
                            params.file_suffix
                            and extract_result_path.path.suffix != params.file_suffix
                        ):
                            continue
                        output_file = Path(root).joinpath(f)
                        required_file_list.append(output_file)
            else:
                if (
                    params.file_suffix
                    and extract_result_path.path.suffix != params.file_suffix
                ):
                    continue

                required_file_list.append(extract_result_path.path)

        if not required_file_list:
            raise SkipIfExtractPathResultEmpty("No files exist to create S3Files from")

        s3_file_list = list()
        for extract_result_path in required_file_list:
            identifier = str(uuid.uuid4())
            file_path_suffix = (
                f"{Path(file_url).stem}_{identifier}_{extract_result_path.name}"
            )
            file_path_prefix = Path(
                params.s3_key_prefix,
                resolved_model.value,
            )

            # Final path format is:
            # s3_key/<model>/<input_file_name>_uuid_target_<batch_number>.ndjson
            s3_key = Path(file_path_prefix, file_path_suffix).as_posix()
            s3_file = S3File(
                file_path=extract_result_path,
                bucket_name=params.bucket_name,
                key_name=s3_key,
                action=S3Action.UPLOAD,
            )
            s3_file_list.append(s3_file)

        return s3_file_list


def run_generate_csv2model_s3_upload_config(
    params: Optional[Params] = None,
    extract_path_result_list: List[ExtractPathResult] = None,
    file_url: str = None,
    auditor: Any = None,
    **kwargs,
) -> Optional[List[S3File]]:

    task = GenerateCsv2ModelS3UploadConfig(auditor=auditor)
    return task.execute(
        extract_path_result_list=extract_path_result_list,
        file_url=file_url,
        params=params,
    )

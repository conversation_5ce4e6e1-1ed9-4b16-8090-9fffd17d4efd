import logging
from enum import Enum
from typing import Any
from typing import Callable
from typing import List
from typing import Optional
from typing import Union

import pandas as pd
from pydantic import Field
from pydantic import root_validator

from se_core_tasks.core.exception import TaskException
from se_core_tasks.core.params import TaskParams
from se_core_tasks.core.task import Task

logger_ = logging.getLogger(__name__)


class CastTo(str, Enum):
    # Numeric
    NUMERIC_ABSOLUTE = "numeric.absolute"
    INTEGER = "integer"
    FLOAT = "float"
    STRING = "string"

    # String
    STRING_LIST = "string.list"
    STRING_LOWER = "string.lower"
    STRING_UPPER = "string.upper"

    __map__ = {
        NUMERIC_ABSOLUTE: abs,
        INTEGER: int,
        FLOAT: float,
        STRING: str,
    }

    @classmethod
    def get_function(cls, value: str) -> Optional[Callable]:
        func = cls.__map__.get(value)

        return func


class Params(TaskParams):
    source_attribute: Optional[str] = Field(
        default=None, description="Name of source column"
    )
    source_attributes: Optional[List[str]] = Field(
        default=None, description="List of the names of the source columns"
    )
    mask: str = Field(
        default=None,
        description="This should follow the pandas syntax for pd.DataFrame.query.",
    )
    raise_on_missing_source_attribute: bool = Field(
        default=True,
        description="If True, if `source_attribute` or "
        "`source_attributes` are missing in "
        "`source_frame`, the KeyError is raised.",
    )
    target_attribute: str = Field(..., description="Name of the target column")
    strip_whitespace: Optional[bool] = Field(
        default=False,
        description="Flag for stripping whitespace from source data to the target column",
    )
    fill_nan: Optional[str] = Field(
        default=None, description="Fill the nulls in source with the value passed in."
    )
    convert_to_nan: Optional[List[str]] = Field(
        default=None,
        description="List of patterns - each source row that matches one of the patterns "
        "will be converted to pd.NA to the target column",
    )
    cast_to: Optional[CastTo] = Field(
        default=None, description="Cast source column to a different type"
    )
    list_delimiter: Optional[Union[str, List[str]]] = Field(
        default=None,
        description="Delimiter to be used to split a String Series into one with Lists of Strings - "
        "Can be a single delimiter or a list",
    )
    start_index: Optional[int] = Field(
        default=None,
        description="Start index to slice string content of source columns",
    )
    end_index: Optional[int] = Field(
        default=None, description="End index to slice string content of source columns"
    )
    prefix: Optional[str] = Field(
        default=None,
        description="Prefix to be added to the values of the source attribute (if populated).",
    )
    suffix: Optional[str] = Field(
        default=None,
        description="Suffix to be added to the values of the source attribute (if populated).",
    )
    join_sep: Optional[str] = Field(
        default=None,
        description="If populated, will be applied when the values are lists, "
        "therefore end result will be a string of those values separated by `join_sep` ",
    )

    @root_validator
    def check_existence_source_attribute_or_attributes(cls, values):

        # TODO: check presence of one or the other, they cannot co-exist
        if not values.get("source_attribute") and not values.get("source_attributes"):
            raise ValueError(
                "`source_attribute` or `source_attributes` must be populated."
            )

        return values

    @root_validator
    def cast_to_list_delimiter_present(cls, values):
        cast_to = values.get("cast_to")

        if (
            cast_to
            and cast_to == CastTo.STRING_LIST
            and not values.get("list_delimiter")
        ):
            raise ValueError(
                "`list_delimiter` has to be defined when `cast_to` is `list`"
            )

        return values


class FailIfMoreThanOneDelimiter(TaskException):
    pass


class MapAttribute(Task):
    """Task maps attribute(s) from a source dataframe to attribute(s) of a target dataframe.

    Examples:
        map: from source df column:  'Profit'
             to target df column:    'emirPosition.valuation.amount.native'

        map: from source df column:  'Deal'
             to target df column:    'emirPosition.identifiers.utiRaw'
             with prefix 'E02|FooBar|MT4|'

        map: from source df column:  'Deal'
             to target df column:    'emirUti.transaction.identifiers.transactionId'
             with suffix 'OPEN'

        ```
        - path: swarm_tasks.transform.map.map_attribute:MapAttribute
          name: MapAttribute
          paramsList:
          - source_attribute: Profit
            target_attribute: emirPosition.valuation.amount.native
            cast_to: numeric.absolute
          - source_attribute: Deal
            target_attribute: emirPosition.identifiers.utiRaw
            prefix: E02|FooBar|MT4|
          - source_attribute: Deal
            target_attribute: emirUti.transaction.identifiers.transactionId
            suffix: OPEN
        ```

    """

    params_class = Params

    def execute(
        self, source_frame: pd.DataFrame, params: Params, **kwargs
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        auditor: Any = None,
        **kwargs,
    ) -> pd.DataFrame:

        """Maps specified attributes to a new target dataframe.

        Takes a source dataframe and the attributes from `params` and then
        maps those to a new target dataframe.

        Args:
            source_frame (pd.DataFrame): source dataframe object.
            params (Params): attributes for the target dataframe specified here.
            auditor (Auditor): auditor for the current task.

        Returns:
            pd.DataFrame: target dataframe with mapped values.

        Raises:
            ValueError: if neither `params.source_attribute` or `params.source_attributes`
                is provided.
            NotImplementedError: if the `params.cast_to` refers to a function not implemented.
        """

        target = pd.DataFrame(index=source_frame.index)
        target[params.target_attribute] = pd.NA

        mask = pd.Series(True, index=source_frame.index)

        if params.mask is not None:
            mask = source_frame.eval(params.mask, engine="python")

        source_data = source_frame.loc[mask]

        data = cls._get_data_to_map(df=source_data, params=params, auditor=auditor)

        if data.isnull().all():
            target.loc[mask, params.target_attribute] = (
                data.fillna(params.fill_nan) if params.fill_nan else pd.NA
            )
            return target

        if params.fill_nan:
            data = data.fillna(params.fill_nan)

        if params.convert_to_nan:
            replace_by_null_mask = data.isin(params.convert_to_nan)
            data.loc[replace_by_null_mask] = pd.NA

        data = data.dropna()

        if data.empty:
            target.loc[mask, params.target_attribute] = data
            target = target.fillna(pd.NA)
            return target

        if params.strip_whitespace:
            data = data.astype("string").str.strip()

        if any(p is not None for p in [params.start_index, params.end_index]):
            data = data.str[params.start_index : params.end_index]

        if params.cast_to and params.cast_to == CastTo.STRING_LIST:
            data = cls._split_string_series_by_delimiter(
                data=data, delimiter=params.list_delimiter, auditor=auditor
            )
        elif params.cast_to:
            data = cls._cast_value_to(params.cast_to, data)

        if params.prefix:
            data = params.prefix + data

        if params.suffix:
            data = data + params.suffix

        if params.join_sep:
            data = cls._apply_join_sep(series=data, sep=params.join_sep)

        target.loc[mask, params.target_attribute] = data

        target = target.fillna(pd.NA)

        return target

    @staticmethod
    def _cast_value_to(target, data):
        """
        :param target: CastTo string
        :param data: pd.Series
        :return: casted pandas series
        """
        result = pd.Series(pd.NA, data.index)
        if target == CastTo.NUMERIC_ABSOLUTE:
            func = CastTo.get_function(target)

            if func is None:
                raise NotImplementedError(f"Cast to {target} not implemented.")
            result = data.loc[:].apply(func)

        elif target == CastTo.INTEGER:
            int_func = CastTo.get_function(target)
            float_func = CastTo.get_function(CastTo.FLOAT)

            if int_func is None and float_func is None:
                raise NotImplementedError(f"Cast to {target} not implemented.")

            result = data.loc[:].apply(float_func).apply(int_func)

        elif target == CastTo.STRING:
            func = CastTo.get_function(target)

            if func is None:
                raise NotImplementedError(f"Cast to {target} not implemented.")

            result = data.loc[:].apply(func)

        elif target == CastTo.STRING_LOWER:
            result = data.astype("string").str.lower()

        elif target == CastTo.STRING_UPPER:
            result = data.astype("string").str.upper()

        return result

    @staticmethod
    def _get_data_to_map(df: pd.DataFrame, params: Params, auditor: Any) -> pd.Series:
        """
        Gets the data series based on source_frame

        :param df:
        :param params:
        :return:
        """

        data = pd.Series(pd.NA, index=df.index)

        # Handle source_attributes
        if params.source_attributes:
            if not params.raise_on_missing_source_attribute:
                for col in params.source_attributes:
                    if col in df.columns:
                        continue
                    message = f"{col} not present in `source_frame"
                    logger_.warning(message)
                    auditor.add(message)
                    data[col] = pd.NA

            col_mask = df.columns.isin(params.source_attributes)
            source_attributes_data = df.loc[:, col_mask]

            if source_attributes_data.isnull().all(axis=1).all():
                return data

            data.loc[:] = source_attributes_data.dropna(how="all").apply(
                lambda x: x.dropna().tolist()[0], axis=1
            )

        # If source_attributes are not defined, source_attribute
        # is mandatory according to root validator in params
        else:
            if (
                params.source_attribute not in df.columns
                and not params.raise_on_missing_source_attribute
            ):
                message = f"{params.source_attribute} not present in `source_frame"
                logger_.warning(message)
                auditor.add(message)
                return data

            data = df[params.source_attribute]

        return data

    @staticmethod
    def _apply_join_sep(series: pd.Series, sep: str) -> pd.Series:
        list_mask = series.notnull() & series.map(lambda x: isinstance(x, list))

        if not list_mask.any():
            return series

        series.loc[list_mask] = series.loc[list_mask].map(
            lambda x: sep.join(x) if len(x) > 0 else pd.NA
        )

        return series

    @staticmethod
    def _split_string_series_by_delimiter(
        data: pd.Series, delimiter: Union[str, List[str]], auditor: Any
    ) -> pd.Series:
        """
        One can pass a single delimiter or a list of delimiters in case the data is inconsistent
        NOTE: It is expected for the same delimiter to be used across the whole source column
        If a single delimiter is passed, that will be used to split the data strings
        If a list of delimiters is passed, we will iterate over each delimiter to find which
        is the one present in the string Series to split its contents, and use that delimiter
        If no delimiters are present in the string Series we use the first one of the list
        which will have no impact on splitting the string Series
        i.e "Identifier1".split(";") -> ["Identifier1"]

        :param data: String Series where each element will be split into a list of strings
        :param delimiter: Delimiter by which to split each element of `data`
        :return: `data` Series with List of Strings as split by `delimiter`
        """

        delimiter_as_list = (
            [delimiter] if not isinstance(delimiter, list) else delimiter
        )

        delimiter_to_use = None

        for delim in delimiter_as_list:
            delimiter_in_data_mask = data.astype("string").str.contains(
                delim, regex=False
            )

            if delimiter_in_data_mask.any() and not delimiter_to_use:
                delimiter_to_use = delim

            elif delimiter_in_data_mask.any() and delimiter_to_use:
                message = f"More than one delimiter ({delimiter_to_use}, {delim}) was found in source column '{data.name}'"
                auditor.add(message)
                raise FailIfMoreThanOneDelimiter(message)

        # use first delimiter from params, even if does not match the String Series contents
        if not delimiter_to_use:
            delimiter_to_use = delimiter_as_list[0]

        split_data = (
            data.astype("string")
            .str.split(delimiter_to_use.strip())
            .apply(lambda x: list(map(str.strip, x)))
        )

        return split_data


def run_map_attribute(
    source_frame: pd.DataFrame = None,
    params: Params = None,
    auditor: Any = None,
    **kwargs,
):

    task = MapAttribute(auditor=auditor)
    return task.execute(
        source_frame=source_frame,
        params=params,
    )

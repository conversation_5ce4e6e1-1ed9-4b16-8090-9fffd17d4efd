import logging
import tempfile
from pathlib import Path
from typing import Any
from typing import Optional
from urllib.parse import unquote_plus

import boto3
from botocore.exceptions import ClientError

from se_core_tasks.core.exception import TaskException
from se_core_tasks.core.params import TaskParams
from se_core_tasks.core.task import Task

logger_ = logging.getLogger(__name__)


class Params(TaskParams):
    s3_key: Optional[str]
    s3_bucket: Optional[str]


class SkipIfFilePartFile(TaskException):
    pass


class S3DownloadFile(Task):
    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        file_url: Optional[str] = None,
        realm: str = None,
        **kwargs,
    ) -> str:

        s3 = boto3.client("s3")
        if file_url is not None:
            if not file_url.lower().startswith("s3://"):
                logger_.error("Param `file_url` must start with `s3://`")
                raise ValueError("Please prepend `s3://` to `file_url`")
            s3_bucket, s3_key = file_url[5:].split("/", 1)
        elif params.s3_key is not None:
            s3_bucket = params.s3_bucket if params.s3_bucket is not None else realm
            s3_key = params.s3_key
        else:
            raise ValueError("`file_url` or `params.s3_key` must be provided")

        filename = Path(s3_key).name
        temp_dir = tempfile.gettempdir()
        temp_file_path = Path(temp_dir).joinpath(filename)

        # stream file object, rather than download in one go
        logger_.info(f"Extracting s3://{s3_bucket}/{s3_key} to: {temp_file_path} ...")
        # Skipping flow for .filepart files because they are only the temporary files created by SFTP tools like WinSCP
        if s3_key.endswith(".filepart"):
            logger_.warning(f"Skipping .filepart file s3_key={s3_key}")
            raise SkipIfFilePartFile("Skipping flow for .filepart file")

        with open(temp_file_path, "wb") as fh:
            # by default, first tries to get the file using the raw filename
            # otherwise (e.g. FileNotFound) tries again, but this time using `unquote_plus`.
            # Explanation:
            # there's cases where the use of `unquote_plus` is not acceptable,
            # like when the recordings metadata is in the filename
            # e.g.: record_2023-03-14_11-19-03_+34934920030_02031502841_275301001.mp3
            # in this case `+` is key information, and in this case can't be ignored
            try:
                s3.download_fileobj(s3_bucket, s3_key, fh)
            except ClientError:
                s3.download_fileobj(s3_bucket, unquote_plus(s3_key), fh)

        return temp_file_path.as_posix()


def run_s3_download_file(
    params: Params = None,
    file_url: str = None,
    auditor: Any = None,
    realm: str = None,
    **kwargs,
):

    task = S3DownloadFile(auditor=auditor)
    return task.execute(
        params=params,
        file_url=file_url,
        realm=realm,
    )

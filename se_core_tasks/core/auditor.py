import textwrap
from typing import List
from typing import Optional
from typing import Union

import pandas as pd
from pydantic import BaseModel
from pydantic import Field


class AuditCtx(BaseModel):
    error: Optional[str] = Field(None, description="Task.execution")
    raw_index: Optional[Union[int, List[int]]] = Field(
        None, description="index of the row in raw data"
    )
    error_count: int = Field(
        0,
        description="Amount that should be aggregated in error count",
    )
    skip_count: int = Field(
        0, description="Amount that should be aggregated in skip count"
    )
    duplicate_count: int = Field(
        0, description="Amount that should be aggregated in duplicate count"
    )
    input_total_count: int = Field(
        0, description="Amount that should be aggregated in input total count"
    )
    traceback: Optional[str] = Field(
        None, description="traceback for the error thrown from the `Task.execution`"
    )
    columns: Optional[list] = None


class AuditorLog(BaseModel):
    message: str
    ctx: Optional[AuditCtx]


class Auditor:
    """
    This mimetizes swarm-sdk Auditor
    """

    def __init__(self):
        self._data: List[dict] = []

    def add(self, message: str, ctx: Optional[dict] = None):
        if ctx:
            ctx = AuditCtx.validate(ctx)
            # Keep only 30000 characters as Lucene fields can be at most 32766 bytes long
            if ctx.error:
                ctx.error = textwrap.shorten(text=ctx.error, width=30000)
        message = textwrap.shorten(text=message, width=30000)
        self._data.append(AuditorLog(message=message, ctx=ctx).dict())

    def to_dataframe(self) -> pd.DataFrame:
        df = pd.json_normalize(self._data)
        return df


def parse_audit_messages(message: str) -> str:
    """
    Strip f"" formatting characters from an audit message

    :param message: Audit message
    :return: Raw Audit message
    """
    return message.replace("{'", "").replace("'}", "")

apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: refinitiv-tr-eikon-chat-transform-worker
  namespace: ${cell_name}
spec:
  dependsOn:
    - name: ${cell_name}
    - name: elasticsearch
    - name: ${cell_name}-aries
    - name: conductor
  interval: 1m
  chart:
    spec:
      chart: se-aries-task-worker-deployment
      version: 1.0.9
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 5m
  values:
    cloud: ${cloud}
    environment: ${environment}
    owner: data-integration
    stack: ${cell_name}
    terminationGracePeriodSeconds: 14400
    image:
      repository: ${image_repo}/integration-text-comms-tasks
      tag: 2025.09.0042-dev.1759141391.8a0e24c.arm64
    env:
      TASK_NAME: refinitiv_tr_eikon_chat
      TASK_WORKER_DOMAIN: ${cell_name}
    envFromConfigMapRef:
      - aries-platform-common-env-vars
      - oma-common-env-vars
    envFromSecretRef:
      - se-elasticsearch
    resources:
      limits:
        cpu: 1500m
        memory: 4Gi
      requests:
        cpu: 1500m
        memory: 4Gi
    nodeSelector:
      kubernetes.io/arch: arm64

apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: language-identification-voice-worker
  namespace: ${cell_name}
spec:
  dependsOn:
    - name: ${cell_name}
    - name: elasticsearch
    - name: ${cell_name}-aries
    - name: conductor
  interval: 1m
  chart:
    spec:
      chart: se-aries-task-worker-deployment
      version: 1.0.9
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 5m
  values:
    cloud: ${cloud}
    environment: ${environment}
    owner: data-integration
    stack: ${cell_name}
    terminationGracePeriodSeconds: 14400
    image:
      repository: ${image_repo}/aries-language-identification-voice
      tag: 2025.09.0022-dev.1757912237.0a765ea.amd64
    env:
      TASK_MAX_EMPTY_POLLS: 2880
      TASK_NAME: language_identification_voice
      TASK_WORKER_DOMAIN: ${cell_name}
    envFromConfigMapRef:
      - aries-platform-common-env-vars
      - oma-common-env-vars
    tolerations:
      - key: "dedicated"
        operator: "Equal"
        value: "gpu"
        effect: "NoSchedule"
    nodeSelector:
      kubernetes.io/arch: amd64
      role: gpu-node
    resources:
      limits:
        cpu: 2
        memory: 6Gi
      requests:
        cpu: 2
        memory: 6Gi

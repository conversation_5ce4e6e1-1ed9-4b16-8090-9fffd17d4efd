apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: dom-config
  namespace: ${cell_name}
spec:
  dependsOn:
    - name: ${cell_name}-aries
  interval: 1m
  chart:
    spec:
      chart: aries-tenant-workflow
      version: 4.0.5
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 5m
  values:
    cloud: ${cloud}
    environment: ${cell_environment}
    stack: ${cell_name}
    strimziNamespace: infra
    tenant: dom
    workflows:
      - name: a1_voice
      - name: asc_chat
      - name: asc_voice
      - name: avatrade_client_data_handler
      - name: avaya_voice
      - name: bloomberg
      - name: bp_dubber_voice
        max_batch_size: 100
        batch_timeout_s: 180
      - name: call_cabinet_voice
      - name: call_cabinet_voice_poll
      - name: cascade
      - name: case_bulk
      - name: cloud9_voice
      - name: cloud9_voice_poll
      - name: commpeak_voice
      - name: csv_to_record
      - name: deepview_chat
      - name: deepview_chat_poll
      - name: dubber_voice
      - name: dubber_voice_poll
      - name: email
        max_batch_size: 100
        batch_timeout_s: 600
      - name: eze_eclipse_poll
      - name: focus_horizon_voice
      - name: gamma_horizon_voice
      - name: generate_insights_report
      - name: ice_chat
      - name: ice_vantage_chat
      - name: ing_ms_teams_chat
        max_batch_size: 50
        batch_timeout_s: 300
      - name: iv_kerv_voice
      - name: iv_redbox_voice
        max_batch_size: 25
        batch_timeout_s: 300
      - name: iv_transcription
      - name: iv_universal_voice
        max_batch_size: 25
        batch_timeout_s: 300
      - name: kerv_text
      - name: kerv_voice
      - name: leapxpert_chat
      - name: legal_hold
      - name: luna_advance_voice
        max_batch_size: 100
        batch_timeout_s: 300
      - name: mar_auditor
      - name: masergy_voice
      - name: ms_graph_email
      - name: ms_teams_chat
      - name: mymarket_person
        max_batch_size: 10000
      - name: natterbox_voice
      - name: nice_voice
      - name: numonix_ix_cloud_voice
      - name: numonix_ix_cloud_voice_poll
      - name: o2_sms
        max_batch_size: 500
      - name: o2_voice
      - name: order_aladdin_v2
        max_batch_size: 10000
      - name: order_blotter
        max_batch_size: 10000
      - name: order_crd
        max_batch_size: 5000
      - name: order_feed_cfox
        max_batch_size: 5000
      - name: order_iress_bell_potter_fix
        max_batch_size: 500
      - name: order_tr_fidessa_eod
        max_batch_size: 5000
      - name: outlook_calendar
      - name: primodialler_voice
        max_batch_size: 100
        batch_timeout_s: 300
      - name: redbox_voice
      - name: refinitiv_fxt_chat
      - name: refinitiv_news_ingestion
      - name: refinitiv_tr_eikon_chat
      - name: restricted_list
        max_batch_size: 50
        batch_timeout_s: 300
      - name: ringcentral_voice
      - name: ringcentral_voice_poll
      - name: run_alerts_watch
      - name: run_comms_watch
      - name: run_mar_watch
      - name: mar_itv3_refinitiv
      - name: mar_wash_trading
      - name: run_trade_watch
      - name: santander_voice
      - name: slack_chat
      - name: slack_chat_poll
      - name: slack_users
      - name: snippet_sentry_chat
      - name: steeleye_universal_chat
      - name: steeleye_universal_voice
      - name: symphony_chat
      - name: tca_metrics
      - name: telemessage
      - name: teleware_voice
      - name: the_comms_guys_voice
      - name: tr_bbg_emsi_orders
        max_batch_size: 10000
      - name: trade_sink_ns
      - name: trade_sink_s
      - name: truphone
      - name: verba_voice
      - name: via_voice
      - name: vibework_tollring_voice
      - name: voip_voice
      - name: voip_voice_poll
      - name: wavenet_voice
      - name: xima_voice
      - name: xima_voice_poll
      - name: zoom_meetings
      - name: zoom_meetings_poll
      - name: zoom_phone_voice
      - name: zoom_phone_voice_poll
      - name: zoom_trium_voice
        max_batch_size: 100
        batch_timeout_s: 300
    workflow_schedules:
      - id: dom-generate_insights_report
        cron: "0 2 * * *"
        schedule_type: cron
        workflow_name: generate_insights_report
        workflow_input: {"io_param": {"params": {}}}

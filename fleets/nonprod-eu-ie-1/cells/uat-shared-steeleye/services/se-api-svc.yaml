apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: se-api-svc
  namespace: ${cell_name}
spec:
  dependsOn:
  - name: ${cell_name}
  - name: elasticsearch
  - name: tenant-db
  interval: 1m
  chart:
    spec:
      chart: se-deployment
      version: 0.4.3
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 1m
  values:
    owner: application
    stack: ${cell_name}
    environment: ${environment}
    cloud: ${cloud}
    autoscaling:
      enabled: true
      maxReplicas: 8
      minReplicas: 4
      targetCPUUtilizationPercentage: 70
    image:
      repository: ${image_repo}/se-api-svc
      tag: 2025.09.0036.arm64
    env:
      DEBUG: "false"
      API_SERVICE_NAME: se-api-svc
      TIKA_CLIENT_ONLY: true
      TIKA_SERVER_ENDPOINT: http://tika:9998
      TIKA_HOST: http://tika:9998
    envFromSecret:
    - envName: REDIS_PASSWORD
      secretName: redis
      secretKey: redis-password
    - envName: TENANT_DB_PG_URL
      secretName: tenant-db-rds-creds
      secretKey: uri
    envFromSecretRef:
    - api-apm-config
    - azure-open-ai
    - se-elasticsearch
    - master-data-creds
    envFromConfigMapRef:
    - api-common-env-vars-config
    - email-common-env-vars-config
    - redis-common-env-vars
    reloader:
      enabled: true
    resources:
      requests:
        cpu: 2
        memory: 8Gi
      limits:
        cpu: 3
        memory: 12Gi
    service:
      type: NodePort
      metricsPort:
        enabled: true
    serviceMonitor:
      enabled: true
      additionalLabels:
        release: prometheus-stack
    serviceAccount:
      name: api-service
      create: false
    nodeSelector:
      role: app
      kubernetes.io/arch: arm64
    tolerations:
    - key: nodepool
      operator: Equal
      value: ${cell_name}-app
      effect: NoSchedule
    ingress:
      enabled: true
      annotations:
        kubernetes.io/ingress.class: alb
        alb.ingress.kubernetes.io/healthcheck-path: /api/v4.0/version
        alb.ingress.kubernetes.io/certificate-arn: ${cell_public_alb_cert_arn}
        alb.ingress.kubernetes.io/group.name: ${cell_name}-customer
        alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-TLS13-1-3-2021-06
        alb.ingress.kubernetes.io/load-balancer-name: ${cell_name}-customer-lb
        alb.ingress.kubernetes.io/tags: Fleet=${fleet_name},Cell=${cell_name}
        alb.ingress.kubernetes.io/target-type: ip
        alb.ingress.kubernetes.io/scheme: internet-facing
        alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=1800
        alb.ingress.kubernetes.io/wafv2-acl-arn: ${cell_waf_arn}
      hosts:
      - host: "*.${public_ingress_domain}"
        paths:
        - path: /api/v4.0/
          pathType: Prefix
    mountVolume:
      enabled: true
    pdb:
      enabled: false
      minAvailable: 1
    livenessProbe:
      enabled: true
      path: /api/v4.0/version
      failureThreshold: 3
      initialDelaySeconds: 30
      periodSeconds: 600
      successThreshold: 1
      timeoutSeconds: 60
    readinessProbe:
      enabled: true
      path: /api/v4.0/version
      failureThreshold: 3
      initialDelaySeconds: 30
      periodSeconds: 600
      successThreshold: 1
      timeoutSeconds: 60
  valuesFrom:
  - kind: ConfigMap
    name: trust-elastic-ca
    valuesKey: values

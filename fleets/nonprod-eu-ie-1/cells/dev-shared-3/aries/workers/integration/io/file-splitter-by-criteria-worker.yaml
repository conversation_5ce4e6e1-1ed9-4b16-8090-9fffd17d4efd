apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: file-splitter-by-criteria-worker
  namespace: ${cell_name}
spec:
  dependsOn:
  - name: ${cell_name}
  - name: elasticsearch
  - name: ${cell_name}-aries
  - name: conductor
  interval: 1m
  chart:
    spec:
      chart: se-aries-task-worker-deployment
      version: 1.0.9
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 5m
  values:
    cloud: ${cloud}
    environment: ${environment}
    owner: data-integration
    stack: ${cell_name}
    terminationGracePeriodSeconds: 14400
    image:
      repository: ${image_repo}/integration-generic-tasks
      tag: 2025.08.0018.arm64
    env:
      TASK_NAME: file_splitter_by_criteria
      TASK_WORKER_DOMAIN: ${cell_name}
      DOWNLOAD_FILES_ASYNC: true
    envFromConfigMapRef:
    - aries-platform-common-env-vars
    - oma-common-env-vars
    envFromSecretRef:
    - se-elasticsearch
    resources:
      limits:
        cpu: 1000m
        memory: 2.5Gi
      requests:
        cpu: 1000m
        memory: 2.5Gi
    nodeSelector:
      kubernetes.io/arch: arm64

apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: slack-chat-poll-worker
  namespace: ${cell_name}
spec:
  dependsOn:
    - name: aries-scaler-api
    - name: elasticsearch
    - name: conductor
  interval: 1m
  chart:
    spec:
      chart: se-aries-task-worker-deployment
      version: 1.0.9
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 5m
  values:
    cloud: ${cloud}
    environment: ${environment}
    stack: ${cell_name}
    owner: solutions-engineering
    terminationGracePeriodSeconds: 7200
    image:
      repository: ${image_repo}/integration-poller-tasks
      tag: 2025.08.0030-dev.1755598995.2a27ee2.arm64
    env:
      TASK_NAME: slack_chat_poll
      TASK_WORKER_DOMAIN: ${cell_name}
      VAULT_K8S_ROLE: aries-worker-${cell_name}
    envFromConfigMapRef:
      - aries-platform-common-env-vars
      - oma-common-env-vars
      - vault-common-env-vars
    envFromSecretRef:
      - se-elasticsearch
    resources:
      limits:
        memory: 2.25Gi
        cpu: 1
      requests:
        memory: 2.25Gi
        cpu: 1
    nodeSelector:
      kubernetes.io/arch: arm64
  valuesFrom:
    - kind: ConfigMap
      name: trust-vault-ca
      valuesKey: values

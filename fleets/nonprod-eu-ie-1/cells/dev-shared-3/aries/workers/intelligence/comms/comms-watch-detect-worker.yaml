apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: comms-watch-detect-worker
  namespace: ${cell_name}
spec:
  dependsOn:
    - name: ${cell_name}
    - name: elasticsearch
    - name: ${cell_name}-aries
    - name: conductor
  interval: 1m
  chart:
    spec:
      chart: se-aries-task-worker-deployment
      version: 1.0.9
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 5m
  values:
    cloud: ${cloud}
    environment: ${environment}
    owner: data-intelligence
    stack: ${cell_name}
    terminationGracePeriodSeconds: 600
    image:
      repository: ${image_repo}/intelligence-core-tasks
      tag: 2025.07.0079-dev.1753247874.6449ed7.arm64
    env:
      TASK_NAME: comms_watch_detect
      TASK_WORKER_DOMAIN: ${cell_name}
    envFromConfigMapRef:
      - aries-platform-common-env-vars
      - oma-common-env-vars
    envFromSecretRef:
      - se-elasticsearch
    resources:
      limits:
        cpu: 1
        memory: 1.25Gi
      requests:
        cpu: 1
        memory: 1.25Gi
    nodeSelector:
      kubernetes.io/arch: arm64
    keda:
      maxReplicaCount: 10

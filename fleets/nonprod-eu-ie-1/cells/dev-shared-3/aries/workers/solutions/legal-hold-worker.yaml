apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: legal-hold-worker
  namespace: ${cell_name}
spec:
  dependsOn:
    - name: aries-scaler-api
    - name: conductor
  interval: 1m
  chart:
    spec:
      chart: se-aries-task-worker-deployment
      version: 1.0.9
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 5m
  values:
    cloud: ${cloud}
    environment: ${environment}
    stack: ${cell_name}
    owner: solutions-engineering
    terminationGracePeriodSeconds: 1800
    image:
      repository: ${image_repo}/platform-core-tasks
      tag: 2025.05.0148.arm64
    env:
      TASK_NAME: apply_legal_hold
      TASK_WORKER_DOMAIN: ${cell_name}
      USE_DEFAULT_AZURE_CREDENTIALS: true
    envFromConfigMapRef:
      - aries-platform-common-env-vars
      - oma-common-env-vars
    envFromSecret:
      - envName: TENANT_DB_PG_URL
        secretName: tenant-db-rds-creds
        secretKey: uri
    resources:
      limits:
        cpu: 100m
        memory: 512Mi
      requests:
        cpu: 100m
        memory: 512Mi
    keda:
      maxReplicaCount: 2
    nodeSelector:
      kubernetes.io/arch: arm64

apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: conductor-callback-service
  namespace: ${cell_name}
spec:
  dependsOn:
    - name: ${cell_name}
    - name: conductor
  interval: 1m
  chart:
    spec:
      chart: se-deployment
      version: 0.4.3
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 5m
  values:
    cloud: ${cloud}
    environment: ${environment}
    stack: ${cell_name}
    owner: solutions-engineering
    image:
      repository: ${image_repo}/platform-core-services
      tag: 2025.05.0148.arm64
    args:
      - "conductor_callback"
    env:
      CALLBACK_EVENTS_TOPIC: aries.${cell_name}.callback.conductor.events
      CONDUCTOR_API_BACKOFF_MAX_TIME_S: "720" # 12 minutes
      CONDUCTOR_API_URL: http://conductor-server.${cell_name}:8080/api
      CONSUMER_GROUP_ID: aries-conductor-callback-service-${cell_name}
      KAFKA_CONSUMER_MAX_POLL_INTERVAL_MS: "2400000" # 40 minutes
      SENTRY_ENABLED: 1
      STACK: ${cell_name}
    envFromConfigMapRef:
      - kafka-consumer-common-env-vars
    resources:
      limits:
        memory: 0.5Gi
      requests:
        cpu: 0.1
        memory: 136Mi
    nodeSelector:
      role: app
      kubernetes.io/arch: arm64
      karpenter.sh/capacity-type: on-demand
    tolerations:
      - key: nodepool
        operator: Equal
        value: ${cell_name}-app
        effect: NoSchedule
    podAnnotations:
      cluster-autoscaler.kubernetes.io/safe-to-evict: "false"
      karpenter.sh/do-not-disrupt: "true"
    keda:
      enabled: true
      maxReplicaCount: 5
      pollingInterval: 5
      fallback:
        failureThreshold: 3
        replicas: 5
      triggers:
        - type: kafka
          metadata:
            bootstrapServers: kafka-cluster-strimzi-cluster-kafka-bootstrap.infra:9092
            consumerGroup: aries-conductor-callback-service-${cell_name}
            topic: aries.${cell_name}.callback.conductor.events
            lagThreshold: "100"

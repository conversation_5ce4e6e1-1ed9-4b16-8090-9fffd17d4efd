apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: bp-sftp-users             # replace with the tenant name  (ensure "-sftp-users" is included on the end)
  namespace: ${cell_name}
spec:
  chart:
    spec:
      chart: sftp-users
      interval: 1m
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      version: 0.1.4
  interval: 1m
  values:
    tenant: bp
    whitelistIPs:
    - ************/32
    - *************/32
    - ***********/32
    - *************/32
    - *************/32
    - ***********/32
    - *************/32
    - ************/32
    users:
    - s3Path: onboarding/MRL_Live_Data/uat/
      username: bp-uat-001-mrlfile
      sshKeys: 
      - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDE81B9K9VSkBiWOsGIXoHMq27JZeGW3TvGhTTUl+BUEUC4Ud9Gb9ELZTs1m0ZV/bcQ3sjA+3s2LFmu2252ZeS2qtT69eA0f4hZauczja0IeB6lPnwBNlxb3yQowCAF+nuDhnS1eugpVuOjApWSg6lmuRHdBg6+CIUarWMrn4mya+eJDV693Y7wVS3WNgzLGDSwPil5wmikwVeBQJTvD9LI0/vJ87OSQhSA1bWPMHRZGsLpWVGYfcghyXhU5b75Nf2D4w8AhOhmoxJ4C0G7hxzJdFuG3G2rLMXeuN8oclRejlIc3qsnJ0DIfBgxv5Pmlc1CpeAwlbmxyTAtAMMp6Q+W6GsyMs3wXEoOOcrutNLTDFMhT2DVa9nbUjM0KTBZ7fpIZ04oQjOSbNt/cQGl976kG2YHjzLynidjKpuwwahzp9gMrWBAnf8nVGDQRkI9GcP3X9mu5s7cuQJYuzmrxYPhIVqmhBcT5sXsRTb7WduMBYek4eI+S+MPDdVgfKKZux8= <EMAIL>
    - s3Path: aries/ingress/nonstreamed/evented/mymarket_bp_person/  
      username: bp-prod-003-mrlfile
      sshKeys: 
      - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC7NV1jO1B0OLbYh4wimissAkC4YGQX959JmtuMi5VwMC39PmGO39rMJ8r9j1fbsWvLc7MpQP07Najz0v4DxK4JvSQg18iLCoa59zSF9XbFbKrg3yTnOGzTbWwbS1AQPpCe7C7P8X6hxFGg10llLyA0TGl2Sb7m/3Pu91xBWP+Okk6JY2XGjUWIFx3HyKFAiOBi6kU4MJUUD+1a/kq8JMqPsBEVyEns9UOstyLVrbDld9ThGQ6Mw1v7ClkWIuRisl0yBCjk14WvvgwLsXNbk6jE/NrtTzCi7icrpyZarPy0Y2ih9KBc+NNulRZvZoeivcHBsZh6x0BJTbEbJJYAsBzNqf3sopm8hGLCYZ4+L6RlPXpqkxDvW/9fwwN0Ew++KZjhMlAwgw0mZ6h+THBlTsTL0p2WLNxv8+IQCgpAf1K5rJB2WUY3Hx4PUg6oJCyEGtfklo9QLasTdhMcB6skDrxcmmDh5JZCwbsi4vTAKPs+IV3uzdAja7GsW0f2hPRnDP0= <EMAIL>
    - s3Path: onboarding/leapxpert2/
      username: bp-uat-005-leapxpert
      sshKeys: 
      - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDkp04IUTs8cg8P0CTkI/RA1dGFaoQmuAIKs9AD944bm9wYa82fHk9jRp7eLTCe7LYzmd+PNRDeZw2CW0WWPKRajwXL5pgOxiSfoaZ3S4fggN6mJ2p1hJL76B/QUy9kRB4QN2GG9ORESE7PCghdWERJu/MX7Ty1XrZTr/gbzlODZdOmWQ0Byux0BJSzbWoW+BEqFWeFUrl7i1bSQK/vBriNY9ga9dle176KUBjXzO6nphiD5rdO8TVSRHl/16B7Dom8vOceum0f95OxelHYt00iZtHc2EpESqVp/NIK/pRUEHZJOYM1s13VLpSHjIWuCI1NgIGqnQrDgvq1P+r5IUPOJE1iRgf3DLsvDQMqSv6VwWOJcHzz3se6EB85vPuUsOdN3a1qPE62ogdj0V9uqS8XljZrlgyND4SUVbKQ+udLPa99fl6NYKYvcEvQAliMYcGxFN8z7nImyF/8Mx8+WppZdJWK/FhbMTGOGhauMu+jQ/cEPgO6TY35uN811LlJx11q55TusoZt2FS3bMWAqT3c3iqlDn1SimlptgeUnLbKdD4dRdRSZSlG71S5ccJbkEs0E8Hhb+0gvc38YbmlSoD/AoPvh65Dy7QoDepUdTy6tNoETXzPxmU6A+EUGtutgPzBcZAAhrNIqAz4cK8NwO7lF9Csh8kRQlUdCLD/E47lPQ== <EMAIL>
    - s3Path: aries/ingress/nonstreamed/evented/mymarket_bp_person/
      username: bp-uat-002-aliasfile
      password: $6$YV4AzMOdr4YyebUN$XYymSA1YUiKbZ7GWZb.X3oDb/FpKMTRa3qj/oc7RHVgrM22C8nkC/BQCTaRVQj0E/p.OBEhjc1rCw2k5kMSnP/            # (OPTIONAL - INCLUDE ONLY IF PASSWORD ACCOUNT AND REMOVE THE sshKeys BLOCK) this is a hashed (secure) version of the password
    - s3Path: onboarding/MRL_Live_Data/prod/
      username: bp-prod-004-aliasfile
      password: $6$YV4AzMOdr4YyebUN$XYymSA1YUiKbZ7GWZb.X3oDb/FpKMTRa3qj/oc7RHVgrM22C8nkC/BQCTaRVQj0E/p.OBEhjc1rCw2k5kMSnP/

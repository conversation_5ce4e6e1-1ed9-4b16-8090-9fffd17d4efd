apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: redis
  namespace: ${cell_name}
spec:
  interval: 1m
  dependsOn:
  - name: ${cell_name}
  chart:
    spec:
      chart: redis
      version: "18.10.0"
      sourceRef:
        kind: HelmRepository
        name: bitnami
        namespace: flux-system
      interval: 3m
  values:
    image:
      registry: 698897937809.dkr.ecr.eu-west-1.amazonaws.com
      repository: docker.io/bitnami/redis
      tag: 7.4.3-debian-12-r0
    global:
      storageClass: ${cell_name}-default
    architecture: standalone
    master:
      nodeSelector:
        role: app
        kubernetes.io/arch: arm64
      tolerations:
      - key: nodepool
        operator: Equal
        value: ${cell_name}-app
        effect: NoSchedule
      - key: nodepool
        operator: Equal
        value: app
        effect: NoSchedule

      service:
        type: ClusterIP

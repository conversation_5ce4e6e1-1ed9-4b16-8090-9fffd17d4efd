apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: transcription-feed-worker
  namespace: ${cell_name}
spec:
  dependsOn:
    - name: ${cell_name}
    - name: elasticsearch
    - name: ${cell_name}-aries
    - name: conductor
  interval: 1m
  chart:
    spec:
      chart: se-aries-task-worker-deployment
      version: 1.0.9
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 5m
  values:
    cloud: ${cloud}
    environment: ${environment}
    owner: data-integration
    stack: ${cell_name}
    terminationGracePeriodSeconds: 3600
    image:
      repository: ${image_repo}/integration-audio-comms-tasks
      tag: 2025.08.0053-dev.1756384846.21465d2.arm64
    env:
      TASK_NAME: transcription_feed
      TASK_WORKER_DOMAIN: ${cell_name}
    envFromConfigMapRef:
      - aries-platform-common-env-vars
      - oma-common-env-vars
    envFromSecretRef:
      - se-elasticsearch
    resources:
      limits:
        cpu: 500m
        memory: 2Gi
      requests:
        cpu: 500m
        memory: 2Gi
    nodeSelector:
      kubernetes.io/arch: arm64

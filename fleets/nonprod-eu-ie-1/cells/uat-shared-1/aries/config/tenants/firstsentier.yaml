apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: firstsentier-config
  namespace: ${cell_name}
spec:
  dependsOn:
    - name: ${cell_name}-aries
  interval: 1m
  chart:
    spec:
      chart: aries-tenant-workflow
      version: 4.0.5
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 5m
  values:
    cloud: ${cloud}
    environment: ${cell_environment}
    stack: ${cell_name}
    strimziNamespace: infra
    tenant: firstsentier
    workflows:
      - name: cascade
      - name: case_bulk
      - name: csv_to_record
      - name: generate_insights_report
      - name: mar_auditor
      - name: mymarket_person
        max_batch_size: 10000
      - name: order_aladdin_v2
        max_batch_size: 10000
      - name: order_blotter
        max_batch_size: 10000
      - name: run_mar_watch
      - name: mar_front_running_v2
      - name: mar_itv3_refinitiv
      - name: mar_marking_v2
      - name: mar_wash_trading
      - name: run_trade_watch
      - name: tca_metrics
    workflow_schedules:
      - id: firstsentier-generate_insights_report
        cron: "0 2 * * *"
        schedule_type: cron
        workflow_name: generate_insights_report
        workflow_input: {"io_param": {"params": {}}}

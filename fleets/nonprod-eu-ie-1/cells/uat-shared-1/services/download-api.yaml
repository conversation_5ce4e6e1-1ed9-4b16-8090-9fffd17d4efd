apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: download-api-svc
  namespace: ${cell_name}
spec:
  dependsOn:
    - name: ${cell_name}
    - name: elasticsearch
    - name: tenant-db
    - name: conductor
  interval: 1m
  chart:
    spec:
      chart: se-deployment
      version: 0.4.3
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 1m
  values:
    owner: application
    stack: ${cell_name}
    environment: ${environment}
    cloud: ${cloud}
    autoscaling:
      enabled: true
      maxReplicas: 3
      minReplicas: 2
      targetCPUUtilizationPercentage: 50
    image:
      repository: ${image_repo}/download-api-svc
      tag: 2025.09.12.arm64
    env:
      API_SERVICE_NAME: download-api-svc
    envFromSecretRef:
      - api-apm-config
      - se-elasticsearch
      - master-data-creds
    envFromSecret:
      - envName: TENANT_DB_PG_URL
        secretName: tenant-db-rds-creds
        secretKey: uri
    envFromConfigMapRef:
      - api-common-env-vars-config
    reloader:
      enabled: true
    resources:
      requests:
        cpu: 0.5
        memory: 1Gi
      limits:
        cpu: 0.5
        memory: 2Gi
    serviceAccount:
      create: false
      name: download-api-service
    nodeSelector:
      role: app
      kubernetes.io/arch: arm64
    tolerations:
      - key: nodepool
        operator: Equal
        value: ${cell_name}-app
        effect: NoSchedule
    service:
      type: NodePort
    ingress:
      enabled: true
      annotations:
        kubernetes.io/ingress.class: alb
        alb.ingress.kubernetes.io/healthcheck-path: /api/v3.0/download/version
        alb.ingress.kubernetes.io/certificate-arn: ${cell_public_alb_cert_arn}
        alb.ingress.kubernetes.io/group.name: ${cell_name}-customer
        alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-TLS13-1-3-2021-06
        alb.ingress.kubernetes.io/load-balancer-name: ${cell_name}-customer-lb
        alb.ingress.kubernetes.io/tags: Fleet=${fleet_name},Cell=${cell_name}
        alb.ingress.kubernetes.io/target-type: ip
        alb.ingress.kubernetes.io/scheme: internet-facing
        alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=1800
        alb.ingress.kubernetes.io/wafv2-acl-arn: ${cell_waf_arn}
      hosts:
        - host: "*.${public_ingress_domain}"
          paths:
            - path: /api/v3.0/download
              pathType: Prefix
    mountVolume:
      enabled: true
    pdb:
      enabled: true
      minAvailable: 1
    livenessProbe:
      enabled: true
      path: /api/v3.0/download/version
      failureThreshold: 3
      initialDelaySeconds: 30
      periodSeconds: 120
      successThreshold: 1
      timeoutSeconds: 300
    readinessProbe:
      enabled: true
      path: /api/v3.0/download/version
      failureThreshold: 3
      initialDelaySeconds: 30
      periodSeconds: 120
      successThreshold: 1
      timeoutSeconds: 300
  valuesFrom:
    - kind: ConfigMap
      name: trust-elastic-ca
      valuesKey: values

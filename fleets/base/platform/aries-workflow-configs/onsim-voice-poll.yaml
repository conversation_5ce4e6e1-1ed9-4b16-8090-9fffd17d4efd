apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: onsim-voice-poll-config
  namespace: platform
spec:
  dependsOn:
    - name: aries-platform-config-api
  interval: 1m
  chart:
    spec:
      chart: io-workflow
      version: 1.0.0
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 1m
  values:
    cloud: ${cloud}
    environment: ${environment}
    name: onsim_voice_poll
    streamed: true
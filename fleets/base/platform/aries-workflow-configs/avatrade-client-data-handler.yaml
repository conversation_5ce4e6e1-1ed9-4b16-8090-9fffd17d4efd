apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: avatrade-client-data-handler-config
  namespace: platform
spec:
  dependsOn:
  - name: aries-platform-config-api
  interval: 1m
  chart:
    spec:
      chart: io-workflow
      version: 1.1.0
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 1m
  values:
    cloud: ${cloud}
    environment: ${environment}
    name: avatrade_client_data_handler
    storagePrefix: aries/ingress/nonstreamed/evented/avatrade_client_data_handler/
    streamed: false
    displayName: Avatrade Client Data Handler

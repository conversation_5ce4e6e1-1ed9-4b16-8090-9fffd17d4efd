apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: three-cx-voice-config
  namespace: platform
spec:
  dependsOn:
  - name: aries-platform-config-api
  interval: 1m
  chart:
    spec:
      chart: io-workflow
      version: 1.1.0
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 1m
  values:
    cloud: ${cloud}
    environment: ${environment}
    name: three_cx_voice
    storagePrefix: aries/ingress/streamed/evented/three_cx_voice/
    streamed: true
    displayName: 3CX Voice

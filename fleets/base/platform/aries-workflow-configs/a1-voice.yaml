apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: a1-voice-config
  namespace: platform
spec:
  dependsOn:
  - name: aries-platform-config-api
  interval: 1m
  chart:
    spec:
      chart: io-workflow
      version: 1.1.0
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 1m
  values:
    cloud: ${cloud}
    environment: ${environment}
    name: a1_voice
    storagePrefix: aries/ingress/streamed/evented/a1_voice/
    streamed: true
    displayName: A1 Voice

apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: trade-sink-s-config
  namespace: platform
spec:
  dependsOn:
  - name: aries-platform-config-api
  interval: 1m
  chart:
    spec:
      chart: io-workflow
      version: 1.1.0
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 1m
  values:
    cloud: ${cloud}
    environment: ${environment}
    name: trade_sink_s
    storagePrefix: aries/ingress/streamed/evented/trade_sink_s/
    streamed: true
    displayName: Trade Sink Streamed

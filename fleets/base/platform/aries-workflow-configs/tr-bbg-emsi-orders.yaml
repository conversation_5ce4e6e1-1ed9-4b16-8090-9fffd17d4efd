apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: tr-bbg-emsi-orders-config
  namespace: platform
spec:
  dependsOn:
    - name: aries-platform-config-api
  interval: 1m
  chart:
    spec:
      chart: io-workflow
      version: 1.0.0
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 1m
  values:
    cloud: ${cloud}
    environment: ${environment}
    name: tr_bbg_emsi_orders
    storagePrefix: aries/ingress/nonstreamed/evented/tr_bbg_emsi_orders/
    streamed: false

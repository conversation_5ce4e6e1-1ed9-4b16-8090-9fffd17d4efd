# mirror kustomize-controller build options
kustomize_flags=("--load-restrictor=LoadRestrictionsNone")
kustomize_config="kustomization.yaml"

# mirror kyverno flags
kyverno_flags=("--audit-warn")

echo "INFO - Get kyverno cluster policies from helm chart" 
helm repo add kyverno https://kyverno.github.io/kyverno/
helm template kyverno/kyverno-policies \
  --set 'podSecurityStandard=restricted' \
  --set 'includeOtherPolicies={require-non-root-groups}' > ./cluster-policies.yaml

echo "INFO - Validating kyverno policies" 
find . -type f -name $kustomize_config -print0 | while IFS= read -r -d $'\0' file;
  do
    echo "INFO - Validating kyverno policies ${file/%$kustomize_config}"
    kustomize build "${file/%$kustomize_config}" "${kustomize_flags[@]}" | \
      kyverno apply ./cluster-policies.yaml ./infrastructure/kyverno/policies "${kyverno_flags[@]}" --resource -
    if [ $? -ne 0 ]; then
      exit 1
    fi
done
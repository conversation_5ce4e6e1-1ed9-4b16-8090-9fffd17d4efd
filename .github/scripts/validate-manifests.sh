# mirror kustomize-controller build options
kustomize_flags=("--load-restrictor=LoadRestrictionsNone")
kustomize_config="kustomization.yaml"

kubeconform_config=("-strict" "-ignore-missing-schemas" "-schema-location" "default" "-schema-location" "/tmp/flux-crd-schemas" "-verbose" "-exit-on-error")

echo "INFO - Downloading Flux OpenAPI schemas"
mkdir -p /tmp/flux-crd-schemas/master-standalone-strict
curl -sL https://github.com/fluxcd/flux2/releases/latest/download/crd-schemas.tar.gz | tar zxf - -C /tmp/flux-crd-schemas/master-standalone-strict

find . -type f -name '*.yaml' -print0 | while IFS= read -r -d $'\0' file;
  do
    echo "INFO - Validating $file"
    yq e 'true' "$file" > /dev/null
done

echo "INFO - Validating clusters"
find ./clusters -maxdepth 2 -type f -name '*.yaml' -print0 | while IFS= read -r -d $'\0' file;
  do
    kubeconform "${kubeconform_config[@]}" "${file}"
    if [ $? -ne 0 ]; then
      exit 1
    fi
done

echo "INFO - Validating fleets"
find ./fleets -maxdepth 2 -type f -name '*.yaml' -print0 | while IFS= read -r -d $'\0' file;
  do
    kubeconform "${kubeconform_config[@]}" "${file}"
    if [ $? -ne 0 ]; then
      exit 1
    fi
done

echo "INFO - Validating cells"
find ./cells -maxdepth 2 -type f -name '*.yaml' -print0 | while IFS= read -r -d $'\0' file;
  do
    kubeconform "${kubeconform_config[@]}" "${file}"
    if [ $? -ne 0 ]; then
      exit 1
    fi
done

echo "INFO - Validating kustomize overlays"
find . -type f -name $kustomize_config -print0 | while IFS= read -r -d $'\0' file;
  do
    echo "INFO - Validating kustomization ${file/%$kustomize_config}"
    tmpfile=$(mktemp)
    kustomize build "${file/%$kustomize_config}" "${kustomize_flags[@]}" --output $tmpfile
    if [ $? -ne 0 ]; then
      exit 1
    fi
    kubeconform "${kubeconform_config[@]}" $tmpfile
    if [ $? -ne 0 ]; then
      exit 1
    fi
    unlink $tmpfile
done

import datetime
from typing import List, Optional

from pydantic import Field

from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8
from se_elastic_schema.static.reference import Module


class UserBookmark(SteelEyeSchemaBaseModelES8):
    created: datetime.datetime = Field(
        ...,
        description="bookmark record creation timestamp",
    )
    createdBy: str = Field(
        ...,
        description="user id of the user created the bookmark",
    )
    createdByName: Optional[str] = Field(
        None,
        description="user name of the user created the bookmark",
    )
    description: Optional[str] = Field(
        None,
        description="brief description of the bookmark",
    )
    favoritedBy: Optional[List[str]] = Field(
        None,
        description="list of user names favorited the bookmark record",
    )
    filters: str = Field(
        ...,
        description="query string used by the UI to filter the page results",
        analyzer="standard",
    )
    filterModel: Optional[str] = Field(
        None,
        description="type of schema model that filters are applied on",
    )
    module: Module = Field(
        ...,
        description="module name enum",
    )
    name: str = Field(
        ...,
        description="custom name user can give to the bookmark",
    )
    path: str = Field(
        ...,
        description="navigation path where the bookmark is created",
    )
    shared: bool = Field(
        False,
        description="a flag to determine if the bookmark can be seen by other users",
    )
    updated: Optional[datetime.datetime] = Field(
        None,
        description="bookmark record updated timestamp",
    )
    updatedByName: Optional[str] = Field(
        None,
        description="user name of the user updated the bookmark",
    )
    updatedBy: Optional[str] = Field(
        None,
        description="user id of the user updated the bookmark",
    )

    @classmethod
    def id_props(cls) -> None:
        return None

    @classmethod
    def hash_props(cls) -> List[str]:
        return [
            "created",
            "createdBy",
            "description",
            "favoritedBy",
            "filters",
            "path",
            "shared",
            "updated",
            "updatedBy",
        ]

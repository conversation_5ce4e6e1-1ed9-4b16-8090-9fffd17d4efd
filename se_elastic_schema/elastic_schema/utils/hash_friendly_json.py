import datetime as dt
import decimal

import pytz
import simplejson
from simplejson.encoder import (
    PosInf,
    _make_iterencode,
    binary_type,
    encode_basestring,
    encode_basestring_ascii,
    text_type,
)


def _float_repr(o):
    return f"{o:.15f}"


def default(o):
    if isinstance(o, dt.date):
        if isinstance(o, dt.datetime):
            if o.utcoffset() is not None:
                return (
                    o.astimezone(pytz.UTC).replace(tzinfo=None).isoformat(timespec="milliseconds")
                    + "Z"
                )
            else:
                return o.isoformat(timespec="milliseconds") + "Z"
        else:
            return dt.datetime(o.year, o.month, o.day).isoformat(timespec="milliseconds") + "Z"

    elif isinstance(o, dt.time):
        return o.isoformat(timespec="milliseconds") + "Z"

    raise TypeError()


class HashFriendlyJsonEncoder(simplejson.JSONEncoder):
    # Tried standard library's json, orjson and simplejson. Neither allows easy customisation of float
    # representation.
    # Simplejson requires only one copied method with two changes:
    # - change _repr=FLOAT_REPR to our custom float representation.
    # - remove the code that allows creation of C encoder as that doesn't allow float customisation at all.

    def iterencode(self, o):
        """Encode the given object and yield each string
        representation as available.

        For example::

            for chunk in JSONEncoder().iterencode(bigobject):
                mysocket.write(chunk)

        """
        if self.check_circular:
            markers = {}
        else:
            markers = None
        if self.ensure_ascii:
            _encoder = encode_basestring_ascii
        else:
            _encoder = encode_basestring
        if self.encoding != "utf-8" and self.encoding is not None:

            def _encoder(o, _orig_encoder=_encoder, _encoding=self.encoding):
                if isinstance(o, binary_type):
                    o = text_type(o, _encoding)
                return _orig_encoder(o)

        def floatstr(
            o,
            allow_nan=self.allow_nan,
            ignore_nan=self.ignore_nan,
            _repr=_float_repr,
            _inf=PosInf,
            _neginf=-PosInf,
        ):
            # Check for specials. Note that this type of test is processor
            # and/or platform-specific, so do tests which don't depend on
            # the internals.

            if o != o:
                text = "NaN"
            elif o == _inf:
                text = "Infinity"
            elif o == _neginf:
                text = "-Infinity"
            else:
                if not isinstance(o, float):
                    # See #118, do not trust custom str/repr
                    o = float(o)
                return _repr(o)

            if ignore_nan:
                text = "null"
            elif not allow_nan:
                raise ValueError("Out of range float values are not JSON compliant: " + repr(o))

            return text

        key_memo = {}
        int_as_string_bitcount = 53 if self.bigint_as_string else self.int_as_string_bitcount

        _iterencode = _make_iterencode(
            markers,
            self.default,
            _encoder,
            self.indent,
            floatstr,
            self.key_separator,
            self.item_separator,
            self.sort_keys,
            self.skipkeys,
            self.use_decimal,
            self.namedtuple_as_object,
            self.tuple_as_array,
            int_as_string_bitcount,
            self.item_sort_key,
            self.encoding,
            self.for_json,
            self.iterable_as_array,
            Decimal=decimal.Decimal,
        )

        try:
            return _iterencode(o, 0)
        finally:
            key_memo.clear()


def dumps(obj, **kwargs) -> str:
    kwargs.setdefault("separators", (",", ":"))
    kwargs.setdefault("sort_keys", True)
    kwargs.setdefault("default", default)
    kwargs.setdefault("use_decimal", True)
    kwargs.setdefault("ignore_nan", True)
    kwargs.setdefault("cls", HashFriendlyJsonEncoder)
    return simplejson.dumps(obj, **kwargs)

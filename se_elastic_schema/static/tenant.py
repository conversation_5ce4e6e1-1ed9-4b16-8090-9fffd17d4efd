from se_elastic_schema.elastic_schema.core.base import BaseStrEnum


class NotificationOptionType(BaseStrEnum):
    __traitFqn__ = "tenant/notification_option_type"
    BY_ADMIN = "BY_ADMIN"
    BY_ANYONE = "BY_ANYONE"
    BY_NON_ADMIN = "BY_NON_ADMIN"


class NotificationSettingType(BaseStrEnum):
    __traitFqn__ = "tenant/notification_setting_type"
    SCHEDULE_EDITED = "SCHEDULE_EDITED"
    WATCH_CREATED = "WATCH_CREATED"
    WATCH_DELETED = "WATCH_DELETED"
    WATCH_EDITED = "WATCH_EDITED"
    WATCH_SCHEDULED = "WATCH_SCHEDULED"


class ReportingMechanismArmType(BaseStrEnum):
    __traitFqn__ = "tenant/reporting_mechanism_arm_type"
    ABIDE_FINANCIAL_DRSP_LIMITED = "Abide Financial DRSP Limited"
    AQ_METRICS_LIMITED = "AQ Metrics Limited"
    BANKERNES_EDB_CENTRAL = "Bankernes EDB Central A.M.B.A."
    BLOOMBERG_DATA_REPORTING_SERVICES_LTD = "Bloomberg Data Reporting Services Ltd"
    BME_REGULATORY_SERVICES = "BME REGULATORY SERVICES"
    CME = "CME"
    DEUTSCHE_BORSE_AKTIENGESELLSCHAFT = "Deutsche Börse Aktiengesellschaft"
    EURONEXT_PARIS_SA = "Euronext Paris SA"
    KELER_KOZPONTI_ERTEKTAR_ZRT = "KELER Központi Értéktár Zrt."
    KRAJOWY_DEPOZYT_PAPIEROW_WARTOSCIOWYCH_S = "Krajowy Depozyt Papierów Wartościowych S.A."
    LONDON_STOCK_EXCHANGE_PLC = "London Stock Exchange plc"
    NASDAQ_BROKER_SERVICES_AKTIEBOLAG = "Nasdaq Broker Services Aktiebolag"
    UNAVISTA = "UnaVista"
    XTRAKTER_LIMITED = "Xtrakter Limited"


class WorkflowEffectType(BaseStrEnum):
    __traitFqn__ = "tenant/workflow_effect_type"
    ALLOW = "ALLOW"
    PREVENT = "PREVENT"
    REVIEW = "REVIEW"


class WorkflowSettingType(BaseStrEnum):
    __traitFqn__ = "tenant/workflow_setting_type"
    ALERT_COMMENTS = "ALERT_COMMENTS"
    CASE_MGR = "CASE_MGR"
    CASE_MGR_ADMIN = "CASE_MGR_ADMIN"
    CASE_MGR_NON_ADMIN = "CASE_MGR_NON_ADMIN"
    CLOSE_CASE_BY_ADMIN = "CLOSE_CASE_BY_ADMIN"
    CLOSE_CASE_BY_NON_ADMIN = "CLOSE_CASE_BY_NON_ADMIN"
    SCHEDULE_AND_WATCH_COMMENTS = "SCHEDULE_AND_WATCH_COMMENTS"
    SCHEDULE_EDITS_BY_NON_ADMIN = "SCHEDULE_EDITS_BY_NON_ADMIN"
    WATCH_CREATE = "WATCH_CREATE"
    WATCH_DELETION = "WATCH_DELETION"
    WATCH_EDITS_BY_ADMIN = "WATCH_EDITS_BY_ADMIN"
    WATCH_EDITS_BY_NON_ADMIN = "WATCH_EDITS_BY_NON_ADMIN"


class WorkflowSubSettingType(BaseStrEnum):
    __traitFqn__ = "tenant/workflow_sub_setting_type"
    CREATED_BY_ADMIN = "CREATED_BY_ADMIN"
    CREATED_BY_NON_ADMIN = "CREATED_BY_NON_ADMIN"
    CREATED_BY_NON_ADMIN_SELF = "CREATED_BY_NON_ADMIN_SELF"
    NA = "NA"


class ResolutionCategoryTypeVisibility(BaseStrEnum):
    CLIENT = "Client"
    STEELEYE = "SteelEye"
    BOTH = "Both"

import datetime
from typing import Optional

from pydantic import Field

from se_elastic_schema.components.mifid2.lis_ssti_threshold import LisSstiThreshold
from se_elastic_schema.elastic_schema.core.base import SteelEyeComponentBaseModel
from se_elastic_schema.static.reference import LiquidityFlag


class ThresholdCalculation(SteelEyeComponentBaseModel):
    __traitFqn__ = "mifid2/threshold_calculation"
    __traitDesc__ = "lis and ssti threshold calculations"
    averageDailyTransactionValue: Optional[float] = Field(None)
    averageDailyTransactionValueCurrency: Optional[str] = Field(None, max_length=3)
    averageDailyTurnover: Optional[float] = Field(None)
    averageDailyTurnoverCurrency: Optional[str] = Field(None, max_length=3)
    averageNumberOfTransactions: Optional[float] = Field(None)
    averageVolumeOfTransactions: Optional[float] = Field(None)
    from_: Optional[datetime.date] = Field(None, alias="from")
    liquid: Optional[LiquidityFlag] = Field(None)
    liquidBand: Optional[str] = Field(None)
    methodology: Optional[str] = Field(None)
    mostRelevantMIC: Optional[str] = Field(None)
    mostRelevantMICAverageNumberOfDailyTransactions: Optional[float] = Field(None)
    post: Optional[LisSstiThreshold] = Field(None)
    pre: Optional[LisSstiThreshold] = Field(None)
    sourceKey: Optional[str] = Field(None)
    to: Optional[datetime.date] = Field(None)

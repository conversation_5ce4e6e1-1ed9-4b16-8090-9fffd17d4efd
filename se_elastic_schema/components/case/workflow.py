import datetime
from typing import List, Optional

from pydantic import Field

from se_elastic_schema.elastic_schema.core.base import SteelEyeComponentBaseModel
from se_elastic_schema.models.tenant.account.account_user import AccountUser
from se_elastic_schema.static.case import AssignReason, WorkflowEvent


class Workflow(SteelEyeComponentBaseModel):
    __traitFqn__ = "case/workflow"
    assignDescription: Optional[str] = Field(None)
    assignReason: Optional[AssignReason] = Field(None)
    assignReasonCustom: Optional[str] = Field(None)
    event: WorkflowEvent = Field(...)
    recordsAffected: Optional[List[str]] = Field(None)
    timestamp: datetime.datetime = Field(...)
    user: AccountUser = Field(..., traitType="reference")

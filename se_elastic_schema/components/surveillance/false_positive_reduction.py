from typing import List, Optional

from pydantic import Field

from se_elastic_schema.elastic_schema.core.base import SteelEyeComponentBaseModel


class PredictedClass(SteelEyeComponentBaseModel):
    __traitFqn__ = "surveillance/predicted_class"
    className: str = Field(...)
    confidenceScore: float = Field(...)


class FalsePositiveReduction(SteelEyeComponentBaseModel):
    __traitFqn__ = "surveillance/false_positive_reduction"
    excludedClassifications: Optional[List[PredictedClass]] = Field(None)
    excludedZones: Optional[List[PredictedClass]] = Field(None)

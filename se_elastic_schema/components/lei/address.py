from typing import Optional

from pydantic import Field

from se_elastic_schema.elastic_schema.core.base import SteelEyeComponentBaseModel


class Address(SteelEyeComponentBaseModel):
    __traitFqn__ = "lei/address"
    city: Optional[str] = Field(None, subFields={"alphanum": {"analyzer": "alphanum"}})
    country: Optional[str] = Field(None, subFields={"alpha": {"analyzer": "alphaonly"}})
    line1: Optional[str] = Field(None)
    line2: Optional[str] = Field(None)
    line3: Optional[str] = Field(None)
    line4: Optional[str] = Field(None)
    postalCode: Optional[str] = Field(None, subFields={"alphanum": {"analyzer": "alphanum"}})
    region: Optional[str] = Field(None)

from typing import Optional

from pydantic import Field

from se_elastic_schema.components.reference.person_communications import PersonCommunications
from se_elastic_schema.elastic_schema.core.base import SteelEyeComponentBaseModel


class Contact(SteelEyeComponentBaseModel):
    __traitFqn__ = "communication/contact"
    __traitDesc__ = (
        "Potential account/market person/firm, so it contains a subset of their properties. Could "
        "eventually replace the id fields in comms"
    )
    communications: Optional[PersonCommunications] = Field(None, description="contact details")
    name: Optional[str] = Field(
        None,
        description="name",
        subFields={"alphanum": {"analyzer": "alphanum"}},
        addTextProperty=True,
    )

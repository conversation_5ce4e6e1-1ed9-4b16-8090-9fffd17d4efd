from typing import Optional

from pydantic import Field

from se_elastic_schema.elastic_schema.core.base import SteelEyeComponentBaseModel
from se_elastic_schema.static.communication import TranscriptQuestionImportanceEnum


class Question(SteelEyeComponentBaseModel):
    """Questions about the transcript
    For example, if the conversation in the transcript is about 2008 banking crisis then:
    questionCategory: General
    question: What were the causes of the 2008 banking crisis?
    importance: MEDIUM"""

    __traitFqn__ = "communication/transcript_question"
    end: Optional[float] = Field(
        default=None,
        description="End Time of the transcript where question was raised.",
    )
    importance: Optional[TranscriptQuestionImportanceEnum] = Field(
        default=None, description="Question importance"
    )
    question: Optional[str] = Field(None, description="Question", addTextProperty=True)
    questionCategory: Optional[str] = Field(default=None, description="Question Category")
    start: Optional[float] = Field(
        default=None,
        description="Start Time of the transcript where the question was raised",
    )

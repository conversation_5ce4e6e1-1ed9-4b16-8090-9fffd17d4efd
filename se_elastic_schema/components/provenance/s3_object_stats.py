from typing import Dict, Optional

from pydantic import Field

from se_elastic_schema.components.timestamps.object_timestamps import ObjectTimestamps
from se_elastic_schema.elastic_schema.core.base import SteelEyeComponentBaseModel
from se_elastic_schema.elastic_schema.utils.types import Long
from se_elastic_schema.static.provenance import S3ObjStatus


class S3ObjectStats(SteelEyeComponentBaseModel):
    __traitFqn__ = "provenance/s3_object_stats"
    __traitDesc__ = "s3 file metadata"
    eTag: Optional[str] = Field(None)
    encrypted: Optional[bool] = Field(None)
    metadata: Optional[Dict[str, str]] = Field(None, indexed=False)
    sizeInBytes: Optional[Long] = Field(None)
    status: Optional[S3ObjStatus] = Field(None)
    storageClass: Optional[str] = Field(None)
    tags: Optional[str] = Field(None)
    timestamps: Optional[ObjectTimestamps] = Field(None)
    versionId: Optional[str] = Field(None)

from typing import Optional

from pydantic import Field

from se_elastic_schema.elastic_schema.core.base import SteelEyeComponentBaseModel
from se_elastic_schema.static.reference import NationalIdType


class NationalId(SteelEyeComponentBaseModel):
    __traitFqn__ = "reference/national_id"
    id: Optional[str] = Field(
        None, description="identifier", subFields={"alphanum": {"analyzer": "alphanum"}}
    )
    label: Optional[NationalIdType] = Field(None, description="identifier label")

from typing import List, Optional

from pydantic import Field

from se_elastic_schema.components.reference.identifier import Identifier
from se_elastic_schema.components.reference.national_id import NationalId
from se_elastic_schema.elastic_schema.core.base import SteelEyeComponentBaseModel
from se_elastic_schema.static.reference import ClientMandateType, MifirIdSubtype, MifirIdType


class PersonOfficialIdentifiers(SteelEyeComponentBaseModel):
    __traitFqn__ = "reference/person_official_identifiers"
    branchCountry: Optional[str] = Field(None, max_length=2, addTextProperty=True)
    clientMandate: Optional[ClientMandateType] = Field(None, addTextProperty=True)
    concatId: Optional[str] = Field(
        None,
        description="ESMA defined concat ID",
        subFields={"alphanum": {"analyzer": "alphanum"}},
    )
    employeeId: Optional[str] = Field(
        None,
        description="employee id",
        subFields={"alphanum": {"analyzer": "alphanum"}},
        addTextProperty=True,
    )
    fcaNo: Optional[str] = Field(
        None, description="fca number", subFields={"alphanum": {"analyzer": "alphanum"}}
    )
    mifirId: Optional[str] = Field(
        None,
        description="ESMA defined MiFIR ID",
        subFields={"alphanum": {"analyzer": "alphanum"}},
    )
    mifirIdSubType: Optional[MifirIdSubtype] = Field(None, description="ESMA defined MiFIR ID Type")
    mifirIdType: Optional[MifirIdType] = Field(None, description="ESMA defined MiFIR ID Type")
    nationalIds: Optional[List[NationalId]] = Field(
        None, description="list of national identifiers", elasticNestedType=True
    )
    passports: Optional[List[Identifier]] = Field(
        None, description="list of passports", elasticNestedType=True
    )
    traderIds: Optional[List[Identifier]] = Field(
        None, description="list of trader/system ids", elasticNestedType=True
    )

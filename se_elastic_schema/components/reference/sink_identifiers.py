from typing import List, Optional

from pydantic import Field

from se_elastic_schema.components.reference.identifier import Identifier
from se_elastic_schema.elastic_schema.core.base import SteelEyeComponentBaseModel


class SinkIdentifiers(SteelEyeComponentBaseModel):
    __traitFqn__ = "reference/sink_identifiers"
    orderFileIdentifiers: Optional[List[Identifier]] = Field(None, elasticNestedType=True)
    tradeFileIdentifiers: Optional[List[Identifier]] = Field(None, elasticNestedType=True)

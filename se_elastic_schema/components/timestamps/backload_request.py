import datetime
from typing import Optional

from pydantic import Field

from se_elastic_schema.elastic_schema.core.base import SteelEyeComponentBaseModel


class BackloadRequestTimestamps(SteelEyeComponentBaseModel):
    __traitFqn__ = "timestamps/backload_request"
    __traitDesc__ = "Track the request for a backload"
    from_: Optional[datetime.datetime] = Field(None, alias="from")
    to: Optional[datetime.datetime] = Field(None)
    requested: datetime.datetime = Field(...)
    completed: Optional[datetime.datetime] = Field(None)

---
apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: dev-enterprise-wakeup
  namespace: system
spec:
  interval: 1m
  chart:
    spec:
      chart: se-cronjob
      version: 0.1.2
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 1m
  values:
    owner: sre
    stack: dev-enterprise
    environment: dev
    serviceAccount:
      name: ooh-scale-down
      create: false
    command:
      - ./scale_up_eks_nodegroups.sh
    schedule: "30 3 * * 1-5"
    image:
      repository: ************.dkr.ecr.eu-west-1.amazonaws.com/ooh-scaler
      tag: 0.1.0
    env:
      EKS_CLUSTER_NAME: dev-enterprise
      DEFAULT_POOL_UP_MIN_SIZE: 1
      DEFAULT_POOL_UP_DESIRED_SIZE: 3
      DEFAULT_POOL_UP_MAX_SIZE: 6

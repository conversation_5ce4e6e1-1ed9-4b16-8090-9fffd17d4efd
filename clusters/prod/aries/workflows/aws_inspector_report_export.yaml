---
apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: aries-aws-inspector-report-export-workflow
  namespace: data-platform
spec:
  interval: 1m
  chart:
    spec:
      chart: se-job
      version: 0.1.0
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 1m
  values:
    owner: sec-ops
    stack: enterprise
    environment: prod

    image:
      repository: 698897937809.dkr.ecr.eu-west-1.amazonaws.com/conductor-client
      tag: 0.1.1

    env:
      CONDUCTOR_SERVER_URL: http://conductor-server.data-platform:8080/api

    configmap:
      volume:
        mount: true
        path: workflow.json
        key: workflow.json
      data:
        workflow.json: |-
          {
            "name": "aws_inspector_report_export",
            "description": "AWS Inspector Report Export Workflow",
            "version": 10,
            "tasks": [
              {
                "name": "aws_inspector_report_export",
                "taskReferenceName": "AWS Inspector Report Export Task",
                "inputParameters": {
                  "workflow": "${workflow.input.workflow}",
                  "io_param": "${workflow.input.io_param}"
                },
                "type": "SIMPLE",
                "startDelay": 0,
                "optional": false,
                "asyncComplete": false
              }
            ],
            "failureWorkflow": null,
            "schemaVersion": 2,
            "restartable": true,
            "workflowStatusListenerEnabled": false,
            "ownerEmail": "<EMAIL>",
            "timeoutPolicy": "TIME_OUT_WF",
            "timeoutSeconds": 0,
            "variables": {},
            "inputTemplate": {
              "workflow": {
                "name": "aws_inspector_report_export"
              }
            }
          }

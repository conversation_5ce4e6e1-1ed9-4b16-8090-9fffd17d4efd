import React from 'react'

import WidgetToolbarTitleComponent from 'view/components/widget-toolbar/title'

it('should render a string page title correctly', () => {
  const wrapper = shallow(<WidgetToolbarTitleComponent title='The Title' type='page' />)
  expect(wrapper).toMatchSnapshot()
})

it('should render a string widget title correctly', () => {
  const wrapper = shallow(<WidgetToolbarTitleComponent title='The Title' type='widget' />)
  expect(wrapper).toMatchSnapshot()
})

it('should render a node page title correctly', () => {
  const wrapper = shallow(<WidgetToolbarTitleComponent title={<div />} type='page' />)
  expect(wrapper).toMatchSnapshot()
})

it('should render a node widget title correctly', () => {
  const wrapper = shallow(<WidgetToolbarTitleComponent title={<div />} type='widget' />)
  expect(wrapper).toMatchSnapshot()
})

it('should render a message page title correctly', () => {
  const wrapper = shallow(
    <WidgetToolbarTitleComponent
      title={{ id: 'foo', defaultMessage: 'Foo' }}
      type='page'
    />
  )

  expect(wrapper).toMatchSnapshot()
})

it('should render a message widget title correctly', () => {
  const wrapper = shallow(
    <WidgetToolbarTitleComponent
      title={{ id: 'foo', defaultMessage: 'Foo' }}
      type='widget'
    />
  )

  expect(wrapper).toMatchSnapshot()
})

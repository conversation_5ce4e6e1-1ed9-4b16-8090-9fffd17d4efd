import React from 'react';

import noop from 'lodash/noop';

import { Component } from 'view/components/form/search';

it('should render correctly', () => {
  const wrapper = shallow(
    <Component
      form="Search"
      onSubmit={noop}
      handleSubmit={noop}
      loading={false}
      invalid={false}
      className="some-classname"
      initialValues={{ term: 'some term' }}
    />
  );

  expect(wrapper).toMatchSnapshot();
});

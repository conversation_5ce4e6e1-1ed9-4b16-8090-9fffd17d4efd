import * as accountUtil from 'util/account';
import ROUTES from 'constants/routes';

describe('getUserMainPagePathname', () => {
  const tests = [
    {
      it: 'should return correct path when has main dashboard',
      arg: {
        hasMainDashboard: true,
        hasComms: true,
        hasOrders: true,
        hasTransactionReporting: true,
        hasMarket: true,
        hasCaseManager: true,
      },
      expected: '/',
    },
    {
      it: 'should return correct path when has nothing',
      arg: {
        hasMainDashboard: false,
        hasComms: false,
        hasOrders: false,
        hasTransactionReporting: false,
        hasMarket: false,
        hasCaseManager: false,
      },
      expected: '/account/profile',
    },
    {
      it: 'should return correct path when has no main dashboard',
      arg: {
        hasMainDashboard: false,
        hasComms: true,
        hasOrders: true,
        hasTransactionReporting: true,
        hasMarket: true,
        hasCaseManager: true,
      },
      expected: '/communications/dashboard',
    },
    {
      it: 'should return correct path when has no comms',
      arg: {
        hasMainDashboard: false,
        hasComms: false,
        hasOrders: true,
        hasTransactionReporting: true,
        hasMarket: true,
        hasCaseManager: true,
      },
      expected: '/orders/dashboard',
    },
    {
      it: 'should return correct path when has no orders',
      arg: {
        hasMainDashboard: false,
        hasComms: false,
        hasOrders: false,
        hasTransactionReporting: true,
        hasMarket: true,
        hasCaseManager: true,
      },
      expected: '/transaction-reporting/dashboard',
    },
    {
      it: 'should return correct path when has no trades',
      arg: {
        hasMainDashboard: false,
        hasComms: false,
        hasOrders: false,
        hasTransactionReporting: true,
        hasMarket: true,
        hasCaseManager: true,
      },
      expected: '/transaction-reporting/dashboard',
    },
    {
      it: 'should return correct path when has no transaction reporting',
      arg: {
        hasMainDashboard: false,
        hasComms: false,
        hasOrders: false,
        hasTransactionReporting: false,
        hasMarket: true,
        hasCaseManager: true,
      },
      expected: '/market/firms',
    },
    {
      it: 'should return correct path when has no my market',
      arg: {
        hasMainDashboard: false,
        hasComms: false,
        hasOrders: false,
        hasTransactionReporting: false,
        hasMarket: false,
        hasCaseManager: true,
      },
      expected: ROUTES.CASE_MANAGER.INDEX,
    },
  ];

  tests.forEach(test => {
    it(test.it, () => {
      const actual = accountUtil.getUserMainPagePathname(test.arg);
      expect(actual).toEqual(test.expected);
    });
  });
});

describe('createUserIdFromEmail', () => {
  const tests = [
    {
      it: 'should handle a simple email address',
      arg: '<EMAIL>',
      expected: 'steve',
    },
    {
      it: 'should handle an email address with uppercase letters',
      arg: '<EMAIL>',
      expected: 'steve',
    },
    {
      it: 'should handle a complex email address',
      arg: '<EMAIL>',
      expected: 'steve+test1',
    },
    {
      it: 'should handle an empty email address',
      arg: '',
      expected: '',
    },
    {
      it: 'should handle a simple string',
      arg: 'steve',
      expected: 'steve',
    },
  ];

  tests.forEach(test => {
    it(test.it, () => {
      const actual = accountUtil.createUserIdFromEmail(test.arg);
      expect(actual).toEqual(test.expected);
    });
  });
});

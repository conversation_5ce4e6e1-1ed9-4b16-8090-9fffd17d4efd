import * as orderUtil from 'util/order';
import * as modelsEnums from '@steeleye/schema/models/enums';
import * as entityConstants from 'constants/entity';

describe('isAnyOrderEntityType', () => {
  const tests = [
    {
      arg: modelsEnums.ENTITY_TYPE_TRANSACTION,
      expected: false,
    },
    {
      arg: entityConstants.ENTITY_TYPE_ORDERS,
      expected: true,
    },
    {
      arg: modelsEnums.ENTITY_TYPE_ORDER_STATE,
      expected: true,
    },
    {
      arg: modelsEnums.ENTITY_TYPE_ORDER,
      expected: true,
    },
  ];

  tests.forEach(test => {
    it(`should return ${test.expected} for arg ${test.arg}`, () => {
      const actual = orderUtil.isAnyOrderEntityType(test.arg);
      expect(actual).toEqual(test.expected);
    });
  });
});

describe('isParfOrFill', () => {
  const tests = [
    {
      it: 'should return false for a new order',
      arg: {
        executionDetails: { orderStatus: modelsEnums.MIFID2_ORDER_STATUS_NEWO },
      },
      expected: false,
    },
    {
      it: 'should return true for a partial fill order',
      arg: {
        executionDetails: { orderStatus: modelsEnums.MIFID2_ORDER_STATUS_PARF },
      },
      expected: true,
    },
    {
      it: 'should return true for a fill order',
      arg: {
        executionDetails: { orderStatus: modelsEnums.MIFID2_ORDER_STATUS_FILL },
      },
      expected: true,
    },
  ];

  tests.forEach(test => {
    it(test.it, () => {
      const actual = orderUtil.isParfOrFill(test.arg);
      expect(actual).toEqual(test.expected);
    });
  });
});

apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: aries-sink-file-audit-finalize-worker
  namespace: ${cell_name}
spec:
  dependsOn:
  - name: ${cell_name}
  - name: ${cell_name}-elasticsearch
  - name: ${cell_name}-aries
  - name: conductor
  interval: 1m
  chart:
    spec:
      chart: se-aries-task-worker-deployment
      version: 1.0.8
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 5m
  values:
    cloud: ${cloud}
    environment: ${environment}
    owner: data-integration
    stack: ${cell_name}
    terminationGracePeriodSeconds: 3600
    image:
      repository: ${image_repo}/integration-generic-tasks
    env:
      DATA_EVENTS_TOPIC: aries.${cell_name}.data.events
      TASK_NAME: sink_file_audit_finalize
      TASK_WORKER_DOMAIN: ${cell_name}
    envFromConfigMapRef:
    - aries-platform-common-env-vars
    - oma-common-env-vars
    envFromSecretRef:
    - se-elasticsearch
    resources:
      limits:
        cpu: 0.75
        memory: 1.5Gi
      requests:
        cpu: 0.75
        memory: 1.5Gi
    nodeSelector:
      role: aries
      kubernetes.io/arch: arm64

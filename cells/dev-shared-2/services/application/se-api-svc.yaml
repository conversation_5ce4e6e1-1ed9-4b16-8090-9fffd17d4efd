apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: se-api-svc
  namespace: ${cell_name}
spec:
  chart:
    spec:
      chart: se-deployment
      version: 0.4.3
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 1m
  values:
    serviceMonitor:
      enabled: true
      additionalLabels:
        release: prometheus-stack
    service:
      metricsPort:
        enabled: true
    replicaCount: 1
    reloader:
      enabled: true
    image:
      tag: 2025.09.0030-dev.**********.9379a01.arm64 #{"$imagepolicy": "flux-system:se-api-svc:tag"}
    envFromSecret:
      - envName: TENANT_DB_PG_URL
        secretName: tenant-db-rds-creds
        secretKey: uri
      - envName: REDIS_PASSWORD
        secretName: redis
        secretKey: redis-password
    ingress:
      enabled: true
      annotations:
        alb.ingress.kubernetes.io/healthcheck-path: /api/v4.0/version
      hosts:
        - host: "*.${public_ingress_domain}"
          paths:
            - path: /api/v4.0/
              pathType: Prefix
    env:
      HIDE_LEXICA_UPGRADES: False
      TIKA_CLIENT_ONLY: true
      TIKA_SERVER_ENDPOINT: http://tika:9998
      TIKA_HOST: http://tika:9998
    envFromSecretRef:
      - api-apm-config
      - azure-open-ai
      - se-elasticsearch
      - kc-secrets
    envFromConfigMapRef:
      - api-common-env-vars-config
      - email-common-env-vars-config
      - redis-common-env-vars
      - efdh-env-vars

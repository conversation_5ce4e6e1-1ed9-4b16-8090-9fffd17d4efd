apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: iris
  namespace: ${cell_name}
spec:
  dependsOn:
  - name: ${cell_name}
  interval: 1m
  chart:
    spec:
      chart: se-deployment
      version: 0.4.3
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 1m
  values:
    owner: application
    stack: ${cell_name}
    environment: ${environment}
    cloud: ${cloud}
    replicaCount: 1
    image:
      repository: ${image_repo}/iris
      tag: 2.3.0-37654 #{"$imagepolicy": "flux-system:iris:tag"}
    port: 9180
    service:
      type: NodePort
    pdb:
      enabled: false
      minAvailable: 1
    livenessProbe:
      enabled: true
      path: /health
    readinessProbe:
      enabled: true
      path: /health
    nodeSelector:
      role: app
      kubernetes.io/arch: arm64
    tolerations:
    - key: nodepool
      operator: Equal
      value: ${cell_name}-app
      effect: NoSchedule
    podSecurityContext:
      runAsGroup: 101
      runAsUser: 101
      seccompProfile:
        type: RuntimeDefault
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      readOnlyRootFilesystem: false
    ingress:
      enabled: true
      annotations:
        kubernetes.io/ingress.class: alb
        alb.ingress.kubernetes.io/certificate-arn: ${cell_public_alb_cert_arn}
        alb.ingress.kubernetes.io/group.name: ${cell_name}-customer
        alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-TLS13-1-3-2021-06
        alb.ingress.kubernetes.io/load-balancer-name: ${cell_name}-customer-lb
        alb.ingress.kubernetes.io/tags: Fleet=${fleet_name},Cell=${cell_name}
        alb.ingress.kubernetes.io/target-type: ip
        alb.ingress.kubernetes.io/scheme: internet-facing
        alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=1800
        alb.ingress.kubernetes.io/group.order: "100"
        alb.ingress.kubernetes.io/wafv2-acl-arn: ${cell_waf_arn}
      hosts:
      - host: "*.${public_ingress_domain}"
        paths:
        - path: /
          pathType: Prefix

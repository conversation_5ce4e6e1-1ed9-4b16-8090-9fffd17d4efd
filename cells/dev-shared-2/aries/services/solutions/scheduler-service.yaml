apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: scheduler-service
  namespace: ${cell_name}
spec:
  dependsOn:
    - name: ${cell_name}
    - name: conductor
    - name: aries-scheduler-db
  interval: 1m
  chart:
    spec:
      chart: se-deployment
      version: 0.4.3
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 5m
  values:
    owner: solutions-engineering
    stack: ${cell_name}
    environment: ${cell_environment}
    image:
      repository: ${image_repo}/platform-core-services
      tag: 2025.03.0071
    args:
      - "scheduler"
    env:
      CONDUCTOR_API_URL: http://conductor-server.${cell_name}:8080/api
      DATA_PLATFORM_CONFIG_API_URL: http://aries-platform-config-api.platform
      STACK: ${cell_name}
      TASK_NAME: aries-scheduler-service
      SENTRY_ENABLED: 1
    envFromSecret:
      - envName: ARIES_SCHEDULER_DB_URL
        secretName: aries-scheduler-rds-creds
        secretKey: uri
    envFromConfigMapRef:
      - oma-common-env-vars
    resources:
      limits:
        memory: 104Mi
      requests:
        cpu: 0.1
        memory: 104Mi
    nodeSelector:
      role: app
      kubernetes.io/arch: arm64
      karpenter.sh/capacity-type: on-demand
    tolerations:
      - key: nodepool
        operator: Equal
        value: ${cell_name}-app
        effect: NoSchedule

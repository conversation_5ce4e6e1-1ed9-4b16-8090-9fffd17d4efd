from pathlib import PosixPath
from typing import List

import pytest

from se_core_tasks.core.core_dataclasses import CloudFile
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.core.core_dataclasses import S3Action
from se_core_tasks.transform.extract_path.cloud_file_list_from_extract_path_result_list import (
    Params,
)
from se_core_tasks.transform.extract_path.cloud_file_list_from_extract_path_result_list import (
    run_cloud_file_list_from_extract_path_result_list,
)
from se_core_tasks.transform.extract_path.cloud_file_list_from_extract_path_result_list import (
    SkipIfExtractPathListEmpty,
)


@pytest.fixture()
def source_extract_result_list() -> list:
    """Creates a sample extract_result_list"""
    return [
        ExtractPathResult(
            path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/Avaya.png"
            )
        ),
        ExtractPathResult(
            path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/000000000004405.opus"
            )
        ),
        ExtractPathResult(
            path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/000000000004444.opus"
            )
        ),
    ]


@pytest.fixture()
def source_single_extract_result() -> ExtractPathResult:
    """Creates a single ExtractPathResult"""
    return ExtractPathResult(
        path=PosixPath(
            "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/Avaya.png"
        )
    )


@pytest.fixture()
def expected_result_no_suffix_param() -> List[CloudFile]:
    """Expected result when the suffix param is not supplied"""
    return [
        CloudFile(
            file_path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/Avaya.png"
            ),
            bucket_name="dummy.dev.steeleye.co",
            key_name="attachments/test/Avaya.png",
            action=S3Action.UPLOAD,
            bytes=0,
        ),
        CloudFile(
            file_path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/000000000004405.opus"
            ),
            bucket_name="dummy.dev.steeleye.co",
            key_name="attachments/test/000000000004405.opus",
            action=S3Action.UPLOAD,
            bytes=0,
        ),
        CloudFile(
            file_path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/000000000004444.opus"
            ),
            bucket_name="dummy.dev.steeleye.co",
            key_name="attachments/test/000000000004444.opus",
            action=S3Action.UPLOAD,
            bytes=0,
        ),
    ]


@pytest.fixture()
def expected_result_single_extract_result():
    return [
        CloudFile(
            file_path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/Avaya.png"
            ),
            bucket_name="dummy.dev.steeleye.co",
            key_name="attachments/test/Avaya.png",
            action=S3Action.UPLOAD,
            bytes=0,
        )
    ]


@pytest.fixture()
def expected_result_opus_suffix_param() -> list:
    """Expected result when the suffix param is '.opus''"""
    return [
        CloudFile(
            file_path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/000000000004405.opus"
            ),
            bucket_name="dummy.dev.steeleye.co",
            key_name="attachments/test/000000000004405.opus",
            action=S3Action.UPLOAD,
            bytes=0,
        ),
        CloudFile(
            file_path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/000000000004444.opus"
            ),
            bucket_name="dummy.dev.steeleye.co",
            key_name="attachments/test/000000000004444.opus",
            action=S3Action.UPLOAD,
            bytes=0,
        ),
    ]


@pytest.fixture()
def params_fixture() -> Params:
    return Params(key_prefix="attachments/test")


class TestCloudFileListFromExtractPathResultList:
    """Class to hold all the test cases for CloudFileListFromExtractPathResultList"""

    def test_task_with_empty_extract_result_list(self, params_fixture: Params):
        """Tests the task when extract_result_list is empty"""

        with pytest.raises(SkipIfExtractPathListEmpty):
            run_cloud_file_list_from_extract_path_result_list(
                extract_result_list=None,
                params=params_fixture,
                realm="dummy.dev.steeleye.co",
            )

    def test_task_with_no_suffix_filter(
        self,
        source_extract_result_list: List[ExtractPathResult],
        expected_result_no_suffix_param: List[CloudFile],
        params_fixture: Params,
    ):
        """Tests the task without a suffix filter"""

        result = run_cloud_file_list_from_extract_path_result_list(
            extract_result_list=source_extract_result_list,
            params=params_fixture,
            realm="dummy.dev.steeleye.co",
        )
        assert result == expected_result_no_suffix_param

    def test_task_with_single_extract_result(
        self,
        source_single_extract_result: List[ExtractPathResult],
        expected_result_single_extract_result: List[CloudFile],
        params_fixture: Params,
    ):
        """Test for the case where the source is not a list of ExtractPathResult objects, but
        a single ExtractPathResult"""

        result = run_cloud_file_list_from_extract_path_result_list(
            extract_result_list=source_single_extract_result,
            params=params_fixture,
            realm="dummy.dev.steeleye.co",
        )

        assert result == expected_result_single_extract_result

    def test_task_with_opus_suffix_filter(
        self,
        source_extract_result_list: List[ExtractPathResult],
        expected_result_opus_suffix_param: List[CloudFile],
    ):
        """Tests the task with a suffix filter for .opus files"""

        params = Params(key_prefix="attachments/test", file_suffix=".opus")
        result = run_cloud_file_list_from_extract_path_result_list(
            extract_result_list=source_extract_result_list,
            params=params,
            realm="dummy.dev.steeleye.co",
        )

        assert result == expected_result_opus_suffix_param

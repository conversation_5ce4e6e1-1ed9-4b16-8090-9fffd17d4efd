import pandas as pd
import pytest

from swarm_tasks.generic.string.extract_part_from_delimited_text import (
    ExtractPartFromDelimitedText,
)
from swarm_tasks.generic.string.extract_part_from_delimited_text import Params


@pytest.fixture()
def source_frame_empty() -> pd.DataFrame:
    """Represents an empty source frame"""
    df = pd.DataFrame({}, columns=["source_col"])
    return df


@pytest.fixture()
def source_dataframe() -> pd.DataFrame:
    """Represents a source frame with data"""
    df = pd.DataFrame(
        {"source_col": ["VFF-17SEP21P-12", "F1", "PUT-13FEB23P-3", pd.NA]}
    )
    return df


class TestExtractPartFromDelimitedText:
    """Test suite for ExtractPartFromDelimitedText"""

    def test_source_frame_empty(self, source_frame_empty):
        """Tests the task for the case where the source frame is empty"""
        params = Params(
            source_attribute="source_col",
            target_attribute="target_col",
            delimiter="-",
            index_of_part_to_extract=0,
        )
        task = ExtractPartFromDelimitedText(name="test-map-static", params=params)
        result = task.execute(source_frame_empty, params)
        expected_result = pd.DataFrame({}, columns=[params.target_attribute])
        assert result.equals(expected_result)

    def test_source_frame_non_empty(self, source_dataframe):
        """Tests the task for the case where the source frame is empty"""
        params = Params(
            source_attribute="source_col",
            target_attribute="target_col",
            delimiter="-",
            index_of_part_to_extract=0,
        )
        task = ExtractPartFromDelimitedText(name="test-map-static", params=params)
        result = task.execute(source_dataframe, params)
        expected_result = pd.DataFrame(
            {params.target_attribute: ["VFF", "F1", "PUT", pd.NA]}
        )
        assert result.equals(expected_result)

    def test_source_frame_non_empty_null_if_no_delimiter(self, source_dataframe):
        """Tests the task for the case where the source frame is empty"""
        params = Params(
            source_attribute="source_col",
            target_attribute="target_col",
            delimiter="-",
            index_of_part_to_extract=0,
            null_if_no_delimiter=True,
        )
        task = ExtractPartFromDelimitedText(name="test-map-static", params=params)
        result = task.execute(source_dataframe, params)
        expected_result = pd.DataFrame(
            {params.target_attribute: ["VFF", pd.NA, "PUT", pd.NA]}
        )
        assert result.equals(expected_result)

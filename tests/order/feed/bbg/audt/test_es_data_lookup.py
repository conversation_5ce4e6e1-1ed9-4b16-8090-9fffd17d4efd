import pandas as pd
import pytest
from addict import addict

from se_trades_tasks.order.feed.bbg.audt.es_data_lookup import (
    run_bbg_audt_es_data_lookup,
    PREFIX,
    get_lookup_columns,
)
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.transformations.bbg.audt.static import SourceColumns
from se_trades_tasks.order_and_tr.party.static import PartiesFields


class MockESClient:
    MAX_TERMS_SIZE = 1000

    meta = addict.Dict(id="&id")

    def __init__(self, mock_es_scroll: pd.DataFrame):
        self.mock_es_scroll = mock_es_scroll

    def scroll(self, **kwargs):
        return self.mock_es_scroll


@pytest.fixture
def input_source_frame():
    return pd.DataFrame(
        {
            SourceColumns.TS_ORD_NUM: ["1", "2", pd.NA],
            SourceColumns.AUDIT_ID_TKT: [pd.NA, "B1", "A1"],
            SourceColumns.TKT_NUM: [pd.NA, "TKT_B1", "TKT_A1"],
            SourceColumns.ACCOUNT: ["A", "B", "C"],
            SourceColumns.IDENTIFIER: ["A", "B", "C"],
        }
    )


@pytest.fixture
def normal_es_mock_hits():
    return pd.DataFrame(
        {
            OrderColumns.ID: ["1", "B1_B", "A1_C_TKT_A1_C"],
            OrderColumns.DATE: ["2024-02-04", "2024-02-05", "2024-02-07"],
            OrderColumns.TIMESTAMPS_ORDER_RECEIVED: [
                "2024-02-04T12:00:00.000000Z",
                "2024-02-05T12:00:00.000000Z",
                "2024-02-07T12:00:00.000000Z",
            ],
            OrderColumns.TIMESTAMPS_ORDER_SUBMITTED: [
                "2024-02-04T15:00:00.000000Z",
                "2024-02-05T15:00:00.000000Z",
                "2024-02-07T15:00:00.000000Z",
            ],
            MockESClient.meta.id: ["dummy"] * 3,
            OrderColumns.MARKET_IDENTIFIERS: ["dummy"] * 3,
            PartiesFields.PARTIES_EXEC_ENTITY_FILE_ID: ["dummy"] * 3,
            PartiesFields.PARTIES_BUYER_FILE_ID: ["dummy"] * 3,
            PartiesFields.PARTIES_BUYER_DECISION_MAKER_FILE_ID: ["dummy"] * 3,
            PartiesFields.PARTIES_SELLER_FILE_ID: ["dummy"] * 3,
            PartiesFields.PARTIES_SELLER_DECISION_MAKER_FILE_ID: ["dummy"] * 3,
            PartiesFields.PARTIES_CLIENT_FILE_ID: ["dummy"] * 3,
            PartiesFields.PARTIES_CP_FILE_ID: ["dummy"] * 3,
            PartiesFields.PARTIES_TRADER_FILE_ID: ["dummy"] * 3,
            PartiesFields.PARTIES_INVEST_DEC_WITHIN_FIRM_FILE_ID: ["dummy"] * 3,
            PartiesFields.PARTIES_EXEC_WITHIN_FIRM_FILE_ID: ["dummy"] * 3,
        }
    )


@pytest.fixture
def empty_es_mock_hits():
    return pd.DataFrame()


@pytest.fixture
def normal_expected_outcome():
    return pd.DataFrame(
        {
            PREFIX + OrderColumns.ID: ["1", "B1_B", "A1_C_TKT_A1_C"],
            PREFIX + OrderColumns.DATE: ["2024-02-04", "2024-02-05", "2024-02-07"],
            PREFIX
            + OrderColumns.TIMESTAMPS_ORDER_RECEIVED: [
                "2024-02-04T12:00:00.000000Z",
                "2024-02-05T12:00:00.000000Z",
                "2024-02-07T12:00:00.000000Z",
            ],
            PREFIX
            + OrderColumns.TIMESTAMPS_ORDER_SUBMITTED: [
                "2024-02-04T15:00:00.000000Z",
                "2024-02-05T15:00:00.000000Z",
                "2024-02-07T15:00:00.000000Z",
            ],
            PREFIX + MockESClient.meta.id: ["dummy"] * 3,
            PREFIX + OrderColumns.MARKET_IDENTIFIERS: ["dummy"] * 3,
            PREFIX + PartiesFields.PARTIES_EXEC_ENTITY_FILE_ID: ["dummy"] * 3,
            PREFIX + PartiesFields.PARTIES_BUYER_FILE_ID: ["dummy"] * 3,
            PREFIX + PartiesFields.PARTIES_BUYER_DECISION_MAKER_FILE_ID: ["dummy"] * 3,
            PREFIX + PartiesFields.PARTIES_SELLER_FILE_ID: ["dummy"] * 3,
            PREFIX + PartiesFields.PARTIES_SELLER_DECISION_MAKER_FILE_ID: ["dummy"] * 3,
            PREFIX + PartiesFields.PARTIES_CLIENT_FILE_ID: ["dummy"] * 3,
            PREFIX + PartiesFields.PARTIES_CP_FILE_ID: ["dummy"] * 3,
            PREFIX + PartiesFields.PARTIES_TRADER_FILE_ID: ["dummy"] * 3,
            PREFIX + PartiesFields.PARTIES_INVEST_DEC_WITHIN_FIRM_FILE_ID: ["dummy"] * 3,
            PREFIX + PartiesFields.PARTIES_EXEC_WITHIN_FIRM_FILE_ID: ["dummy"] * 3,
        }
    )


@pytest.fixture
def empty_expected_outcome():
    return pd.DataFrame(
        data=pd.NA,
        index=list(range(3)),
        columns=get_lookup_columns(es_client=MockESClient, base=True, inherit=True, prefix=True),
    )


class TestBBGAudtESDataLookup:
    @pytest.mark.parametrize(
        "es_mock_hits,expected_outcome",
        [
            ("empty_es_mock_hits", "empty_expected_outcome"),
            ("normal_es_mock_hits", "normal_expected_outcome"),
        ],
    )
    def test_end_to_end(self, request, input_source_frame, es_mock_hits, expected_outcome):

        mock_es_client = MockESClient(mock_es_scroll=request.getfixturevalue(es_mock_hits))

        result = run_bbg_audt_es_data_lookup(
            source_frame=input_source_frame, tenant="dummy_tenant", es_client=mock_es_client
        )

        pd.testing.assert_frame_equal(result, request.getfixturevalue(expected_outcome))

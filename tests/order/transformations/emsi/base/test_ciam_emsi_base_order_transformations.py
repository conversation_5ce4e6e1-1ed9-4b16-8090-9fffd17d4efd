import pandas as pd
import pytest

from se_core_tasks.core.auditor import Auditor
from se_trades_tasks.order.transformations.emsi.base.ciam_emsi_base_order_transformations import (
    CIAMEMSIBaseOrderTransformations,
)
from se_trades_tasks.order.transformations.emsi.base.emsi_base_order_transformations import SkipNoOrdersToProcess


class MockESFound:
    @staticmethod
    def search(**kwargs):
        return {"hits": {"hits": [{"_source": {"firmIdentifiers": {"lei": "dummylei"}}}]}}


class TestCIAMEMSIBaseOrderTransformations:
    def test_end_to_end_fills(self, ciam_source_frame: pd.DataFrame, ciam_fills_expected_frame: pd.DataFrame):
        task = CIAMEMSIBaseOrderTransformations(
            source_frame=ciam_source_frame,
            source_file_uri="s3://test.dev.steeleye.co/flows/order-feed-emsi-base/test_file_fills.csv",
            tenant="dummy",
            es_client=MockESFound(),
            auditor=Auditor(),
        )
        result = task.process()

        pd.testing.assert_frame_equal(result, ciam_fills_expected_frame, check_dtype=False)

    def test_end_to_end_orders(self, ciam_source_frame: pd.DataFrame, ciam_orders_expected_frame: pd.DataFrame):
        task = CIAMEMSIBaseOrderTransformations(
            source_frame=ciam_source_frame,
            source_file_uri="s3://test.dev.steeleye.co/flows/order-feed-emsi-base/test_file_orders.csv",
            tenant="dummy",
            es_client=MockESFound(),
            auditor=Auditor(),
        )
        result = task.process()

        pd.testing.assert_frame_equal(result, ciam_orders_expected_frame, check_dtype=False)

    def test_end_to_end_orders_no_cancels(self, source_frame: pd.DataFrame):
        with pytest.raises(SkipNoOrdersToProcess) as e:
            CIAMEMSIBaseOrderTransformations(
                source_frame=source_frame,
                source_file_uri="s3://test.dev.steeleye.co/flows/order-feed-emsi-base/test_file_orders.csv",
                tenant="dummy",
                es_client=MockESFound(),
                auditor=Auditor(),
            )

        assert e.match("No orders to process after keeping only canceled orders.")

import pandas as pd
import pytest
from addict import addict

from se_trades_tasks.order.transformations.universal.steeleye_universal_order_blotter_schema import (
    CLIENT_ID,
    COUNTERPARTY_ID,
    EXECUTING_ENTITY_ID,
    INVESTMENT_DECISION_MAKER,
    TRADER_ID,
)
from se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.blotter_party_identifiers import (
    Params,
    DFColumns,
    BlotterPartyIdentifiers,
    run_blotter_party_identifiers,
)
from se_trades_tasks.order_and_tr.party.static import PartiesFields


@pytest.fixture()
def params() -> Params:
    params = Params(
        firm_id=None,
        override_discretionary=False,
        override_non_discretionary=False,
        override_non_lei_prefix=None,
    )
    return params


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    df = pd.DataFrame()
    return df


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            CLIENT_ID.normalized_column_name: [
                "FJYYT1HYBN8ONKLZC633",
                "********************",
                pd.NA,
            ],
            COUNTERPARTY_ID.normalized_column_name: [
                "W22LROWP2IHZNBB6K528",
                "W22LROWP2IHZNBB6K528",
                pd.NA,
            ],
            EXECUTING_ENTITY_ID.normalized_column_name: [
                "213800NR44FZFC9RPO56",
                "213800NR44FZFC9RPO56",
                pd.NA,
            ],
            INVESTMENT_DECISION_MAKER.normalized_column_name: [
                "219694",
                "219694",
                pd.NA,
            ],
            TRADER_ID.normalized_column_name: ["301791", "301791", pd.NA],
            DFColumns.EXECUTION_WITHIN_FIRM: ["randomID", pd.NA, pd.NA],
            PartiesFields.TRX_DTL_BUY_SELL_INDICATOR: ["BUYI", "SELL", pd.NA],
            DFColumns.TRADING_CAPACITY: ["AOTC", pd.NA, pd.NA],
        }
    )
    return df


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            CLIENT_ID.normalized_column_name: [
                "FJYYT1HYBN8ONKLZC633",
                "********************",
                pd.NA,
            ],
            TRADER_ID.normalized_column_name: ["301791", "301791", pd.NA],
            PartiesFields.TRX_DTL_BUY_SELL_INDICATOR: ["BUYI", "SELL", pd.NA],
            DFColumns.TRADING_CAPACITY: ["AOTC", pd.NA, pd.NA],
        }
    )
    return df


@pytest.fixture
def mock_get_tenant_lei() -> pd.DataFrame:
    return pd.DataFrame({DFColumns.TENANT_LEI: ["account_firm_lei"] * 3})


# Execution Within Firm tests


@pytest.fixture()
def exec_wf_all_data_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            DFColumns.CLIENT_MANDATE: ["DISCRETIONARY"] * 5,
            INVESTMENT_DECISION_MAKER.normalized_column_name: [
                "Inv1",
                "Inv2",
                "Inv3",
                "Inv4",
                "Inv5",
            ],
            TRADER_ID.normalized_column_name: [
                "TraderId1",
                "TraderId2",
                "TraderId3",
                "TraderId4",
                "TraderId5",
            ],
            DFColumns.EXECUTION_WITHIN_FIRM: [pd.NA] * 5,
        }
    )
    return df


@pytest.fixture()
def exec_wf_clnt_nore_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            DFColumns.CLIENT_MANDATE: ["DISCRETIONARY"] * 5,
            INVESTMENT_DECISION_MAKER.normalized_column_name: [pd.NA] * 5,
            TRADER_ID.normalized_column_name: [pd.NA] * 5,
            DFColumns.EXECUTION_WITHIN_FIRM: [pd.NA] * 5,
        }
    )
    return df


@pytest.fixture()
def exec_wf_partial_clnt_nore_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            DFColumns.CLIENT_MANDATE: ["DISCRETIONARY"] * 5,
            INVESTMENT_DECISION_MAKER.normalized_column_name: [
                "Inv1",
                "Inv2",
                pd.NA,
                "Inv4",
                pd.NA,
            ],
            TRADER_ID.normalized_column_name: [
                "TraderId1",
                pd.NA,
                pd.NA,
                "TraderId4",
                "TraderId5",
            ],
            DFColumns.EXECUTION_WITHIN_FIRM: [pd.NA, pd.NA, pd.NA, pd.NA, "Exec1"],
        }
    )
    return df


@pytest.fixture()
def deal_mask_all_true() -> pd.Series:
    data = [True] * 5
    series = pd.Series({"transactionDetails.tradingCapacity": data})
    return series


@pytest.fixture()
def deal_mask_all_false() -> pd.Series:
    data = [False] * 5
    series = pd.Series({"transactionDetails.tradingCapacity": data})
    return series


@pytest.fixture()
def all_cols_expected_result() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "marketIdentifiers.parties": [
                "[{'labelId': 'lei:fjyyt1hybn8onklzc633', 'path': 'buyer', 'type': "
                "<IdentifierType.ARRAY>}, "
                "{'labelId': 'lei:213800nr44fzfc9rpo56', 'path': "
                "'buyerDecisionMaker', 'type': <IdentifierType.ARRAY>}, "
                "{'labelId': 'lei:213800nr44fzfc9rpo56', "
                "'path': 'reportDetails.executingEntity', 'type': "
                "<IdentifierType.OBJECT>}, {'labelId': 'lei:w22lrowp2ihznbb6k528', "
                "'path': 'seller', 'type': <IdentifierType.ARRAY>}, {'labelId': "
                "'lei:w22lrowp2ihznbb6k528', 'path': 'counterparty', 'type': "
                "<IdentifierType.OBJECT>}, {'labelId': 'account:219694', 'path': "
                "'tradersAlgosWaiversIndicators.investmentDecisionWithinFirm', "
                "'type': "
                "<IdentifierType.OBJECT>}, "
                "{'labelId': 'account:randomid', 'path': "
                "'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': "
                "<IdentifierType.OBJECT>}, "
                "{'labelId': 'lei:fjyyt1hybn8onklzc633', 'path': "
                "'clientIdentifiers.client', 'type': "
                "<IdentifierType.ARRAY>}, "
                "{'labelId': 'account:301791', 'path': 'trader', 'type': "
                "<IdentifierType.ARRAY>}]",
                "[{'labelId': 'lei:w22lrowp2ihznbb6k528', 'path': 'buyer', 'type': "
                "<IdentifierType.ARRAY>}, "
                "{'labelId': 'lei:213800nr44fzfc9rpo56', "
                "'path': 'reportDetails.executingEntity', 'type': "
                "<IdentifierType.OBJECT>}, {'labelId': 'lei:ak79l73snc8gpsp6pe61', "
                "'path': 'seller', 'type': <IdentifierType.ARRAY>}, "
                "{'labelId': 'lei:213800nr44fzfc9rpo56', 'path': "
                "'sellerDecisionMaker', 'type': <IdentifierType.ARRAY>}, "
                "{'labelId': "
                "'lei:w22lrowp2ihznbb6k528', 'path': 'counterparty', 'type': "
                "<IdentifierType.OBJECT>}, {'labelId': 'account:219694', 'path': "
                "'tradersAlgosWaiversIndicators.investmentDecisionWithinFirm', "
                "'type': "
                "<IdentifierType.OBJECT>}, "
                "{'labelId': 'account:301791', 'path': "
                "'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': "
                "<IdentifierType.OBJECT>}, "
                "{'labelId': 'lei:ak79l73snc8gpsp6pe61', 'path': "
                "'clientIdentifiers.client', 'type': "
                "<IdentifierType.ARRAY>}, "
                "{'labelId': 'account:301791', 'path': 'trader', 'type': "
                "<IdentifierType.ARRAY>}]",
                "[{'labelId': 'account:account_firm_lei', "
                "'path': 'reportDetails.executingEntity', 'type': "
                "<IdentifierType.OBJECT>}, {'labelId': 'clnt:nore', 'path': 'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': <IdentifierType.OBJECT>}]",  # noqa E501
            ],
            "reportDetails.executingEntity.fileIdentifier": [
                "lei:213800nr44fzfc9rpo56",
                "lei:213800nr44fzfc9rpo56",
                "account:account_firm_lei",
            ],
            "buyerFileIdentifier": [
                "lei:fjyyt1hybn8onklzc633",
                "lei:w22lrowp2ihznbb6k528",
                pd.NA,
            ],
            "sellerFileIdentifier": [
                "lei:w22lrowp2ihznbb6k528",
                "lei:ak79l73snc8gpsp6pe61",
                pd.NA,
            ],
            "counterpartyFileIdentifier": [
                "lei:w22lrowp2ihznbb6k528",
                "lei:w22lrowp2ihznbb6k528",
                pd.NA,
            ],
            "sellerDecisionMakerFileIdentifier": [
                pd.NA,
                "lei:213800nr44fzfc9rpo56",
                pd.NA,
            ],
            "buyerDecisionMakerFileIdentifier": [
                "lei:213800nr44fzfc9rpo56",
                pd.NA,
                pd.NA,
            ],
            "tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier": [
                "account:219694",
                "account:219694",
                pd.NA,
            ],
            "tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier": [
                "account:randomid",
                "account:301791",
                "clnt:nore",
            ],
            "clientFileIdentifier": [
                "lei:fjyyt1hybn8onklzc633",
                "lei:ak79l73snc8gpsp6pe61",
                pd.NA,
            ],
            "traderFileIdentifier": ["account:301791", "account:301791", pd.NA],
        }
    )


@pytest.fixture()
def all_cols_expected_result_preserve_original() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "marketIdentifiers.parties": [
                "[{'labelId': 'lei:FJYYT1HYBN8ONKLZC633', 'path': 'buyer', 'type': "
                "<IdentifierType.ARRAY>}, "
                "{'labelId': 'lei:213800NR44FZFC9RPO56', 'path': "
                "'buyerDecisionMaker', 'type': <IdentifierType.ARRAY>}, "
                "{'labelId': 'lei:213800NR44FZFC9RPO56', "
                "'path': 'reportDetails.executingEntity', 'type': "
                "<IdentifierType.OBJECT>}, {'labelId': 'lei:W22LROWP2IHZNBB6K528', "
                "'path': 'seller', 'type': <IdentifierType.ARRAY>}, {'labelId': "
                "'lei:W22LROWP2IHZNBB6K528', 'path': 'counterparty', 'type': "
                "<IdentifierType.OBJECT>}, {'labelId': 'account:219694', 'path': "
                "'tradersAlgosWaiversIndicators.investmentDecisionWithinFirm', "
                "'type': "
                "<IdentifierType.OBJECT>}, "
                "{'labelId': 'account:randomID', 'path': "
                "'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': "
                "<IdentifierType.OBJECT>}, "
                "{'labelId': 'lei:FJYYT1HYBN8ONKLZC633', 'path': "
                "'clientIdentifiers.client', 'type': "
                "<IdentifierType.ARRAY>}, "
                "{'labelId': 'account:301791', 'path': 'trader', 'type': "
                "<IdentifierType.ARRAY>}]",
                "[{'labelId': 'lei:W22LROWP2IHZNBB6K528', 'path': 'buyer', 'type': "
                "<IdentifierType.ARRAY>}, "
                "{'labelId': 'lei:213800NR44FZFC9RPO56', "
                "'path': 'reportDetails.executingEntity', 'type': "
                "<IdentifierType.OBJECT>}, {'labelId': 'lei:********************', "
                "'path': 'seller', 'type': <IdentifierType.ARRAY>}, "
                "{'labelId': 'lei:213800NR44FZFC9RPO56', 'path': "
                "'sellerDecisionMaker', 'type': <IdentifierType.ARRAY>}, "
                "{'labelId': "
                "'lei:W22LROWP2IHZNBB6K528', 'path': 'counterparty', 'type': "
                "<IdentifierType.OBJECT>}, {'labelId': 'account:219694', 'path': "
                "'tradersAlgosWaiversIndicators.investmentDecisionWithinFirm', "
                "'type': "
                "<IdentifierType.OBJECT>}, "
                "{'labelId': 'account:301791', 'path': "
                "'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': "
                "<IdentifierType.OBJECT>}, "
                "{'labelId': 'lei:********************', 'path': "
                "'clientIdentifiers.client', 'type': "
                "<IdentifierType.ARRAY>}, "
                "{'labelId': 'account:301791', 'path': 'trader', 'type': "
                "<IdentifierType.ARRAY>}]",
                "[{'labelId': 'account:account_firm_lei', "
                "'path': 'reportDetails.executingEntity', 'type': "
                "<IdentifierType.OBJECT>}, {'labelId': 'clnt:nore', 'path': 'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': <IdentifierType.OBJECT>}]",  # noqa E501
            ],
            "reportDetails.executingEntity.fileIdentifier": [
                "lei:213800NR44FZFC9RPO56",
                "lei:213800NR44FZFC9RPO56",
                "account:account_firm_lei",
            ],
            "buyerFileIdentifier": [
                "lei:FJYYT1HYBN8ONKLZC633",
                "lei:W22LROWP2IHZNBB6K528",
                pd.NA,
            ],
            "sellerFileIdentifier": [
                "lei:W22LROWP2IHZNBB6K528",
                "lei:********************",
                pd.NA,
            ],
            "counterpartyFileIdentifier": [
                "lei:W22LROWP2IHZNBB6K528",
                "lei:W22LROWP2IHZNBB6K528",
                pd.NA,
            ],
            "sellerDecisionMakerFileIdentifier": [
                pd.NA,
                "lei:213800NR44FZFC9RPO56",
                pd.NA,
            ],
            "buyerDecisionMakerFileIdentifier": [
                "lei:213800NR44FZFC9RPO56",
                pd.NA,
                pd.NA,
            ],
            "tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier": [
                "account:219694",
                "account:219694",
                pd.NA,
            ],
            "tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier": [
                "account:randomID",
                "account:301791",
                "clnt:nore",
            ],
            "clientFileIdentifier": [
                "lei:FJYYT1HYBN8ONKLZC633",
                "lei:********************",
                pd.NA,
            ],
            "traderFileIdentifier": ["account:301791", "account:301791", pd.NA],
        }
    )


@pytest.fixture()
def all_cols_expected_result_preserve_fallback_original() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "marketIdentifiers.parties": [
                "[{'labelId': 'lei:fjyyt1hybn8onklzc633', 'path': 'buyer', 'type': <IdentifierType.ARRAY>},"
                " {'labelId': 'lei:213800nr44fzfc9rpo56', 'path': 'buyerDecisionMaker',"
                " 'type': <IdentifierType.ARRAY>}, {'labelId': 'lei:213800nr44fzfc9rpo56',"
                " 'path': 'reportDetails.executingEntity', 'type': <IdentifierType.OBJECT>},"
                " {'labelId': 'lei:w22lrowp2ihznbb6k528', 'path': 'seller', 'type': <IdentifierType.ARRAY>},"
                " {'labelId': 'lei:w22lrowp2ihznbb6k528', 'path': 'counterparty',"
                " 'type': <IdentifierType.OBJECT>}, {'labelId': 'account:219694',"
                " 'path': 'tradersAlgosWaiversIndicators.investmentDecisionWithinFirm',"
                " 'type': <IdentifierType.OBJECT>}, {'labelId': 'account:randomid',"
                " 'path': 'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': <IdentifierType.OBJECT>},"
                " {'labelId': 'lei:fjyyt1hybn8onklzc633', 'path': 'clientIdentifiers.client',"
                " 'type': <IdentifierType.ARRAY>}, {'labelId': 'account:301791', 'path': 'trader',"
                " 'type': <IdentifierType.ARRAY>}]",
                "[{'labelId': 'lei:w22lrowp2ihznbb6k528', 'path': 'buyer', 'type': <IdentifierType.ARRAY>},"
                " {'labelId': 'lei:213800nr44fzfc9rpo56', 'path': 'reportDetails.executingEntity',"
                " 'type': <IdentifierType.OBJECT>}, {'labelId': 'lei:ak79l73snc8gpsp6pe61', 'path': 'seller',"
                " 'type': <IdentifierType.ARRAY>}, {'labelId': 'lei:213800nr44fzfc9rpo56',"
                " 'path': 'sellerDecisionMaker', 'type': <IdentifierType.ARRAY>},"
                " {'labelId': 'lei:w22lrowp2ihznbb6k528', 'path': 'counterparty', 'type': <IdentifierType.OBJECT>},"
                " {'labelId': 'account:219694', 'path': 'tradersAlgosWaiversIndicators.investmentDecisionWithinFirm',"
                " 'type': <IdentifierType.OBJECT>}, {'labelId': 'account:301791',"
                " 'path': 'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': <IdentifierType.OBJECT>},"
                " {'labelId': 'lei:ak79l73snc8gpsp6pe61', 'path': 'clientIdentifiers.client',"
                " 'type': <IdentifierType.ARRAY>}, {'labelId': 'account:301791', 'path': 'trader',"
                " 'type': <IdentifierType.ARRAY>}]",
                "[{'labelId': 'account:account_firm_lei', 'path': 'reportDetails.executingEntity',"
                " 'type': <IdentifierType.OBJECT>}, {'labelId': 'clnt:nore',"
                " 'path': 'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': <IdentifierType.OBJECT>}]",
            ],
            "reportDetails.executingEntity.fileIdentifier": [
                "lei:213800nr44fzfc9rpo56",
                "lei:213800nr44fzfc9rpo56",
                "account:account_firm_lei",
            ],
            "buyerFileIdentifier": ["lei:fjyyt1hybn8onklzc633", "lei:w22lrowp2ihznbb6k528", pd.NA],
            "sellerFileIdentifier": ["lei:w22lrowp2ihznbb6k528", "lei:ak79l73snc8gpsp6pe61", pd.NA],
            "counterpartyFileIdentifier": ["lei:w22lrowp2ihznbb6k528", "lei:w22lrowp2ihznbb6k528", pd.NA],
            "sellerDecisionMakerFileIdentifier": [pd.NA, "lei:213800nr44fzfc9rpo56", pd.NA],
            "buyerDecisionMakerFileIdentifier": ["lei:213800nr44fzfc9rpo56", pd.NA, pd.NA],
            "tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier": [
                "account:219694",
                "account:219694",
                pd.NA,
            ],
            "tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier": [
                "account:randomid",
                "account:301791",
                "clnt:nore",
            ],
            "clientFileIdentifier": ["lei:fjyyt1hybn8onklzc633", "lei:ak79l73snc8gpsp6pe61", pd.NA],
            "traderFileIdentifier": ["account:301791", "account:301791", pd.NA],
            "__fallback_buyer__": ["FJYYT1HYBN8ONKLZC633", "W22LROWP2IHZNBB6K528", pd.NA],
            "__fallback_seller__": ["W22LROWP2IHZNBB6K528", "********************", pd.NA],
            "__fallback_counterparty__": ["W22LROWP2IHZNBB6K528", "W22LROWP2IHZNBB6K528", pd.NA],
            "__fallback_client__": ["FJYYT1HYBN8ONKLZC633", "********************", pd.NA],
            "__fallback_buyer_dec_maker__": ["213800NR44FZFC9RPO56", pd.NA, pd.NA],
            "__fallback_seller_dec_maker__": [pd.NA, "213800NR44FZFC9RPO56", pd.NA],
            "__fallback_inv_dec_in_firm__": ["219694", "219694", pd.NA],
            "__fallback_trader__": ["301791", "301791", pd.NA],
            "__fallback_exec_within_firm__": ["randomID", "301791", "clnt:nore"],
            "__fallback_executing_entity__": ["213800NR44FZFC9RPO56", "213800NR44FZFC9RPO56", "account_firm_lei"],
        }
    )


@pytest.fixture()
def all_cols_expected_result_preserve_identifiers_original() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "marketIdentifiers.parties": [
                "[{'labelId': 'lei:FJYYT1HYBN8ONKLZC633', 'path': 'buyer', 'type': <IdentifierType.ARRAY>},"
                " {'labelId': 'lei:213800NR44FZFC9RPO56', 'path': 'buyerDecisionMaker',"
                " 'type': <IdentifierType.ARRAY>}, {'labelId': 'lei:213800NR44FZFC9RPO56',"
                " 'path': 'reportDetails.executingEntity', 'type': <IdentifierType.OBJECT>},"
                " {'labelId': 'lei:W22LROWP2IHZNBB6K528', 'path': 'seller', 'type': <IdentifierType.ARRAY>},"
                " {'labelId': 'lei:W22LROWP2IHZNBB6K528', 'path': 'counterparty', 'type': <IdentifierType.OBJECT>},"
                " {'labelId': 'account:219694', 'path': 'tradersAlgosWaiversIndicators.investmentDecisionWithinFirm',"
                " 'type': <IdentifierType.OBJECT>}, {'labelId': 'account:randomID',"
                " 'path': 'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': <IdentifierType.OBJECT>},"
                " {'labelId': 'lei:FJYYT1HYBN8ONKLZC633', 'path': 'clientIdentifiers.client',"
                " 'type': <IdentifierType.ARRAY>}, {'labelId': 'account:301791', 'path': 'trader',"
                " 'type': <IdentifierType.ARRAY>}]",
                "[{'labelId': 'lei:W22LROWP2IHZNBB6K528', 'path': 'buyer', 'type': <IdentifierType.ARRAY>},"
                " {'labelId': 'lei:213800NR44FZFC9RPO56', 'path': 'reportDetails.executingEntity',"
                " 'type': <IdentifierType.OBJECT>}, {'labelId': 'lei:********************',"
                " 'path': 'seller', 'type': <IdentifierType.ARRAY>}, {'labelId': 'lei:213800NR44FZFC9RPO56',"
                " 'path': 'sellerDecisionMaker', 'type': <IdentifierType.ARRAY>},"
                " {'labelId': 'lei:W22LROWP2IHZNBB6K528', 'path': 'counterparty', 'type': <IdentifierType.OBJECT>},"
                " {'labelId': 'account:219694', 'path': 'tradersAlgosWaiversIndicators.investmentDecisionWithinFirm',"
                " 'type': <IdentifierType.OBJECT>}, {'labelId': 'account:301791',"
                " 'path': 'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': <IdentifierType.OBJECT>},"
                " {'labelId': 'lei:********************', 'path': 'clientIdentifiers.client',"
                " 'type': <IdentifierType.ARRAY>}, {'labelId': 'account:301791', 'path': 'trader',"
                " 'type': <IdentifierType.ARRAY>}]",
                "[{'labelId': 'account:account_firm_lei', 'path': 'reportDetails.executingEntity',"
                " 'type': <IdentifierType.OBJECT>}, {'labelId': 'clnt:nore',"
                " 'path': 'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': <IdentifierType.OBJECT>}]",
            ],
            "reportDetails.executingEntity.fileIdentifier": [
                "lei:213800NR44FZFC9RPO56",
                "lei:213800NR44FZFC9RPO56",
                "account:account_firm_lei",
            ],
            "buyerFileIdentifier": ["lei:FJYYT1HYBN8ONKLZC633", "lei:W22LROWP2IHZNBB6K528", pd.NA],
            "sellerFileIdentifier": ["lei:W22LROWP2IHZNBB6K528", "lei:********************", pd.NA],
            "counterpartyFileIdentifier": ["lei:W22LROWP2IHZNBB6K528", "lei:W22LROWP2IHZNBB6K528", pd.NA],
            "sellerDecisionMakerFileIdentifier": [pd.NA, "lei:213800NR44FZFC9RPO56", pd.NA],
            "buyerDecisionMakerFileIdentifier": ["lei:213800NR44FZFC9RPO56", pd.NA, pd.NA],
            "tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier": [
                "account:219694",
                "account:219694",
                pd.NA,
            ],
            "tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier": [
                "account:randomID",
                "account:301791",
                "clnt:nore",
            ],
            "clientFileIdentifier": ["lei:FJYYT1HYBN8ONKLZC633", "lei:********************", pd.NA],
            "traderFileIdentifier": ["account:301791", "account:301791", pd.NA],
            "__fallback_buyer__": ["fjyyt1hybn8onklzc633", "w22lrowp2ihznbb6k528", pd.NA],
            "__fallback_seller__": ["w22lrowp2ihznbb6k528", "ak79l73snc8gpsp6pe61", pd.NA],
            "__fallback_counterparty__": ["w22lrowp2ihznbb6k528", "w22lrowp2ihznbb6k528", pd.NA],
            "__fallback_client__": ["fjyyt1hybn8onklzc633", "ak79l73snc8gpsp6pe61", pd.NA],
            "__fallback_buyer_dec_maker__": ["213800nr44fzfc9rpo56", pd.NA, pd.NA],
            "__fallback_seller_dec_maker__": [pd.NA, "213800nr44fzfc9rpo56", pd.NA],
            "__fallback_inv_dec_in_firm__": ["219694", "219694", pd.NA],
            "__fallback_trader__": ["301791", "301791", pd.NA],
            "__fallback_exec_within_firm__": ["randomid", "301791", "clnt:nore"],
            "__fallback_executing_entity__": ["213800nr44fzfc9rpo56", "213800nr44fzfc9rpo56", "account_firm_lei"],
        }
    )


@pytest.fixture()
def all_cols_no_tenant_lei_hit_expected_result() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "marketIdentifiers.parties": [
                "[{'labelId': 'lei:fjyyt1hybn8onklzc633', 'path': 'buyer', 'type': "
                "<IdentifierType.ARRAY>}, "
                "{'labelId': 'lei:213800nr44fzfc9rpo56', 'path': "
                "'buyerDecisionMaker', 'type': <IdentifierType.ARRAY>}, "
                "{'labelId': 'lei:213800nr44fzfc9rpo56', "
                "'path': 'reportDetails.executingEntity', 'type': "
                "<IdentifierType.OBJECT>}, {'labelId': 'lei:w22lrowp2ihznbb6k528', "
                "'path': 'seller', 'type': <IdentifierType.ARRAY>}, {'labelId': "
                "'lei:w22lrowp2ihznbb6k528', 'path': 'counterparty', 'type': "
                "<IdentifierType.OBJECT>}, {'labelId': 'account:219694', 'path': "
                "'tradersAlgosWaiversIndicators.investmentDecisionWithinFirm', "
                "'type': "
                "<IdentifierType.OBJECT>}, "
                "{'labelId': 'account:randomid', 'path': "
                "'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': "
                "<IdentifierType.OBJECT>}, "
                "{'labelId': 'lei:fjyyt1hybn8onklzc633', 'path': "
                "'clientIdentifiers.client', 'type': "
                "<IdentifierType.ARRAY>}, "
                "{'labelId': 'account:301791', 'path': 'trader', 'type': "
                "<IdentifierType.ARRAY>}]",
                "[{'labelId': 'lei:w22lrowp2ihznbb6k528', 'path': 'buyer', 'type': "
                "<IdentifierType.ARRAY>}, "
                "{'labelId': 'lei:213800nr44fzfc9rpo56', "
                "'path': 'reportDetails.executingEntity', 'type': "
                "<IdentifierType.OBJECT>}, {'labelId': 'lei:ak79l73snc8gpsp6pe61', "
                "'path': 'seller', 'type': <IdentifierType.ARRAY>}, "
                "{'labelId': 'lei:213800nr44fzfc9rpo56', 'path': "
                "'sellerDecisionMaker', 'type': <IdentifierType.ARRAY>}, "
                "{'labelId': "
                "'lei:w22lrowp2ihznbb6k528', 'path': 'counterparty', 'type': "
                "<IdentifierType.OBJECT>}, {'labelId': 'account:219694', 'path': "
                "'tradersAlgosWaiversIndicators.investmentDecisionWithinFirm', "
                "'type': "
                "<IdentifierType.OBJECT>}, "
                "{'labelId': 'account:301791', 'path': "
                "'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': "
                "<IdentifierType.OBJECT>}, "
                "{'labelId': 'lei:ak79l73snc8gpsp6pe61', 'path': "
                "'clientIdentifiers.client', 'type': "
                "<IdentifierType.ARRAY>}, "
                "{'labelId': 'account:301791', 'path': 'trader', 'type': "
                "<IdentifierType.ARRAY>}]",
                "[{'labelId': 'clnt:nore', 'path': 'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': <IdentifierType.OBJECT>}]",  # noqa E501
            ],
            "reportDetails.executingEntity.fileIdentifier": [
                "lei:213800nr44fzfc9rpo56",
                "lei:213800nr44fzfc9rpo56",
                pd.NA,
            ],
            "buyerFileIdentifier": [
                "lei:fjyyt1hybn8onklzc633",
                "lei:w22lrowp2ihznbb6k528",
                pd.NA,
            ],
            "sellerFileIdentifier": [
                "lei:w22lrowp2ihznbb6k528",
                "lei:ak79l73snc8gpsp6pe61",
                pd.NA,
            ],
            "counterpartyFileIdentifier": [
                "lei:w22lrowp2ihznbb6k528",
                "lei:w22lrowp2ihznbb6k528",
                pd.NA,
            ],
            "sellerDecisionMakerFileIdentifier": [
                pd.NA,
                "lei:213800nr44fzfc9rpo56",
                pd.NA,
            ],
            "buyerDecisionMakerFileIdentifier": [
                "lei:213800nr44fzfc9rpo56",
                pd.NA,
                pd.NA,
            ],
            "tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier": [
                "account:219694",
                "account:219694",
                pd.NA,
            ],
            "tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier": [
                "account:randomid",
                "account:301791",
                "clnt:nore",
            ],
            "clientFileIdentifier": [
                "lei:fjyyt1hybn8onklzc633",
                "lei:ak79l73snc8gpsp6pe61",
                pd.NA,
            ],
            "traderFileIdentifier": ["account:301791", "account:301791", pd.NA],
        }
    )


class TestBlotterPartyIdentifiers:
    def test_empty_input_df_without_source_columns(self, empty_source_df, mocker, params):
        mock_client_map = mocker.patch.object(BlotterPartyIdentifiers, "_get_client_map")
        mock_client_map.return_value = {
            "FJYYT1HYBN8ONKLZC633": "Discretionary",
            "********************": "Discretionary",
        }
        result = run_blotter_party_identifiers(
            source_frame=empty_source_df,
            params=params,
            es_client=None,
            tenant="foo",
        )
        assert result.empty
        assert mock_client_map.not_called

    def test_all_col_in_source_df(
        self,
        all_col_in_source_df: pd.DataFrame,
        all_cols_expected_result: pd.DataFrame,
        mocker,
        params: Params,
        mock_get_tenant_lei: pd.DataFrame,
    ):
        mock_client_map = mocker.patch.object(BlotterPartyIdentifiers, "_get_client_map")
        mock_client_map.return_value = {
            "FJYYT1HYBN8ONKLZC633": "DISCRETIONARY",
            "********************": "DISCRETIONARY",
        }
        # mock get_tenant_lei
        mocker.patch(
            "se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.blotter_party_identifiers.run_get_tenant_lei",
            return_value=mock_get_tenant_lei,
        )

        result = run_blotter_party_identifiers(
            source_frame=all_col_in_source_df,
            params=params,
            es_client=None,
            tenant="foo",
        )

        assert result.astype("str").equals(all_cols_expected_result.astype("str"))
        assert mock_client_map.called

    def test_all_col_in_source_df_preserve_original_value_full(
        self,
        all_col_in_source_df: pd.DataFrame,
        all_cols_expected_result_preserve_original: pd.DataFrame,
        mocker,
        params: Params,
        mock_get_tenant_lei: pd.DataFrame,
    ):
        mock_client_map = mocker.patch.object(BlotterPartyIdentifiers, "_get_client_map")
        mock_client_map.return_value = {
            "FJYYT1HYBN8ONKLZC633": "DISCRETIONARY",
            "********************": "DISCRETIONARY",
        }
        # mock get_tenant_lei
        mocker.patch(
            "se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.blotter_party_identifiers.run_get_tenant_lei",
            return_value=mock_get_tenant_lei,
        )
        params.preserve_original_casing_in_fallback_fields = True
        params.preserve_original_casing_in_market_identifiers = True

        result = run_blotter_party_identifiers(
            source_frame=all_col_in_source_df,
            params=params,
            es_client=None,
            tenant="foo",
        )

        assert result.astype("str").equals(all_cols_expected_result_preserve_original.astype("str"))
        assert mock_client_map.called

    def test_all_col_in_source_df_preserve_original_value_fallback_only(
        self,
        all_col_in_source_df: pd.DataFrame,
        all_cols_expected_result_preserve_fallback_original: pd.DataFrame,
        mocker,
        params: Params,
        mock_get_tenant_lei: pd.DataFrame,
    ):
        mock_client_map = mocker.patch.object(BlotterPartyIdentifiers, "_get_client_map")
        mock_client_map.return_value = {
            "FJYYT1HYBN8ONKLZC633": "DISCRETIONARY",
            "********************": "DISCRETIONARY",
        }
        # mock get_tenant_lei
        mocker.patch(
            "se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.blotter_party_identifiers.run_get_tenant_lei",
            return_value=mock_get_tenant_lei,
        )
        params.create_fallback_fields = True
        params.preserve_original_casing_in_fallback_fields = True

        result = run_blotter_party_identifiers(
            source_frame=all_col_in_source_df,
            params=params,
            es_client=None,
            tenant="foo",
        )

        assert result.astype("str").equals(all_cols_expected_result_preserve_fallback_original.astype("str"))
        assert mock_client_map.called

    def test_all_col_in_source_df_preserve_original_value_identifiers_only(
        self,
        all_col_in_source_df: pd.DataFrame,
        all_cols_expected_result_preserve_identifiers_original: pd.DataFrame,
        mocker,
        params: Params,
        mock_get_tenant_lei: pd.DataFrame,
    ):
        mock_client_map = mocker.patch.object(BlotterPartyIdentifiers, "_get_client_map")
        mock_client_map.return_value = {
            "FJYYT1HYBN8ONKLZC633": "DISCRETIONARY",
            "********************": "DISCRETIONARY",
        }
        # mock get_tenant_lei
        mocker.patch(
            "se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.blotter_party_identifiers.run_get_tenant_lei",
            return_value=mock_get_tenant_lei,
        )
        params.create_fallback_fields = True
        params.preserve_original_casing_in_market_identifiers = True

        result = run_blotter_party_identifiers(
            source_frame=all_col_in_source_df,
            params=params,
            es_client=None,
            tenant="foo",
        )

        assert result.astype("str").equals(all_cols_expected_result_preserve_identifiers_original.astype("str"))
        assert mock_client_map.called

    def test_all_col_in_source_df_no_tenant_lei_hit(
        self,
        all_col_in_source_df: pd.DataFrame,
        all_cols_no_tenant_lei_hit_expected_result: pd.DataFrame,
        mocker,
        params: Params,
    ):
        mock_client_map = mocker.patch.object(BlotterPartyIdentifiers, "_get_client_map")
        mock_client_map.return_value = {
            "FJYYT1HYBN8ONKLZC633": "DISCRETIONARY",
            "********************": "DISCRETIONARY",
        }

        result = run_blotter_party_identifiers(
            source_frame=all_col_in_source_df,
            params=params,
            es_client=None,
            tenant="foo",
        )

        assert result.astype("str").equals(all_cols_no_tenant_lei_hit_expected_result.astype("str"))
        assert mock_client_map.called

    def test_it_strips_leading_and_trailing_whitespaces(
        self,
        all_col_in_source_df: pd.DataFrame,
        all_cols_expected_result: pd.DataFrame,
        mocker,
        params: Params,
        mock_get_tenant_lei: pd.DataFrame,
    ):
        mock_client_map = mocker.patch.object(BlotterPartyIdentifiers, "_get_client_map")
        mock_client_map.return_value = {
            "FJYYT1HYBN8ONKLZC633": "DISCRETIONARY",
            "********************": "DISCRETIONARY",
        }
        # mock get_tenant_lei
        mocker.patch(
            "se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.blotter_party_identifiers.run_get_tenant_lei",
            return_value=mock_get_tenant_lei,
        )

        # Adds a whitespace to the end of each value to ensure its working
        for col in all_col_in_source_df.columns:
            all_col_in_source_df[col] = " " + all_col_in_source_df[col] + " "

        result = run_blotter_party_identifiers(
            source_frame=all_col_in_source_df,
            params=params,
            es_client=None,
            tenant="foo",
        )

        pd.testing.assert_frame_equal(left=result.astype("str"), right=all_cols_expected_result.astype("str"))

    def test_missing_some_col_in_source_df(
        self,
        missing_some_col_in_source_df: pd.DataFrame,
        mocker,
        params: Params,
        mock_get_tenant_lei: pd.DataFrame,
    ):
        mock_client_map = mocker.patch.object(BlotterPartyIdentifiers, "_get_client_map")
        mock_client_map.return_value = {
            "FJYYT1HYBN8ONKLZC633": "DISCRETIONARY",
            "********************": "DISCRETIONARY",
        }
        # mock get_tenant_lei
        mocker.patch(
            "se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.blotter_party_identifiers.run_get_tenant_lei",
            return_value=mock_get_tenant_lei,
        )

        expected_result = pd.DataFrame(
            {
                "marketIdentifiers.parties": [
                    "[{'labelId': 'lei:fjyyt1hybn8onklzc633', 'path': 'buyer', 'type': <IdentifierType.ARRAY>}, "
                    "{'labelId': 'account:account_firm_lei', 'path': 'buyerDecisionMaker', "
                    "'type': <IdentifierType.ARRAY>}, {'labelId': 'account:account_firm_lei', "
                    "'path': 'reportDetails.executingEntity', 'type': <IdentifierType.OBJECT>}, {'labelId': "
                    "'account:301791', 'path': 'tradersAlgosWaiversIndicators.investmentDecisionWithinFirm',"
                    " 'type': <IdentifierType.OBJECT>}, {'labelId': 'account:301791', "
                    "'path': 'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': <IdentifierType.OBJECT>}, "
                    "{'labelId': 'lei:fjyyt1hybn8onklzc633', 'path': 'clientIdentifiers.client', "
                    "'type': <IdentifierType.ARRAY>}, {'labelId': 'account:301791', 'path': 'trader', "
                    "'type': <IdentifierType.ARRAY>}]",
                    "[{'labelId': 'account:account_firm_lei', 'path': 'reportDetails.executingEntity', "
                    "'type': <IdentifierType.OBJECT>}, {'labelId': 'lei:ak79l73snc8gpsp6pe61', 'path': 'seller', "
                    "'type': <IdentifierType.ARRAY>}, {'labelId': 'account:account_firm_lei', "
                    "'path': 'sellerDecisionMaker', 'type': <IdentifierType.ARRAY>}, {'labelId': 'account:301791', "
                    "'path': 'tradersAlgosWaiversIndicators.investmentDecisionWithinFirm', "
                    "'type': <IdentifierType.OBJECT>}, {'labelId': 'account:301791', "
                    "'path': 'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': <IdentifierType.OBJECT>}, "
                    "{'labelId': 'lei:ak79l73snc8gpsp6pe61', 'path': 'clientIdentifiers.client', "
                    "'type': <IdentifierType.ARRAY>}, {'labelId': 'account:301791', 'path': 'trader', "
                    "'type': <IdentifierType.ARRAY>}]",
                    "[{'labelId': 'account:account_firm_lei', "
                    "'path': 'reportDetails.executingEntity', 'type': "
                    "<IdentifierType.OBJECT>}, {'labelId': 'clnt:nore', 'path': "
                    "'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': <IdentifierType.OBJECT>}]",
                ],
                "reportDetails.executingEntity.fileIdentifier": [
                    "account:account_firm_lei",
                    "account:account_firm_lei",
                    "account:account_firm_lei",
                ],
                "buyerFileIdentifier": ["lei:fjyyt1hybn8onklzc633", pd.NA, pd.NA],
                "sellerFileIdentifier": [pd.NA, "lei:ak79l73snc8gpsp6pe61", pd.NA],
                "counterpartyFileIdentifier": [pd.NA, pd.NA, pd.NA],
                "sellerDecisionMakerFileIdentifier": [pd.NA, "account:account_firm_lei", pd.NA],
                "buyerDecisionMakerFileIdentifier": ["account:account_firm_lei", pd.NA, pd.NA],
                "tradersAlgosWaiversIndicators"
                ".investmentDecisionWithinFirmFileIdentifier": [
                    "account:301791",
                    "account:301791",
                    pd.NA,
                ],
                "tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier": [
                    "account:301791",
                    "account:301791",
                    "clnt:nore",
                ],
                "clientFileIdentifier": [
                    "lei:fjyyt1hybn8onklzc633",
                    "lei:ak79l73snc8gpsp6pe61",
                    pd.NA,
                ],
                "traderFileIdentifier": ["account:301791", "account:301791", pd.NA],
            }
        )

        result = run_blotter_party_identifiers(
            source_frame=missing_some_col_in_source_df,
            params=params,
            es_client=None,
            tenant="foo",
        )
        assert result.astype("str").equals(expected_result.astype("str"))
        assert mock_client_map.called

    def test_exec_wf_all_data_with_deal_df(
        self,
        exec_wf_all_data_df: pd.DataFrame,
        deal_mask_all_true,
        params,
    ):
        task = BlotterPartyIdentifiers()
        expected_result = pd.Series(["TraderId1", "TraderId2", "TraderId3", "TraderId4", "TraderId5"])

        result = task._get_execution_within_firm(exec_wf_all_data_df, deal_mask_all_true)

        assert expected_result.equals(result)

    def test_exec_wf_all_data_no_deal_df(
        self,
        exec_wf_all_data_df: pd.DataFrame,
        deal_mask_all_false,
        params,
    ):
        task = BlotterPartyIdentifiers()
        expected_result = pd.Series(["TraderId1", "TraderId2", "TraderId3", "TraderId4", "TraderId5"])

        result = task._get_execution_within_firm(exec_wf_all_data_df, deal_mask_all_false)

        assert expected_result.equals(result)

    def test_exec_wf_clnt_nore_df(
        self,
        exec_wf_clnt_nore_df: pd.DataFrame,
        deal_mask_all_true,
        params,
    ):
        task = BlotterPartyIdentifiers()
        expected_result = pd.Series(["clnt:nore"] * 5)

        result = task._get_execution_within_firm(exec_wf_clnt_nore_df, deal_mask_all_true)

        assert expected_result.equals(result)

    def test_exec_wf_partial_clnt_nore_df(
        self,
        exec_wf_partial_clnt_nore_df: pd.DataFrame,
        deal_mask_all_true,
        params,
    ):
        task = BlotterPartyIdentifiers()
        expected_result = pd.Series(["TraderId1", "Inv2", "clnt:nore", "TraderId4", "Exec1"])

        result = task._get_execution_within_firm(exec_wf_partial_clnt_nore_df, deal_mask_all_true)

        assert expected_result.equals(result)

    def test_drop_file_identifiers_but_populate_fallback_fields(
        self,
        all_col_in_source_df: pd.DataFrame,
        mocker,
        mock_get_tenant_lei: pd.DataFrame,
    ):
        """
        This test asserts that when we want to drop the file identifier columns, the fallback
        fields are still populated correctly if that Param is enabled
        """

        mock_client_map = mocker.patch.object(BlotterPartyIdentifiers, "_get_client_map")
        mock_client_map.return_value = {
            "FJYYT1HYBN8ONKLZC633": "DISCRETIONARY",
            "********************": "DISCRETIONARY",
        }
        # mock get_tenant_lei
        mocker.patch(
            "se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.blotter_party_identifiers.run_get_tenant_lei",
            return_value=mock_get_tenant_lei,
        )

        expected_result = pd.DataFrame(
            {
                "marketIdentifiers.parties": [
                    "[{'labelId': 'lei:fjyyt1hybn8onklzc633', 'path': 'buyer', 'type': "
                    "<IdentifierType.ARRAY>}, "
                    "{'labelId': 'lei:213800nr44fzfc9rpo56', 'path': "
                    "'buyerDecisionMaker', 'type': <IdentifierType.ARRAY>}, "
                    "{'labelId': 'lei:213800nr44fzfc9rpo56', "
                    "'path': 'reportDetails.executingEntity', 'type': "
                    "<IdentifierType.OBJECT>}, {'labelId': 'lei:w22lrowp2ihznbb6k528', "
                    "'path': 'seller', 'type': <IdentifierType.ARRAY>}, {'labelId': "
                    "'lei:w22lrowp2ihznbb6k528', 'path': 'counterparty', 'type': "
                    "<IdentifierType.OBJECT>}, {'labelId': 'account:219694', 'path': "
                    "'tradersAlgosWaiversIndicators.investmentDecisionWithinFirm', "
                    "'type': "
                    "<IdentifierType.OBJECT>}, "
                    "{'labelId': 'account:randomid', 'path': "
                    "'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': "
                    "<IdentifierType.OBJECT>}, "
                    "{'labelId': 'lei:fjyyt1hybn8onklzc633', 'path': "
                    "'clientIdentifiers.client', 'type': "
                    "<IdentifierType.ARRAY>}, "
                    "{'labelId': 'account:301791', 'path': 'trader', 'type': "
                    "<IdentifierType.ARRAY>}]",
                    "[{'labelId': 'lei:w22lrowp2ihznbb6k528', 'path': 'buyer', 'type': "
                    "<IdentifierType.ARRAY>}, "
                    "{'labelId': 'lei:213800nr44fzfc9rpo56', "
                    "'path': 'reportDetails.executingEntity', 'type': "
                    "<IdentifierType.OBJECT>}, {'labelId': 'lei:ak79l73snc8gpsp6pe61', "
                    "'path': 'seller', 'type': <IdentifierType.ARRAY>}, "
                    "{'labelId': 'lei:213800nr44fzfc9rpo56', 'path': "
                    "'sellerDecisionMaker', 'type': <IdentifierType.ARRAY>}, "
                    "{'labelId': "
                    "'lei:w22lrowp2ihznbb6k528', 'path': 'counterparty', 'type': "
                    "<IdentifierType.OBJECT>}, {'labelId': 'account:219694', 'path': "
                    "'tradersAlgosWaiversIndicators.investmentDecisionWithinFirm', "
                    "'type': "
                    "<IdentifierType.OBJECT>}, "
                    "{'labelId': 'account:301791', 'path': "
                    "'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': "
                    "<IdentifierType.OBJECT>}, "
                    "{'labelId': 'lei:ak79l73snc8gpsp6pe61', 'path': "
                    "'clientIdentifiers.client', 'type': "
                    "<IdentifierType.ARRAY>}, "
                    "{'labelId': 'account:301791', 'path': 'trader', 'type': "
                    "<IdentifierType.ARRAY>}]",
                    "[{'labelId': 'account:account_firm_lei', "
                    "'path': 'reportDetails.executingEntity', 'type': "
                    "<IdentifierType.OBJECT>}, {'labelId': 'clnt:nore', 'path': 'tradersAlgosWaiversIndicators.executionWithinFirm', 'type': <IdentifierType.OBJECT>}]",  # noqa E501
                ],
                "__fallback_buyer__": [
                    "fjyyt1hybn8onklzc633",
                    "w22lrowp2ihznbb6k528",
                    pd.NA,
                ],
                "__fallback_seller__": [
                    "w22lrowp2ihznbb6k528",
                    "ak79l73snc8gpsp6pe61",
                    pd.NA,
                ],
                "__fallback_counterparty__": [
                    "w22lrowp2ihznbb6k528",
                    "w22lrowp2ihznbb6k528",
                    pd.NA,
                ],
                "__fallback_client__": [
                    "fjyyt1hybn8onklzc633",
                    "ak79l73snc8gpsp6pe61",
                    pd.NA,
                ],
                "__fallback_buyer_dec_maker__": [
                    "213800nr44fzfc9rpo56",
                    pd.NA,
                    pd.NA,
                ],
                "__fallback_seller_dec_maker__": [
                    pd.NA,
                    "213800nr44fzfc9rpo56",
                    pd.NA,
                ],
                "__fallback_inv_dec_in_firm__": [
                    "219694",
                    "219694",
                    pd.NA,
                ],
                "__fallback_trader__": ["301791", "301791", pd.NA],
                "__fallback_exec_within_firm__": [
                    "randomid",
                    "301791",
                    "clnt:nore",
                ],
                "__fallback_executing_entity__": [
                    "213800nr44fzfc9rpo56",
                    "213800nr44fzfc9rpo56",
                    "account_firm_lei",
                ],
            }
        )

        result = run_blotter_party_identifiers(
            source_frame=all_col_in_source_df,
            params=Params(create_fallback_fields=True, drop_file_identifier_columns=True),
            es_client=None,
            tenant="foo",
        )
        assert result.astype("str").equals(expected_result.astype("str"))
        assert mock_client_map.called


def test_trade_identifiers_query():

    es_client = addict.Dict(
        {
            "MAX_TERMS_SIZE": 5,
            "SIZE": 500,
        }
    )

    ids = [f"id_{x}" for x in range(1, 10)]

    res = BlotterPartyIdentifiers._trade_file_identifiers_query(es_client=es_client, ids=ids)

    assert res == {
        "_source": {"includes": ["details.clientMandate", "sinkIdentifiers.tradeFileIdentifiers"]},
        "query": {
            "bool": {
                "filter": [
                    {"terms": {"&model": ["AccountFirm", "AccountPerson", "MarketPerson", "MarketCounterparty"]}},
                    {
                        "nested": {
                            "path": "sinkIdentifiers.tradeFileIdentifiers",
                            "query": {
                                "bool": {
                                    "should": [
                                        {
                                            "terms": {
                                                "sinkIdentifiers.tradeFileIdentifiers.id": [
                                                    "id_1",
                                                    "id_2",
                                                    "id_3",
                                                    "id_4",
                                                    "id_5",
                                                ]
                                            }
                                        },
                                        {
                                            "terms": {
                                                "sinkIdentifiers.tradeFileIdentifiers.id": [
                                                    "id_6",
                                                    "id_7",
                                                    "id_8",
                                                    "id_9",
                                                ]
                                            }
                                        },
                                    ],
                                    "minimum_should_match": 1,
                                }
                            },
                            "ignore_unmapped": "true",
                        }
                    },
                ]
            }
        },
    }

import logging
import os
from pathlib import Path

import boto3
import pytest
from moto import mock_aws

from se_core_tasks.io.write.cloud.delete_file import run_delete_file

os.environ["AWS_DEFAULT_REGION"] = "us-east-1"
SCRIPT_PATH = Path(__file__).parent
TEST_FILE = SCRIPT_PATH.joinpath("data", "json_file.json")


class TestDeleteFile:
    """Tests file Delete for S3 or any other cloud provider."""

    def test_empty_file_uri(self, caplog):
        caplog.set_level(logging.WARNING)
        run_delete_file(file_uri=None)

        assert "No file_uri's were passed to this task" in caplog.text

    @mock_aws
    def test_end_to_end_file_upload(self):
        s3 = boto3.client("s3")
        bucket_name = "test.dev.steeleye.co"
        key = "aries/ingress/nonstreamed/evented/file_{}.json"
        s3.create_bucket(Bucket=bucket_name)

        for i in range(3):
            with open(TEST_FILE, "rb") as fp:
                s3.put_object(
                    Body=fp.read(),
                    Bucket=bucket_name,
                    Key=key.format(i),
                )
        run_delete_file(
            file_uri=[
                f"s3://{bucket_name}/{key.format(1)}",
                f"s3://{bucket_name}/{key.format(2)}",
                None,
            ]
        )
        with pytest.raises(s3.exceptions.NoSuchKey):
            s3.get_object(Bucket=bucket_name, Key=key.format(1))

        with pytest.raises(s3.exceptions.NoSuchKey):
            s3.get_object(Bucket=bucket_name, Key=key.format(2))

        assert (
            s3.get_object(Bucket=bucket_name, Key=key.format(0))["Body"].read()
            == open(TEST_FILE, "rb").read()
        )

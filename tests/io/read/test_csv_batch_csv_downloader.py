from unittest.mock import MagicMock
from unittest.mock import patch

import pandas as pd
import pytest
from botocore.exceptions import ClientError

from se_core_tasks.io.read import csv_batch_csv_downloader
from se_core_tasks.io.read.csv_batch_csv_downloader import FailIfEmptySourceFrame
from se_core_tasks.io.read.csv_batch_csv_downloader import (
    FailIfRequiredColumnsNotPresent,
)
from se_core_tasks.io.read.csv_batch_csv_downloader import Params
from se_core_tasks.io.read.csv_batch_csv_downloader import run_csv_batch_csv_downloader


@pytest.fixture()
def source_frame():
    source_frame_dict = {
        "s3_audio_file_url": {
            0: "s3://test.uat.steeleye.co/flows/comms-truphone-controller/sample_file.zip"
        },
        "s3_csv_file_url": {
            0: "s3://test.uat.steeleye.co/flows/comms-truphone-controller/sample.csv"
        },
    }
    return pd.DataFrame(source_frame_dict)


@pytest.fixture()
def source_frame_3_rows():
    return pd.DataFrame(
        {
            "s3_audio_file_url": [
                "s3://test.uat.steeleye.co/flows/comms-truphone-controller/sample_file1.zip",
                "s3://test.uat.steeleye.co/flows/comms-truphone-controller/sample_file2.zip",
                "s3://test.uat.steeleye.co/flows/comms-truphone-controller/sample_file3.zip",
            ],
            "s3_csv_file_url": [
                "s3://test.uat.steeleye.co/flows/comms-truphone-controller/sample1.csv",
                "s3://test.uat.steeleye.co/flows/comms-truphone-controller/sample2.csv",
                "s3://test.uat.steeleye.co/flows/comms-truphone-controller/sample3.csv",
            ],
        }
    )


@pytest.fixture()
def sample_csv_df():
    data_dict = {
        "From": {0: "+4412343455343;", 1: "+4477454396911;", 2: "+4478024355077;"},
        "To": {
            0: "Ulf Ek [+447408454215];",
            1: "Ulf Ek [+447454195215];",
            2: "Ulf Ek [+447408454515];",
        },
        "File Name": {
            0: "+404782435077-11Mar2022104705.cStMVce",
            1: "+447799546911-11Mar2022101935.cStMVce",
            2: "+447845445077-11Mar2022100805.cStMVce",
        },
        "s3_csv_file_url": {
            0: "s3://test.uat.steeleye.co/flows/comms-truphone-controller/UE CSV Files 110322.csv",
            1: "s3://test.uat.steeleye.co/flows/comms-truphone-controller/UE CSV Files 110322.csv",
            2: "s3://test.uat.steeleye.co/flows/comms-truphone-controller/UE CSV Files 110322.csv",
        },
    }
    return pd.DataFrame(data_dict)


@pytest.fixture()
def sample_csv_df_with_only_nulls():
    data_dict = {
        "From": {0: pd.NA},
        "To": {
            0: pd.NA,
        },
        "File Name": {
            0: pd.NA,
        },
        "s3_csv_file_url": {
            0: pd.NA,
        },
    }
    return pd.DataFrame(data_dict)


@pytest.fixture()
def expected_df_with_additional_column():
    data_dict = {
        "From": {0: "+4412343455343;", 1: "+4477454396911;", 2: "+4478024355077;"},
        "To": {
            0: "Ulf Ek [+447408454215];",
            1: "Ulf Ek [+447454195215];",
            2: "Ulf Ek [+447408454515];",
        },
        "File Name": {
            0: "+404782435077-11Mar2022104705.cStMVce",
            1: "+447799546911-11Mar2022101935.cStMVce",
            2: "+447845445077-11Mar2022100805.cStMVce",
        },
        "s3_csv_file_url": {
            0: "s3://test.uat.steeleye.co/flows/comms-truphone-controller/UE CSV Files 110322.csv",
            1: "s3://test.uat.steeleye.co/flows/comms-truphone-controller/UE CSV Files 110322.csv",
            2: "s3://test.uat.steeleye.co/flows/comms-truphone-controller/UE CSV Files 110322.csv",
        },
        "additional_column": {0: pd.NA, 1: pd.NA, 2: pd.NA},
    }
    return pd.DataFrame(data_dict)


@pytest.fixture()
def params_fixture() -> Params:
    return Params(
        metadata_column="s3_csv_file_url", dataframe_columns=["additional_column"]
    )


class TestCSVBatchCSVDownloader:
    def test_empty_source_frame_raises_fail(self, params_fixture: Params):
        with pytest.raises(FailIfEmptySourceFrame):
            run_csv_batch_csv_downloader(
                source_frame=pd.DataFrame(), params=params_fixture
            )

    def test_missing_metadata_col_raises_fail(self, params_fixture: Params):
        source_frame = pd.DataFrame({"random_col": [1, 2, 3]})
        with pytest.raises(FailIfRequiredColumnsNotPresent):
            run_csv_batch_csv_downloader(
                source_frame=source_frame, params=params_fixture
            )

    @patch.object(
        csv_batch_csv_downloader.CSVBatchCSVDownloader, "_get_content_from_csv_line"
    )
    def test_end_to_end_csv_batch_csv_downloader(
        self,
        mock_s3_download: MagicMock,
        source_frame: pd.DataFrame,
        sample_csv_df: pd.DataFrame,
        expected_df_with_additional_column: pd.DataFrame,
        params_fixture: Params,
    ):

        mock_s3_download.return_value = sample_csv_df
        result = run_csv_batch_csv_downloader(
            source_frame=source_frame, params=params_fixture
        )
        assert result.equals(expected_df_with_additional_column)

    @patch.object(csv_batch_csv_downloader, "run_download_file")
    def test_end_to_end_csv_batch_csv_downloader_with_client_error(
        self,
        mock_s3_download: MagicMock,
        source_frame: pd.DataFrame,
        expected_df_with_additional_column: pd.DataFrame,
        params_fixture: Params,
    ):

        mock_s3_download.side_effect = ClientError({}, "not found")
        result = run_csv_batch_csv_downloader(
            source_frame=source_frame, params=params_fixture
        )
        assert result.empty

    @patch.object(
        csv_batch_csv_downloader.CSVBatchCSVDownloader, "_get_content_from_csv_line"
    )
    def test_end_to_end_csv_batch_csv_downloader_with_one_error_two_dfs(
        self,
        mock_s3_download: MagicMock,
        source_frame_3_rows: pd.DataFrame,
        expected_df_with_additional_column: pd.DataFrame,
        params_fixture: Params,
    ):

        mock_s3_download.side_effect = [
            pd.DataFrame(),
            pd.DataFrame({"from": ["123"]}),
            pd.DataFrame({"from": ["456"]}),
        ]
        result = run_csv_batch_csv_downloader(
            source_frame=source_frame_3_rows, params=params_fixture
        )
        assert result.equals(
            pd.DataFrame({"from": ["123", "456"], "additional_column": [pd.NA, pd.NA]})
        )

    @patch.object(
        csv_batch_csv_downloader.CSVBatchCSVDownloader, "_get_content_from_csv_line"
    )
    def test_csv_file_with_nulls(
        self,
        mock_s3_download: MagicMock,
        source_frame: pd.DataFrame,
        sample_csv_df_with_only_nulls: pd.DataFrame,
        expected_df_with_additional_column: pd.DataFrame,
        params_fixture: Params,
    ):

        mock_s3_download.return_value = sample_csv_df_with_only_nulls
        result = run_csv_batch_csv_downloader(
            source_frame=source_frame, params=params_fixture
        )
        assert result.equals(
            pd.DataFrame(
                [
                    {
                        "From": pd.NA,
                        "To": pd.NA,
                        "File Name": pd.NA,
                        "s3_csv_file_url": pd.NA,
                        "additional_column": pd.NA,
                    }
                ]
            )
        )

import pytest

from app.repository.comms.suggestions import (
    ParseSuggestionError,
    _parse_suggestion_hit,
    _reconcile_multisearch_response,
)
from app.schemas.order import Order

ORDER_DICT = {
    "hash_": "f8cf13157b9c4a1220ea0e16f0dd53f10045ba665906bec893d4b26a7e8cc668",
    "id_": "SE|********|23|4:2:NEWO",
    "traitFqn_": None,
    "parent_": None,
    "key_": "Order:SE|********|23|4:2:NEWO:1676647209707",
    "transactionDetails": {
        "venue": "XOFF",
        "priceCurrency": "GBP",
        "tradingCapacity": "AOTC",
        "quantityNotation": "NOML",
        "buySellIndicator": "SELL",
        "priceNotation": "MONE",
        "quantityCurrency": "GBP",
    },
    "sourceIndex": "6",
    "orderLifeCycle": {
        "price": {"averageVolumeWeighted": 1.0, "average": 91.818291445252},
        "isCancelled": False,
        "isFilledFully": True,
        "isPartiallyFilled": False,
        "timestamps": {"firstFill": "2023-02-15T15:28:01Z", "lastFill": "2023-02-15T15:28:01Z"},
        "count": {"amends": 0, "total": 1, "executions": 1, "replacements": 0},
        "tradedQuantity": {"percentageFilled": 1.0, "total": 100000.0},
    },
    "date": "2023-02-15",
    "reportDetails": {
        "executingEntity": {"fileIdentifier": "lei:894500wota5040khgx73"},
        "transactionRefNo": "SE********234SELL",
    },
    "counterpartyFileIdentifier": "lei:3ccujeu88p85mig5r153",
    "marketIdentifiers": [
        {"path": "instrumentDetails.instrument", "labelId": "GB00BD9MZZ71", "type": "OBJECT"},
        {"path": "buyer", "labelId": "lei:3ccujeu88p85mig5r153", "type": "ARRAY"},
        {"path": "reportDetails.executingEntity", "labelId": "lei:894500wota5040khgx73", "type": "OBJECT"},
        {"path": "seller", "labelId": "account:jaypal.sihra", "type": "ARRAY"},
        {"path": "counterparty", "labelId": "lei:3ccujeu88p85mig5r153", "type": "OBJECT"},
        {"path": "tradersAlgosWaiversIndicators.executionWithinFirm", "labelId": "clnt:nore", "type": "OBJECT"},
        {"path": "clientIdentifiers.client", "labelId": "account:jaypal.sihra", "type": "ARRAY"},
        {"path": "trader", "labelId": "account:natasha.sevant", "type": "ARRAY"},
    ],
    "marDetails": {"isPersonalAccountDealing": False},
    "timestamps": {
        "orderStatusUpdated": "2023-02-15T15:28:00Z",
        "orderReceived": "2023-02-15T15:28:00Z",
        "orderSubmitted": "2023-02-15T15:28:00Z",
    },
    "traderFileIdentifier": "account:natasha.sevant",
    "instrumentDetails": {
        "instrument": {
            "ext": {
                "aii": {"daily": "XXXXGBPO"},
                "mifirEligible": True,
                "pricingReferences": {"ICE": "isin/GB00BD9MZZ71/GBP", "RIC": ',"GBIL0E65":RRPS'},
                "bestExAssetClassSub": "Bonds",
                "bestExAssetClassMain": "Debt Instruments",
                "venueInEEA": True,
                "alternativeInstrumentIdentifier": "XXXXGBPO",
                "instrumentUniqueIdentifier": "*******************",
                "onFIRDS": True,
                "instrumentIdCodeType": "ID",
            },
            "sourceKey": "FULINS_D_20230211_02of02.xml",
            "&id": "*******************",
            "venue": {
                "admissionToTradingOrFirstTradeDate": "2016-02-22T00:00:00",
                "tradingVenue": "XLON",
                "issuerRequestForAdmissionToTrading": True,
                "financialInstrumentShortName": "HM TREA/0.125 LN STK 20651122 GOVT",
                "admissionToTradingApprovalDate": "2016-02-19T00:00:00",
                "admissionToTradingRequestDate": "2016-02-19T00:00:00",
            },
            "cfiGroup": "Bonds",
            "cfiCategory": "Debt",
            "instrumentIdCode": "GB00BD9MZZ71",
            "commoditiesOrEmissionAllowanceDerivativeInd": False,
            "instrumentFullName": "TREASURY 0 1/8% IDX-LKD  TREASURY GILT 2065",
            "derivative": {"deliveryType": "CASH"},
            "&key": "FcaFirdsInstrument:*******************:1676133848000",
            "notionalCurrency1": "GBP",
            "bond": {
                "totalIssuedNominalAmount": 0.0,
                "maturityDate": "2065-11-22",
                "fixedRate": 0.125,
                "nominalUnitOrMinTradedValue": 100.0,
            },
            "isCreatedThroughFallback": False,
            "instrumentClassificationEMIRContractType": "OT",
            "issuerOrOperatorOfTradingVenueId": "ECTRVYYCEF89VWYS6K36",
            "cfiAttribute1": "Fixed Rate",
            "cfiAttribute2": "Government/State Guarantee",
            "cfiAttribute3": "Fixed Maturity",
            "cfiAttribute4": "Registered",
            "instrumentClassification": "DBFTFR",
        }
    },
    "id": "SE|********|23|4",
    "priceFormingData": {"remainingQuantity": 0.0, "initialQuantity": 100000.0},
    "orderIdentifiers": {
        "aggregatedOrderId": "SE|********|23|4",
        "internalOrderIdCode": "SE|********|23|4",
        "orderIdCode": "SE|********|23|4",
    },
    "tradersAlgosWaiversIndicators": {
        "shortSellingIndicator": "SELL",
        "executionWithinFirmFileIdentifier": "clnt:nore",
    },
    "buyerFileIdentifier": "lei:3ccujeu88p85mig5r153",
    "sourceKey": "s3://iris.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/********/steeleyeBlotter.base.********.bonds.government.csv",
    "dataSourceName": "SteelEyeTradeBlotter",
    "executionDetails": {
        "orderType": "Market",
        "tradingCapacity": "AOTC",
        "orderStatus": "NEWO",
        "buySellIndicator": "SELL",
        "outgoingOrderAddlInfo": 'Top Notional, Symbol -,"GBIL0E65":RRPS',
    },
    "sellerFileIdentifier": "account:jaypal.sihra",
    "hierarchy": "Standalone",
    "clientFileIdentifier": "account:jaypal.sihra",
    "buySell": "2",
}

ORDER = Order.from_dict(ORDER_DICT)

HIT_FROM_RESPONSE = {
    "_index": "iris_dev_steeleye_co_email",
    "_type": "Email",
    "_id": "b6417a9c-fb72-5973-aac6-dcfa79a075b8",
    "_score": None,
    "_source": {
        "&id": "b6417a9c-fb72-5973-aac6-dcfa79a075b8",
        "subject": "VCON - Trade Confirm - 15-Feb-23 - GB00BD9MZZ71",
        "timestamps": {
            "timestampEnd": "2023-02-15T15:28:01.000000Z",
            "localTimestampEnd": "2023-02-15T15:28:01.000000Z",
            "timestampStart": "2023-02-15T15:28:01.000000Z",
            "localTimestampStart": "2023-02-15T15:28:01.000000Z",
        },
        "messageId": "2023-02-15T15:28:01Z",
        "&key": "Email:b6417a9c-fb72-5973-aac6-dcfa79a075b8:*************",
        "body": {
            "displayText": "Order ID: SE|********|23|4",
            "text": "Order ID: SE|********|23|4",
            "type": "HTML",
        },
        "participants": [],
    },
    "matched_queries": ["Email contains instrument code", "Email contains order ID"],
}


def _make_response(hit):
    return {"responses": [{"status": 200, "hits": {"hits": [hit]}}]}


def test_reconcile_multisearch_aggregates_correctly():
    "Happy path for ``_reconcile_multisearch_response``"
    results = _reconcile_multisearch_response(
        _make_response(HIT_FROM_RESPONSE),
        ORDER,
    )

    assert results.results == [
        {
            "&id": "b6417a9c-fb72-5973-aac6-dcfa79a075b8",
            "&key": "Email:b6417a9c-fb72-5973-aac6-dcfa79a075b8:*************",
            "body": {"displayText": "Order ID: SE|********|23|4", "text": "Order ID: SE|********|23|4", "type": "HTML"},
            "confidenceScore": "high",
            "matchReasons": list(set(["Email contains instrument code", "Email contains order ID"])),
            "matchedOrderFields": [],
            "matchedValues": [],
            "messageId": "2023-02-15T15:28:01Z",
            "participants": [],
            "subject": "VCON - Trade Confirm - 15-Feb-23 - GB00BD9MZZ71",
            "timestamps": {
                "localTimestampEnd": "2023-02-15T15:28:01.000000Z",
                "localTimestampStart": "2023-02-15T15:28:01.000000Z",
                "timestampEnd": "2023-02-15T15:28:01.000000Z",
                "timestampStart": "2023-02-15T15:28:01.000000Z",
            },
        }
    ]


@pytest.mark.regression
def test_parse_suggestion_hit_errors_on_missing_source():
    "Covers API-15"
    with pytest.raises(ParseSuggestionError):
        _parse_suggestion_hit({"_source": None}, ORDER)


@pytest.mark.regression
def test_reconcile_multisearch_handles_none_hit_source():
    "Covers API-15"
    results = [
        _reconcile_multisearch_response(_make_response({"_source": None}), ORDER),
        _reconcile_multisearch_response(_make_response({"_source": []}), ORDER),
        _reconcile_multisearch_response(_make_response({"_source": {}}), ORDER),
    ]

    for result in results:
        assert result.results == []

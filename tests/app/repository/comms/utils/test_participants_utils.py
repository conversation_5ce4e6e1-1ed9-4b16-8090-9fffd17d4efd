from enum import auto
from types import SimpleNamespace

from api_sdk.models.elasticsearch import RawResult, RawResultHits
from api_sdk.utils.utils import StringEnum

from app.repository.comms.utils.participants import (
    ACCOUNT_PERSON_MODEL_NAME,
    MARKET_PERSON_MODEL_NAME,
    CommsDetail,
    NetworkLink,
    NetworkNode,
    get_initials_from_name,
    get_network_map_nodes_and_links,
)

DUMMY_PARTICIPANTS = [
    SimpleNamespace(**{"id": "def", "name": "<PERSON>", "type": ACCOUNT_PERSON_MODEL_NAME, "count": 54}),
    SimpleNamespace(**{"id": "ghi", "name": "<PERSON><PERSON>", "type": MARKET_PERSON_MODEL_NAME, "count": 40}),
]


class NetworkMapTestsTypes(StringEnum):
    EMPTY_NODES_AND_LINKS = auto()
    VALUE_ERROR = auto()
    WITH_NODES_AND_LINKS = auto()


def generate_participants_agg_bucket(participants):
    return [
        SimpleNamespace(
            **{
                "key": f"{participant.id}|{participant.name}|{participant.type[0:3]}",
                "doc_count": participant.count,
            }
        )
        for participant in participants
    ]


def generate_network_links(root_node, participants):
    return [
        NetworkLink(
            id=f"{root_node.id}--{participant.id}",
            comms=participant.count,
            depth=root_node.depth + 1,
            source=root_node.id,
            target=participant.id,
            normalisedWeight=0.1,
        )
        for participant in participants
    ]


def generate_network_nodes(root_node, participants):
    return [
        NetworkNode(
            id=participant.id,
            depth=root_node.depth + 1,
            name=participant.name,
            initials=get_initials_from_name(participant.name),
            type=participant.type,
            totalComms=0,
            commsDetail={root_node.id: CommsDetail(name=root_node.name, count=participant.count)},
        )
        for participant in participants
    ]


def get_network_map_params_and_response(test_type: NetworkMapTestsTypes):
    root_node = NetworkNode(
        id="abc",
        name="John Doe",
        initials=get_initials_from_name("John Doe"),
        type="MarketPerson",
    )
    nodes_by_id = {}
    links_by_id = {}
    depth_result: RawResult = RawResult(
        took=2, timed_out=False, hits=RawResultHits(total=348, max_score=0.0, hits=[]), aggregations={}, last_sort=None
    )

    aggs, output = {
        NetworkMapTestsTypes.EMPTY_NODES_AND_LINKS: ({}, ([], [])),
        NetworkMapTestsTypes.VALUE_ERROR: (
            {"random_root_id": "junk_data"},
            ValueError(f"could not find parent node {root_node.id}"),
        ),
        NetworkMapTestsTypes.WITH_NODES_AND_LINKS: (
            {
                root_node.id: {
                    "doc_count": 556,
                    "ONLY_ID": {
                        "doc_count": 348,
                        "REVERSE": {
                            "doc_count": 348,
                            "PARTICIPANT": {
                                "doc_count": 556,
                                "FILTER_PARTICIPANTS": {
                                    "doc_count": 208,
                                    "LINKS": {
                                        "doc_count_error_upper_bound": 0,
                                        "sum_other_doc_count": 20,
                                        "buckets": generate_participants_agg_bucket(DUMMY_PARTICIPANTS),
                                    },
                                },
                            },
                        },
                    },
                }
            },
            (
                generate_network_nodes(root_node, DUMMY_PARTICIPANTS),
                generate_network_links(root_node, DUMMY_PARTICIPANTS),
            ),
        ),
    }[test_type]

    depth_result.aggregations = aggs
    return (
        {
            "response": depth_result,
            "parent_nodes": [root_node],
            "existing_nodes_lookup": nodes_by_id,
            "existing_links_lookup": links_by_id,
        },
        output,
    )


def test_get_initials_from_name():
    assert get_initials_from_name("") == ""
    assert get_initials_from_name("John") == "J"
    assert get_initials_from_name("John Doe") == "JD"
    assert get_initials_from_name("John Doe Junior") == "JJ"


def test_get_network_map():
    for test_type in NetworkMapTestsTypes:
        input_params, expected_output = get_network_map_params_and_response(test_type)

        # If the expect output is a handled value error, we need to handle it differently
        if isinstance(expected_output, ValueError):
            try:
                get_network_map_nodes_and_links(**input_params)
                assert False
            except ValueError:
                assert True
        else:
            assert get_network_map_nodes_and_links(**input_params) == expected_output

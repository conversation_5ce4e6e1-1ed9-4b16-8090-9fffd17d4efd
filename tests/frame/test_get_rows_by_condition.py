import pandas as pd
import pytest
from pandas import Index

try:
    from pandas.core.computation.ops import UndefinedVariableError
except ImportError:
    from pandas.errors import UndefinedVariableError
from pydantic import ValidationError

from se_core_tasks.core.auditor import Auditor
from se_core_tasks.frame.get_rows_by_condition import Params
from se_core_tasks.frame.get_rows_by_condition import run_get_rows_by_condition
from se_core_tasks.frame.get_rows_by_condition import SkipIfSourceFrameEmpty


@pytest.fixture()
def source_dataframe() -> pd.DataFrame:
    """
    Represents a source dataframe with two columns
    """
    df = pd.DataFrame({"a": [1, 2, 3], "b": ["test", pd.NA, "pass"]})
    return df


@pytest.fixture()
def source_dataframe_skips() -> pd.DataFrame:
    """
    Represents a source dataframe with two columns
    """
    df = pd.DataFrame(
        {"a": [1, 2, 3], "b": ["test", pd.NA, pd.NA]}, index=Index([10, 15, 20])
    )
    return df


@pytest.fixture()
def source_dataframe_multiple_cols() -> pd.DataFrame:
    """
    Represents a source dataframe with four columns
    """
    df = pd.DataFrame(
        {
            "a": [1, 2, 3],
            "b": ["test", pd.NA, "pass"],
            "c": [pd.NA] * 3,
            "d": [pd.NA] * 3,
        }
    )
    return df


@pytest.fixture()
def source_dataframe_on_2821() -> pd.DataFrame:
    """
    Represents a source dataframe for ON-2821 use case
    """
    df = pd.DataFrame(
        {
            "TYPE": ["ORDER", "FILL", "ORDER", "ORDER", "ORDER"],
            "As Of Date": [pd.NA, pd.NA, "2022-01-10", "2022-01-10", "2022-01-10"],
            "Create Date": [pd.NA, pd.NA, "2022-01-09", "2022-01-13", pd.NA],
        }
    )
    return df


@pytest.fixture()
def source_query_on_2821() -> str:
    """
    Represents a query for ON-2821 use case
    """
    return (
        "(`TYPE`.astype('string').str.upper() != 'ORDER') or (`TYPE`.astype('string').str.upper() == 'ORDER' and (`As Of Date`.isnull() or "
        "`Create Date`.isnull())) or (`TYPE`.astype('string').str.upper() == 'ORDER' and `As Of Date`.notnull() and "
        "`Create Date`.notnull() and `As Of Date` <= `Create Date`)"
    )


class TestGetRowsByCondition:
    """
    Test cases for "GetRowsByCondition" task
    """

    def test_empty_frame(self):
        source_frame = pd.DataFrame(columns=["a", "b"])
        params = Params(query="`a`.isnull()")

        outcome_df = run_get_rows_by_condition(source_frame=source_frame, params=params)

        assert outcome_df.empty
        assert outcome_df is not source_frame
        assert outcome_df.equals(source_frame)

    def test_raise_error_on_empty_query(self, source_dataframe):

        with pytest.raises(ValidationError) as e:
            _ = Params(query="   ")

        assert e.match("Received empty query")

    def test_skip_on_empty_false_empty_frame(self):
        source_frame = pd.DataFrame(columns=["a", "b"])
        params = Params(query="`a`.isnull()", skip_on_empty=False)

        outcome_df = run_get_rows_by_condition(source_frame=source_frame, params=params)

        assert outcome_df.empty
        assert outcome_df is not source_frame
        assert outcome_df.equals(source_frame)

    def test_skip_on_empty_false_post_query_empty(self, source_dataframe):
        params = Params(query="a>4")

        outcome_df = run_get_rows_by_condition(
            source_frame=source_dataframe, params=params
        )

        assert outcome_df.empty

    def test_skip_on_empty_true_empty_frame(self):
        source_frame = pd.DataFrame(columns=["a", "b"])
        params = Params(query="`a`.isnull()", skip_on_empty=True)

        with pytest.raises(SkipIfSourceFrameEmpty) as _:
            run_get_rows_by_condition(source_frame=source_frame, params=params)

    def test_skip_on_empty_true_post_query_empty(self, source_dataframe):
        params = Params(query="a>4", skip_on_empty=True)

        with pytest.raises(SkipIfSourceFrameEmpty) as _:
            run_get_rows_by_condition(source_frame=source_dataframe, params=params)

    def test_query_on_missing_col(self, source_dataframe):
        params = Params(query="c.isnull()")
        with pytest.raises(UndefinedVariableError) as _:
            run_get_rows_by_condition(source_frame=source_dataframe, params=params)

    def test_success_scenario(self, source_dataframe):
        params = Params(query="a<4 & b.notnull()", audit_skipped_rows=True)

        outcome_df = run_get_rows_by_condition(
            source_frame=source_dataframe, params=params
        )
        expected_df = pd.DataFrame({"a": [1, 3], "b": ["test", "pass"]}, index=[0, 2])
        assert outcome_df.equals(expected_df)

    def test_success_scenario_with_reset_index(self, source_dataframe):
        params = Params(query="a<4 & b.notnull()", reset_index=True)

        outcome_df = run_get_rows_by_condition(
            source_frame=source_dataframe, params=params
        )
        expected_df = pd.DataFrame({"a": [1, 3], "b": ["test", "pass"]}, index=[0, 1])
        assert outcome_df.equals(expected_df)

    def test_no_params(self, source_dataframe):
        with pytest.raises(ValidationError) as e:
            Params()
        assert e.match("Received empty query")

    def test_discard_rows_where_subset_of_columns_is_null(
        self, source_dataframe_multiple_cols
    ):
        params = Params(
            discard_rows_where_subset_of_columns_is_all_null=["b", "c", "d"]
        )

        outcome_df = run_get_rows_by_condition(
            source_frame=source_dataframe_multiple_cols, params=params
        )
        expected_df = pd.DataFrame(
            {"a": [1, 3], "b": ["test", "pass"], "c": [pd.NA] * 2, "d": [pd.NA] * 2},
            index=[0, 2],
        )
        assert outcome_df.equals(expected_df)

    def test_on_2821(
        self, source_dataframe_on_2821: pd.DataFrame, source_query_on_2821: str
    ):
        params = Params(query=source_query_on_2821)
        outcome_df = run_get_rows_by_condition(
            source_frame=source_dataframe_on_2821, params=params
        )
        expected_df = pd.DataFrame(
            {
                "TYPE": ["ORDER", "FILL", "ORDER", "ORDER"],
                "As Of Date": [pd.NA, pd.NA, "2022-01-10", "2022-01-10"],
                "Create Date": [pd.NA, pd.NA, "2022-01-13", pd.NA],
            },
            index=[0, 1, 3, 4],
        )
        assert outcome_df.equals(expected_df)

    def test_audit_message(self, source_dataframe):
        auditor = Auditor()
        params = Params(query="a<4 & b.notnull()", audit_skipped_rows=True)
        run_get_rows_by_condition(
            source_frame=source_dataframe, params=params, auditor=auditor
        )
        assert auditor.to_dataframe().loc[0, "ctx.skip_count"] == 1

    def test_audit_message_record_level(self, source_dataframe_skips):
        auditor = Auditor()
        query = "a<4 & b.notnull()"
        params = Params(query=query, audit_skipped_rows_record_level=True)
        run_get_rows_by_condition(
            source_frame=source_dataframe_skips, params=params, auditor=auditor
        )

        pd.testing.assert_frame_equal(
            auditor.to_dataframe().loc[
                :, ["message", "ctx.raw_index", "ctx.skip_count"]
            ],
            pd.DataFrame(
                {
                    "message": [f'Did not match query: "{query}"'],
                    "ctx.raw_index": [[15, 20]],
                    "ctx.skip_count": [1],
                }
            ),
        )

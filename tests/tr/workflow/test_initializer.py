import json

from unittest.mock import MagicMock

import pytest
import boto3
from moto import mock_aws
from freezegun import freeze_time
from se_elastic_schema.static.mifid2 import ReportStatus

from se_trades_tasks.tr.workflow.initializer import run_workflow_initializer
from se_trades_tasks.tr.workflow.static import ActionType, ScheduleTypeEnum


class TestWorkflowInitializer:
    """
    Test cases for "TestWorkflowInitializer" class
    """

    @pytest.fixture()
    def tr_config_data(self):
        tr_config = {
            "scheduleType": ScheduleTypeEnum.enable_auto_submission.value,
            "workflowName": "tr_workflow_initializer",
            "enabled": True,
            "cron": "30 12 * * MON-FRI",
            "recipients": ["<EMAIL>"],
            "scheduleISOTimeZone": "IST, Asia/Kolkata",
            "createdDateTime": "2024-01-22T06:52:24.360173",
        }

        return tr_config

    def test_no_execution_if_no_tr_config(self, caplog):
        es_client = MagicMock()

        run_workflow_initializer(
            es_client=es_client,
            realm="tenant.steeleye.co",
            tenant="tenant",
            tr_schedule_config=dict(),
            action_type=ActionType.CURRENT,
        )

        assert "TR auto scheduling config is not present for tenant" in caplog.text

    @mock_aws
    @freeze_time(time_to_freeze="2023-12-25 11:19:42.26576+00:00")
    @pytest.mark.parametrize(
        "realm, queue",
        [
            ("tenant.dev.steeleye.co", "tenant-dev-flows"),
            ("tenant.steeleye.co", "tenant-prod-flows"),
        ],
    )
    def test_valid_execution_for_current_action(self, tr_config_data, realm, queue):
        es_client = MagicMock()
        es_client.client.indices.exists.return_value = False

        region = "eu-west-1"
        sqs_client = boto3.resource("sqs", region_name=region)
        queue = sqs_client.create_queue(QueueName=queue)

        run_workflow_initializer(
            es_client=es_client,
            realm=realm,
            tenant="tenant",
            tr_schedule_config=tr_config_data,
            action_type=ActionType.CURRENT,
        )

        sqs_messages = queue.receive_messages()
        expected_result = {
            "Records": [
                {
                    "eventVersion": "1.0",
                    "eventSource": "aws:cloudwatch",
                    "awsRegion": "eu-west-1",
                    "eventName": "Rule:Schedule",
                    "realm": realm,
                    "bundle": {"id": "tr-workflow-unavista-generate-report"},
                    "args": {
                        "action": "current",
                        "fromDate": "1703203200000",
                        "toDate": "1703462399999",
                        "user": "system",
                        "lockDocId": "current:1703462400000:1703503182266",
                    },
                }
            ]
        }
        assert sqs_messages[0].body == json.dumps(expected_result)

    @mock_aws
    @freeze_time(time_to_freeze="2023-12-25 11:19:42.26576+00:00")
    def test_valid_execution_for_canc_transactions(self, tr_config_data):
        es_client = MagicMock()
        es_client.client.indices.exists.return_value = False

        region = "eu-west-1"
        realm = "tenant.dev.steeleye.co"
        sqs_client = boto3.resource("sqs", region_name=region)
        queue = sqs_client.create_queue(QueueName="tenant-dev-flows")

        run_workflow_initializer(
            es_client=es_client,
            realm=realm,
            tenant="tenant",
            tr_schedule_config=tr_config_data,
            action_type=ReportStatus.CANC.value,
        )

        sqs_messages = queue.receive_messages()
        expected_result = {
            "Records": [
                {
                    "eventVersion": "1.0",
                    "eventSource": "aws:cloudwatch",
                    "awsRegion": "eu-west-1",
                    "eventName": "Rule:Schedule",
                    "realm": "tenant.dev.steeleye.co",
                    "bundle": {"id": "tr-workflow-unavista-generate-report"},
                    "args": {
                        "user": "system",
                        "overrideQuery": {
                            "query": {
                                "bool": {
                                    "must_not": [
                                        {"exists": {"field": "&expiry"}},
                                        {"term": {"workflow.isReported": True}},
                                    ],
                                    "must": [
                                        {"term": {"&model": "RTS22Transaction"}},
                                        {"term": {"reportDetails.reportStatus": "CANC"}},
                                        {"terms": {"workflow.status": ["REPORTABLE", "REPORTABLE_USER_OVERRIDE"]}},
                                    ],
                                }
                            }
                        },
                        "lockDockId": "CANC:0:1703503182266",
                    },
                }
            ]
        }
        assert sqs_messages[0].body == json.dumps(expected_result)

    @mock_aws
    @freeze_time(time_to_freeze="2024-01-10 11:45:42.26576+00:00")
    def test_execution_for_no_previous_submission(self, tr_config_data):
        es_client = MagicMock()
        es_client.client.indices.exists.return_value = True
        es_client.client.search.return_value = {
            "aggregations": {
                "DUE": {
                    "doc_count_error_upper_bound": 0,
                    "sum_other_doc_count": 0,
                    "buckets": [
                        {
                            "key": "current",
                            "doc_count": 6,
                            "TO": {
                                "doc_count_error_upper_bound": 0,
                                "sum_other_doc_count": 5,
                                "buckets": [
                                    {
                                        "key": 1704844800000,
                                        "key_as_string": "2024-01-10T00:00:00.000Z",
                                        "doc_count": 1,
                                        "CREATED": {
                                            "value": 1704885895884.0,
                                            "value_as_string": "2024-01-10T11:24:55.884Z",
                                        },
                                    }
                                ],
                            },
                        }
                    ],
                }
            }
        }

        region = "eu-west-1"
        realm = "tenant.dev.steeleye.co"
        sqs_client = boto3.resource("sqs", region_name=region)
        queue = sqs_client.create_queue(QueueName="tenant-dev-flows")

        run_workflow_initializer(
            es_client=es_client,
            realm=realm,
            tenant="tenant",
            tr_schedule_config=tr_config_data,
            action_type=ActionType.CURRENT,
        )

        sqs_messages = queue.receive_messages()
        expected_result = {
            "Records": [
                {
                    "eventVersion": "1.0",
                    "eventSource": "aws:cloudwatch",
                    "awsRegion": "eu-west-1",
                    "eventName": "Rule:Schedule",
                    "realm": "tenant.dev.steeleye.co",
                    "bundle": {"id": "tr-workflow-unavista-generate-report"},
                    "args": {
                        "action": "current",
                        "fromDate": "1704758400000",
                        "toDate": "1704844799999",
                        "user": "system",
                        "lockDocId": "current:1704844800000:1704887142266",
                    },
                }
            ]
        }
        assert sqs_messages[0].body == json.dumps(expected_result)

    @mock_aws
    @freeze_time(time_to_freeze="2024-01-10 11:25:42.26576+00:00")
    def test_execution_for_previous_submission(self, tr_config_data):
        es_client = MagicMock()
        es_client.client.indices.exists.return_value = True
        es_client.client.search.return_value = {
            "aggregations": {
                "DUE": {
                    "doc_count_error_upper_bound": 0,
                    "sum_other_doc_count": 0,
                    "buckets": [
                        {
                            "key": "current",
                            "doc_count": 6,
                            "TO": {
                                "doc_count_error_upper_bound": 0,
                                "sum_other_doc_count": 5,
                                "buckets": [
                                    {
                                        "key": 1704844800000,
                                        "key_as_string": "2024-01-10T00:00:00.000Z",
                                        "doc_count": 1,
                                        "CREATED": {
                                            "value": 1704885895884.0,
                                            "value_as_string": "2024-01-10T11:24:55.884Z",
                                        },
                                    }
                                ],
                            },
                        }
                    ],
                }
            }
        }

        region = "eu-west-1"
        realm = "tenant.dev.steeleye.co"
        sqs_client = boto3.resource("sqs", region_name=region)
        queue = sqs_client.create_queue(QueueName="tenant-dev-flows")

        run_workflow_initializer(
            es_client=es_client,
            realm=realm,
            tenant="tenant",
            tr_schedule_config=tr_config_data,
            action_type=ActionType.CURRENT,
        )

        sqs_messages = queue.receive_messages()
        assert sqs_messages == []

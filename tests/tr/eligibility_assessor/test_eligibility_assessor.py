from pathlib import Path

import addict
import numpy as np
import pandas as pd
from se_elastic_schema.static.mifid2 import RTS22TransactionStatus

from se_core_tasks.core.auditor import Auditor
from se_trades_tasks.tr.eligibility_assessor.eligibility_assessor import (
    WorkflowFields,
    InstrumentFields,
    EligibilityAssessor,
    InstrumentSourceFields,
    TempColumns,
    run_eligibility_assessor,
)

DEFAULT_ELIGIBILITY = {
    "onFirds": False,
    "eligible": True,
    "reason": "Non-EEA traded Derivative with uToTV constituents",
    "terminationDate": pd.NaT,
    "totv": False,
    "underlyingOnFirds": True,
    "utotv": True,
    "applicableVenues": [],
    "discountedVenues": [],
    "firstAdmissionToTrading": pd.NaT,
}


DATA_PATH = Path(__file__).parent.joinpath("data")
FETCH_INSTRUMENT = DATA_PATH.joinpath("fetch_instruments.csv")


class MockClient:

    meta = addict.Dict(model="&model")
    MAX_QUERY_SIZE = 1024
    MAX_TERMS_SIZE = 1024

    @staticmethod
    def search(**kwargs) -> addict.Dict:
        return addict.Dict(hits={"hits": {"reportingMechanism": None}})

    @staticmethod
    def scroll(**kwargs) -> pd.DataFrame:
        return pd.read_csv(FETCH_INSTRUMENT)


class MockNoISIN(MockClient):
    @staticmethod
    def scroll(**kwargs) -> pd.DataFrame:
        return pd.DataFrame()


class TestEligibilityAssessor:
    def test_end_to_end(self, input_frame, expected_frame):
        result = run_eligibility_assessor(
            source_frame=input_frame, tenant="dummy", es_client=MockClient(), srp_client=MockClient()
        )

        pd.testing.assert_frame_equal(result, expected_frame)

    def test_end_to_end_no_isin_in_srp_case(self, input_frame_no_isins, expected_frame_no_isins):
        result = run_eligibility_assessor(
            source_frame=input_frame_no_isins, tenant="dummy", es_client=MockClient(), srp_client=MockNoISIN()
        )

        pd.testing.assert_frame_equal(result, expected_frame_no_isins)

    def test_end_to_end_no_instruments_at_all(self, input_frame_no_instruments, expected_frame_no_isins):
        result = run_eligibility_assessor(
            source_frame=input_frame_no_instruments, tenant="dummy", es_client=MockClient(), srp_client=MockNoISIN()
        )

        pd.testing.assert_frame_equal(result, expected_frame_no_isins)

    def test_null_report_mechanism_in_get_firdsmodel(
        self,
        source_frame_complete,
        eligibility_result_with_reportable_records,
        non_reportable_mask,
    ):

        mock_client_ = MockClient()

        task = EligibilityAssessor(name="test-eligibility-assessor")
        result = task._get_firds_model_by_jurisdiction(es_client=mock_client_, tenant="dummy")
        expected = InstrumentSourceFields.FCA_FIRDS_INSTRUMENT

        assert result == expected

    def test_eligibility_with_reportable_records(
        self,
        source_frame_complete,
        eligibility_result_with_reportable_records,
        non_reportable_mask,
    ):

        self.execute_assess_eligibility_of_non_isin_carrying_securities(
            source_frame_complete=source_frame_complete,
            eligibility_result_with_reportable_records=eligibility_result_with_reportable_records,
            non_reportable_mask=non_reportable_mask,
        )

        eligibility = [
            {
                "onFirds": True,
                "eligible": True,
                "applicableVenues": ["Venue1", "Venue2"],
                "discountedVenues": [],
                "firstAdmissionToTrading": pd.NaT,
                "terminationDate": pd.NaT,
                "totv": True,
                "underlyingOnFirds": True,
                "utotv": True,
            },
            DEFAULT_ELIGIBILITY,
            DEFAULT_ELIGIBILITY,
            pd.NA,
            pd.NA,
        ]

        df = pd.DataFrame(
            {
                WorkflowFields.STATUS: [
                    RTS22TransactionStatus.REPORTABLE,
                    RTS22TransactionStatus.REPORTABLE,
                    RTS22TransactionStatus.REPORTABLE,
                    RTS22TransactionStatus.NON_REPORTABLE,
                    RTS22TransactionStatus.NON_REPORTABLE,
                ],
                WorkflowFields.ELIGIBILITY: [pd.NA] * 5,
                WorkflowFields.ELIGIBILITY_EXECUTION_VENUE: [pd.NA] * 5,
                WorkflowFields.ELIGIBILITY_TOTV_ON_EXECUTION_VENUE: [pd.NA] * 5,
            },
            index=[10, 11, 12, 13, 14],
        )

        mask = pd.Series(index=df.index, data=[True] * 5)

        df.loc[mask, WorkflowFields.ELIGIBILITY] = [eligibility[i] for i in range(df.loc[mask, :].shape[0])]

        assert eligibility_result_with_reportable_records.equals(df)

    @staticmethod
    def execute_assess_eligibility_of_non_isin_carrying_securities(
        source_frame_complete,
        eligibility_result_with_reportable_records,
        non_reportable_mask,
    ):

        task = EligibilityAssessor(name="test-eligibility-assessor")
        task._assess_eligibility_of_non_isin_carrying_securities(
            df=source_frame_complete,
            eligibility_result=eligibility_result_with_reportable_records,
            non_reportable_mask=non_reportable_mask,
        )

    def test_not_populated_isin(self):
        task = EligibilityAssessor(name="test-eligibility-assessor")
        source_frame = pd.DataFrame({"empty_isin": ["", "   ", np.nan, pd.NA, "IAMAVALIDISIN"]})

        result = task.handle_not_populated_isin(df=source_frame, column="empty_isin")
        expected_result = pd.Series([pd.NA, pd.NA, pd.NA, pd.NA, "IAMAVALIDISIN"])
        assert expected_result.equals(result)

    def test_elegibility_assessor_end_to_end(
        self,
        auditor: Auditor,
        elegibility_assessor_source_df: pd.DataFrame,
        eligibility_assessor_eligibility_result_df: pd.DataFrame,
    ):
        result = EligibilityAssessor._run_elegibility_assessor(
            source_frame=elegibility_assessor_source_df,
            firds_model="FcaFirdsInstrument",
            eligibility_result=eligibility_assessor_eligibility_result_df,
            srp_client="reference-data",
            auditor=auditor,
        )

        assert not pd.testing.assert_frame_equal(left=result, right=eligibility_assessor_eligibility_result_df)

    def test_get_isin_instruments_and_eligibility_mask(
        self,
        auditor: Auditor,
        get_isin_instruments_and_eligibility_mask_series: pd.Series,
        get_isin_instruments_and_eligibility_mask_df: pd.DataFrame,
    ):
        srp_client = addict.Dict(
            {
                "MAX_TERMS_SIZE": 1024,
                "meta": {
                    "key": "&key",
                    "model": "&model",
                    "id": "&id",
                    "hash": "&hash",
                },
            }
        )

        isin, mask = EligibilityAssessor._get_isin_instruments_and_eligibility_mask(
            record=get_isin_instruments_and_eligibility_mask_series,
            instruments=get_isin_instruments_and_eligibility_mask_df,
            srp_client=srp_client,
            firds_model="FcaFirdsInstrument",
            use_underlying=False,
        )

        pd.testing.assert_series_equal(left=mask.reset_index(drop=True), right=pd.Series([False] * 4))

        assert (isin["instrumentIdCode"] == "ABCD").all()

        assert isin["venue.terminationDate"].iloc[3] == pd.to_datetime("2023-10-11 23:59:59", utc=True)

    def test__override_eligibility_result_if_requested_by_client_case_1(
        self,
    ):
        """
        Testing that runs successfully when transactions are marked as none reportable.

        And match the correct mask conditions.

        This test covers the following:
        - `InstrumentFields.INSTRUMENT_ISIN_PATH` should be 12 in length
        - `TempColumns.DATA_CATEGORY` should be in allowed list

        If the above is followed:
        - `WorkflowFields.STATUS` is updated to `RTS22TransactionStatus.REPORTABLE_USER_OVERRIDE`
        - `WorkflowFields.ELIGIBILITY` is overwritten
        Otherwise:
        - Previous values are preserved
        """
        target_df: pd.DataFrame = pd.DataFrame(
            {
                WorkflowFields.STATUS: [
                    RTS22TransactionStatus.NON_REPORTABLE,
                    RTS22TransactionStatus.NON_REPORTABLE,
                    RTS22TransactionStatus.NON_REPORTABLE,
                    RTS22TransactionStatus.NON_REPORTABLE,
                ],
                WorkflowFields.ELIGIBILITY_EXECUTION_VENUE: [pd.NA] * 4,
                WorkflowFields.ELIGIBILITY: [
                    {
                        WorkflowFields.ELIGIBLE: False,
                        WorkflowFields.IS_DEFAULTED: False,
                        WorkflowFields.ON_FIRDS: True,
                        WorkflowFields.TOTV: True,
                        WorkflowFields.REASON: "This will be removed",
                        WorkflowFields.APPLICABLE_VENUES: ["BMTF", "XOOS", "TSASS"],
                    },
                ]
                * 4,
            }
        )

        df: pd.DataFrame = pd.DataFrame(
            {
                InstrumentFields.INSTRUMENT_PATH: [
                    {InstrumentFields.INSTRUMENT_ISIN_PATH: "US912810TG31"},
                    {InstrumentFields.INSTRUMENT_ISIN_PATH: "US812810TG32"},
                    {InstrumentFields.INSTRUMENT_ISIN_PATH: "SHORT"},
                    {InstrumentFields.INSTRUMENT_ISIN_PATH: "US812810TG32"},
                ],
                TempColumns.DATA_CATEGORY: [
                    "BMTF",
                    "TREU",
                    "TREU",
                    "not-in-allowed-value",
                ],
            }
        )

        task = EligibilityAssessor(name="test-eligibility-assessor")

        task._override_eligibility_result_if_requested_by_client(
            source_frame=df,
            non_reportable_mask=pd.Series([True] * 4),
            target=target_df,
        )

        pd.testing.assert_frame_equal(
            left=target_df,
            right=pd.DataFrame(
                {
                    WorkflowFields.STATUS: [
                        RTS22TransactionStatus.REPORTABLE_USER_OVERRIDE,
                        RTS22TransactionStatus.REPORTABLE_USER_OVERRIDE,
                        RTS22TransactionStatus.NON_REPORTABLE,
                        RTS22TransactionStatus.NON_REPORTABLE,
                    ],
                    WorkflowFields.ELIGIBILITY_EXECUTION_VENUE: [
                        "BMTF",
                        "TREU",
                        pd.NA,
                        pd.NA,
                    ],
                    WorkflowFields.ELIGIBILITY: [
                        {
                            WorkflowFields.ELIGIBLE: True,
                            WorkflowFields.IS_DEFAULTED: True,
                            WorkflowFields.ON_FIRDS: True,
                            WorkflowFields.TOTV: True,
                            WorkflowFields.APPLICABLE_VENUES: ["BMTF"],
                        },
                        {
                            WorkflowFields.ELIGIBLE: True,
                            WorkflowFields.IS_DEFAULTED: True,
                            WorkflowFields.ON_FIRDS: True,
                            WorkflowFields.TOTV: True,
                            WorkflowFields.APPLICABLE_VENUES: ["TREU"],
                        },
                        {
                            WorkflowFields.ELIGIBLE: False,
                            WorkflowFields.IS_DEFAULTED: False,
                            WorkflowFields.ON_FIRDS: True,
                            WorkflowFields.TOTV: True,
                            WorkflowFields.REASON: "This will be removed",
                            WorkflowFields.APPLICABLE_VENUES: ["BMTF", "XOOS", "TSASS"],
                        },
                        {
                            WorkflowFields.ELIGIBLE: False,
                            WorkflowFields.IS_DEFAULTED: False,
                            WorkflowFields.ON_FIRDS: True,
                            WorkflowFields.TOTV: True,
                            WorkflowFields.REASON: "This will be removed",
                            WorkflowFields.APPLICABLE_VENUES: ["BMTF", "XOOS", "TSASS"],
                        },
                    ],
                }
            ),
        )

    def test__override_eligibility_result_if_requested_by_client_case_2(
        self,
    ):
        """
        Testing when transaction status are not `RTS22TransactionStatus.NON_REPORTABLE`,
        no changes are made
        """
        target_df: pd.DataFrame = pd.DataFrame(
            {
                WorkflowFields.STATUS: [RTS22TransactionStatus.NON_REPORTABLE],
                WorkflowFields.ELIGIBILITY_EXECUTION_VENUE: [pd.NA],
                WorkflowFields.ELIGIBILITY: [
                    {
                        WorkflowFields.ELIGIBLE: False,
                        WorkflowFields.IS_DEFAULTED: False,
                        WorkflowFields.ON_FIRDS: True,
                        WorkflowFields.TOTV: True,
                        WorkflowFields.REASON: "This will be removed",
                        WorkflowFields.APPLICABLE_VENUES: ["BMTF", "XOOS", "TSASS"],
                    },
                ],
            }
        )

        df: pd.DataFrame = pd.DataFrame(
            {
                InstrumentFields.INSTRUMENT_PATH: [
                    {InstrumentFields.INSTRUMENT_ISIN_PATH: "US912810TG31"},
                ],
                TempColumns.DATA_CATEGORY: ["BMTF"],
            }
        )

        task = EligibilityAssessor(name="test-eligibility-assessor")

        task._override_eligibility_result_if_requested_by_client(
            source_frame=df,
            non_reportable_mask=pd.Series([False]),
            target=target_df,
        )

        pd.testing.assert_frame_equal(
            left=target_df,
            right=pd.DataFrame(
                {
                    WorkflowFields.STATUS: [
                        RTS22TransactionStatus.NON_REPORTABLE,
                    ],
                    WorkflowFields.ELIGIBILITY_EXECUTION_VENUE: [pd.NA],
                    WorkflowFields.ELIGIBILITY: [
                        {
                            WorkflowFields.ELIGIBLE: False,
                            WorkflowFields.IS_DEFAULTED: False,
                            WorkflowFields.ON_FIRDS: True,
                            WorkflowFields.TOTV: True,
                            WorkflowFields.REASON: "This will be removed",
                            WorkflowFields.APPLICABLE_VENUES: ["BMTF", "XOOS", "TSASS"],
                        },
                    ],
                }
            ),
        )

    def test_get_isin_instruments_and_eligibility_mask_without_termination_date(
        self,
        auditor: Auditor,
        get_isin_instruments_and_eligibility_mask_series: pd.Series,
        isin_instrument_without_termination_date_df: pd.DataFrame,
    ):
        srp_client = addict.Dict(
            {
                "MAX_TERMS_SIZE": 1024,
                "meta": {
                    "key": "&key",
                    "model": "&model",
                    "id": "&id",
                    "hash": "&hash",
                },
            }
        )

        isin, mask = EligibilityAssessor._get_isin_instruments_and_eligibility_mask(
            record=get_isin_instruments_and_eligibility_mask_series,
            instruments=isin_instrument_without_termination_date_df,
            srp_client=srp_client,
            firds_model="FcaFirdsInstrument",
            use_underlying=False,
        )

        assert isin[InstrumentFields.VENUE_TERMINATION_DATE].iloc[0] != pd.NaT

import pytest
from unittest.mock import patch, call

import pandas as pd
from se_elastic_schema.validators.iso.isin import ISIN

from se_trades_tasks.tr.transformations.fix_universal.fix_universal_transformations import (
    FixUniversalTransformations,
    SkipNoOrdersToProcess,
)
from se_trades_tasks.tr.transformations.fix_universal.static import SourceColumns, TempColumns


class TestFixUniversalTransformations:
    def test_end_to_end(self, mocker, input_df, mock_get_tenant_lei, mock_instrument_identifiers, expected_df):
        # mock get_tenant_lei
        mocker.patch(
            "se_trades_tasks.tr.transformations.fix_universal.fix_universal_transformations.run_get_tenant_lei",
            return_value=mock_get_tenant_lei,
        )
        # mock instrument identifiers
        mocker.patch(
            "se_trades_tasks.tr.transformations.fix_universal.fix_universal_transformations.run_instrument_identifiers",
            return_value=mock_instrument_identifiers,
        )

        transformations = FixUniversalTransformations(
            source_frame=input_df, es_client=None, tenant="dummy", auditor=None
        )

        result = transformations.process()

        pd.testing.assert_frame_equal(result, expected_df)

    def test_it_can_remove_orders_that_were_canceled(self, caplog, mocker, input_df):
        mocker.patch(
            "se_trades_tasks.tr.transformations.fix_universal.fix_universal_transformations.run_get_tenant_lei",
            return_value=pd.DataFrame({TempColumns.TENANT_LEI: ["lei:dummylei"] * 1}),
        )

        mocker.patch.object(ISIN, "validate_isin_code", return_value=mocker.MagicMock(is_valid=True))

        input_df[SourceColumns.EXECTYPE] = pd.Series(
            data=["F", "H", "F", "F", "H"],
            name=SourceColumns.EXECTYPE,
        )

        input_df[SourceColumns.EXECREFID] = pd.Series(
            data=["1", "1", "2", "3", "2"],
            name=SourceColumns.EXECREFID,
        )

        input_df[SourceColumns.EXECID] = pd.Series(
            data=["1", "1", "2", "3", "2"],
            name=SourceColumns.EXECID,
        )

        with patch(
            "se_trades_tasks.tr.transformations.fix_universal.fix_universal_transformations.logger"
        ) as mock_logger:
            transformations = FixUniversalTransformations(
                source_frame=input_df, es_client=None, tenant="dummy", auditor=None
            )

            result = transformations.process()

        assert len(result) == 1
        assert result.index.tolist() == [0]

        assert mock_logger.info.call_count == 2

        mock_logger.assert_has_calls(
            calls=[
                call.info("Removed 4 order(s)."),
                call.info("Orders removed: ['1', '2']"),
            ],
        )

    def test_it_does_not_log_anything_if_no_orders_were_removed(self, caplog, mocker, input_df):
        mocker.patch(
            "se_trades_tasks.tr.transformations.fix_universal.fix_universal_transformations.run_get_tenant_lei",
            return_value=pd.DataFrame({TempColumns.TENANT_LEI: ["lei:dummylei"] * 1}),
        )

        mocker.patch.object(ISIN, "validate_isin_code", return_value=mocker.MagicMock(is_valid=True))

        input_df[SourceColumns.EXECTYPE] = pd.Series(
            data=["F", "F", "F", "F", "F"],
            name=SourceColumns.EXECTYPE,
        )

        input_df[SourceColumns.EXECREFID] = pd.Series(
            data=["1", "1", "2", "3", "2"],
            name=SourceColumns.EXECREFID,
        )

        input_df[SourceColumns.EXECID] = pd.Series(
            data=["1", "1", "2", "3", "2"],
            name=SourceColumns.EXECID,
        )

        with patch(
            "se_trades_tasks.tr.transformations.fix_universal.fix_universal_transformations.logger"
        ) as mock_logger:
            transformations = FixUniversalTransformations(
                source_frame=input_df, es_client=None, tenant="dummy", auditor=None
            )

            result = transformations.process()

        assert len(result) == 5

        assert mock_logger.info.call_count == 0

    def test_it_removes_canceled_orders(self, caplog, mocker, input_df):
        """
        This test ensures:
        - that orders that were canceled are removed from the dataframe
        - that the correct number of orders removed is logged
        - that the correct order ref ids are logged
        - that an exception is raised if there are no orders to process after removing canceled orders
        """
        mocker.patch(
            "se_trades_tasks.tr.transformations.fix_universal.fix_universal_transformations.run_get_tenant_lei",
            return_value=pd.DataFrame({TempColumns.TENANT_LEI: ["lei:dummylei"] * 1}),
        )

        mocker.patch.object(ISIN, "validate_isin_code", return_value=mocker.MagicMock(is_valid=True))

        input_df[SourceColumns.EXECTYPE] = pd.Series(
            data=["F", "H", "F", "F", "F"],
            name=SourceColumns.EXECTYPE,
        )

        input_df[SourceColumns.EXECREFID] = pd.Series(
            data=["1", "1", "1", "1", "1"],
            name=SourceColumns.EXECREFID,
        )

        input_df[SourceColumns.EXECID] = pd.Series(
            data=["1", "1", "1", "1", "1"],
            name=SourceColumns.EXECID,
        )

        with patch(
            "se_trades_tasks.tr.transformations.fix_universal.fix_universal_transformations.logger"
        ) as mock_logger:
            with pytest.raises(SkipNoOrdersToProcess) as e:
                transformations = FixUniversalTransformations(
                    source_frame=input_df, es_client=None, tenant="dummy", auditor=None
                )

                transformations.process()

        assert e.value.args[0] == "No orders to process after removing canceled orders."

        assert mock_logger.info.call_count == 2

        mock_logger.assert_has_calls(
            calls=[
                call.info("Removed 5 order(s)."),
                call.info("Orders removed: ['1']"),
            ],
        )

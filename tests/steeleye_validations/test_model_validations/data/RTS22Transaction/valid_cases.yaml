cases:
  - error_code: SE_DV_1
    record_mutation: {"transactionDetails": {"priceNotation": "MONE", "priceCurrency":"USD"}}
  - error_code: SE_DV_3
    record_mutation: {"transactionDetails": {"priceCurrency":"USD"}}
  - error_code: SE_DV_4
    record_mutation: {"transactionDetails": {"quantityNotation": "MONE", "quantityCurrency":"USD"}}
  - error_code: SE_DV_5
    record_mutation: {"transactionDetails": {"quantityCurrency":"USD"}}
  - error_code: SE_DV_6
    record_mutation: {"transactionDetails": {"upFrontPayment": 1.23, "upFrontPaymentCurrency":"USD"}}
  - error_code: SE_DV_7
    record_mutation: {"transactionDetails": {"upFrontPaymentCurrency":"USD"}}
  - error_code: SE_DV_14
    record_mutation: {"transactionDetails": {"tradingDateTime": "2021-03-30T13:47:08.124512"}}
  - error_code: SE_DV_15
    record_mutation: {"transactionDetails": {"tradingDateTime": "2021-03-30T10:53:48.800441+00:00"}}
  - error_code: SE_DV_16
    record_mutation: {"transactionDetails": {"buySellIndicator":"BUYI"}, "instrumentDetails": {"instrument": {"instrumentClassification":"CPPPCO"}}}
  - error_code: SE_DV_17
    record_mutation: {"transactionDetails": {"netAmount": 23.10}, "instrumentDetails": {"instrument": {"instrumentClassification":"DBCCCC"}}}
  - error_code: SE_DV_17
    record_mutation: {"transactionDetails": {"netAmount": 23.10}, "instrumentDetails": {"instrument": {"instrumentClassification":"DCCCCC"}}}
  - error_code: SE_DV_17
    record_mutation: {"transactionDetails": {"netAmount": 23.10}, "instrumentDetails": {"instrument": {"instrumentClassification":"DTCCCC"}}}
  - error_code: SE_DV_17
    record_mutation: {"transactionDetails": {"netAmount": 23.10}, "instrumentDetails": {"instrument": {"instrumentClassification":"DNCCCC"}}}
  - error_code: SE_DV_18
    record_mutation: {"transactionDetails": {}, "instrumentDetails": {"instrument": {"instrumentClassification":"CPPPCO"}}}
  - error_code: SE_DV_19
    record_mutation: {"transactionDetails": {"netAmount": 23.10}}
  - error_code: SE_DV_19
    record_mutation: {"transactionDetails": {"netAmount": 0}}
  - error_code: SE_DV_21
    record_mutation: {"transactionDetails": {"priceNotation": "MONE"}}
  - error_code: SE_DV_22
    record_mutation: {"transactionDetails": {"priceNotation": "MONE", "quantityNotation": "UNIT"}, "instrumentDetails": {"instrument": {"instrumentClassification":"DPPPCO"}}}
  - error_code: SE_DV_23
    record_mutation: {"transactionDetails": {"quantityNotation": "MONE"}}
  - error_code: SE_DV_24
    record_mutation: {"transactionDetails": {"tradingCapacity": "DEAL"}}
  - error_code: SE_DV_25
    record_mutation: {"transactionDetails": {"tradingCapacity": "MTCH"}, "parties": {"buyer": [{"&key": "AccountFirm", "firmIdentifiers": {"lei": "12340084UKLVMY22DS16"}}], "executingEntity": {"firmIdentifiers": {"lei": "54930084UKLVMY22DS16"}}, "seller": [{"&key": "AccountFirm", "firmIdentifiers": {"lei": "67890084UKLVMY22DS16"}}]}}
  - error_code: SE_DV_26
    record_mutation: {"transactionDetails": {"tradingCapacity": "DEAL"}}
  - error_code: SE_DV_28
    record_mutation: {"reportDetails": {"transactionRefNo": "ABCDEFGH"}}
  - error_code: SE_DV_29
    record_mutation: {"reportDetails": {"transactionRefNo": "ABCDEFGH"}}
  - error_code: SE_DV_30
    record_mutation: {"transactionDetails": {"venue": "XOFF"}}
  - error_code: SE_DV_31
    record_mutation: {"transactionDetails": {"venue": "XOFF"}}
  - error_code: SE_DV_32
    record_mutation: {"transactionDetails": {"complexTradeComponentId": "AQWERFSCTV"}}
  - error_code: SE_DV_32
    record_mutation: {"transactionDetails": {"complexTradeComponentId": "AQW1234RAQW1234RAQW1234RAQW1234RYUI"}}
  - error_code: SE_DV_33
    record_mutation: {"transactionDetails": {"netAmount": "*********"}}
  - error_code: SE_DV_34
    record_mutation: {"transactionDetails": {"venue": "XOFF"}, "reportDetails": {}}
  - error_code: SE_DV_35
    record_mutation: {"reportDetails": {"tradingVenueTransactionIdCode" : "AQWSDFRETG1234"}}
  - error_code: SE_DV_36
    record_mutation: {"reportDetails": {"transactionRefNo" : "AQWSDFRETG1234"}}
  - error_code: SE_DV_37
    record_mutation: {"transactionDetails": {"derivativeNotionalChange": "INCR", "upFrontPayment": 23.5}}
  - error_code: SE_DV_39
    record_mutation: {"transactionDetails": {"quantityNotation": "MONE", "priceNotation": "PERC"}, "instrumentDetails": {"instrument": {"instrumentClassification": "DBPOIU"}}}
  - error_code: SE_DV_40
    record_mutation: {"transactionDetails": {"upFrontPayment": "1324658"}}
  - error_code: SE_DV_40
    record_mutation: {"transactionDetails": {"upFrontPayment": "123456789123456789"}}
  - error_code: SE_DV_41
    record_mutation: {"instrumentDetails": {"instrument": {"instrumentClassification": "JTAACA"}}, "tradersAlgosWaiversIndicators": {"commodityDerivativeIndicator": True}}
  - error_code: SE_DV_42
    record_mutation: {"transactionDetails": {"derivativeNotionalChange": "INCR"}}
  - error_code: SE_DV_42
    record_mutation: {"transactionDetails": {"derivativeNotionalChange": "DECR"}}
  - error_code: SE_DV_43
    record_mutation: {"tradersAlgosWaiversIndicators": {"securitiesFinancingTxnIndicator": True}}
  - error_code: SE_DV_44
    record_mutation: {"transactionDetails": {"venue": "XOFF"}, "tradersAlgosWaiversIndicators": {"waiverIndicator": []}}
  - error_code: SE_DV_45
    record_mutation: {"transmissionDetails": {"orderTransmissionIndicator": True}}
  - error_code: SE_DV_46
    record_mutation: {"transmissionDetails": {"orderTransmissionIndicator": True}}
  - error_code: SE_DV_46
    record_mutation: {"transmissionDetails": {"orderTransmissionIndicator": False}}
  - error_code: SE_DV_47
    record_mutation: {"transactionDetails": {"tradingCapacity": "MTCH"}, "transmissionDetails": {"orderTransmissionIndicator": False}}
  - error_code: SE_DV_50
    record_mutation: {"instrumentDetails": {"instrument": {"instrumentClassification": "FFNAAA", "notionalCurrency1": "USD"}}}
  - error_code: SE_DV_51
    record_mutation: {"workflow": {"eligibility": {"totv": True}}, "instrumentDetails": {"instrument": {"instrumentClassification": "FFWAAA", "fxDerivatives": {}}}}
  - error_code: SE_DV_53
    record_mutation: {"transactionDetails": {"quantityCurrency": "USD"}, "instrumentDetails": {"instrument": {"instrumentClassification": "FFCAAA", "fxDerivatives": {"notionalCurrency2":"USD"}}}}
  - error_code: SE_DV_54
    record_mutation: {"instrumentDetails": {"instrument": {"fxDerivatives": {"notionalCurrency2":"USD"}}}}
  - error_code: SE_DV_55
    record_mutation: {"workflow": {"eligibility": {"totv": True}}, "instrumentDetails": {"instrument": {"instrumentClassification": "RWBAAA", "derivative": {"priceMultiplier": 2.3}}}}
  - error_code: SE_DV_56
    record_mutation: {"workflow": {"eligibility": {"totv": True}}, "instrumentDetails": {"instrument": {"derivative": {"priceMultiplier": 2.3}}}}
  - error_code: SE_DV_57
    record_mutation: {"instrumentDetails": {"instrument": {"derivative": {"underlyingInstruments": [{"underlyingInstrumentCode": "US0378331005"}]}}}}
  - error_code: SE_DV_58
    record_mutation: {"instrumentDetails": {"instrument": {"instrumentClassification": "RWBAAA"}}}
  - error_code: SE_DV_59
    record_mutation: {"instrumentDetails": {"instrument": {"instrumentClassification": "AABAAA"}}}
  - error_code: SE_DV_60
    record_mutation: {"instrumentDetails": {"instrument": {"instrumentIdCode": "US0378331005"}}}
  - error_code: SE_DV_61
    record_mutation: {"workflow": {"eligibility": {"totv": True}},"instrumentDetails": {"instrument": { "instrumentClassification": "OCPAPA", "derivative": {"deliveryType": "PHYS"}}}}
  - error_code: SE_DV_62
    record_mutation: {"workflow": {"eligibility": {"totv": True}}, "instrumentDetails": {"instrument": {"instrumentClassification": "RWDTMA", "derivative": {"expiryDate": "2021-03-31"}}}}
  - error_code: SE_DV_63
    record_mutation: {"workflow": {"eligibility": {"totv": True}}, "instrumentDetails": {"instrument": {"instrumentClassification": "ECDTMA", "derivative": {}}}}
  - error_code: SE_DV_64
    record_mutation: {"transactionDetails": {"tradingDateTime": "2021-02-01T21:07:44+00:00"}, "instrumentDetails": {"instrument": {"derivative": {"expiryDate": "2021-03-31"}}}}
  - error_code: SE_DV_65
    record_mutation: {"workflow": {"eligibility": {"totv": True}}, "instrumentDetails": {"instrument": {"instrumentClassification": "DAAAAA", "bond": {"maturityDate": "2021-03-31"}}}}
  - error_code: SE_DV_66
    record_mutation: {"workflow": {"eligibility": {"totv": True}}, "instrumentDetails": {"instrument": {"instrumentClassification": "SAAAAA", "bond": {}}}}
  - error_code: SE_DV_67
    record_mutation: {"transactionDetails": {"tradingDateTime": "2021-02-01T21:07:44+00:00"}, "instrumentDetails": {"instrument": {"bond": {"maturityDate": "2021-03-31"}}}}
  - error_code: SE_DV_68
    record_mutation: {"workflow": {"eligibility": {"totv": True}},"instrumentDetails": {"instrument": { "instrumentClassification": "OAEASD", "derivative": {"optionExerciseStyle": "EURO"}}}}
  - error_code: SE_DV_69
    record_mutation: {"workflow": {"eligibility": {"totv": True}},"instrumentDetails": {"instrument": { "instrumentClassification": "OAEASD", "derivative": {"optionExerciseStyle": "EURO"}}}}
  - error_code: SE_DV_70
    record_mutation: {"workflow": {"eligibility": {"totv": True}},"instrumentDetails": {"instrument": { "instrumentClassification": "FSDEFT", "derivative": {}}}}
  - error_code: SE_DV_71
    record_mutation: {"workflow": {"eligibility": {"totv": True}},"instrumentDetails": {"instrument": { "instrumentClassification": "HAABCT", "derivative": {"optionType": "CALL"}}}}
  - error_code: SE_DV_72
    record_mutation: {"workflow": {"eligibility": {"totv": True}},"instrumentDetails": {"instrument": { "instrumentClassification": "HAABCT", "derivative": {"optionType": "CALL"}}}}
  - error_code: SE_DV_73
    record_mutation: {"workflow": {"eligibility": {"totv": True}},"instrumentDetails": {"instrument": { "instrumentClassification": "FAABCT", "derivative": {}}}}
  - error_code: SE_DV_74
    record_mutation: {"workflow": {"eligibility": {"totv": True}},"instrumentDetails": {"instrument": {"derivative": {"priceMultiplier": "123456789"}}}}
  - error_code: SE_DV_75
    record_mutation: {"workflow": {"eligibility": {"totv": True}},"instrumentDetails": {"instrument": { "instrumentClassification": "RFABCT", "derivative": {"strikePrice": 23.7}}}}
  - error_code: SE_DV_76
    record_mutation: {"instrumentDetails": {"instrument": { "instrumentClassification": "FFABCT", "derivative": {}}}}
  - error_code: SE_DV_77
    record_mutation: {"workflow": {"eligibility": {"totv": True}},"instrumentDetails": {"instrument": { "ext": {"strikePriceType": "MntryVal"}, "derivative": {"strikePriceCurrency": "USD", "strikePrice": 23.7}}}}
  - error_code: SE_DV_78
    record_mutation: {"workflow": {"eligibility": {"totv": True}},"instrumentDetails": {"instrument": {"derivative": {"strikePriceCurrency": "USD"}}}}
  - error_code: SE_DV_79
    record_mutation: {"instrumentDetails": {"instrument": { "ext": {"strikePriceType": "MntryVal"}, "derivative": {"strikePrice": 23.7}}}}
  - error_code: SE_DV_80
    record_mutation: {"workflow": {"eligibility": {"totv": True}},"instrumentDetails": {"instrument": { "instrumentClassification": "FFNBCT", "instrumentIdCode": "54930084UKLVMY22DS16","derivative": {"underlyingIndexTerm": "123DAYS"}}}}
  - error_code: SE_DV_81
    record_mutation: {"instrumentDetails": {"instrument": { "instrumentClassification": "RFIBCT", "derivative": {"underlyingIndexName": "123DAYS"}}}}
  - error_code: SE_DV_81
    record_mutation: {"instrumentDetails": {"instrument": { "instrumentClassification": "FFNBCT", "derivative": {"underlyingIndexName": "123DAYS"}}}}
  - error_code: SE_DV_81
    record_mutation: { "instrumentDetails": { "instrument": { "instrumentClassification": "RFIBCT", "derivative": { "underlyingInstruments": [{ "underlyingInstrumentCode": "AUSHDIUAHS"}]}}}}
  - error_code: SE_DV_81
    record_mutation: { "instrumentDetails": { "instrument": { "instrumentClassification": "RFIBCT", "derivative": { "underlyingInstruments": [{}, { "underlyingInstrumentCode": "AUSHDIUAHS"}]}}}}
  - error_code: SE_DV_82
    record_mutation: {"workflow": {"eligibility": {"totv": True}}, "instrumentDetails": {"instrument": { "instrumentClassification": "DTNBCT"}}}
  - error_code: SE_DV_82
    record_mutation: {"workflow": {"eligibility": {"totv": True}}, "instrumentDetails": {"instrument": { "instrumentClassification": "ESABCT"}}}
  - error_code: SE_DV_83
    record_mutation: {"parties": {"buyer": [{"&key": "AccountFirm", "firmIdentifiers": {"lei": "54930084UKLVMY22DS16"}}]}, "marketIdentifiers": [{"labelId": "QWERT123:intc","path": "parties.buyer"}]}
  - error_code: SE_DV_84
    record_mutation: {"parties": {"buyer": [{"&key": "AccountFirm", "firmIdentifiers": {"lei": "54930084UKLVMY22DS16"}}]}, "marketIdentifiers": [{"labelId": "QWERT123:intc","path": "parties.buyer"}]}
  - error_code: SE_DV_85
    record_mutation: {"parties": {"buyer": [{"&key": "AccountPerson", "officialIdentifiers": {"mifirId": "54930084UKLVMY22DS16"}}]}}
  - error_code: SE_DV_86
    record_mutation: {"marketIdentifiers": [{"labelId": "test","path": "parties.buyer"}]}
  - error_code: SE_DV_89
    record_mutation: {"parties": {"buyer": [{"&key": "AccountPerson", "officialIdentifiers": {"branchCountry": "PT"}, "structure": {"type": "Client"}}]}}
  - error_code: SE_DV_90
    record_mutation: {"parties": {"buyer": [{"&key": "AccountFirm", "firmIdentifiers": {"branchCountry": "PT"}}]}}
  - error_code: SE_DV_91
    record_mutation: {"parties": {"seller": [{"&key": "AccountPerson", "officialIdentifiers": {"branchCountry": "PT"}, "structure": {"type": "Client"}}]}}
  - error_code: SE_DV_92
    record_mutation: {"parties": {"seller": [{"&key": "AccountFirm", "firmIdentifiers": {"branchCountry": "PT"}}]}}
  - error_code: SE_DV_93
    record_mutation: {"parties": {"investmentDecisionWithinFirm": {"&key": "MarketPerson", officialIdentifiers: {"branchCountry": "PT"}, "structure": {"executionWithinFirm": {"idType": "A"}}}}}
  - error_code: SE_DV_94
    record_mutation: {"parties": {"investmentDecisionWithinFirm": {"&key": "MarketPerson", officialIdentifiers: {"branchCountry": "PT"}}}}
  - error_code: SE_DV_95
    record_mutation: {"parties": {"executionWithinFirm": {"&key": "MarketPerson", officialIdentifiers: {"branchCountry": "PT"}, "structure": {"decisionMaker": {"idType": "A"}}}}}
  - error_code: SE_DV_96
    record_mutation: {"parties": {"executionWithinFirm": {"&key": "MarketPerson", officialIdentifiers: {"branchCountry": "PT"}}}}
  - error_code: SE_DV_97
    record_mutation: {"parties": {"executingEntity": {"&key": "MarketPerson", firmIdentifiers: {"lei": "54930084UKLVMY22DS16"}}}}
  - error_code: SE_DV_98
    record_mutation: {"parties": {"executingEntity": {"&key": "MarketPerson", firmIdentifiers: {"lei": "54930084UKLVMY22DS16"}}}}
  - error_code: SE_DV_99
    record_mutation: {"marketIdentifiers": [{"labelId": "AAAA321","path": "parties.executingEntity"}]}
  - error_code: SE_DV_100
    record_mutation: {"transactionDetails": {"tradingCapacity": "MTCH"}, "parties": {"executionWithinFirm": {"&key": "MarketPerson"}}}
  - error_code: SE_DV_101
    record_mutation: {"transactionDetails": {"tradingCapacity": "MTCH"}, "parties": {"executionWithinFirm": {"&key": "MarketPerson", "officialIdentifiers": {"mifirId": "54930084UKLVMY22DS16"}, "structure": {"decisionMaker": {"idType": "A"}}}}}
  - error_code: SE_DV_102
    record_mutation: {"transactionDetails": {"tradingCapacity": "MTCH"}, "parties": {"investmentDecisionWithinFirm": {"&key": "MarketPerson"}, "executionWithinFirm": {"&key": "MarketPerson", "structure": {"decisionMaker": {"idType": "A"}}}}}
  - error_code: SE_DV_103
    record_mutation: {"parties": {"investmentDecisionWithinFirm": {"&key": "MarketPerson", "officialIdentifiers": {"mifirId": "54930084UKLVMY22DS16"}, "structure": {"decisionMaker": {"idType": "A"}} }}}
  - error_code: SE_DV_104
    record_mutation: {"reportDetails": {"investmentFirmCoveredDirective": True}}
  - error_code: SE_DV_105
    record_mutation: {"parties": {"seller": [{"&key": "AccountFirm", "firmIdentifiers": {"lei": "54930084UKLVMY22DS16"}}]}, "marketIdentifiers": [{"labelId": "QWERT123:intc","path": "parties.seller"}]}
  - error_code: SE_DV_106
    record_mutation: {"parties": {"seller": [{"&key": "AccountFirm", "firmIdentifiers": {"lei": "54930084UKLVMY22DS16"}}]}, "marketIdentifiers": [{"labelId": "QWERT123:intc","path": "parties.seller"}]}
  - error_code: SE_DV_107
    record_mutation: {"parties": {"seller": [{"&key": "AccountPerson", "officialIdentifiers": {"mifirId": "54930084UKLVMY22DS16"}}]}}
  - error_code: SE_DV_108
    record_mutation: {"marketIdentifiers": [{"labelId": "test","path": "parties.seller"}]}
  - error_code: SE_DV_111
    record_mutation: {"transactionDetails": {"tradingCapacity": "AOTC"}, "parties": {"buyerTransmittingFirm": {"&key": "AccountFirm", "firmIdentifiers": {"lei": "54930084UKLVMY22DS16"}}}}
  - error_code: SE_DV_112
    record_mutation: {"parties": {"buyerTransmittingFirm": {"&key": "AccountFirm", "firmIdentifiers": {"lei": "54930084UKLVMY22DS16"}}}}
  - error_code: SE_DV_113
    record_mutation: {"transactionDetails": {"tradingCapacity": "AOTC"}, "parties": {"sellerTransmittingFirm": {"&key": "AccountFirm", "firmIdentifiers": {"lei": "54930084UKLVMY22DS16"}}}}
  - error_code: SE_DV_114
    record_mutation: {"parties": {"sellerTransmittingFirm": {"&key": "AccountFirm", "firmIdentifiers": {"lei": "54930084UKLVMY22DS16"}}}}
  - error_code: SE_DV_115
    record_mutation: {"transactionDetails" : {"pricePending": False, "price": 23.4}}
  - error_code: SE_DV_117
    record_mutation: {"transactionDetails" : {"pricePending": True}}
  - error_code: SE_DV_123
    record_mutation: {"transactionDetails" : {"price": 1423563.4}}
  - error_code: SE_DV_124
    record_mutation: {"instrumentDetails" : {"instrument": {"derivative": {"strikePrice": 1423563.4}}}}
  - error_code: SE_DV_132
    record_mutation: {"transactionDetails" : {"quantity": 1423563.4}}
  - error_code: SE_DV_133
    record_mutation: {"transactionDetails" : {"quantity": 1423563.4}}
  - error_code: SE_DV_144
    record_mutation: {"transactionDetails": {"tradingCapacity": "DEAL"}, "parties": {"executingEntity": {"&key": "AccountFirm", "firmIdentifiers": {"lei": "54930084UKLVMY22DS16"}}, "buyer": [{"&key": "AccountFirm", "firmIdentifiers": {"lei": "54930084UKLVMY22DS16"}}], "seller": [{"&key": "AccountFirm", "firmIdentifiers": {"lei": "54930084UKLVMY22DS16"}}]}}
  - error_code: SE_DV_146
    record_mutation: {"parties": {"executingEntity": {"&key": "MarketPerson", details: {"leiRegistrationStatus": "ISSUED"}}}}
  - error_code: SE_DV_149
    record_mutation: {"workflow": {"eligibility": {"totv": True}},"instrumentDetails": {"instrument": {"instrumentClassification":"FFNPCO", "derivative": {"underlyingIndexTermValue": "AAA"}}}}
  - error_code: SE_DV_150
    record_mutation: {"transactionDetails": {"tradingCapacity": "MTCH"}}
  - error_code: SE_DV_151
    record_mutation: {"transactionDetails": {"upFrontPayment": 324.5}, "instrumentDetails": {"instrument": {"instrumentClassification":"SCNPCO"}}}
  - error_code: SE_DV_152
    record_mutation: {"workflow": {"eligibility": {"totv": FALSE}},"instrumentDetails": {"instrument": {"derivative": {"underlyingIndexName": "ASHOUASHDIUS"}}}}
  - error_code: SE_DV_153
    record_mutation: {"transactionDetails": {"tradingCapacity": "MTCH"}}
  - error_code: SE_DV_154
    record_mutation: {"transactionDetails": {"tradingCapacity": "AOTC"},"parties":{"executingEntity":{"&key":"AccountFirm", "firmIdentifiers":{"lei":"12340084UKLVMY22DS16"}},"buyerDecisionMaker":[{"&key":"AccountFirm","firmIdentifiers":{"lei":"12340084UKLVMY22DS16"}}], "sellerDecisionMaker":[{"&key":"AccountFirm","firmIdentifiers":{"lei":"12340084UKLVMY22DS16"}}], "investmentDecisionWithinFirm":{"&key":"MarketPerson"}}}
  - error_code: SE_DV_155
    record_mutation: {"transactionDetails": {"tradingCapacity": "AOTC"},"parties":{"executingEntity":{"&key":"AccountFirm", "firmIdentifiers":{"lei":"12340084UKLVMY22DS16"}},"buyerDecisionMaker":[{"&key":"AccountFirm","firmIdentifiers":{"lei":"12340084UKLVMY22DS16"}}], "sellerDecisionMaker":[{"&key":"AccountFirm","firmIdentifiers":{"lei":"12340084UKLVMY22DS16"}}], "investmentDecisionWithinFirm":{"&key":"MarketPerson", "officialIdentifiers":{"mifirId":"JDOAISJDO"}}}}
  - error_code: SE_DV_157
    record_mutation: {"transactionDetails": {"tradingCapacity": "AOTC"},"parties":{"executingEntity":{"&key":"AccountFirm", "firmIdentifiers":{"lei":"12340084UKLVMY22DS16"}},"buyerDecisionMaker":[{"&key":"AccountFirm","firmIdentifiers":{"lei":"12340084UKLVMY22DS16"}}], "sellerDecisionMaker":[{"&key":"AccountFirm","firmIdentifiers":{"lei":"12340084UKLVMY22DS16"}}], "investmentDecisionWithinFirm":{"&key":"MarketPerson", "officialIdentifiers":{"mifirId":"JDOAISJDO"}}}, "marketIdentifiers":[{"labelId":"clnt:nore", "path":"parties.investmentDecisionWithinFirm"}]}
  - error_code: SE_DV_160
    record_mutation: {"transactionDetails": {"tradingCapacity": "AOTC"},"parties":{"executionWithinFirm":{"&key":"MarketPerson"}}, "marketIdentifiers":[{"labelId":"AIUSDIAUSGDI", "path":"parties.executionWithinFirm"}]}
  - error_code: SE_DV_161
    record_mutation: {"marketIdentifiers":[{"labelId":"AIUSDIAUSGDI", "path":"parties.executionWithinFirm"}]}
  - error_code: SE_DV_161
    record_mutation: {"marketIdentifiers":[{"labelId":"AIUSDIAUSGDI", "path":"parties.buyer"}, {"labelId":"AIUSDIAUSGDI", "path":"parties.seller"}, {"labelId":"AIUSDIAUSGDI", "path":"parties.executionWithinFirm"}]}
  - error_code: SE_DV_162
    record_mutation: {"transactionDetails": {"tradingCapacity": "MTCH"}, "marketIdentifiers":[{"labelId":"AIUSDIAUSGDI", "path":"parties.executionWithinFirm"}]}
  - error_code: SE_DV_163
    record_mutation: {"transactionDetails": {"tradingCapacity": "DEAL"}, "marketIdentifiers":[{"labelId":"AIUSDIAUSGDI", "path":"parties.investmentDecisionWithinFirm"}]}
  - error_code: SE_DV_164
    record_mutation: {"transactionDetails": {"tradingCapacity": "DEAL"}, "parties": {"executionWithinFirm":{"structure":{"executionWithinFirm":{"idType":"L"}},"&key":"MarketPerson"}, "investmentDecisionWithinFirm":{"&key":"MarketPerson", "officialIdentifiers":{"mifirId":"JDOAISJDO"}}}}
  - error_code: SE_DV_165
    record_mutation: {"transactionDetails": {"tradingCapacity": "AOTC"},"parties":{"executingEntity":{"&key":"AccountFirm", "firmIdentifiers":{"lei":"12340084UKLVMY22DS16"}},"buyerDecisionMaker":[{"&key":"AccountFirm","firmIdentifiers":{"lei":"12340084UKLVMY22DS16"}}], "sellerDecisionMaker":[{"&key":"AccountFirm","firmIdentifiers":{"lei":"12340084UKLVMY22DS16"}}], "investmentDecisionWithinFirm":{"&key":"MarketPerson", "officialIdentifiers":{"mifirId":"JDOAISJDO"}}}, "marketIdentifiers":[{"labelId":"clnt:nore", "path":"parties.investmentDecisionWithinFirm"}]}
  - error_code: SE_DV_166
    record_mutation: {"transactionDetails": {"tradingCapacity": "DEAL"}, "marketIdentifiers":[{"labelId":"AIUSDIAUSGDI", "path":"parties.buyer"}]}
  - error_code: SE_DV_166
    record_mutation: {"transactionDetails": {"tradingCapacity": "MTCH"}, "marketIdentifiers":[{"labelId":"AIUSDIAUS:intc", "path":"parties.buyer"}]}
  - error_code: SE_DV_167
    record_mutation: {"transactionDetails": {"tradingCapacity": "DEAL"}, "marketIdentifiers":[{"labelId":"AIUSDIAUSGDI", "path":"parties.seller"}]}
  - error_code: SE_DV_167
    record_mutation: {"transactionDetails": {"tradingCapacity": "MTCH"}, "marketIdentifiers":[{"labelId":"AIUSDIAUS:intc", "path":"parties.seller"}]}
  - error_code: SE_DV_168
    record_mutation: {"instrumentDetails": {"instrument": {"notionalCurrency1": "EUR"}}}
  - error_code: SE_DV_169
    record_mutation: {"instrumentDetails": {"instrument": {"notionalCurrency1": "EUR"}}}
  - error_code: SE_DV_170
    record_mutation: {"tradersAlgosWaiversIndicators": {"commodityDerivativeIndicator": TRUE, "shortSellingIndicator": "SELL"}}
  - error_code: SE_DV_171
    record_mutation: {"instrumentDetails": {"instrument": {"ext": {"mifirEligible": TRUE, "underlyingInstruments":[{}]}, "derivative":{"underlyingInstruments":[{"underlyingInstrumentCode":"AUSHDIUAHS"}]}}}}
  - error_code: SE_DV_172
    record_mutation: {"transactionDetails": {"branchMembershipCountry": "PT"}}
  - error_code: SE_DV_174
    record_mutation: {"transactionDetails": {"recordType": "Market Side"}, "marketIdentifiers":[{"labelId":"AIUSDIAUSGDI", "path":"parties.seller"}]}
  - error_code: SE_DV_175
    record_mutation: {"transactionDetails": {"recordType": "Market Side"}, "marketIdentifiers":[{"labelId":"AIUSDIAUintc", "path":"parties.seller"}]}
  - error_code: SE_DV_176
    record_mutation: {"transactionDetails": {"recordType": "Market Side"}, "marketIdentifiers":[{"labelId":"AIUSDIAUintc", "path":"parties.buyer"}]}
  - error_code: SE_DV_177
    record_mutation: {"parties": {"buyer": [{"&key": "MarketCounterparty", "firmIdentifiers": {"branchCountry": "PT"}}], "executingEntity": {"firmIdentifiers": {"branchCountry": "PT"}}}}
  - error_code: SE_DV_178
    record_mutation: {"parties": {"seller": [{"&key": "MarketCounterparty", "firmIdentifiers": {"branchCountry": "PT"}}], "executingEntity": {"firmIdentifiers": {"branchCountry": "PT"}}}}
  - error_code: SE_DV_179
    record_mutation: {"transactionDetails":{"recordType":"Market Side"}}
  - error_code: SE_DV_180
    record_mutation: {"transactionDetails":{"isUseOfINTC": TRUE, "recordType":"Market Side"}}
  - error_code: SE_DV_181
    record_mutation: {"marketIdentifiers":[{"labelId":"AIUSDIAU:intc", "path":"parties.buyer"}]}
  - error_code: SE_DV_182
    record_mutation: {"marketIdentifiers":[{"labelId":"AIUSDIAU:intc", "path":"parties.seller"}]}
  - error_code: SE_DV_183
    record_mutation: {"workflow":{"eligibility":{"onFirds":TRUE}}, "transactionDetails":{"venue": "XLON"}}
  - error_code: SE_DV_184
    record_mutation: {"parties": {"buyer": [{"&key": "MarketCounterparty", "details":{"firmType":"Counterparty"}}]}}
  - error_code: SE_DV_185
    record_mutation: {"parties": {"seller": [{"&key": "MarketCounterparty", "details":{"firmType":"Counterparty"}}]}}
  - error_code: SE_DV_186
    record_mutation: {"parties":{"executingEntity":{"&key":"AccountFirm", "firmIdentifiers":{"branchCountry":"PT"}}}}
  - error_code: SE_DV_187
    record_mutation: {"parties":{"executingEntity":{"&key":"AccountFirm", "firmIdentifiers":{"branchCountry":"PT"}}}}
  - error_code: SE_DV_188
    record_mutation: {"parties": {"buyer": [{"&key": "MarketCounterparty"}]}}
  - error_code: SE_DV_189
    record_mutation: {"parties": {"seller": [{"&key": "MarketCounterparty"}]}}
  - error_code: SE_DV_190
    record_mutation: {"parties":{"executingEntity":{"&key":"AccountFirm"}}}
  - error_code: SE_DV_191
    record_mutation: {"parties":{"buyerDecisionMaker":[{"&key":"AccountFirm"}]},"marketIdentifiers":[{"labelId":"AIUSDIAU:intc", "path":"parties.buyerDecisionMaker"}] }
  - error_code: SE_DV_192
    record_mutation: {"parties":{"buyerDecisionMaker":[{"&key":"MarketPerson","officialIdentifiers":{"mifirId":"12340084UKLVMY22DS16"}}]}}
  - error_code: SE_DV_193
    record_mutation: {"parties":{"buyerDecisionMaker":[{"&key":"MarketCounterparty","firmIdentifiers":{"lei":"12340084UKLVMY22DS16"}}]}, "marketIdentifiers":[{"labelId":"AIUSDIAUASDJOIASJ", "path":"parties.buyerDecisionMaker"}]}
  - error_code: SE_DV_194
    record_mutation: {"parties":{"buyerDecisionMaker":[{"&key":"MarketCounterparty","details":{"leiRegistrationStatus":"ISSUED"}}]}}
  - error_code: SE_DV_197
    record_mutation: {"parties":{"sellerDecisionMaker":[{"&key":"MarketCounterparty"}]}, "marketIdentifiers":[{"labelId":"AIUSDIAU:intc", "path":"parties.sellerDecisionMaker"}]}
  - error_code: SE_DV_198
    record_mutation: {"parties":{"sellerDecisionMaker":[{"&key":"MarketPerson","officialIdentifiers":{"mifirId":"12340084UKLVMY22DS16"}}]}}
  - error_code: SE_DV_199
    record_mutation: {"parties":{"sellerDecisionMaker":[{"&key":"MarketCounterparty","firmIdentifiers":{"lei":"12340084UKLVMY22DS16"}}]}, "marketIdentifiers":[{"labelId":"AIUSDIAUASDJOIASJ", "path":"parties.sellerDecisionMaker"}]}
  - error_code: SE_DV_200
    record_mutation: {"parties":{"sellerDecisionMaker":[{"&key":"MarketCounterparty","details":{"leiRegistrationStatus":"ISSUED"}}]}}
  - error_code: SE_DV_203
    record_mutation: {"parties": {"buyer": [{"&key": "MarketCounterparty","firmIdentifiers":{"lei":"12340084UKLVMY22D216"}}],"seller":[{"&key": "MarketCounterparty","firmIdentifiers":{"lei":"12340084UKLVMY22DS16"}}]}}
  - error_code: SE_DV_204
    record_mutation: {"instrumentDetails": {"instrument":{"ext":{"strikePriceType": "MntryVal"}, "derivative":{"strikePrice":1000}}}}
  - error_code: SE_DV_205
    record_mutation: {"instrumentDetails": {"instrument":{"instrumentFullName":"FOOBAR"}}}
  - error_code: SE_DV_206
    record_mutation: {"instrumentDetails": {"instrument": {"instrumentClassification": "CSOWKD"}}}
  - error_code: SE_DV_290
    record_mutation: {"transactionDetails": {"venue": "XLON"}}
  - error_code: SE_DV_291
    record_mutation: {"transactionDetails": {"tradingDateTime": "2020-01-03T00:00:00Z"}}
  - error_code: SE_DV_292
    record_mutation: {"transactionDetails": {"tradingCapacity": "AOTC"},"parties":{"buyerDecisionMaker":[{"&key":"AccountFirm","firmIdentifiers":{"lei":"12340084UKLVMY22DS16"}}], "buyerTransmittingFirm":{"&key":"AccountFirm","firmIdentifiers":{"lei":"12340084UKLVMY23DS16"}}, "sellerDecisionMaker":[{"&key":"AccountFirm","firmIdentifiers":{"lei":"12340084UKLVMY22DS16"}}], "sellerTransmittingFirm":{"&key":"AccountFirm","firmIdentifiers":{"lei":"22340084UKLVMY22DS16"}}, "investmentDecisionWithinFirm":{"&key":"MarketPerson", "officialIdentifiers":{"mifirId":"JDOAISJDO"}}}}
  - error_code: SE_DV_293
    record_mutation: {"instrumentDetails": {"instrument": {"commoditiesOrEmissionAllowanceDerivativeInd": FALSE}}}
  - error_code: SE_DV_294
    record_mutation: {"instrumentDetails": {"instrument": {"commoditiesOrEmissionAllowanceDerivativeInd": FALSE}}, "tradersAlgosWaiversIndicators": {"commodityDerivativeIndicator": FALSE}}
  - error_code: SE_DV_295
    record_mutation: {"instrumentDetails": {"instrument": {"instrumentClassification": "OAASXE", "derivative":{"optionExerciseStyle":"AMER"}}}, "workflow":{"eligibility":{"totv": FALSE}}}
  - error_code: SE_DV_296
    record_mutation: {"instrumentDetails": {"instrument": {"instrumentClassification": "OAESXE", "derivative":{"optionExerciseStyle":"EURO"}}}, "workflow":{"eligibility":{"totv": FALSE}}}
  - error_code: SE_DV_297
    record_mutation: {"instrumentDetails": {"instrument": {"instrumentClassification": "OABSXE", "derivative":{"optionExerciseStyle":"BERM"}}}, "workflow":{"eligibility":{"totv": FALSE}}}
  - error_code: SE_DV_300
    record_mutation: {"instrumentDetails": {"instrument": {"instrumentClassification": "OAASXE"}}}
  - error_code: SE_DV_301
    record_mutation: {"transactionDetails": {"venue":  "XXXX"}, "workflow":{"eligibility":{"totv": FALSE, "onFirds":FALSE}}}
  - error_code: SE_DV_304
    record_mutation: {"transactionDetails": {"venue":  "XXXX", "recordType": "Client Side" }}
  - error_code: SE_DV_305
    record_mutation: {"transactionDetails": {"branchMembershipCountry": "PT", "recordType": "Market Side" }}
  - error_code: SE_DV_306
    record_mutation: {"transactionDetails": {"venue": "ABFI" }}
  - error_code: SE_DV_306
    record_mutation: {"transactionDetails": {"venue": "XOFF" }}
  - error_code: SE_DV_310
    record_mutation: {"reportDetails": {"tradingVenueTransactionIdCode": "ABFIAOISHDOIASHDAOISH" }}
  - error_code: SE_DV_311
    record_mutation: {"parties": {"investmentDecisionWithinFirm":{"structure":{"decisionMaker":{"idType":"A", "id":"AOSIHDOASHOHD"}},"&key":"MarketPerson"}}}
  - error_code: SE_DV_312
    record_mutation : {"parties": {"executionWithinFirm":{"structure":{"decisionMaker":{"idType":"A", "id":"AOSIHDOASHOHD"}},"&key":"MarketPerson"}}}
  - error_code: SE_DV_313
    record_mutation: {"parties":{"buyer": [{"&key": "AccountFirm", "firmIdentifiers": {"lei": "intc"}}]},"marketIdentifiers":[{"labelId":"AIUSDIAU:intc", "path":"parties.buyer"}] }
  - error_code: SE_DV_314
    record_mutation: {"parties":{"seller": [{"&key": "AccountFirm", "firmIdentifiers": {"lei": "intc"}}]},"marketIdentifiers":[{"labelId":"AIUSDIAU:intc", "path":"parties.seller"}] }
  - error_code: SE_DV_318
    record_mutation: {"reportDetails":{"reportStatus": "NEWT"}}
  - error_code: SE_DV_319
    record_mutation: {"reportDetails":{"reportStatus": "NEWT"}}
  - error_code: SE_DV_319
    record_mutation: {"reportDetails":{"reportStatus": "NEWT"}}
  - error_code: SE_DV_320
    record_mutation: {"date": "2019-04-14"}
  - error_code: SE_DV_320
    record_mutation: {"reportDetails": {"reportStatus": "CANC"}}
  - error_code: SE_DV_321
    record_mutation: {"date": "2020-04-2"}
  - error_code: SE_DV_321
    record_mutation: {"reportDetails": {"reportStatus": "CANC"}}
  - error_code: SE_DV_322
    record_mutation: {"sourceIndex": "1"}
  - error_code: SE_DV_323
    record_mutation: {"parties":{"seller": [{"&key": "AccountFirm", "details": {"firmType": "Client"}, "firmIdentifiers": {"branchCountry": "PT"}}]}}
  - error_code: SE_DV_324
    record_mutation: {"parties":{"buyer": [{"&key": "AccountPerson", "structure": {"type": "Client"}, "officialIdentifiers": {"branchCountry": "PT"}}]}}
  - error_code: SE_DV_325
    record_mutation: {"parties":{"buyer": [{"&key": "AccountFirm", "details": {"firmType": "Client"}, "firmIdentifiers": {"branchCountry": "PT"}}]}}
#  - error_code: SE_DV_326 # currently disabled
#    record_mutation: {"transactionDetails": {"venue": "BERB"}}
  - error_code: SE_DV_327
    record_mutation: {"transactionDetails": {"venue": "BATE"},"reportDetails": {"tradingVenueTransactionIdCode" : "123456"}}
  - error_code: SE_DV_328
    record_mutation: {"transactionDetails": {"quantityCurrency": "EUR"}}
  - error_code: SE_DV_329
    record_mutation : {"parties": {"seller":[{"&key":"MarketPerson", "officialIdentifiers":{"mifirId":"********************", "mifirIdSubType": "CONCAT"}, "personalDetails": {"dob": "1994-07-11"}}]}}
  - error_code: SE_DV_329
    record_mutation: {}
  - error_code: SE_DV_329
    record_mutation: {"parties": {"seller": [{"&key": "AccountFirm", "firmIdentifiers": {"lei": "12340084UKLVMY22DS16"}}, {"&key": "MarketCounterparty", "firmIdentifiers": {"lei": "43210084UKLVMY22DS16"}}]}}
  - error_code: SE_DV_330
    record_mutation : {"parties": {"buyer":[{"&key":"MarketPerson", "officialIdentifiers":{"mifirId":"********************", "mifirIdSubType": "CONCAT"}, "personalDetails": {"dob": "1994-07-11"}}]}}
  - error_code: SE_DV_330
    record_mutation: {}
  - error_code: SE_DV_330
    record_mutation: {"parties": {"buyer": [{"&key": "AccountFirm", "firmIdentifiers": {"lei": "12340084UKLVMY22DS16"}}, {"&key": "MarketCounterparty", "firmIdentifiers": {"lei": "43210084UKLVMY22DS16"}}]}}
  - error_code: SE_DV_331
    record_mutation : {"parties": {"investmentDecisionWithinFirm":{"&key":"MarketPerson", "officialIdentifiers":{"mifirId":"********************", "mifirIdSubType": "CONCAT"}, "personalDetails": {"dob": "1994-07-11"}}}}
  - error_code: SE_DV_331
    record_mutation: {}
  - error_code: SE_DV_332
    record_mutation : {"parties": {"executionWithinFirm":{"&key":"MarketPerson", "officialIdentifiers":{"mifirId":"********************", "mifirIdSubType": "CONCAT"}, "personalDetails": {"dob": "1994-07-11"}}}}
  - error_code: SE_DV_332
    record_mutation: {}
  - error_code: SE_DV_333
    record_mutation: {"transactionDetails": {"venue": "XLON","branchMembershipCountry": "PT"}}
  - error_code: SE_DV_334
    record_mutation: {"transactionDetails": {"ultimateVenue": "XLON"}}
  - error_code: SE_DV_335
    record_mutation: {"instrumentDetails": {"instrument": {"instrumentClassification": "DFNAAA","issuerOrOperatorOfTradingVenueId":"ECTRVYYCEF89VWYS6K31"}}}
  - error_code: SE_DV_336
    record_mutation : {"parties": {"executionWithinFirm":{"structure":{"decisionMaker":{"idType":"M", "id":"AOSIHDOASHOHD"}},"&key":"MarketPerson", "officialIdentifiers":{"mifirId":"AISHDOAISHD"}}}}
  - error_code: SE_DV_337
    record_mutation: {"sourceKey": "s3://abc"}
  - error_code: SE_DV_344
    record_mutation: {"&timestamp": "*************"}
  - error_code: SE_DV_345
    record_mutation: {"&timestamp": "*************"}
  - error_code: SE_DV_346
    record_mutation: {"&timestamp": "*************"}
  - error_code: SE_DV_347
    record_mutation: {"&user": "notme"}
  - error_code: SE_DV_351
    record_mutation: {"parties": {"buyer": [{"&key": "AccountFirm", "firmIdentifiers": {"lei": "12340084UKLVMY22DS16"}}, {"&key": "MarketCounterparty", "firmIdentifiers": {"lei": "43210084UKLVMY22DS16"}}]}}
  - error_code: SE_DV_351
    record_mutation: {"parties": {"buyer": [{"&key": "AccountPerson", "officialIdentifiers": {"mifirId": "abc"}}, {"&key": "MarketPerson", "officialIdentifiers": {"mifirId": "xyz"}}]}}
  - error_code: SE_DV_351
    record_mutation: {"parties": {"buyer": [{"&key": "AccountPerson", "officialIdentifiers": {"mifirId": "abc"}}]}}
  - error_code: SE_DV_351
    record_mutation: {"parties": {"buyer": [{"&key": "AccountFirm", "firmIdentifiers": {"lei": "12340084UKLVMY22DS16"}}, {"&key": "MarketCounterparty", "firmIdentifiers": {"lei": "43210084UKLVMY22DS16"}}, {"&key": "AccountPerson", "officialIdentifiers": {"mifirId": "abc"}}, {"&key": "MarketPerson", "officialIdentifiers": {"mifirId": "xyz"}}]}}
  - error_code: SE_DV_351
    record_mutation: {"parties": {"buyer": [{"&key": "MarketCounterparty", "firmIdentifiers": {"lei": "43210084UKLVMY22DS16"}}]}}
  - error_code: SE_DV_351
    record_mutation: {}
  - error_code: SE_DV_352
    record_mutation: {"parties": {"seller": [{"&key": "AccountFirm", "firmIdentifiers": {"lei": "12340084UKLVMY22DS16"}}, {"&key": "MarketCounterparty", "firmIdentifiers": {"lei": "43210084UKLVMY22DS16"}}]}}
  - error_code: SE_DV_352
    record_mutation: {"parties": {"seller": [{"&key": "AccountPerson", "officialIdentifiers": {"mifirId": "abc"}}, {"&key": "MarketPerson", "officialIdentifiers": {"mifirId": "xyz"}}]}}
  - error_code: SE_DV_352
    record_mutation: {"parties": {"seller": [{"&key": "AccountPerson", "officialIdentifiers": {"mifirId": "abc"}}]}}
  - error_code: SE_DV_352
    record_mutation: {"parties": {"seller": [{"&key": "AccountFirm", "firmIdentifiers": {"lei": "12340084UKLVMY22DS16"}}, {"&key": "MarketCounterparty", "firmIdentifiers": {"lei": "43210084UKLVMY22DS16"}}, {"&key": "AccountPerson", "officialIdentifiers": {"mifirId": "abc"}}, {"&key": "MarketPerson", "officialIdentifiers": {"mifirId": "xyz"}}]}}
  - error_code: SE_DV_352
    record_mutation: {"parties": {"seller": [{"&key": "MarketCounterparty", "firmIdentifiers": {"lei": "43210084UKLVMY22DS16"}}]}}
  - error_code: SE_DV_352
    record_mutation: {}

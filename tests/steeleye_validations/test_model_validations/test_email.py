from pathlib import Path
from typing import Set

import pytest

from se_elastic_schema.elastic_schema.core.steeleye_record_validation import (
    SteelEyeRecordValidationError,
    SteelEyeRecordValidationRule,
)
from se_elastic_schema.elastic_schema.utils.schema_types import get_schema_field_types
from se_elastic_schema.models.tenant.communication.email import Email


@pytest.fixture
def email_record() -> dict:
    """
    Returns a dict corresponding to a valid Email record with only the schema required fields
    populated.
    :return: Dict of Email record
    """
    return {}


@pytest.fixture
def email_model_fields() -> Set[str]:
    """
    Returns the flatten field names of Email model as a set

    :return:
    """
    model_fields = set(get_schema_field_types(Email).keys())

    return model_fields


EMAIL_RAISED_ERROR_CASES = pytest.helpers.read_test_cases(
    path=Path(__file__).parent.joinpath("data", "Email", "raised_error_cases.yaml")
)

EMAIL_VALID_CASES = pytest.helpers.read_test_cases(
    path=Path(__file__).parent.joinpath("data", "Email", "valid_cases.yaml")
)

EMAIL_ALL_RECORD_VALIDATION_RULES = Email.steeleye_validation_rules()


class TestEmailSteeleyeRecordValidations:
    @pytest.mark.parametrize("error_code, record_mutation", EMAIL_RAISED_ERROR_CASES)
    def test_steeleye_validation_error_raised(
        self, email_record, error_code, record_mutation, email_model_fields
    ):
        pytest.helpers.check_steeleye_validation_raised(
            error_code=error_code,
            core_record=email_record,
            record_mutation=record_mutation,
            model_class=Email,
            model_fields=email_model_fields,
        )

    @pytest.mark.parametrize("error_code, record_mutation", EMAIL_VALID_CASES)
    def test_steeleye_validation_valid_cases(
        self,
        email_record,
        error_code,
        record_mutation,
    ):
        pytest.helpers.check_steeleye_validation_passed(
            error_code=error_code,
            core_record=email_record,
            record_mutation=record_mutation,
            model_class=Email,
        )

    @pytest.mark.parametrize("validation_rule", EMAIL_ALL_RECORD_VALIDATION_RULES)
    def test_record_with_no_modifications_passes(
        self, email_record, validation_rule: SteelEyeRecordValidationRule
    ):
        parsed_record = Email.parse_obj(email_record)
        try:
            validation_rule.run_record_validation(record=parsed_record)
        except SteelEyeRecordValidationError:  # this is expected
            pass

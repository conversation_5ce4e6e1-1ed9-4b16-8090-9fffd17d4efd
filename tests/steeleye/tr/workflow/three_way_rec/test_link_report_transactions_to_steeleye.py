from unittest.mock import patch

import addict
import pandas as pd
import pytest

from swarm_tasks.steeleye.tr.static import ARMReport
from swarm_tasks.steeleye.tr.static import NCAReport
from swarm_tasks.steeleye.tr.static import RecFields
from swarm_tasks.steeleye.tr.static import StaticValues
from swarm_tasks.steeleye.tr.workflow.three_way_rec import (
    link_report_transactions_to_steeleye,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.link_report_transactions_to_steeleye import (
    LinkReportTransactionsToSteelEye,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.link_report_transactions_to_steeleye import (
    Params,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.link_report_transactions_to_steeleye import (
    ReportDetailsIdFields,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.report_mechanism_mappings import (
    REPORT_MECHANISM_MAP,
)


@pytest.fixture()
def preprocess_data_new_and_canc_tx_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            NCAReport.TXID: ["ID1", "ID2", pd.NA],
            NCAReport.CANC_TXID: [pd.NA, pd.NA, "CANC_ID3"],
        }
    )
    return df


@pytest.fixture()
def preprocess_data_all_null_df() -> pd.DataFrame:
    df = pd.DataFrame({NCAReport.TXID: [pd.NA] * 3, NCAReport.CANC_TXID: [pd.NA] * 3})
    return df


@pytest.fixture()
def nca_merge_steeleye_transactions_with_report_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            ReportDetailsIdFields.TEMP_REPORT_STATUS: ["NEWT", "NEWT", "CANC", "NEWT"],
            ReportDetailsIdFields.TEMP_ID: ["ID1", "ID2", "CANC_ID3", "ID4"],
            "FIELD3": ["val1", "val2", "val3", "val4"],
        }
    )
    return df


@pytest.fixture()
def arm_merge_steeleye_transactions_with_report_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            ARMReport.REPORT_STATUS: ["NEWT", "NEWT", "CANC", "NEWT"],
            ARMReport.TRANSACTION_REF_NO: ["ID1", "ID2", "CANC_ID3", "ID4"],
            "FIELD3": ["val1", "val2", "val3", "val4"],
        }
    )
    return df


@pytest.fixture()
def arm_rejection_cleared_status_trades_with_rejected_trades_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            ARMReport.REPORT_STATUS: ["NEWT", "NEWT", "CANC", "NEWT"],
            ARMReport.TRANSACTION_REF_NO: ["ID1", "ID2", "CANC_ID3", "ID4"],
            RecFields.WORKFLOW_ARM_STATUS: [
                StaticValues.REJECTED,
                StaticValues.REJECTION_CLEARED,
                StaticValues.REJECTION_CLEARED,
                StaticValues.REJECTED,
            ],
        }
    )
    return df


@pytest.fixture()
def nca_rejection_cleared_status_trades_with_rejected_trades_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            ARMReport.REPORT_STATUS: ["NEWT", "NEWT", "CANC", "NEWT"],
            ARMReport.TRANSACTION_REF_NO: ["ID1", "ID2", "CANC_ID3", "ID4"],
            ReportDetailsIdFields.TEMP_REPORT_STATUS: ["NEWT", "NEWT", "CANC", "NEWT"],
            ReportDetailsIdFields.TEMP_ID: ["ID1", "ID2", "CANC_ID3", "ID4"],
            RecFields.WORKFLOW_NCA_STATUS: [
                StaticValues.REJECTED,
                StaticValues.REJECTION_CLEARED,
                StaticValues.REJECTION_CLEARED,
                StaticValues.REJECTED,
            ],
        }
    )
    return df


def fetch_transactions_mock(*args, **kwargs) -> pd.DataFrame:

    if (
        kwargs["query"]["query"]["bool"]["filter"][1]["term"][
            ReportDetailsIdFields.REPORT_DETAILS_REPORT_STATUS
        ]
        == "NEWT"
    ):
        return pd.DataFrame(
            {
                "&id": ["meta_id2"],
                ReportDetailsIdFields.TRANSACTION_REF_NO: "ID2",
                ReportDetailsIdFields.REPORT_DETAILS_REPORT_STATUS: "NEWT",
            }
        )

    elif (
        kwargs["query"]["query"]["bool"]["filter"][1]["term"][
            ReportDetailsIdFields.REPORT_DETAILS_REPORT_STATUS
        ]
        == "CANC"
    ):
        return pd.DataFrame(
            {
                "&id": ["meta_id3"],
                ReportDetailsIdFields.TRANSACTION_REF_NO: "CANC_ID3",
                ReportDetailsIdFields.REPORT_DETAILS_REPORT_STATUS: "CANC",
            }
        )


def fetch_transactions_empty_result_mock(*args, **kwargs) -> pd.DataFrame:

    return pd.DataFrame()


class TestLinkReportTransactionsToSteelEye:
    """
    Test cases for "LinkReportTransactionsToSteeleye" class
    """

    nca_params = Params(report_type="nca_fca")
    arm_params = Params(report_type="arm_unavista")
    es_client = addict.Dict(
        {
            "MAX_TERMS_SIZE": 1024,
            "meta": {"key": "&key", "model": "RTS22_Transaction", "id": "&id"},
        }
    )

    def test_preprocess_data_new_and_canc_tx_df(
        self, preprocess_data_new_and_canc_tx_df: pd.DataFrame
    ):
        task = LinkReportTransactionsToSteelEye(
            name="link-report-transactions-to-steeleye", params=self.nca_params
        )

        task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            self.nca_params.report_type
        ]()

        expected_result = pd.DataFrame(
            {
                NCAReport.TXID: ["ID1", "ID2", pd.NA],
                NCAReport.CANC_TXID: [pd.NA, pd.NA, "CANC_ID3"],
                ReportDetailsIdFields.TEMP_REPORT_STATUS: ["NEWT", "NEWT", "CANC"],
                ReportDetailsIdFields.TEMP_ID: ["ID1", "ID2", "CANC_ID3"],
            }
        )
        result = task.report_mechanism_reconciliation.link_preprocess_data(
            preprocess_data_new_and_canc_tx_df
        )
        assert result.equals(expected_result)

    def test_preprocess_data_all_null_df(
        self, preprocess_data_all_null_df: pd.DataFrame
    ):
        task = LinkReportTransactionsToSteelEye(
            name="link-report-transactions-to-steeleye", params=self.nca_params
        )
        task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            self.nca_params.report_type
        ]()

        expected_result = pd.DataFrame(
            {
                NCAReport.TXID: [pd.NA] * 3,
                NCAReport.CANC_TXID: [pd.NA] * 3,
                ReportDetailsIdFields.TEMP_REPORT_STATUS: [pd.NA] * 3,
                ReportDetailsIdFields.TEMP_ID: [pd.NA] * 3,
            }
        )
        result = task.report_mechanism_reconciliation.link_preprocess_data(
            preprocess_data_all_null_df
        )
        assert result.equals(expected_result)

    @patch.object(
        link_report_transactions_to_steeleye.LinkReportTransactionsToSteelEye,
        "_fetch_transactions",
        new_callable=lambda: fetch_transactions_empty_result_mock,
    )
    def test_nca_merge_steeleye_transactions_with_report_no_matches_df(
        self, mock_result, nca_merge_steeleye_transactions_with_report_df: pd.DataFrame
    ):
        task = LinkReportTransactionsToSteelEye(
            name="link-report-transactions-to-steeleye", params=self.nca_params
        )
        task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            self.nca_params.report_type
        ]()
        expected_result = pd.DataFrame(
            {
                ReportDetailsIdFields.TEMP_REPORT_STATUS: [
                    "NEWT",
                    "NEWT",
                    "CANC",
                    "NEWT",
                ],
                ReportDetailsIdFields.TEMP_ID: ["ID1", "ID2", "CANC_ID3", "ID4"],
                "FIELD3": ["val1", "val2", "val3", "val4"],
                "&id": [pd.NA] * 4,
                "isCreatedThroughReconciliation.flag": [pd.NA] * 4,
            }
        )

        result = task._merge_steeleye_transactions_with_report(
            report_transactions=nca_merge_steeleye_transactions_with_report_df,
            es_client=self.es_client,
            tenant="pinafore",
        )

        assert expected_result.equals(result)

    @patch.object(
        link_report_transactions_to_steeleye.LinkReportTransactionsToSteelEye,
        "_fetch_transactions",
        new_callable=lambda: fetch_transactions_mock,
    )
    def test_nca_merge_steeleye_transactions_with_report_with_matches_df(
        self, mock_result, nca_merge_steeleye_transactions_with_report_df: pd.DataFrame
    ):
        task = LinkReportTransactionsToSteelEye(
            name="link-report-transactions-to-steeleye", params=self.nca_params
        )
        task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            self.nca_params.report_type
        ]()

        expected_result = pd.DataFrame(
            {
                ReportDetailsIdFields.TEMP_REPORT_STATUS: [
                    "NEWT",
                    "NEWT",
                    "CANC",
                    "NEWT",
                ],
                ReportDetailsIdFields.TEMP_ID: ["ID1", "ID2", "CANC_ID3", "ID4"],
                "FIELD3": ["val1", "val2", "val3", "val4"],
                "&id": [pd.NA, "meta_id2", "meta_id3", pd.NA],
                ReportDetailsIdFields.TRANSACTION_REF_NO: [
                    pd.NA,
                    "ID2",
                    "CANC_ID3",
                    pd.NA,
                ],
                ReportDetailsIdFields.REPORT_DETAILS_REPORT_STATUS: [
                    pd.NA,
                    "NEWT",
                    "CANC",
                    pd.NA,
                ],
                "isCreatedThroughReconciliation.flag": [pd.NA] * 4,
            }
        )

        result = task._merge_steeleye_transactions_with_report(
            report_transactions=nca_merge_steeleye_transactions_with_report_df,
            es_client=self.es_client,
            tenant="pinafore",
        )

        assert expected_result.equals(result)

    @patch.object(
        link_report_transactions_to_steeleye.LinkReportTransactionsToSteelEye,
        "_fetch_transactions",
        new_callable=lambda: fetch_transactions_mock,
    )
    def test_arm_merge_steeleye_transactions_with_report_with_matches_df(
        self, mock_result, arm_merge_steeleye_transactions_with_report_df: pd.DataFrame
    ):
        task = LinkReportTransactionsToSteelEye(
            name="link-report-transactions-to-steeleye", params=self.arm_params
        )
        task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            self.arm_params.report_type
        ]()
        expected_result = pd.DataFrame(
            {
                ARMReport.REPORT_STATUS: ["NEWT", "NEWT", "CANC", "NEWT"],
                ARMReport.TRANSACTION_REF_NO: ["ID1", "ID2", "CANC_ID3", "ID4"],
                "FIELD3": ["val1", "val2", "val3", "val4"],
                "&id": [pd.NA, "meta_id2", "meta_id3", pd.NA],
                ReportDetailsIdFields.TRANSACTION_REF_NO: [
                    pd.NA,
                    "ID2",
                    "CANC_ID3",
                    pd.NA,
                ],
                ReportDetailsIdFields.REPORT_DETAILS_REPORT_STATUS: [
                    pd.NA,
                    "NEWT",
                    "CANC",
                    pd.NA,
                ],
                "isCreatedThroughReconciliation.flag": [pd.NA] * 4,
            }
        )

        result = task._merge_steeleye_transactions_with_report(
            report_transactions=arm_merge_steeleye_transactions_with_report_df,
            es_client=self.es_client,
            tenant="pinafore",
        )

        assert expected_result.equals(result)

    @patch.object(
        link_report_transactions_to_steeleye.LinkReportTransactionsToSteelEye,
        "_fetch_transactions",
        new_callable=lambda: fetch_transactions_mock,
    )
    def test_nca_rejection_cleared_status_trades_with_rejected_trades(
        self,
        mock_result,
        nca_rejection_cleared_status_trades_with_rejected_trades_df: pd.DataFrame,
    ):
        task = LinkReportTransactionsToSteelEye(
            name="link-report-transactions-to-steeleye", params=self.nca_params
        )
        task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            self.nca_params.report_type
        ]()
        expected_result = pd.DataFrame(
            {
                ARMReport.REPORT_STATUS: ["NEWT", "NEWT", "CANC", "NEWT"],
                ARMReport.TRANSACTION_REF_NO: ["ID1", "ID2", "CANC_ID3", "ID4"],
                ReportDetailsIdFields.TEMP_REPORT_STATUS: [
                    "NEWT",
                    "NEWT",
                    "CANC",
                    "NEWT",
                ],
                ReportDetailsIdFields.TEMP_ID: ["ID1", "ID2", "CANC_ID3", "ID4"],
                RecFields.WORKFLOW_NCA_STATUS: [
                    StaticValues.REJECTED,
                    StaticValues.REJECTED,
                    StaticValues.REJECTED,
                    StaticValues.REJECTED,
                ],
                "&id": [pd.NA, "meta_id2", "meta_id3", pd.NA],
                ReportDetailsIdFields.TRANSACTION_REF_NO: [
                    pd.NA,
                    "ID2",
                    "CANC_ID3",
                    pd.NA,
                ],
                ReportDetailsIdFields.REPORT_DETAILS_REPORT_STATUS: [
                    pd.NA,
                    "NEWT",
                    "CANC",
                    pd.NA,
                ],
                "isCreatedThroughReconciliation.flag": [pd.NA] * 4,
            }
        )

        result = task._merge_steeleye_transactions_with_report(
            report_transactions=nca_rejection_cleared_status_trades_with_rejected_trades_df,
            es_client=self.es_client,
            tenant="pinafore",
        )

        assert expected_result.equals(result)

    @patch.object(
        link_report_transactions_to_steeleye.LinkReportTransactionsToSteelEye,
        "_fetch_transactions",
        new_callable=lambda: fetch_transactions_mock,
    )
    def test_arm_rejection_cleared_status_trades_with_rejected_trades(
        self,
        mock_result,
        arm_rejection_cleared_status_trades_with_rejected_trades_df: pd.DataFrame,
    ):
        task = LinkReportTransactionsToSteelEye(
            name="link-report-transactions-to-steeleye", params=self.arm_params
        )
        task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            self.arm_params.report_type
        ]()
        expected_result = pd.DataFrame(
            {
                ARMReport.REPORT_STATUS: ["NEWT", "NEWT", "CANC", "NEWT"],
                ARMReport.TRANSACTION_REF_NO: ["ID1", "ID2", "CANC_ID3", "ID4"],
                RecFields.WORKFLOW_ARM_STATUS: [
                    StaticValues.REJECTED,
                    StaticValues.REJECTED,
                    StaticValues.REJECTED,
                    StaticValues.REJECTED,
                ],
                "&id": [pd.NA, "meta_id2", "meta_id3", pd.NA],
                ReportDetailsIdFields.TRANSACTION_REF_NO: [
                    pd.NA,
                    "ID2",
                    "CANC_ID3",
                    pd.NA,
                ],
                ReportDetailsIdFields.REPORT_DETAILS_REPORT_STATUS: [
                    pd.NA,
                    "NEWT",
                    "CANC",
                    pd.NA,
                ],
                "isCreatedThroughReconciliation.flag": [pd.NA] * 4,
            }
        )

        result = task._merge_steeleye_transactions_with_report(
            report_transactions=arm_rejection_cleared_status_trades_with_rejected_trades_df,
            es_client=self.es_client,
            tenant="pinafore",
        )

        assert expected_result.equals(result)
